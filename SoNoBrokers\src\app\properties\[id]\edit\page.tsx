import { notFound, redirect } from 'next/navigation';
import { createServerSupabaseClient } from '@/lib/supabase';
import { PropertyForm } from '@/components/shared/properties/PropertyForm';
import { PropertyImageUpload } from '@/components/shared/properties/PropertyImageUpload';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { updateProperty } from '@/lib/property-actions';

interface PropertyEditPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function PropertyEditPage({ params }: PropertyEditPageProps) {
  const resolvedParams = await params;
  const supabase = await createServerSupabaseClient();

  const { data: property } = await supabase
    .from('properties')
    .select('*')
    .eq('id', resolvedParams.id)
    .single();

  if (!property) {
    notFound();
  }

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user || user.id !== property.seller_id) {
    redirect('/properties/' + resolvedParams.id);
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      <h1 className="text-3xl font-bold">Edit Property</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Property Information</CardTitle>
            </CardHeader>
            <CardContent>
              <PropertyForm
                property={property}
                onSubmit={updateProperty.bind(null, resolvedParams.id)}
              />
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Property Images</CardTitle>
            </CardHeader>
            <CardContent>
              <PropertyImageUpload
                propertyId={property.id}
                propertyName={property.title}
                onUploadComplete={(images) => {
                  // This will be handled client-side
                  console.log('Images updated:', images);
                }}
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}