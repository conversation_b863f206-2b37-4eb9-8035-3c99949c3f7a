using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Services.Auth.Interfaces;
using MicroSaasWebApi.Models.Auth.Clerk;

using System.Security.Claims;

namespace MicroSaasWebApi.Controllers.Core
{
    /// <summary>
    /// Clerk Authentication controller with role-based authorization
    /// Handles Clerk authentication and role management (<PERSON><PERSON>, <PERSON>er, Seller)
    /// Public endpoints: Login, Register, Logout (no token required)
    /// All other controllers require valid Clerk token
    /// </summary>
    [Route("api/clerk-auth")]
    [ApiController]
    public class ClerkAuthController : ControllerBase
    {
        private readonly IClerkAuthService _clerkAuthService;
        private readonly ILogger<ClerkAuthController> _logger;

        public ClerkAuthController(
            IClerkAuthService clerkAuthService,
            ILogger<ClerkAuthController> logger)
        {
            _clerkAuthService = clerkAuthService;
            _logger = logger;
        }

        #region Public Clerk Authentication (No Token Required)

        /// <summary>
        /// Authenticate user with Clerk - PUBLIC ENDPOINT
        /// </summary>
        [HttpPost("login")]
        [AllowAnonymous]

        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _clerkAuthService.LoginAsync(request);

                if (!result.Success)
                {
                    return Unauthorized(result.Message);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during Clerk login for user: {Email}", request.Email);
                return StatusCode(500, "Login failed");
            }
        }

        /// <summary>
        /// Register new user with Clerk - PUBLIC ENDPOINT
        /// </summary>
        [HttpPost("register")]
        [AllowAnonymous]

        public async Task<IActionResult> Register([FromBody] RegisterRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _clerkAuthService.RegisterAsync(request);

                if (!result.Success)
                {
                    return BadRequest(result.Message);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during Clerk registration for user: {Email}", request.Email);
                return StatusCode(500, "Registration failed");
            }
        }

        /// <summary>
        /// Refresh Clerk authentication token - PUBLIC ENDPOINT
        /// </summary>
        [HttpPost("refresh-token")]
        [AllowAnonymous]

        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _clerkAuthService.RefreshTokenAsync(request.RefreshToken);

                if (!result.Success)
                {
                    return Unauthorized(result.Message);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing Clerk token");
                return StatusCode(500, "Token refresh failed");
            }
        }

        /// <summary>
        /// Sign out user from Clerk - PUBLIC ENDPOINT
        /// </summary>
        [HttpPost("logout")]
        [AllowAnonymous]

        public async Task<IActionResult> Logout()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

                if (!string.IsNullOrEmpty(userId))
                {
                    var result = await _clerkAuthService.LogoutAsync(userId);

                    if (!result)
                    {
                        return BadRequest("Logout failed");
                    }
                }

                return Ok("Logged out successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during Clerk logout");
                return StatusCode(500, "Logout failed");
            }
        }

        #endregion

        #region Token Validation (Requires Valid Clerk Token)

        /// <summary>
        /// Validate Clerk token - REQUIRES VALID TOKEN
        /// </summary>
        [HttpPost("validate-token")]
        [Authorize]

        public async Task<IActionResult> ValidateToken([FromBody] TokenValidationRequest request)
        {
            try
            {
                var principal = await _clerkAuthService.ValidateClerkTokenAsync(request.Token);

                if (principal == null)
                {
                    return Unauthorized("Invalid token");
                }

                var claims = principal.Claims.Select(c => new { c.Type, c.Value }).ToList();

                return Ok(new
                {
                    Valid = true,
                    Claims = claims,
                    UserId = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating Clerk token");
                return StatusCode(500, "Token validation failed");
            }
        }

        #endregion

        #region Role-Based Authorization - Simplified for SoNoBrokers

        /// <summary>
        /// Get user roles from Clerk metadata - REQUIRES VALID TOKEN
        /// </summary>
        [HttpGet("roles")]
        [Authorize]

        public Task<IActionResult> GetUserRoles()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Task.FromResult<IActionResult>(BadRequest("User ID not found"));
                }

                // For SoNoBrokers, roles are managed in Clerk metadata
                // This is a simplified implementation
                var roles = User.FindAll("role").Select(c => c.Value).ToList();
                return Task.FromResult<IActionResult>(Ok(new { roles }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user roles");
                return Task.FromResult<IActionResult>(StatusCode(500, "Failed to get user roles"));
            }
        }

        /// <summary>
        /// Get user permissions from Clerk metadata - REQUIRES VALID TOKEN
        /// </summary>
        [HttpGet("permissions")]
        [Authorize]

        public Task<IActionResult> GetUserPermissions()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Task.FromResult<IActionResult>(BadRequest("User ID not found"));
                }

                // For SoNoBrokers, permissions are managed in Clerk metadata
                // This is a simplified implementation
                var permissions = User.FindAll("permission").Select(c => c.Value).ToList();
                return Task.FromResult<IActionResult>(Ok(new { permissions }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permissions");
                return Task.FromResult<IActionResult>(StatusCode(500, "Failed to get user permissions"));
            }
        }

        #endregion
    }
}
