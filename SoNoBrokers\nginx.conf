server {
	listen 80 default_server;

	gzip on;
	gzip_min_length 1000;
	gzip_types text/plain text/xml application/javascript text/css;

	root /app;

	# normal routes
	# serve given url and default to index.html if not found
	# e.g. /, /user and /foo/bar will return index.html
	location / {
		add_header Cache-Control "no-store";
		try_files $uri /index.html;
	}
    location ~ ^/auth/ {
        # Add any specific directives you need for /auth here
        # For example, you might not want any caching for this route
        # add_header Cache-Control "no-cache, no-store, must-revalidate";
		add_header Cache-Control "no-store";
        try_files $uri /index.html;
    }
	# files
	# for all routes matching a dot, check for files and return 404 if not found
	# e.g. /file.js returns a 404 if not found
	location ~ \.(?!html) {
		add_header Cache-Control "public, max-age=2678400";
		try_files $uri =404;
	}
}
