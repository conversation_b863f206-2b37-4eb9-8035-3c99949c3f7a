'use client';

import { AppProvider } from '@/contexts/AppContext';
import { ReactNode, useEffect, useState } from 'react';

interface AppClientProviderProps {
  children: ReactNode;
}

export function AppClientProvider({ children }: AppClientProviderProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render AppProvider during SSR to avoid Clerk hook issues
  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <AppProvider>
      {children}
    </AppProvider>
  );
}
