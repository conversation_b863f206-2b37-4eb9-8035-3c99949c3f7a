using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Sas;
using MicroSaasWebApi.Services.Storage.Interfaces;
using MicroSaasWebApi.Models.Storage;

namespace MicroSaasWebApi.Services.Storage
{
    /// <summary>
    /// Azure Blob Storage service implementing SOLID principles
    /// Handles CRUD operations for Azure Blob Storage (files, images, videos)
    /// </summary>
    public class AzureBlobStorageService : IAzureBlobStorageService
    {
        private readonly BlobServiceClient _blobServiceClient;
        private readonly ILogger<AzureBlobStorageService> _logger;
        private readonly IConfiguration _configuration;

        public AzureBlobStorageService(
            BlobServiceClient blobServiceClient,
            ILogger<AzureBlobStorageService> logger,
            IConfiguration configuration)
        {
            _blobServiceClient = blobServiceClient;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Uploads file to Azure Blob Storage
        /// </summary>
        public async Task<BlobStorageFile?> UploadFileAsync(string containerName, string fileName, Stream fileStream, string? contentType = null, Dictionary<string, string>? metadata = null)
        {
            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);
                await containerClient.CreateIfNotExistsAsync(PublicAccessType.None);

                var blobClient = containerClient.GetBlobClient(fileName);

                var blobHttpHeaders = new BlobHttpHeaders();
                if (!string.IsNullOrEmpty(contentType))
                {
                    blobHttpHeaders.ContentType = contentType;
                }

                var uploadOptions = new BlobUploadOptions
                {
                    HttpHeaders = blobHttpHeaders,
                    Metadata = metadata
                };

                var response = await blobClient.UploadAsync(fileStream, uploadOptions);

                var properties = await blobClient.GetPropertiesAsync();

                _logger.LogInformation("File uploaded to Azure Blob Storage: {FileName}, Container: {ContainerName}", fileName, containerName);

                return new BlobStorageFile
                {
                    Name = fileName,
                    ContainerName = containerName,
                    Size = properties.Value.ContentLength,
                    ContentType = properties.Value.ContentType,
                    CreatedOn = properties.Value.CreatedOn,
                    LastModified = properties.Value.LastModified,
                    ETag = properties.Value.ETag.ToString(),
                    Url = blobClient.Uri.ToString(),
                    Metadata = properties.Value.Metadata
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file to Azure Blob Storage: {FileName}, Container: {ContainerName}", fileName, containerName);
                return null;
            }
        }

        /// <summary>
        /// Downloads file from Azure Blob Storage
        /// </summary>
        public async Task<BlobStorageFileContent?> DownloadFileAsync(string containerName, string fileName)
        {
            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);
                var blobClient = containerClient.GetBlobClient(fileName);

                if (!await blobClient.ExistsAsync())
                {
                    _logger.LogWarning("File not found in Azure Blob Storage: {FileName}, Container: {ContainerName}", fileName, containerName);
                    return null;
                }

                var response = await blobClient.DownloadStreamingAsync();
                var properties = await blobClient.GetPropertiesAsync();

                _logger.LogInformation("File downloaded from Azure Blob Storage: {FileName}, Container: {ContainerName}", fileName, containerName);

                return new BlobStorageFileContent
                {
                    File = new BlobStorageFile
                    {
                        Name = fileName,
                        ContainerName = containerName,
                        Size = properties.Value.ContentLength,
                        ContentType = properties.Value.ContentType,
                        CreatedOn = properties.Value.CreatedOn,
                        LastModified = properties.Value.LastModified,
                        ETag = properties.Value.ETag.ToString(),
                        Url = blobClient.Uri.ToString(),
                        Metadata = properties.Value.Metadata
                    },
                    Content = response.Value.Content
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading file from Azure Blob Storage: {FileName}, Container: {ContainerName}", fileName, containerName);
                return null;
            }
        }

        /// <summary>
        /// Updates file in Azure Blob Storage
        /// </summary>
        public async Task<BlobStorageFile?> UpdateFileAsync(string containerName, string fileName, Stream? newContent = null, string? newContentType = null, Dictionary<string, string>? newMetadata = null)
        {
            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);
                var blobClient = containerClient.GetBlobClient(fileName);

                if (!await blobClient.ExistsAsync())
                {
                    _logger.LogWarning("File not found for update in Azure Blob Storage: {FileName}, Container: {ContainerName}", fileName, containerName);
                    return null;
                }

                // Update content if provided
                if (newContent != null)
                {
                    var blobHttpHeaders = new BlobHttpHeaders();
                    if (!string.IsNullOrEmpty(newContentType))
                    {
                        blobHttpHeaders.ContentType = newContentType;
                    }

                    var uploadOptions = new BlobUploadOptions
                    {
                        HttpHeaders = blobHttpHeaders,
                        Metadata = newMetadata
                    };

                    await blobClient.UploadAsync(newContent, uploadOptions);
                }
                else
                {
                    // Update metadata only
                    if (newMetadata != null)
                    {
                        await blobClient.SetMetadataAsync(newMetadata);
                    }

                    // Update content type only
                    if (!string.IsNullOrEmpty(newContentType))
                    {
                        var blobHttpHeaders = new BlobHttpHeaders { ContentType = newContentType };
                        await blobClient.SetHttpHeadersAsync(blobHttpHeaders);
                    }
                }

                var properties = await blobClient.GetPropertiesAsync();

                _logger.LogInformation("File updated in Azure Blob Storage: {FileName}, Container: {ContainerName}", fileName, containerName);

                return new BlobStorageFile
                {
                    Name = fileName,
                    ContainerName = containerName,
                    Size = properties.Value.ContentLength,
                    ContentType = properties.Value.ContentType,
                    CreatedOn = properties.Value.CreatedOn,
                    LastModified = properties.Value.LastModified,
                    ETag = properties.Value.ETag.ToString(),
                    Url = blobClient.Uri.ToString(),
                    Metadata = properties.Value.Metadata
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating file in Azure Blob Storage: {FileName}, Container: {ContainerName}", fileName, containerName);
                return null;
            }
        }

        /// <summary>
        /// Deletes file from Azure Blob Storage
        /// </summary>
        public async Task<bool> DeleteFileAsync(string containerName, string fileName)
        {
            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);
                var blobClient = containerClient.GetBlobClient(fileName);

                var response = await blobClient.DeleteIfExistsAsync();

                if (response.Value)
                {
                    _logger.LogInformation("File deleted from Azure Blob Storage: {FileName}, Container: {ContainerName}", fileName, containerName);
                }
                else
                {
                    _logger.LogWarning("File not found for deletion in Azure Blob Storage: {FileName}, Container: {ContainerName}", fileName, containerName);
                }

                return response.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file from Azure Blob Storage: {FileName}, Container: {ContainerName}", fileName, containerName);
                return false;
            }
        }

        /// <summary>
        /// Lists files in Azure Blob Storage container
        /// </summary>
        public async Task<IEnumerable<BlobStorageFile>> ListFilesAsync(string containerName, string? prefix = null, int maxResults = 100)
        {
            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);

                if (!await containerClient.ExistsAsync())
                {
                    _logger.LogWarning("Container not found in Azure Blob Storage: {ContainerName}", containerName);
                    return new List<BlobStorageFile>();
                }

                var blobs = new List<BlobStorageFile>();
                var count = 0;

                await foreach (var blobItem in containerClient.GetBlobsAsync(prefix: prefix))
                {
                    if (count >= maxResults) break;

                    blobs.Add(new BlobStorageFile
                    {
                        Name = blobItem.Name,
                        ContainerName = containerName,
                        Size = blobItem.Properties.ContentLength ?? 0,
                        ContentType = blobItem.Properties.ContentType,
                        CreatedOn = blobItem.Properties.CreatedOn,
                        LastModified = blobItem.Properties.LastModified,
                        ETag = blobItem.Properties.ETag?.ToString(),
                        Url = containerClient.GetBlobClient(blobItem.Name).Uri.ToString(),
                        Metadata = blobItem.Metadata
                    });

                    count++;
                }

                _logger.LogInformation("Listed {Count} files from Azure Blob Storage container: {ContainerName}", blobs.Count, containerName);

                return blobs;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing files from Azure Blob Storage container: {ContainerName}", containerName);
                return new List<BlobStorageFile>();
            }
        }

        /// <summary>
        /// Gets file metadata from Azure Blob Storage
        /// </summary>
        public async Task<BlobStorageFile?> GetFileMetadataAsync(string containerName, string fileName)
        {
            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);
                var blobClient = containerClient.GetBlobClient(fileName);

                if (!await blobClient.ExistsAsync())
                {
                    _logger.LogWarning("File not found in Azure Blob Storage: {FileName}, Container: {ContainerName}", fileName, containerName);
                    return null;
                }

                var properties = await blobClient.GetPropertiesAsync();

                return new BlobStorageFile
                {
                    Name = fileName,
                    ContainerName = containerName,
                    Size = properties.Value.ContentLength,
                    ContentType = properties.Value.ContentType,
                    CreatedOn = properties.Value.CreatedOn,
                    LastModified = properties.Value.LastModified,
                    ETag = properties.Value.ETag.ToString(),
                    Url = blobClient.Uri.ToString(),
                    Metadata = properties.Value.Metadata
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting file metadata from Azure Blob Storage: {FileName}, Container: {ContainerName}", fileName, containerName);
                return null;
            }
        }

        /// <summary>
        /// Generates SAS URL for file access
        /// </summary>
        public async Task<string?> GenerateSasUrlAsync(string containerName, string fileName, TimeSpan expiry, BlobSasPermissions permissions = BlobSasPermissions.Read)
        {
            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);
                var blobClient = containerClient.GetBlobClient(fileName);

                if (!await blobClient.ExistsAsync())
                {
                    _logger.LogWarning("File not found for SAS URL generation: {FileName}, Container: {ContainerName}", fileName, containerName);
                    return null;
                }

                if (blobClient.CanGenerateSasUri)
                {
                    var sasBuilder = new BlobSasBuilder
                    {
                        BlobContainerName = containerName,
                        BlobName = fileName,
                        Resource = "b",
                        ExpiresOn = DateTimeOffset.UtcNow.Add(expiry)
                    };

                    sasBuilder.SetPermissions(permissions);

                    var sasUrl = blobClient.GenerateSasUri(sasBuilder).ToString();

                    _logger.LogInformation("SAS URL generated for file: {FileName}, Container: {ContainerName}", fileName, containerName);

                    return sasUrl;
                }
                else
                {
                    _logger.LogWarning("Cannot generate SAS URL for file: {FileName}, Container: {ContainerName}", fileName, containerName);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating SAS URL for file: {FileName}, Container: {ContainerName}", fileName, containerName);
                return null;
            }
        }

        /// <summary>
        /// Copies file within Azure Blob Storage
        /// </summary>
        public async Task<bool> CopyFileAsync(string sourceContainerName, string sourceFileName, string destinationContainerName, string destinationFileName)
        {
            try
            {
                var sourceContainerClient = _blobServiceClient.GetBlobContainerClient(sourceContainerName);
                var sourceBlobClient = sourceContainerClient.GetBlobClient(sourceFileName);

                var destinationContainerClient = _blobServiceClient.GetBlobContainerClient(destinationContainerName);
                await destinationContainerClient.CreateIfNotExistsAsync(PublicAccessType.None);
                var destinationBlobClient = destinationContainerClient.GetBlobClient(destinationFileName);

                if (!await sourceBlobClient.ExistsAsync())
                {
                    _logger.LogWarning("Source file not found for copy: {SourceFileName}, Container: {SourceContainerName}", sourceFileName, sourceContainerName);
                    return false;
                }

                var copyOperation = await destinationBlobClient.StartCopyFromUriAsync(sourceBlobClient.Uri);
                await copyOperation.WaitForCompletionAsync();

                _logger.LogInformation("File copied in Azure Blob Storage: {SourceFileName} -> {DestinationFileName}", sourceFileName, destinationFileName);

                return copyOperation.HasCompleted;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error copying file in Azure Blob Storage: {SourceFileName} -> {DestinationFileName}", sourceFileName, destinationFileName);
                return false;
            }
        }
    }
}
