using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    [Table("SearchFilter", Schema = "public")]
    public class SearchFilter
    {
        [Key]
        [Column("id")]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [Column("userId")]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [Column("name")]
        [MaxLength(255)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [Column("filters", TypeName = "jsonb")]
        public string Filters { get; set; } = string.Empty;

        [Column("isActive")]
        public bool IsActive { get; set; } = true;

        [NotMapped]
        public bool IsDefault { get; set; } = false; // Not in database yet, but used by controller

        [<PERSON>um<PERSON>("createdAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("updatedAt")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        // Helper methods for JSON fields
        public T? GetFilterDataAs<T>() where T : class
        {
            if (string.IsNullOrEmpty(Filters)) return null;
            try
            {
                return JsonSerializer.Deserialize<T>(Filters);
            }
            catch
            {
                return null;
            }
        }

        public void SetFilterData<T>(T filterData) where T : class
        {
            Filters = filterData != null ? JsonSerializer.Serialize(filterData) : "{}";
        }
    }
}
