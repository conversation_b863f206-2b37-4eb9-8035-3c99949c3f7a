2025-06-26 11:21:00.275 -04:00 [INF] Configuring middleware pipeline...
2025-06-26 11:21:00.800 -04:00 [INF] Middleware pipeline configured successfully
2025-06-26 11:21:00.862 -04:00 [INF] Initializing UI Database
2025-06-26 11:21:01.242 -04:00 [INF] Saving healthchecks configuration to database
2025-06-26 11:21:02.228 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:21:02.233 -04:00 [INF] Response: 307 in 10.7272ms
2025-06-26 11:21:02.284 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:21:02.351 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:21:02.371 -04:00 [INF] Response: 401 in 86.2274ms
2025-06-26 11:21:32.511 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:21:32.513 -04:00 [INF] Response: 307 in 2.7157ms
2025-06-26 11:21:32.515 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:21:32.519 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:21:32.520 -04:00 [INF] Response: 401 in 4.4375ms
2025-06-26 11:21:32.542 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:22:02.557 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:22:02.558 -04:00 [INF] Response: 307 in 1.771ms
2025-06-26 11:22:02.560 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:22:02.563 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:22:02.563 -04:00 [INF] Response: 401 in 3.6233ms
2025-06-26 11:22:32.578 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:22:32.579 -04:00 [INF] Response: 307 in 1.0834ms
2025-06-26 11:22:32.580 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:22:32.581 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:22:32.582 -04:00 [INF] Response: 401 in 1.8549ms
2025-06-26 11:22:32.584 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:23:02.598 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:23:02.599 -04:00 [INF] Response: 307 in 0.9945ms
2025-06-26 11:23:02.607 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:23:02.608 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:23:02.608 -04:00 [INF] Response: 401 in 1.6846ms
2025-06-26 11:23:32.624 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:23:32.626 -04:00 [INF] Response: 307 in 1.7769ms
2025-06-26 11:23:32.628 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:23:32.629 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:23:32.630 -04:00 [INF] Response: 401 in 1.651ms
2025-06-26 11:23:32.631 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:24:02.650 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:24:02.651 -04:00 [INF] Response: 307 in 1.5147ms
2025-06-26 11:24:02.653 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:24:02.654 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:24:02.655 -04:00 [INF] Response: 401 in 2.7111ms
2025-06-26 11:24:32.676 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:24:32.677 -04:00 [INF] Response: 307 in 0.6815ms
2025-06-26 11:24:32.678 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:24:32.679 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:24:32.680 -04:00 [INF] Response: 401 in 1.8187ms
2025-06-26 11:24:32.681 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:25:02.701 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:25:02.702 -04:00 [INF] Response: 307 in 0.9515ms
2025-06-26 11:25:02.712 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:25:02.713 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:25:02.713 -04:00 [INF] Response: 401 in 1.7693ms
2025-06-26 11:25:32.727 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:25:32.728 -04:00 [INF] Response: 307 in 1.588ms
2025-06-26 11:25:32.730 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:25:32.731 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:25:32.732 -04:00 [INF] Response: 401 in 2.3091ms
2025-06-26 11:25:32.734 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:25:56.309 -04:00 [INF] Request: GET /api/health/ping from ::1
2025-06-26 11:25:56.313 -04:00 [INF] Response: 307 in 3.463ms
2025-06-26 11:25:56.362 -04:00 [INF] Request: GET /api/health/ping from ::1
2025-06-26 11:25:56.381 -04:00 [INF] Response: 200 in 18.618ms
2025-06-26 11:25:58.868 -04:00 [INF] Request: OPTIONS /api/sonobrokers/test/ping from ::1
2025-06-26 11:25:58.870 -04:00 [INF] Response: 307 in 2.4211ms
2025-06-26 11:25:58.876 -04:00 [INF] Request: OPTIONS /api/sonobrokers/test/health from ::1
2025-06-26 11:25:58.878 -04:00 [INF] Response: 307 in 2.8938ms
2025-06-26 11:25:58.882 -04:00 [INF] Request: OPTIONS /api/sonobrokers/properties from ::1
2025-06-26 11:25:58.884 -04:00 [INF] Response: 307 in 1.8431ms
2025-06-26 11:25:58.887 -04:00 [INF] Request: OPTIONS /api/sonobrokers/users/profile from ::1
2025-06-26 11:25:58.888 -04:00 [INF] Response: 307 in 1.2107ms
2025-06-26 11:25:58.891 -04:00 [INF] Request: OPTIONS /api/sonobrokers/geo/enums from ::1
2025-06-26 11:25:58.892 -04:00 [INF] Response: 307 in 1.2331ms
2025-06-26 11:26:01.956 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:26:01.957 -04:00 [INF] Response: 307 in 1.0203ms
2025-06-26 11:26:01.959 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:26:01.960 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:26:01.961 -04:00 [INF] Response: 401 in 2.5395ms
2025-06-26 11:26:02.738 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:26:02.739 -04:00 [INF] Response: 307 in 0.982ms
2025-06-26 11:26:02.740 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:26:02.741 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:26:02.742 -04:00 [INF] Response: 401 in 1.6115ms
2025-06-26 11:26:08.327 -04:00 [INF] Request: GET /api/sonobrokers/test/ping from ::1
2025-06-26 11:26:08.330 -04:00 [INF] Response: 307 in 2.2392ms
2025-06-26 11:26:08.332 -04:00 [INF] Request: GET /api/sonobrokers/test/ping from ::1
2025-06-26 11:26:08.434 -04:00 [INF] Response: 200 in 102.367ms
2025-06-26 11:26:32.746 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:26:32.747 -04:00 [INF] Response: 307 in 1.002ms
2025-06-26 11:26:32.748 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:26:32.749 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:26:32.750 -04:00 [INF] Response: 401 in 1.4523ms
2025-06-26 11:26:32.751 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:27:02.766 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:27:02.768 -04:00 [INF] Response: 307 in 1.2016ms
2025-06-26 11:27:02.774 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:27:02.775 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:27:02.776 -04:00 [INF] Response: 401 in 1.9679ms
2025-06-26 11:27:08.232 -04:00 [INF] Request: GET /api/sonobrokers/test/health from ::1
2025-06-26 11:27:08.233 -04:00 [INF] Response: 307 in 1.2411ms
2025-06-26 11:27:08.235 -04:00 [INF] Request: GET /api/sonobrokers/test/health from ::1
2025-06-26 11:27:08.701 -04:00 [INF] Response: 200 in 466.4618ms
2025-06-26 11:27:17.441 -04:00 [INF] Request: GET /api/sonobrokers/geo/enums from ::1
2025-06-26 11:27:17.442 -04:00 [INF] Response: 307 in 1.4362ms
2025-06-26 11:27:17.444 -04:00 [INF] Request: GET /api/sonobrokers/geo/enums from ::1
2025-06-26 11:27:17.446 -04:00 [INF] Response: 404 in 2.5683ms
2025-06-26 11:27:23.100 -04:00 [INF] Request: GET /scalar/v1 from ::1
2025-06-26 11:27:23.102 -04:00 [INF] Response: 307 in 1.7699ms
2025-06-26 11:27:23.140 -04:00 [INF] Request: GET /scalar/v1 from ::1
2025-06-26 11:27:23.160 -04:00 [INF] Response: 200 in 19.7886ms
2025-06-26 11:27:23.204 -04:00 [INF] Request: GET /scalar/scalar.js from ::1
2025-06-26 11:27:23.269 -04:00 [INF] Response: 200 in 64.9981ms
2025-06-26 11:27:23.332 -04:00 [INF] Request: GET /openapi/v1.json from ::1
2025-06-26 11:27:23.680 -04:00 [INF] Request: GET /favicon.ico from ::1
2025-06-26 11:27:23.682 -04:00 [WRN] No authorization token found for protected endpoint: /favicon.ico
2025-06-26 11:27:23.683 -04:00 [INF] Response: 401 in 3.155ms
2025-06-26 11:27:26.261 -04:00 [INF] Response: 200 in 2929.0421ms
2025-06-26 11:27:26.263 -04:00 [INF] Request: GET /openapi/v1.json from ::1
2025-06-26 11:27:27.943 -04:00 [INF] Response: 200 in 1679.6398ms
2025-06-26 11:27:29.204 -04:00 [INF] Request: GET /api/sonobrokers/properties from ::1
2025-06-26 11:27:29.205 -04:00 [INF] Response: 307 in 1.3309ms
2025-06-26 11:27:29.207 -04:00 [INF] Request: GET /api/sonobrokers/properties from ::1
2025-06-26 11:27:29.242 -04:00 [ERR] Error searching properties
System.Net.Sockets.SocketException (0x00002AF9): No such host is known.
   at System.Net.Dns.GetHostEntryOrAddressesCore(String hostName, Boolean justAddresses, AddressFamily addressFamily, Nullable`1 activityOrDefault)
   at System.Net.Dns.GetHostAddresses(String hostNameOrAddress, AddressFamily family)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken, Boolean isFirstAttempt)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|213_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken, Boolean isFirstAttempt)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|34_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at MicroSaasWebApi.App.Context.DapperDbContext.get_Connection() in C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App\Context\DapperDbContext.cs:line 53
   at MicroSaasWebApi.App.Context.DapperDbContext.QuerySingleAsync[T](String sql, Object parameters) in C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App\Context\DapperDbContext.cs:line 144
   at MicroSaasWebApi.App.Repositories.PropertyRepository.SearchAsync(String searchTerm, String city, String province, Nullable`1 minPrice, Nullable`1 maxPrice, Nullable`1 bedrooms, Nullable`1 bathrooms, Nullable`1 status, Int32 page, Int32 pageSize) in C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App\Repositories\PropertyRepository.cs:line 311
2025-06-26 11:27:29.279 -04:00 [ERR] Error retrieving properties
System.Net.Sockets.SocketException (0x00002AF9): No such host is known.
   at System.Net.Dns.GetHostEntryOrAddressesCore(String hostName, Boolean justAddresses, AddressFamily addressFamily, Nullable`1 activityOrDefault)
   at System.Net.Dns.GetHostAddresses(String hostNameOrAddress, AddressFamily family)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken, Boolean isFirstAttempt)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|213_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken, Boolean isFirstAttempt)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|34_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at MicroSaasWebApi.App.Context.DapperDbContext.get_Connection() in C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App\Context\DapperDbContext.cs:line 53
   at MicroSaasWebApi.App.Context.DapperDbContext.QuerySingleAsync[T](String sql, Object parameters) in C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App\Context\DapperDbContext.cs:line 144
   at MicroSaasWebApi.App.Repositories.PropertyRepository.SearchAsync(String searchTerm, String city, String province, Nullable`1 minPrice, Nullable`1 maxPrice, Nullable`1 bedrooms, Nullable`1 bathrooms, Nullable`1 status, Int32 page, Int32 pageSize) in C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App\Repositories\PropertyRepository.cs:line 311
   at MicroSaasWebApi.Controllers.SoNoBrokers.PropertiesController.GetProperties(Nullable`1 status, String propertyType, Nullable`1 minPrice, Nullable`1 maxPrice, Int32 page, Int32 pageSize) in C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App\Controllers\SoNoBrokers\PropertiesController.cs:line 48
2025-06-26 11:27:29.286 -04:00 [INF] Response: 500 in 79.5077ms
2025-06-26 11:27:31.831 -04:00 [INF] Request: GET /favicon.ico from ::1
2025-06-26 11:27:31.834 -04:00 [WRN] No authorization token found for protected endpoint: /favicon.ico
2025-06-26 11:27:31.835 -04:00 [INF] Response: 401 in 4.4999ms
2025-06-26 11:27:32.785 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:27:32.786 -04:00 [INF] Response: 307 in 1.3897ms
2025-06-26 11:27:32.787 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:27:32.789 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:27:32.790 -04:00 [INF] Response: 401 in 2.1456ms
2025-06-26 11:27:32.791 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:28:02.796 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:28:02.798 -04:00 [INF] Response: 307 in 1.6865ms
2025-06-26 11:28:02.800 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:28:02.801 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:28:02.802 -04:00 [INF] Response: 401 in 2.3286ms
2025-06-26 11:28:10.583 -04:00 [INF] Request: GET /favicon.ico from ::1
2025-06-26 11:28:10.586 -04:00 [WRN] No authorization token found for protected endpoint: /favicon.ico
2025-06-26 11:28:10.589 -04:00 [INF] Response: 401 in 5.2889ms
2025-06-26 11:28:24.930 -04:00 [INF] Request: GET /api/sonobrokers/geo from ::1
2025-06-26 11:28:24.931 -04:00 [INF] Response: 307 in 0.8589ms
2025-06-26 11:28:24.932 -04:00 [INF] Request: GET /api/sonobrokers/geo from ::1
2025-06-26 11:28:24.932 -04:00 [INF] Response: 404 in 0.8178ms
2025-06-26 11:28:32.693 -04:00 [INF] Request: GET /api/sonobrokers/enums from ::1
2025-06-26 11:28:32.694 -04:00 [INF] Response: 307 in 0.8905ms
2025-06-26 11:28:32.695 -04:00 [INF] Request: GET /api/sonobrokers/enums from ::1
2025-06-26 11:28:32.696 -04:00 [WRN] No authorization token found for protected endpoint: /api/sonobrokers/enums
2025-06-26 11:28:32.697 -04:00 [INF] Response: 401 in 1.7984ms
2025-06-26 11:28:32.805 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:28:32.806 -04:00 [INF] Response: 307 in 1.0227ms
2025-06-26 11:28:32.808 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:28:32.810 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:28:32.810 -04:00 [INF] Response: 401 in 1.955ms
2025-06-26 11:28:32.812 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:29:02.833 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:29:02.834 -04:00 [INF] Response: 307 in 0.979ms
2025-06-26 11:29:02.844 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:29:02.846 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:29:02.846 -04:00 [INF] Response: 401 in 1.8217ms
2025-06-26 11:30:51.360 -04:00 [INF] Configuring middleware pipeline...
2025-06-26 11:30:51.534 -04:00 [INF] Middleware pipeline configured successfully
2025-06-26 11:30:51.567 -04:00 [INF] Initializing UI Database
2025-06-26 11:30:51.878 -04:00 [INF] Saving healthchecks configuration to database
2025-06-26 11:30:52.104 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:30:52.108 -04:00 [INF] Response: 307 in 8.1301ms
2025-06-26 11:30:52.150 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:30:52.194 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:30:52.211 -04:00 [INF] Response: 401 in 60.8806ms
2025-06-26 11:31:22.343 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:31:22.344 -04:00 [INF] Response: 307 in 1.0543ms
2025-06-26 11:31:22.346 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:31:22.351 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:31:22.352 -04:00 [INF] Response: 401 in 5.1518ms
2025-06-26 11:31:22.359 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:31:31.025 -04:00 [INF] Request: GET /api/health/ping from ::1
2025-06-26 11:31:31.053 -04:00 [INF] Response: 200 in 27.9142ms
2025-06-26 11:31:31.278 -04:00 [INF] Request: GET /favicon.ico from ::1
2025-06-26 11:31:31.284 -04:00 [WRN] No authorization token found for protected endpoint: /favicon.ico
2025-06-26 11:31:31.286 -04:00 [INF] Response: 401 in 8.4118ms
2025-06-26 11:31:52.373 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:31:52.374 -04:00 [INF] Response: 307 in 0.8767ms
2025-06-26 11:31:52.375 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:31:52.376 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:31:52.377 -04:00 [INF] Response: 401 in 1.5702ms
2025-06-26 11:32:22.394 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:32:22.395 -04:00 [INF] Response: 307 in 1.3147ms
2025-06-26 11:32:22.396 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:32:22.397 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:32:22.398 -04:00 [INF] Response: 401 in 1.6287ms
2025-06-26 11:32:22.400 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:32:52.414 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:32:52.415 -04:00 [INF] Response: 307 in 1.661ms
2025-06-26 11:32:52.422 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:32:52.423 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:32:52.424 -04:00 [INF] Response: 401 in 2.146ms
2025-06-26 11:32:57.836 -04:00 [INF] Request: GET /favicon.ico from ::1
2025-06-26 11:32:57.838 -04:00 [WRN] No authorization token found for protected endpoint: /favicon.ico
2025-06-26 11:32:57.839 -04:00 [INF] Response: 401 in 2.9534ms
2025-06-26 11:33:00.475 -04:00 [INF] Request: GET /favicon.ico from ::1
2025-06-26 11:33:00.477 -04:00 [WRN] No authorization token found for protected endpoint: /favicon.ico
2025-06-26 11:33:00.478 -04:00 [INF] Response: 401 in 2.6134ms
2025-06-26 11:33:04.549 -04:00 [INF] Request: GET /favicon.ico from ::1
2025-06-26 11:33:04.553 -04:00 [WRN] No authorization token found for protected endpoint: /favicon.ico
2025-06-26 11:33:04.557 -04:00 [INF] Response: 401 in 8.4973ms
2025-06-26 11:33:22.441 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:33:22.443 -04:00 [INF] Response: 307 in 1.6275ms
2025-06-26 11:33:22.444 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:33:22.445 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:33:22.445 -04:00 [INF] Response: 401 in 1.4609ms
2025-06-26 11:33:22.447 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:33:52.462 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:33:52.464 -04:00 [INF] Response: 307 in 1.9694ms
2025-06-26 11:33:52.466 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:33:52.469 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:33:52.471 -04:00 [INF] Response: 401 in 4.2976ms
2025-06-26 11:34:22.481 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:34:22.482 -04:00 [INF] Response: 307 in 0.9853ms
2025-06-26 11:34:22.483 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:34:22.484 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:34:22.485 -04:00 [INF] Response: 401 in 1.5418ms
2025-06-26 11:34:22.486 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:34:52.504 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:34:52.505 -04:00 [INF] Response: 307 in 0.9119ms
2025-06-26 11:34:52.515 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:34:52.516 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:34:52.517 -04:00 [INF] Response: 401 in 2.4373ms
2025-06-26 11:35:22.528 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:35:22.530 -04:00 [INF] Response: 307 in 2.0649ms
2025-06-26 11:35:22.532 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:35:22.533 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:35:22.533 -04:00 [INF] Response: 401 in 1.6359ms
2025-06-26 11:35:22.535 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:35:52.550 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:35:52.551 -04:00 [INF] Response: 307 in 1.3825ms
2025-06-26 11:35:52.553 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:35:52.554 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:35:52.554 -04:00 [INF] Response: 401 in 1.4852ms
2025-06-26 11:36:22.562 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:36:22.564 -04:00 [INF] Response: 307 in 1.8611ms
2025-06-26 11:36:22.565 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:36:22.566 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:36:22.567 -04:00 [INF] Response: 401 in 1.6932ms
2025-06-26 11:36:22.568 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:36:52.587 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:36:52.588 -04:00 [INF] Response: 307 in 1.0563ms
2025-06-26 11:36:52.595 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:36:52.596 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:36:52.596 -04:00 [INF] Response: 401 in 1.8368ms
2025-06-26 11:37:22.603 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:37:22.605 -04:00 [INF] Response: 307 in 2.0845ms
2025-06-26 11:37:22.607 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:37:22.607 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:37:22.608 -04:00 [INF] Response: 401 in 1.4309ms
2025-06-26 11:37:22.609 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:37:52.622 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:37:52.623 -04:00 [INF] Response: 307 in 1.8594ms
2025-06-26 11:37:52.625 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:37:52.627 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:37:52.628 -04:00 [INF] Response: 401 in 2.991ms
2025-06-26 11:38:22.636 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:38:22.638 -04:00 [INF] Response: 307 in 1.7729ms
2025-06-26 11:38:22.639 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:38:22.640 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:38:22.641 -04:00 [INF] Response: 401 in 1.7702ms
2025-06-26 11:38:22.642 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:38:52.656 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:38:52.658 -04:00 [INF] Response: 307 in 1.2229ms
2025-06-26 11:38:52.665 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:38:52.666 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:38:52.667 -04:00 [INF] Response: 401 in 2.0019ms
2025-06-26 11:39:22.671 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:39:22.673 -04:00 [INF] Response: 307 in 2.3951ms
2025-06-26 11:39:22.675 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:39:22.676 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:39:22.677 -04:00 [INF] Response: 401 in 2.1032ms
2025-06-26 11:39:22.681 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:39:52.687 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:39:52.689 -04:00 [INF] Response: 307 in 2.3662ms
2025-06-26 11:39:52.691 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:39:52.692 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:39:52.693 -04:00 [INF] Response: 401 in 1.9225ms
2025-06-26 11:40:22.703 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:40:22.704 -04:00 [INF] Response: 307 in 1.0073ms
2025-06-26 11:40:22.705 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:40:22.706 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:40:22.707 -04:00 [INF] Response: 401 in 1.6065ms
2025-06-26 11:40:22.708 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:40:52.718 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:40:52.719 -04:00 [INF] Response: 307 in 1.3559ms
2025-06-26 11:40:52.726 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:40:52.728 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:40:52.729 -04:00 [INF] Response: 401 in 2.7824ms
2025-06-26 11:41:22.745 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:41:22.746 -04:00 [INF] Response: 307 in 1.5448ms
2025-06-26 11:41:22.747 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:41:22.748 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:41:22.749 -04:00 [INF] Response: 401 in 1.5017ms
2025-06-26 11:41:22.750 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:41:52.755 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:41:52.757 -04:00 [INF] Response: 307 in 1.7765ms
2025-06-26 11:41:52.758 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:41:52.759 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:41:52.760 -04:00 [INF] Response: 401 in 1.4367ms
2025-06-26 11:42:22.764 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:42:22.766 -04:00 [INF] Response: 307 in 2.2824ms
2025-06-26 11:42:22.768 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:42:22.769 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:42:22.771 -04:00 [INF] Response: 401 in 2.859ms
2025-06-26 11:42:22.772 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:42:52.783 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:42:52.784 -04:00 [INF] Response: 307 in 0.7996ms
2025-06-26 11:42:52.789 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:42:52.790 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:42:52.791 -04:00 [INF] Response: 401 in 1.852ms
2025-06-26 11:43:22.799 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:43:22.801 -04:00 [INF] Response: 307 in 2.0631ms
2025-06-26 11:43:22.803 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:43:22.805 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:43:22.806 -04:00 [INF] Response: 401 in 2.111ms
2025-06-26 11:43:22.809 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:43:52.827 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:43:52.829 -04:00 [INF] Response: 307 in 1.7864ms
2025-06-26 11:43:52.830 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:43:52.834 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:43:52.835 -04:00 [INF] Response: 401 in 4.6131ms
2025-06-26 11:44:22.852 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:44:22.854 -04:00 [INF] Response: 307 in 2.0494ms
2025-06-26 11:44:22.857 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:44:22.859 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:44:22.860 -04:00 [INF] Response: 401 in 3.4058ms
2025-06-26 11:44:22.862 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:44:52.874 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:44:52.875 -04:00 [INF] Response: 307 in 0.8347ms
2025-06-26 11:44:52.880 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:44:52.881 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:44:52.883 -04:00 [INF] Response: 401 in 2.3195ms
2025-06-26 11:45:22.890 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:45:22.892 -04:00 [INF] Response: 307 in 2.2161ms
2025-06-26 11:45:22.893 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:45:22.895 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:45:22.895 -04:00 [INF] Response: 401 in 1.5862ms
2025-06-26 11:45:22.896 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:45:52.900 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:45:52.902 -04:00 [INF] Response: 307 in 1.6806ms
2025-06-26 11:45:52.903 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:45:52.904 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:45:52.905 -04:00 [INF] Response: 401 in 1.4678ms
2025-06-26 11:46:22.914 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:46:22.916 -04:00 [INF] Response: 307 in 1.7941ms
2025-06-26 11:46:22.917 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:46:22.919 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:46:22.920 -04:00 [INF] Response: 401 in 2.262ms
2025-06-26 11:46:22.921 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:46:52.943 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:46:52.945 -04:00 [INF] Response: 307 in 2.3009ms
2025-06-26 11:46:52.951 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:46:52.953 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:46:52.954 -04:00 [INF] Response: 401 in 2.3894ms
2025-06-26 11:47:22.963 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:47:22.965 -04:00 [INF] Response: 307 in 2.4487ms
2025-06-26 11:47:22.969 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:47:22.969 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:47:22.971 -04:00 [INF] Response: 401 in 2.3547ms
2025-06-26 11:47:22.973 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:47:52.984 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:47:52.986 -04:00 [INF] Response: 307 in 2.033ms
2025-06-26 11:47:52.988 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:47:52.989 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:47:52.990 -04:00 [INF] Response: 401 in 1.8791ms
2025-06-26 11:48:22.999 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:48:23.001 -04:00 [INF] Response: 307 in 2.1765ms
2025-06-26 11:48:23.003 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:48:23.004 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:48:23.004 -04:00 [INF] Response: 401 in 1.5427ms
2025-06-26 11:48:23.006 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:48:53.017 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:48:53.018 -04:00 [INF] Response: 307 in 1.0019ms
2025-06-26 11:48:53.023 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:48:53.024 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:48:53.024 -04:00 [INF] Response: 401 in 1.47ms
2025-06-26 11:49:23.038 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:49:23.040 -04:00 [INF] Response: 307 in 1.9689ms
2025-06-26 11:49:23.041 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:49:23.042 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:49:23.042 -04:00 [INF] Response: 401 in 0.9979ms
2025-06-26 11:49:23.043 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:49:53.053 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:49:53.054 -04:00 [INF] Response: 307 in 1.3792ms
2025-06-26 11:49:53.055 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:49:53.057 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:49:53.057 -04:00 [INF] Response: 401 in 1.5992ms
2025-06-26 11:50:23.076 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:50:23.078 -04:00 [INF] Response: 307 in 2.0619ms
2025-06-26 11:50:23.080 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:50:23.081 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:50:23.082 -04:00 [INF] Response: 401 in 1.4879ms
2025-06-26 11:50:23.083 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:50:53.097 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:50:53.100 -04:00 [INF] Response: 307 in 2.6888ms
2025-06-26 11:50:53.105 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:50:53.107 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:50:53.107 -04:00 [INF] Response: 401 in 1.7236ms
2025-06-26 11:51:23.113 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:51:23.115 -04:00 [INF] Response: 307 in 1.8058ms
2025-06-26 11:51:23.117 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:51:23.118 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:51:23.119 -04:00 [INF] Response: 401 in 1.7677ms
2025-06-26 11:51:23.120 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:51:53.125 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:51:53.127 -04:00 [INF] Response: 307 in 1.4983ms
2025-06-26 11:51:53.129 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:51:53.130 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:51:53.130 -04:00 [INF] Response: 401 in 1.8796ms
2025-06-26 11:52:23.138 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:52:23.140 -04:00 [INF] Response: 307 in 1.8028ms
2025-06-26 11:52:23.141 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:52:23.142 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:52:23.142 -04:00 [INF] Response: 401 in 1.3897ms
2025-06-26 11:52:23.143 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:52:53.154 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:52:53.155 -04:00 [INF] Response: 307 in 1.0625ms
2025-06-26 11:52:53.161 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:52:53.162 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:52:53.163 -04:00 [INF] Response: 401 in 1.8672ms
2025-06-26 11:53:23.170 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:53:23.171 -04:00 [INF] Response: 307 in 0.67ms
2025-06-26 11:53:23.172 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:53:23.173 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:53:23.174 -04:00 [INF] Response: 401 in 1.4908ms
2025-06-26 11:53:23.175 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:53:53.184 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:53:53.187 -04:00 [INF] Response: 307 in 2.6465ms
2025-06-26 11:53:53.189 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:53:53.190 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:53:53.191 -04:00 [INF] Response: 401 in 2.0782ms
2025-06-26 11:54:23.197 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:54:23.199 -04:00 [INF] Response: 307 in 1.8454ms
2025-06-26 11:54:23.201 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:54:23.202 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:54:23.202 -04:00 [INF] Response: 401 in 1.8206ms
2025-06-26 11:54:23.205 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:54:53.214 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:54:53.215 -04:00 [INF] Response: 307 in 1.8238ms
2025-06-26 11:54:53.222 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:54:53.223 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:54:53.224 -04:00 [INF] Response: 401 in 1.8605ms
2025-06-26 11:55:23.239 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:55:23.242 -04:00 [INF] Response: 307 in 2.3917ms
2025-06-26 11:55:23.243 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:55:23.244 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:55:23.244 -04:00 [INF] Response: 401 in 1.5189ms
2025-06-26 11:55:23.253 -04:00 [INF] Notification is sent on same window time.
2025-06-26 11:55:53.264 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:55:53.265 -04:00 [INF] Response: 307 in 1.5861ms
2025-06-26 11:55:53.267 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:55:53.268 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:55:53.269 -04:00 [INF] Response: 401 in 1.7685ms
2025-06-26 11:56:23.281 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:56:23.284 -04:00 [INF] Response: 307 in 2.3739ms
2025-06-26 11:56:23.285 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 11:56:23.286 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 11:56:23.287 -04:00 [INF] Response: 401 in 1.7524ms
2025-06-26 11:56:23.288 -04:00 [INF] Notification is sent on same window time.
2025-06-26 12:37:41.222 -04:00 [INF] Configuring middleware pipeline...
2025-06-26 12:37:41.403 -04:00 [INF] Middleware pipeline configured successfully
2025-06-26 12:37:41.436 -04:00 [INF] Initializing UI Database
2025-06-26 12:37:41.766 -04:00 [INF] Saving healthchecks configuration to database
2025-06-26 12:37:41.982 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 12:37:41.986 -04:00 [INF] Response: 307 in 9.4281ms
2025-06-26 12:37:42.031 -04:00 [INF] Request: GET /api/health from ::1
2025-06-26 12:37:42.077 -04:00 [WRN] No authorization token found for protected endpoint: /api/health
2025-06-26 12:37:42.095 -04:00 [INF] Response: 401 in 63.4823ms
