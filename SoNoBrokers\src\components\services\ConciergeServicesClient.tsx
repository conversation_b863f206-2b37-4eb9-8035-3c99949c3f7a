'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Crown,
  CheckCircle,
  Star,
  Clock,
  Shield,
  Users,
  Home,
  Camera,
  FileText,
  Hammer,
  Phone,
  Mail,
  MapPin,
  ArrowRight,
  PlayCircle,
  Award,
  Target,
  Zap,
  X
} from 'lucide-react'

interface ConciergeServicesClientProps {
  country: 'CA' | 'US' | 'UAE'
}

export default function ConciergeServicesClient({ country }: ConciergeServicesClientProps) {
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null)
  const [showContactForm, setShowContactForm] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    propertyAddress: '',
    propertyValue: '',
    timeline: '',
    requirements: '',
    packageInterest: ''
  })

  const countryData = {
    CA: {
      name: 'Canada',
      flag: '🇨🇦',
      currency: 'CAD',
      heroImage: 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
      buildingGif: 'https://images.unsplash.com/photo-1541888946425-d81bb19240f5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      processImage: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      teamImage: 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      specialties: ['MLS Alternatives', 'Bilingual Services', 'Provincial Compliance'],
      regulations: 'Provincial real estate regulations and FINTRAC compliance'
    },
    US: {
      name: 'United States',
      flag: '🇺🇸',
      currency: 'USD',
      heroImage: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
      buildingGif: 'https://images.unsplash.com/photo-1503387762-592deb58ef4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      processImage: 'https://images.unsplash.com/photo-1560472355-536de3962603?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      teamImage: 'https://images.unsplash.com/photo-1600880292089-90a7e086ee0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      specialties: ['State Compliance', 'Title Services', 'Escrow Management'],
      regulations: 'State-specific real estate laws and federal compliance'
    },
    UAE: {
      name: 'United Arab Emirates',
      flag: '🇦🇪',
      currency: 'AED',
      heroImage: 'https://images.unsplash.com/photo-1512453979798-5ea266f8880c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
      buildingGif: 'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      processImage: 'https://images.unsplash.com/photo-1560472354-a33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      teamImage: 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      specialties: ['RERA Compliance', 'Investment Services', 'Arabic Support'],
      regulations: 'RERA regulations and UAE property investment laws'
    }
  }

  const conciergePackages = [
    {
      id: 'essential',
      name: 'Essential Concierge',
      price: country === 'UAE' ? '2,999' : country === 'CA' ? '1,999' : '1,799',
      description: 'Perfect for standard property sales with professional coordination',
      features: [
        'Dedicated concierge coordinator',
        'Professional photography session',
        'Property inspection coordination',
        'Legal document review',
        'Buyer screening and scheduling',
        'Marketing strategy consultation',
        'Progress tracking and updates'
      ],
      popular: false
    },
    {
      id: 'premium',
      name: 'Premium Concierge',
      price: country === 'UAE' ? '4,999' : country === 'CA' ? '3,499' : '2,999',
      description: 'Comprehensive service for high-value properties with white-glove treatment',
      features: [
        'Everything in Essential package',
        'Virtual tour and drone photography',
        'Professional staging consultation',
        'Market analysis and pricing strategy',
        'Negotiation support and guidance',
        'Transaction management',
        'Post-sale support and coordination',
        'Priority customer support'
      ],
      popular: true
    },
    {
      id: 'luxury',
      name: 'Luxury Concierge',
      price: country === 'UAE' ? '7,999' : country === 'CA' ? '5,999' : '4,999',
      description: 'Ultimate white-glove service for luxury properties and complex transactions',
      features: [
        'Everything in Premium package',
        'Personal concierge manager',
        'Luxury marketing campaign',
        'International buyer network access',
        'Private showing coordination',
        'Investment analysis and consultation',
        'Tax and legal advisory services',
        '24/7 concierge hotline',
        'Exclusive networking events'
      ],
      popular: false
    }
  ]

  const services = [
    {
      icon: Camera,
      title: 'Professional Photography',
      description: 'High-quality property photography and virtual tours'
    },
    {
      icon: FileText,
      title: 'Legal Documentation',
      description: 'Complete legal review and document preparation'
    },
    {
      icon: Hammer,
      title: 'Property Preparation',
      description: 'Staging, repairs, and enhancement coordination'
    },
    {
      icon: Users,
      title: 'Buyer Management',
      description: 'Screening, scheduling, and communication with potential buyers'
    },
    {
      icon: Shield,
      title: 'Transaction Security',
      description: 'Secure payment processing and fraud protection'
    },
    {
      icon: Home,
      title: 'Market Analysis',
      description: 'Comprehensive market research and pricing strategy'
    }
  ]

  const conciergeSteps = [
    {
      step: 1,
      title: 'Initial Consultation',
      description: 'Free property assessment and strategy planning session',
      icon: Target,
      image: 'https://picsum.photos/400/300?random=1'
    },
    {
      step: 2,
      title: 'Team Assembly',
      description: 'Curate the perfect team of professionals for your property',
      icon: Users,
      image: 'https://picsum.photos/400/300?random=2'
    },
    {
      step: 3,
      title: 'Property Preparation',
      description: 'Photography, staging, inspections, and legal documentation',
      icon: Camera,
      image: 'https://picsum.photos/400/300?random=3'
    },
    {
      step: 4,
      title: 'Marketing Launch',
      description: 'Strategic marketing campaign across multiple channels',
      icon: Zap,
      image: 'https://picsum.photos/400/300?random=4'
    },
    {
      step: 5,
      title: 'Buyer Management',
      description: 'Screen buyers, coordinate viewings, and manage negotiations',
      icon: Shield,
      image: 'https://picsum.photos/400/300?random=5'
    },
    {
      step: 6,
      title: 'Transaction Completion',
      description: 'Handle all paperwork, closing, and post-sale coordination',
      icon: Award,
      image: 'https://picsum.photos/400/300?random=6'
    }
  ]

  const handleContactSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const response = await fetch('/api/contact-concierge', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          country,
          selectedPackage
        })
      })

      if (response.ok) {
        alert('Thank you! Your concierge inquiry has been sent. We\'ll contact you within 24 hours.')
        setFormData({
          name: '',
          email: '',
          phone: '',
          propertyAddress: '',
          propertyValue: '',
          timeline: '',
          requirements: '',
          packageInterest: ''
        })
        setShowContactForm(false)
      } else {
        alert('There was an error sending your inquiry. Please try again.')
      }
    } catch (error) {
      console.error('Error submitting form:', error)
      alert('There was an error sending your inquiry. Please try again.')
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative h-[80vh] flex items-center justify-center overflow-hidden">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${countryData[country].heroImage})` }}
        >
          <div className="absolute inset-0 bg-black/60" />
        </div>

        <div className="relative z-10 text-center text-white max-w-5xl mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <Badge variant="secondary" className="mb-4 text-lg px-4 py-2">
              <Crown className="w-4 h-4 mr-2" />
              {countryData[country].flag} {countryData[country].name} Concierge
            </Badge>
            <h1 className="text-5xl md:text-7xl font-bold mb-6">
              White-Glove Property
              <span className="text-primary block">Concierge Services</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto">
              Let our expert team handle every aspect of your property sale while you focus on what matters most.
              From photography to closing, we coordinate it all.
            </p>
            <div className="flex flex-wrap justify-center gap-4 mb-8">
              {countryData[country].specialties.map((specialty, index) => (
                <Badge key={index} variant="outline" className="text-white border-white">
                  {specialty}
                </Badge>
              ))}
            </div>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="text-lg px-8 py-4"
                onClick={() => setShowContactForm(true)}
              >
                Get Started Today
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="text-lg px-8 py-4 text-white border-white hover:bg-white hover:text-black"
              >
                <PlayCircle className="mr-2 h-5 w-5" />
                Watch How It Works
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Building Process Section */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              Building Your Success
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Watch how our concierge team coordinates multiple professional services to create a seamless property sale experience
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <img
                src={countryData[country].buildingGif}
                alt="Property development process"
                className="rounded-lg shadow-2xl w-full h-[400px] object-cover"
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <h3 className="text-2xl font-bold">Coordinated Excellence</h3>
              <p className="text-muted-foreground">
                Our concierge services bring together photographers, inspectors, lawyers,
                contractors, and marketing specialists to create a comprehensive property
                sale experience that maximizes your return while minimizing your stress.
              </p>

              <div className="grid grid-cols-2 gap-4">
                {services.map((service, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <service.icon className="h-5 w-5 text-primary" />
                    <span className="text-sm font-medium">{service.title}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Process Steps Section */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              Our 6-Step Concierge Process
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              From initial consultation to final closing, our systematic approach ensures
              every detail is handled with precision and care.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {conciergeSteps.map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader className="text-center">
                    <div className="relative mb-4">
                      <img
                        src={step.image}
                        alt={step.title}
                        className="w-full h-48 object-cover rounded-lg"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = `https://via.placeholder.com/400x300/72e3ad/ffffff?text=Step+${step.step}`;
                        }}
                      />
                      <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
                        <div className="bg-primary text-primary-foreground rounded-full w-12 h-12 flex items-center justify-center font-bold text-lg">
                          {step.step}
                        </div>
                      </div>
                    </div>
                    <CardTitle className="flex items-center justify-center gap-2 mt-4">
                      <step.icon className="h-5 w-5 text-primary" />
                      {step.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-center">
                      {step.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              Choose Your Concierge Package
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Select the perfect level of service for your property sale. All packages include
              dedicated coordination and professional service management.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {conciergePackages.map((pkg, index) => (
              <motion.div
                key={pkg.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className={`h-full relative ${pkg.popular ? 'border-primary shadow-lg scale-105' : ''}`}>
                  {pkg.popular && (
                    <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      Most Popular
                    </Badge>
                  )}
                  <CardHeader className="text-center">
                    <CardTitle className="text-2xl">{pkg.name}</CardTitle>
                    <div className="text-3xl font-bold text-primary">
                      {countryData[country].currency} ${pkg.price}
                    </div>
                    <CardDescription>{pkg.description}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {pkg.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                    <Button
                      className="w-full mt-6"
                      variant={pkg.popular ? "default" : "outline"}
                      onClick={() => {
                        setSelectedPackage(pkg.id)
                        setFormData(prev => ({ ...prev, packageInterest: pkg.name }))
                        setShowContactForm(true)
                      }}
                    >
                      Get Started
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form Modal */}
      {showContactForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="bg-background rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-2xl font-bold">Contact Our Concierge Team</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowContactForm(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <form onSubmit={handleContactSubmit} className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Full Name *</label>
                    <Input
                      required
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Your full name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Email *</label>
                    <Input
                      type="email"
                      required
                      value={formData.email}
                      onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Phone Number</label>
                    <Input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                      placeholder="Your phone number"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Property Value</label>
                    <Input
                      value={formData.propertyValue}
                      onChange={(e) => setFormData(prev => ({ ...prev, propertyValue: e.target.value }))}
                      placeholder="Estimated property value"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Property Address</label>
                  <Input
                    value={formData.propertyAddress}
                    onChange={(e) => setFormData(prev => ({ ...prev, propertyAddress: e.target.value }))}
                    placeholder="Property address or general location"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Timeline</label>
                  <Input
                    value={formData.timeline}
                    onChange={(e) => setFormData(prev => ({ ...prev, timeline: e.target.value }))}
                    placeholder="When are you planning to sell?"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Requirements & Questions</label>
                  <Textarea
                    value={formData.requirements}
                    onChange={(e) => setFormData(prev => ({ ...prev, requirements: e.target.value }))}
                    placeholder="Tell us about your specific needs, questions, or any special requirements..."
                    rows={4}
                  />
                </div>

                {selectedPackage && (
                  <div className="bg-muted p-4 rounded-lg">
                    <p className="text-sm text-muted-foreground">
                      Package Interest: <span className="font-medium">{formData.packageInterest}</span>
                    </p>
                  </div>
                )}

                <div className="flex gap-4 pt-4">
                  <Button type="submit" className="flex-1">
                    Send Inquiry
                    <Mail className="ml-2 h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowContactForm(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </form>

              <div className="mt-6 pt-6 border-t text-center text-sm text-muted-foreground">
                <p>Our concierge team will contact you within 24 hours.</p>
                <p>Questions? Email us at <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a></p>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}
