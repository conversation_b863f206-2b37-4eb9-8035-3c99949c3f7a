using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    public enum BookingStatus
    {
        pending,
        confirmed,
        completed,
        cancelled
    }

    [Table("ServiceBooking", Schema = "snb")]
    public class ServiceBooking
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string PropertyId { get; set; } = string.Empty;

        [Required]
        public string ServiceProviderId { get; set; } = string.Empty;

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        public ServiceType ServiceType { get; set; }

        public BookingStatus Status { get; set; } = BookingStatus.pending;

        public DateTime? ScheduledAt { get; set; }

        public DateTime? CompletedAt { get; set; }

        public string? Notes { get; set; }

        public string? PaymentId { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        [ForeignKey("PropertyId")]
        public virtual Property Property { get; set; } = null!;

        [ForeignKey("ServiceProviderId")]
        public virtual ServiceProvider ServiceProvider { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }
}
