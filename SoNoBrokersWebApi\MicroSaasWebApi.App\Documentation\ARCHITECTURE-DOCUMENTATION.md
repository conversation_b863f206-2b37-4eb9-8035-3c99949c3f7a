# 🏗️ MicroSaaS Web API - Architecture Documentation

## 📋 Table of Contents
- [Clean Namespace Structure](#clean-namespace-structure)
- [Multi-Tenant Architecture](#multi-tenant-architecture)
- [Middleware Ordering System](#middleware-ordering-system)
- [Modern .NET 9 Patterns](#modern-net-9-patterns)
- [Extensible Design](#extensible-design)
- [Implementation Examples](#implementation-examples)

## 🗂️ Clean Namespace Structure

### **Legacy vs Modern Namespace Mapping**

| Legacy Namespace | Modern Namespace | Purpose |
|------------------|------------------|---------|
| `PXW` | `Base` | Foundational services and models |
| `AlphaCCO` | `Tenant` | Tenant-specific functionality |
| `MICROSAAS` | `Core` | Core business logic |

### **Namespace Organization**

```
MicroSaasWebApi/
├── Models/
│   ├── Base/                    # Foundation models
│   │   ├── Tenant.cs           # Core tenant model
│   │   ├── TenantSettings.cs   # Tenant configuration
│   │   └── RequestBody.cs      # Base request model
│   ├── Tenant/                 # Tenant-specific models
│   │   ├── TenantWorkflows.cs  # Workflow definitions
│   │   ├── TenantUser.cs       # User-tenant relationships
│   │   └── TenantSubscription.cs # Subscription models
│   └── Core/                   # Core business models
│       ├── Payment/            # Payment models
│       ├── Auth/               # Authentication models
│       └── Configuration/      # App configuration
├── Services/
│   ├── Base/                   # Foundation services
│   │   ├── Interface/          # Service contracts
│   │   └── Implementation/     # Service implementations
│   ├── Tenant/                 # Tenant-specific services
│   │   ├── TenantResolver/     # Tenant resolution logic
│   │   └── TenantProvider/     # Tenant context management
│   └── Core/                   # Core business services
│       ├── Payment/            # Payment processing
│       ├── Auth/               # Authentication
│       └── Configuration/      # Configuration management
└── Middleware/                 # Request pipeline middleware
    ├── TenantResolution/       # Tenant identification
    ├── Authentication/         # Auth middleware
    └── Ordering/               # Middleware orchestration
```

### **Benefits of Clean Namespace Structure**

✅ **Clear Separation of Concerns**: Each namespace has a specific responsibility  
✅ **Easy Navigation**: Developers can quickly find relevant code  
✅ **Maintainable**: Changes are isolated to specific areas  
✅ **Testable**: Clear boundaries make unit testing easier  
✅ **Scalable**: New features fit naturally into the structure  

## 🏢 Multi-Tenant Architecture

### **Tenant Resolution Strategies**

Our multi-tenant system supports multiple tenant identification methods:

#### **1. Subdomain-Based Resolution**
```csharp
// URL: https://acme.microsaas.com/api/users
// Tenant: "acme"
```

#### **2. Header-Based Resolution**
```csharp
// Header: X-Tenant-Id: acme-corp
// Tenant: "acme-corp"
```

#### **3. Path-Based Resolution**
```csharp
// URL: https://api.microsaas.com/tenant/acme/users
// Tenant: "acme"
```

#### **4. Query Parameter Resolution**
```csharp
// URL: https://api.microsaas.com/api/users?tenantId=acme
// Tenant: "acme"
```

#### **5. JWT Claims Resolution**
```csharp
// JWT Claim: "tenant_id": "acme-corp"
// Tenant: "acme-corp"
```

### **Tenant Model Structure**

```csharp
public class Tenant
{
    public string Id { get; set; }                    // Unique identifier
    public string Name { get; set; }                  // Display name
    public bool IsActive { get; set; }                // Status flag
    public TenantSettings Settings { get; set; }      // Configuration
    public TenantSubscription Subscription { get; set; } // Billing info
    public List<TenantUser> Users { get; set; }       // User relationships
}

public class TenantSettings
{
    public string DatabaseConnectionString { get; set; }     // Isolated data
    public Dictionary<string, bool> Features { get; set; }   // Feature flags
    public TenantBranding Branding { get; set; }            // UI customization
    public TenantLimits Limits { get; set; }                // Usage limits
}
```

### **Multi-Tenant Data Isolation**

#### **Database Per Tenant (Recommended)**
```csharp
public string GetTenantConnectionString(string tenantId)
{
    return $"Server=localhost;Database=MicroSaas_{tenantId};Integrated Security=true;";
}
```

#### **Schema Per Tenant**
```csharp
public string GetTenantSchema(string tenantId)
{
    return $"tenant_{tenantId}";
}
```

#### **Row-Level Security**
```csharp
public class BaseEntity
{
    public string TenantId { get; set; }  // Every entity has tenant context
}
```

### **Tenant Context Management**

```csharp
public interface ITenantProviderService
{
    Tenant GetCurrentTenant();
    string GetCurrentTenantId();
    void SetTenant(Tenant tenant);
    bool HasFeature(string featureName);
    T GetTenantSetting<T>(string settingName, T defaultValue = default);
}
```

## ⚙️ Middleware Ordering System

### **Chain of Responsibility Pattern**

Our middleware system implements the Chain of Responsibility pattern for ordered execution:

```csharp
public static WebApplication ConfigureMiddlewarePipeline(this WebApplication app)
{
    // 1. Exception Handling (Must be first)
    app.UseExceptionHandling();
    
    // 2. Security Headers (Early security)
    app.UseSecurityHeaders();
    
    // 3. Rate Limiting (Protect against abuse)
    app.UseRateLimiting();
    
    // 4. Request Logging (Audit trail)
    app.UseRequestLogging();
    
    // 5. HTTPS Redirection (Force secure connections)
    app.UseHttpsRedirection();
    
    // 6. Static Files (Serve assets)
    app.UseStaticFiles();
    
    // 7. Routing (Enable routing)
    app.UseRouting();
    
    // 8. CORS (Cross-origin requests)
    app.UseCorsPolicy();
    
    // 9. Authentication (Identify users)
    app.UseAuthentication();
    
    // 10. Authorization (Check permissions)
    app.UseAuthorization();
    
    // 11. Tenant Resolution (Multi-tenant support)
    app.UseTenantResolution();
    
    // 12. Custom Business Logic
    app.UseCustomBusinessLogic();
    
    // 13. Response Compression (Optimize responses)
    app.UseResponseCompression();
    
    // 14. Health Checks (Monitoring)
    app.UseHealthChecks();
    
    // 15. Development Tools (Swagger, etc.)
    app.UseDevelopmentTools();
    
    // 16. Endpoints (Controllers, hubs)
    app.MapEndpoints();
    
    return app;
}
```

### **Middleware Execution Order Enum**

```csharp
public enum MiddlewareOrder
{
    ExceptionHandling = 1,    // Catch all exceptions
    SecurityHeaders = 2,      // Security first
    RateLimiting = 3,        // Prevent abuse
    RequestLogging = 4,      // Audit requests
    HttpsRedirection = 5,    // Force HTTPS
    StaticFiles = 6,         // Serve static content
    Routing = 7,             // Enable routing
    Cors = 8,                // Cross-origin support
    Authentication = 9,      // Identify users
    Authorization = 10,      // Check permissions
    TenantResolution = 11,   // Resolve tenant
    CustomLogic = 12,        // Business logic
    ResponseCompression = 13, // Optimize responses
    HealthChecks = 14,       // Health monitoring
    DevelopmentTools = 15,   // Dev tools
    Endpoints = 16           // Map endpoints
}
```

### **Benefits of Ordered Middleware**

✅ **Predictable Execution**: Always runs in the same order  
✅ **Security First**: Security middleware runs early  
✅ **Performance Optimized**: Efficient request processing  
✅ **Easy Debugging**: Clear execution flow  
✅ **Maintainable**: Easy to add/remove middleware  

## 🚀 Modern .NET 9 Patterns

### **Minimal APIs with Controllers**

```csharp
// Traditional Controller
[ApiController]
[Route("api/[controller]")]
public class TenantsController : ControllerBase
{
    [HttpGet]
    public async Task<ActionResult<IEnumerable<Tenant>>> GetTenants()
    {
        // Implementation
    }
}

// Minimal API Alternative
app.MapGet("/api/tenants", async (ITenantService service) =>
{
    return await service.GetTenantsAsync();
});
```

### **Dependency Injection with Keyed Services**

```csharp
// .NET 9 Keyed Services
builder.Services.AddKeyedScoped<IPaymentProvider, StripePaymentProvider>("stripe");
builder.Services.AddKeyedScoped<IPaymentProvider, PayPalPaymentProvider>("paypal");

// Usage
public class PaymentService
{
    public PaymentService(
        [FromKeyedServices("stripe")] IPaymentProvider stripeProvider,
        [FromKeyedServices("paypal")] IPaymentProvider paypalProvider)
    {
        // Implementation
    }
}
```

### **Configuration Binding**

```csharp
// Strongly-typed configuration
public class TenantSettings
{
    public const string SectionName = "TenantSettings";
    
    public string DefaultConnectionString { get; set; }
    public int MaxTenantsPerUser { get; set; }
    public bool EnableTenantIsolation { get; set; }
}

// Registration
builder.Services.Configure<TenantSettings>(
    builder.Configuration.GetSection(TenantSettings.SectionName));
```

### **Health Checks**

```csharp
// Comprehensive health checks
builder.Services.AddHealthChecks()
    .AddCheck<DatabaseHealthCheck>("database")
    .AddCheck<TenantHealthCheck>("tenant-resolution")
    .AddCheck<ExternalApiHealthCheck>("external-apis");
```

### **Background Services**

```csharp
public class TenantMaintenanceService : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            await PerformTenantMaintenance();
            await Task.Delay(TimeSpan.FromHours(1), stoppingToken);
        }
    }
}
```

## 🔧 Extensible Design

### **Plugin Architecture**

```csharp
public interface ITenantPlugin
{
    string Name { get; }
    Version Version { get; }
    Task InitializeAsync(Tenant tenant);
    Task<bool> IsEnabledForTenantAsync(string tenantId);
}

public class TenantPluginManager
{
    private readonly List<ITenantPlugin> _plugins = new();
    
    public void RegisterPlugin<T>() where T : class, ITenantPlugin
    {
        // Plugin registration logic
    }
    
    public async Task ExecutePluginsAsync(string tenantId)
    {
        foreach (var plugin in _plugins)
        {
            if (await plugin.IsEnabledForTenantAsync(tenantId))
            {
                // Execute plugin
            }
        }
    }
}
```

### **Event-Driven Architecture**

```csharp
public interface IDomainEvent
{
    DateTime OccurredOn { get; }
    string TenantId { get; }
}

public class TenantCreatedEvent : IDomainEvent
{
    public DateTime OccurredOn { get; set; }
    public string TenantId { get; set; }
    public Tenant Tenant { get; set; }
}

public interface IEventHandler<T> where T : IDomainEvent
{
    Task HandleAsync(T domainEvent);
}
```

### **Strategy Pattern for Tenant Resolution**

```csharp
public interface ITenantResolutionStrategy
{
    Task<string> ResolveTenantIdAsync(HttpContext context);
    int Priority { get; }
}

public class SubdomainTenantResolutionStrategy : ITenantResolutionStrategy
{
    public int Priority => 1;
    
    public async Task<string> ResolveTenantIdAsync(HttpContext context)
    {
        // Subdomain resolution logic
    }
}
```

### **Factory Pattern for Tenant Services**

```csharp
public interface ITenantServiceFactory
{
    T CreateService<T>(string tenantId) where T : class;
}

public class TenantServiceFactory : ITenantServiceFactory
{
    public T CreateService<T>(string tenantId) where T : class
    {
        // Create tenant-specific service instances
    }
}
```

## 📊 Benefits Summary

### **✅ Clean Namespace Structure**
- **Organized Code**: Logical grouping of related functionality
- **Easy Maintenance**: Clear boundaries for changes
- **Team Collaboration**: Multiple developers can work without conflicts
- **Code Reusability**: Shared components in Base namespace

### **✅ Multi-Tenant Support**
- **Data Isolation**: Each tenant's data is completely separate
- **Customization**: Per-tenant branding and features
- **Scalability**: Add new tenants without code changes
- **Security**: Tenant boundaries prevent data leakage

### **✅ Proper Middleware Ordering**
- **Security**: Security middleware runs first
- **Performance**: Optimized request processing
- **Debugging**: Clear execution flow
- **Extensibility**: Easy to add new middleware

### **✅ Modern .NET 9 Patterns**
- **Performance**: Latest .NET optimizations
- **Developer Experience**: Modern C# features
- **Maintainability**: Clean, readable code
- **Future-Proof**: Ready for .NET updates

### **✅ Extensible Design**
- **Plugin System**: Add functionality without core changes
- **Event-Driven**: Loose coupling between components
- **Strategy Pattern**: Flexible tenant resolution
- **Factory Pattern**: Dynamic service creation

This architecture provides a solid foundation for building scalable, maintainable, and secure multi-tenant SaaS applications with modern .NET 9 patterns.
