'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

import { ArrowRight, DollarSign, Clock, Shield, Star } from 'lucide-react'
import Link from 'next/link'

interface CallToActionProps {
  userType: 'buyer' | 'seller'
  isSignedIn: boolean
}

export function CallToAction({ userType, isSignedIn }: CallToActionProps) {
  const buyerContent = {
    title: "Ready to Find Your Dream Home?",
    subtitle: "Join thousands of buyers saving on commission fees",
    description: "Start browsing commission-free properties today and connect directly with sellers across Canada.",
    primaryCta: isSignedIn ? "Browse Properties" : "Start Searching",
    primaryLink: isSignedIn ? "/ca/properties" : "/sign-up",
    secondaryCta: "Learn More",
    secondaryLink: "/ca/how-it-works",
    stats: [
      { icon: DollarSign, value: "$15,000", label: "Average savings" },
      { icon: Clock, value: "24/7", label: "Property access" },
      { icon: Shield, value: "100%", label: "Verified listings" }
    ]
  }

  const sellerContent = {
    title: "Ready to Sell Commission-Free?",
    subtitle: "Keep more of your sale price with SoNoBrokers",
    description: "List your property today and connect directly with qualified buyers without paying realtor commissions.",
    primaryCta: isSignedIn ? "List Your Property" : "Start Selling",
    primaryLink: isSignedIn ? "/ca/list-property" : "/sign-up",
    secondaryCta: "Calculate Savings",
    secondaryLink: "/ca/calculator",
    stats: [
      { icon: DollarSign, value: "5-6%", label: "Commission saved" },
      { icon: Clock, value: "30 days", label: "Average sale time" },
      { icon: Star, value: "4.9/5", label: "Seller satisfaction" }
    ]
  }

  const content = userType === 'buyer' ? buyerContent : sellerContent

  return (
    <section className="py-20 bg-gradient-to-br from-accent/10 via-background to-accent/5">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <Card className="bg-background/80 backdrop-blur border border-border shadow-2xl overflow-hidden">
            <CardContent className="p-0">
              <div className="grid lg:grid-cols-2 gap-0">
                {/* Content Side */}
                <div className="p-8 lg:p-12 space-y-8">
                  <div className="space-y-4">
                    <h2 className="text-3xl lg:text-4xl font-bold text-foreground leading-tight">
                      {content.title}
                    </h2>

                    <p className="text-xl text-accent font-semibold">
                      {content.subtitle}
                    </p>

                    <p className="text-lg text-muted-foreground leading-relaxed">
                      {content.description}
                    </p>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4">
                    {content.stats.map((stat, index) => (
                      <div key={index} className="text-center p-4 rounded-lg bg-accent/10 border border-accent/20">
                        <stat.icon className="h-6 w-6 text-accent mx-auto mb-2" />
                        <p className="text-lg font-bold text-foreground">{stat.value}</p>
                        <p className="text-xs text-muted-foreground">{stat.label}</p>
                      </div>
                    ))}
                  </div>

                  {/* CTA Buttons */}
                  <div className="space-y-4">
                    <Button asChild size="lg" className="w-full text-lg py-6">
                      <Link href={content.primaryLink}>
                        {content.primaryCta}
                        <ArrowRight className="ml-2 h-5 w-5" />
                      </Link>
                    </Button>

                    <Button variant="outline" size="lg" asChild className="w-full text-lg py-6">
                      <Link href={content.secondaryLink}>
                        {content.secondaryCta}
                      </Link>
                    </Button>
                  </div>

                  {/* Trust Indicators */}
                  <div className="pt-4 border-t border-border">
                    <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Shield className="h-4 w-4 text-green-600" />
                        <span>Secure Platform</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-500" />
                        <span>5-Star Rated</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-4 w-4 text-green-600" />
                        <span>No Hidden Fees</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Visual Side */}
                <div className="bg-gradient-to-br from-accent/20 to-accent/10 p-8 lg:p-12 flex items-center justify-center">
                  <div className="text-center space-y-6">
                    <div className="w-32 h-32 bg-accent/20 rounded-full flex items-center justify-center mx-auto">
                      <div className="w-24 h-24 bg-accent rounded-full flex items-center justify-center">
                        {userType === 'buyer' ? (
                          <DollarSign className="h-12 w-12 text-white" />
                        ) : (
                          <Star className="h-12 w-12 text-white" />
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <p className="text-2xl font-bold text-foreground">
                        {userType === 'buyer' ? '10,000+' : '5,000+'}
                      </p>
                      <p className="text-muted-foreground">
                        {userType === 'buyer' ? 'Properties Available' : 'Successful Sales'}
                      </p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-center gap-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star key={star} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Trusted by thousands of Canadians
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Additional Info */}
        <div className="text-center mt-12 space-y-4">
          <p className="text-sm text-muted-foreground">
            {userType === 'buyer'
              ? 'Questions? Our support team is here to help you find the perfect property.'
              : 'Need help getting started? Our team will guide you through the listing process.'
            }
          </p>
          <div className="flex justify-center gap-4">
            <Button variant="ghost" size="sm">
              Contact Support
            </Button>
            <Button variant="ghost" size="sm">
              Live Chat
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
