'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { 
  Calendar, 
  Clock, 
  MapPin, 
  User, 
  Phone, 
  Mail,
  CheckCircle,
  AlertCircle,
  X
} from 'lucide-react'

interface CalendarSchedulingProps {
  propertyId: string
  sellerId: string
  sellerName: string
  propertyTitle: string
  propertyAddress: string
  trigger?: React.ReactNode
}

interface TimeSlot {
  time: string
  available: boolean
  label: string
}

const timeSlots: TimeSlot[] = [
  { time: '09:00', available: true, label: '9:00 AM' },
  { time: '10:00', available: true, label: '10:00 AM' },
  { time: '11:00', available: false, label: '11:00 AM' },
  { time: '12:00', available: true, label: '12:00 PM' },
  { time: '13:00', available: true, label: '1:00 PM' },
  { time: '14:00', available: true, label: '2:00 PM' },
  { time: '15:00', available: false, label: '3:00 PM' },
  { time: '16:00', available: true, label: '4:00 PM' },
  { time: '17:00', available: true, label: '5:00 PM' },
  { time: '18:00', available: true, label: '6:00 PM' },
]

export function CalendarScheduling({
  propertyId,
  sellerId,
  sellerName,
  propertyTitle,
  propertyAddress,
  trigger
}: CalendarSchedulingProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedTime, setSelectedTime] = useState('')
  const [visitType, setVisitType] = useState<'in-person' | 'virtual'>('in-person')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  // Get today's date in YYYY-MM-DD format
  const today = new Date().toISOString().split('T')[0]
  
  // Get date 30 days from now
  const maxDate = new Date()
  maxDate.setDate(maxDate.getDate() + 30)
  const maxDateStr = maxDate.toISOString().split('T')[0]

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const formData = new FormData(e.currentTarget)
      
      // Add selected date and time to form data
      formData.set('preferredVisitDate', selectedDate)
      formData.set('preferredVisitTime', selectedTime)
      formData.set('visitType', visitType)
      
      // Here you would call your contact sharing API
      // For now, we'll simulate a successful submission
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      setIsSubmitted(true)
      
      // Close modal after 3 seconds
      setTimeout(() => {
        setIsOpen(false)
        setIsSubmitted(false)
        setSelectedDate('')
        setSelectedTime('')
      }, 3000)
    } catch (error) {
      console.error('Failed to schedule visit:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const defaultTrigger = (
    <Button variant="outline" size="sm">
      <Calendar className="h-4 w-4 mr-2" />
      Schedule Visit
    </Button>
  )

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Schedule Property Visit
          </DialogTitle>
          <DialogDescription>
            Schedule a visit to view "{propertyTitle}" with {sellerName}
          </DialogDescription>
        </DialogHeader>

        {/* Property Info */}
        <Card className="mb-4">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <MapPin className="h-5 w-5 text-gray-500 mt-0.5" />
              <div>
                <h4 className="font-medium">{propertyTitle}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">{propertyAddress}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {isSubmitted ? (
          /* Success State */
          <div className="text-center py-8">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-green-700 dark:text-green-400 mb-2">
              Visit Request Sent!
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Your visit request has been sent to {sellerName}. They will receive an email with your contact information and preferred time.
            </p>
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <p className="text-sm text-green-800 dark:text-green-200">
                <strong>Next Steps:</strong><br />
                • {sellerName} will contact you to confirm the visit<br />
                • You'll receive a confirmation email<br />
                • Check your email for visit details
              </p>
            </div>
          </div>
        ) : (
          /* Scheduling Form */
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Visit Type Selection */}
            <div className="space-y-3">
              <Label className="text-base font-medium">Visit Type</Label>
              <div className="grid grid-cols-2 gap-3">
                <button
                  type="button"
                  onClick={() => setVisitType('in-person')}
                  className={`p-4 border rounded-lg text-left transition-colors ${
                    visitType === 'in-person'
                      ? 'border-primary bg-primary/5 text-primary'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <MapPin className="h-5 w-5" />
                    <span className="font-medium">In-Person Visit</span>
                  </div>
                  <p className="text-sm text-gray-600">
                    Visit the property in person with the seller
                  </p>
                </button>
                
                <button
                  type="button"
                  onClick={() => setVisitType('virtual')}
                  className={`p-4 border rounded-lg text-left transition-colors ${
                    visitType === 'virtual'
                      ? 'border-primary bg-primary/5 text-primary'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="h-5 w-5" />
                    <span className="font-medium">Virtual Tour</span>
                  </div>
                  <p className="text-sm text-gray-600">
                    Video call tour with the seller
                  </p>
                </button>
              </div>
            </div>

            {/* Date Selection */}
            <div className="space-y-3">
              <Label htmlFor="visitDate" className="text-base font-medium">
                Preferred Date
              </Label>
              <Input
                id="visitDate"
                type="date"
                min={today}
                max={maxDateStr}
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                required
              />
            </div>

            {/* Time Selection */}
            {selectedDate && (
              <div className="space-y-3">
                <Label className="text-base font-medium">Preferred Time</Label>
                <div className="grid grid-cols-3 sm:grid-cols-5 gap-2">
                  {timeSlots.map((slot) => (
                    <button
                      key={slot.time}
                      type="button"
                      disabled={!slot.available}
                      onClick={() => setSelectedTime(slot.time)}
                      className={`p-2 text-sm border rounded-lg transition-colors ${
                        selectedTime === slot.time
                          ? 'border-primary bg-primary text-primary-foreground'
                          : slot.available
                          ? 'border-gray-200 hover:border-gray-300'
                          : 'border-gray-100 bg-gray-50 text-gray-400 cursor-not-allowed'
                      }`}
                    >
                      {slot.label}
                    </button>
                  ))}
                </div>
                {selectedTime && (
                  <p className="text-sm text-green-600">
                    Selected: {timeSlots.find(s => s.time === selectedTime)?.label} on {new Date(selectedDate).toLocaleDateString()}
                  </p>
                )}
              </div>
            )}

            {/* Contact Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="buyerName">Your Name *</Label>
                <Input
                  id="buyerName"
                  name="buyerName"
                  placeholder="Enter your full name"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="buyerEmail">Your Email *</Label>
                <Input
                  id="buyerEmail"
                  name="buyerEmail"
                  type="email"
                  placeholder="Enter your email"
                  required
                />
              </div>
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="buyerPhone">Your Phone *</Label>
                <Input
                  id="buyerPhone"
                  name="buyerPhone"
                  type="tel"
                  placeholder="Enter your phone number"
                  required
                />
              </div>
            </div>

            {/* Additional Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">Additional Notes (Optional)</Label>
              <Textarea
                id="notes"
                name="notes"
                placeholder="Any specific requests or questions about the visit..."
                rows={3}
              />
            </div>

            {/* Hidden Fields */}
            <input type="hidden" name="propertyId" value={propertyId} />
            <input type="hidden" name="sellerId" value={sellerId} />
            <input type="hidden" name="shareType" value="3" /> {/* ScheduleVisit */}

            {/* Submit Button */}
            <div className="flex gap-3">
              <Button
                type="submit"
                disabled={!selectedDate || !selectedTime || isSubmitting}
                className="flex-1"
              >
                {isSubmitting ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    Sending Request...
                  </>
                ) : (
                  <>
                    <Calendar className="h-4 w-4 mr-2" />
                    Send Visit Request
                  </>
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsOpen(false)}
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </div>

            {/* Info */}
            <div className="text-xs text-gray-500 space-y-1">
              <p>• Your contact information will be shared with the seller</p>
              <p>• The seller will contact you to confirm the visit</p>
              <p>• All visits are subject to seller availability</p>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  )
}
