import { NextRequest, NextResponse } from 'next/server'
import { requireAdminAPI } from '@/lib/auth'
import { EnhancedStripeService } from '@/services/stripeService'
// TODO: Migrate to .NET Web API - temporarily disabled
// import prisma from '@/lib/prisma'

// POST /api/admin/stripe/sync - Sync Stripe products and prices (Admin only)
export async function POST(request: NextRequest) {
  // TODO: Migrate to .NET Web API
  return NextResponse.json(
    { error: 'API route temporarily disabled - migrating to .NET Web API' },
    { status: 503 }
  )
}

/* DISABLED - MIGRATE TO .NET WEB API
export async function POST_DISABLED(request: NextRequest) {
  try {
    // Require admin authentication
    const adminUser = await requireAdminAPI()

    if (!adminUser) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }
    
    // Sync products and prices from Stripe
    await EnhancedStripeService.syncProductsAndPrices()
    
    return NextResponse.json({
      success: true,
      message: 'Stripe products and prices synced successfully'
    })
  } catch (error) {
    console.error('Error syncing Stripe data:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to sync Stripe data' },
      { status: 500 }
    )
  }
}

// GET /api/admin/stripe/sync - Get sync status and stats (Admin only)
export async function GET(request: NextRequest) {
  try {
    // Require admin authentication
    const adminUser = await requireAdminAPI()

    if (!adminUser) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }
    
    // Get stats from database
    const [productCount, priceCount, customerCount, paymentCount, invoiceCount, webhookEventCount] = await Promise.all([
      prisma.stripeProduct.count(),
      prisma.stripePrice.count(),
      prisma.stripeCustomer.count(),
      prisma.stripePayment.count(),
      prisma.stripeInvoice.count(),
      prisma.stripeWebhookEvent.count()
    ])
    
    return NextResponse.json({
      success: true,
      data: {
        products: productCount,
        prices: priceCount,
        customers: customerCount,
        payments: paymentCount,
        invoices: invoiceCount,
        webhookEvents: webhookEventCount
      }
    })
  } catch (error) {
    console.error('Error getting Stripe stats:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get Stripe stats' },
      { status: 500 }
    )
  }
}
*/
