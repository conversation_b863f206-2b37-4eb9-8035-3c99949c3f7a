# MicroSaasWebApi Test Suite

This document provides comprehensive information about the test suite for the MicroSaasWebApi project.

## Overview

The test suite is built using XUnit and includes:
- Unit tests for services and business logic
- Integration tests for controllers and API endpoints
- Database integration tests
- Authentication and authorization tests
- Performance tests
- Mock services and test utilities

## Test Structure

```
MicroSaasWebApi.Tests/
├── Common/                     # Shared test utilities and base classes
│   ├── TestBase.cs            # Base class for all tests
│   ├── TestHelpers.cs         # Helper methods and utilities
│   ├── TestDataBuilders.cs    # Bogus data builders for test objects
│   └── TestWebApplicationFactory.cs # Test server factory
├── Services/                   # Unit tests for service classes
│   ├── AdvertiserServiceTests.cs
│   ├── AIPropertyServiceTests.cs
│   ├── CommunicationServiceTests.cs
│   ├── UserServiceTests.cs
│   ├── ProjectServiceTests.cs
│   └── AdminServiceTests.cs
├── Controllers/                # Integration tests for controllers
│   ├── AdvertisersControllerTests.cs
│   ├── AIPropertyControllerTests.cs
│   ├── CommunicationControllerTests.cs
│   └── AuthControllerTests.cs
├── Database/                   # Database integration tests
│   ├── DatabaseTestBase.cs
│   ├── DatabaseIntegrationTests.cs
│   └── StoredProcedureTests.cs
├── Authentication/             # Authentication and authorization tests
│   ├── ClerkAuthServiceTests.cs
│   └── AuthorizationTests.cs
├── Performance/                # Performance and load tests
│   └── PerformanceTests.cs
├── Mocks/                     # Mock services and test doubles
│   └── MockServices.cs
├── Configuration/             # Test configuration utilities
│   └── TestConfiguration.cs
└── README.md                  # This documentation file
```

## Running Tests

### Prerequisites

1. .NET 9 SDK installed
2. PostgreSQL database for integration tests (optional - can use in-memory)
3. Test database connection string configured

### Command Line

```bash
# Run all tests
dotnet test

# Run tests with detailed output
dotnet test --verbosity normal

# Run specific test category
dotnet test --filter Category=Unit
dotnet test --filter Category=Integration

# Run tests in parallel
dotnet test --parallel

# Generate code coverage report
dotnet test --collect:"XPlat Code Coverage"
```

### Visual Studio

1. Open Test Explorer (Test → Test Explorer)
2. Build the solution
3. Run tests from Test Explorer
4. Use filters to run specific test categories

### Test Categories

Tests are categorized using traits:

- **Unit**: Fast, isolated tests with mocked dependencies
- **Integration**: Tests that interact with external systems
- **Database**: Tests that require database connectivity
- **Performance**: Load and performance tests
- **Authentication**: Security and auth-related tests

## Configuration

### Test Settings

Test configuration is managed through:

1. `appsettings.Test.json` - Test-specific settings
2. Environment variables with `TEST_` prefix
3. In-memory configuration for unit tests

### Key Configuration Options

```json
{
  "ConnectionStrings": {
    "SupabaseConnection": "Host=localhost;Port=5432;Database=sonobrokers_test;Username=********;Password=********;"
  },
  "TestSettings": {
    "UseInMemoryDatabase": true,
    "SeedTestData": true,
    "EnableDetailedLogging": false,
    "MockExternalServices": true
  },
  "Clerk": {
    "SecretKey": "test-secret-key",
    "PublishableKey": "test-publishable-key"
  }
}
```

### Environment Variables

For CI/CD environments:

```bash
TEST_DATABASE_URL=********ql://user:pass@localhost:5432/test_db
TEST_CLERK_SECRET_KEY=your_test_clerk_key
TEST_ENABLE_INTEGRATION_TESTS=true
TEST_PARALLEL_EXECUTION=false
TEST_TIMEOUT_SECONDS=300
```

## Test Data Management

### Test Data Builders

Using Bogus library for generating realistic test data:

```csharp
// Generate a test user
var user = TestDataBuilders.Users.ValidUser.Generate();

// Generate multiple advertisers
var advertisers = TestDataBuilders.Advertisers.ValidAdvertiser.Generate(5);

// Generate with specific properties
var request = TestDataBuilders.Advertisers.CreateAdvertiserRequest
    .RuleFor(x => x.Email, "<EMAIL>")
    .Generate();
```

### Database Test Data

- Test data is automatically seeded before tests
- Each test class has isolated test data
- Cleanup is performed after tests complete
- Use `db-test-` prefix for test data IDs

## Mock Services

Mock services are provided for external dependencies:

```csharp
// Use pre-configured mocks
var mockClerkAuth = MockServices.CreateMockClerkAuthService();
var mockUserService = MockServices.CreateMockUserService();

// Configure specific behavior
mockClerkAuth.Setup(x => x.GetUserIdAsync(It.IsAny<HttpContext>()))
    .ReturnsAsync("test-user-id");
```

## Writing Tests

### Unit Test Example

```csharp
[Fact]
public async Task GetUserByIdAsync_WithValidId_ReturnsUser()
{
    // Arrange
    var userId = TestHelpers.GenerateTestId("user");
    var expectedUser = TestDataBuilders.Users.ValidUser.Generate();
    
    _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<User>(
        It.IsAny<string>(), It.IsAny<object>()))
        .ReturnsAsync(expectedUser);

    // Act
    var result = await _userService.GetUserByIdAsync(userId);

    // Assert
    result.Should().NotBeNull();
    result.Id.Should().Be(expectedUser.Id);
}
```

### Integration Test Example

```csharp
[Fact]
public async Task GetAdvertisers_ReturnsSuccessWithAdvertisers()
{
    // Act
    var response = await _client.GetAsync("/api/sonobrokers/advertisers");

    // Assert
    response.StatusCode.Should().Be(HttpStatusCode.OK);
    
    var content = await response.Content.ReadAsStringAsync();
    var result = JsonSerializer.Deserialize<AdvertiserSearchResponse>(content);
    
    result.Should().NotBeNull();
    result.Advertisers.Should().NotBeNull();
}
```

### Database Test Example

```csharp
[Fact]
public async Task User_CRUD_Operations_WorkCorrectly()
{
    // Create
    var userId = TestHelpers.GenerateTestId("crud-user");
    var created = await ExecuteAsync(createSql, createParams);
    created.Should().Be(1);

    // Read
    var user = await QuerySingleOrDefaultAsync<User>(readSql, new { id = userId });
    user.Should().NotBeNull();

    // Update & Delete...
}
```

## Best Practices

### Test Organization

1. **Arrange-Act-Assert**: Structure tests clearly
2. **One assertion per test**: Focus on single behavior
3. **Descriptive names**: Test names should describe the scenario
4. **Test categories**: Use appropriate test traits

### Test Data

1. **Isolated data**: Each test should have independent data
2. **Realistic data**: Use Bogus for generating realistic test data
3. **Cleanup**: Always clean up test data
4. **Deterministic**: Tests should be repeatable

### Mocking

1. **Mock external dependencies**: Database, HTTP clients, external services
2. **Verify interactions**: Check that mocks are called correctly
3. **Setup realistic responses**: Mock responses should match real behavior
4. **Minimal mocking**: Only mock what's necessary

### Performance

1. **Fast unit tests**: Unit tests should run in milliseconds
2. **Reasonable integration tests**: Integration tests under 5 seconds
3. **Parallel execution**: Design tests to run in parallel
4. **Resource cleanup**: Dispose resources properly

## Continuous Integration

### GitHub Actions

```yaml
- name: Run Tests
  run: |
    dotnet test --configuration Release \
                --logger trx \
                --collect:"XPlat Code Coverage" \
                --results-directory ./TestResults

- name: Publish Test Results
  uses: dorny/test-reporter@v1
  if: success() || failure()
  with:
    name: Test Results
    path: './TestResults/*.trx'
    reporter: dotnet-trx
```

### Test Reports

- Test results are published as TRX files
- Code coverage reports generated with Coverlet
- Performance metrics tracked over time
- Failed tests automatically reported

## Troubleshooting

### Common Issues

1. **Database connection failures**
   - Check connection string in test configuration
   - Ensure test database is running
   - Verify permissions

2. **Test data conflicts**
   - Use unique test data IDs
   - Ensure proper cleanup
   - Check for parallel execution issues

3. **Authentication failures**
   - Verify Clerk test keys
   - Check mock authentication setup
   - Ensure proper test headers

4. **Performance test failures**
   - Check system resources
   - Verify network connectivity
   - Review timeout settings

### Debugging Tests

1. **Enable detailed logging**
   ```json
   "TestSettings": {
     "EnableDetailedLogging": true
   }
   ```

2. **Use test output**
   ```csharp
   _output.WriteLine($"Debug info: {value}");
   ```

3. **Breakpoint debugging**
   - Set breakpoints in test code
   - Use Visual Studio debugger
   - Inspect test data and responses

## Contributing

When adding new tests:

1. Follow existing patterns and structure
2. Add appropriate test categories
3. Include both positive and negative test cases
4. Update documentation if needed
5. Ensure tests pass in CI/CD pipeline

## Resources

- [XUnit Documentation](https://xunit.net/)
- [FluentAssertions](https://fluentassertions.com/)
- [Bogus Data Generator](https://github.com/bchavez/Bogus)
- [Moq Mocking Framework](https://github.com/moq/moq4)
- [ASP.NET Core Testing](https://docs.microsoft.com/en-us/aspnet/core/test/)
