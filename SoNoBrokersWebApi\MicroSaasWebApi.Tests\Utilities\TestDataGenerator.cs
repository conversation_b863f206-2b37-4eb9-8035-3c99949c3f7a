using Bogus;
using MicroSaasWebApi.Models.Auth;
using MicroSaasWebApi.Models.SoNoBrokers;
using System.Security.Claims;

namespace MicroSaasWebApi.Tests.Utilities
{
    /// <summary>
    /// Advanced test data generator with realistic scenarios and relationships
    /// </summary>
    public static class TestDataGenerator
    {
        private static readonly Faker _faker = new();

        /// <summary>
        /// Generates a complete user scenario with related data
        /// </summary>
        public static UserScenario GenerateUserScenario(UserRole role = UserRole.USER, SnbUserType userType = SnbUserType.Buyer)
        {
            var user = new User
            {
                Id = GenerateId("user"),
                Email = _faker.Internet.Email(),
                FullName = _faker.Name.FullName(),
                FirstName = _faker.Name.FirstName(),
                LastName = _faker.Name.LastName(),
                Phone = _faker.Phone.PhoneNumber(),
                ClerkUserId = GenerateClerkId(),
                Role = role,
                UserType = userType,
                IsActive = true,
                LoggedIn = false,
                CreatedAt = DateTime.UtcNow.AddDays(-_faker.Random.Int(1, 30)),
                UpdatedAt = DateTime.UtcNow.AddDays(-_faker.Random.Int(0, 7)),
                LastLoginAt = DateTime.UtcNow.AddHours(-_faker.Random.Int(1, 24))
            };

            var properties = new List<Property>();
            var advertisers = new List<Advertiser>();
            var projects = new List<Project>();

            // Generate related data based on user type
            if (userType == SnbUserType.Seller)
            {
                properties = GeneratePropertiesForSeller(user.Id, _faker.Random.Int(1, 5));
            }

            if (_faker.Random.Bool(0.3f)) // 30% chance of being an advertiser
            {
                advertisers.Add(GenerateAdvertiserForUser(user.Id));
            }

            if (role == UserRole.USER && _faker.Random.Bool(0.4f)) // 40% chance of having projects
            {
                projects = GenerateProjectsForUser(user.ClerkUserId, _faker.Random.Int(1, 3));
            }

            return new UserScenario
            {
                User = user,
                Properties = properties,
                Advertisers = advertisers,
                Projects = projects,
                Claims = GenerateClaimsForUser(user)
            };
        }

        /// <summary>
        /// Generates realistic property data with market-appropriate pricing
        /// </summary>
        public static Property GenerateRealisticProperty(string? sellerId = null, string? location = null)
        {
            var propertyTypes = new[] { "Detached House", "Townhouse", "Condominium", "Semi-Detached", "Duplex" };
            var propertyType = _faker.PickRandom(propertyTypes);
            
            var bedrooms = _faker.Random.Int(1, 6);
            var bathrooms = _faker.Random.Decimal(1, 4);
            var sqft = GenerateRealisticSquareFootage(propertyType, bedrooms);
            var price = GenerateRealisticPrice(propertyType, bedrooms, sqft, location);

            return new Property
            {
                Id = GenerateId("prop"),
                Title = GeneratePropertyTitle(propertyType, bedrooms, bathrooms, location),
                Description = GeneratePropertyDescription(propertyType, bedrooms, bathrooms, sqft),
                Price = price,
                PropertyType = propertyType,
                Bedrooms = bedrooms,
                Bathrooms = bathrooms,
                Sqft = sqft,
                SellerId = sellerId ?? GenerateId("user"),
                Status = PropertyStatus.active,
                CreatedAt = DateTime.UtcNow.AddDays(-_faker.Random.Int(1, 90)),
                UpdatedAt = DateTime.UtcNow.AddDays(-_faker.Random.Int(0, 30))
            };
        }

        /// <summary>
        /// Generates advertiser with realistic business data
        /// </summary>
        public static Advertiser GenerateRealisticAdvertiser(string? userId = null, ServiceType? serviceType = null)
        {
            var selectedServiceType = serviceType ?? _faker.PickRandom<ServiceType>();
            var businessName = GenerateBusinessName(selectedServiceType);
            var plan = _faker.PickRandom<AdvertiserPlan>();

            return new Advertiser
            {
                Id = GenerateId("adv"),
                UserId = userId ?? GenerateId("user"),
                BusinessName = businessName,
                ContactName = _faker.Name.FullName(),
                Email = _faker.Internet.Email(businessName.Replace(" ", "").ToLower()),
                Phone = _faker.Phone.PhoneNumber(),
                Website = _faker.Internet.Url(),
                Description = GenerateBusinessDescription(selectedServiceType),
                ServiceType = selectedServiceType,
                ServiceAreas = GenerateServiceAreas(),
                LicenseNumber = GenerateLicenseNumber(selectedServiceType),
                Plan = plan,
                Status = AdvertiserStatus.active,
                IsPremium = plan == AdvertiserPlan.premium,
                IsVerified = _faker.Random.Bool(0.7f),
                Images = GenerateBusinessImages(),
                CreatedAt = DateTime.UtcNow.AddDays(-_faker.Random.Int(1, 365)),
                UpdatedAt = DateTime.UtcNow.AddDays(-_faker.Random.Int(0, 30))
            };
        }

        /// <summary>
        /// Generates test claims for authentication scenarios
        /// </summary>
        public static List<Claim> GenerateTestClaims(string userId, string email, UserRole role, params string[] additionalClaims)
        {
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, userId),
                new(ClaimTypes.Email, email),
                new(ClaimTypes.Role, role.ToString()),
                new("sub", userId),
                new("email_verified", "true"),
                new("iat", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString()),
                new("exp", DateTimeOffset.UtcNow.AddHours(1).ToUnixTimeSeconds().ToString())
            };

            // Add additional claims in pairs (type, value)
            for (int i = 0; i < additionalClaims.Length - 1; i += 2)
            {
                claims.Add(new Claim(additionalClaims[i], additionalClaims[i + 1]));
            }

            return claims;
        }

        /// <summary>
        /// Generates test data for specific scenarios
        /// </summary>
        public static class Scenarios
        {
            public static UserScenario NewUser() => GenerateUserScenario(UserRole.USER, SnbUserType.Buyer);
            
            public static UserScenario ActiveSeller() => GenerateUserScenario(UserRole.USER, SnbUserType.Seller);
            
            public static UserScenario AdminUser() => GenerateUserScenario(UserRole.ADMIN, SnbUserType.Seller);
            
            public static UserScenario ProductManager() => GenerateUserScenario(UserRole.PRODUCT, SnbUserType.Buyer);
            
            public static UserScenario VerifiedAdvertiser()
            {
                var scenario = GenerateUserScenario(UserRole.USER, SnbUserType.Seller);
                scenario.Advertisers.Add(GenerateRealisticAdvertiser(scenario.User.Id, ServiceType.photographer));
                scenario.Advertisers[0].IsVerified = true;
                scenario.Advertisers[0].Plan = AdvertiserPlan.premium;
                return scenario;
            }

            public static List<Property> HighValueProperties(int count = 5)
            {
                return Enumerable.Range(0, count)
                    .Select(_ => GenerateRealisticProperty(location: "Toronto"))
                    .Select(p => { p.Price = _faker.Random.Decimal(800000, 2500000); return p; })
                    .ToList();
            }

            public static List<Property> AffordableProperties(int count = 5)
            {
                return Enumerable.Range(0, count)
                    .Select(_ => GenerateRealisticProperty(location: "Suburbs"))
                    .Select(p => { p.Price = _faker.Random.Decimal(200000, 600000); return p; })
                    .ToList();
            }
        }

        #region Private Helper Methods

        private static string GenerateId(string prefix) => $"{prefix}-{Guid.NewGuid():N}";
        
        private static string GenerateClerkId() => $"user_{_faker.Random.AlphaNumeric(24)}";

        private static List<Property> GeneratePropertiesForSeller(string sellerId, int count)
        {
            return Enumerable.Range(0, count)
                .Select(_ => GenerateRealisticProperty(sellerId))
                .ToList();
        }

        private static Advertiser GenerateAdvertiserForUser(string userId)
        {
            return GenerateRealisticAdvertiser(userId);
        }

        private static List<Project> GenerateProjectsForUser(string userClerkId, int count)
        {
            return Enumerable.Range(0, count)
                .Select(_ => new Project
                {
                    Id = GenerateId("proj"),
                    ConnectionId = _faker.Random.AlphaNumeric(20),
                    WebhookId = _faker.Random.AlphaNumeric(20),
                    ScenarioId = _faker.Random.AlphaNumeric(20),
                    UserClerkId = userClerkId,
                    WebhookLink = _faker.Internet.Url(),
                    AssistantId = _faker.Random.AlphaNumeric(20),
                    Type = _faker.PickRandom("automation", "integration", "workflow"),
                    Status = _faker.PickRandom("active", "inactive", "pending"),
                    CreatedAt = DateTime.UtcNow.AddDays(-_faker.Random.Int(1, 60)),
                    UpdatedAt = DateTime.UtcNow.AddDays(-_faker.Random.Int(0, 30))
                })
                .ToList();
        }

        private static List<Claim> GenerateClaimsForUser(User user)
        {
            return GenerateTestClaims(user.ClerkUserId, user.Email, user.Role);
        }

        private static int GenerateRealisticSquareFootage(string propertyType, int bedrooms)
        {
            return propertyType switch
            {
                "Condominium" => _faker.Random.Int(500, 1500),
                "Townhouse" => _faker.Random.Int(1000, 2500),
                "Detached House" => _faker.Random.Int(1200, 4000),
                "Semi-Detached" => _faker.Random.Int(1000, 3000),
                "Duplex" => _faker.Random.Int(800, 2000),
                _ => _faker.Random.Int(800, 3000)
            } + (bedrooms * 200); // Add space per bedroom
        }

        private static decimal GenerateRealisticPrice(string propertyType, int bedrooms, int sqft, string? location)
        {
            var basePrice = propertyType switch
            {
                "Condominium" => 400000,
                "Townhouse" => 600000,
                "Detached House" => 800000,
                "Semi-Detached" => 700000,
                "Duplex" => 550000,
                _ => 500000
            };

            var locationMultiplier = location?.ToLower() switch
            {
                "toronto" => 1.5m,
                "vancouver" => 1.4m,
                "calgary" => 1.1m,
                "suburbs" => 0.8m,
                _ => 1.0m
            };

            var bedroomBonus = bedrooms * 50000;
            var sqftValue = sqft * _faker.Random.Decimal(200, 400);

            return (basePrice + bedroomBonus + sqftValue) * locationMultiplier;
        }

        private static string GeneratePropertyTitle(string propertyType, int bedrooms, decimal bathrooms, string? location)
        {
            var locationPart = location != null ? $" in {location}" : "";
            return $"Beautiful {bedrooms} Bed, {bathrooms} Bath {propertyType}{locationPart}";
        }

        private static string GeneratePropertyDescription(string propertyType, int bedrooms, decimal bathrooms, int sqft)
        {
            var features = new[] { "modern kitchen", "hardwood floors", "updated bathrooms", "large windows", "private parking", "garden", "fireplace" };
            var selectedFeatures = _faker.PickRandom(features, _faker.Random.Int(2, 4));
            
            return $"This stunning {propertyType.ToLower()} features {bedrooms} bedrooms and {bathrooms} bathrooms across {sqft} square feet. " +
                   $"Highlights include {string.Join(", ", selectedFeatures)}. Perfect for families or professionals seeking quality living space.";
        }

        private static string GenerateBusinessName(ServiceType serviceType)
        {
            var prefixes = serviceType switch
            {
                ServiceType.photographer => new[] { "Elite", "Professional", "Creative", "Premier", "Artistic" },
                ServiceType.lawyer => new[] { "Trusted", "Expert", "Professional", "Premier", "Reliable" },
                ServiceType.inspector => new[] { "Thorough", "Professional", "Certified", "Expert", "Reliable" },
                _ => new[] { "Professional", "Expert", "Quality", "Premier", "Trusted" }
            };

            var suffixes = serviceType switch
            {
                ServiceType.photographer => new[] { "Photography", "Photo Services", "Visual Solutions", "Imaging" },
                ServiceType.lawyer => new[] { "Law Firm", "Legal Services", "Legal Group", "Law Office" },
                ServiceType.inspector => new[] { "Inspections", "Inspection Services", "Property Inspectors", "Home Inspections" },
                _ => new[] { "Services", "Solutions", "Group", "Company" }
            };

            return $"{_faker.PickRandom(prefixes)} {_faker.PickRandom(suffixes)}";
        }

        private static string GenerateBusinessDescription(ServiceType serviceType)
        {
            return serviceType switch
            {
                ServiceType.photographer => "Professional real estate photography services with high-quality images, virtual tours, and drone photography to showcase your property in the best light.",
                ServiceType.lawyer => "Experienced real estate lawyers providing comprehensive legal services for property transactions, contracts, and closing procedures.",
                ServiceType.inspector => "Certified home inspection services providing detailed reports on property condition, safety, and potential issues.",
                _ => "Professional services tailored to meet your real estate needs with expertise and reliability."
            };
        }

        private static string[] GenerateServiceAreas()
        {
            var cities = new[] { "Toronto", "Vancouver", "Calgary", "Ottawa", "Montreal", "Edmonton", "Winnipeg", "Hamilton", "Mississauga", "Brampton" };
            return _faker.PickRandom(cities, _faker.Random.Int(2, 5)).ToArray();
        }

        private static string GenerateLicenseNumber(ServiceType serviceType)
        {
            var prefix = serviceType switch
            {
                ServiceType.photographer => "PH",
                ServiceType.lawyer => "LAW",
                ServiceType.inspector => "INS",
                _ => "LIC"
            };
            return $"{prefix}{_faker.Random.Number(100000, 999999)}";
        }

        private static string[] GenerateBusinessImages()
        {
            return Enumerable.Range(0, _faker.Random.Int(2, 5))
                .Select(_ => _faker.Internet.Url())
                .ToArray();
        }

        #endregion
    }

    /// <summary>
    /// Complete user scenario with related data
    /// </summary>
    public class UserScenario
    {
        public User User { get; set; } = null!;
        public List<Property> Properties { get; set; } = new();
        public List<Advertiser> Advertisers { get; set; } = new();
        public List<Project> Projects { get; set; } = new();
        public List<Claim> Claims { get; set; } = new();
    }
}
