using System.ComponentModel.DataAnnotations;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    public class CreateAdvertiserRequest
    {
        [Required]
        public string BusinessName { get; set; } = string.Empty;

        public string? ContactName { get; set; }

        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        public string? Phone { get; set; }

        public string? Website { get; set; }

        public string? Description { get; set; }

        [Required]
        public ServiceType ServiceType { get; set; }

        public string[] ServiceAreas { get; set; } = Array.Empty<string>();

        public string? LicenseNumber { get; set; }

        [Required]
        public AdvertiserPlan Plan { get; set; }

        public string[] Images { get; set; } = Array.Empty<string>();
    }

    public class UpdateAdvertiserRequest
    {
        [Required]
        public string Id { get; set; } = string.Empty;

        public string? BusinessName { get; set; }

        public string? ContactName { get; set; }

        [EmailAddress]
        public string? Email { get; set; }

        public string? Phone { get; set; }

        public string? Website { get; set; }

        public string? Description { get; set; }

        public ServiceType? ServiceType { get; set; }

        public string[]? ServiceAreas { get; set; }

        public string? LicenseNumber { get; set; }

        public AdvertiserPlan? Plan { get; set; }

        public string[]? Images { get; set; }
    }

    public class AdvertiserResponse
    {
        public string Id { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string? ServiceProviderId { get; set; }
        public string BusinessName { get; set; } = string.Empty;
        public string? ContactName { get; set; }
        public string Email { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Website { get; set; }
        public string? Description { get; set; }
        public ServiceType ServiceType { get; set; }
        public string[] ServiceAreas { get; set; } = Array.Empty<string>();
        public string? LicenseNumber { get; set; }
        public AdvertiserPlan Plan { get; set; }
        public AdvertiserStatus Status { get; set; }
        public DateTime? FeaturedUntil { get; set; }
        public bool IsPremium { get; set; }
        public bool IsVerified { get; set; }
        public string[] Images { get; set; } = Array.Empty<string>();
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class AdvertiserSearchRequest
    {
        public int Page { get; set; } = 1;
        public int Limit { get; set; } = 20;
        public ServiceType? ServiceType { get; set; }
        public string? Location { get; set; }
        public string? SortBy { get; set; } = "premium";
        public bool? Verified { get; set; }
        public bool? Premium { get; set; }
    }

    public class AdvertiserSearchResponse
    {
        public List<AdvertiserResponse> Advertisers { get; set; } = new();
        public int Total { get; set; }
        public int Page { get; set; }
        public int TotalPages { get; set; }
        public bool HasMore { get; set; }
    }

    public class AdvertiserSubscriptionRequest
    {
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        [Required]
        public AdvertiserPlan Plan { get; set; }

        [Required]
        public string StripePriceId { get; set; } = string.Empty;

        [Required]
        public string SuccessUrl { get; set; } = string.Empty;

        [Required]
        public string CancelUrl { get; set; } = string.Empty;
    }

    public class AdvertiserPlanFeatures
    {
        public AdvertiserPlan Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Price { get; set; } = string.Empty;
        public string Period { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string[] Features { get; set; } = Array.Empty<string>();
        public bool Popular { get; set; } = false;
        public string? StripePriceId { get; set; }
    }
}
