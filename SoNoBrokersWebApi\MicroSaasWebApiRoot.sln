﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33110.190
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MicroSaasWebApi.App", "MicroSaasWebApi.App\MicroSaasWebApi.App.csproj", "{C8BEDD5A-3CD9-4EF2-B7FB-B0419DD347E8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MicroSaasWebApi.Config", "MicroSaasWebApi.Config\MicroSaasWebApi.Config.csproj", "{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C8BEDD5A-3CD9-4EF2-B7FB-B0419DD347E8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C8BEDD5A-3CD9-4EF2-B7FB-B0419DD347E8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C8BEDD5A-3CD9-4EF2-B7FB-B0419DD347E8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C8BEDD5A-3CD9-4EF2-B7FB-B0419DD347E8}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {D8612FD2-E42B-4BD9-BC24-35B073156432}
	EndGlobalSection
EndGlobal
