import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { Country } from '@/types'

interface PageProps {
  params: Promise<{
    country: string
  }>
}

export default async function CountryDashboardPage({ params }: PageProps) {
  const { userId } = await auth()

  if (!userId) {
    redirect('/sign-in')
  }

  const resolvedParams = await params

  // Validate country
  const validCountries = Object.values(Country).map(c => c.toLowerCase())
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/dashboard')
  }

  // Redirect to the main dashboard with country context
  redirect(`/dashboard?country=${country}`)
}

export async function generateMetadata({ params }: PageProps) {
  const resolvedParams = await params
  const countryDisplay = resolvedParams.country.toUpperCase()

  return {
    title: `Dashboard - ${countryDisplay} | SoNoBrokers`,
    description: `Your personalized dashboard for ${countryDisplay} real estate activities.`,
  }
}
