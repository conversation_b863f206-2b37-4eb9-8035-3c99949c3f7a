using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MicroSaasWebApi.Services.Auth.Interfaces;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using MicroSaasWebApi.Tests.Mocks;
using System.Text.Json;

namespace MicroSaasWebApi.Tests.Utilities
{
    /// <summary>
    /// Utilities for setting up test environments with different configurations
    /// </summary>
    public static class TestEnvironmentSetup
    {
        /// <summary>
        /// Creates a test web application factory with custom configuration
        /// </summary>
        public static TestWebApplicationFactory<Program> CreateTestFactory(Action<IServiceCollection>? configureServices = null)
        {
            return new TestWebApplicationFactory<Program>(configureServices);
        }

        /// <summary>
        /// Creates a test factory with mocked external services
        /// </summary>
        public static TestWebApplicationFactory<Program> CreateMockedFactory()
        {
            return CreateTestFactory(services =>
            {
                // Replace real services with mocks
                ReplaceService<IClerkAuthService>(services, MockServices.CreateMockClerkAuthService().Object);
                ReplaceService<IUserService>(services, MockServices.CreateMockUserService().Object);
                ReplaceService<IAdvertiserService>(services, MockServices.CreateMockAdvertiserService().Object);
                ReplaceService<IAIPropertyService>(services, MockServices.CreateMockAIPropertyService().Object);
                ReplaceService<ICommunicationService>(services, MockServices.CreateMockCommunicationService().Object);
                ReplaceService<IProjectService>(services, MockServices.CreateMockProjectService().Object);
                ReplaceService<IAdminService>(services, MockServices.CreateMockAdminService().Object);
            });
        }

        /// <summary>
        /// Creates a test factory for integration testing with real database
        /// </summary>
        public static TestWebApplicationFactory<Program> CreateIntegrationFactory()
        {
            return CreateTestFactory(services =>
            {
                // Configure for integration testing
                services.Configure<TestSettings>(options =>
                {
                    options.UseInMemoryDatabase = false;
                    options.SeedTestData = true;
                    options.MockExternalServices = false;
                });
            });
        }

        /// <summary>
        /// Creates a test factory for performance testing
        /// </summary>
        public static TestWebApplicationFactory<Program> CreatePerformanceFactory()
        {
            return CreateTestFactory(services =>
            {
                // Configure for performance testing
                services.Configure<TestSettings>(options =>
                {
                    options.UseInMemoryDatabase = true;
                    options.MockExternalServices = true;
                    options.EnableDetailedLogging = false;
                });

                // Reduce logging for performance tests
                services.AddLogging(builder => builder.SetMinimumLevel(LogLevel.Warning));
            });
        }

        /// <summary>
        /// Creates a test factory for security testing
        /// </summary>
        public static TestWebApplicationFactory<Program> CreateSecurityFactory()
        {
            return CreateTestFactory(services =>
            {
                // Configure for security testing
                services.Configure<TestSettings>(options =>
                {
                    options.UseInMemoryDatabase = true;
                    options.MockExternalServices = true;
                    options.EnableDetailedLogging = true;
                });

                // Add security-focused configuration
                services.PostConfigure<SecuritySettings>(options =>
                {
                    options.RequireHttps = true;
                    options.EnableRateLimiting = true;
                });
            });
        }

        /// <summary>
        /// Sets up test configuration with environment-specific settings
        /// </summary>
        public static IConfiguration CreateTestConfiguration(TestEnvironment environment = TestEnvironment.Unit)
        {
            var configBuilder = new ConfigurationBuilder();

            // Add base test configuration
            configBuilder.AddInMemoryCollection(GetBaseTestConfiguration());

            // Add environment-specific configuration
            configBuilder.AddInMemoryCollection(GetEnvironmentConfiguration(environment));

            // Add environment variables
            configBuilder.AddEnvironmentVariables("TEST_");

            return configBuilder.Build();
        }

        /// <summary>
        /// Sets up test logging with appropriate levels
        /// </summary>
        public static IServiceCollection AddTestLogging(this IServiceCollection services, LogLevel minimumLevel = LogLevel.Information)
        {
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddConsole();
                builder.AddDebug();
                builder.SetMinimumLevel(minimumLevel);
                
                // Add test-specific logging configuration
                builder.AddFilter("Microsoft.AspNetCore", LogLevel.Warning);
                builder.AddFilter("Microsoft.EntityFrameworkCore", LogLevel.Warning);
                builder.AddFilter("System.Net.Http.HttpClient", LogLevel.Warning);
            });

            return services;
        }

        /// <summary>
        /// Configures test database settings
        /// </summary>
        public static IServiceCollection ConfigureTestDatabase(this IServiceCollection services, bool useInMemory = true)
        {
            if (useInMemory)
            {
                // Configure in-memory database for fast tests
                services.Configure<DatabaseSettings>(options =>
                {
                    options.UseInMemoryDatabase = true;
                    options.ConnectionString = "InMemory";
                });
            }
            else
            {
                // Configure real database for integration tests
                services.Configure<DatabaseSettings>(options =>
                {
                    options.UseInMemoryDatabase = false;
                    options.ConnectionString = Environment.GetEnvironmentVariable("TEST_DATABASE_URL") 
                        ?? "Host=localhost;Port=5432;Database=sonobrokers_test;Username=********;Password=********;";
                });
            }

            return services;
        }

        /// <summary>
        /// Configures test authentication settings
        /// </summary>
        public static IServiceCollection ConfigureTestAuthentication(this IServiceCollection services)
        {
            services.Configure<AuthenticationSettings>(options =>
            {
                options.ClerkSecretKey = "test-clerk-secret";
                options.ClerkPublishableKey = "test-clerk-publishable";
                options.JwtExpirationMinutes = 60;
                options.RequireEmailVerification = false;
            });

            return services;
        }

        /// <summary>
        /// Waits for test environment to be ready
        /// </summary>
        public static async Task WaitForEnvironmentReadyAsync(TestWebApplicationFactory<Program> factory, TimeSpan? timeout = null)
        {
            timeout ??= TimeSpan.FromSeconds(30);
            var client = factory.CreateClient();
            var endTime = DateTime.UtcNow.Add(timeout.Value);

            while (DateTime.UtcNow < endTime)
            {
                try
                {
                    var response = await client.GetAsync("/health");
                    if (response.IsSuccessStatusCode)
                        return;
                }
                catch
                {
                    // Continue waiting
                }

                await Task.Delay(TimeSpan.FromSeconds(1));
            }

            throw new TimeoutException($"Test environment not ready within {timeout}");
        }

        /// <summary>
        /// Creates test data for specific scenarios
        /// </summary>
        public static async Task SeedTestDataAsync(TestWebApplicationFactory<Program> factory, TestDataScenario scenario)
        {
            using var scope = factory.Services.CreateScope();
            var dbUtilities = new DatabaseTestUtilities(scope.ServiceProvider.GetRequiredService<IConfiguration>());

            switch (scenario)
            {
                case TestDataScenario.BasicUsers:
                    await dbUtilities.SeedUsersAsync(5);
                    break;

                case TestDataScenario.UsersWithProperties:
                    var users = await dbUtilities.SeedUsersAsync(3);
                    foreach (var user in users)
                    {
                        await dbUtilities.SeedPropertiesAsync(user.Id, 2);
                    }
                    break;

                case TestDataScenario.CompleteScenario:
                    await dbUtilities.SeedUserScenarioAsync();
                    await dbUtilities.SeedAdvertisersAsync(5);
                    break;

                case TestDataScenario.PerformanceData:
                    await dbUtilities.SeedUsersAsync(100);
                    var performanceUsers = await dbUtilities.SeedUsersAsync(10);
                    foreach (var user in performanceUsers)
                    {
                        await dbUtilities.SeedPropertiesAsync(user.Id, 10);
                    }
                    break;
            }
        }

        #region Private Helper Methods

        private static void ReplaceService<T>(IServiceCollection services, T implementation) where T : class
        {
            var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(T));
            if (descriptor != null)
            {
                services.Remove(descriptor);
            }
            services.AddSingleton(implementation);
        }

        private static Dictionary<string, string?> GetBaseTestConfiguration()
        {
            return new Dictionary<string, string?>
            {
                ["ConnectionStrings:DefaultConnection"] = "Host=localhost;Port=5432;Database=sonobrokers_test;Username=********;Password=********;",
                ["ConnectionStrings:SupabaseConnection"] = "Host=localhost;Port=5432;Database=sonobrokers_test;Username=********;Password=********;",
                ["Clerk:SecretKey"] = "test-secret-key",
                ["Clerk:PublishableKey"] = "test-publishable-key",
                ["Logging:LogLevel:Default"] = "Information",
                ["TestSettings:UseInMemoryDatabase"] = "true",
                ["TestSettings:SeedTestData"] = "true",
                ["TestSettings:MockExternalServices"] = "true"
            };
        }

        private static Dictionary<string, string?> GetEnvironmentConfiguration(TestEnvironment environment)
        {
            return environment switch
            {
                TestEnvironment.Unit => new Dictionary<string, string?>
                {
                    ["TestSettings:UseInMemoryDatabase"] = "true",
                    ["TestSettings:MockExternalServices"] = "true",
                    ["Logging:LogLevel:Default"] = "Warning"
                },
                TestEnvironment.Integration => new Dictionary<string, string?>
                {
                    ["TestSettings:UseInMemoryDatabase"] = "false",
                    ["TestSettings:MockExternalServices"] = "false",
                    ["Logging:LogLevel:Default"] = "Information"
                },
                TestEnvironment.Performance => new Dictionary<string, string?>
                {
                    ["TestSettings:UseInMemoryDatabase"] = "true",
                    ["TestSettings:MockExternalServices"] = "true",
                    ["Logging:LogLevel:Default"] = "Error"
                },
                TestEnvironment.Security => new Dictionary<string, string?>
                {
                    ["TestSettings:UseInMemoryDatabase"] = "true",
                    ["TestSettings:MockExternalServices"] = "true",
                    ["Security:RequireHttps"] = "true",
                    ["Security:EnableRateLimiting"] = "true"
                },
                _ => new Dictionary<string, string?>()
            };
        }

        #endregion
    }

    /// <summary>
    /// Test environment types
    /// </summary>
    public enum TestEnvironment
    {
        Unit,
        Integration,
        Performance,
        Security
    }

    /// <summary>
    /// Test data scenarios
    /// </summary>
    public enum TestDataScenario
    {
        BasicUsers,
        UsersWithProperties,
        CompleteScenario,
        PerformanceData
    }

    /// <summary>
    /// Test settings configuration
    /// </summary>
    public class TestSettings
    {
        public bool UseInMemoryDatabase { get; set; } = true;
        public bool SeedTestData { get; set; } = true;
        public bool MockExternalServices { get; set; } = true;
        public bool EnableDetailedLogging { get; set; } = false;
        public bool CleanupAfterTests { get; set; } = true;
        public int TestTimeout { get; set; } = 300;
    }

    /// <summary>
    /// Database settings for testing
    /// </summary>
    public class DatabaseSettings
    {
        public bool UseInMemoryDatabase { get; set; } = true;
        public string ConnectionString { get; set; } = string.Empty;
        public int CommandTimeout { get; set; } = 30;
    }

    /// <summary>
    /// Authentication settings for testing
    /// </summary>
    public class AuthenticationSettings
    {
        public string ClerkSecretKey { get; set; } = string.Empty;
        public string ClerkPublishableKey { get; set; } = string.Empty;
        public int JwtExpirationMinutes { get; set; } = 60;
        public bool RequireEmailVerification { get; set; } = false;
    }

    /// <summary>
    /// Security settings for testing
    /// </summary>
    public class SecuritySettings
    {
        public bool RequireHttps { get; set; } = false;
        public bool EnableRateLimiting { get; set; } = false;
        public string[] AllowedOrigins { get; set; } = Array.Empty<string>();
    }
}
