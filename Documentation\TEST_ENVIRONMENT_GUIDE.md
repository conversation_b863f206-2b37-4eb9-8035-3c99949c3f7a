# SoNoBrokers Test Environment Configuration Guide

## 🎯 Overview

This guide covers the comprehensive test environment setup for both the React frontend and .NET Core Web API backend, including automated testing, CI/CD integration, and development workflows.

## 🏗️ Test Environment Architecture

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│   React Frontend    │    │   .NET Core API     │    │   Test Database     │
│   Jest + RTL        │◄──►│   XUnit + Moq       │◄──►│   PostgreSQL        │
│   Playwright E2E    │    │   Integration Tests │    │   Test Data         │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
           │                           │                           │
           ▼                           ▼                           ▼
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│   Mock Services     │    │   Test Utilities    │    │   CI/CD Pipeline    │
│   API Mocking       │    │   Data Builders     │    │   GitHub Actions    │
│   Auth Mocking      │    │   Test Helpers      │    │   Automated Tests   │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- .NET 9.0+
- PostgreSQL 15+
- Docker (optional)

### 1. Setup React Test Environment

```bash
cd SoNoBrokers

# Install dependencies
npm install

# Install additional test dependencies
npm install --save-dev @testing-library/react @testing-library/jest-dom
npm install --save-dev @testing-library/user-event jest-environment-jsdom
npm install --save-dev @playwright/test jest-junit jest-html-reporters

# Run tests
npm run test              # Run all tests
npm run test:watch        # Run tests in watch mode
npm run test:ci           # Run tests for CI
npm run test:coverage     # Run tests with coverage
npm run test:e2e          # Run E2E tests
```

### 2. Setup .NET Core Test Environment

```bash
cd SoNoBrokersWebApi

# Restore packages
dotnet restore

# Run tests
dotnet test                                    # Run all tests
dotnet test --logger trx                      # Run with TRX output
dotnet test --collect:"XPlat Code Coverage"   # Run with coverage
dotnet test --filter Category=Unit            # Run unit tests only
dotnet test --filter Category=Integration     # Run integration tests only
```

### 3. Setup Test Database

```bash
# Using Docker
docker run --name ********-test -e POSTGRES_PASSWORD=******** -e POSTGRES_DB=sonobrokers_test -p 5432:5432 -d ********:15

# Or using local PostgreSQL
createdb sonobrokers_test
```

## 📋 Test Configuration

### React Test Configuration

#### `jest.config.js`
- **Test Environment**: jsdom for React components
- **Setup Files**: `jest.setup.js` for global mocks
- **Module Mapping**: Absolute imports with `@/` prefix
- **Coverage**: 70% threshold for all metrics
- **Reporters**: JUnit XML, HTML reports, console

#### `jest.setup.js`
- **Global Mocks**: Next.js router, Clerk auth, API client
- **Test Utilities**: Mock data creators, async helpers
- **Environment Setup**: localStorage, fetch, window objects

### .NET Core Test Configuration

#### `appsettings.Test.json`
- **Database**: Test PostgreSQL connection
- **Authentication**: Test Clerk keys
- **External Services**: Mocked endpoints
- **Feature Flags**: Test-specific features
- **Logging**: Detailed test logging

#### `TestConfiguration.cs`
- **Service Registration**: Test-specific services
- **Mock Services**: External API mocking
- **Database Setup**: Test database management
- **Environment Validation**: Required settings check

## 🧪 Test Types and Structure

### 1. Unit Tests

#### React Unit Tests
```typescript
// src/components/__tests__/PropertyCard.test.tsx
import { render, screen } from '@testing-library/react'
import { PropertyCard } from '../PropertyCard'

describe('PropertyCard', () => {
  it('renders property information correctly', () => {
    const mockProperty = testUtils.createMockProperty()
    render(<PropertyCard property={mockProperty} />)
    
    expect(screen.getByText(mockProperty.title)).toBeInTheDocument()
    expect(screen.getByText(`$${mockProperty.price.toLocaleString()}`)).toBeInTheDocument()
  })
})
```

#### .NET Core Unit Tests
```csharp
// MicroSaasWebApi.Tests/Services/PropertyServiceTests.cs
[Test]
public async Task GetPropertiesAsync_WithValidFilters_ReturnsFilteredProperties()
{
    // Arrange
    var mockContext = new Mock<IDapperDbContext>();
    var service = new PropertyService(mockContext.Object, _logger);
    
    // Act
    var result = await service.GetPropertiesAsync(new PropertySearchParams());
    
    // Assert
    Assert.That(result, Is.Not.Null);
    Assert.That(result.Properties.Count, Is.GreaterThan(0));
}
```

### 2. Integration Tests

#### React Integration Tests
```typescript
// tests/integration/api-integration.test.ts
describe('API Integration', () => {
  it('should fetch properties from API', async () => {
    // Mock API response
    global.fetch.mockResolvedValueOnce(
      testUtils.createMockApiResponse([testUtils.createMockProperty()])
    )
    
    const properties = await getProperties()
    expect(properties).toHaveLength(1)
  })
})
```

#### .NET Core Integration Tests
```csharp
// MicroSaasWebApi.Tests/Integration/PropertiesControllerTests.cs
[Test]
public async Task GetProperties_ReturnsOkWithProperties()
{
    // Arrange
    using var factory = new TestWebApplicationFactory();
    var client = factory.CreateClient();
    
    // Act
    var response = await client.GetAsync("/api/sonobrokers/properties");
    
    // Assert
    response.StatusCode.Should().Be(HttpStatusCode.OK);
}
```

### 3. End-to-End Tests

#### Playwright E2E Tests
```typescript
// tests/e2e/property-search.spec.ts
import { test, expect } from '@playwright/test'

test('user can search for properties', async ({ page }) => {
  await page.goto('/')
  await page.fill('[data-testid=search-input]', 'Toronto')
  await page.click('[data-testid=search-button]')
  
  await expect(page.locator('[data-testid=property-card]')).toBeVisible()
})
```

## 🔧 Test Utilities and Helpers

### React Test Utilities

#### Mock Data Creators
```typescript
// Available in jest.setup.js
global.testUtils = {
  createMockUser: (overrides = {}) => ({ /* mock user */ }),
  createMockProperty: (overrides = {}) => ({ /* mock property */ }),
  createMockApiResponse: (data, status = 200) => ({ /* mock response */ }),
  waitFor: (callback, timeout = 5000) => { /* async helper */ }
}
```

#### Custom Render Function
```typescript
// tests/utils/test-utils.tsx
export function renderWithProviders(ui: React.ReactElement, options = {}) {
  return render(ui, {
    wrapper: ({ children }) => (
      <ClerkProvider>
        <ThemeProvider>
          {children}
        </ThemeProvider>
      </ClerkProvider>
    ),
    ...options,
  })
}
```

### .NET Core Test Utilities

#### Test Data Builders
```csharp
// MicroSaasWebApi.Tests/Builders/PropertyBuilder.cs
public class PropertyBuilder
{
    public Property Build() => new Property { /* default values */ };
    public PropertyBuilder WithTitle(string title) { /* fluent API */ }
    public PropertyBuilder WithPrice(decimal price) { /* fluent API */ }
}
```

#### Database Test Helpers
```csharp
// MicroSaasWebApi.Tests/Helpers/DatabaseTestHelper.cs
public static class DatabaseTestHelper
{
    public static async Task SeedTestDataAsync(IDapperDbContext context)
    {
        // Seed test data
    }
    
    public static async Task CleanupTestDataAsync(IDapperDbContext context)
    {
        // Cleanup test data
    }
}
```

## 🚀 CI/CD Integration

### GitHub Actions Workflow

The test environment includes a comprehensive GitHub Actions workflow:

#### Jobs:
1. **API Tests** - .NET Core unit and integration tests
2. **Frontend Tests** - React unit tests and linting
3. **Integration Tests** - Full stack integration testing
4. **E2E Tests** - Playwright end-to-end tests (on main branch)
5. **Test Summary** - Aggregated test results

#### Features:
- **Parallel Execution** - Tests run in parallel for speed
- **Test Database** - PostgreSQL service container
- **Coverage Reports** - Uploaded to Codecov
- **Test Results** - JUnit XML and HTML reports
- **Artifact Storage** - Test results and coverage data

### Environment Variables

#### Required Secrets:
```bash
TEST_CLERK_SECRET_KEY=sk_test_your_test_key
TEST_CLERK_PUBLISHABLE_KEY=pk_test_your_test_key
CODECOV_TOKEN=your_codecov_token
```

#### Environment Variables:
```bash
NODE_ENV=test
ASPNETCORE_ENVIRONMENT=Test
TEST_DATABASE_URL=Host=localhost;Port=5432;Database=sonobrokers_test;Username=********;Password=********;
TEST_ENABLE_INTEGRATION_TESTS=true
API_AVAILABLE=true
```

## 📊 Test Coverage and Reporting

### Coverage Thresholds

#### React Frontend:
- **Lines**: 70%
- **Functions**: 70%
- **Branches**: 70%
- **Statements**: 70%

#### .NET Core API:
- **Lines**: 80%
- **Branches**: 75%
- **Methods**: 80%

### Reports Generated:
- **HTML Coverage Report** - `coverage/index.html`
- **JUnit XML** - `test-results/junit.xml`
- **Test Summary** - `test-results/test-summary.json`
- **Cobertura XML** - For Codecov integration

## 🛠️ Development Workflow

### Running Tests During Development

#### Watch Mode (React)
```bash
npm run test:watch
# Automatically re-runs tests when files change
```

#### Continuous Testing (.NET)
```bash
dotnet watch test
# Re-runs tests when code changes
```

### Debugging Tests

#### React Tests
```bash
npm run test:debug
# Runs tests with Node.js debugger
```

#### .NET Tests
```bash
dotnet test --logger "console;verbosity=detailed"
# Detailed test output for debugging
```

### Pre-commit Testing

#### Git Hooks (recommended)
```bash
# Install husky for git hooks
npm install --save-dev husky

# Add pre-commit hook
npx husky add .husky/pre-commit "npm run test:ci && cd SoNoBrokersWebApi && dotnet test"
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Database Connection Errors
```bash
# Check PostgreSQL is running
pg_isready -h localhost -p 5432

# Reset test database
dropdb sonobrokers_test && createdb sonobrokers_test
```

#### 2. Port Conflicts
```bash
# Check what's using port 8080
lsof -i :8080

# Kill process if needed
kill -9 <PID>
```

#### 3. Mock Service Issues
```bash
# Clear Jest cache
npm run test:clear-cache

# Reset mock state
jest.clearAllMocks()
```

#### 4. API Integration Test Failures
```bash
# Verify API is running
curl http://localhost:8080/health

# Check test configuration
cat SoNoBrokersWebApi/MicroSaasWebApi.Tests/appsettings.Test.json
```

### Performance Optimization

#### Parallel Test Execution
```bash
# React tests
npm run test -- --maxWorkers=4

# .NET tests
dotnet test --parallel
```

#### Test Isolation
- Use transactions for database tests
- Reset mocks between tests
- Clean up test data after each test

## 📚 Best Practices

### Test Organization
1. **Group related tests** in describe blocks
2. **Use descriptive test names** that explain the scenario
3. **Follow AAA pattern** - Arrange, Act, Assert
4. **Keep tests independent** - no shared state
5. **Use test data builders** for complex objects

### Mock Strategy
1. **Mock external dependencies** (APIs, databases, file system)
2. **Use real objects** for internal logic when possible
3. **Verify mock interactions** when behavior matters
4. **Reset mocks** between tests

### Performance
1. **Run fast tests first** (unit tests before integration)
2. **Use parallel execution** when tests are independent
3. **Cache dependencies** in CI/CD
4. **Optimize test data setup** and teardown

## 🎉 Summary

The test environment provides:

✅ **Comprehensive Testing** - Unit, integration, and E2E tests
✅ **Automated CI/CD** - GitHub Actions workflow
✅ **Mock Services** - External dependencies mocked
✅ **Test Database** - Isolated test data
✅ **Coverage Reporting** - Detailed coverage metrics
✅ **Development Tools** - Watch mode, debugging, utilities
✅ **Documentation** - Clear setup and usage guides

This setup ensures reliable, fast, and maintainable testing for the entire SoNoBrokers application stack!
