using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using System.Net;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using MicroSaasWebApi.Models.SoNoBrokers.Messaging;
using MicroSaasWebApi.Tests.Configuration;

namespace MicroSaasWebApi.Tests.Integration
{
    [TestFixture]
    public class MessagingControllerTests
    {
        private WebApplicationFactory<Program> _factory;
        private HttpClient _client;
        private string _testUserId;
        private string _testPropertyId;
        private string _testSellerId;

        [OneTimeSetUp]
        public void OneTimeSetUp()
        {
            _factory = new WebApplicationFactory<Program>()
                .WithWebHostBuilder(builder =>
                {
                    builder.ConfigureServices(services =>
                    {
                        // Add test configuration
                        var configuration = TestConfiguration.CreateTestConfiguration();
                        services.AddSingleton(configuration);
                    });
                });

            _client = _factory.CreateClient();
            
            // Set up test data
            _testUserId = "test-user-123";
            _testPropertyId = "test-property-123";
            _testSellerId = "test-seller-123";
        }

        [SetUp]
        public void SetUp()
        {
            // Add authorization header for tests
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");
        }

        [Test]
        public async Task CreateConversation_WithValidRequest_ReturnsCreated()
        {
            // Arrange
            var request = new CreateConversationRequest
            {
                PropertyId = _testPropertyId,
                SellerId = _testSellerId,
                Subject = "Test Property Inquiry",
                InitialMessage = "I'm interested in this property. Can you provide more details?"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/messaging/conversations", request);

            // Assert
            // Note: This would return 401 Unauthorized in a real test without proper auth setup
            // For integration testing, we'd need to set up proper test authentication
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Unauthorized).Or.EqualTo(HttpStatusCode.Created));
        }

        [Test]
        public async Task GetConversations_WithoutAuth_ReturnsUnauthorized()
        {
            // Arrange
            _client.DefaultRequestHeaders.Authorization = null;

            // Act
            var response = await _client.GetAsync("/api/sonobrokers/messaging/conversations");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Unauthorized));
        }

        [Test]
        public async Task SendMessage_WithValidRequest_ReturnsOk()
        {
            // Arrange
            var request = new CreateMessageRequest
            {
                ConversationId = "test-conversation-123",
                Content = "This is a test message",
                MessageType = "text"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/messaging/messages", request);

            // Assert
            // Note: This would return 401 Unauthorized in a real test without proper auth setup
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Unauthorized).Or.EqualTo(HttpStatusCode.OK));
        }

        [Test]
        public async Task GetMessages_WithValidConversationId_ReturnsMessages()
        {
            // Arrange
            var conversationId = "test-conversation-123";

            // Act
            var response = await _client.GetAsync($"/api/sonobrokers/messaging/conversations/{conversationId}/messages");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Unauthorized).Or.EqualTo(HttpStatusCode.OK));
        }

        [Test]
        public async Task MarkMessagesAsRead_WithValidRequest_ReturnsOk()
        {
            // Arrange
            var conversationId = "test-conversation-123";
            var request = new MarkMessagesReadRequest
            {
                MessageIds = new List<string> { "msg-1", "msg-2" }
            };

            // Act
            var response = await _client.PutAsJsonAsync(
                $"/api/sonobrokers/messaging/conversations/{conversationId}/messages/read", 
                request);

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Unauthorized).Or.EqualTo(HttpStatusCode.OK));
        }

        [Test]
        public async Task GetMessageStats_ReturnsStats()
        {
            // Act
            var response = await _client.GetAsync("/api/sonobrokers/messaging/stats");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Unauthorized).Or.EqualTo(HttpStatusCode.OK));
        }

        [Test]
        public async Task CreateConversation_WithInvalidRequest_ReturnsBadRequest()
        {
            // Arrange
            var request = new CreateConversationRequest
            {
                PropertyId = "", // Invalid - empty property ID
                SellerId = _testSellerId,
                Subject = "Test",
                InitialMessage = "Test message"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/messaging/conversations", request);

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Unauthorized).Or.EqualTo(HttpStatusCode.BadRequest));
        }

        [Test]
        public async Task SendMessage_WithEmptyContent_ReturnsBadRequest()
        {
            // Arrange
            var request = new CreateMessageRequest
            {
                ConversationId = "test-conversation-123",
                Content = "", // Invalid - empty content
                MessageType = "text"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/messaging/messages", request);

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Unauthorized).Or.EqualTo(HttpStatusCode.BadRequest));
        }

        [Test]
        public async Task GetConversation_WithInvalidId_ReturnsNotFound()
        {
            // Arrange
            var invalidConversationId = "non-existent-conversation";

            // Act
            var response = await _client.GetAsync($"/api/sonobrokers/messaging/conversations/{invalidConversationId}");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Unauthorized).Or.EqualTo(HttpStatusCode.NotFound));
        }

        [Test]
        public async Task UpdateConversation_WithValidRequest_ReturnsOk()
        {
            // Arrange
            var conversationId = "test-conversation-123";
            var request = new UpdateConversationRequest
            {
                Subject = "Updated Subject",
                IsActive = true
            };

            // Act
            var response = await _client.PutAsJsonAsync(
                $"/api/sonobrokers/messaging/conversations/{conversationId}", 
                request);

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Unauthorized).Or.EqualTo(HttpStatusCode.OK));
        }

        [Test]
        public async Task DeleteConversation_WithValidId_ReturnsOk()
        {
            // Arrange
            var conversationId = "test-conversation-123";

            // Act
            var response = await _client.DeleteAsync($"/api/sonobrokers/messaging/conversations/{conversationId}");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Unauthorized).Or.EqualTo(HttpStatusCode.OK));
        }

        [Test]
        public async Task GetConversations_WithSearchParams_ReturnsFilteredResults()
        {
            // Arrange
            var queryParams = "?search=test&page=1&limit=10&isActive=true";

            // Act
            var response = await _client.GetAsync($"/api/sonobrokers/messaging/conversations{queryParams}");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Unauthorized).Or.EqualTo(HttpStatusCode.OK));
        }

        [Test]
        public async Task GetMessages_WithPagination_ReturnsPagedResults()
        {
            // Arrange
            var conversationId = "test-conversation-123";
            var queryParams = "?page=1&limit=20&sortBy=createdAt&sortOrder=desc";

            // Act
            var response = await _client.GetAsync(
                $"/api/sonobrokers/messaging/conversations/{conversationId}/messages{queryParams}");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Unauthorized).Or.EqualTo(HttpStatusCode.OK));
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            _client?.Dispose();
            _factory?.Dispose();
        }
    }

    [TestFixture]
    public class AdminMessagingControllerTests
    {
        private WebApplicationFactory<Program> _factory;
        private HttpClient _client;

        [OneTimeSetUp]
        public void OneTimeSetUp()
        {
            _factory = new WebApplicationFactory<Program>()
                .WithWebHostBuilder(builder =>
                {
                    builder.ConfigureServices(services =>
                    {
                        var configuration = TestConfiguration.CreateTestConfiguration();
                        services.AddSingleton(configuration);
                    });
                });

            _client = _factory.CreateClient();
        }

        [SetUp]
        public void SetUp()
        {
            // Add admin authorization header for tests
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "admin-test-token");
        }

        [Test]
        public async Task GetAllConversations_WithoutAdminAuth_ReturnsForbidden()
        {
            // Arrange
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "user-test-token");

            // Act
            var response = await _client.GetAsync("/api/admin/messaging/conversations");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Unauthorized).Or.EqualTo(HttpStatusCode.Forbidden));
        }

        [Test]
        public async Task GetAllConversations_WithAdminAuth_ReturnsOk()
        {
            // Act
            var response = await _client.GetAsync("/api/admin/messaging/conversations");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Unauthorized).Or.EqualTo(HttpStatusCode.OK));
        }

        [Test]
        public async Task GetMessagingStats_WithAdminAuth_ReturnsStats()
        {
            // Act
            var response = await _client.GetAsync("/api/admin/messaging/stats");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Unauthorized).Or.EqualTo(HttpStatusCode.OK));
        }

        [Test]
        public async Task GetMessagingActivity_WithDateRange_ReturnsActivity()
        {
            // Arrange
            var fromDate = DateTime.UtcNow.AddDays(-30).ToString("yyyy-MM-dd");
            var toDate = DateTime.UtcNow.ToString("yyyy-MM-dd");
            var queryParams = $"?fromDate={fromDate}&toDate={toDate}";

            // Act
            var response = await _client.GetAsync($"/api/admin/messaging/activity{queryParams}");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Unauthorized).Or.EqualTo(HttpStatusCode.OK));
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            _client?.Dispose();
            _factory?.Dispose();
        }
    }
}
