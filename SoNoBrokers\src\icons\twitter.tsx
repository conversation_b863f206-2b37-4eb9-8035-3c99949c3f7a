const Twitter = () => {
  return (
    <svg
      width="85"
      height="18"
      viewBox="0 0 85 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.6839 1.6875H16.1649L10.7447 7.8825L17.1212 16.3125H12.1284L8.21791 11.1997L3.74341 16.3125H1.26091L7.05841 9.68625L0.941406 1.6875H6.06091L9.59566 6.36075L13.6839 1.6875ZM12.8132 14.8275H14.1879L5.31391 3.0945H3.83866L12.8132 14.8275Z"
        fill="#010610"
      />
      <path
        d="M24.5284 5.90625V4.77273H31.2827V5.90625H28.5597V13.5H27.2472V5.90625H24.5284ZM33.342 13.5L31.4158 6.95455H32.7326L34.0153 11.7614H34.0792L35.3661 6.95455H36.6829L37.9613 11.7401H38.0252L39.2994 6.95455H40.6161L38.6942 13.5H37.3945L36.065 8.77415H35.967L34.6374 13.5H33.342ZM41.8253 13.5V6.95455H43.0994V13.5H41.8253ZM42.4688 5.9446C42.2472 5.9446 42.0568 5.87074 41.8977 5.72301C41.7415 5.57244 41.6634 5.39347 41.6634 5.18608C41.6634 4.97585 41.7415 4.79687 41.8977 4.64915C42.0568 4.49858 42.2472 4.4233 42.4688 4.4233C42.6903 4.4233 42.8793 4.49858 43.0355 4.64915C43.1946 4.79687 43.2741 4.97585 43.2741 5.18608C43.2741 5.39347 43.1946 5.57244 43.0355 5.72301C42.8793 5.87074 42.6903 5.9446 42.4688 5.9446ZM47.869 6.95455V7.97727H44.2937V6.95455H47.869ZM45.2525 5.38636H46.5266V11.5781C46.5266 11.8253 46.5636 12.0114 46.6374 12.1364C46.7113 12.2585 46.8065 12.3423 46.9229 12.3878C47.0423 12.4304 47.1715 12.4517 47.3107 12.4517C47.413 12.4517 47.5025 12.4446 47.5792 12.4304C47.6559 12.4162 47.7156 12.4048 47.7582 12.3963L47.9883 13.4489C47.9144 13.4773 47.8093 13.5057 47.6729 13.5341C47.5366 13.5653 47.3661 13.5824 47.1616 13.5852C46.8263 13.5909 46.5138 13.5312 46.2241 13.4062C45.9343 13.2812 45.6999 13.0881 45.521 12.8267C45.342 12.5653 45.2525 12.2372 45.2525 11.8423V5.38636ZM52.3338 6.95455V7.97727H48.7585V6.95455H52.3338ZM49.7173 5.38636H50.9915V11.5781C50.9915 11.8253 51.0284 12.0114 51.1023 12.1364C51.1761 12.2585 51.2713 12.3423 51.3878 12.3878C51.5071 12.4304 51.6364 12.4517 51.7756 12.4517C51.8778 12.4517 51.9673 12.4446 52.044 12.4304C52.1207 12.4162 52.1804 12.4048 52.223 12.3963L52.4531 13.4489C52.3793 13.4773 52.2741 13.5057 52.1378 13.5341C52.0014 13.5653 51.831 13.5824 51.6264 13.5852C51.2912 13.5909 50.9787 13.5312 50.6889 13.4062C50.3991 13.2812 50.1648 13.0881 49.9858 12.8267C49.8068 12.5653 49.7173 12.2372 49.7173 11.8423V5.38636ZM56.494 13.6321C55.8491 13.6321 55.2937 13.4943 54.8278 13.2188C54.3647 12.9403 54.0067 12.5497 53.7539 12.0469C53.5039 11.5412 53.3789 10.9489 53.3789 10.2699C53.3789 9.59943 53.5039 9.00852 53.7539 8.49716C54.0067 7.9858 54.359 7.58665 54.8107 7.29972C55.2653 7.01278 55.7965 6.86932 56.4045 6.86932C56.7738 6.86932 57.1317 6.9304 57.4783 7.05256C57.8249 7.17472 58.136 7.36648 58.4116 7.62784C58.6871 7.8892 58.9045 8.22869 59.0636 8.64631C59.2227 9.06108 59.3022 9.56534 59.3022 10.1591V10.6108H54.0991V9.65625H58.0536C58.0536 9.32102 57.9854 9.02415 57.8491 8.76562C57.7127 8.50426 57.521 8.2983 57.2738 8.14773C57.0295 7.99716 56.7425 7.92188 56.413 7.92188C56.055 7.92188 55.7425 8.00994 55.4755 8.18608C55.2113 8.35937 55.0067 8.58665 54.8619 8.8679C54.7198 9.14631 54.6488 9.44886 54.6488 9.77557V10.5213C54.6488 10.9588 54.7255 11.331 54.8789 11.6378C55.0352 11.9446 55.2525 12.179 55.5309 12.3409C55.8093 12.5 56.1346 12.5795 56.5067 12.5795C56.7482 12.5795 56.9684 12.5455 57.1673 12.4773C57.3661 12.4062 57.538 12.3011 57.6829 12.1619C57.8278 12.0227 57.9386 11.8509 58.0153 11.6463L59.2212 11.8636C59.1246 12.2187 58.9513 12.5298 58.7013 12.7969C58.4542 13.0611 58.1431 13.267 57.7681 13.4148C57.396 13.5597 56.9712 13.6321 56.494 13.6321ZM60.7159 13.5V6.95455H61.9474V7.99432H62.0156C62.1349 7.64205 62.3452 7.36506 62.6463 7.16335C62.9503 6.95881 63.294 6.85653 63.6776 6.85653C63.7571 6.85653 63.8509 6.85937 63.9588 6.86506C64.0696 6.87074 64.1562 6.87784 64.2188 6.88636V8.10511C64.1676 8.09091 64.0767 8.07528 63.946 8.05824C63.8153 8.03835 63.6847 8.02841 63.554 8.02841C63.2528 8.02841 62.9844 8.09233 62.7486 8.22017C62.5156 8.34517 62.331 8.51989 62.1946 8.74432C62.0582 8.96591 61.9901 9.21875 61.9901 9.50284V13.5H60.7159ZM69.9162 4.77273V13.5H68.5994V4.77273H69.9162ZM73.0057 9.61364V13.5H71.7315V6.95455H72.9545V8.01989H73.0355C73.1861 7.6733 73.4219 7.39489 73.7429 7.18466C74.0668 6.97443 74.4744 6.86932 74.9659 6.86932C75.4119 6.86932 75.8026 6.96307 76.1378 7.15057C76.473 7.33523 76.733 7.6108 76.9176 7.97727C77.1023 8.34375 77.1946 8.79687 77.1946 9.33665V13.5H75.9205V9.49006C75.9205 9.01562 75.7969 8.64489 75.5497 8.37784C75.3026 8.10795 74.9631 7.97301 74.5312 7.97301C74.2358 7.97301 73.973 8.03693 73.7429 8.16477C73.5156 8.29261 73.3352 8.48011 73.2017 8.72727C73.071 8.97159 73.0057 9.26705 73.0057 9.61364ZM81.6605 13.6321C81.027 13.6321 80.4815 13.4886 80.0241 13.2017C79.5696 12.9119 79.2202 12.5128 78.9759 12.0043C78.7315 11.4957 78.6094 10.9134 78.6094 10.2571C78.6094 9.59233 78.7344 9.00568 78.9844 8.49716C79.2344 7.9858 79.5866 7.58665 80.0412 7.29972C80.4957 7.01278 81.0313 6.86932 81.6477 6.86932C82.1449 6.86932 82.5881 6.96165 82.9773 7.14631C83.3665 7.32812 83.6804 7.58381 83.919 7.91335C84.1605 8.2429 84.304 8.62784 84.3494 9.06818H83.1094C83.0412 8.76136 82.8849 8.49716 82.6406 8.27557C82.3991 8.05398 82.0753 7.94318 81.669 7.94318C81.3139 7.94318 81.0028 8.03693 80.7358 8.22443C80.4716 8.40909 80.2656 8.6733 80.1179 9.01705C79.9702 9.35795 79.8963 9.76136 79.8963 10.2273C79.8963 10.7045 79.9688 11.1165 80.1136 11.4631C80.2585 11.8097 80.4631 12.0781 80.7273 12.2685C80.9943 12.4588 81.3082 12.554 81.669 12.554C81.9105 12.554 82.1293 12.5099 82.3253 12.4219C82.5241 12.331 82.6903 12.2017 82.8239 12.0341C82.9602 11.8665 83.0554 11.6648 83.1094 11.429H84.3494C84.304 11.8523 84.1662 12.2301 83.9361 12.5625C83.706 12.8949 83.3977 13.1562 83.0114 13.3466C82.6278 13.5369 82.1776 13.6321 81.6605 13.6321Z"
        fill="#010610"
      />
    </svg>
  );
};

export default Twitter;
