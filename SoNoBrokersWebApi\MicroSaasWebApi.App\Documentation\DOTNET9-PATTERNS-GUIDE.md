# 🚀 .NET 9 Modern Patterns Guide

## 📋 Table of Contents
- [Minimal APIs](#minimal-apis)
- [Keyed Dependency Injection](#keyed-dependency-injection)
- [Configuration Patterns](#configuration-patterns)
- [Background Services](#background-services)
- [Health Checks](#health-checks)
- [Performance Patterns](#performance-patterns)
- [Security Patterns](#security-patterns)

## 🎯 Minimal APIs

### **Traditional vs Minimal API Comparison**

```csharp
// Traditional Controller Approach
[ApiController]
[Route("api/[controller]")]
public class TenantsController : ControllerBase
{
    private readonly ITenantService _tenantService;
    
    public TenantsController(ITenantService tenantService)
    {
        _tenantService = tenantService;
    }
    
    [HttpGet]
    public async Task<ActionResult<IEnumerable<Tenant>>> GetTenants()
    {
        var tenants = await _tenantService.GetTenantsAsync();
        return Ok(tenants);
    }
    
    [HttpPost]
    public async Task<ActionResult<Tenant>> CreateTenant(CreateTenantRequest request)
    {
        var tenant = await _tenantService.CreateTenantAsync(request);
        return CreatedAtAction(nameof(GetTenant), new { id = tenant.Id }, tenant);
    }
}

// Modern Minimal API Approach
public static class TenantEndpoints
{
    public static void MapTenantEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/tenants")
            .WithTags("Tenants")
            .WithOpenApi();

        group.MapGet("/", GetTenants)
            .WithName("GetTenants")
            .WithSummary("Get all tenants")
            .Produces<IEnumerable<Tenant>>();

        group.MapPost("/", CreateTenant)
            .WithName("CreateTenant")
            .WithSummary("Create a new tenant")
            .Accepts<CreateTenantRequest>("application/json")
            .Produces<Tenant>(201)
            .ProducesValidationProblem();

        group.MapGet("/{id}", GetTenant)
            .WithName("GetTenant")
            .WithSummary("Get tenant by ID")
            .Produces<Tenant>()
            .Produces(404);
    }

    private static async Task<IResult> GetTenants(ITenantService tenantService)
    {
        var tenants = await tenantService.GetTenantsAsync();
        return Results.Ok(tenants);
    }

    private static async Task<IResult> CreateTenant(
        CreateTenantRequest request, 
        ITenantService tenantService,
        IValidator<CreateTenantRequest> validator)
    {
        var validationResult = await validator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return Results.ValidationProblem(validationResult.ToDictionary());
        }

        var tenant = await tenantService.CreateTenantAsync(request);
        return Results.Created($"/api/tenants/{tenant.Id}", tenant);
    }

    private static async Task<IResult> GetTenant(string id, ITenantService tenantService)
    {
        var tenant = await tenantService.GetTenantByIdAsync(id);
        return tenant is not null ? Results.Ok(tenant) : Results.NotFound();
    }
}
```

### **Minimal API with Filters**

```csharp
public class TenantValidationFilter : IEndpointFilter
{
    public async ValueTask<object?> InvokeAsync(
        EndpointFilterInvocationContext context, 
        EndpointFilterDelegate next)
    {
        var tenantId = context.HttpContext.Request.RouteValues["tenantId"]?.ToString();
        
        if (string.IsNullOrEmpty(tenantId))
        {
            return Results.BadRequest("Tenant ID is required");
        }

        var tenantService = context.HttpContext.RequestServices.GetRequiredService<ITenantService>();
        var tenant = await tenantService.GetTenantByIdAsync(tenantId);
        
        if (tenant == null)
        {
            return Results.NotFound($"Tenant '{tenantId}' not found");
        }

        context.HttpContext.Items["Tenant"] = tenant;
        return await next(context);
    }
}

// Usage
group.MapGet("/{tenantId}/users", GetTenantUsers)
    .AddEndpointFilter<TenantValidationFilter>();
```

## 🔑 Keyed Dependency Injection

### **Keyed Services Registration**

```csharp
// Register multiple implementations with keys
builder.Services.AddKeyedScoped<IPaymentProvider, StripePaymentProvider>("stripe");
builder.Services.AddKeyedScoped<IPaymentProvider, PayPalPaymentProvider>("paypal");
builder.Services.AddKeyedScoped<IPaymentProvider, SquarePaymentProvider>("square");

// Register notification providers
builder.Services.AddKeyedScoped<INotificationProvider, EmailNotificationProvider>("email");
builder.Services.AddKeyedScoped<INotificationProvider, SmsNotificationProvider>("sms");
builder.Services.AddKeyedScoped<INotificationProvider, PushNotificationProvider>("push");

// Register tenant-specific services
builder.Services.AddKeyedScoped<ITenantService, BasicTenantService>("basic");
builder.Services.AddKeyedScoped<ITenantService, PremiumTenantService>("premium");
builder.Services.AddKeyedScoped<ITenantService, EnterpriseTenantService>("enterprise");
```

### **Consuming Keyed Services**

```csharp
public class PaymentService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ITenantProviderService _tenantProvider;

    public PaymentService(
        IServiceProvider serviceProvider,
        ITenantProviderService tenantProvider)
    {
        _serviceProvider = serviceProvider;
        _tenantProvider = tenantProvider;
    }

    public async Task<PaymentResult> ProcessPaymentAsync(PaymentRequest request)
    {
        var tenant = _tenantProvider.GetCurrentTenant();
        var paymentProvider = tenant.Settings.PaymentProvider; // "stripe", "paypal", etc.

        var provider = _serviceProvider.GetRequiredKeyedService<IPaymentProvider>(paymentProvider);
        return await provider.ProcessPaymentAsync(request);
    }
}

// Direct injection with FromKeyedServices attribute
public class NotificationService
{
    private readonly INotificationProvider _emailProvider;
    private readonly INotificationProvider _smsProvider;

    public NotificationService(
        [FromKeyedServices("email")] INotificationProvider emailProvider,
        [FromKeyedServices("sms")] INotificationProvider smsProvider)
    {
        _emailProvider = emailProvider;
        _smsProvider = smsProvider;
    }

    public async Task SendWelcomeNotificationAsync(User user)
    {
        await _emailProvider.SendAsync(user.Email, "Welcome!", "Welcome to our platform!");
        
        if (!string.IsNullOrEmpty(user.PhoneNumber))
        {
            await _smsProvider.SendAsync(user.PhoneNumber, "Welcome to our platform!");
        }
    }
}
```

### **Factory Pattern with Keyed Services**

```csharp
public interface ITenantServiceFactory
{
    ITenantService CreateTenantService(string subscriptionTier);
}

public class TenantServiceFactory : ITenantServiceFactory
{
    private readonly IServiceProvider _serviceProvider;

    public TenantServiceFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public ITenantService CreateTenantService(string subscriptionTier)
    {
        return subscriptionTier.ToLower() switch
        {
            "basic" => _serviceProvider.GetRequiredKeyedService<ITenantService>("basic"),
            "premium" => _serviceProvider.GetRequiredKeyedService<ITenantService>("premium"),
            "enterprise" => _serviceProvider.GetRequiredKeyedService<ITenantService>("enterprise"),
            _ => _serviceProvider.GetRequiredKeyedService<ITenantService>("basic")
        };
    }
}
```

## ⚙️ Configuration Patterns

### **Strongly-Typed Configuration**

```csharp
// Configuration classes
public class TenantSettings
{
    public const string SectionName = "TenantSettings";
    
    public string DefaultConnectionString { get; set; } = string.Empty;
    public int MaxTenantsPerUser { get; set; } = 5;
    public bool EnableTenantIsolation { get; set; } = true;
    public TimeSpan TenantCacheExpiry { get; set; } = TimeSpan.FromMinutes(30);
    public List<string> AllowedDomains { get; set; } = new();
}

public class PaymentSettings
{
    public const string SectionName = "PaymentSettings";
    
    public string StripeSecretKey { get; set; } = string.Empty;
    public string StripePublishableKey { get; set; } = string.Empty;
    public string WebhookSecret { get; set; } = string.Empty;
    public bool EnableTestMode { get; set; } = false;
    public Dictionary<string, decimal> PlanPrices { get; set; } = new();
}

// Registration
builder.Services.Configure<TenantSettings>(
    builder.Configuration.GetSection(TenantSettings.SectionName));

builder.Services.Configure<PaymentSettings>(
    builder.Configuration.GetSection(PaymentSettings.SectionName));

// Validation
builder.Services.AddOptions<TenantSettings>()
    .Bind(builder.Configuration.GetSection(TenantSettings.SectionName))
    .ValidateDataAnnotations()
    .Validate(settings => settings.MaxTenantsPerUser > 0, "MaxTenantsPerUser must be greater than 0")
    .ValidateOnStart();
```

### **Options Pattern with IOptionsSnapshot**

```csharp
public class TenantService
{
    private readonly IOptionsSnapshot<TenantSettings> _tenantSettings;
    private readonly IOptionsSnapshot<PaymentSettings> _paymentSettings;

    public TenantService(
        IOptionsSnapshot<TenantSettings> tenantSettings,
        IOptionsSnapshot<PaymentSettings> paymentSettings)
    {
        _tenantSettings = tenantSettings;
        _paymentSettings = paymentSettings;
    }

    public async Task<Tenant> CreateTenantAsync(CreateTenantRequest request)
    {
        var settings = _tenantSettings.Value;
        
        // Use current configuration values
        var connectionString = settings.DefaultConnectionString;
        var maxTenants = settings.MaxTenantsPerUser;
        
        // Implementation
    }
}
```

### **Configuration Providers**

```csharp
// Custom configuration provider for tenant-specific settings
public class TenantConfigurationProvider : ConfigurationProvider
{
    private readonly string _tenantId;
    private readonly IServiceProvider _serviceProvider;

    public override void Load()
    {
        using var scope = _serviceProvider.CreateScope();
        var tenantService = scope.ServiceProvider.GetRequiredService<ITenantService>();
        
        var tenant = tenantService.GetTenantByIdAsync(_tenantId).Result;
        if (tenant?.Settings != null)
        {
            foreach (var setting in tenant.Settings.CustomSettings)
            {
                Data[$"Tenant:{setting.Key}"] = setting.Value;
            }
        }
    }
}

// Usage
builder.Configuration.Add(new TenantConfigurationSource(tenantId));
```

## 🔄 Background Services

### **Modern Background Service Patterns**

```csharp
public class TenantMaintenanceService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<TenantMaintenanceService> _logger;
    private readonly IOptionsMonitor<TenantSettings> _settings;

    public TenantMaintenanceService(
        IServiceProvider serviceProvider,
        ILogger<TenantMaintenanceService> logger,
        IOptionsMonitor<TenantSettings> settings)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _settings = settings;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PerformMaintenanceAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during tenant maintenance");
            }

            var delay = _settings.CurrentValue.MaintenanceInterval;
            await Task.Delay(delay, stoppingToken);
        }
    }

    private async Task PerformMaintenanceAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var tenantService = scope.ServiceProvider.GetRequiredService<ITenantService>();
        
        var tenants = await tenantService.GetActiveTenantsAsync();
        
        await Parallel.ForEachAsync(tenants, cancellationToken, async (tenant, ct) =>
        {
            await PerformTenantMaintenanceAsync(tenant, scope.ServiceProvider, ct);
        });
    }

    private async Task PerformTenantMaintenanceAsync(
        Tenant tenant, 
        IServiceProvider serviceProvider, 
        CancellationToken cancellationToken)
    {
        var tenantProvider = serviceProvider.GetRequiredService<ITenantProviderService>();
        tenantProvider.SetTenant(tenant);

        // Perform tenant-specific maintenance
        _logger.LogInformation("Performing maintenance for tenant {TenantId}", tenant.Id);
        
        // Cleanup expired data, update statistics, etc.
    }
}
```

### **Hosted Service with Timer**

```csharp
public class MetricsCollectionService : IHostedService, IDisposable
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<MetricsCollectionService> _logger;
    private Timer? _timer;

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _timer = new Timer(CollectMetrics, null, TimeSpan.Zero, TimeSpan.FromMinutes(5));
        await Task.CompletedTask;
    }

    private async void CollectMetrics(object? state)
    {
        using var scope = _serviceProvider.CreateScope();
        var metricsService = scope.ServiceProvider.GetRequiredService<IMetricsService>();
        
        try
        {
            await metricsService.CollectTenantMetricsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting metrics");
        }
    }

    public async Task StopAsync(CancellationToken cancellationToken)
    {
        _timer?.Change(Timeout.Infinite, 0);
        await Task.CompletedTask;
    }

    public void Dispose()
    {
        _timer?.Dispose();
    }
}
```

## 🏥 Health Checks

### **Comprehensive Health Check System**

```csharp
// Custom health checks
public class TenantDatabaseHealthCheck : IHealthCheck
{
    private readonly ITenantService _tenantService;

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var tenants = await _tenantService.GetActiveTenantsAsync();
            var healthyTenants = 0;
            var totalTenants = tenants.Count();

            foreach (var tenant in tenants)
            {
                if (await _tenantService.IsTenantDatabaseHealthyAsync(tenant.Id))
                {
                    healthyTenants++;
                }
            }

            var healthPercentage = totalTenants > 0 ? (double)healthyTenants / totalTenants * 100 : 100;

            if (healthPercentage >= 90)
            {
                return HealthCheckResult.Healthy($"All tenant databases healthy ({healthyTenants}/{totalTenants})");
            }
            else if (healthPercentage >= 70)
            {
                return HealthCheckResult.Degraded($"Some tenant databases unhealthy ({healthyTenants}/{totalTenants})");
            }
            else
            {
                return HealthCheckResult.Unhealthy($"Many tenant databases unhealthy ({healthyTenants}/{totalTenants})");
            }
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy("Failed to check tenant databases", ex);
        }
    }
}

// Registration
builder.Services.AddHealthChecks()
    .AddCheck<TenantDatabaseHealthCheck>("tenant-databases")
    .AddCheck<ExternalApiHealthCheck>("external-apis")
    .AddNpgSql(connectionString, name: "database")
    .AddRedis(redisConnectionString, name: "cache")
    .AddUrlGroup(new Uri("https://api.stripe.com/v1"), "stripe-api");

// Health check endpoints
app.MapHealthChecks("/health", new HealthCheckOptions
{
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});

app.MapHealthChecks("/health/ready", new HealthCheckOptions
{
    Predicate = check => check.Tags.Contains("ready"),
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});

app.MapHealthChecks("/health/live", new HealthCheckOptions
{
    Predicate = _ => false
});
```

## ⚡ Performance Patterns

### **Memory Management**

```csharp
// Object pooling
public class TenantContextPool : ObjectPool<TenantContext>
{
    public override TenantContext Get()
    {
        return new TenantContext();
    }

    public override void Return(TenantContext obj)
    {
        obj.Reset();
    }
}

// ArrayPool usage
public class DataProcessor
{
    public async Task<ProcessResult> ProcessDataAsync(Stream dataStream)
    {
        var buffer = ArrayPool<byte>.Shared.Rent(4096);
        try
        {
            int bytesRead;
            while ((bytesRead = await dataStream.ReadAsync(buffer)) > 0)
            {
                // Process buffer
            }
        }
        finally
        {
            ArrayPool<byte>.Shared.Return(buffer);
        }
    }
}
```

### **Caching Patterns**

```csharp
public class TenantCacheService
{
    private readonly IMemoryCache _memoryCache;
    private readonly IDistributedCache _distributedCache;

    public async Task<T?> GetOrSetAsync<T>(
        string key, 
        Func<Task<T>> factory, 
        TimeSpan? expiry = null) where T : class
    {
        // Try memory cache first
        if (_memoryCache.TryGetValue(key, out T? cached))
        {
            return cached;
        }

        // Try distributed cache
        var distributedValue = await _distributedCache.GetStringAsync(key);
        if (distributedValue != null)
        {
            var deserialized = JsonSerializer.Deserialize<T>(distributedValue);
            _memoryCache.Set(key, deserialized, TimeSpan.FromMinutes(5));
            return deserialized;
        }

        // Fetch from source
        var value = await factory();
        if (value != null)
        {
            var serialized = JsonSerializer.Serialize(value);
            await _distributedCache.SetStringAsync(key, serialized, new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = expiry ?? TimeSpan.FromHours(1)
            });
            _memoryCache.Set(key, value, TimeSpan.FromMinutes(5));
        }

        return value;
    }
}
```

## 🔒 Security Patterns

### **Rate Limiting**

```csharp
// Configure rate limiting
builder.Services.AddRateLimiter(options =>
{
    options.AddFixedWindowLimiter("api", limiterOptions =>
    {
        limiterOptions.PermitLimit = 100;
        limiterOptions.Window = TimeSpan.FromMinutes(1);
        limiterOptions.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
        limiterOptions.QueueLimit = 10;
    });

    options.AddPolicy("tenant", context =>
    {
        var tenantId = context.Request.Headers["X-Tenant-Id"].FirstOrDefault();
        return RateLimitPartition.GetFixedWindowLimiter(tenantId ?? "anonymous", 
            _ => new FixedWindowRateLimiterOptions
            {
                PermitLimit = 50,
                Window = TimeSpan.FromMinutes(1)
            });
    });
});

// Usage
app.MapGet("/api/data", GetData)
    .RequireRateLimiting("api");
```

### **Authentication & Authorization**

```csharp
// JWT configuration
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"]!))
        };
    });

// Policy-based authorization
builder.Services.AddAuthorizationBuilder()
    .AddPolicy("TenantAdmin", policy =>
        policy.RequireClaim("role", "admin")
              .RequireClaim("tenant_id"))
    .AddPolicy("TenantUser", policy =>
        policy.RequireAuthenticatedUser()
              .RequireClaim("tenant_id"));
```

This guide showcases the most important .NET 9 patterns for building modern, scalable, and maintainable MicroSaaS applications!
