using Dapper;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using SoNoBrokersUser = MicroSaasWebApi.Models.SoNoBrokers.User;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.App.Context;

namespace MicroSaasWebApi.Services.SoNoBrokers
{
    public class UserService : IUserService
    {
        private readonly IDapperDbContext _dbContext;
        private readonly ILogger<UserService> _logger;

        public UserService(IDapperDbContext dbContext, ILogger<UserService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<IEnumerable<SoNoBrokersUser>> GetAllUsersAsync()
        {
            try
            {
                var sql = @"
                    SELECT * FROM public.""User""
                    ORDER BY ""createdAt"" DESC";

                return await _dbContext.QueryAsync<SoNoBrokersUser>(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all users");
                throw;
            }
        }

        public async Task<SoNoBrokersUser?> GetUserByIdAsync(string id)
        {
            try
            {
                var sql = @"SELECT * FROM public.""User"" WHERE id = @id";
                return await _dbContext.QueryFirstOrDefaultAsync<SoNoBrokersUser>(sql, new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user {UserId}", id);
                throw;
            }
        }

        public async Task<SoNoBrokersUser?> GetUserByEmailAsync(string email)
        {
            try
            {
                var sql = @"SELECT * FROM public.""User"" WHERE email = @email";
                return await _dbContext.QueryFirstOrDefaultAsync<SoNoBrokersUser>(sql, new { email });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user by email {Email}", email);
                throw;
            }
        }

        public async Task<SoNoBrokersUser?> GetUserByClerkIdAsync(string clerkUserId)
        {
            try
            {
                var sql = @"SELECT * FROM public.""User"" WHERE ""clerkId"" = @clerkUserId OR ""clerkUserId"" = @clerkUserId";
                return await _dbContext.QueryFirstOrDefaultAsync<SoNoBrokersUser>(sql, new { clerkUserId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user by Clerk ID {ClerkUserId}", clerkUserId);
                throw;
            }
        }

        public async Task<SoNoBrokersUser> CreateUserAsync(SoNoBrokersUser user)
        {
            try
            {
                // Check if user with email already exists
                var emailExists = await _dbContext.QuerySingleAsync<bool>(
                    @"SELECT EXISTS(SELECT 1 FROM public.""User"" WHERE email = @email)",
                    new { email = user.Email });

                if (emailExists)
                {
                    throw new InvalidOperationException("User with this email already exists");
                }

                user.Id = Guid.NewGuid().ToString();
                user.CreatedAt = DateTime.UtcNow;
                user.UpdatedAt = DateTime.UtcNow;

                var sql = @"
                    INSERT INTO public.""User"" (
                        id, ""fullName"", ""firstName"", ""lastName"", email, ""phoneNumber"",
                        ""profileImageUrl"", address, role, ""userType"", status, ""emailVerified"",
                        ""phoneVerified"", ""isActive"", ""loggedIn"", ""clerkId"", ""clerkUserId"",
                        ""authUserId"", ""createdByAdmin"", ""createdAt"", ""updatedAt"", ""lastLoginAt""
                    ) VALUES (
                        @Id, @FullName, @FirstName, @LastName, @Email, @PhoneNumber,
                        @ProfileImageUrl, @Address, @Role, @UserType, @Status, @EmailVerified,
                        @PhoneVerified, @IsActive, @LoggedIn, @ClerkId, @ClerkUserId,
                        @AuthUserId, @CreatedByAdmin, @CreatedAt, @UpdatedAt, @LastLoginAt
                    )";

                await _dbContext.ExecuteAsync(sql, user);

                return user;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user");
                throw;
            }
        }

        public async Task<SoNoBrokersUser> UpdateUserAsync(SoNoBrokersUser user)
        {
            try
            {
                var exists = await _dbContext.QuerySingleAsync<bool>(
                    @"SELECT EXISTS(SELECT 1 FROM public.""User"" WHERE id = @id)",
                    new { id = user.Id });

                if (!exists)
                {
                    throw new InvalidOperationException("User not found");
                }

                user.UpdatedAt = DateTime.UtcNow;

                var sql = @"
                    UPDATE public.""User"" SET
                        ""fullName"" = @FullName,
                        ""firstName"" = @FirstName,
                        ""lastName"" = @LastName,
                        ""phoneNumber"" = @PhoneNumber,
                        ""profileImageUrl"" = @ProfileImageUrl,
                        address = @Address,
                        role = @Role,
                        ""userType"" = @UserType,
                        status = @Status,
                        ""emailVerified"" = @EmailVerified,
                        ""phoneVerified"" = @PhoneVerified,
                        ""isActive"" = @IsActive,
                        ""updatedAt"" = @UpdatedAt,
                        ""lastLoginAt"" = @LastLoginAt,
                        ""loggedIn"" = @LoggedIn,
                        ""clerkId"" = @ClerkId,
                        ""clerkUserId"" = @ClerkUserId,
                        ""authUserId"" = @AuthUserId,
                        ""createdByAdmin"" = @CreatedByAdmin
                    WHERE id = @Id";

                await _dbContext.ExecuteAsync(sql, user);

                // Return updated user
                return await GetUserByIdAsync(user.Id) ?? user;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {UserId}", user.Id);
                throw;
            }
        }

        public async Task<bool> DeleteUserAsync(string id)
        {
            try
            {
                var exists = await _dbContext.QuerySingleAsync<bool>(
                    @"SELECT EXISTS(SELECT 1 FROM public.""User"" WHERE id = @id)",
                    new { id });

                if (!exists)
                {
                    return false;
                }

                await _dbContext.ExecuteAsync(@"DELETE FROM public.""User"" WHERE id = @id", new { id });
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user {UserId}", id);
                throw;
            }
        }

        public async Task<bool> UpdateLoginStatusAsync(string id, bool loggedIn)
        {
            try
            {
                var exists = await _dbContext.QuerySingleAsync<bool>(
                    @"SELECT EXISTS(SELECT 1 FROM public.""User"" WHERE id = @id)",
                    new { id });

                if (!exists)
                {
                    return false;
                }

                var sql = @"
                    UPDATE public.""User"" SET
                        ""loggedIn"" = @loggedIn,
                        ""lastLoginAt"" = CASE WHEN @loggedIn THEN @now ELSE ""lastLoginAt"" END,
                        ""updatedAt"" = @now
                    WHERE id = @id";

                await _dbContext.ExecuteAsync(sql, new { id, loggedIn, now = DateTime.UtcNow });
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating login status for user {UserId}", id);
                throw;
            }
        }

        public async Task<bool> UserExistsAsync(string id)
        {
            try
            {
                return await _dbContext.QuerySingleAsync<bool>(
                    @"SELECT EXISTS(SELECT 1 FROM public.""User"" WHERE id = @id)",
                    new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if user exists {UserId}", id);
                throw;
            }
        }

        public async Task<bool> EmailExistsAsync(string email)
        {
            try
            {
                return await _dbContext.QuerySingleAsync<bool>(
                    @"SELECT EXISTS(SELECT 1 FROM public.""User"" WHERE email = @email)",
                    new { email });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if email exists {Email}", email);
                throw;
            }
        }

        public async Task<IEnumerable<SoNoBrokersUser>> GetUsersByTypeAsync(SnbUserType userType)
        {
            try
            {
                var sql = @"
                    SELECT * FROM public.""User""
                    WHERE ""userType"" = @userType
                    ORDER BY ""createdAt"" DESC";

                return await _dbContext.QueryAsync<SoNoBrokersUser>(sql, new { userType = userType.ToString() });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving users by type {UserType}", userType);
                throw;
            }
        }

        public async Task<SoNoBrokersUser> CreateUserAsync(CreateUserRequest request)
        {
            try
            {
                // Check if user with email already exists
                var emailExists = await _dbContext.QuerySingleAsync<bool>(
                    @"SELECT EXISTS(SELECT 1 FROM public.""User"" WHERE email = @email)",
                    new { email = request.Email });

                if (emailExists)
                {
                    throw new InvalidOperationException("User with this email already exists");
                }

                var id = Guid.NewGuid().ToString();
                var now = DateTime.UtcNow;

                var sql = @"
                    INSERT INTO public.""User"" (
                        id, email, ""fullName"", ""firstName"", ""lastName"", phone,
                        ""clerkUserId"", role, ""userType"", ""isActive"", ""loggedIn"",
                        ""createdAt"", ""updatedAt""
                    ) VALUES (
                        @id, @email, @fullName, @firstName, @lastName, @phone,
                        @clerkUserId, @role, @userType, @isActive, @loggedIn,
                        @createdAt, @updatedAt
                    )";

                var parameters = new
                {
                    id,
                    email = request.Email,
                    fullName = request.FullName,
                    firstName = request.FirstName,
                    lastName = request.LastName,
                    phoneNumber = request.PhoneNumber,
                    clerkUserId = request.ClerkUserId,
                    role = request.Role.ToString(),
                    userType = request.UserType.ToString(),
                    isActive = true,
                    loggedIn = false,
                    createdAt = now,
                    updatedAt = now
                };

                await _dbContext.ExecuteAsync(sql, parameters);

                var created = await GetUserByIdAsync(id);
                return created ?? throw new InvalidOperationException("Failed to retrieve created user");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user with DTO");
                throw;
            }
        }

        public async Task<SoNoBrokersUser?> UpdateUserAsync(UpdateUserRequest request)
        {
            try
            {
                var exists = await _dbContext.QuerySingleAsync<bool>(
                    @"SELECT EXISTS(SELECT 1 FROM public.""User"" WHERE id = @id)",
                    new { id = request.Id });

                if (!exists)
                {
                    return null;
                }

                var updates = new List<string>();
                var parameters = new DynamicParameters();
                parameters.Add("id", request.Id);
                parameters.Add("updatedAt", DateTime.UtcNow);

                if (!string.IsNullOrEmpty(request.Email))
                {
                    updates.Add(@"email = @email");
                    parameters.Add("email", request.Email);
                }

                if (!string.IsNullOrEmpty(request.FullName))
                {
                    updates.Add(@"""fullName"" = @fullName");
                    parameters.Add("fullName", request.FullName);
                }

                if (!string.IsNullOrEmpty(request.FirstName))
                {
                    updates.Add(@"""firstName"" = @firstName");
                    parameters.Add("firstName", request.FirstName);
                }

                if (!string.IsNullOrEmpty(request.LastName))
                {
                    updates.Add(@"""lastName"" = @lastName");
                    parameters.Add("lastName", request.LastName);
                }

                if (!string.IsNullOrEmpty(request.PhoneNumber))
                {
                    updates.Add(@"""phoneNumber"" = @phoneNumber");
                    parameters.Add("phoneNumber", request.PhoneNumber);
                }

                if (request.Role.HasValue)
                {
                    updates.Add(@"role = @role");
                    parameters.Add("role", request.Role.Value.ToString());
                }

                if (request.UserType.HasValue)
                {
                    updates.Add(@"""userType"" = @userType");
                    parameters.Add("userType", request.UserType.Value.ToString());
                }

                if (request.IsActive.HasValue)
                {
                    updates.Add(@"""isActive"" = @isActive");
                    parameters.Add("isActive", request.IsActive.Value);
                }

                if (!updates.Any())
                {
                    return await GetUserByIdAsync(request.Id);
                }

                updates.Add(@"""updatedAt"" = @updatedAt");

                var sql = $@"
                    UPDATE public.""User""
                    SET {string.Join(", ", updates)}
                    WHERE id = @id";

                await _dbContext.ExecuteAsync(sql, parameters);

                return await GetUserByIdAsync(request.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user with DTO {UserId}", request.Id);
                throw;
            }
        }

        public async Task TrackLoginAsync(string userId)
        {
            try
            {
                var sql = @"
                    UPDATE public.""User""
                    SET ""lastLoginAt"" = @lastLoginAt, ""loggedIn"" = true, ""updatedAt"" = @updatedAt
                    WHERE id = @userId";

                await _dbContext.ExecuteAsync(sql, new
                {
                    lastLoginAt = DateTime.UtcNow,
                    updatedAt = DateTime.UtcNow,
                    userId
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking login for user {UserId}", userId);
                throw;
            }
        }

        public async Task TrackLogoutAsync(string userId)
        {
            try
            {
                var sql = @"
                    UPDATE public.""User""
                    SET ""loggedIn"" = false, ""updatedAt"" = @updatedAt
                    WHERE id = @userId";

                await _dbContext.ExecuteAsync(sql, new
                {
                    updatedAt = DateTime.UtcNow,
                    userId
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking logout for user {UserId}", userId);
                throw;
            }
        }

        public async Task<LoginStatsResponse> GetLoginStatsAsync(string userId)
        {
            try
            {
                var sql = @"
                    SELECT ""loggedIn"", ""lastLoginAt"", ""createdAt""
                    FROM public.""User""
                    WHERE id = @userId";

                var user = await _dbContext.QueryFirstOrDefaultAsync<dynamic>(sql, new { userId });

                if (user == null)
                {
                    return new LoginStatsResponse
                    {
                        IsLoggedIn = false,
                        LastLoginAt = null
                    };
                }

                return new LoginStatsResponse
                {
                    IsLoggedIn = user.loggedIn ?? false,
                    LastLoginAt = user.lastLoginAt
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting login stats for user {UserId}", userId);
                return new LoginStatsResponse
                {
                    IsLoggedIn = false,
                    LastLoginAt = null
                };
            }
        }

        public async Task<SoNoBrokersUser> MapAuthUserToSnbUserAsync(MapAuthUserRequest request)
        {
            try
            {
                // Check if user exists
                var existingUserSql = @"
                    SELECT * FROM public.""User""
                    WHERE email = @email OR ""clerkUserId"" = @clerkUserId OR ""authUserId"" = @authUserId
                    LIMIT 1";

                var existingUser = await _dbContext.QueryFirstOrDefaultAsync<SoNoBrokersUser>(existingUserSql, new
                {
                    email = request.Email,
                    clerkUserId = request.ClerkUserId,
                    authUserId = request.AuthUserId
                });

                if (existingUser != null)
                {
                    // Update existing user
                    var updateSql = @"
                        UPDATE public.""User""
                        SET
                            email = @email,
                            ""fullName"" = COALESCE(@fullName, ""fullName""),
                            ""firstName"" = COALESCE(@firstName, ""firstName""),
                            ""lastName"" = COALESCE(@lastName, ""lastName""),
                            ""clerkUserId"" = COALESCE(@clerkUserId, ""clerkUserId""),
                            ""authUserId"" = COALESCE(@authUserId, ""authUserId""),
                            ""lastLoginAt"" = @lastLoginAt,
                            ""loggedIn"" = true,
                            ""updatedAt"" = @updatedAt
                        WHERE id = @id
                        RETURNING *";

                    var updatedUser = await _dbContext.QueryFirstAsync<SoNoBrokersUser>(updateSql, new
                    {
                        email = request.Email,
                        fullName = request.FullName,
                        firstName = request.FirstName,
                        lastName = request.LastName,
                        clerkUserId = request.ClerkUserId,
                        authUserId = request.AuthUserId,
                        lastLoginAt = DateTime.UtcNow,
                        updatedAt = DateTime.UtcNow,
                        id = existingUser.Id
                    });

                    return updatedUser;
                }
                else
                {
                    // Create new user
                    var createRequest = new CreateUserRequest
                    {
                        Email = request.Email,
                        FullName = request.FullName ?? request.Email.Split('@')[0],
                        FirstName = request.FirstName,
                        LastName = request.LastName,
                        ClerkUserId = request.ClerkUserId ?? string.Empty,
                        Role = UserRole.USER,
                        UserType = SnbUserType.Buyer
                    };

                    return await CreateUserAsync(createRequest);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error mapping auth user to SNB user for email: {Email}", request.Email);
                throw;
            }
        }

        public async Task<SoNoBrokersUser?> GetCurrentUserAsync(string clerkUserId)
        {
            try
            {
                return await GetUserByClerkIdAsync(clerkUserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current user for Clerk ID: {ClerkUserId}", clerkUserId);
                return null;
            }
        }

        public async Task<SoNoBrokersUser?> SyncUserWithClerkAsync(string clerkUserId)
        {
            try
            {
                // This would typically call Clerk API to get latest user data
                // For now, just return the existing user
                return await GetUserByClerkIdAsync(clerkUserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing user with Clerk for ID: {ClerkUserId}", clerkUserId);
                return null;
            }
        }

        public async Task<bool> HasPermissionAsync(string userId, string permission, string? resource = null)
        {
            try
            {
                var userSql = @"SELECT role, ""isActive"" FROM public.""User"" WHERE id = @userId";
                var user = await _dbContext.QueryFirstOrDefaultAsync<dynamic>(userSql, new { userId });

                if (user == null || !(user.isActive ?? false)) return false;

                // Check role permissions
                var permissionSql = @"
                    SELECT 1 FROM public.role_permissions
                    WHERE role = @role AND permission = @permission AND (resource = @resource OR resource IS NULL)
                    LIMIT 1";

                var hasPermission = await _dbContext.QueryFirstOrDefaultAsync<int?>(permissionSql, new
                {
                    role = user.role,
                    permission,
                    resource
                });

                return hasPermission.HasValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking permission for user {UserId}", userId);
                return false;
            }
        }

        public async Task<IEnumerable<SoNoBrokersUser>> GetAllUsersWithPaginationAsync(int page = 1, int limit = 10, UserRole? role = null)
        {
            try
            {
                var skip = (page - 1) * limit;
                var roleFilter = role.HasValue ? @"WHERE u.role = @role" : "";

                var sql = $@"
                    SELECT u.*, cb.id as ""createdBy_id"", cb.email as ""createdBy_email"", cb.""fullName"" as ""createdBy_fullName""
                    FROM public.""User"" u
                    LEFT JOIN public.""User"" cb ON u.""createdByAdmin"" = cb.id
                    {roleFilter}
                    ORDER BY u.""createdAt"" DESC
                    LIMIT @limit OFFSET @skip";

                return await _dbContext.QueryAsync<SoNoBrokersUser>(sql, new { role = role?.ToString(), limit, skip });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users with pagination");
                throw;
            }
        }

        public async Task<UserPermissionsResponse> GetUserPermissionsAsync(string userId)
        {
            try
            {
                var user = await GetUserByIdAsync(userId);
                if (user == null)
                {
                    throw new ArgumentException("User not found", nameof(userId));
                }

                // Get user permissions based on role
                var permissionsSql = @"
                    SELECT role, permission, resource
                    FROM public.role_permissions
                    WHERE role = @role";

                var permissions = await _dbContext.QueryAsync<dynamic>(permissionsSql, new { role = user.Role.ToString() });

                return new UserPermissionsResponse
                {
                    UserId = userId,
                    Role = user.Role,
                    IsActive = user.IsActive,
                    Permissions = permissions.Select(p => new PermissionInfo
                    {
                        Permission = p.permission,
                        Resource = p.resource ?? string.Empty
                    }).ToList()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permissions for user {UserId}", userId);
                throw;
            }
        }
    }
}
