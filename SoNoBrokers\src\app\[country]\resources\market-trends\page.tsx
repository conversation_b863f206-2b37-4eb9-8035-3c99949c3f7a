import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function MarketTrendsPage({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/resources/market-trends')
  }

  const userType = resolvedSearchParams.userType || 'buyer'

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8">
            Real Estate Market Trends
          </h1>

          <div className="bg-card rounded-lg p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-4">Current Market Conditions in {country.toUpperCase()}</h2>
            <p className="text-muted-foreground mb-6">
              Stay informed with the latest real estate market trends, price movements, and buying opportunities
              across {country.toUpperCase()}. Our data-driven insights help you make informed decisions.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6 mb-8">
            <div className="bg-card rounded-lg p-6 text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">+5.2%</div>
              <div className="text-sm text-muted-foreground">Average Price Growth</div>
              <div className="text-xs text-muted-foreground mt-1">Year over Year</div>
            </div>

            <div className="bg-card rounded-lg p-6 text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">28</div>
              <div className="text-sm text-muted-foreground">Days on Market</div>
              <div className="text-xs text-muted-foreground mt-1">Average</div>
            </div>

            <div className="bg-card rounded-lg p-6 text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">1.8</div>
              <div className="text-sm text-muted-foreground">Months of Inventory</div>
              <div className="text-xs text-muted-foreground mt-1">Current Supply</div>
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <div className="bg-card rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Price Trends by Property Type</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span>Single Family Homes</span>
                  <span className="text-green-600 font-semibold">+6.1%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Condominiums</span>
                  <span className="text-green-600 font-semibold">+4.8%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Townhouses</span>
                  <span className="text-green-600 font-semibold">+5.5%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Multi-Family</span>
                  <span className="text-green-600 font-semibold">+3.2%</span>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Regional Highlights</h3>
              <div className="space-y-3">
                <div>
                  <div className="font-semibold">Urban Centers</div>
                  <div className="text-sm text-muted-foreground">Strong demand, limited inventory</div>
                </div>
                <div>
                  <div className="font-semibold">Suburban Areas</div>
                  <div className="text-sm text-muted-foreground">Balanced market conditions</div>
                </div>
                <div>
                  <div className="font-semibold">Rural Markets</div>
                  <div className="text-sm text-muted-foreground">Emerging opportunities</div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6 mb-8">
            <h3 className="text-xl font-semibold mb-4">Market Forecast</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Next 6 Months</h4>
                <ul className="space-y-1 text-sm">
                  <li>• Continued moderate price growth expected</li>
                  <li>• Inventory levels may increase slightly</li>
                  <li>• Interest rate stability anticipated</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">12-Month Outlook</h4>
                <ul className="space-y-1 text-sm">
                  <li>• Market normalization likely</li>
                  <li>• Regional variations to persist</li>
                  <li>• First-time buyer opportunities</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4">Market Analysis Tools</h3>
            <p className="text-muted-foreground mb-4">
              Access detailed market reports and analysis tools to stay ahead of trends.
            </p>
            <div className="flex gap-4">
              <button className="bg-primary text-primary-foreground px-6 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors">
                Download Report
              </button>
              <button className="border border-border px-6 py-3 rounded-lg font-semibold hover:bg-muted transition-colors">
                Set Price Alerts
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'buyer'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Real Estate Market Trends for ${country} | SoNoBrokers`,
    description: `Current real estate market trends and analysis for ${country}. Price movements, inventory levels, and market forecasts to guide your decisions.`,
    keywords: `market trends, real estate market, property prices, ${country}, market analysis`,
  }
}
