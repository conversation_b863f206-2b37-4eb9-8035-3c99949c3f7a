using MicroSaasWebApi.Models.Auth;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.Auth.Interfaces;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using MicroSaasWebApi.Tests.Common;
using Moq;

namespace MicroSaasWebApi.Tests.Mocks
{
    public static class MockServices
    {
        public static Mock<IClerkAuthService> CreateMockClerkAuthService()
        {
            var mock = new Mock<IClerkAuthService>();

            // Setup default behaviors
            mock.Setup(x => x.GetUserIdAsync(It.IsAny<HttpContext>()))
                .ReturnsAsync("test-user-id");

            mock.Setup(x => x.GetUserProfileAsync(It.IsAny<HttpContext>()))
                .ReturnsAsync(TestDataBuilders.Auth.UserInfo.Generate());

            mock.Setup(x => x.LoginAsync(It.IsAny<LoginRequest>()))
                .ReturnsAsync((LoginRequest request) => new AuthResponse
                {
                    Success = request.Email == "<EMAIL>" && request.Password == "TestPassword123!",
                    Token = request.Email == "<EMAIL>" ? "mock-jwt-token" : null,
                    Message = request.Email == "<EMAIL>" ? "Login successful" : "Invalid credentials",
                    User = request.Email == "<EMAIL>" ? TestDataBuilders.Auth.UserInfo.Generate() : null,
                    UserId = request.Email == "<EMAIL>" ? "test-user-id" : null
                });

            mock.Setup(x => x.RegisterAsync(It.IsAny<RegisterRequest>()))
                .ReturnsAsync((RegisterRequest request) => new AuthResponse
                {
                    Success = !request.Email.Contains("existing"),
                    Token = !request.Email.Contains("existing") ? "mock-jwt-token" : null,
                    Message = !request.Email.Contains("existing") ? "Registration successful" : "Email already exists",
                    User = !request.Email.Contains("existing") ? new UserInfo
                    {
                        Id = Guid.NewGuid(),
                        Email = request.Email,
                        FirstName = request.FirstName,
                        LastName = request.LastName,
                        IsEmailVerified = false
                    } : null,
                    UserId = !request.Email.Contains("existing") ? Guid.NewGuid().ToString() : null
                });

            mock.Setup(x => x.RefreshTokenAsync(It.IsAny<string>()))
                .ReturnsAsync((string token) => new AuthResponse
                {
                    Success = !string.IsNullOrEmpty(token),
                    Token = !string.IsNullOrEmpty(token) ? "new-mock-jwt-token" : null,
                    Message = !string.IsNullOrEmpty(token) ? "Token refreshed" : "Invalid refresh token"
                });

            mock.Setup(x => x.LogoutAsync(It.IsAny<string>()))
                .ReturnsAsync(true);

            return mock;
        }

        public static Mock<IUserService> CreateMockUserService()
        {
            var mock = new Mock<IUserService>();

            mock.Setup(x => x.GetUserByIdAsync(It.IsAny<string>()))
                .ReturnsAsync((string id) => id.StartsWith("test-") ? TestDataBuilders.Users.ValidUser.Generate() : null);

            mock.Setup(x => x.GetUserByEmailAsync(It.IsAny<string>()))
                .ReturnsAsync((string email) => email.Contains("test") ? TestDataBuilders.Users.ValidUser.Generate() : null);

            mock.Setup(x => x.GetUserByClerkIdAsync(It.IsAny<string>()))
                .ReturnsAsync((string clerkId) => clerkId.StartsWith("test-") ? TestDataBuilders.Users.ValidUser.Generate() : null);

            mock.Setup(x => x.CreateUserAsync(It.IsAny<CreateUserRequest>()))
                .ReturnsAsync((CreateUserRequest request) =>
                {
                    var user = TestDataBuilders.Users.ValidUser.Generate();
                    user.Email = request.Email;
                    user.FullName = request.FullName;
                    user.ClerkUserId = request.ClerkUserId;
                    return user;
                });

            mock.Setup(x => x.UpdateUserAsync(It.IsAny<UpdateUserRequest>()))
                .ReturnsAsync((UpdateUserRequest request) =>
                {
                    var user = TestDataBuilders.Users.ValidUser.Generate();
                    user.Id = request.Id;
                    if (!string.IsNullOrEmpty(request.Email)) user.Email = request.Email;
                    if (!string.IsNullOrEmpty(request.FullName)) user.FullName = request.FullName;
                    return user;
                });

            mock.Setup(x => x.EmailExistsAsync(It.IsAny<string>()))
                .ReturnsAsync((string email) => email.Contains("existing"));

            mock.Setup(x => x.UserExistsAsync(It.IsAny<string>()))
                .ReturnsAsync((string id) => id.StartsWith("test-"));

            return mock;
        }

        public static Mock<IAdvertiserService> CreateMockAdvertiserService()
        {
            var mock = new Mock<IAdvertiserService>();

            mock.Setup(x => x.GetAdvertisersAsync(It.IsAny<AdvertiserSearchRequest>()))
                .ReturnsAsync((AdvertiserSearchRequest request) => new AdvertiserSearchResponse
                {
                    Advertisers = TestDataBuilders.Advertisers.ValidAdvertiser.Generate(request.Limit).Select(a => new AdvertiserResponse
                    {
                        Id = a.Id,
                        BusinessName = a.BusinessName,
                        Email = a.Email,
                        ServiceType = a.ServiceType,
                        Plan = a.Plan,
                        Status = a.Status
                    }).ToList(),
                    Total = 50,
                    Page = request.Page,
                    TotalPages = (int)Math.Ceiling(50.0 / request.Limit),
                    HasMore = request.Page * request.Limit < 50
                });

            mock.Setup(x => x.GetAdvertiserByIdAsync(It.IsAny<string>()))
                .ReturnsAsync((string id) => id.StartsWith("test-") ? new AdvertiserResponse
                {
                    Id = id,
                    BusinessName = "Test Business",
                    Email = "<EMAIL>",
                    ServiceType = ServiceType.photographer,
                    Plan = AdvertiserPlan.basic,
                    Status = AdvertiserStatus.active
                } : null);

            mock.Setup(x => x.CreateAdvertiserAsync(It.IsAny<CreateAdvertiserRequest>(), It.IsAny<string>()))
                .ReturnsAsync((CreateAdvertiserRequest request, string userId) => new AdvertiserResponse
                {
                    Id = TestHelpers.GenerateTestId("adv"),
                    BusinessName = request.BusinessName,
                    Email = request.Email,
                    ServiceType = request.ServiceType,
                    Plan = request.Plan,
                    Status = AdvertiserStatus.pending
                });

            return mock;
        }

        public static Mock<IAIPropertyService> CreateMockAIPropertyService()
        {
            var mock = new Mock<IAIPropertyService>();

            mock.Setup(x => x.ImportPropertyDataAsync(It.IsAny<AIPropertyImportRequest>()))
                .ReturnsAsync((AIPropertyImportRequest request) => new AIPropertyImportResponse
                {
                    Status = "success",
                    Message = "Property data imported successfully",
                    PropertyDetails = new AIPropertyDetails
                    {
                        Address = request.Address,
                        PropertyType = "Detached House",
                        Bedrooms = 3,
                        Bathrooms = 2.5m,
                        SquareFeet = 2000,
                        YearBuilt = 2010,
                        LotSize = 0.25m,
                        Features = new[] { "Garage", "Fireplace", "Hardwood Floors" },
                        Amenities = new[] { "Swimming Pool", "Garden", "Patio" },
                        Description = "Beautiful property with modern amenities"
                    }
                });

            mock.Setup(x => x.GetPropertyValuationAsync(It.IsAny<AIPropertyValuationRequest>()))
                .ReturnsAsync((AIPropertyValuationRequest request) => new AIPropertyValuationResponse
                {
                    Status = "success",
                    Message = "Property valuation completed successfully",
                    Valuation = new PropertyValuation
                    {
                        EstimatedValue = 750000,
                        LowEstimate = 700000,
                        HighEstimate = 800000,
                        Currency = request.Country switch
                        {
                            "CA" => "CAD",
                            "US" => "USD",
                            "UAE" => "AED",
                            _ => "CAD"
                        },
                        PricePerSquareFoot = 375,
                        ConfidenceScore = 85
                    },
                    PropertyInfo = new PropertyInfo
                    {
                        Address = request.Address,
                        PropertyType = "Detached House",
                        Bedrooms = 3,
                        Bathrooms = 2.5m,
                        SquareFeet = 2000
                    },
                    MarketAnalysis = new MarketAnalysis
                    {
                        MedianPrice = 725000,
                        AveragePrice = 740000,
                        DaysOnMarket = 25,
                        MarketTrend = "Stable",
                        MarketCondition = "Balanced"
                    },
                    ComparableProperties = new[]
                    {
                        new ComparableProperty
                        {
                            Address = "Similar Property 1",
                            SoldPrice = 720000,
                            SoldDate = "2024-01-15",
                            Bedrooms = 3,
                            Bathrooms = 2,
                            SquareFeet = 1950,
                            Distance = 0.3m,
                            PricePerSquareFoot = 369
                        }
                    }
                });

            mock.Setup(x => x.GeneratePropertyDescriptionAsync(It.IsAny<GenerateDescriptionRequest>()))
                .ReturnsAsync((GenerateDescriptionRequest request) => new GenerateDescriptionResponse
                {
                    Status = "success",
                    Message = "Description generated successfully",
                    Description = $"AI-generated description based on: {request.Prompt}. This beautiful property offers modern living with excellent amenities and great location."
                });

            return mock;
        }

        public static Mock<ICommunicationService> CreateMockCommunicationService()
        {
            var mock = new Mock<ICommunicationService>();

            mock.Setup(x => x.SendConciergeInquiryAsync(It.IsAny<ContactConciergeRequest>()))
                .ReturnsAsync(new ContactConciergeResponse
                {
                    Success = true,
                    Message = "Inquiry sent successfully"
                });

            mock.Setup(x => x.AddToWaitingListAsync(It.IsAny<WaitingListRequest>()))
                .ReturnsAsync(new WaitingListResponse
                {
                    Success = true,
                    Message = "Successfully added to waiting list",
                    ContactId = TestHelpers.GenerateTestId("contact")
                });

            mock.Setup(x => x.GetLocationByIpAsync(It.IsAny<GeoLocationRequest>()))
                .ReturnsAsync((GeoLocationRequest request) => new GeoLocationResponse
                {
                    Country = "CA",
                    CountryName = "Canada",
                    City = "Toronto",
                    Region = "Ontario",
                    Timezone = "America/Toronto"
                });

            mock.Setup(x => x.GetEnumValuesAsync())
                .ReturnsAsync(new EnumValuesResponse
                {
                    Enums = new Dictionary<string, object>
                    {
                        ["countries"] = new List<CountryInfo>
                        {
                            new() { Code = "CA", Name = "Canada", Flag = "🇨🇦" },
                            new() { Code = "US", Name = "United States", Flag = "🇺🇸" },
                            new() { Code = "UAE", Name = "United Arab Emirates", Flag = "🇦🇪" }
                        },
                        ["serviceTypes"] = new List<ServiceTypeInfo>
                        {
                            new() { Value = "photographer", Label = "Property Photographer" },
                            new() { Value = "lawyer", Label = "Real Estate Lawyer" }
                        }
                    }
                });

            return mock;
        }

        public static Mock<IProjectService> CreateMockProjectService()
        {
            var mock = new Mock<IProjectService>();

            mock.Setup(x => x.GetProjectsAsync(It.IsAny<ProjectSearchRequest>(), It.IsAny<string>()))
                .ReturnsAsync((ProjectSearchRequest request, string userClerkId) => new ProjectSearchResponse
                {
                    Projects = TestDataBuilders.Projects.ValidProject.Generate(request.Limit).Select(p => new ProjectResponse
                    {
                        Id = p.Id,
                        ConnectionId = p.ConnectionId,
                        WebhookId = p.WebhookId,
                        ScenarioId = p.ScenarioId,
                        UserClerkId = userClerkId,
                        WebhookLink = p.WebhookLink,
                        Type = p.Type,
                        Status = p.Status,
                        CreatedAt = p.CreatedAt,
                        UpdatedAt = p.UpdatedAt
                    }).ToList(),
                    Total = 10,
                    Page = request.Page,
                    TotalPages = (int)Math.Ceiling(10.0 / request.Limit),
                    HasMore = request.Page * request.Limit < 10
                });

            mock.Setup(x => x.GetProjectByIdAsync(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync((string id, string userClerkId) => id.StartsWith("test-") ? new ProjectResponse
                {
                    Id = id,
                    UserClerkId = userClerkId,
                    ConnectionId = "conn-123",
                    WebhookId = "hook-123",
                    ScenarioId = "scenario-123",
                    WebhookLink = "https://test.webhook.com",
                    Type = "automation",
                    Status = "active",
                    CreatedAt = DateTime.UtcNow.AddDays(-7),
                    UpdatedAt = DateTime.UtcNow.AddDays(-1)
                } : null);

            return mock;
        }

        public static Mock<IAdminService> CreateMockAdminService()
        {
            var mock = new Mock<IAdminService>();

            mock.Setup(x => x.GetDashboardDataAsync())
                .ReturnsAsync(new AdminDashboardResponse
                {
                    Success = true,
                    Data = new AdminDashboardData
                    {
                        Users = new UserStatistics
                        {
                            Total = 100,
                            Active = 85,
                            Recent = 15,
                            ByRole = new UserRoleStatistics
                            {
                                Admin = 5,
                                User = 90,
                                Product = 3,
                                Operator = 2
                            },
                            ByType = new UserTypeStatistics
                            {
                                Buyers = 60,
                                Sellers = 40,
                                Operators = 0
                            }
                        },
                        Activity = new ActivityStatistics
                        {
                            RecentLogins = 45
                        },
                        Subscriptions = new Dictionary<string, int>
                        {
                            ["active"] = 25,
                            ["inactive"] = 10,
                            ["cancelled"] = 5
                        }
                    }
                });

            return mock;
        }
    }
}
