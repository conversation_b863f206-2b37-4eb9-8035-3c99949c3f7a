using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using MicroSaasWebApi.Services.Auth.Interfaces;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/sonobrokers/advertisers")]
    [Tags("Advertisers")]
    public class AdvertisersController : ControllerBase
    {
        private readonly IAdvertiserService _advertiserService;
        private readonly IClerkAuthService _authService;
        private readonly ILogger<AdvertisersController> _logger;

        public AdvertisersController(
            IAdvertiserService advertiserService,
            IClerkAuthService authService,
            ILogger<AdvertisersController> logger)
        {
            _advertiserService = advertiserService;
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// Get advertisers with filtering and pagination
        /// </summary>
        [HttpGet]
        [AllowAnonymous]
        public async Task<ActionResult<AdvertiserSearchResponse>> GetAdvertisers([FromQuery] AdvertiserSearchRequest request)
        {
            try
            {
                var result = await _advertiserService.GetAdvertisersAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting advertisers");
                return StatusCode(500, new { error = "Failed to get advertisers" });
            }
        }

        /// <summary>
        /// Get advertiser by ID
        /// </summary>
        [HttpGet("{id}")]
        [AllowAnonymous]
        public async Task<ActionResult<AdvertiserResponse>> GetAdvertiser(string id)
        {
            try
            {
                var advertiser = await _advertiserService.GetAdvertiserByIdAsync(id);
                if (advertiser == null)
                {
                    return NotFound(new { error = "Advertiser not found" });
                }

                return Ok(advertiser);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting advertiser: {Id}", id);
                return StatusCode(500, new { error = "Failed to get advertiser" });
            }
        }

        /// <summary>
        /// Create a new advertiser
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<AdvertiserResponse>> CreateAdvertiser([FromBody] CreateAdvertiserRequest request)
        {
            try
            {
                // Get authenticated user
                var userId = await _authService.GetUserIdAsync(HttpContext);
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { error = "Authentication required" });
                }

                // Check if user already has an advertiser profile
                var existing = await _advertiserService.GetAdvertiserByUserIdAsync(userId);
                if (existing != null)
                {
                    return Conflict(new { error = "User already has an advertiser profile" });
                }

                var advertiser = await _advertiserService.CreateAdvertiserAsync(request, userId);
                return CreatedAtAction(nameof(GetAdvertiser), new { id = advertiser.Id }, advertiser);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating advertiser");
                return StatusCode(500, new { error = "Failed to create advertiser" });
            }
        }

        /// <summary>
        /// Update an existing advertiser
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<AdvertiserResponse>> UpdateAdvertiser(string id, [FromBody] UpdateAdvertiserRequest request)
        {
            try
            {
                // Get authenticated user
                var userId = await _authService.GetUserIdAsync(HttpContext);
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { error = "Authentication required" });
                }

                // Verify ownership
                var existing = await _advertiserService.GetAdvertiserByIdAsync(id);
                if (existing == null)
                {
                    return NotFound(new { error = "Advertiser not found" });
                }

                if (existing.UserId != userId)
                {
                    return Forbid("You can only update your own advertiser profile");
                }

                request.Id = id;
                var updated = await _advertiserService.UpdateAdvertiserAsync(request);
                if (updated == null)
                {
                    return NotFound(new { error = "Advertiser not found" });
                }

                return Ok(updated);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating advertiser: {Id}", id);
                return StatusCode(500, new { error = "Failed to update advertiser" });
            }
        }

        /// <summary>
        /// Delete an advertiser
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteAdvertiser(string id)
        {
            try
            {
                // Get authenticated user
                var userId = await _authService.GetUserIdAsync(HttpContext);
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { error = "Authentication required" });
                }

                // Verify ownership
                var existing = await _advertiserService.GetAdvertiserByIdAsync(id);
                if (existing == null)
                {
                    return NotFound(new { error = "Advertiser not found" });
                }

                if (existing.UserId != userId)
                {
                    return Forbid("You can only delete your own advertiser profile");
                }

                var deleted = await _advertiserService.DeleteAdvertiserAsync(id);
                if (!deleted)
                {
                    return NotFound(new { error = "Advertiser not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting advertiser: {Id}", id);
                return StatusCode(500, new { error = "Failed to delete advertiser" });
            }
        }

        /// <summary>
        /// Get current user's advertiser profile
        /// </summary>
        [HttpGet("me")]
        public async Task<ActionResult<AdvertiserResponse>> GetMyAdvertiser()
        {
            try
            {
                // Get authenticated user
                var userId = await _authService.GetUserIdAsync(HttpContext);
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { error = "Authentication required" });
                }

                var advertiser = await _advertiserService.GetAdvertiserByUserIdAsync(userId);
                if (advertiser == null)
                {
                    return NotFound(new { error = "Advertiser profile not found" });
                }

                return Ok(advertiser);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user's advertiser profile");
                return StatusCode(500, new { error = "Failed to get advertiser profile" });
            }
        }

        /// <summary>
        /// Get available advertiser plans
        /// </summary>
        [HttpGet("plans")]
        [AllowAnonymous]
        public async Task<ActionResult<List<AdvertiserPlanFeatures>>> GetAdvertiserPlans()
        {
            try
            {
                var plans = await _advertiserService.GetAdvertiserPlansAsync();
                return Ok(plans);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting advertiser plans");
                return StatusCode(500, new { error = "Failed to get advertiser plans" });
            }
        }

        /// <summary>
        /// Admin: Verify an advertiser
        /// </summary>
        [HttpPatch("{id}/verify")]
        public async Task<ActionResult> VerifyAdvertiser(string id, [FromBody] bool isVerified)
        {
            try
            {
                // TODO: Add admin authorization check
                var updated = await _advertiserService.VerifyAdvertiserAsync(id, isVerified);
                if (!updated)
                {
                    return NotFound(new { error = "Advertiser not found" });
                }

                return Ok(new { message = $"Advertiser {(isVerified ? "verified" : "unverified")} successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying advertiser: {Id}", id);
                return StatusCode(500, new { error = "Failed to verify advertiser" });
            }
        }

        /// <summary>
        /// Admin: Update advertiser status
        /// </summary>
        [HttpPatch("{id}/status")]
        public async Task<ActionResult> UpdateAdvertiserStatus(string id, [FromBody] AdvertiserStatus status)
        {
            try
            {
                // TODO: Add admin authorization check
                var updated = await _advertiserService.UpdateAdvertiserStatusAsync(id, status);
                if (!updated)
                {
                    return NotFound(new { error = "Advertiser not found" });
                }

                return Ok(new { message = "Advertiser status updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating advertiser status: {Id}", id);
                return StatusCode(500, new { error = "Failed to update advertiser status" });
            }
        }
    }
}
