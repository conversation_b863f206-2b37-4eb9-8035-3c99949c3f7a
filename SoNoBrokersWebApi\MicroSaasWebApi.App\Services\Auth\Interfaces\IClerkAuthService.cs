using System.Security.Claims;
using MicroSaasWebApi.Models.Auth;
using MicroSaasWebApi.Models.Auth.Clerk;
using MicroSaasWebApi.Services.Auth.Clerk.Interface;

namespace MicroSaasWebApi.Services.Auth.Interfaces
{
    /// <summary>
    /// Interface for Clerk authentication service
    /// Follows Interface Segregation Principle
    /// </summary>
    public interface IClerkAuthService
    {
        /// <summary>
        /// Validates Clerk JWT token and returns claims principal
        /// </summary>
        Task<ClaimsPrincipal?> ValidateClerkTokenAsync(string token);

        /// <summary>
        /// Authenticates user with email and password via Clerk
        /// </summary>
        Task<AuthResponse> AuthenticateAsync(string email, string password);

        /// <summary>
        /// Login user with Clerk
        /// </summary>
        Task<AuthResponse> LoginAsync(LoginRequest request);

        /// <summary>
        /// Registers new user via Clerk
        /// </summary>
        Task<AuthResponse> RegisterAsync(RegisterRequest request);

        /// <summary>
        /// Logout user from Clerk
        /// </summary>
        Task<bool> LogoutAsync(string userId);

        /// <summary>
        /// Gets user information from Clerk
        /// </summary>
        Task<ClerkUser?> GetUserAsync(string userId);

        /// <summary>
        /// Updates user information in Clerk
        /// </summary>
        Task<bool> UpdateUserAsync(string userId, string? firstName = null, string? lastName = null);

        /// <summary>
        /// Deletes user from Clerk
        /// </summary>
        Task<bool> DeleteUserAsync(string userId);

        /// <summary>
        /// Refreshes authentication token
        /// </summary>
        Task<AuthResponse> RefreshTokenAsync(string refreshToken);

        /// <summary>
        /// Signs out user from Clerk
        /// </summary>
        Task<bool> SignOutAsync(string userId);

        /// <summary>
        /// Gets the authenticated user ID from HTTP context
        /// </summary>
        Task<string?> GetUserIdAsync(HttpContext httpContext);

        /// <summary>
        /// Gets the authenticated user profile from HTTP context and database
        /// </summary>
        Task<UserInfo?> GetUserProfileAsync(HttpContext httpContext);
    }
}
