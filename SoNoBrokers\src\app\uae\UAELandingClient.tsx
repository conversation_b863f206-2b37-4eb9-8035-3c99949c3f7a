'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'motion/react'
import {
  Search, Star, ArrowRight, Home, Users, MapPin, Shield, DollarSign,
  Clock, CheckCircle, TrendingUp, Award, Phone, Mail, Globe,
  Calculator, FileText, Camera, Gavel, Building, Zap, Flag
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useAppContext } from '@/contexts/AppContext'

export function UAELandingClient() {
  // Get authentication state from AppContext
  const { isSignedIn } = useAppContext()
  const [searchQuery, setSearchQuery] = useState('')
  const router = useRouter()

  const handleSearch = () => {
    if (searchQuery.trim()) {
      router.push(`/uae/properties?search=${encodeURIComponent(searchQuery)}`)
    }
  }

  const handleStartBrowsing = () => {
    router.push('/uae/properties')
  }

  const handleListProperty = () => {
    router.push('/uae/list-property')
  }

  return (
    <div className="bg-background text-foreground">
      {/* Hero Section */}
      <section className="relative py-12 overflow-hidden">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <Badge variant="secondary" className="mb-4 text-sm px-4 py-2">
              🇦🇪 UAE's Revolutionary Commission-Free Platform
            </Badge>

            <h1 className="text-4xl md:text-6xl font-bold mb-4 leading-tight">
              Transform UAE<br />
              <span className="text-primary">Real Estate</span> Forever
            </h1>

            <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
              RERA-compliant commission-free platform serving all seven Emirates.
              Connect directly with property owners and save thousands on your next investment.
            </p>

            <div className="flex items-center max-w-2xl mx-auto mb-6 bg-card rounded-full shadow-md border overflow-hidden">
              <Input
                type="text"
                placeholder="Search Dubai, Abu Dhabi, Sharjah, Ajman..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 border-0 focus-visible:ring-0 text-base px-6 h-12 rounded-none bg-transparent"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button
                onClick={handleSearch}
                className="rounded-full bg-primary hover:bg-primary/90 text-primary-foreground px-6 h-10 m-1"
                size="sm"
              >
                <Search className="w-5 h-5" />
              </Button>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-center items-center mb-8">
              <Button
                onClick={handleStartBrowsing}
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 h-11 rounded-full font-semibold"
              >
                Browse Properties
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
              <Button
                onClick={handleListProperty}
                variant="outline"
                className="border-2 border-primary text-primary hover:bg-primary/10 px-8 h-11 rounded-full font-semibold"
              >
                List Your Property
              </Button>
            </div>

            <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-primary" />
                <span>RERA Compliant</span>
              </div>
              <div className="flex items-center space-x-2">
                <Globe className="h-4 w-4 text-primary" />
                <span>Arabic & English</span>
              </div>
              <div className="flex items-center space-x-2">
                <Flag className="h-4 w-4 text-primary" />
                <span>All 7 Emirates</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-8 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="space-y-2"
            >
              <div className="flex items-center justify-center mb-2">
                <Home className="w-6 h-6 text-primary mr-2" />
                <div className="text-3xl font-bold text-primary">25k+</div>
              </div>
              <div className="text-muted-foreground font-medium">Active Properties</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="space-y-2"
            >
              <div className="flex items-center justify-center mb-2">
                <Users className="w-6 h-6 text-primary mr-2" />
                <div className="text-3xl font-bold text-primary">15k+</div>
              </div>
              <div className="text-muted-foreground font-medium">Happy Clients</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="space-y-2"
            >
              <div className="flex items-center justify-center mb-2">
                <Flag className="w-6 h-6 text-primary mr-2" />
                <div className="text-3xl font-bold text-primary">7</div>
              </div>
              <div className="text-muted-foreground font-medium">Emirates Covered</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="space-y-2"
            >
              <div className="flex items-center justify-center mb-2">
                <DollarSign className="w-6 h-6 text-primary mr-2" />
                <div className="text-3xl font-bold text-primary">1.8B+</div>
              </div>
              <div className="text-muted-foreground font-medium">AED Commission Saved</div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Why Choose SoNoBrokers Section */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-6"
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-3">
              Why Choose SoNoBrokers UAE?
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              The UAE's most trusted commission-free real estate platform, built for the Emirates market.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              {
                icon: Shield,
                title: "RERA Compliance",
                description: "Fully compliant with Dubai Real Estate Regulatory Agency requirements and UAE property laws."
              },
              {
                icon: Globe,
                title: "Bilingual Platform",
                description: "Complete Arabic and English language support with cultural understanding of the UAE market."
              },
              {
                icon: DollarSign,
                title: "Zero Commission",
                description: "Save thousands of AED on traditional broker commissions. Keep more money for your investment."
              },
              {
                icon: Users,
                title: "Direct Communication",
                description: "Connect directly with property owners and investors. Transparent negotiations without middlemen."
              },
              {
                icon: TrendingUp,
                title: "Investment Analytics",
                description: "Advanced ROI calculators, market trends, and investment analysis tools for the UAE market."
              },
              {
                icon: Award,
                title: "Licensed Professionals",
                description: "Access to RERA-licensed professionals, UAE-qualified lawyers, and certified service providers."
              }
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <feature.icon className="h-8 w-8 text-primary mb-2" />
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Emirates Coverage */}
      <section className="py-8 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-6"
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-3">
              Coverage Across All Emirates
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              From Dubai's skyscrapers to Abu Dhabi's cultural districts, we serve all seven Emirates.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-3">
            {[
              { emirate: "Dubai", areas: ["Downtown", "Marina", "JBR"], properties: "12,000+" },
              { emirate: "Abu Dhabi", areas: ["Corniche", "Saadiyat", "Yas"], properties: "8,500+" },
              { emirate: "Sharjah", areas: ["Al Majaz", "Al Qasba", "Muwaileh"], properties: "3,200+" },
              { emirate: "Ajman", areas: ["Ajman Marina", "Al Nuaimiya", "Al Rashidiya"], properties: "1,800+" },
              { emirate: "Ras Al Khaimah", areas: ["Al Hamra", "Mina Al Arab", "Julfar"], properties: "900+" },
              { emirate: "Fujairah", areas: ["Fujairah City", "Dibba", "Kalba"], properties: "600+" },
              { emirate: "Umm Al Quwain", areas: ["UAQ Marina", "Al Salam", "Falaj Al Mualla"], properties: "400+" },
              { emirate: "Northern Emirates", areas: ["Various", "Locations", "Available"], properties: "500+" }
            ].map((region, index) => (
              <motion.div
                key={region.emirate}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">{region.emirate}</CardTitle>
                    <Badge variant="secondary" className="w-fit text-xs">
                      {region.properties}
                    </Badge>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-1">
                      {region.areas.map((area) => (
                        <div key={area} className="text-xs text-muted-foreground">
                          • {area}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Professional Services */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-6"
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-3">
              Professional Services Network
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Access the UAE's largest network of RERA-licensed real estate professionals.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              {
                icon: Gavel,
                title: "Legal Services",
                description: "UAE-qualified lawyers specializing in property law and RERA regulations.",
                features: ["Contract Review", "RERA Compliance", "Title Transfer"]
              },
              {
                icon: Calculator,
                title: "Mortgage & Finance",
                description: "Connect with UAE banks and Islamic finance institutions.",
                features: ["Conventional Loans", "Islamic Finance", "Investment Loans"]
              },
              {
                icon: Camera,
                title: "Photography & Media",
                description: "Professional real estate photography and virtual tours.",
                features: ["HDR Photography", "Virtual Tours", "Drone Videos"]
              }
            ].map((service, index) => (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <service.icon className="h-8 w-8 text-primary mb-2" />
                    <CardTitle className="text-lg">{service.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3 pt-0">
                    <p className="text-sm text-muted-foreground">
                      {service.description}
                    </p>
                    <div className="space-y-1">
                      {service.features.map((feature) => (
                        <div key={feature} className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-primary" />
                          <span className="text-xs">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-8 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-6"
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-3">
              How SoNoBrokers Works in UAE
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              RERA-compliant commission-free real estate platform designed for the UAE market.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-6">
            {[
              {
                step: "01",
                title: "Register & Verify",
                description: "Create account with Emirates ID verification. Choose investor, buyer, or seller profile.",
                icon: Users
              },
              {
                step: "02",
                title: "Browse or List",
                description: "Search RERA-verified properties or list with professional photography and documentation.",
                icon: Home
              },
              {
                step: "03",
                title: "Connect & Close",
                description: "Direct communication with owners/buyers. Use our network of UAE professionals for closing.",
                icon: Phone
              }
            ].map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="relative mb-4">
                  <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mx-auto mb-3">
                    <step.icon className="h-6 w-6 text-primary-foreground" />
                  </div>
                  <Badge variant="secondary" className="absolute -top-1 -right-1 text-xs font-bold">
                    {step.step}
                  </Badge>
                </div>
                <h3 className="text-lg font-bold mb-2">{step.title}</h3>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {step.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center bg-primary/5 rounded-xl p-6"
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-3">
              Ready to Transform UAE Real Estate?
            </h2>
            <p className="text-base text-muted-foreground mb-4 max-w-2xl mx-auto">
              Join thousands of UAE investors who have saved millions in commission fees.
            </p>

            <div className="flex flex-col sm:flex-row gap-3 justify-center items-center mb-4">
              <Button
                onClick={handleStartBrowsing}
                size="lg"
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-6"
              >
                Start Browsing Properties
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
              <Button
                onClick={handleListProperty}
                variant="outline"
                size="lg"
                className="border-2 border-primary text-primary hover:bg-primary/10 px-6"
              >
                List Your Property Free
              </Button>
            </div>

            <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                <span>RERA Compliant</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                <span>Arabic & English</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                <span>Licensed Professionals</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
