import { PricingCalculator } from '@/components/shared/resources/PricingCalculator'
import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function PricingCalculatorPage({ params, searchParams }: PageProps) {
  // No authentication required for resources pages
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us', 'uae']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/resources/pricing-calculator')
  }

  // Default to seller for pricing calculator
  const userType = resolvedSearchParams.userType || 'seller'

  return (
    <PricingCalculator
      userType={userType}
      isSignedIn={isSignedIn}
      country={country.toUpperCase()}
    />
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'seller'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Property Pricing Calculator for ${country} | SoNoBrokers`,
    description: `Free property valuation calculator for ${country}. Get instant estimates of your home's market value based on current market data and comparable sales.`,
    keywords: `property calculator, home valuation, market value, ${country}, property pricing, real estate calculator`,
  }
}
