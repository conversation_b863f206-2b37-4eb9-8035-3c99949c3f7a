import Image from 'next/image';
import Link from 'next/link';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { formatCurrency } from '@/lib/utils';
import { Property } from '@/types/property';
import { CompactContactShareButton } from '@/components/contact-sharing/ContactShareButton';
import { CompactPropertyActions } from '@/components/contact-sharing/PropertyActionButtons';
import { Bed, Bath, Square, MapPin, Eye } from 'lucide-react';

interface PropertyCardProps {
  property: Property;
}

export function PropertyCard({ property }: PropertyCardProps) {
  const primaryImage = property.images[0]?.url;

  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow group">
      {/* Property Image */}
      <div className="relative aspect-video">
        <Link href={`/properties/${property.id}`}>
          {primaryImage ? (
            <Image
              src={primaryImage}
              alt={property.title}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
            />
          ) : (
            <div className="w-full h-full bg-gray-200 dark:bg-gray-800 flex items-center justify-center">
              <div className="text-gray-400 text-center">
                <Square className="h-12 w-12 mx-auto mb-2" />
                <p className="text-sm">No Image</p>
              </div>
            </div>
          )}
        </Link>

        {/* Quick Actions Overlay */}
        <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="flex gap-2">
            <Button
              variant="secondary"
              size="sm"
              className="bg-white/90 hover:bg-white text-gray-900"
              asChild
            >
              <Link href={`/properties/${property.id}`}>
                <Eye className="h-4 w-4" />
              </Link>
            </Button>
            <CompactContactShareButton
              propertyId={property.id}
              sellerId={property.seller?.id || property.sellerId || ''}
              sellerName={property.seller?.full_name || 'Property Owner'}
              propertyTitle={property.title}
              propertyPrice={property.price}
              propertyAddress={`${property.address}, ${property.city}, ${property.state || ''}`}
              className="bg-white/90 hover:bg-white text-gray-900"
            />
          </div>
        </div>
      </div>

      {/* Property Details */}
      <CardHeader className="pb-2">
        <Link href={`/properties/${property.id}`}>
          <CardTitle className="line-clamp-1 hover:text-primary transition-colors">
            {property.title}
          </CardTitle>
        </Link>
        <div className="flex items-center text-sm text-muted-foreground">
          <MapPin className="h-4 w-4 mr-1" />
          <span className="line-clamp-1">{property.address}, {property.city}</span>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Price */}
        <div className="mb-3">
          <span className="text-2xl font-bold text-primary">
            {formatCurrency(property.price)}
          </span>
        </div>

        {/* Property Features */}
        <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
          <div className="flex items-center gap-1">
            <Bed className="h-4 w-4" />
            <span>{property.bedrooms}</span>
          </div>
          <div className="flex items-center gap-1">
            <Bath className="h-4 w-4" />
            <span>{property.bathrooms}</span>
          </div>
          {(property.square_footage || property.sqft) && (
            <div className="flex items-center gap-1">
              <Square className="h-4 w-4" />
              <span>{(property.square_footage || property.sqft)?.toLocaleString()} sqft</span>
            </div>
          )}
        </div>

        {/* Seller Info */}
        {property.seller && (
          <div className="flex items-center gap-2 mb-4">
            {property.seller.avatar_url && (
              <div className="relative w-6 h-6 rounded-full overflow-hidden">
                <Image
                  src={property.seller.avatar_url}
                  alt={property.seller.full_name}
                  fill
                  className="object-cover"
                />
              </div>
            )}
            <div className="text-sm">
              <p className="font-medium">{property.seller.full_name}</p>
              <p className="text-muted-foreground">{property.seller.role}</p>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-2">
          <Button variant="outline" size="sm" className="w-full" asChild>
            <Link href={`/properties/${property.id}`}>
              View Details
            </Link>
          </Button>

          <div className="flex gap-2">
            <CompactContactShareButton
              propertyId={property.id}
              sellerId={property.seller?.id || property.sellerId || ''}
              sellerName={property.seller?.full_name || 'Property Owner'}
              propertyTitle={property.title}
              propertyPrice={property.price}
              propertyAddress={`${property.address}, ${property.city}, ${property.state || ''}`}
              className="flex-1"
            />
            <CompactPropertyActions
              propertyId={property.id}
              sellerId={property.seller?.id || property.sellerId || ''}
              sellerName={property.seller?.full_name || 'Property Owner'}
              propertyTitle={property.title}
              propertyPrice={property.price}
              propertyAddress={`${property.address}, ${property.city}, ${property.state || ''}`}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
