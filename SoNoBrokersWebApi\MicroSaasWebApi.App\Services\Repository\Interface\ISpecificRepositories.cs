using MicroSaasWebApi.Models.Database;

namespace MicroSaasWebApi.Services.Repository.Interface
{
    /// <summary>
    /// User repository interface with specific operations
    /// </summary>
    public interface IUserRepository : IBaseRepository<User>, IDapperRepository<User>
    {
        Task<User?> GetByClerkUserIdAsync(string clerkUserId);
        Task<User?> GetByEmailAsync(string email);
        Task<IEnumerable<User>> GetActiveUsersAsync();
        Task<IEnumerable<User>> GetUsersByRoleAsync(string roleName);
        Task<bool> IsEmailUniqueAsync(string email, Guid? excludeUserId = null);
        Task<(IEnumerable<User> Users, int TotalCount)> SearchUsersAsync(
            string? searchTerm,
            int pageNumber,
            int pageSize,
            bool? isActive = null);
    }

    /// <summary>
    /// Subscription repository interface with specific operations
    /// </summary>
    public interface ISubscriptionRepository : IBaseRepository<Subscription>, IDapperRepository<Subscription>
    {
        Task<Subscription?> GetByStripeCustomerIdAsync(string stripeCustomerId);
        Task<Subscription?> GetByStripeSubscriptionIdAsync(string stripeSubscriptionId);
        Task<Subscription?> GetActiveSubscriptionByUserIdAsync(Guid userId);
        Task<IEnumerable<Subscription>> GetSubscriptionsByStatusAsync(string status);
        Task<IEnumerable<Subscription>> GetExpiringSubscriptionsAsync(DateTime beforeDate);
        Task<IEnumerable<Subscription>> GetSubscriptionsByPlanAsync(string planId);
        Task<decimal> GetTotalRevenueAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<int> GetActiveSubscriptionCountAsync();
    }

    /// <summary>
    /// Role repository interface with specific operations
    /// </summary>
    public interface IRoleRepository : IBaseRepository<Role>, IDapperRepository<Role>
    {
        Task<Role?> GetByNameAsync(string name);
        Task<IEnumerable<Role>> GetActiveRolesAsync();
        Task<IEnumerable<Role>> GetRolesByUserIdAsync(Guid userId);
        Task<bool> IsRoleNameUniqueAsync(string name, Guid? excludeRoleId = null);
        Task<IEnumerable<Permission>> GetPermissionsByRoleIdAsync(Guid roleId);
    }

    /// <summary>
    /// Permission repository interface with specific operations
    /// </summary>
    public interface IPermissionRepository : IBaseRepository<Permission>, IDapperRepository<Permission>
    {
        Task<Permission?> GetByResourceAndActionAsync(string resource, string action);
        Task<IEnumerable<Permission>> GetActivePermissionsAsync();
        Task<IEnumerable<Permission>> GetPermissionsByUserIdAsync(Guid userId);
        Task<IEnumerable<Permission>> GetPermissionsByRoleIdAsync(Guid roleId);
        Task<bool> IsPermissionUniqueAsync(string resource, string action, Guid? excludePermissionId = null);
        Task<IEnumerable<Permission>> GetPermissionsByResourceAsync(string resource);
    }

    /// <summary>
    /// Audit log repository interface with specific operations
    /// </summary>
    public interface IAuditLogRepository : IBaseRepository<AuditLog>, IDapperRepository<AuditLog>
    {
        Task<IEnumerable<AuditLog>> GetLogsByUserIdAsync(Guid userId, int pageNumber = 1, int pageSize = 50);
        Task<IEnumerable<AuditLog>> GetLogsByEntityAsync(string entityType, Guid entityId);
        Task<IEnumerable<AuditLog>> GetLogsByActionAsync(string action, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<AuditLog>> GetLogsByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<int> CleanupOldLogsAsync(DateTime beforeDate);
        Task<(IEnumerable<AuditLog> Logs, int TotalCount)> SearchLogsAsync(
            string? searchTerm,
            string? action,
            string? entityType,
            Guid? userId,
            DateTime? fromDate,
            DateTime? toDate,
            int pageNumber,
            int pageSize);
    }
}
