// Removed auth import - no authentication required for browsing AI property creator
import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function AIPropertyCreatorPage({ params, searchParams }: PageProps) {
  // No authentication required for browsing AI property creator
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/services/ai-property-creator')
  }

  const userType = resolvedSearchParams.userType || 'seller'

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8">
            AI Property Creator
          </h1>

          <div className="bg-gradient-to-r from-primary/10 to-blue-500/10 rounded-lg p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-4">Create Professional Listings with AI</h2>
            <p className="text-muted-foreground mb-6">
              Transform your property into a compelling listing with our AI-powered tools. Generate professional
              descriptions, optimize photos, and create marketing materials that attract qualified buyers.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <div className="bg-card rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">AI Features</h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                  <div>
                    <div className="font-semibold">Smart Descriptions</div>
                    <div className="text-sm text-muted-foreground">AI-generated property descriptions that highlight key features</div>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                  <div>
                    <div className="font-semibold">Photo Enhancement</div>
                    <div className="text-sm text-muted-foreground">Automatic photo optimization and virtual staging</div>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                  <div>
                    <div className="font-semibold">Market Analysis</div>
                    <div className="text-sm text-muted-foreground">AI-powered pricing recommendations based on market data</div>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                  <div>
                    <div className="font-semibold">Marketing Materials</div>
                    <div className="text-sm text-muted-foreground">Automated flyers, social media posts, and listing sheets</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">How It Works</h3>
              <div className="space-y-4">
                <div className="flex gap-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold">1</div>
                  <div>
                    <div className="font-semibold text-sm">Upload Property Details</div>
                    <div className="text-xs text-muted-foreground">Add photos, features, and basic information</div>
                  </div>
                </div>

                <div className="flex gap-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold">2</div>
                  <div>
                    <div className="font-semibold text-sm">AI Analysis</div>
                    <div className="text-xs text-muted-foreground">Our AI analyzes your property and market data</div>
                  </div>
                </div>

                <div className="flex gap-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold">3</div>
                  <div>
                    <div className="font-semibold text-sm">Generate Content</div>
                    <div className="text-xs text-muted-foreground">Professional listing and marketing materials created</div>
                  </div>
                </div>

                <div className="flex gap-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold">4</div>
                  <div>
                    <div className="font-semibold text-sm">Review & Publish</div>
                    <div className="text-xs text-muted-foreground">Review, edit, and publish your listing</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6 mb-8">
            <h3 className="text-xl font-semibold mb-4">Sample AI-Generated Description</h3>
            <div className="bg-muted rounded-lg p-4 mb-4">
              <p className="text-sm italic">
                "Welcome to this stunning 3-bedroom, 2-bathroom home nestled in the heart of a desirable neighborhood.
                This beautifully maintained property features an open-concept living space with hardwood floors throughout,
                a modern kitchen with granite countertops and stainless steel appliances, and a spacious master suite with
                walk-in closet. The private backyard offers the perfect space for entertaining, while the attached garage
                provides convenient parking. Located within walking distance of top-rated schools and local amenities,
                this home offers the perfect blend of comfort and convenience."
              </p>
            </div>
            <div className="text-xs text-muted-foreground">
              ✨ Generated in seconds based on property features and market trends
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-6 mb-8">
            <div className="bg-card rounded-lg p-6 text-center">
              <div className="text-2xl font-bold text-primary mb-2">90%</div>
              <div className="text-sm text-muted-foreground">Faster Listing Creation</div>
            </div>

            <div className="bg-card rounded-lg p-6 text-center">
              <div className="text-2xl font-bold text-primary mb-2">35%</div>
              <div className="text-sm text-muted-foreground">More Qualified Inquiries</div>
            </div>

            <div className="bg-card rounded-lg p-6 text-center">
              <div className="text-2xl font-bold text-primary mb-2">15</div>
              <div className="text-sm text-muted-foreground">Days Faster Sale</div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4">Get Started with AI Property Creator</h3>
            <p className="text-muted-foreground mb-6">
              Ready to create a professional property listing in minutes? Our AI tools are designed to help you
              showcase your property's best features and attract serious buyers.
            </p>

            <div className="flex gap-4">
              <button className="bg-primary text-primary-foreground px-8 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors">
                Start Creating Listing
              </button>
              <button className="border border-border px-6 py-3 rounded-lg font-semibold hover:bg-muted transition-colors">
                View Sample Listings
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'seller'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `AI Property Creator for ${country} | SoNoBrokers`,
    description: `Create professional property listings with AI in ${country}. Generate descriptions, optimize photos, and create marketing materials automatically.`,
    keywords: `AI property listing, automated listing creation, property marketing, ${country}, real estate AI`,
  }
}
