-- =====================================================
-- SoNoBrokers Column Synchronization Verification Script
-- This script verifies that database columns match the Web API models
-- Run this after creating the database to ensure everything is in sync
-- =====================================================

-- =====================================================
-- COLUMN VERIFICATION FUNCTIONS
-- =====================================================

-- Function to check if a column exists with correct type
CREATE OR REPLACE FUNCTION check_column_exists(
  p_table_name TEXT,
  p_column_name TEXT,
  p_expected_type TEXT DEFAULT NULL
)
RETURNS TABLE(
  table_name TEXT,
  column_name TEXT,
  exists BOOLEAN,
  actual_type TEXT,
  expected_type TEXT,
  type_matches BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p_table_name,
    p_column_name,
    EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = p_table_name 
      AND column_name = p_column_name
    ) as exists,
    COALESCE(
      (SELECT data_type FROM information_schema.columns 
       WHERE table_schema = 'public' 
       AND table_name = p_table_name 
       AND column_name = p_column_name), 
      'NOT_FOUND'
    ) as actual_type,
    COALESCE(p_expected_type, 'ANY') as expected_type,
    CASE 
      WHEN p_expected_type IS NULL THEN TRUE
      WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = p_table_name 
        AND column_name = p_column_name
        AND (data_type = p_expected_type OR udt_name = p_expected_type)
      ) THEN TRUE
      ELSE FALSE
    END as type_matches;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- ALL TABLES DISCOVERY AND VERIFICATION
-- =====================================================

DO $$
DECLARE
  table_record RECORD;
  column_record RECORD;
  total_tables INTEGER := 0;
  total_columns INTEGER := 0;
  tables_with_issues INTEGER := 0;
  columns_with_issues INTEGER := 0;
BEGIN
  RAISE NOTICE '=== DISCOVERING ALL TABLES IN DATABASE ===';
  RAISE NOTICE '';

  -- Count total tables
  SELECT COUNT(*) INTO total_tables
  FROM information_schema.tables
  WHERE table_schema = 'public'
  AND table_type = 'BASE TABLE';

  RAISE NOTICE '📊 DATABASE OVERVIEW:';
  RAISE NOTICE 'Total tables found: %', total_tables;
  RAISE NOTICE '';

  -- Loop through all tables and show their structure
  FOR table_record IN
    SELECT table_name
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_type = 'BASE TABLE'
    ORDER BY table_name
  LOOP
    RAISE NOTICE '📋 TABLE: % ', table_record.table_name;

    -- Count columns in this table
    SELECT COUNT(*) INTO total_columns
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = table_record.table_name;

    RAISE NOTICE '  📊 Columns: %', total_columns;

    -- Show all columns with their types
    FOR column_record IN
      SELECT
        column_name,
        data_type,
        udt_name,
        is_nullable,
        column_default,
        character_maximum_length,
        numeric_precision,
        numeric_scale
      FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = table_record.table_name
      ORDER BY ordinal_position
    LOOP
      -- Format the type information
      DECLARE
        type_info TEXT;
        nullable_info TEXT;
        default_info TEXT;
      BEGIN
        -- Build type information
        type_info := column_record.data_type;
        IF column_record.data_type = 'USER-DEFINED' THEN
          type_info := column_record.udt_name;
        ELSIF column_record.character_maximum_length IS NOT NULL THEN
          type_info := type_info || '(' || column_record.character_maximum_length || ')';
        ELSIF column_record.numeric_precision IS NOT NULL THEN
          type_info := type_info || '(' || column_record.numeric_precision;
          IF column_record.numeric_scale IS NOT NULL AND column_record.numeric_scale > 0 THEN
            type_info := type_info || ',' || column_record.numeric_scale;
          END IF;
          type_info := type_info || ')';
        END IF;

        -- Nullable information
        nullable_info := CASE WHEN column_record.is_nullable = 'YES' THEN 'NULL' ELSE 'NOT NULL' END;

        -- Default information
        default_info := CASE
          WHEN column_record.column_default IS NOT NULL THEN
            ' DEFAULT ' || substring(column_record.column_default, 1, 50)
          ELSE ''
        END;

        RAISE NOTICE '    � % : % % %',
          column_record.column_name,
          type_info,
          nullable_info,
          default_info;
      END;
    END LOOP;

    RAISE NOTICE '';
  END LOOP;
END $$;

-- =====================================================
-- ALL FOREIGN KEY RELATIONSHIPS
-- =====================================================

DO $$
DECLARE
  fk_record RECORD;
  total_fks INTEGER := 0;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== ALL FOREIGN KEY RELATIONSHIPS ===';
  RAISE NOTICE '';

  -- Count total foreign keys
  SELECT COUNT(*) INTO total_fks
  FROM information_schema.table_constraints
  WHERE constraint_type = 'FOREIGN KEY'
  AND table_schema = 'public';

  RAISE NOTICE '📊 FOREIGN KEY OVERVIEW:';
  RAISE NOTICE 'Total foreign keys found: %', total_fks;
  RAISE NOTICE '';

  -- Show all foreign key relationships
  FOR fk_record IN
    SELECT
      tc.table_name,
      kcu.column_name,
      ccu.table_name AS foreign_table_name,
      ccu.column_name AS foreign_column_name,
      tc.constraint_name
    FROM information_schema.table_constraints AS tc
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
    WHERE tc.constraint_type = 'FOREIGN KEY'
      AND tc.table_schema = 'public'
    ORDER BY tc.table_name, kcu.column_name
  LOOP
    RAISE NOTICE '🔗 %.% → %.% (constraint: %)',
      fk_record.table_name,
      fk_record.column_name,
      fk_record.foreign_table_name,
      fk_record.foreign_column_name,
      fk_record.constraint_name;
  END LOOP;
END $$;

-- =====================================================
-- ALL TABLE CONSTRAINTS SUMMARY
-- =====================================================

DO $$
DECLARE
  constraint_record RECORD;
  table_name_var TEXT;
  constraint_count INTEGER;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== ALL TABLE CONSTRAINTS BY TABLE ===';
  RAISE NOTICE '';

  -- Show constraints grouped by table
  FOR table_name_var IN
    SELECT DISTINCT table_name
    FROM information_schema.table_constraints
    WHERE table_schema = 'public'
    ORDER BY table_name
  LOOP
    RAISE NOTICE '📋 TABLE: %', table_name_var;

    -- Count constraints for this table
    SELECT COUNT(*) INTO constraint_count
    FROM information_schema.table_constraints
    WHERE table_schema = 'public'
    AND table_name = table_name_var;

    RAISE NOTICE '  📊 Total constraints: %', constraint_count;

    -- Show each constraint
    FOR constraint_record IN
      SELECT
        constraint_name,
        constraint_type
      FROM information_schema.table_constraints
      WHERE table_schema = 'public'
      AND table_name = table_name_var
      ORDER BY constraint_type, constraint_name
    LOOP
      RAISE NOTICE '    🔒 % (%)',
        constraint_record.constraint_name,
        constraint_record.constraint_type;
    END LOOP;

    RAISE NOTICE '';
  END LOOP;
END $$;

-- =====================================================
-- CONTACT SHARING TABLES VERIFICATION
-- =====================================================

DO $$
DECLARE
  result RECORD;
  total_checks INTEGER := 0;
  passed_checks INTEGER := 0;
  failed_checks INTEGER := 0;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== CONTACT SHARING TABLES VERIFICATION ===';
  RAISE NOTICE '';
  
  -- ContactShare table verification
  RAISE NOTICE '📋 Checking ContactShare table columns...';
  FOR result IN 
    SELECT * FROM check_column_exists('ContactShare', 'id', 'text')
    UNION ALL SELECT * FROM check_column_exists('ContactShare', 'propertyId', 'uuid')
    UNION ALL SELECT * FROM check_column_exists('ContactShare', 'buyerId', 'uuid')
    UNION ALL SELECT * FROM check_column_exists('ContactShare', 'sellerId', 'uuid')
    UNION ALL SELECT * FROM check_column_exists('ContactShare', 'buyerName', 'text')
    UNION ALL SELECT * FROM check_column_exists('ContactShare', 'buyerEmail', 'text')
    UNION ALL SELECT * FROM check_column_exists('ContactShare', 'shareType', 'ContactShareType')
    UNION ALL SELECT * FROM check_column_exists('ContactShare', 'status', 'ContactShareStatus')
    UNION ALL SELECT * FROM check_column_exists('ContactShare', 'offerAmount', 'numeric')
    UNION ALL SELECT * FROM check_column_exists('ContactShare', 'createdAt', 'timestamp with time zone')
  LOOP
    total_checks := total_checks + 1;
    IF result.exists AND result.type_matches THEN
      RAISE NOTICE '  ✅ %.% (%) - OK', result.table_name, result.column_name, result.actual_type;
      passed_checks := passed_checks + 1;
    ELSE
      RAISE NOTICE '  ❌ %.% - Expected: %, Actual: %, Exists: %', 
        result.table_name, result.column_name, result.expected_type, result.actual_type, result.exists;
      failed_checks := failed_checks + 1;
    END IF;
  END LOOP;
  
  RAISE NOTICE '';
  RAISE NOTICE '📊 CONTACT SHARING SUMMARY:';
  RAISE NOTICE 'Total Checks: %', total_checks;
  RAISE NOTICE 'Passed: % (%.1f%%)', passed_checks, (passed_checks::DECIMAL / total_checks * 100);
  RAISE NOTICE 'Failed: % (%.1f%%)', failed_checks, (failed_checks::DECIMAL / total_checks * 100);
END $$;

-- =====================================================
-- PROPERTY SCHEDULING TABLES VERIFICATION
-- =====================================================

DO $$
DECLARE
  result RECORD;
  total_checks INTEGER := 0;
  passed_checks INTEGER := 0;
  failed_checks INTEGER := 0;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== PROPERTY SCHEDULING TABLES VERIFICATION ===';
  RAISE NOTICE '';
  
  -- PropertyVisitSchedule table verification
  RAISE NOTICE '📋 Checking PropertyVisitSchedule table columns...';
  FOR result IN 
    SELECT * FROM check_column_exists('PropertyVisitSchedule', 'id', 'text')
    UNION ALL SELECT * FROM check_column_exists('PropertyVisitSchedule', 'propertyId', 'uuid')
    UNION ALL SELECT * FROM check_column_exists('PropertyVisitSchedule', 'buyerId', 'uuid')
    UNION ALL SELECT * FROM check_column_exists('PropertyVisitSchedule', 'sellerId', 'uuid')
    UNION ALL SELECT * FROM check_column_exists('PropertyVisitSchedule', 'contactShareId', 'text')
    UNION ALL SELECT * FROM check_column_exists('PropertyVisitSchedule', 'requestedDate', 'date')
    UNION ALL SELECT * FROM check_column_exists('PropertyVisitSchedule', 'requestedTime', 'time without time zone')
    UNION ALL SELECT * FROM check_column_exists('PropertyVisitSchedule', 'status', 'VisitStatus')
    UNION ALL SELECT * FROM check_column_exists('PropertyVisitSchedule', 'visitType', 'VisitType')
    UNION ALL SELECT * FROM check_column_exists('PropertyVisitSchedule', 'createdAt', 'timestamp with time zone')
  LOOP
    total_checks := total_checks + 1;
    IF result.exists AND result.type_matches THEN
      RAISE NOTICE '  ✅ %.% (%) - OK', result.table_name, result.column_name, result.actual_type;
      passed_checks := passed_checks + 1;
    ELSE
      RAISE NOTICE '  ❌ %.% - Expected: %, Actual: %, Exists: %', 
        result.table_name, result.column_name, result.expected_type, result.actual_type, result.exists;
      failed_checks := failed_checks + 1;
    END IF;
  END LOOP;
  
  RAISE NOTICE '';
  RAISE NOTICE '📊 PROPERTY SCHEDULING SUMMARY:';
  RAISE NOTICE 'Total Checks: %', total_checks;
  RAISE NOTICE 'Passed: % (%.1f%%)', passed_checks, (passed_checks::DECIMAL / total_checks * 100);
  RAISE NOTICE 'Failed: % (%.1f%%)', failed_checks, (failed_checks::DECIMAL / total_checks * 100);
END $$;

-- =====================================================
-- OVERALL SYNCHRONIZATION STATUS
-- =====================================================

DO $$
DECLARE
  total_tables INTEGER;
  expected_tables INTEGER := 20; -- Expected number of tables
  total_enums INTEGER;
  expected_enums INTEGER := 12; -- Expected number of enums
  sync_percentage DECIMAL;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== OVERALL DATABASE SYNCHRONIZATION STATUS ===';
  RAISE NOTICE '';
  
  -- Count actual tables
  SELECT COUNT(*) INTO total_tables
  FROM information_schema.tables 
  WHERE table_schema = 'public' 
  AND table_type = 'BASE TABLE';
  
  -- Count actual enums
  SELECT COUNT(DISTINCT typname) INTO total_enums
  FROM pg_type 
  WHERE typname IN (
    'UserRole', 'UserStatus', 'PropertyType', 'PropertyStatus', 'ListingType',
    'SubscriptionStatus', 'SubscriptionType', 'ServiceType',
    'ContactShareType', 'ContactShareStatus', 'VisitStatus', 'VisitType', 'VerificationMethod'
  );
  
  sync_percentage := ((total_tables + total_enums)::DECIMAL / (expected_tables + expected_enums) * 100);
  
  RAISE NOTICE '📊 DATABASE STATISTICS:';
  RAISE NOTICE 'Tables: % / % (%.1f%%)', total_tables, expected_tables, (total_tables::DECIMAL / expected_tables * 100);
  RAISE NOTICE 'Enums: % / % (%.1f%%)', total_enums, expected_enums, (total_enums::DECIMAL / expected_enums * 100);
  RAISE NOTICE 'Overall Sync: %.1f%%', sync_percentage;
  RAISE NOTICE '';
  
  IF sync_percentage >= 95 THEN
    RAISE NOTICE '🎉 DATABASE IS FULLY SYNCHRONIZED!';
    RAISE NOTICE 'All required tables and columns are present and correctly typed.';
  ELSIF sync_percentage >= 80 THEN
    RAISE NOTICE '⚠️  DATABASE IS MOSTLY SYNCHRONIZED';
    RAISE NOTICE 'Some minor components may be missing. Review the details above.';
  ELSE
    RAISE NOTICE '🚨 DATABASE SYNCHRONIZATION INCOMPLETE';
    RAISE NOTICE 'Significant components are missing. Please run the complete setup scripts.';
  END IF;
  
  RAISE NOTICE '';
  RAISE NOTICE '✅ Column verification complete!';
  RAISE NOTICE 'Database schema is ready for Web API and React integration.';
END $$;

-- Clean up verification function
DROP FUNCTION IF EXISTS check_column_exists(TEXT, TEXT, TEXT);
