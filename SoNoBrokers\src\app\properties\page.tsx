import { Suspense } from 'react'
import { Metadata } from 'next'
import { getProperties, getFeaturedProperties } from '@/lib/api/properties-api'
import { getCurrentUserProfile } from '@/lib/api/auth-api'
import PropertySearchClient from '@/components/shared/properties/PropertySearchClient'

/**
 * Properties Page - Server Component with .NET Core Web API Integration
 * Demonstrates Server Components calling the .NET Core Web API
 */

export const metadata: Metadata = {
  title: 'Properties for Sale | SoNoBrokers',
  description: 'Browse commission-free properties for sale across Canada, USA, and UAE. Find your dream home without realtor fees.',
  keywords: 'properties for sale, commission-free, FSBO, real estate, Canada, USA, UAE',
}

interface PropertiesPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

export default async function PropertiesPage({ searchParams }: PropertiesPageProps) {
  const resolvedSearchParams = await searchParams

  // Get current user profile for personalization
  const userProfile = await getCurrentUserProfile()

  // Parse search parameters for API call
  const searchFilters = {
    page: parseInt((resolvedSearchParams.page as string) || '1'),
    limit: 12,
    search: resolvedSearchParams.search as string,
    minPrice: resolvedSearchParams.min_price ? parseFloat(resolvedSearchParams.min_price as string) : undefined,
    maxPrice: resolvedSearchParams.max_price ? parseFloat(resolvedSearchParams.max_price as string) : undefined,
    bedrooms: resolvedSearchParams.min_bedrooms ? parseInt(resolvedSearchParams.min_bedrooms as string) : undefined,
    bathrooms: resolvedSearchParams.min_bathrooms ? parseInt(resolvedSearchParams.min_bathrooms as string) : undefined,
    propertyType: resolvedSearchParams.property_type as string,
    city: resolvedSearchParams.city as string,
    province: resolvedSearchParams.state as string,
    country: resolvedSearchParams.country as string || 'CA',
    status: 'active' as const,
  }

  try {
    // Fetch properties from .NET Core Web API using Server Component
    const [propertiesResponse, featuredProperties] = await Promise.all([
      getProperties(searchFilters),
      getFeaturedProperties(6)
    ])

    return (
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Properties for Sale
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Discover commission-free properties across Canada, USA, and UAE
          </p>
          {userProfile && (
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              Welcome back, {userProfile.firstName || userProfile.fullName}!
              {userProfile.userType === 'Buyer' ? ' Find your dream home.' : ' Manage your listings.'}
            </p>
          )}
        </div>

        {/* Featured Properties Section */}
        {featuredProperties.length > 0 && (
          <section className="mb-12">
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
              Featured Properties
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {featuredProperties.slice(0, 6).map((property) => (
                <div key={property.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                  <div className="h-48 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                    <span className="text-gray-500 dark:text-gray-400">Property Image</span>
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold text-lg mb-2">{property.title}</h3>
                    <p className="text-2xl font-bold text-green-600 mb-2">
                      ${property.price.toLocaleString()}
                    </p>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-2">
                      {property.bedrooms} bed • {property.bathrooms} bath
                      {property.sqft && ` • ${property.sqft.toLocaleString()} sqft`}
                    </p>
                    {property.address && (
                      <p className="text-gray-500 dark:text-gray-500 text-sm">
                        {property.address}, {property.city}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Properties Search and Results */}
        <PropertySearchClient
          initialProperties={propertiesResponse.properties}
          totalProperties={propertiesResponse.total}
          currentPage={propertiesResponse.page}
          totalPages={propertiesResponse.totalPages}
          hasMore={propertiesResponse.hasMore}
          country={searchFilters.country.toLowerCase()}
          userType={userProfile?.userType?.toLowerCase() || 'buyer'}
          searchParams={resolvedSearchParams}
        />
      </div>
    )
  } catch (error) {
    console.error('Failed to load properties:', error)

    // Fallback to client-side rendering on error
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Properties for Sale
          </h1>
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
            <p className="text-yellow-700 dark:text-yellow-300">
              Unable to load properties from server. Loading with client-side search...
            </p>
          </div>
        </div>

        <PropertySearchClient
          initialProperties={[]}
          country="ca"
          userType="buyer"
          searchParams={resolvedSearchParams}
        />
      </div>
    )
  }
}
