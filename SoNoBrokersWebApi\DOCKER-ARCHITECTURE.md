# SoNoBrokers Web API - Docker Architecture

## 🏗️ **Separated Docker Structure**

The Docker configuration has been restructured to provide clean separation between projects:

```
SoNoBrokersWebApi/
├── docker-compose.master.yml          # Master orchestration file
├── MicroSaasWebApi.App/               # Main Web API Application
│   ├── Dockerfile
│   ├── docker-compose.yml
│   ├── docker-compose.dev.yml
│   ├── docker-run.ps1
│   ├── .env
│   ├── .env.docker
│   ├── .env.docker.development
│   ├── .env.docker.production
│   └── DOCKER-README.md
├── MicroSaasWebApi.Config/            # Configuration Server
│   ├── Dockerfile
│   └── docker-compose.yml
└── MicroSaasWebApi.Tests/             # Test Runner
    ├── Dockerfile
    └── docker-compose.yml
```

## 🚀 **Usage Instructions**

### **Individual Project Deployment**

#### 1. Deploy Web API Only
```bash
cd SoNoBrokersWebApi/MicroSaasWebApi.App
docker-compose up -d
```
- **API URL**: `https://localhost:7163`
- **Health Check**: `https://localhost:7163/api/health/ping`
- **Documentation**: `https://localhost:7163/scalar/v1`

#### 2. Deploy Configuration Server Only
```bash
cd SoNoBrokersWebApi/MicroSaasWebApi.Config
docker-compose up -d
```
- **Config Server**: `http://localhost:8080`
- **Health Check**: `http://localhost:8080/health`

#### 3. Run Tests Only
```bash
cd SoNoBrokersWebApi/MicroSaasWebApi.Tests
docker-compose up
```
- **Test Results**: Available in container volumes

### **Complete System Deployment**

#### Deploy All Services Together
```bash
cd SoNoBrokersWebApi
docker-compose -f docker-compose.master.yml up -d
```

#### Deploy with Testing Profile
```bash
cd SoNoBrokersWebApi
docker-compose -f docker-compose.master.yml --profile testing up -d
```

#### Deploy Development Environment
```bash
cd SoNoBrokersWebApi
docker-compose -f docker-compose.master.yml --profile development up -d
```

## 🔧 **Configuration Management**

### **Environment Variables**

Each project manages its own environment variables:

- **MicroSaasWebApi.App**: `.env`, `.env.docker`, `.env.docker.development`, `.env.docker.production`
- **MicroSaasWebApi.Config**: Uses mounted volumes for configuration files
- **MicroSaasWebApi.Tests**: Uses test-specific environment variables

### **CORS Configuration**

All projects are configured with the standardized CORS origins:
```
https://localhost:3000
https://www.api.sonobrokers.com
https://sonobrokers.com
https://localhost:7163
```

## 📊 **Service Ports**

| Service | HTTP Port | HTTPS Port | Purpose |
|---------|-----------|------------|---------|
| Web API | 7163 | 7164 | Main application |
| Config Server | 8080 | - | Configuration management |
| Tests | - | - | Test execution |

## 🔍 **Health Checks**

Each service includes comprehensive health checks:

- **Web API**: `/api/health/ping`, `/api/health`, `/api/sonobrokers/test/health`
- **Config Server**: `/health`, `/api/config`
- **Tests**: Automated test result validation

## 🌐 **Production Deployment**

For production deployment to `https://www.sonobrokers.com`:

1. Update environment variables in `.env.docker.production`
2. Use the master docker-compose file
3. Configure reverse proxy/load balancer
4. Set up SSL certificates
5. Configure monitoring and logging

## 📝 **Development Workflow**

1. **Local Development**: Run individual services as needed
2. **Integration Testing**: Use master docker-compose with testing profile
3. **Production Testing**: Use production environment variables
4. **CI/CD**: Automated deployment using individual Dockerfiles

## 🛠️ **Maintenance**

- **Logs**: Each service maintains separate log volumes
- **Updates**: Update individual services independently
- **Scaling**: Scale services based on specific requirements
- **Monitoring**: Each service has dedicated health checks and metrics
