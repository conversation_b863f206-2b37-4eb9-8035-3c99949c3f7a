import { redirect } from 'next/navigation'
import { ProfessionalAppraisalService } from './ProfessionalAppraisalService'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function ProfessionalAppraisalPage({ params, searchParams }: PageProps) {
  // Services pages don't require authentication
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us', 'uae']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/services/professional-appraisal')
  }

  const userType = resolvedSearchParams.userType || 'seller'

  return (
    <ProfessionalAppraisalService
      userType={userType}
      isSignedIn={isSignedIn}
      country={country.toUpperCase()}
    />
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'seller'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Professional Property Appraisal Services in ${country} | SoNoBrokers`,
    description: `Find certified property appraisers in ${country}. Get accurate property valuations from licensed professionals for buying, selling, or refinancing.`,
    keywords: `property appraisal, certified appraisers, property valuation, ${country}, real estate appraisal`,
  }
}
