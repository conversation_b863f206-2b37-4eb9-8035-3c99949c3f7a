using MicroSaasWebApi.Models.Core;

namespace MicroSaasWebApi.Services.Core.Interfaces
{
    /// <summary>
    /// User service interface for user management operations
    /// </summary>
    public interface IUserService
    {
        /// <summary>
        /// Get users with pagination and filtering
        /// </summary>
        Task<IEnumerable<User>> GetUsersAsync(int page, int pageSize, string? search = null, bool? isActive = null);

        /// <summary>
        /// Get user by ID
        /// </summary>
        Task<User?> GetUserByIdAsync(Guid id);

        /// <summary>
        /// Get user by Clerk ID
        /// </summary>
        Task<User?> GetUserByClerkIdAsync(string clerkUserId);

        /// <summary>
        /// Get user by email
        /// </summary>
        Task<User?> GetUserByEmailAsync(string email);

        /// <summary>
        /// Create new user
        /// </summary>
        Task<User> CreateUserAsync(User user);

        /// <summary>
        /// Update existing user
        /// </summary>
        Task<User> UpdateUserAsync(User user);

        /// <summary>
        /// Delete user (soft delete)
        /// </summary>
        Task<bool> DeleteUserAsync(Guid id);

        /// <summary>
        /// Update user's last login timestamp
        /// </summary>
        Task<bool> UpdateLastLoginAsync(Guid id);

        /// <summary>
        /// Verify user's email
        /// </summary>
        Task<bool> VerifyEmailAsync(Guid id);

        /// <summary>
        /// Activate/deactivate user
        /// </summary>
        Task<bool> SetUserActiveStatusAsync(Guid id, bool isActive);

        /// <summary>
        /// Get user count
        /// </summary>
        Task<int> GetUserCountAsync(bool? isActive = null);

        /// <summary>
        /// Check if user exists by email
        /// </summary>
        Task<bool> UserExistsByEmailAsync(string email);

        /// <summary>
        /// Check if user exists by Clerk ID
        /// </summary>
        Task<bool> UserExistsByClerkIdAsync(string clerkUserId);

        /// <summary>
        /// Get users by role
        /// </summary>
        Task<IEnumerable<User>> GetUsersByRoleAsync(string role, int page = 1, int pageSize = 10);

        /// <summary>
        /// Search users by name or email
        /// </summary>
        Task<IEnumerable<User>> SearchUsersAsync(string searchTerm, int page = 1, int pageSize = 10);
    }
}
