# Quick fix for IIDapperDbContext -> IDapperDbContext
param(
    [string]$RootPath = ".",
    [switch]$WhatIf = $false
)

Write-Host "Fixing IIDapperDbContext references..." -ForegroundColor Green

# Get all C# files
$files = Get-ChildItem -Path $RootPath -Include "*.cs" -Recurse

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Fix the double I issue
    $content = $content -replace '\bIIDapperDbContext\b', 'IDapperDbContext'
    
    if ($content -ne $originalContent) {
        if ($WhatIf) {
            Write-Host "  [WHAT-IF] Would update $($file.Name)" -ForegroundColor Magenta
        } else {
            Set-Content $file.FullName $content -NoNewline
            Write-Host "  Updated $($file.Name)" -ForegroundColor Green
        }
    }
}

Write-Host "Double I fix completed!" -ForegroundColor Green
