import React from 'react'
import { ServiceLayout } from '@/components/shared/services/ServiceLayout'

interface HomeRenovationServiceProps {
  userType: 'buyer' | 'seller'
  isSignedIn: boolean
  country: string
}

// Mock data for home renovation contractors
function getMockRenovationContractors(country: string) {
  const baseContractors = [
    {
      id: 'reno-001',
      name: '<PERSON>',
      businessName: 'Elite Home Renovations',
      serviceType: 'General Contractor',
      location: country === 'CA' ? 'Toronto, ON' : country === 'US' ? 'New York, NY' : 'Dubai, UAE',
      distance: '1.8 km',
      rating: 4.9,
      reviewCount: 234,
      price: country === 'CA' ? '$85-120/hr' : country === 'US' ? '$75-110/hr' : 'AED 250-350/hr',
      specialties: ['Kitchen Renovations', 'Bathroom Remodeling', 'Basement Finishing', 'Painting'],
      verified: true,
      isAdvertiser: true,
      isPremium: true,
      image: '/api/placeholder/300/200',
      phone: country === 'CA' ? '+****************' : country === 'US' ? '+****************' : '+971 4 555 0123',
      email: '<EMAIL>',
      website: 'https://eliterenovations.com',
      description: 'Full-service home renovation contractor with 20+ years experience in residential projects.',
      coordinates: { lat: 43.6532, lng: -79.3832 },
      advertiser: {
        plan: 'premium' as const,
        status: 'active' as const,
        featuredUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        isPremium: true
      }
    },
    {
      id: 'reno-002',
      name: 'Sarah Martinez',
      businessName: 'Precision Painting & Renovations',
      serviceType: 'Painting & General Contracting',
      location: country === 'CA' ? 'Mississauga, ON' : country === 'US' ? 'Los Angeles, CA' : 'Abu Dhabi, UAE',
      distance: '3.2 km',
      rating: 4.8,
      reviewCount: 156,
      price: country === 'CA' ? '$65-95/hr' : country === 'US' ? '$55-85/hr' : 'AED 200-300/hr',
      specialties: ['Interior Painting', 'Exterior Painting', 'Drywall Repair', 'Trim Work'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/300/200',
      phone: country === 'CA' ? '+****************' : country === 'US' ? '+****************' : '+971 2 555 0456',
      email: '<EMAIL>',
      website: 'https://precisionpainting.com',
      description: 'Professional painting and renovation services with attention to detail and quality finishes.',
      coordinates: { lat: 43.5890, lng: -79.6441 },
      advertiser: {
        plan: 'basic' as const,
        status: 'active' as const,
        isPremium: false
      }
    },
    {
      id: 'reno-003',
      name: 'David Chen',
      businessName: 'Superior Roofing Solutions',
      serviceType: 'Roofing Contractor',
      location: country === 'CA' ? 'Vaughan, ON' : country === 'US' ? 'Chicago, IL' : 'Sharjah, UAE',
      distance: '5.7 km',
      rating: 4.7,
      reviewCount: 189,
      price: country === 'CA' ? '$95-140/hr' : country === 'US' ? '$85-130/hr' : 'AED 300-450/hr',
      specialties: ['Roof Replacement', 'Roof Repair', 'Gutter Installation', 'Skylight Installation'],
      verified: true,
      isAdvertiser: true,
      isPremium: true,
      image: '/api/placeholder/300/200',
      phone: country === 'CA' ? '+****************' : country === 'US' ? '+****************' : '+971 6 555 0789',
      email: '<EMAIL>',
      website: 'https://superiorroofing.com',
      description: 'Licensed roofing contractor specializing in residential and commercial roofing solutions.',
      coordinates: { lat: 43.8361, lng: -79.4985 },
      advertiser: {
        plan: 'premium' as const,
        status: 'active' as const,
        featuredUntil: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000),
        isPremium: true
      }
    },
    {
      id: 'reno-004',
      name: 'Jennifer Walsh',
      businessName: 'Premium Flooring Experts',
      serviceType: 'Flooring Contractor',
      location: country === 'CA' ? 'Oakville, ON' : country === 'US' ? 'Miami, FL' : 'Ajman, UAE',
      distance: '8.1 km',
      rating: 4.6,
      reviewCount: 98,
      price: country === 'CA' ? '$70-105/hr' : country === 'US' ? '$60-95/hr' : 'AED 220-320/hr',
      specialties: ['Hardwood Installation', 'Laminate Flooring', 'Tile Installation', 'Carpet Installation'],
      verified: true,
      isAdvertiser: false,
      isPremium: false,
      image: '/api/placeholder/300/200',
      phone: country === 'CA' ? '+****************' : country === 'US' ? '+****************' : '+971 6 555 0321',
      email: '<EMAIL>',
      description: 'Expert flooring installation and refinishing services for residential properties.',
      coordinates: { lat: 43.4675, lng: -79.6877 }
    },
    {
      id: 'reno-005',
      name: 'Robert Kim',
      businessName: 'Complete Home Solutions',
      serviceType: 'Full-Service Renovation',
      location: country === 'CA' ? 'Markham, ON' : country === 'US' ? 'Seattle, WA' : 'Ras Al Khaimah, UAE',
      distance: '12.3 km',
      rating: 4.8,
      reviewCount: 167,
      price: country === 'CA' ? '$90-125/hr' : country === 'US' ? '$80-115/hr' : 'AED 280-380/hr',
      specialties: ['Complete Renovations', 'Additions', 'Custom Carpentry', 'Project Management'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/300/200',
      phone: country === 'CA' ? '+****************' : country === 'US' ? '+****************' : '+971 7 555 0654',
      email: '<EMAIL>',
      website: 'https://completehome.com',
      description: 'Full-service renovation company handling projects from design to completion.',
      coordinates: { lat: 43.8561, lng: -79.3370 },
      advertiser: {
        plan: 'basic' as const,
        status: 'active' as const,
        isPremium: false
      }
    },
    {
      id: 'reno-006',
      name: 'Lisa Thompson',
      businessName: 'Modern Kitchen & Bath',
      serviceType: 'Kitchen & Bathroom Specialist',
      location: country === 'CA' ? 'Richmond Hill, ON' : country === 'US' ? 'Phoenix, AZ' : 'Fujairah, UAE',
      distance: '14.5 km',
      rating: 4.9,
      reviewCount: 142,
      price: country === 'CA' ? '$100-150/hr' : country === 'US' ? '$90-140/hr' : 'AED 320-480/hr',
      specialties: ['Kitchen Renovations', 'Bathroom Remodeling', 'Custom Cabinetry', 'Countertop Installation'],
      verified: true,
      isAdvertiser: true,
      isPremium: true,
      image: '/api/placeholder/300/200',
      phone: country === 'CA' ? '+****************' : country === 'US' ? '+****************' : '+971 9 555 0987',
      email: '<EMAIL>',
      website: 'https://modernkitchenbath.com',
      description: 'Specialized in high-end kitchen and bathroom renovations with custom design solutions.',
      coordinates: { lat: 43.8828, lng: -79.4403 },
      advertiser: {
        plan: 'premium' as const,
        status: 'active' as const,
        featuredUntil: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
        isPremium: true
      }
    }
  ]

  return baseContractors
}

export function HomeRenovationService({
  userType,
  isSignedIn,
  country
}: HomeRenovationServiceProps) {
  // Get providers data
  const mockContractors = getMockRenovationContractors(country)

  // Sort by distance and premium status
  const contractors = mockContractors.sort((a, b) => {
    if (a.isPremium && !b.isPremium) return -1
    if (!a.isPremium && b.isPremium) return 1
    if (a.isAdvertiser && !b.isAdvertiser) return -1
    if (!a.isAdvertiser && b.isAdvertiser) return 1
    return parseFloat(a.distance) - parseFloat(b.distance)
  })

  const serviceDescription = userType === 'seller'
    ? 'Professional home renovation and general contracting services to enhance your property value. Our verified contractors specialize in painting, roofing, flooring, kitchen and bathroom renovations, and complete home makeovers.'
    : 'Trusted home renovation contractors for your new property. From move-in ready improvements to major renovations, find qualified professionals for painting, flooring, roofing, and complete home transformations.'

  // Check environment variable for Google providers
  const showGoogleProviders = process.env.NEXT_PUBLIC_ENABLE_GOOGLE_PROVIDERS !== 'false'

  return (
    <ServiceLayout
      userType={userType}
      isSignedIn={isSignedIn}
      serviceTitle="Home Renovation & General Contracting"
      serviceDescription={serviceDescription}
      country={country}
      providers={contractors}
      showGoogleProviders={showGoogleProviders}
    />
  )
}
