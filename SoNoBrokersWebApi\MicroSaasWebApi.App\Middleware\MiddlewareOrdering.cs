using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using HealthChecks.UI.Client;
// using Hangfire;
// using Hangfire.Dashboard;
using MicroSaasWebApi.Middlewares;
using MicroSaasWebApi.Hubs;
using MicroSaasWebApi.Filters;
using MicroSaasWebApi.Services.SoNoBrokers;
using Scalar.AspNetCore;

namespace MicroSaasWebApi.Middleware
{
    /// <summary>
    /// Middleware ordering configuration for .NET 9
    /// Implements the Chain of Responsibility pattern for middleware execution
    /// </summary>
    public static class MiddlewareOrdering
    {
        /// <summary>
        /// Configure middleware pipeline in the correct order
        /// Order is critical for security, performance, and functionality
        /// </summary>
        public static WebApplication ConfigureMiddlewarePipeline(this WebApplication app)
        {
            var logger = app.Services.GetRequiredService<ILogger<Program>>();
            logger.LogInformation("Configuring middleware pipeline...");

            // 1. Database Migrations (Run before anything else)
            // Temporarily disabled due to database connection issues
            // await app.UseDatabaseMigrations();

            // 2. Exception Handling (Must be first to catch all exceptions)
            ConfigureExceptionHandling(app);

            // 3. Security Headers (Early security measures)
            app.UseSecurityHeaders();

            // 3. Rate Limiting (Protect against abuse early)
            app.UseRateLimiting();

            // 4. Request Logging (Log all requests)
            app.UseRequestLogging();

            // 5. HTTPS Redirection (Force HTTPS)
            app.UseHttpsRedirection();

            // 6. Static Files (Serve static content)
            app.UseStaticFiles();

            // 7. Routing (Enable routing)
            app.UseRouting();

            // 8. CORS (Cross-origin requests)
            app.UseCorsPolicy();

            // 9. Authentication (Identify users)
            app.UseAuthentication();

            // 10. Authorization (Check permissions)
            app.UseAuthorization();

            // 11. Custom Business Logic Middleware
            app.UseCustomBusinessLogic();

            // 12. Response Compression (Compress responses)
            app.UseResponseCompression();

            // 13. Health Checks
            app.UseHealthChecks();

            // 14. Development Tools (Swagger, etc.)
            app.UseDevelopmentTools();

            // 15. Endpoints (Map controllers, hubs, etc.)
            app.MapEndpoints();

            logger.LogInformation("Middleware pipeline configured successfully");
            return app;
        }

        private static void ConfigureExceptionHandling(WebApplication app)
        {
            if (app.Environment.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler("/Error");
                app.UseHsts();
            }
        }

        private static WebApplication UseSecurityHeaders(this WebApplication app)
        {
            app.Use(async (context, next) =>
            {
                // Security headers
                context.Response.Headers.Append("X-Content-Type-Options", "nosniff");
                context.Response.Headers.Append("X-Frame-Options", "DENY");
                context.Response.Headers.Append("X-XSS-Protection", "1; mode=block");
                context.Response.Headers.Append("Referrer-Policy", "strict-origin-when-cross-origin");

                await next();
            });
            return app;
        }

        private static WebApplication UseRateLimiting(this WebApplication app)
        {
            // Use AspNetCoreRateLimit - commented out until package is added
            // app.UseIpRateLimiting();
            return app;
        }

        private static WebApplication UseRequestLogging(this WebApplication app)
        {
            app.Use(async (context, next) =>
            {
                var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();
                var startTime = DateTime.UtcNow;

                logger.LogInformation("Request: {Method} {Path} from {RemoteIp}",
                    context.Request.Method,
                    context.Request.Path,
                    context.Connection.RemoteIpAddress);

                await next();

                var duration = DateTime.UtcNow - startTime;
                logger.LogInformation("Response: {StatusCode} in {Duration}ms",
                    context.Response.StatusCode,
                    duration.TotalMilliseconds);
            });
            return app;
        }

        private static WebApplication UseCorsPolicy(this WebApplication app)
        {
            if (app.Environment.IsDevelopment())
            {
                app.UseCors("AllowAll");
            }
            else
            {
                app.UseCors("MicroSaasPolicy");
            }
            return app;
        }

        private static WebApplication UseCustomBusinessLogic(this WebApplication app)
        {
            // Custom middleware in order
            // app.UseMiddleware<TenantResolutionMiddleware>(); // Commented out - tenant functionality removed
            // app.UseMiddleware<CustomRedirectMiddleware>(); // Temporarily disabled - needs refactoring
            // app.UseMiddleware<AzureB2CTokenAuthenticationMiddleware>(); // Commented out - replaced with ClerkAuthMiddleware

            // Use Clerk Authentication Middleware instead
            app.UseClerkAuthentication();
            return app;
        }

        private static WebApplication UseHealthChecks(this WebApplication app)
        {
            app.MapHealthChecks("/health");
            app.MapHealthChecks("/health/ready", new HealthCheckOptions
            {
                Predicate = check => check.Tags.Contains("ready"),
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
            });
            app.MapHealthChecks("/health/live", new HealthCheckOptions
            {
                Predicate = _ => false,
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
            });
            return app;
        }

        private static WebApplication UseDevelopmentTools(this WebApplication app)
        {
            if (app.Environment.IsDevelopment() || app.Environment.IsStaging())
            {
                // .NET 9 native OpenAPI + Scalar
                app.MapOpenApi();
                app.MapScalarApiReference(options =>
                {
                    options
                        .WithTitle("SoNoBrokers API Documentation")
                        .WithTheme(ScalarTheme.Kepler)
                        .WithDarkModeToggle(true)
                        .WithFavicon("/favicon.ico")
                        .WithSidebar(true)
                        .WithModels(true)
                        .WithDefaultHttpClient(ScalarTarget.CSharp, ScalarClient.HttpClient);
                });

                // Hangfire dashboard - temporarily commented out for build fix
                // app.UseHangfireDashboard("/hangfire", new DashboardOptions
                // {
                //     Authorization = new IDashboardAuthorizationFilter[] { new HangfireAuthorizationFilter() }
                // });
            }
            return app;
        }

        private static WebApplication MapEndpoints(this WebApplication app)
        {
            // Map controllers
            app.MapControllers();

            // Map SignalR hubs
            app.MapHub<MicroSaasWebApi.Hubs.MicroSaasHub>("/microsaashub");
            app.MapHub<MicroSaasWebApi.Hubs.MessagingHub>("/messaginghub");

            // Map health checks UI
            if (app.Environment.IsDevelopment())
            {
                app.MapHealthChecksUI();
            }

            return app;
        }
    }

    /// <summary>
    /// Middleware execution order enum for reference
    /// </summary>
    public enum MiddlewareOrder
    {
        ExceptionHandling = 1,
        SecurityHeaders = 2,
        RateLimiting = 3,
        RequestLogging = 4,
        HttpsRedirection = 5,
        StaticFiles = 6,
        Routing = 7,
        Cors = 8,
        Authentication = 9,
        Authorization = 10,
        TenantResolution = 11,
        CustomRedirect = 12,
        TokenAuthentication = 13,
        ResponseCompression = 14,
        HealthChecks = 15,
        DevelopmentTools = 16,
        Endpoints = 17
    }
}
