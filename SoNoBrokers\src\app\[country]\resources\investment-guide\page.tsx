import { redirect } from 'next/navigation'
import { Metadata } from 'next'

interface PageProps {
  params: Promise<{ country: string }>
  searchParams: Promise<{ userType?: string }>
}

export default async function InvestmentGuidePage({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/resources/investment-guide')
  }

  const userType = resolvedSearchParams.userType || 'buyer'

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8">
            Real Estate Investment Guide
          </h1>

          <div className="bg-card rounded-lg p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-4">Build Your Investment Portfolio</h2>
            <p className="text-muted-foreground mb-6">
              Learn the fundamentals of real estate investing in {country.toUpperCase()}. From rental properties
              to commercial real estate, discover strategies to build wealth through property investment.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <div className="bg-card rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Investment Strategies</h3>
              <div className="space-y-4">
                <div className="border-l-4 border-primary pl-4">
                  <h4 className="font-semibold">Buy and Hold</h4>
                  <p className="text-sm text-muted-foreground">
                    Purchase properties for long-term rental income and appreciation.
                  </p>
                </div>

                <div className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-semibold">Fix and Flip</h4>
                  <p className="text-sm text-muted-foreground">
                    Renovate properties to sell for a profit in the short term.
                  </p>
                </div>

                <div className="border-l-4 border-green-500 pl-4">
                  <h4 className="font-semibold">REITs</h4>
                  <p className="text-sm text-muted-foreground">
                    Invest in real estate investment trusts for passive income.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Key Metrics</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                  <span className="font-semibold">Cap Rate</span>
                  <span className="text-sm">4-8%</span>
                </div>

                <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                  <span className="font-semibold">Cash-on-Cash Return</span>
                  <span className="text-sm">8-12%</span>
                </div>

                <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                  <span className="font-semibold">1% Rule</span>
                  <span className="text-sm">Monthly rent ≥ 1% of price</span>
                </div>

                <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                  <span className="font-semibold">Debt Service Coverage</span>
                  <span className="text-sm">≥ 1.25x</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6 mb-8">
            <h3 className="text-xl font-semibold mb-4">Investment Property Types</h3>
            <div className="grid md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Residential</h4>
                <ul className="space-y-2 text-sm">
                  <li>• Single-family homes</li>
                  <li>• Multi-family properties</li>
                  <li>• Condominiums</li>
                  <li>• Townhouses</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Commercial</h4>
                <ul className="space-y-2 text-sm">
                  <li>• Office buildings</li>
                  <li>• Retail spaces</li>
                  <li>• Industrial properties</li>
                  <li>• Mixed-use developments</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Alternative</h4>
                <ul className="space-y-2 text-sm">
                  <li>• Vacation rentals</li>
                  <li>• Student housing</li>
                  <li>• Senior living</li>
                  <li>• Storage facilities</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-6 mb-8">
            <div className="bg-card rounded-lg p-6 text-center">
              <div className="text-2xl font-bold text-primary mb-2">20%</div>
              <div className="text-sm text-muted-foreground">Minimum Down Payment</div>
              <div className="text-xs text-muted-foreground mt-1">Investment Properties</div>
            </div>

            <div className="bg-card rounded-lg p-6 text-center">
              <div className="text-2xl font-bold text-primary mb-2">6.5%</div>
              <div className="text-sm text-muted-foreground">Average Mortgage Rate</div>
              <div className="text-xs text-muted-foreground mt-1">Investment Properties</div>
            </div>

            <div className="bg-card rounded-lg p-6 text-center">
              <div className="text-2xl font-bold text-primary mb-2">$2,500</div>
              <div className="text-sm text-muted-foreground">Average Monthly Rent</div>
              <div className="text-xs text-muted-foreground mt-1">2BR Condo</div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4">Getting Started</h3>
            <div className="grid md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-2">1</div>
                <h4 className="font-semibold text-sm">Set Goals</h4>
                <p className="text-xs text-muted-foreground">Define investment objectives</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-2">2</div>
                <h4 className="font-semibold text-sm">Secure Financing</h4>
                <p className="text-xs text-muted-foreground">Get pre-approved</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-2">3</div>
                <h4 className="font-semibold text-sm">Market Research</h4>
                <p className="text-xs text-muted-foreground">Analyze local markets</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-2">4</div>
                <h4 className="font-semibold text-sm">Make Offers</h4>
                <p className="text-xs text-muted-foreground">Start investing</p>
              </div>
            </div>

            <div className="mt-6 text-center">
              <button className="bg-primary text-primary-foreground px-8 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors">
                Start Your Investment Journey
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params, searchParams }: PageProps): Promise<Metadata> {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'buyer'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Real Estate Investment Guide for ${country} | SoNoBrokers`,
    description: `Learn real estate investment strategies, property types, and key metrics for building wealth in ${country}. Start your investment journey today.`,
    keywords: `real estate investment, property investment, rental properties, ${country}, investment guide`,
  }
}
