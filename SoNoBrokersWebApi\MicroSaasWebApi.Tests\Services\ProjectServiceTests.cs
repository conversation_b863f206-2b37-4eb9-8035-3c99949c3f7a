using FluentAssertions;
using Microsoft.Extensions.Logging;
using MicroSaasWebApi.Data;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers;
using MicroSaasWebApi.Tests.Common;
using Moq;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Services
{
    public class ProjectServiceTests : TestBase
    {
        private readonly Mock<DapperDbContext> _mockDbContext;
        private readonly Mock<ILogger<ProjectService>> _mockLogger;
        private readonly ProjectService _projectService;

        public ProjectServiceTests(ITestOutputHelper output) : base(output)
        {
            _mockDbContext = new Mock<DapperDbContext>();
            _mockLogger = new Mock<ILogger<ProjectService>>();
            _projectService = new ProjectService(_mockDbContext.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task GetProjectsAsync_WithValidRequest_ReturnsProjects()
        {
            // Arrange
            var userClerkId = TestHelpers.GenerateTestId("clerk");
            var request = new ProjectSearchRequest
            {
                Page = 1,
                Limit = 10,
                Type = "automation"
            };

            var projects = TestDataBuilders.Projects.ValidProject.Generate(5);
            projects.ForEach(p => p.UserClerkId = userClerkId);
            var totalCount = 15;

            _mockDbContext.Setup(x => x.QueryAsync<Project>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(projects);

            _mockDbContext.Setup(x => x.QuerySingleAsync<int>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(totalCount);

            // Act
            var result = await _projectService.GetProjectsAsync(request, userClerkId);

            // Assert
            result.Should().NotBeNull();
            result.Projects.Should().HaveCount(5);
            result.Total.Should().Be(totalCount);
            result.Page.Should().Be(1);
            result.TotalPages.Should().Be(2);
            result.HasMore.Should().BeTrue();
        }

        [Fact]
        public async Task GetProjectByIdAsync_WithValidId_ReturnsProject()
        {
            // Arrange
            var projectId = TestHelpers.GenerateTestId("proj");
            var userClerkId = TestHelpers.GenerateTestId("clerk");
            var project = TestDataBuilders.Projects.ValidProject.Generate();
            project.Id = projectId;
            project.UserClerkId = userClerkId;

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<Project>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(project);

            // Act
            var result = await _projectService.GetProjectByIdAsync(projectId, userClerkId);

            // Assert
            result.Should().NotBeNull();
            result!.Id.Should().Be(projectId);
            result.UserClerkId.Should().Be(userClerkId);
        }

        [Fact]
        public async Task GetProjectByIdAsync_WithInvalidId_ReturnsNull()
        {
            // Arrange
            var projectId = "non-existent-id";
            var userClerkId = TestHelpers.GenerateTestId("clerk");

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<Project>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync((Project?)null);

            // Act
            var result = await _projectService.GetProjectByIdAsync(projectId, userClerkId);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task CreateProjectAsync_WithValidRequest_ReturnsCreatedProject()
        {
            // Arrange
            var userClerkId = TestHelpers.GenerateTestId("clerk");
            var request = TestDataBuilders.Projects.CreateProjectRequest.Generate();
            var createdProject = TestDataBuilders.Projects.ValidProject.Generate();
            createdProject.UserClerkId = userClerkId;

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<Project>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(createdProject);

            // Act
            var result = await _projectService.CreateProjectAsync(request, userClerkId);

            // Assert
            result.Should().NotBeNull();
            result.UserClerkId.Should().Be(userClerkId);
            result.Type.Should().Be(createdProject.Type);
            result.Status.Should().Be(createdProject.Status);
        }

        [Fact]
        public async Task UpdateProjectAsync_WithValidRequest_ReturnsUpdatedProject()
        {
            // Arrange
            var projectId = TestHelpers.GenerateTestId("proj");
            var userClerkId = TestHelpers.GenerateTestId("clerk");
            var request = new UpdateProjectRequest
            {
                Id = projectId,
                Type = "integration",
                Status = "inactive"
            };

            var updatedProject = TestDataBuilders.Projects.ValidProject.Generate();
            updatedProject.Id = projectId;
            updatedProject.UserClerkId = userClerkId;
            updatedProject.Type = request.Type!;
            updatedProject.Status = request.Status!;

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<Project>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(updatedProject);

            // Act
            var result = await _projectService.UpdateProjectAsync(request, userClerkId);

            // Assert
            result.Should().NotBeNull();
            result!.Id.Should().Be(projectId);
            result.Type.Should().Be(request.Type);
            result.Status.Should().Be(request.Status);
        }

        [Fact]
        public async Task UpdateProjectAsync_WithNoChanges_ReturnsExistingProject()
        {
            // Arrange
            var projectId = TestHelpers.GenerateTestId("proj");
            var userClerkId = TestHelpers.GenerateTestId("clerk");
            var request = new UpdateProjectRequest
            {
                Id = projectId
                // No fields to update
            };

            var existingProject = TestDataBuilders.Projects.ValidProject.Generate();
            existingProject.Id = projectId;
            existingProject.UserClerkId = userClerkId;

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<Project>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(existingProject);

            // Act
            var result = await _projectService.UpdateProjectAsync(request, userClerkId);

            // Assert
            result.Should().NotBeNull();
            result!.Id.Should().Be(projectId);

            // Verify no update was executed
            _mockDbContext.Verify(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()), Times.Never);
        }

        [Fact]
        public async Task UpdateProjectAsync_WithNonExistentProject_ReturnsNull()
        {
            // Arrange
            var projectId = "non-existent-id";
            var userClerkId = TestHelpers.GenerateTestId("clerk");
            var request = new UpdateProjectRequest
            {
                Id = projectId,
                Type = "integration"
            };

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(0); // No rows affected

            // Act
            var result = await _projectService.UpdateProjectAsync(request, userClerkId);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task DeleteProjectAsync_WithValidId_ReturnsTrue()
        {
            // Arrange
            var projectId = TestHelpers.GenerateTestId("proj");
            var userClerkId = TestHelpers.GenerateTestId("clerk");

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            // Act
            var result = await _projectService.DeleteProjectAsync(projectId, userClerkId);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task DeleteProjectAsync_WithInvalidId_ReturnsFalse()
        {
            // Arrange
            var projectId = "non-existent-id";
            var userClerkId = TestHelpers.GenerateTestId("clerk");

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(0);

            // Act
            var result = await _projectService.DeleteProjectAsync(projectId, userClerkId);

            // Assert
            result.Should().BeFalse();
        }

        [Theory]
        [InlineData("automation")]
        [InlineData("integration")]
        [InlineData("workflow")]
        public async Task GetProjectsAsync_WithDifferentTypes_FiltersCorrectly(string type)
        {
            // Arrange
            var userClerkId = TestHelpers.GenerateTestId("clerk");
            var request = new ProjectSearchRequest
            {
                Page = 1,
                Limit = 10,
                Type = type
            };

            var projects = TestDataBuilders.Projects.ValidProject.Generate(3);
            projects.ForEach(p => { p.UserClerkId = userClerkId; p.Type = type; });

            _mockDbContext.Setup(x => x.QueryAsync<Project>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(projects);

            _mockDbContext.Setup(x => x.QuerySingleAsync<int>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(3);

            // Act
            var result = await _projectService.GetProjectsAsync(request, userClerkId);

            // Assert
            result.Should().NotBeNull();
            result.Projects.Should().HaveCount(3);
            result.Projects.Should().OnlyContain(p => p.Type == type);
        }

        [Theory]
        [InlineData("active")]
        [InlineData("inactive")]
        [InlineData("pending")]
        public async Task GetProjectsAsync_WithDifferentStatuses_FiltersCorrectly(string status)
        {
            // Arrange
            var userClerkId = TestHelpers.GenerateTestId("clerk");
            var request = new ProjectSearchRequest
            {
                Page = 1,
                Limit = 10,
                Status = status
            };

            var projects = TestDataBuilders.Projects.ValidProject.Generate(3);
            projects.ForEach(p => { p.UserClerkId = userClerkId; p.Status = status; });

            _mockDbContext.Setup(x => x.QueryAsync<Project>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(projects);

            _mockDbContext.Setup(x => x.QuerySingleAsync<int>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(3);

            // Act
            var result = await _projectService.GetProjectsAsync(request, userClerkId);

            // Assert
            result.Should().NotBeNull();
            result.Projects.Should().HaveCount(3);
            result.Projects.Should().OnlyContain(p => p.Status == status);
        }

        [Fact]
        public async Task GetProjectsAsync_WithPagination_ReturnsCorrectPage()
        {
            // Arrange
            var userClerkId = TestHelpers.GenerateTestId("clerk");
            var request = new ProjectSearchRequest
            {
                Page = 2,
                Limit = 5
            };

            var projects = TestDataBuilders.Projects.ValidProject.Generate(5);
            var totalCount = 12;

            _mockDbContext.Setup(x => x.QueryAsync<Project>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(projects);

            _mockDbContext.Setup(x => x.QuerySingleAsync<int>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(totalCount);

            // Act
            var result = await _projectService.GetProjectsAsync(request, userClerkId);

            // Assert
            result.Should().NotBeNull();
            result.Projects.Should().HaveCount(5);
            result.Total.Should().Be(totalCount);
            result.Page.Should().Be(2);
            result.TotalPages.Should().Be(3);
            result.HasMore.Should().BeTrue();
        }
    }
}
