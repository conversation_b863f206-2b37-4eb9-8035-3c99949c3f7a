import { LanguageSwitchClient } from './LanguageSwitchClient'

interface LanguageSwitchProps {
  currentLanguage?: string
}

export function LanguageSwitch({ currentLanguage = 'en' }: LanguageSwitchProps) {
  const handleLanguageChange = (language: string) => {
    // This will be handled on the client side
    // For now, we'll store in localStorage and potentially reload
    if (typeof window !== 'undefined') {
      localStorage.setItem('sonobrokers-language', language)
      // In a full i18n implementation, this would trigger a language change
      // For now, we'll just store the preference
      window.location.reload()
    }
  }

  return (
    <LanguageSwitchClient
      currentLanguage={currentLanguage}
      onLanguageChange={handleLanguageChange}
    />
  )
}
