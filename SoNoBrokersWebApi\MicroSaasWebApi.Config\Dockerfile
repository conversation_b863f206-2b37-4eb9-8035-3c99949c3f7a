# =============================================================================
# MicroSaasWebApi.Config - Dockerfile
# =============================================================================
# Configuration management container for environment-specific settings
# This container serves configuration files and scripts
# =============================================================================

FROM nginx:alpine AS config-server

# Install necessary tools
RUN apk add --no-cache curl jq

# Create configuration directories
RUN mkdir -p /usr/share/nginx/html/config
RUN mkdir -p /usr/share/nginx/html/scripts
RUN mkdir -p /usr/share/nginx/html/templates

# Copy configuration files
COPY AppSettings/ /usr/share/nginx/html/config/appsettings/
COPY Environment/ /usr/share/nginx/html/config/environment/
COPY Scripts/ /usr/share/nginx/html/scripts/
COPY EmailTemplates/ /usr/share/nginx/html/templates/email/
COPY Postman/ /usr/share/nginx/html/config/postman/

# Copy documentation
COPY *.md /usr/share/nginx/html/docs/

# Create nginx configuration for serving files
RUN cat > /etc/nginx/conf.d/default.conf << 'EOF'
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # Enable CORS for configuration access
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
    add_header Access-Control-Allow-Headers "Content-Type, Authorization";

    # Serve configuration files
    location /config/ {
        autoindex on;
        autoindex_format json;
    }

    # Serve scripts
    location /scripts/ {
        autoindex on;
        autoindex_format json;
    }

    # Serve templates
    location /templates/ {
        autoindex on;
        autoindex_format json;
    }

    # Health check
    location /health {
        access_log off;
        return 200 "Configuration server is healthy\n";
        add_header Content-Type text/plain;
    }

    # API endpoint for configuration
    location /api/config {
        return 200 '{"status":"healthy","service":"config-server","timestamp":"$time_iso8601"}';
        add_header Content-Type application/json;
    }
}
EOF

# Create index page
RUN cat > /usr/share/nginx/html/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>SoNoBrokers Configuration Server</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <h1>SoNoBrokers Configuration Server</h1>
        <div class="section">
            <h2>Available Resources</h2>
            <ul>
                <li><a href="/config/">Configuration Files</a></li>
                <li><a href="/scripts/">Scripts</a></li>
                <li><a href="/templates/">Email Templates</a></li>
                <li><a href="/docs/">Documentation</a></li>
            </ul>
        </div>
        <div class="section">
            <h2>Health Check</h2>
            <p><a href="/health">Health Status</a></p>
            <p><a href="/api/config">API Status</a></p>
        </div>
    </div>
</body>
</html>
EOF

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost/health || exit 1

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
