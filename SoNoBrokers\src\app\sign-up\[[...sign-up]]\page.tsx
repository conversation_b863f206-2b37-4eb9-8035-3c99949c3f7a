'use client'

import { SignUp } from '@clerk/nextjs'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { useAppContext } from '@/contexts/AppContext'
import { getDashboardUrl } from '@/lib/geo'

export default function Page() {
  const { isSignedIn, country } = useAppContext()
  const router = useRouter()

  useEffect(() => {
    // If user is already signed in, redirect to country-specific dashboard
    if (isSignedIn) {
      const dashboardUrl = getDashboardUrl(country);
      router.push(dashboardUrl);
    }
  }, [isSignedIn, country, router])

  // Don't render SignUp component if user is already signed in
  if (isSignedIn) {
    return <div>Redirecting to your dashboard...</div>
  }

  return <SignUp forceRedirectUrl="/dashboard" />
}