-- Migration: 0007_create_functions.sql
-- Description: Create stored procedures and functions for SoNoBrokers
-- Date: 2024-12-25
-- Author: Migration System
-- Dependencies: 0006_create_triggers.sql

-- =====================================================
-- CORE PROPERTY AND USER MANAGEMENT FUNCTIONS
-- =====================================================

-- Function to search properties with advanced filtering
CREATE OR REPLACE FUNCTION public.search_properties_advanced(
    p_location TEXT DEFAULT NULL,
    p_property_type TEXT DEFAULT NULL,
    p_min_price DECIMAL DEFAULT NULL,
    p_max_price DECIMAL DEFAULT NULL,
    p_bedrooms INTEGER DEFAULT NULL,
    p_bathrooms DECIMAL DEFAULT NULL,
    p_user_id UUID DEFAULT NULL,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    title TEXT,
    description TEXT,
    price DECIMAL,
    property_type "PropertyType",
    bedrooms INTEGER,
    bathrooms DECIMAL,
    square_footage INTEGER,
    address JSONB,
    status "PropertyStatus",
    seller_id UUID,
    seller_name TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    image_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.title,
        p.description,
        p.price,
        p."propertyType" as property_type,
        p.bedrooms,
        p.bathrooms,
        p."squareFootage" as square_footage,
        p.address,
        p.status,
        p."sellerId" as seller_id,
        u."fullName" as seller_name,
        p."createdAt" as created_at,
        p."updatedAt" as updated_at,
        COALESCE(img_count.count, 0)::INTEGER as image_count
    FROM public."Property" p
    LEFT JOIN public."User" u ON p."sellerId" = u.id
    LEFT JOIN (
        SELECT "propertyId", COUNT(*) as count
        FROM public."PropertyImage"
        GROUP BY "propertyId"
    ) img_count ON p.id = img_count."propertyId"
    WHERE
        (p_location IS NULL OR p.address->>'city' ILIKE '%' || p_location || '%'
         OR p.address->>'province' ILIKE '%' || p_location || '%')
        AND (p_property_type IS NULL OR p."propertyType"::TEXT = p_property_type)
        AND (p_min_price IS NULL OR p.price >= p_min_price)
        AND (p_max_price IS NULL OR p.price <= p_max_price)
        AND (p_bedrooms IS NULL OR p.bedrooms >= p_bedrooms)
        AND (p_bathrooms IS NULL OR p.bathrooms >= p_bathrooms)
        AND (p_user_id IS NULL OR p."sellerId" != p_user_id) -- Exclude user's own properties
        AND p.status = 'ACTIVE'
        AND p."isActive" = TRUE
    ORDER BY p."createdAt" DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Function to get property analytics
CREATE OR REPLACE FUNCTION public.get_property_analytics(
    p_property_id UUID
)
RETURNS TABLE (
    property_id UUID,
    view_count INTEGER,
    favorite_count INTEGER,
    inquiry_count INTEGER,
    contact_share_count INTEGER,
    visit_request_count INTEGER,
    last_viewed TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id as property_id,
        p."viewCount" as view_count,
        p."favoriteCount" as favorite_count,
        COALESCE(msg_count.count, 0)::INTEGER as inquiry_count,
        COALESCE(cs_count.count, 0)::INTEGER as contact_share_count,
        COALESCE(visit_count.count, 0)::INTEGER as visit_request_count,
        GREATEST(p."updatedAt", COALESCE(cs_count.last_contact, p."updatedAt")) as last_viewed
    FROM public."Property" p
    LEFT JOIN (
        SELECT c."propertyId", COUNT(*) as count
        FROM public."Conversation" c
        INNER JOIN public."Message" m ON c.id = m."conversationId"
        WHERE c."propertyId" = p_property_id
        GROUP BY c."propertyId"
    ) msg_count ON p.id = msg_count."propertyId"
    LEFT JOIN (
        SELECT "propertyId", COUNT(*) as count, MAX("createdAt") as last_contact
        FROM public."ContactShare"
        WHERE "propertyId" = p_property_id
        GROUP BY "propertyId"
    ) cs_count ON p.id = cs_count."propertyId"
    LEFT JOIN (
        SELECT "propertyId", COUNT(*) as count
        FROM public."PropertyVisitSchedule"
        WHERE "propertyId" = p_property_id
        GROUP BY "propertyId"
    ) visit_count ON p.id = visit_count."propertyId"
    WHERE p.id = p_property_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get user dashboard statistics
CREATE OR REPLACE FUNCTION public.get_user_dashboard_stats(
    p_user_id UUID
)
RETURNS TABLE (
    total_properties INTEGER,
    active_properties INTEGER,
    sold_properties INTEGER,
    total_inquiries INTEGER,
    pending_visits INTEGER,
    total_views INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COALESCE(prop_stats.total, 0)::INTEGER as total_properties,
        COALESCE(prop_stats.active, 0)::INTEGER as active_properties,
        COALESCE(prop_stats.sold, 0)::INTEGER as sold_properties,
        COALESCE(inquiry_stats.total, 0)::INTEGER as total_inquiries,
        COALESCE(visit_stats.pending, 0)::INTEGER as pending_visits,
        COALESCE(prop_stats.total_views, 0)::INTEGER as total_views
    FROM (
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active,
            COUNT(CASE WHEN status = 'SOLD' THEN 1 END) as sold,
            SUM("viewCount") as total_views
        FROM public."Property"
        WHERE "sellerId" = p_user_id
    ) prop_stats
    CROSS JOIN (
        SELECT COUNT(*) as total
        FROM public."ContactShare"
        WHERE "sellerId" = p_user_id
    ) inquiry_stats
    CROSS JOIN (
        SELECT COUNT(*) as pending
        FROM public."PropertyVisitSchedule"
        WHERE "sellerId" = p_user_id AND status = 'Pending'
    ) visit_stats;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CONTACT SHARING FUNCTIONS
-- =====================================================

-- Function to create contact share request
CREATE OR REPLACE FUNCTION public.create_contact_share_request(
    p_property_id UUID,
    p_buyer_id UUID,
    p_seller_id UUID,
    p_buyer_name TEXT,
    p_buyer_email TEXT,
    p_buyer_phone TEXT DEFAULT NULL,
    p_message TEXT DEFAULT NULL,
    p_share_type "ContactShareType" DEFAULT 'ContactRequest',
    p_offer_amount DECIMAL DEFAULT NULL
)
RETURNS TEXT AS $$
DECLARE
    v_contact_share_id TEXT;
BEGIN
    -- Generate unique ID
    v_contact_share_id := gen_random_uuid()::TEXT;
    
    -- Insert contact share request
    INSERT INTO public."ContactShare" (
        id, "propertyId", "buyerId", "sellerId", "buyerName", "buyerEmail", 
        "buyerPhone", message, "shareType", "offerAmount", status, "createdAt"
    ) VALUES (
        v_contact_share_id, p_property_id, p_buyer_id, p_seller_id, p_buyer_name, 
        p_buyer_email, p_buyer_phone, p_message, p_share_type, p_offer_amount, 
        'Pending', NOW()
    );
    
    RETURN v_contact_share_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- PROPERTY SCHEDULING FUNCTIONS
-- =====================================================

-- Function to check seller availability
CREATE OR REPLACE FUNCTION public.check_seller_availability(
    p_property_id UUID,
    p_seller_id UUID,
    p_requested_date DATE,
    p_requested_time TIME
)
RETURNS BOOLEAN AS $$
DECLARE
    v_day_of_week INTEGER;
    v_is_available BOOLEAN := FALSE;
BEGIN
    -- Get day of week (0=Sunday, 6=Saturday)
    v_day_of_week := EXTRACT(DOW FROM p_requested_date);
    
    -- Check if seller has availability for this day and time
    SELECT EXISTS(
        SELECT 1 FROM public."SellerAvailability"
        WHERE "propertyId" = p_property_id
        AND "sellerId" = p_seller_id
        AND "dayOfWeek" = v_day_of_week
        AND "startTime" <= p_requested_time
        AND "endTime" >= p_requested_time
        AND "isAvailable" = TRUE
    ) INTO v_is_available;
    
    RETURN v_is_available;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ Migration 0007_create_functions.sql completed successfully';
    RAISE NOTICE 'Created functions:';
    RAISE NOTICE '- search_properties_advanced() for property search';
    RAISE NOTICE '- get_property_analytics() for property statistics';
    RAISE NOTICE '- get_user_dashboard_stats() for user dashboard';
    RAISE NOTICE '- create_contact_share_request() for contact sharing';
    RAISE NOTICE '- check_seller_availability() for scheduling';
    RAISE NOTICE '';
END $$;
