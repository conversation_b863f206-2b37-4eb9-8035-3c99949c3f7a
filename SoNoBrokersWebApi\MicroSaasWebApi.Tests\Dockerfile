# =============================================================================
# MicroSaasWebApi.Tests - Dockerfile
# =============================================================================
# Test runner container for SoNoBrokers Web API tests
# Includes unit tests, integration tests, and E2E tests
# =============================================================================

# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Install additional tools for testing
RUN apt-get update && apt-get install -y \
    curl \
    jq \
    && rm -rf /var/lib/apt/lists/*

# Copy project files
COPY ["MicroSaasWebApi.Tests.csproj", "./"]
COPY ["../MicroSaasWebApi.App/MicroSaasWebApi.App.csproj", "../MicroSaasWebApi.App/"]
COPY ["../MicroSaasWebApi.Config/MicroSaasWebApi.Config.csproj", "../MicroSaasWebApi.Config/"]

# Restore dependencies
RUN dotnet restore "MicroSaasWebApi.Tests.csproj"

# Copy source code
COPY . .
COPY ../MicroSaasWebApi.App/ ../MicroSaasWebApi.App/
COPY ../MicroSaasWebApi.Config/ ../MicroSaasWebApi.Config/

# Build tests
RUN dotnet build "MicroSaasWebApi.Tests.csproj" -c Release -o /app/build

# Test stage
FROM build AS test
WORKDIR /src

# Create test results directory
RUN mkdir -p /app/test-results

# Set environment variables for testing
ENV ASPNETCORE_ENVIRONMENT=Test
ENV DOTNET_ENVIRONMENT=Test

# Install test report generators
RUN dotnet tool install -g dotnet-reportgenerator-globaltool
RUN dotnet tool install -g coverlet.console
ENV PATH="$PATH:/root/.dotnet/tools"

# Create test script
RUN cat > /app/run-tests.sh << 'EOF'
#!/bin/bash
set -e

echo "Starting SoNoBrokers API Tests..."
echo "=================================="

# Run unit tests
echo "Running Unit Tests..."
dotnet test /src/MicroSaasWebApi.Tests.csproj \
    --configuration Release \
    --logger "trx;LogFileName=unit-tests.trx" \
    --logger "console;verbosity=detailed" \
    --results-directory /app/test-results \
    --collect:"XPlat Code Coverage" \
    --filter "Category=Unit" \
    || echo "Unit tests completed with issues"

# Run integration tests
echo "Running Integration Tests..."
dotnet test /src/MicroSaasWebApi.Tests.csproj \
    --configuration Release \
    --logger "trx;LogFileName=integration-tests.trx" \
    --logger "console;verbosity=detailed" \
    --results-directory /app/test-results \
    --collect:"XPlat Code Coverage" \
    --filter "Category=Integration" \
    || echo "Integration tests completed with issues"

# Run E2E tests
echo "Running E2E Tests..."
dotnet test /src/MicroSaasWebApi.Tests.csproj \
    --configuration Release \
    --logger "trx;LogFileName=e2e-tests.trx" \
    --logger "console;verbosity=detailed" \
    --results-directory /app/test-results \
    --collect:"XPlat Code Coverage" \
    --filter "Category=E2E" \
    || echo "E2E tests completed with issues"

# Generate coverage report
echo "Generating Coverage Report..."
reportgenerator \
    -reports:"/app/test-results/**/coverage.cobertura.xml" \
    -targetdir:"/app/test-results/coverage" \
    -reporttypes:"Html;Cobertura;JsonSummary" \
    || echo "Coverage report generation completed"

echo "=================================="
echo "Test execution completed!"
echo "Results available in /app/test-results"
EOF

RUN chmod +x /app/run-tests.sh

# Health check
HEALTHCHECK --interval=60s --timeout=30s --start-period=120s --retries=2 \
  CMD test -f /app/test-results/unit-tests.trx || exit 1

# Default command
CMD ["/app/run-tests.sh"]
