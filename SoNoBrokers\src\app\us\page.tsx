import { USLandingClient } from './USLandingClient'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'SoNoBrokers USA | For Sale By Owner Real Estate Platform',
  description: 'Sell your property directly to buyers in the United States with SoNoBrokers. Save on commissions, access professional services, and connect with verified buyers across US markets.',
  keywords: 'for sale by owner USA, FSBO USA, real estate USA, property sale USA, no commission real estate',
  openGraph: {
    title: 'SoNoBrokers USA | For Sale By Owner Real Estate',
    description: 'The leading FSBO platform in the United States. Sell directly, save commissions, access professional services.',
    url: 'https://www.sonobrokers.com/us',
    siteName: 'SoNoBrokers',
    locale: 'en_US',
    type: 'website',
  },
}

export default function USALandingPage() {
  return (
    <div>
      <USLandingClient />
    </div>
  )
}
