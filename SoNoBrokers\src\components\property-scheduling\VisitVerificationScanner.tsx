'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { 
  QrCode, 
  Camera, 
  CheckCircle, 
  XCircle, 
  MapPin,
  Clock,
  User,
  Smartphone,
  AlertTriangle
} from 'lucide-react'

interface VisitVerificationScannerProps {
  visitId: string
  propertyTitle: string
  propertyAddress: string
  visitDateTime: string
  onVerificationComplete?: (success: boolean) => void
}

interface VerificationResult {
  success: boolean
  message: string
  verificationId?: string
  timestamp?: string
}

export function VisitVerificationScanner({
  visitId,
  propertyTitle,
  propertyAddress,
  visitDateTime,
  onVerificationComplete
}: VisitVerificationScannerProps) {
  const [isScanning, setIsScanning] = useState(false)
  const [manualEntry, setManualEntry] = useState(false)
  const [qrCodeData, setQrCodeData] = useState('')
  const [notes, setNotes] = useState('')
  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null)
  const [isVerifying, setIsVerifying] = useState(false)
  const [showResult, setShowResult] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  const startCamera = async () => {
    try {
      setIsScanning(true)
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { 
          facingMode: 'environment' // Use back camera on mobile
        } 
      })
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        videoRef.current.play()
      }
    } catch (error) {
      console.error('Failed to start camera:', error)
      setManualEntry(true)
      setIsScanning(false)
    }
  }

  const stopCamera = () => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream
      stream.getTracks().forEach(track => track.stop())
      videoRef.current.srcObject = null
    }
    setIsScanning(false)
  }

  const captureQrCode = () => {
    if (!videoRef.current || !canvasRef.current) return

    const canvas = canvasRef.current
    const video = videoRef.current
    const context = canvas.getContext('2d')

    if (!context) return

    canvas.width = video.videoWidth
    canvas.height = video.videoHeight
    context.drawImage(video, 0, 0)

    // In a real implementation, you would use a QR code scanning library here
    // For now, we'll simulate QR code detection
    const imageData = canvas.toDataURL()
    
    // Mock QR code detection
    setTimeout(() => {
      const mockQrData = 'encrypted-property-qr-data-12345'
      setQrCodeData(mockQrData)
      stopCamera()
      verifyVisit(mockQrData)
    }, 1000)
  }

  const verifyVisit = async (qrData?: string) => {
    try {
      setIsVerifying(true)
      
      const verificationData = {
        qrCodeData: qrData || qrCodeData,
        method: qrData ? 'QrCode' : 'Manual',
        deviceInfo: navigator.userAgent,
        location: await getCurrentLocation(),
        notes: notes || undefined
      }

      // TODO: Replace with actual API call
      // const response = await verifyVisit(visitId, verificationData)
      
      // Mock verification
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const mockResult: VerificationResult = {
        success: true,
        message: 'Visit verified successfully! The seller has been notified.',
        verificationId: 'ver-12345',
        timestamp: new Date().toISOString()
      }

      setVerificationResult(mockResult)
      setShowResult(true)
      onVerificationComplete?.(mockResult.success)
    } catch (error) {
      console.error('Failed to verify visit:', error)
      setVerificationResult({
        success: false,
        message: 'Failed to verify visit. Please try again or contact support.'
      })
      setShowResult(true)
      onVerificationComplete?.(false)
    } finally {
      setIsVerifying(false)
    }
  }

  const getCurrentLocation = async (): Promise<string | undefined> => {
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        resolve(undefined)
        return
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords
          resolve(`${latitude},${longitude}`)
        },
        () => resolve(undefined),
        { timeout: 5000 }
      )
    })
  }

  useEffect(() => {
    return () => {
      stopCamera()
    }
  }, [])

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <QrCode className="h-5 w-5" />
            Verify Your Visit
          </CardTitle>
          <CardDescription>
            Scan the property QR code to verify your visit
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Visit Information */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-gray-500" />
              <div>
                <p className="font-medium">{propertyTitle}</p>
                <p className="text-sm text-gray-600">{propertyAddress}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <p className="text-sm">Scheduled: {visitDateTime}</p>
            </div>
          </div>

          {/* Scanning Interface */}
          {!manualEntry ? (
            <div className="space-y-4">
              {!isScanning ? (
                <div className="text-center py-8">
                  <Camera className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Ready to Scan</h3>
                  <p className="text-gray-600 mb-4">
                    Point your camera at the property QR code
                  </p>
                  <div className="space-y-2">
                    <Button onClick={startCamera} className="w-full">
                      <Camera className="h-4 w-4 mr-2" />
                      Start Camera
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setManualEntry(true)}
                      className="w-full"
                    >
                      Enter Code Manually
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="relative">
                    <video
                      ref={videoRef}
                      className="w-full h-64 object-cover rounded-lg border"
                      playsInline
                    />
                    <canvas ref={canvasRef} className="hidden" />
                    
                    {/* Scanning overlay */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-48 h-48 border-2 border-white border-dashed rounded-lg flex items-center justify-center">
                        <QrCode className="h-8 w-8 text-white" />
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button onClick={captureQrCode} className="flex-1">
                      <QrCode className="h-4 w-4 mr-2" />
                      Scan QR Code
                    </Button>
                    <Button variant="outline" onClick={stopCamera}>
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            /* Manual Entry */
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="qrCodeData">QR Code Data</Label>
                <Input
                  id="qrCodeData"
                  placeholder="Enter the QR code data manually..."
                  value={qrCodeData}
                  onChange={(e) => setQrCodeData(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  placeholder="Any additional notes about your visit..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                />
              </div>
              
              <div className="flex gap-2">
                <Button 
                  onClick={() => verifyVisit()} 
                  disabled={!qrCodeData || isVerifying}
                  className="flex-1"
                >
                  {isVerifying ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Verifying...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Verify Visit
                    </>
                  )}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setManualEntry(false)}
                >
                  Use Camera
                </Button>
              </div>
            </div>
          )}

          {/* Security Notice */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Security Notice:</strong> This verification process logs your device information and location for security purposes. Only scan QR codes provided by the property seller.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Verification Result Dialog */}
      <Dialog open={showResult} onOpenChange={setShowResult}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {verificationResult?.success ? (
                <>
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  Visit Verified
                </>
              ) : (
                <>
                  <XCircle className="h-5 w-5 text-red-500" />
                  Verification Failed
                </>
              )}
            </DialogTitle>
            <DialogDescription>
              {verificationResult?.message}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {verificationResult?.success && (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Badge variant="default">Verified</Badge>
                  <span className="text-sm text-gray-600">
                    {verificationResult.timestamp && 
                      new Date(verificationResult.timestamp).toLocaleString()
                    }
                  </span>
                </div>
                
                {verificationResult.verificationId && (
                  <div className="text-sm">
                    <p className="text-gray-600">Verification ID:</p>
                    <p className="font-mono text-xs bg-gray-100 p-2 rounded">
                      {verificationResult.verificationId}
                    </p>
                  </div>
                )}

                <Alert>
                  <User className="h-4 w-4" />
                  <AlertDescription>
                    The seller has been notified of your visit. You may now proceed with viewing the property.
                  </AlertDescription>
                </Alert>
              </div>
            )}

            <Button onClick={() => setShowResult(false)} className="w-full">
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
