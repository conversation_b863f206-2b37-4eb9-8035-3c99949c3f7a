using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    [Table("buyer_listings", Schema = "snb")]
    public class BuyerListings
    {
        [Key]
        [Column("id")]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [Column("user_id")]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [Column("title")]
        [MaxLength(255)]
        public string Title { get; set; } = string.Empty;

        [Column("description")]
        public string? Description { get; set; }

        [Column("price", TypeName = "decimal(12,2)")]
        public decimal? Price { get; set; }

        [Required]
        [Column("property_type")]
        [MaxLength(100)]
        public string PropertyType { get; set; } = string.Empty;

        [Column("ad_type")]
        [MaxLength(50)]
        public string? AdType { get; set; }

        [Column("bedrooms")]
        public int? Bedrooms { get; set; }

        [Column("bathrooms")]
        public int? Bathrooms { get; set; }

        [Column("sqft")]
        public int? Sqft { get; set; }

        [Column("storeys")]
        public int? Storeys { get; set; }

        [Column("year_built")]
        public int? YearBuilt { get; set; }

        [Column("lot_size", TypeName = "decimal(10,2)")]
        public decimal? LotSize { get; set; }

        [Column("lot_size_unit")]
        [MaxLength(20)]
        public string? LotSizeUnit { get; set; }

        [Column("address", TypeName = "jsonb")]
        public string? Address { get; set; }

        [Column("parking_types", TypeName = "jsonb")]
        public string? ParkingTypes { get; set; }

        [Column("extra_features", TypeName = "jsonb")]
        public string? ExtraFeatures { get; set; }

        [Column("mls_number")]
        [MaxLength(50)]
        public string? MlsNumber { get; set; }

        [Column("virtual_tour")]
        public string? VirtualTour { get; set; }

        [Column("open_house", TypeName = "jsonb")]
        public string? OpenHouse { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }
}
