import { MarketingService } from './MarketingService'
// Removed auth import - no authentication required for browsing marketing services
import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function MarketingPage({ params, searchParams }: PageProps) {
  // No authentication required for browsing marketing services
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/services/marketing')
  }

  // Default to seller for marketing services
  const userType = resolvedSearchParams.userType || 'seller'

  return (
    <MarketingService
      userType={userType}
      isSignedIn={isSignedIn}
      country={country.toUpperCase()}
    />
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'seller'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Real Estate Marketing Services for ${userType === 'buyer' ? 'Buyers' : 'Sellers'} in ${country} | SoNoBrokers`,
    description: `Professional real estate marketing services in ${country}. Digital marketing, social media campaigns, and traditional advertising to sell your property faster.`,
    keywords: `real estate marketing, property marketing, digital marketing, ${userType}, ${country}, marketing services`,
  }
}
