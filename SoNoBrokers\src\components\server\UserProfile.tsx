import { getAuthUser } from '@/lib/auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { User, Mail, Shield, UserCheck } from 'lucide-react'

/**
 * Server Component for displaying user profile information
 * This component runs on the server and has access to authentication
 */
export async function UserProfile() {
  const user = await getAuthUser()

  if (!user) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            User Profile
          </CardTitle>
          <CardDescription>
            Please sign in to view your profile
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          User Profile
        </CardTitle>
        <CardDescription>
          Your account information and settings
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2">
          <Mail className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{user.email}</span>
        </div>
        
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{user.fullName || 'No name set'}</span>
        </div>

        <div className="flex items-center gap-2">
          <Shield className="h-4 w-4 text-muted-foreground" />
          <Badge variant="secondary">{user.role}</Badge>
        </div>

        <div className="flex items-center gap-2">
          <UserCheck className="h-4 w-4 text-muted-foreground" />
          <Badge variant={user.userType === 'Buyer' ? 'default' : 'outline'}>
            {user.userType}
          </Badge>
        </div>

        {user.permissions && user.permissions.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Permissions</h4>
            <div className="flex flex-wrap gap-1">
              {user.permissions.slice(0, 3).map((permission, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {permission.permission}
                </Badge>
              ))}
              {user.permissions.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{user.permissions.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
