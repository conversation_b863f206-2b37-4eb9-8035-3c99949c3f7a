# SoNoBrokers - For-Sale-By-Owner Property Marketplace

## Overview

SoNoBrokers is a comprehensive for-sale-by-owner property marketplace that operates across Canada (CA), United States (US), and United Arab Emirates (UAE). The platform prohibits realtor/broker MLS listings and focuses on direct property sales between owners and buyers.

## Architecture

### Frontend (React/Next.js)
- **Framework**: Next.js 14 with App Router
- **UI Library**: shadcn/ui with Tailwind CSS
- **Authentication**: Clerk
- **Internationalization**: react-i18next
- **State Management**: React Context + Server Components

### Backend (.NET Web API)
- **Framework**: .NET 9 Web API
- **Database**: PostgreSQL (Supabase)
- **ORM**: Dapper with DapperDbContext
- **Authentication**: Clerk JWT validation
- **Documentation**: Scalar (OpenAPI)
- **Health Checks**: Built-in health monitoring

### Database
- **Provider**: Supabase (PostgreSQL)
- **Migration**: Custom migration system
- **Connection**: Direct connection with pooling support

  Web API: http://localhost:7163
  React App: http://localhost:3000
  Test API Page: http://localhost:3000/test-api
  API Documentation: http://localhost:7163/scalar/v1
powershell -Command "cd 'C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App'; docker-compose up -d --build"
powershell -Command "cd 'C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App'; docker-compose down; docker-compose up -d"
## Project Structure

```
SoNoBrokersRoot/
├── SoNoBrokers/                    # React/Next.js Frontend
│   ├── src/
│   │   ├── app/                    # Next.js App Router pages
│   │   ├── components/             # React components
│   │   ├── lib/                    # Utilities and API clients
│   │   │   ├── api/               # API service layer
│   │   │   └── actions/           # Server actions
│   │   └── services/              # Legacy services (being migrated)
│   └── docs/                      # Frontend documentation
├── SoNoBrokersWebApi/             # .NET Web API Backend
│   └── MicroSaasWebApi.App/       # Main API project
│       ├── Controllers/           # API controllers
│       ├── Services/              # Business logic services
│       ├── Data/                  # Data access layer
│       └── Documentation/         # API documentation
└── docs/                          # Project documentation
    └── SoNoBrokers/              # SoNoBrokers specific docs
```

## Key Features

### Property Management
- Property listing creation and management
- Image upload and management
- Property search and filtering
- Property favorites and saved searches

### User Management
- Multi-role authentication (Buyer, Seller, Admin)
- User profiles and preferences
- Contact sharing and communication

### Scheduling & Visits
- Property visit scheduling
- QR code generation for property access
- Calendar integration
- Visit verification system

### Communication
- In-app messaging system
- Email notifications
- Contact sharing with privacy controls

### Multi-Region Support
- Country-specific routing (CA, US, UAE)
- Localized content and pricing
- Regional compliance features

## Technology Stack

### Frontend Technologies
- **Next.js 14**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first CSS framework
- **shadcn/ui**: Modern UI component library
- **Clerk**: Authentication and user management
- **react-i18next**: Internationalization
- **Mapbox**: Maps and location services

### Backend Technologies
- **ASP.NET Core 9**: Web API framework
- **Dapper**: Micro ORM for database access
- **PostgreSQL**: Primary database
- **Clerk**: JWT token validation
- **Scalar**: API documentation
- **Health Checks**: Monitoring and diagnostics

### Development Tools
- **Docker**: Containerization
- **PowerShell**: Automation scripts
- **Git**: Version control
- **VS Code**: Development environment

## Getting Started

### Prerequisites
- Node.js 18+ and npm
- .NET 9 SDK
- PostgreSQL (or Supabase account)
- Clerk account for authentication

### Environment Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd SoNoBrokersRoot
   ```

2. **Setup Frontend**
   ```bash
   cd SoNoBrokers
   npm install
   cp .env.example .env.local
   # Configure environment variables
   npm run dev
   ```

3. **Setup Backend**
   ```bash
   cd SoNoBrokersWebApi/MicroSaasWebApi.App
   cp .env.example .env
   # Configure environment variables
   dotnet run
   ```

### Environment Variables

#### Frontend (.env.local)
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:5005
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_key
CLERK_SECRET_KEY=your_clerk_secret
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_key
```

#### Backend (.env)
```env
DATABASE_URL=postgresql://user:password@host:port/database
Clerk__SecretKey=your_clerk_secret
Clerk__PublishableKey=your_clerk_publishable_key
```

## API Integration

### React to .NET API Migration

The project has been migrated from direct Prisma/Supabase calls to .NET Web API integration:

#### API Client Configuration
```typescript
// src/lib/api-client.ts
const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});
```

#### Service Layer Pattern
```typescript
// src/lib/api/properties-api.ts
export const propertiesApi = {
  getProperties: (params: PropertySearchParams) => 
    apiClient.get('/api/sonobrokers/properties', { params }),
  
  createProperty: (data: CreatePropertyRequest) =>
    apiClient.post('/api/sonobrokers/properties', data),
};
```

#### Server Actions
```typescript
// src/lib/actions/property-actions.ts
'use server';

export async function getPropertiesAction(params: PropertySearchParams) {
  const response = await propertiesApi.getProperties(params);
  return response.data;
}
```

## API Endpoints

### Core Endpoints

#### Properties
- `GET /api/sonobrokers/properties` - Search properties
- `POST /api/sonobrokers/properties` - Create property
- `GET /api/sonobrokers/properties/{id}` - Get property details
- `PUT /api/sonobrokers/properties/{id}` - Update property
- `DELETE /api/sonobrokers/properties/{id}` - Delete property

#### Users
- `GET /api/sonobrokers/users/profile` - Get user profile
- `PUT /api/sonobrokers/users/profile` - Update user profile
- `GET /api/sonobrokers/users/{id}` - Get user by ID

#### Authentication
- `POST /api/sonobrokers/auth/login` - User login
- `POST /api/sonobrokers/auth/register` - User registration
- `POST /api/sonobrokers/auth/logout` - User logout

#### Property Images
- `POST /api/sonobrokers/property-images` - Upload property image
- `GET /api/sonobrokers/property-images/{propertyId}` - Get property images
- `DELETE /api/sonobrokers/property-images/{id}` - Delete property image

#### Scheduling
- `POST /api/sonobrokers/scheduling/visits` - Schedule property visit
- `GET /api/sonobrokers/scheduling/visits` - Get user's visits
- `PUT /api/sonobrokers/scheduling/visits/{id}` - Update visit
- `DELETE /api/sonobrokers/scheduling/visits/{id}` - Cancel visit

### Testing Endpoints
- `GET /api/sonobrokers/test/ping` - API health check (public)
- `GET /api/sonobrokers/test/health` - Database connectivity test (public)

## Development Guidelines

### Code Organization
- Use TypeScript for type safety
- Follow Next.js App Router conventions
- Implement proper error handling
- Use server components where possible
- Keep client components minimal

### API Integration
- Use the centralized API client
- Implement proper error handling
- Use server actions for data mutations
- Cache responses appropriately
- Handle loading and error states

### Authentication
- All API calls include Clerk JWT tokens
- Implement role-based access control
- Handle token refresh automatically
- Secure sensitive operations

## Deployment

### Frontend Deployment
- Build: `npm run build`
- Deploy to Vercel, Netlify, or similar
- Configure environment variables
- Set up custom domains

### Backend Deployment
- Build: `dotnet publish`
- Deploy to Azure, AWS, or similar
- Configure database connections
- Set up health checks and monitoring

## Monitoring and Health Checks

### API Health Endpoints
- `/api/health` - Overall API health
- `/api/health/ready` - Readiness probe
- `/api/health/live` - Liveness probe
- `/scalar/v1` - API documentation

### Monitoring Features
- Database connectivity checks
- Memory usage monitoring
- Response time tracking
- Error rate monitoring

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For support and questions:
- Email: <EMAIL>
- Documentation: See `/docs` folder
- API Documentation: http://localhost:5005/scalar/v1

## License

[License information to be added]
