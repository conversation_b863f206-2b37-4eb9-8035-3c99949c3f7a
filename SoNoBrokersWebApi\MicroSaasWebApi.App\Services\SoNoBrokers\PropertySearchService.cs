using Dapper;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using static MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IPropertySearchService;

namespace MicroSaasWebApi.Services.SoNoBrokers
{
    public class PropertySearchService : IPropertySearchService
    {
        private readonly MicroSaasWebApi.App.Context.IDapperDbContext _dbContext;
        private readonly ILogger<PropertySearchService> _logger;

        public PropertySearchService(MicroSaasWebApi.App.Context.IDapperDbContext dbContext, ILogger<PropertySearchService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<IEnumerable<Property>> SearchPropertiesAsync(PropertySearchRequest request)
        {
            try
            {
                // Option 1: Use direct SQL query (current implementation)
                var sql = @"
                    SELECT p.*, u.full_name as seller_name, u.avatar_url as seller_avatar, u.role as seller_role
                    FROM snb.properties p
                    LEFT JOIN auth.users u ON p.seller_id = u.id
                    WHERE 1=1";

                var parameters = new DynamicParameters();

                // Add filters based on search request
                if (!string.IsNullOrEmpty(request.Location))
                {
                    sql += " AND (p.address ILIKE @location OR p.city ILIKE @location OR p.state ILIKE @location)";
                    parameters.Add("location", $"%{request.Location}%");
                }

                if (!string.IsNullOrEmpty(request.PropertyType))
                {
                    sql += " AND p.property_type = @propertyType";
                    parameters.Add("propertyType", request.PropertyType);
                }

                if (request.MinPrice.HasValue)
                {
                    sql += " AND p.price >= @minPrice";
                    parameters.Add("minPrice", request.MinPrice.Value);
                }

                if (request.MaxPrice.HasValue)
                {
                    sql += " AND p.price <= @maxPrice";
                    parameters.Add("maxPrice", request.MaxPrice.Value);
                }

                if (request.Bedrooms.HasValue)
                {
                    sql += " AND p.bedrooms >= @bedrooms";
                    parameters.Add("bedrooms", request.Bedrooms.Value);
                }

                if (request.Bathrooms.HasValue)
                {
                    sql += " AND p.bathrooms >= @bathrooms";
                    parameters.Add("bathrooms", request.Bathrooms.Value);
                }

                if (!string.IsNullOrEmpty(request.MlsNumber))
                {
                    sql += " AND p.mls_number = @mlsNumber";
                    parameters.Add("mlsNumber", request.MlsNumber);
                }

                sql += " ORDER BY p.created_at DESC";

                var properties = await _dbContext.QueryAsync<Property>(sql, parameters);

                // Load images for each property
                foreach (var property in properties)
                {
                    property.Images = await GetPropertyImagesAsync(property.Id);
                }

                return properties;

                // Option 2: Use Supabase stored function (example for future implementation)
                // You can create a PostgreSQL function in Supabase like:
                // CREATE OR REPLACE FUNCTION snb.search_properties(
                //     p_location TEXT DEFAULT NULL,
                //     p_property_type TEXT DEFAULT NULL,
                //     p_min_price DECIMAL DEFAULT NULL,
                //     p_max_price DECIMAL DEFAULT NULL,
                //     p_bedrooms INTEGER DEFAULT NULL,
                //     p_bathrooms DECIMAL DEFAULT NULL,
                //     p_mls_number TEXT DEFAULT NULL
                // ) RETURNS TABLE(...) AS $$
                // ... function implementation
                // $$ LANGUAGE plpgsql;
                //
                // Then call it like this:
                // var searchParams = new
                // {
                //     p_location = request.Location,
                //     p_property_type = request.PropertyType,
                //     p_min_price = request.MinPrice,
                //     p_max_price = request.MaxPrice,
                //     p_bedrooms = request.Bedrooms,
                //     p_bathrooms = request.Bathrooms,
                //     p_mls_number = request.MlsNumber
                // };
                // return await _dbContext.ExecuteFunctionAsync<Property>("snb.search_properties", searchParams);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching properties with request: {@Request}", request);
                throw;
            }
        }

        public async Task<Property?> GetPropertyByIdAsync(string propertyId)
        {
            try
            {
                var sql = @"
                    SELECT p.*, u.full_name as seller_name, u.avatar_url as seller_avatar, u.role as seller_role
                    FROM snb.properties p
                    LEFT JOIN auth.users u ON p.seller_id = u.id
                    WHERE p.id = @propertyId";

                var property = await _dbContext.QueryFirstOrDefaultAsync<Property>(sql, new { propertyId });

                if (property != null)
                {
                    property.Images = await GetPropertyImagesAsync(property.Id);
                }

                return property;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property by ID: {PropertyId}", propertyId);
                throw;
            }
        }

        private async Task<List<PropertyImage>> GetPropertyImagesAsync(string propertyId)
        {
            try
            {
                var sql = "SELECT * FROM snb.property_images WHERE property_id = @propertyId ORDER BY display_order";

                var images = await _dbContext.QueryAsync<PropertyImage>(sql, new { propertyId });

                return images.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting images for property: {PropertyId}", propertyId);
                return new List<PropertyImage>();
            }
        }

        /// <summary>
        /// Example method showing how to use Supabase stored procedures/functions
        /// This method demonstrates different ways to call database functions
        /// </summary>
        public async Task<IEnumerable<Property>> SearchPropertiesUsingStoredProcedureAsync(PropertySearchRequest request)
        {
            try
            {
                // Example 1: Using ExecuteFunctionAsync for Supabase functions
                // First, you would create a function in Supabase like this:
                /*
                CREATE OR REPLACE FUNCTION snb.search_properties_advanced(
                    p_location TEXT DEFAULT NULL,
                    p_property_type TEXT DEFAULT NULL,
                    p_min_price DECIMAL DEFAULT NULL,
                    p_max_price DECIMAL DEFAULT NULL,
                    p_bedrooms INTEGER DEFAULT NULL,
                    p_bathrooms DECIMAL DEFAULT NULL
                )
                RETURNS TABLE(
                    id UUID,
                    title TEXT,
                    description TEXT,
                    price DECIMAL,
                    property_type TEXT,
                    address TEXT,
                    city TEXT,
                    state TEXT,
                    bedrooms INTEGER,
                    bathrooms DECIMAL,
                    seller_name TEXT,
                    seller_avatar TEXT
                ) AS $$
                BEGIN
                    RETURN QUERY
                    SELECT
                        p.id,
                        p.title,
                        p.description,
                        p.price,
                        p.property_type,
                        p.address,
                        p.city,
                        p.state,
                        p.bedrooms,
                        p.bathrooms,
                        u.full_name as seller_name,
                        u.avatar_url as seller_avatar
                    FROM snb.properties p
                    LEFT JOIN auth.users u ON p.seller_id = u.id
                    WHERE
                        (p_location IS NULL OR p.address ILIKE '%' || p_location || '%'
                         OR p.city ILIKE '%' || p_location || '%'
                         OR p.state ILIKE '%' || p_location || '%')
                        AND (p_property_type IS NULL OR p.property_type = p_property_type)
                        AND (p_min_price IS NULL OR p.price >= p_min_price)
                        AND (p_max_price IS NULL OR p.price <= p_max_price)
                        AND (p_bedrooms IS NULL OR p.bedrooms >= p_bedrooms)
                        AND (p_bathrooms IS NULL OR p.bathrooms >= p_bathrooms)
                    ORDER BY p.created_at DESC;
                END;
                $$ LANGUAGE plpgsql;
                */

                var searchParams = new
                {
                    p_location = request.Location,
                    p_property_type = request.PropertyType,
                    p_min_price = request.MinPrice,
                    p_max_price = request.MaxPrice,
                    p_bedrooms = request.Bedrooms,
                    p_bathrooms = request.Bathrooms
                };

                // Call the Supabase function
                var properties = await _dbContext.ExecuteFunctionAsync<Property>("snb.search_properties_advanced", searchParams);

                return properties;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching properties using stored procedure with request: {@Request}", request);
                throw;
            }
        }

        /// <summary>
        /// Example method showing how to call a simple Supabase function without parameters
        /// </summary>
        public async Task<int> GetTotalPropertiesCountAsync()
        {
            try
            {
                // Example function in Supabase:
                /*
                CREATE OR REPLACE FUNCTION snb.get_total_properties_count()
                RETURNS INTEGER AS $$
                BEGIN
                    RETURN (SELECT COUNT(*) FROM snb.properties WHERE deleted_at IS NULL);
                END;
                $$ LANGUAGE plpgsql;
                */

                var result = await _dbContext.ExecuteFunctionSingleAsync<int?>("snb.get_total_properties_count");
                return result ?? 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total properties count");
                throw;
            }
        }

        /// <summary>
        /// Example method showing how to use stored procedures for complex operations
        /// </summary>
        public async Task<bool> UpdatePropertyStatusAsync(string propertyId, string status, string userId)
        {
            try
            {
                // Example stored procedure in Supabase:
                /*
                CREATE OR REPLACE FUNCTION snb.update_property_status(
                    p_property_id UUID,
                    p_status TEXT,
                    p_user_id UUID
                )
                RETURNS BOOLEAN AS $$
                DECLARE
                    property_exists BOOLEAN;
                    user_authorized BOOLEAN;
                BEGIN
                    -- Check if property exists
                    SELECT EXISTS(SELECT 1 FROM snb.properties WHERE id = p_property_id) INTO property_exists;

                    IF NOT property_exists THEN
                        RETURN FALSE;
                    END IF;

                    -- Check if user is authorized (owner or admin)
                    SELECT EXISTS(
                        SELECT 1 FROM snb.properties p
                        LEFT JOIN auth.users u ON p.seller_id = u.id
                        WHERE p.id = p_property_id
                        AND (p.seller_id = p_user_id OR u.role = 'admin')
                    ) INTO user_authorized;

                    IF NOT user_authorized THEN
                        RETURN FALSE;
                    END IF;

                    -- Update the property status
                    UPDATE snb.properties
                    SET status = p_status, updated_at = NOW()
                    WHERE id = p_property_id;

                    RETURN TRUE;
                END;
                $$ LANGUAGE plpgsql;
                */

                var parameters = new
                {
                    p_property_id = propertyId,
                    p_status = status,
                    p_user_id = userId
                };

                var result = await _dbContext.ExecuteFunctionSingleAsync<bool?>("snb.update_property_status", parameters);
                return result ?? false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating property status for property {PropertyId}", propertyId);
                throw;
            }
        }
    }


}
