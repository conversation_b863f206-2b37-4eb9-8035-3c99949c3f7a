using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    public enum UserType
    {
        buyer,
        seller,
        service_provider,
        admin,
        product
    }

    [Table("User", Schema = "snb")]
    public class User
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        public string FullName { get; set; } = string.Empty;

        public string? Phone { get; set; }

        [Column(TypeName = "jsonb")]
        public string? Address { get; set; }

        [Required]
        public UserType UserType { get; set; }

        [Required]
        public UserRole Role { get; set; } = UserRole.USER;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? LastLoginAt { get; set; }

        public bool LoggedIn { get; set; } = false;

        // Navigation properties
        public virtual ICollection<Property> Properties { get; set; } = new List<Property>();
        public virtual ICollection<Subscription> Subscriptions { get; set; } = new List<Subscription>();
        public virtual ServiceProvider? ServiceProvider { get; set; }
        public virtual ICollection<ServiceBooking> Bookings { get; set; } = new List<ServiceBooking>();
        public virtual ICollection<Message> Messages { get; set; } = new List<Message>();
        public virtual ICollection<PropertyViewing> Viewings { get; set; } = new List<PropertyViewing>();
        public virtual BuyerProfile? BuyerProfile { get; set; }
        public virtual SellerProfile? SellerProfile { get; set; }
        public virtual ICollection<BuyerListings> BuyerListings { get; set; } = new List<BuyerListings>();
        public virtual ICollection<SearchFilter> SearchFilters { get; set; } = new List<SearchFilter>();

        // Helper methods for JSON fields
        public T? GetAddressAs<T>() where T : class
        {
            if (string.IsNullOrEmpty(Address)) return null;
            try
            {
                return JsonSerializer.Deserialize<T>(Address);
            }
            catch
            {
                return null;
            }
        }

        public void SetAddress<T>(T address) where T : class
        {
            Address = address != null ? JsonSerializer.Serialize(address) : null;
        }
    }
}
