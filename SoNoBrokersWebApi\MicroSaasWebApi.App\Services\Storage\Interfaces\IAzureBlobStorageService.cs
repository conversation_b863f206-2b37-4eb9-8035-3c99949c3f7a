using Azure.Storage.Sas;
using MicroSaasWebApi.Models.Storage;

namespace MicroSaasWebApi.Services.Storage.Interfaces
{
    /// <summary>
    /// Interface for Azure Blob Storage service
    /// Follows Interface Segregation Principle
    /// </summary>
    public interface IAzureBlobStorageService
    {
        /// <summary>
        /// Uploads file to Azure Blob Storage
        /// </summary>
        Task<BlobStorageFile?> UploadFileAsync(string containerName, string fileName, Stream fileStream, string? contentType = null, Dictionary<string, string>? metadata = null);

        /// <summary>
        /// Downloads file from Azure Blob Storage
        /// </summary>
        Task<BlobStorageFileContent?> DownloadFileAsync(string containerName, string fileName);

        /// <summary>
        /// Updates file in Azure Blob Storage
        /// </summary>
        Task<BlobStorageFile?> UpdateFileAsync(string containerName, string fileName, Stream? newContent = null, string? newContentType = null, Dictionary<string, string>? newMetadata = null);

        /// <summary>
        /// Deletes file from Azure Blob Storage
        /// </summary>
        Task<bool> DeleteFileAsync(string containerName, string fileName);

        /// <summary>
        /// Lists files in Azure Blob Storage container
        /// </summary>
        Task<IEnumerable<BlobStorageFile>> ListFilesAsync(string containerName, string? prefix = null, int maxResults = 100);

        /// <summary>
        /// Gets file metadata from Azure Blob Storage
        /// </summary>
        Task<BlobStorageFile?> GetFileMetadataAsync(string containerName, string fileName);

        /// <summary>
        /// Generates SAS URL for file access
        /// </summary>
        Task<string?> GenerateSasUrlAsync(string containerName, string fileName, TimeSpan expiry, BlobSasPermissions permissions = BlobSasPermissions.Read);

        /// <summary>
        /// Copies file within Azure Blob Storage
        /// </summary>
        Task<bool> CopyFileAsync(string sourceContainerName, string sourceFileName, string destinationContainerName, string destinationFileName);
    }
}
