'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Search, MessageCircle, Handshake, Key, Camera, Users, DollarSign, Home } from 'lucide-react'

interface HowItWorksProps {
  userType: 'buyer' | 'seller'
}

export function HowItWorks({ userType }: HowItWorksProps) {
  const buyerSteps = [
    {
      step: 1,
      icon: Search,
      title: "Search Properties",
      description: "Browse thousands of commission-free listings with detailed photos and information.",
      highlight: "No hidden fees"
    },
    {
      step: 2,
      icon: MessageCircle,
      title: "Contact Sellers",
      description: "Message property owners directly through our secure platform to ask questions and schedule viewings.",
      highlight: "Direct communication"
    },
    {
      step: 3,
      icon: Users,
      title: "Get Professional Help",
      description: "Connect with lawyers, inspectors, and other professionals when you need expert assistance.",
      highlight: "Trusted professionals"
    },
    {
      step: 4,
      icon: Handshake,
      title: "Make an Offer",
      description: "Submit offers directly to sellers and negotiate terms without realtor interference.",
      highlight: "Direct negotiation"
    },
    {
      step: 5,
      icon: Key,
      title: "Close the Deal",
      description: "Complete your purchase with legal support and move into your new home.",
      highlight: "Save thousands"
    }
  ]

  const sellerSteps = [
    {
      step: 1,
      icon: Camera,
      title: "Professional Photos",
      description: "Get high-quality photos and virtual tours to showcase your property at its best.",
      highlight: "Professional quality"
    },
    {
      step: 2,
      icon: Home,
      title: "Create Your Listing",
      description: "Upload photos, add descriptions, and set your price. Our platform makes it easy.",
      highlight: "Easy listing process"
    },
    {
      step: 3,
      icon: Search,
      title: "Get Discovered",
      description: "Your property appears in search results and gets promoted to qualified buyers.",
      highlight: "Maximum exposure"
    },
    {
      step: 4,
      icon: MessageCircle,
      title: "Connect with Buyers",
      description: "Receive inquiries and messages from interested buyers directly through our platform.",
      highlight: "Direct buyer contact"
    },
    {
      step: 5,
      icon: DollarSign,
      title: "Sell Commission-Free",
      description: "Close the sale and keep 100% of your proceeds without paying realtor commissions.",
      highlight: "Keep more money"
    }
  ]

  const steps = userType === 'buyer' ? buyerSteps : sellerSteps

  return (
    <section className="py-16 bg-background w-full">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold text-foreground mb-4">How It Works</h2>
        <p className="text-lg text-muted-foreground">
          {userType === 'buyer' 
            ? 'Your journey to finding the perfect home, commission-free'
            : 'Sell your property in 5 simple steps and save thousands'
          }
        </p>
      </div>
      <div className="w-full px-4">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          {steps.map((step, index) => (
            <div key={step.step} className="flex flex-col h-full justify-between bg-background rounded-2xl shadow-lg border p-6 text-center min-h-[340px]">
              <div>
                <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                  <step.icon className="h-8 w-8 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">{step.title}</h3>
                <p className="text-sm text-muted-foreground mb-4">{step.description}</p>
              </div>
              <span className="inline-block bg-primary/10 text-primary rounded-full px-3 py-1 text-xs font-semibold">{step.highlight}</span>
            </div>
          ))}
        </div>
      </div>
      {/* Benefits Summary */}
      <div className="mt-16 text-center">
        <div className="max-w-2xl mx-auto bg-primary/5 border border-primary/10 rounded-2xl p-8">
          <h3 className="text-2xl font-bold text-foreground mb-4">
            {userType === 'buyer' ? 'Why Choose SoNoBrokers?' : 'Start Saving Today'}
          </h3>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <DollarSign className="h-8 w-8 text-primary mx-auto mb-2" />
              <p className="font-semibold text-foreground">No Commission</p>
              <p className="text-sm text-muted-foreground">
                {userType === 'buyer' ? 'Lower prices' : 'Keep more money'}
              </p>
            </div>
            <div className="text-center">
              <MessageCircle className="h-8 w-8 text-primary mx-auto mb-2" />
              <p className="font-semibold text-foreground">Direct Contact</p>
              <p className="text-sm text-muted-foreground">
                {userType === 'buyer' ? 'Talk to owners' : 'Meet buyers directly'}
              </p>
            </div>
            <div className="text-center">
              <Users className="h-8 w-8 text-primary mx-auto mb-2" />
              <p className="font-semibold text-foreground">Professional Support</p>
              <p className="text-sm text-muted-foreground">Expert help when you need it</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
