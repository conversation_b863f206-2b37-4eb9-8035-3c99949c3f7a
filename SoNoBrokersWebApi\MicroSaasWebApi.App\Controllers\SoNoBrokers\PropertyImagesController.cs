using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Models.Core;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using Microsoft.AspNetCore.Authorization;


namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/sonobrokers/property-images")]
    [Tags("Property Images")]
    public class PropertyImagesController : ControllerBase
    {
        private readonly IPropertyImageService _propertyImageService;
        private readonly ILogger<PropertyImagesController> _logger;

        public PropertyImagesController(IPropertyImageService propertyImageService, ILogger<PropertyImagesController> logger)
        {
            _propertyImageService = propertyImageService;
            _logger = logger;
        }

        /// <summary>
        /// Get images for a property
        /// </summary>
        /// <param name="propertyId">Property ID</param>
        /// <returns>List of property images</returns>
        [HttpGet("property/{propertyId}")]
        [AllowAnonymous]
        public async Task<ActionResult<ApiResponse<IEnumerable<PropertyImage>>>> GetPropertyImages(string propertyId)
        {
            try
            {
                var images = await _propertyImageService.GetPropertyImagesAsync(propertyId);
                return Ok(ApiResponse<IEnumerable<PropertyImage>>.SuccessResult(images, "Images retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving images for property {PropertyId}", propertyId);
                return StatusCode(500, ApiResponse<IEnumerable<PropertyImage>>.ErrorResult("Failed to retrieve images"));
            }
        }

        /// <summary>
        /// Get image by ID
        /// </summary>
        /// <param name="id">Image ID</param>
        /// <returns>Property image details</returns>
        [HttpGet("{id}")]
        [AllowAnonymous]
        public async Task<ActionResult<ApiResponse<PropertyImage>>> GetPropertyImage(string id)
        {
            try
            {
                var image = await _propertyImageService.GetPropertyImageByIdAsync(id);

                if (image == null)
                {
                    return NotFound(ApiResponse<PropertyImage>.ErrorResult("Image not found"));
                }

                return Ok(ApiResponse<PropertyImage>.SuccessResult(image, "Image retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving image {ImageId}", id);
                return StatusCode(500, ApiResponse<PropertyImage>.ErrorResult("Failed to retrieve image"));
            }
        }

        /// <summary>
        /// Upload property images
        /// </summary>
        /// <param name="propertyId">Property ID</param>
        /// <param name="files">Image files</param>
        /// <returns>Uploaded images</returns>
        [HttpPost("property/{propertyId}/upload")]




        public async Task<ActionResult<ApiResponse<IEnumerable<PropertyImage>>>> UploadPropertyImages(
            string propertyId,
            [FromForm] IFormFileCollection files)
        {
            try
            {
                // This method should be moved to PropertyImageService
                // For now, we'll assume the property exists since this is an upload endpoint
                // The service layer should handle property validation

                if (files == null || files.Count == 0)
                {
                    return BadRequest(ApiResponse<IEnumerable<PropertyImage>>.ErrorResult("No files provided"));
                }

                var uploadedImages = await _propertyImageService.UploadImagesAsync(propertyId, files);

                return CreatedAtAction(nameof(GetPropertyImages), new { propertyId },
                    ApiResponse<IEnumerable<PropertyImage>>.SuccessResult(uploadedImages, "Images uploaded successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading images for property {PropertyId}", propertyId);
                return StatusCode(500, ApiResponse<IEnumerable<PropertyImage>>.ErrorResult("Failed to upload images"));
            }
        }

        /// <summary>
        /// Create property image record
        /// </summary>
        /// <param name="propertyImage">Property image data</param>
        /// <returns>Created property image</returns>
        [HttpPost]



        public async Task<ActionResult<ApiResponse<PropertyImage>>> CreatePropertyImage(PropertyImage propertyImage)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse<PropertyImage>.ErrorResult("Invalid image data"));
                }

                var createdImage = await _propertyImageService.CreatePropertyImageAsync(propertyImage);

                return CreatedAtAction(nameof(GetPropertyImage), new { id = createdImage.Id },
                    ApiResponse<PropertyImage>.SuccessResult(createdImage, "Image record created successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating property image record");
                return StatusCode(500, ApiResponse<PropertyImage>.ErrorResult("Failed to create image record"));
            }
        }

        /// <summary>
        /// Update property image
        /// </summary>
        /// <param name="id">Image ID</param>
        /// <param name="propertyImage">Updated image data</param>
        /// <returns>Updated property image</returns>
        [HttpPut("{id}")]




        public async Task<ActionResult<ApiResponse<PropertyImage>>> UpdatePropertyImage(string id, PropertyImage propertyImage)
        {
            try
            {
                if (id != propertyImage.Id)
                {
                    return BadRequest(ApiResponse<PropertyImage>.ErrorResult("Image ID mismatch"));
                }

                propertyImage.Id = id; // Ensure ID is set
                var updatedImage = await _propertyImageService.UpdatePropertyImageAsync(propertyImage);

                return Ok(ApiResponse<PropertyImage>.SuccessResult(updatedImage, "Image updated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating property image {ImageId}", id);
                return StatusCode(500, ApiResponse<PropertyImage>.ErrorResult("Failed to update image"));
            }
        }

        /// <summary>
        /// Delete property image
        /// </summary>
        /// <param name="id">Image ID</param>
        /// <returns>Success message</returns>
        [HttpDelete("{id}")]



        public async Task<ActionResult<ApiResponse<object>>> DeletePropertyImage(string id)
        {
            try
            {
                var deleted = await _propertyImageService.DeletePropertyImageAsync(id);

                if (!deleted)
                {
                    return NotFound(ApiResponse<object>.ErrorResult("Image not found"));
                }

                return Ok(ApiResponse<object>.SuccessResult(new { }, "Image deleted successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting property image {ImageId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResult("Failed to delete image"));
            }
        }

        /// <summary>
        /// Set primary image for property
        /// </summary>
        /// <param name="propertyId">Property ID</param>
        /// <param name="imageId">Image ID to set as primary</param>
        /// <returns>Success message</returns>
        [HttpPatch("property/{propertyId}/primary/{imageId}")]



        public async Task<ActionResult<ApiResponse<object>>> SetPrimaryImage(string propertyId, string imageId)
        {
            try
            {
                var success = await _propertyImageService.SetPrimaryImageAsync(propertyId, imageId);

                if (!success)
                {
                    return NotFound(ApiResponse<object>.ErrorResult("Image not found or property not found"));
                }

                return Ok(ApiResponse<object>.SuccessResult(new { }, "Primary image set successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting primary image for property {PropertyId}", propertyId);
                return StatusCode(500, ApiResponse<object>.ErrorResult("Failed to set primary image"));
            }
        }
    }
}
