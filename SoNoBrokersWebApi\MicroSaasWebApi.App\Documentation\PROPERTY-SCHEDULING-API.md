# Property Scheduling API Documentation

## Overview

The Property Scheduling API enables comprehensive property visit scheduling, seller availability management, QR code generation for visit verification, and calendar integration for property showings.

## Base URL

```
/api/sonobrokers/property-scheduling
```

## Authentication

All endpoints require authentication via Clerk JWT token:

```http
Authorization: Bearer <jwt_token>
```

## Enums

### Visit Status
| Status | Value | Description |
|--------|-------|-------------|
| Pending | 1 | Visit request pending seller approval |
| Confirmed | 2 | Visit confirmed by seller |
| Rescheduled | 3 | Visit rescheduled |
| Cancelled | 4 | Visit cancelled |
| Completed | 5 | Visit completed |
| NoShow | 6 | Buyer didn't show up |
| Expired | 7 | Visit request expired |

### Visit Type
| Type | Value | Description |
|------|-------|-------------|
| InPerson | 1 | In-person property visit |
| Virtual | 2 | Virtual property tour |
| SelfGuided | 3 | Self-guided visit with QR verification |

### Verification Method
| Method | Value | Description |
|--------|-------|-------------|
| QrCode | 1 | QR code scan verification |
| SellerPresent | 2 | Seller present verification |
| KeyBox | 3 | Key box access verification |
| PropertyManager | 4 | Property manager verification |
| Other | 5 | Other verification method |

## Seller Availability Endpoints

### Create Seller Availability

Set availability for property showings.

```http
POST /api/sonobrokers/property-scheduling/availability
```

**Request Body:**
```json
{
  "propertyId": "property-123",
  "dayOfWeek": 1,
  "startTime": "09:00",
  "endTime": "17:00",
  "isAvailable": true,
  "notes": "Available for showings"
}
```

**Response:**
```json
{
  "id": "availability-123",
  "propertyId": "property-123",
  "propertyTitle": "Beautiful Family Home",
  "dayOfWeek": 1,
  "dayOfWeekDisplay": "Monday",
  "startTime": "09:00",
  "endTime": "17:00",
  "timeRange": "09:00 - 17:00",
  "isAvailable": true,
  "notes": "Available for showings",
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

### Get Seller Availability

Retrieve seller's availability schedule.

```http
GET /api/sonobrokers/property-scheduling/availability
```

**Query Parameters:**
- `propertyId` (string): Filter by specific property

**Response:**
```json
[
  {
    "id": "availability-123",
    "propertyId": "property-123",
    "propertyTitle": "Beautiful Family Home",
    "dayOfWeek": 1,
    "dayOfWeekDisplay": "Monday",
    "startTime": "09:00",
    "endTime": "17:00",
    "timeRange": "09:00 - 17:00",
    "isAvailable": true,
    "notes": "Available for showings",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
]
```

### Update Seller Availability

Update existing availability slot.

```http
PUT /api/sonobrokers/property-scheduling/availability/{id}
```

**Request Body:**
```json
{
  "startTime": "10:00",
  "endTime": "18:00",
  "isAvailable": true,
  "notes": "Updated availability"
}
```

### Delete Seller Availability

Remove availability slot.

```http
DELETE /api/sonobrokers/property-scheduling/availability/{id}
```

### Get Property Availability (Public)

Get available time slots for a property (accessible to buyers).

```http
GET /api/sonobrokers/property-scheduling/property/{propertyId}/availability
```

**Response:**
```json
[
  {
    "dayOfWeek": 1,
    "dayOfWeekDisplay": "Monday",
    "timeSlots": [
      {
        "startTime": "09:00",
        "endTime": "12:00",
        "available": true
      },
      {
        "startTime": "13:00",
        "endTime": "17:00",
        "available": true
      }
    ]
  }
]
```

## Visit Scheduling Endpoints

### Create Visit Schedule

Request a property visit.

```http
POST /api/sonobrokers/property-scheduling/visits
```

**Request Body:**
```json
{
  "propertyId": "property-123",
  "sellerId": "seller-123",
  "contactShareId": "contact-share-123",
  "requestedDate": "2024-12-31",
  "requestedTime": "14:00",
  "visitType": 1,
  "buyerNotes": "Looking forward to viewing the property"
}
```

**Response:**
```json
{
  "id": "visit-123",
  "propertyId": "property-123",
  "propertyTitle": "Beautiful Family Home",
  "propertyAddress": "123 Main St, City, State",
  "buyerId": "buyer-123",
  "buyerName": "John Buyer",
  "buyerEmail": "<EMAIL>",
  "sellerId": "seller-123",
  "sellerName": "Jane Seller",
  "contactShareId": "contact-share-123",
  "requestedDate": "2024-12-31",
  "requestedTime": "14:00",
  "requestedDateTime": "2024-12-31 14:00",
  "confirmedDate": null,
  "confirmedTime": null,
  "confirmedDateTime": null,
  "status": 1,
  "statusDisplay": "Pending",
  "visitType": 1,
  "visitTypeDisplay": "In-Person",
  "buyerNotes": "Looking forward to viewing the property",
  "sellerNotes": null,
  "sellerResponse": null,
  "qrCode": null,
  "qrCodeGenerated": false,
  "visitVerified": false,
  "calendarInviteSent": false,
  "createdAt": "2024-01-15T10:30:00Z",
  "respondedAt": null
}
```

### Get Visit Schedule

Retrieve specific visit details.

```http
GET /api/sonobrokers/property-scheduling/visits/{id}
```

### Get Visit Schedules

Retrieve user's visit schedules.

```http
GET /api/sonobrokers/property-scheduling/visits
```

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 20)
- `propertyId` (string): Filter by property
- `status` (int): Filter by visit status
- `visitType` (int): Filter by visit type
- `fromDate` (date): Filter visits from date
- `toDate` (date): Filter visits to date

### Respond to Visit Request

Seller responds to visit request.

```http
PUT /api/sonobrokers/property-scheduling/visits/{id}/respond
```

**Request Body:**
```json
{
  "status": 2,
  "confirmedDate": "2024-12-31",
  "confirmedTime": "15:00",
  "sellerResponse": "Confirmed for 3 PM instead of 2 PM",
  "sellerNotes": "Please use the front entrance"
}
```

### Cancel Visit Schedule

Cancel a visit schedule.

```http
PUT /api/sonobrokers/property-scheduling/visits/{id}/cancel
```

**Request Body:**
```json
{
  "reason": "Seller no longer available"
}
```

## QR Code Management Endpoints

### Generate Property QR Code

Generate QR code for property visit verification.

```http
POST /api/sonobrokers/property-scheduling/property/{propertyId}/qr-code
```

**Response:**
```json
{
  "id": "qr-123",
  "propertyId": "property-123",
  "propertyTitle": "Beautiful Family Home",
  "qrCodeData": "encrypted-qr-data-string",
  "qrCodeImage": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
  "isActive": true,
  "expiresAt": "2024-02-15T10:30:00Z",
  "scanCount": 0,
  "createdAt": "2024-01-15T10:30:00Z",
  "lastScannedAt": null
}
```

### Get Property QR Code

Retrieve existing QR code for property.

```http
GET /api/sonobrokers/property-scheduling/property/{propertyId}/qr-code
```

### Regenerate Property QR Code

Generate new QR code (invalidates previous one).

```http
PUT /api/sonobrokers/property-scheduling/property/{propertyId}/qr-code
```

## Visit Verification Endpoints

### Verify Visit

Verify property visit using QR code.

```http
POST /api/sonobrokers/property-scheduling/visits/{visitId}/verify
```

**Request Body:**
```json
{
  "qrCodeData": "encrypted-qr-data-string",
  "method": 1,
  "deviceInfo": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)",
  "location": "40.7128,-74.0060",
  "notes": "Arrived at property for scheduled visit"
}
```

**Response:**
```json
{
  "id": "verification-123",
  "visitScheduleId": "visit-123",
  "propertyTitle": "Beautiful Family Home",
  "buyerName": "John Buyer",
  "method": 1,
  "methodDisplay": "QR Code",
  "isValid": true,
  "notes": "Arrived at property for scheduled visit",
  "verifiedAt": "2024-01-15T14:30:00Z"
}
```

### Get Visit Verifications

Retrieve verification history for a visit.

```http
GET /api/sonobrokers/property-scheduling/visits/{visitId}/verifications
```

## Statistics Endpoints

### Get Visit Scheduling Statistics

Get user's visit scheduling statistics.

```http
GET /api/sonobrokers/property-scheduling/stats
```

**Response:**
```json
{
  "totalVisitRequests": 25,
  "pendingRequests": 5,
  "confirmedVisits": 15,
  "completedVisits": 12,
  "cancelledVisits": 3,
  "noShowVisits": 1,
  "verifiedVisits": 10,
  "confirmationRate": 0.75,
  "completionRate": 0.80,
  "verificationRate": 0.83,
  "lastVisitRequest": "2024-01-15T10:30:00Z",
  "nextScheduledVisit": "2024-01-20T14:00:00Z",
  "propertyStats": [
    {
      "propertyId": "property-123",
      "propertyTitle": "Beautiful Family Home",
      "visitRequests": 8,
      "confirmedVisits": 6,
      "completedVisits": 5,
      "lastVisit": "2024-01-10T15:00:00Z",
      "nextVisit": "2024-01-20T14:00:00Z"
    }
  ]
}
```

### Get Upcoming Visits

Get upcoming visits for user.

```http
GET /api/sonobrokers/property-scheduling/upcoming
```

**Query Parameters:**
- `days` (int): Number of days ahead (default: 7)

**Response:**
```json
[
  {
    "id": "visit-123",
    "propertyTitle": "Beautiful Family Home",
    "propertyAddress": "123 Main St, City, State",
    "confirmedDate": "2024-01-20",
    "confirmedTime": "14:00",
    "visitType": 1,
    "visitTypeDisplay": "In-Person",
    "status": 2,
    "statusDisplay": "Confirmed",
    "buyerName": "John Buyer",
    "sellerName": "Jane Seller"
  }
]
```

## Calendar Integration

### Calendar Invite Features
- Automatic calendar invites sent when visit is confirmed
- ICS file generation for calendar applications
- Email notifications with calendar attachments
- Timezone handling for different locations

### Calendar Invite Endpoints

```http
POST /api/sonobrokers/property-scheduling/visits/{visitId}/calendar-invite
PUT /api/sonobrokers/property-scheduling/visits/{visitId}/calendar-invite
DELETE /api/sonobrokers/property-scheduling/visits/{visitId}/calendar-invite
```

## Security Features

### QR Code Security
- Encrypted QR code data with property and seller information
- Time-limited QR codes (30-day expiration)
- Signature validation to prevent tampering
- Scan tracking and logging

### Access Control
- Sellers can only manage their property schedules
- Buyers can only access their visit schedules
- QR code generation restricted to property owners
- Visit verification requires valid QR code

## Error Responses

### 400 Bad Request - Invalid Visit Time
```json
{
  "error": "Invalid visit time",
  "details": "Requested time is outside seller's availability",
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/api/sonobrokers/property-scheduling/visits"
}
```

### 409 Conflict - Time Slot Unavailable
```json
{
  "error": "Time slot unavailable",
  "details": "Another visit is already scheduled for this time",
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/api/sonobrokers/property-scheduling/visits"
}
```

## Business Rules

### Visit Scheduling
- Visits can only be scheduled during seller's available hours
- Maximum 3 visits per property per day
- Visits must be scheduled at least 2 hours in advance
- Buyers can have maximum 5 pending visit requests

### QR Code Management
- One active QR code per property
- QR codes expire after 30 days
- QR codes can be regenerated by property owner
- Scan attempts are logged for security

### Calendar Integration
- Calendar invites sent automatically when visit confirmed
- Invites updated when visit is rescheduled
- Cancellation notices sent when visit cancelled
- Supports major calendar applications (Outlook, Google, Apple)

## Rate Limiting

- **Visit requests**: 10 per day per user
- **QR code generation**: 5 per day per property
- **API requests**: 1000 per hour per user

## Webhooks

Property scheduling events can trigger webhooks:

### Events
- `visit.requested`
- `visit.confirmed`
- `visit.cancelled`
- `visit.completed`
- `visit.verified`
- `qr_code.generated`
- `qr_code.scanned`

### Webhook Payload
```json
{
  "event": "visit.confirmed",
  "data": {
    "visitId": "visit-123",
    "propertyId": "property-123",
    "buyerId": "buyer-123",
    "sellerId": "seller-123",
    "confirmedDate": "2024-12-31",
    "confirmedTime": "14:00",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```
