using System.ComponentModel.DataAnnotations;

namespace MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling
{
    /// <summary>
    /// Seller availability schedule for property visits
    /// </summary>
    public class SellerAvailability
    {
        public string Id { get; set; } = string.Empty;
        public string PropertyId { get; set; } = string.Empty;
        public string SellerId { get; set; } = string.Empty;
        public DayOfWeek DayOfWeek { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public bool IsAvailable { get; set; } = true;
        public string? Notes { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Property visit schedule request and confirmation
    /// </summary>
    public class PropertyVisitSchedule
    {
        public string Id { get; set; } = string.Empty;
        public string PropertyId { get; set; } = string.Empty;
        public string BuyerId { get; set; } = string.Empty;
        public string SellerId { get; set; } = string.Empty;
        public string ContactShareId { get; set; } = string.Empty;
        public DateTime RequestedDate { get; set; }
        public TimeSpan RequestedTime { get; set; }
        public DateTime? ConfirmedDate { get; set; }
        public TimeSpan? ConfirmedTime { get; set; }
        public VisitStatus Status { get; set; } = VisitStatus.Pending;
        public VisitType VisitType { get; set; } = VisitType.InPerson;
        public string? BuyerNotes { get; set; }
        public string? SellerNotes { get; set; }
        public string? SellerResponse { get; set; }
        public string? QrCode { get; set; }
        public bool QrCodeGenerated { get; set; } = false;
        public DateTime? QrCodeGeneratedAt { get; set; }
        public bool VisitVerified { get; set; } = false;
        public DateTime? VisitVerifiedAt { get; set; }
        public string? VerificationMethod { get; set; }
        public bool CalendarInviteSent { get; set; } = false;
        public DateTime? CalendarInviteSentAt { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? RespondedAt { get; set; }
    }

    /// <summary>
    /// Visit verification log for security tracking
    /// </summary>
    public class VisitVerification
    {
        public string Id { get; set; } = string.Empty;
        public string VisitScheduleId { get; set; } = string.Empty;
        public string PropertyId { get; set; } = string.Empty;
        public string BuyerId { get; set; } = string.Empty;
        public string? SellerId { get; set; }
        public VerificationMethod Method { get; set; }
        public string? QrCodeScanned { get; set; }
        public string? DeviceInfo { get; set; }
        public string? IpAddress { get; set; }
        public string? Location { get; set; }
        public bool IsValid { get; set; } = true;
        public string? Notes { get; set; }
        public DateTime VerifiedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Property QR code for visit verification
    /// </summary>
    public class PropertyQrCode
    {
        public string Id { get; set; } = string.Empty;
        public string PropertyId { get; set; } = string.Empty;
        public string SellerId { get; set; } = string.Empty;
        public string QrCodeData { get; set; } = string.Empty;
        public string QrCodeImage { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public DateTime? ExpiresAt { get; set; }
        public int ScanCount { get; set; } = 0;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? LastScannedAt { get; set; }
    }

    /// <summary>
    /// Enums for visit scheduling
    /// </summary>
    public enum VisitStatus
    {
        Pending = 1,
        Confirmed = 2,
        Rescheduled = 3,
        Cancelled = 4,
        Completed = 5,
        NoShow = 6,
        Expired = 7
    }

    public enum VisitType
    {
        InPerson = 1,
        Virtual = 2,
        SelfGuided = 3
    }

    public enum VerificationMethod
    {
        QrCode = 1,
        SellerPresent = 2,
        KeyBox = 3,
        PropertyManager = 4,
        Other = 5
    }

    /// <summary>
    /// Request models for API endpoints
    /// </summary>
    public class CreateSellerAvailabilityRequest
    {
        [Required]
        public string PropertyId { get; set; } = string.Empty;
        
        [Required]
        public DayOfWeek DayOfWeek { get; set; }
        
        [Required]
        public TimeSpan StartTime { get; set; }
        
        [Required]
        public TimeSpan EndTime { get; set; }
        
        public bool IsAvailable { get; set; } = true;
        public string? Notes { get; set; }
    }

    public class UpdateSellerAvailabilityRequest
    {
        public TimeSpan? StartTime { get; set; }
        public TimeSpan? EndTime { get; set; }
        public bool? IsAvailable { get; set; }
        public string? Notes { get; set; }
    }

    public class CreateVisitScheduleRequest
    {
        [Required]
        public string PropertyId { get; set; } = string.Empty;
        
        [Required]
        public string SellerId { get; set; } = string.Empty;
        
        [Required]
        public string ContactShareId { get; set; } = string.Empty;
        
        [Required]
        public DateTime RequestedDate { get; set; }
        
        [Required]
        public TimeSpan RequestedTime { get; set; }
        
        public VisitType VisitType { get; set; } = VisitType.InPerson;
        public string? BuyerNotes { get; set; }
    }

    public class RespondToVisitRequest
    {
        [Required]
        public VisitStatus Status { get; set; }
        
        public DateTime? ConfirmedDate { get; set; }
        public TimeSpan? ConfirmedTime { get; set; }
        public string? SellerResponse { get; set; }
        public string? SellerNotes { get; set; }
    }

    public class VerifyVisitRequest
    {
        [Required]
        public string QrCodeData { get; set; } = string.Empty;
        
        public VerificationMethod Method { get; set; } = VerificationMethod.QrCode;
        public string? DeviceInfo { get; set; }
        public string? Location { get; set; }
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Response models for API endpoints
    /// </summary>
    public class SellerAvailabilityResponse
    {
        public string Id { get; set; } = string.Empty;
        public string PropertyId { get; set; } = string.Empty;
        public string PropertyTitle { get; set; } = string.Empty;
        public DayOfWeek DayOfWeek { get; set; }
        public string DayOfWeekDisplay { get; set; } = string.Empty;
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public string TimeRange { get; set; } = string.Empty;
        public bool IsAvailable { get; set; }
        public string? Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class PropertyVisitScheduleResponse
    {
        public string Id { get; set; } = string.Empty;
        public string PropertyId { get; set; } = string.Empty;
        public string PropertyTitle { get; set; } = string.Empty;
        public string PropertyAddress { get; set; } = string.Empty;
        public string BuyerId { get; set; } = string.Empty;
        public string BuyerName { get; set; } = string.Empty;
        public string BuyerEmail { get; set; } = string.Empty;
        public string SellerId { get; set; } = string.Empty;
        public string SellerName { get; set; } = string.Empty;
        public string ContactShareId { get; set; } = string.Empty;
        public DateTime RequestedDate { get; set; }
        public TimeSpan RequestedTime { get; set; }
        public string RequestedDateTime { get; set; } = string.Empty;
        public DateTime? ConfirmedDate { get; set; }
        public TimeSpan? ConfirmedTime { get; set; }
        public string? ConfirmedDateTime { get; set; }
        public VisitStatus Status { get; set; }
        public string StatusDisplay { get; set; } = string.Empty;
        public VisitType VisitType { get; set; }
        public string VisitTypeDisplay { get; set; } = string.Empty;
        public string? BuyerNotes { get; set; }
        public string? SellerNotes { get; set; }
        public string? SellerResponse { get; set; }
        public string? QrCode { get; set; }
        public bool QrCodeGenerated { get; set; }
        public bool VisitVerified { get; set; }
        public DateTime? VisitVerifiedAt { get; set; }
        public bool CalendarInviteSent { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? RespondedAt { get; set; }
    }

    public class PropertyQrCodeResponse
    {
        public string Id { get; set; } = string.Empty;
        public string PropertyId { get; set; } = string.Empty;
        public string PropertyTitle { get; set; } = string.Empty;
        public string QrCodeData { get; set; } = string.Empty;
        public string QrCodeImage { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public int ScanCount { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastScannedAt { get; set; }
    }

    public class VisitVerificationResponse
    {
        public string Id { get; set; } = string.Empty;
        public string VisitScheduleId { get; set; } = string.Empty;
        public string PropertyTitle { get; set; } = string.Empty;
        public string BuyerName { get; set; } = string.Empty;
        public VerificationMethod Method { get; set; }
        public string MethodDisplay { get; set; } = string.Empty;
        public bool IsValid { get; set; }
        public string? Notes { get; set; }
        public DateTime VerifiedAt { get; set; }
    }

    /// <summary>
    /// Search and filter parameters
    /// </summary>
    public class VisitScheduleSearchParams
    {
        public string? PropertyId { get; set; }
        public string? BuyerId { get; set; }
        public string? SellerId { get; set; }
        public VisitStatus? Status { get; set; }
        public VisitType? VisitType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool? VerifiedOnly { get; set; }
        public string? Search { get; set; }
        public int Page { get; set; } = 1;
        public int Limit { get; set; } = 20;
        public string SortBy { get; set; } = "createdAt";
        public string SortOrder { get; set; } = "desc";
    }

    public class VisitScheduleSearchResponse
    {
        public List<PropertyVisitScheduleResponse> Visits { get; set; } = new();
        public int Total { get; set; }
        public int Page { get; set; }
        public int TotalPages { get; set; }
        public bool HasMore { get; set; }
    }
}
