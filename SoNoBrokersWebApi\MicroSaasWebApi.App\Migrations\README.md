# SoNoBrokers Database Migrations

This folder contains SQL script-based migrations for the SoNoBrokers database, following a numbered migration pattern for reliable, atomic database changes.

## 📋 Migration Overview

| Migration | Purpose | Dependencies |
|-----------|---------|--------------|
| `0001_create_enums.sql` | **Database enums** - All enum types and extensions | None |
| `0002_create_core_tables.sql` | **Core tables** - User, Property, PropertyImage, Conversation, Message, BuyerOffer, PropertyViewing | 0001 |
| `0003_create_subscription_tables.sql` | **Subscription tables** - SubscriptionSnb, Advertiser, ServiceProvider, Stripe tables | 0002 |
| `0004_create_contact_scheduling_tables.sql` | **Contact & Scheduling** - ContactShare, SellerAvailability, PropertyVisitSchedule, QR codes | 0003 |
| `0005_create_indexes.sql` | **Performance indexes** - 50+ indexes for query optimization | 0004 |
| `0006_create_triggers.sql` | **Database triggers** - Automatic timestamp updates and utility functions | 0005 |
| `0007_create_functions.sql` | **Stored procedures** - Business logic functions for search, analytics, contact sharing | 0006 |
| `0008_insert_seed_data.sql` | **Sample data** - Development and testing data | 0007 |

## 🎯 Migration Benefits

✅ **Atomic Changes** - Each migration represents one logical change
✅ **Ordered Execution** - Migrations run in sequence (0001, 0002, 0003...)
✅ **Rollback Support** - Easy to identify and reverse specific changes
✅ **Version Control** - Clear history of database schema evolution
✅ **Team Collaboration** - No merge conflicts with numbered files
✅ **Production Safety** - Migrations track what's been applied

## 🚀 Quick Start

### Option 1: PowerShell Scripts (Recommended)

```powershell
# Check current migration status
.\Migration-Status.ps1

# Run all pending migrations
.\Run-Migrations.ps1

# Run migrations with custom connection string
.\Run-Migrations.ps1 -ConnectionString "Host=localhost;Database=sonobrokers;Username=postgres;Password=yourpassword;"

# Dry run to see what would be executed
.\Run-Migrations.ps1 -DryRun

# Force run without confirmation
.\Run-Migrations.ps1 -Force
```

### Option 2: Manual psql Execution

```bash
# Connect to your Supabase database
psql "***************************************************************************************************/postgres"

# Run migrations in order
\i 0001_create_enums.sql
\i 0002_create_core_tables.sql
\i 0003_create_subscription_tables.sql
\i 0004_create_contact_scheduling_tables.sql
\i 0005_create_indexes.sql
\i 0006_create_triggers.sql
\i 0007_create_functions.sql
\i 0008_insert_seed_data.sql
```

### Option 3: .NET Migration Service

The `DatabaseMigrationService` automatically runs pending migrations on application startup.

## 📊 What Gets Created

### 🗃️ Database Enums (0001)
- **User enums**: UserRole, UserStatus
- **Property enums**: PropertyType, PropertyStatus, ListingType
- **Service enums**: ServiceType, SubscriptionType, SubscriptionStatus
- **Contact enums**: ContactShareType, ContactShareStatus
- **Scheduling enums**: VisitStatus, VisitType, VerificationMethod
- **Extensions**: uuid-ossp, postgis

### 👥 Core Tables (0002)
- **User** - User accounts with Clerk integration
- **Property** - Property listings with JSONB address and features
- **PropertyImage** - Property photos with display order
- **Conversation** - Buyer-seller conversations
- **Message** - Chat messages with read status
- **BuyerOffer** - Property purchase offers
- **PropertyViewing** - Scheduled property viewings

### 💳 Subscription & Service Tables (0003)
- **SubscriptionSnb** - User subscription management
- **Advertiser** - Business advertiser profiles
- **AdvertiserSubscription** - Advertiser subscription plans
- **ServiceProvider** - Service provider profiles (photographers, inspectors, etc.)
- **ServiceBooking** - Service booking management
- **stripe_customers** - Stripe customer integration
- **stripe_products** - Stripe product catalog

### 🤝 Contact & Scheduling Tables (0004)
- **ContactShare** - Buyer-seller contact sharing (4 types: ContactRequest, PropertyOffer, ScheduleVisit, OfferWithVisit)
- **SellerAvailability** - Seller weekly availability schedules
- **PropertyVisitSchedule** - Visit requests and confirmations
- **PropertyQrCode** - QR codes for visit verification
- **VisitVerification** - Security log of visit verifications

### ⚡ Performance Optimizations (0005)
- **50+ indexes** for optimal query performance
- **GIS indexes** for location-based searches
- **Composite indexes** for complex queries
- **Unique constraints** for data integrity

### 🔄 Automation & Logic (0006-0007)
- **Triggers** for automatic timestamp updates
- **Utility functions** for validation (email, phone, UUID generation)
- **Search functions** for advanced property filtering
- **Analytics functions** for dashboard statistics
- **Contact sharing functions** for business logic
- **Scheduling functions** for availability checking

### 🧪 Sample Data (0008)
- **7 sample users** (3 sellers, 3 buyers, 1 admin)
- **3 sample properties** (downtown condo, suburban house, modern townhouse)
- **5 property images** with proper display order
- **2 contact share requests** with different types
- **7 seller availability schedules** covering weekdays and weekends

## 🔧 Configuration Requirements

### Environment Variables
Make sure your .NET Web API has the correct connection string:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=db.***.supabase.co;Database=postgres;Username=postgres;Password=***;SSL Mode=Require;"
  }
}
```

### Supabase Setup
1. Create a Supabase project
2. Get your database connection details
3. Ensure you have the correct permissions
4. Run the scripts using psql or Supabase SQL editor

## ✅ Verification

After running the scripts, verify everything is working:

### 1. Check Tables
```sql
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;
```

### 2. Check Sample Data
```sql
SELECT 'Users' as table_name, COUNT(*) as count FROM public."User"
UNION ALL
SELECT 'Properties', COUNT(*) FROM public."Property"
UNION ALL
SELECT 'ContactShares', COUNT(*) FROM public."ContactShare"
UNION ALL
SELECT 'SellerAvailability', COUNT(*) FROM public."SellerAvailability";
```

### 3. Test Stored Procedures
```sql
-- Test contact share creation
SELECT * FROM sp_create_contact_share(
  (SELECT id FROM public."Property" LIMIT 1),
  (SELECT id FROM public."User" WHERE role = 'USER' LIMIT 1),
  (SELECT "sellerId" FROM public."Property" LIMIT 1),
  'Test Buyer', '<EMAIL>', NULL, 'Test message', 'ContactRequest'
);
```

## 🐛 Troubleshooting

### Migration Status Issues

**1. Check Migration Status**
```powershell
.\Migration-Status.ps1
```
This shows which migrations have been applied and which are pending.

**2. Migration Already Applied**
```
⏭️ Migration 0001_create_enums already executed, skipping
```
**Solution**: This is normal. Migrations track what's been applied and skip duplicates.

**3. Permission Denied**
```
ERROR: permission denied for schema public
```
**Solution**: Ensure you're connected as a superuser or have the correct permissions.

**4. Extension Not Found**
```
ERROR: extension "uuid-ossp" is not available
```
**Solution**: Enable the extension in Supabase dashboard or contact your database administrator.

**5. Migration Order Issues**
```
ERROR: relation "Property" does not exist
```
**Solution**: Migrations must run in order. Use `.\Run-Migrations.ps1` to ensure proper sequencing.

### PowerShell Script Issues

**1. psql Command Not Found**
```
❌ psql command not found
```
**Solution**: Install PostgreSQL client tools or use the Supabase SQL editor.

**2. Connection Issues**
```powershell
# Test with custom connection string
.\Run-Migrations.ps1 -ConnectionString "Host=localhost;Database=sonobrokers;Username=postgres;Password=yourpassword;"
```

### Reset Database (Nuclear Option)
If you need to start completely fresh:

```sql
-- Drop all tables (be very careful!)
DROP SCHEMA public CASCADE;
CREATE SCHEMA public;
GRANT ALL ON SCHEMA public TO postgres;
GRANT ALL ON SCHEMA public TO public;

-- Then run migrations from scratch
```

## 📚 Related Documentation

- **API Reference**: [../Documentation/API-REFERENCE.md](../Documentation/API-REFERENCE.md)
- **Contact Sharing API**: [../Documentation/CONTACT-SHARING-API.md](../Documentation/CONTACT-SHARING-API.md)
- **Property Scheduling API**: [../Documentation/PROPERTY-SCHEDULING-API.md](../Documentation/PROPERTY-SCHEDULING-API.md)
- **React Integration**: [../../../SoNoBrokers/docs/react-api-integration.md](../../../SoNoBrokers/docs/react-api-integration.md)
- **Database Analysis**: [../../../SoNoBrokers/docs/database-synchronization-analysis.md](../../../SoNoBrokers/docs/database-synchronization-analysis.md)

## 🎯 Next Steps

After running migrations:

1. **Verify Migration Status**
   ```powershell
   .\Migration-Status.ps1
   ```

2. **Test .NET Web API**
   - Start the application (migrations run automatically)
   - Test API endpoints
   - Verify database connectivity

3. **Test React Components**
   - Property search and listing
   - Contact sharing functionality
   - Property scheduling features
   - QR code generation and scanning

4. **Production Deployment**
   - Run migrations on staging environment
   - Test all functionality
   - Deploy to production
   - Monitor migration execution

## 📚 Migration Best Practices

✅ **Always backup** before running migrations in production
✅ **Test migrations** on staging environment first
✅ **Run migrations** during maintenance windows
✅ **Monitor execution** and check logs
✅ **Verify data integrity** after migrations
✅ **Keep migrations small** and focused on single changes

## 📞 Support

If you encounter issues:

1. **Check migration status**: `.\Migration-Status.ps1`
2. **Review logs**: Check PowerShell script output
3. **Verify connection**: Test database connectivity
4. **Check dependencies**: Ensure migrations run in order
5. **Consult documentation**: Review this README

## 🎉 Success!

Your SoNoBrokers database is now:
- ✅ **Fully structured** with all tables and relationships
- ✅ **Performance optimized** with 50+ indexes
- ✅ **Business logic ready** with stored procedures and functions
- ✅ **Sample data loaded** for development and testing
- ✅ **Migration tracked** for reliable deployments

Ready for development and production use! 🚀
