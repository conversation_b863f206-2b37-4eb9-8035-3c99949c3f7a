using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Extensions;
using MicroSaasWebApi.Models.SoNoBrokers.Messaging;
using MicroSaasWebApi.Services.SoNoBrokers;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    /// <summary>
    /// Controller for managing conversations and messages between buyers and sellers
    /// </summary>
    [ApiController]
    [Route("api/sonobrokers/messaging")]
    [Tags("Messaging")]
    [Authorize]
    public class MessagingController : ControllerBase
    {
        private readonly IMessagingService _messagingService;
        private readonly ILogger<MessagingController> _logger;

        public MessagingController(IMessagingService messagingService, ILogger<MessagingController> logger)
        {
            _messagingService = messagingService;
            _logger = logger;
        }

        /// <summary>
        /// Create a new conversation about a property
        /// </summary>
        [HttpPost("conversations")]
        public async Task<ActionResult<ConversationResponse>> CreateConversation([FromBody] CreateConversationRequest request)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var conversation = await _messagingService.CreateConversationAsync(userId, request);
                return Ok(conversation);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized access attempt to create conversation");
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating conversation");
                return StatusCode(500, "An error occurred while creating the conversation");
            }
        }

        /// <summary>
        /// Get all conversations for the current user
        /// </summary>
        [HttpGet("conversations")]
        public async Task<ActionResult<ConversationSearchResponse>> GetConversations([FromQuery] ConversationSearchParams searchParams)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var conversations = await _messagingService.GetConversationsAsync(userId, searchParams);
                return Ok(conversations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting conversations");
                return StatusCode(500, "An error occurred while retrieving conversations");
            }
        }

        /// <summary>
        /// Get a specific conversation with messages
        /// </summary>
        [HttpGet("conversations/{conversationId}")]
        public async Task<ActionResult<ConversationResponse>> GetConversation(string conversationId)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var conversation = await _messagingService.GetConversationAsync(conversationId, userId);
                if (conversation == null)
                {
                    return NotFound("Conversation not found or access denied");
                }

                return Ok(conversation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting conversation {ConversationId}", conversationId);
                return StatusCode(500, "An error occurred while retrieving the conversation");
            }
        }

        /// <summary>
        /// Send a message in a conversation
        /// </summary>
        [HttpPost("messages")]
        public async Task<ActionResult<MessageResponse>> SendMessage([FromBody] CreateMessageRequest request)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var message = await _messagingService.SendMessageAsync(userId, request);
                return Ok(message);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized access attempt to send message");
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message");
                return StatusCode(500, "An error occurred while sending the message");
            }
        }

        /// <summary>
        /// Get messages in a conversation
        /// </summary>
        [HttpGet("conversations/{conversationId}/messages")]
        public async Task<ActionResult<MessageSearchResponse>> GetMessages(string conversationId, [FromQuery] MessageSearchParams searchParams)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var messages = await _messagingService.GetMessagesAsync(conversationId, userId, searchParams);
                return Ok(messages);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized access attempt to get messages");
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting messages for conversation {ConversationId}", conversationId);
                return StatusCode(500, "An error occurred while retrieving messages");
            }
        }

        /// <summary>
        /// Mark messages as read
        /// </summary>
        [HttpPut("conversations/{conversationId}/messages/read")]
        public async Task<ActionResult> MarkMessagesAsRead(string conversationId, [FromBody] MarkMessagesReadRequest? request = null)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var success = await _messagingService.MarkMessagesAsReadAsync(userId, conversationId, request?.MessageIds);
                if (!success)
                {
                    return NotFound("Conversation not found or access denied");
                }

                return Ok(new { success = true, message = "Messages marked as read" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking messages as read for conversation {ConversationId}", conversationId);
                return StatusCode(500, "An error occurred while marking messages as read");
            }
        }

        /// <summary>
        /// Get message statistics for the current user
        /// </summary>
        [HttpGet("stats")]
        public async Task<ActionResult<MessageStats>> GetMessageStats()
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var stats = await _messagingService.GetMessageStatsAsync(userId);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting message stats");
                return StatusCode(500, "An error occurred while retrieving message statistics");
            }
        }

        /// <summary>
        /// Update a conversation
        /// </summary>
        [HttpPut("conversations/{conversationId}")]
        public async Task<ActionResult> UpdateConversation(string conversationId, [FromBody] UpdateConversationRequest request)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var success = await _messagingService.UpdateConversationAsync(conversationId, userId, request);
                if (!success)
                {
                    return NotFound("Conversation not found or access denied");
                }

                return Ok(new { success = true, message = "Conversation updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating conversation {ConversationId}", conversationId);
                return StatusCode(500, "An error occurred while updating the conversation");
            }
        }

        /// <summary>
        /// Delete (deactivate) a conversation
        /// </summary>
        [HttpDelete("conversations/{conversationId}")]
        public async Task<ActionResult> DeleteConversation(string conversationId)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var success = await _messagingService.DeleteConversationAsync(conversationId, userId);
                if (!success)
                {
                    return NotFound("Conversation not found or access denied");
                }

                return Ok(new { success = true, message = "Conversation deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting conversation {ConversationId}", conversationId);
                return StatusCode(500, "An error occurred while deleting the conversation");
            }
        }
    }
}
