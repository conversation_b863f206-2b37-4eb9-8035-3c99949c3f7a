using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Data.Context;
using MicroSaasWebApi.Data.Repositories.Interfaces;
using System.Text;

namespace MicroSaasWebApi.Data.Repositories.SoNoBrokers
{
    /// <summary>
    /// User repository implementation using Dapper
    /// </summary>
    public class UserRepository : IUserRepository
    {
        private readonly IDapperDbContext _dbContext;
        private readonly ILogger<UserRepository> _logger;

        public UserRepository(IDapperDbContext dbContext, ILogger<UserRepository> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<IEnumerable<User>> GetAllAsync()
        {
            try
            {
                const string sql = @"
                    SELECT * FROM public.""User""
                    ORDER BY ""createdAt"" DESC";

                return await _dbContext.QueryAsync<User>(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all users");
                throw;
            }
        }

        public async Task<User?> GetByIdAsync(string id)
        {
            try
            {
                const string sql = @"
                    SELECT * FROM public.""User""
                    WHERE id = @Id";

                return await _dbContext.QueryFirstOrDefaultAsync<User>(sql, new { Id = id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user by ID: {Id}", id);
                throw;
            }
        }

        public async Task<User?> GetByEmailAsync(string email)
        {
            try
            {
                const string sql = @"
                    SELECT * FROM public.""User""
                    WHERE email = @Email";

                return await _dbContext.QueryFirstOrDefaultAsync<User>(sql, new { Email = email });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user by email: {Email}", email);
                throw;
            }
        }

        public async Task<User?> GetByClerkIdAsync(string clerkId)
        {
            try
            {
                const string sql = @"
                    SELECT * FROM public.""User""
                    WHERE ""clerkId"" = @ClerkId";

                return await _dbContext.QueryFirstOrDefaultAsync<User>(sql, new { ClerkId = clerkId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user by Clerk ID: {ClerkId}", clerkId);
                throw;
            }
        }

        public async Task<string> CreateAsync(User user)
        {
            try
            {
                user.Id = Guid.NewGuid().ToString();
                user.CreatedAt = DateTime.UtcNow;
                user.UpdatedAt = DateTime.UtcNow;

                const string sql = @"
                    INSERT INTO public.""User"" (
                        id, ""clerkId"", email, ""fullName"", ""firstName"", ""lastName"", 
                        phone, role, ""userType"", ""isActive"", ""lastLogin"", 
                        ""createdAt"", ""updatedAt""
                    ) VALUES (
                        @Id, @ClerkId, @Email, @FullName, @FirstName, @LastName,
                        @Phone, @Role, @UserType, @IsActive, @LastLogin,
                        @CreatedAt, @UpdatedAt
                    )";

                await _dbContext.ExecuteAsync(sql, user);
                return user.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user");
                throw;
            }
        }

        public async Task<bool> UpdateAsync(User user)
        {
            try
            {
                user.UpdatedAt = DateTime.UtcNow;

                const string sql = @"
                    UPDATE public.""User"" SET
                        ""fullName"" = @FullName,
                        ""firstName"" = @FirstName,
                        ""lastName"" = @LastName,
                        phone = @Phone,
                        role = @Role,
                        ""userType"" = @UserType,
                        ""isActive"" = @IsActive,
                        ""lastLogin"" = @LastLogin,
                        ""updatedAt"" = @UpdatedAt
                    WHERE id = @Id";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, user);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user: {Id}", user.Id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(string id)
        {
            try
            {
                const string sql = @"DELETE FROM public.""User"" WHERE id = @Id";
                var rowsAffected = await _dbContext.ExecuteAsync(sql, new { Id = id });
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user: {Id}", id);
                throw;
            }
        }

        public async Task<IEnumerable<User>> GetByRoleAsync(UserRole role)
        {
            try
            {
                const string sql = @"
                    SELECT * FROM public.""User""
                    WHERE role = @Role
                    ORDER BY ""createdAt"" DESC";

                return await _dbContext.QueryAsync<User>(sql, new { Role = role });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving users by role: {Role}", role);
                throw;
            }
        }

        public async Task<IEnumerable<User>> GetByUserTypeAsync(UserType userType)
        {
            try
            {
                const string sql = @"
                    SELECT * FROM public.""User""
                    WHERE ""userType"" = @UserType
                    ORDER BY ""createdAt"" DESC";

                return await _dbContext.QueryAsync<User>(sql, new { UserType = userType });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving users by user type: {UserType}", userType);
                throw;
            }
        }

        public async Task<bool> UpdateLastLoginAsync(string id)
        {
            try
            {
                const string sql = @"
                    UPDATE public.""User"" SET
                        ""lastLogin"" = @LastLogin,
                        ""updatedAt"" = @UpdatedAt
                    WHERE id = @Id";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, new
                {
                    Id = id,
                    LastLogin = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating last login for user: {Id}", id);
                throw;
            }
        }

        public async Task<bool> UpdateStatusAsync(string id, bool isActive)
        {
            try
            {
                const string sql = @"
                    UPDATE public.""User"" SET
                        ""isActive"" = @IsActive,
                        ""updatedAt"" = @UpdatedAt
                    WHERE id = @Id";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, new
                {
                    Id = id,
                    IsActive = isActive,
                    UpdatedAt = DateTime.UtcNow
                });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating status for user: {Id}", id);
                throw;
            }
        }

        public async Task<int> GetCountByRoleAsync(UserRole role)
        {
            try
            {
                const string sql = @"
                    SELECT COUNT(*) FROM public.""User""
                    WHERE role = @Role";

                return await _dbContext.QuerySingleAsync<int>(sql, new { Role = role });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user count by role: {Role}", role);
                throw;
            }
        }

        public async Task<int> GetActiveUsersCountAsync()
        {
            try
            {
                const string sql = @"
                    SELECT COUNT(*) FROM public.""User""
                    WHERE ""isActive"" = true";

                return await _dbContext.QuerySingleAsync<int>(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active users count");
                throw;
            }
        }

        public async Task<(IEnumerable<User> Users, int TotalCount)> SearchAsync(
            string? searchTerm = null,
            UserRole? role = null,
            UserType? userType = null,
            bool? isActive = null,
            int page = 1,
            int pageSize = 20)
        {
            try
            {
                var whereConditions = new List<string>();
                var parameters = new Dictionary<string, object>();

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    whereConditions.Add(@"(""fullName"" ILIKE @SearchTerm OR email ILIKE @SearchTerm)");
                    parameters.Add("SearchTerm", $"%{searchTerm}%");
                }

                if (role.HasValue)
                {
                    whereConditions.Add("role = @Role");
                    parameters.Add("Role", role.Value);
                }

                if (userType.HasValue)
                {
                    whereConditions.Add(@"""userType"" = @UserType");
                    parameters.Add("UserType", userType.Value);
                }

                if (isActive.HasValue)
                {
                    whereConditions.Add(@"""isActive"" = @IsActive");
                    parameters.Add("IsActive", isActive.Value);
                }

                var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";
                var offset = (page - 1) * pageSize;

                parameters.Add("Offset", offset);
                parameters.Add("PageSize", pageSize);

                // Get total count
                var countSql = $@"
                    SELECT COUNT(*) FROM public.""User""
                    {whereClause}";

                var totalCount = await _dbContext.QuerySingleAsync<int>(countSql, parameters);

                // Get paginated results
                var dataSql = $@"
                    SELECT * FROM public.""User""
                    {whereClause}
                    ORDER BY ""createdAt"" DESC
                    LIMIT @PageSize OFFSET @Offset";

                var users = await _dbContext.QueryAsync<User>(dataSql, parameters);

                return (users, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching users");
                throw;
            }
        }
    }
}
