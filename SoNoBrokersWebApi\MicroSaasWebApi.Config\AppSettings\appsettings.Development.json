{"AppInfo": {"Environment": "Development"}, "TenantSettings": {"EnableMultiTenantFeatures": true, "TenantCacheExpiry": "00:05:00", "MaxTenantsPerUser": 10, "EnableTenantIsolation": true}, "FeatureFlags": {"EnableSwagger": true, "EnableHangfire": true, "EnableDetailedLogging": true, "EnablePayments": false, "EnableAnalytics": true, "EnableCustomBranding": true, "EnableMultiRegion": false, "EnableAdvancedSecurity": false, "EnableRateLimiting": false, "EnableResponseCompression": true, "EnableHealthChecks": true}, "RateLimiting": {"EnableGlobalRateLimit": false, "GlobalRequestsPerMinute": 10000, "EnablePerTenantRateLimit": false, "TenantRequestsPerMinute": 1000, "EnablePerUserRateLimit": false, "UserRequestsPerMinute": 600}, "Security": {"Cors": {"AllowedOrigins": ["https://localhost:3000", "https://www.api.sonobrokers.com", "https://sonobrokers.com", "https://localhost:7163"], "AllowCredentials": true}, "Headers": {"EnableSecurityHeaders": false, "EnableHsts": false}}, "Monitoring": {"HealthChecks": {"EnableUI": true, "EnableDetailedErrors": true, "CacheDuration": "00:00:30"}}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore": "Information", "Microsoft.AspNetCore.Hosting": "Information", "Microsoft.AspNetCore.Routing": "Information", "System.Net.Http.HttpClient": "Information", "Hangfire": "Information"}, "Console": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss.fff "}}, "ExternalServices": {"Stripe": {"EnableTestMode": true}, "Azure": {"Storage": {"ContainerName": "microsaas-dev-files"}}, "Email": {"Smtp": {"Port": 587, "EnableSsl": true}}}}