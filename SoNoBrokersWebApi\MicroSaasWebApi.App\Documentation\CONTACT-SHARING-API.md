# Contact Sharing API Documentation

## Overview

The Contact Sharing API enables buyers to share their contact information with property sellers through various interaction types including contact requests, property offers, visit scheduling, and combined offers with visits.

## Base URL

```
/api/sonobrokers/contact-sharing
```

## Authentication

All endpoints require authentication via Clerk JWT token:

```http
Authorization: Bearer <jwt_token>
```

## Contact Share Types

| Type | Value | Description |
|------|-------|-------------|
| ContactRequest | 1 | Basic contact information sharing |
| PropertyOffer | 2 | Property purchase offer |
| VisitScheduling | 3 | Schedule property visit |
| OfferWithVisit | 4 | Combined offer and visit request |

## Contact Share Status

| Status | Value | Description |
|--------|-------|-------------|
| Sent | 1 | Contact share sent to seller |
| Viewed | 2 | Seller viewed the contact share |
| Responded | 3 | Seller responded |
| Accepted | 5 | Seller accepted the request/offer |
| Declined | 6 | Seller declined the request/offer |
| Expired | 7 | Contact share expired |

## Endpoints

### Create Contact Share

Create a new contact share between buyer and seller.

```http
POST /api/sonobrokers/contact-sharing
```

**Request Body:**
```json
{
  "propertyId": "string",
  "sellerId": "string",
  "buyerName": "string",
  "buyerEmail": "string",
  "buyerPhone": "string",
  "message": "string",
  "shareType": 1,
  "offerAmount": 500000,
  "preferredVisitDate": "2024-12-31",
  "preferredVisitTime": "14:00"
}
```

**Response:**
```json
{
  "id": "contact-share-123",
  "propertyId": "property-123",
  "buyerId": "buyer-123",
  "sellerId": "seller-123",
  "buyerName": "John Buyer",
  "buyerEmail": "<EMAIL>",
  "buyerPhone": "+1234567890",
  "message": "I'm interested in this property",
  "shareType": 1,
  "shareTypeDisplay": "Contact Request",
  "status": 1,
  "statusDisplay": "Sent",
  "offerAmount": 500000,
  "preferredVisitDate": "2024-12-31",
  "preferredVisitTime": "14:00",
  "emailSent": true,
  "emailSentAt": "2024-01-15T10:30:00Z",
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

### Get Contact Shares

Retrieve contact shares for the authenticated user.

```http
GET /api/sonobrokers/contact-sharing
```

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 20, max: 100)
- `sortBy` (string): Sort field (default: "createdAt")
- `sortOrder` (string): Sort order "asc" or "desc" (default: "desc")
- `shareType` (int): Filter by share type
- `status` (int): Filter by status
- `propertyId` (string): Filter by property
- `search` (string): Search in messages and buyer names

**Response:**
```json
{
  "contactShares": [
    {
      "id": "contact-share-123",
      "propertyId": "property-123",
      "propertyTitle": "Beautiful Family Home",
      "propertyAddress": "123 Main St, City, State",
      "propertyPrice": 500000,
      "buyerId": "buyer-123",
      "buyerName": "John Buyer",
      "buyerEmail": "<EMAIL>",
      "sellerId": "seller-123",
      "sellerName": "Jane Seller",
      "shareType": 1,
      "shareTypeDisplay": "Contact Request",
      "status": 1,
      "statusDisplay": "Sent",
      "message": "I'm interested in this property",
      "offerAmount": null,
      "sellerResponse": null,
      "emailSent": true,
      "createdAt": "2024-01-15T10:30:00Z",
      "respondedAt": null
    }
  ],
  "total": 25,
  "page": 1,
  "totalPages": 3,
  "hasMore": true
}
```

### Get Contact Share by ID

Retrieve a specific contact share by ID.

```http
GET /api/sonobrokers/contact-sharing/{id}
```

**Response:**
```json
{
  "id": "contact-share-123",
  "propertyId": "property-123",
  "propertyTitle": "Beautiful Family Home",
  "propertyAddress": "123 Main St, City, State",
  "propertyPrice": 500000,
  "propertyImage": "https://example.com/image.jpg",
  "buyerId": "buyer-123",
  "buyerName": "John Buyer",
  "buyerEmail": "<EMAIL>",
  "buyerPhone": "+1234567890",
  "sellerId": "seller-123",
  "sellerName": "Jane Seller",
  "sellerEmail": "<EMAIL>",
  "shareType": 2,
  "shareTypeDisplay": "Property Offer",
  "status": 5,
  "statusDisplay": "Accepted",
  "message": "I'd like to make an offer on this property",
  "offerAmount": 475000,
  "sellerResponse": "Thank you for your offer. I accept!",
  "emailSent": true,
  "emailSentAt": "2024-01-15T10:30:00Z",
  "viewedAt": "2024-01-15T11:00:00Z",
  "respondedAt": "2024-01-15T14:30:00Z",
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T14:30:00Z"
}
```

### Update Contact Share Status (Seller Response)

Allow sellers to respond to contact shares.

```http
PUT /api/sonobrokers/contact-sharing/{id}/respond
```

**Request Body:**
```json
{
  "contactShareId": "contact-share-123",
  "status": 5,
  "response": "Thank you for your interest. I'd be happy to show you the property!"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Response sent successfully",
  "contactShare": {
    "id": "contact-share-123",
    "status": 5,
    "statusDisplay": "Accepted",
    "sellerResponse": "Thank you for your interest. I'd be happy to show you the property!",
    "respondedAt": "2024-01-15T14:30:00Z",
    "updatedAt": "2024-01-15T14:30:00Z"
  }
}
```

### Delete Contact Share

Delete a contact share (only by the creator).

```http
DELETE /api/sonobrokers/contact-sharing/{id}
```

**Response:**
```json
{
  "success": true,
  "message": "Contact share deleted successfully"
}
```

### Get Contact Share Statistics

Get statistics for the authenticated user's contact shares.

```http
GET /api/sonobrokers/contact-sharing/stats
```

**Response:**
```json
{
  "totalContactShares": 25,
  "contactRequests": 10,
  "propertyOffers": 8,
  "visitSchedules": 5,
  "offerWithVisits": 2,
  "pendingResponses": 5,
  "acceptedOffers": 3,
  "declinedOffers": 2,
  "averageResponseTime": "2.5 hours",
  "responseRate": 0.85,
  "lastContactShare": "2024-01-15T10:30:00Z",
  "totalOfferAmount": 2500000,
  "averageOfferAmount": 500000,
  "propertyStats": [
    {
      "propertyId": "property-123",
      "propertyTitle": "Beautiful Family Home",
      "contactShares": 5,
      "offers": 3,
      "averageOffer": 475000,
      "lastContact": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### Get Property Contact Share Statistics

Get contact share statistics for a specific property.

```http
GET /api/sonobrokers/contact-sharing/property/{propertyId}/stats
```

**Response:**
```json
{
  "propertyId": "property-123",
  "propertyTitle": "Beautiful Family Home",
  "totalContactShares": 12,
  "contactRequests": 5,
  "propertyOffers": 4,
  "visitSchedules": 2,
  "offerWithVisits": 1,
  "averageOfferAmount": 485000,
  "highestOffer": 500000,
  "lowestOffer": 450000,
  "responseRate": 0.75,
  "averageResponseTime": "3.2 hours",
  "lastContactShare": "2024-01-15T10:30:00Z",
  "dailyStats": [
    {
      "date": "2024-01-15",
      "contactShares": 3,
      "offers": 2,
      "visits": 1
    }
  ]
}
```

## Error Responses

### 400 Bad Request
```json
{
  "error": "Validation failed",
  "details": {
    "buyerEmail": ["Email is required"],
    "offerAmount": ["Offer amount must be greater than 0"]
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/api/sonobrokers/contact-sharing"
}
```

### 401 Unauthorized
```json
{
  "error": "Authentication required",
  "details": "Valid JWT token required",
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/api/sonobrokers/contact-sharing"
}
```

### 403 Forbidden
```json
{
  "error": "Access denied",
  "details": "You don't have permission to access this contact share",
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/api/sonobrokers/contact-sharing/123"
}
```

### 404 Not Found
```json
{
  "error": "Contact share not found",
  "details": "Contact share with ID '123' does not exist",
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/api/sonobrokers/contact-sharing/123"
}
```

### 429 Too Many Requests
```json
{
  "error": "Rate limit exceeded",
  "details": "Maximum 10 contact shares per day allowed",
  "retryAfter": 3600,
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/api/sonobrokers/contact-sharing"
}
```

## Business Rules

### Contact Share Creation
- Users can create maximum 10 contact shares per day
- Cannot create duplicate contact shares for the same property within 24 hours
- Offer amount must be positive for PropertyOffer and OfferWithVisit types
- Visit date must be in the future for VisitScheduling and OfferWithVisit types

### Access Control
- Buyers can view their own contact shares
- Sellers can view contact shares for their properties
- Only sellers can respond to contact shares
- Only creators can delete their contact shares

### Email Notifications
- Email sent to seller when contact share is created
- Email sent to buyer when seller responds
- Email templates vary by share type
- Email delivery is tracked and logged

## Rate Limiting

- **Contact share creation**: 10 per day per user
- **API requests**: 1000 per hour per user
- **Email sending**: 100 emails per day per user

## Webhooks

Contact sharing events can trigger webhooks:

### Events
- `contact_share.created`
- `contact_share.viewed`
- `contact_share.responded`
- `contact_share.accepted`
- `contact_share.declined`

### Webhook Payload
```json
{
  "event": "contact_share.created",
  "data": {
    "contactShareId": "contact-share-123",
    "propertyId": "property-123",
    "buyerId": "buyer-123",
    "sellerId": "seller-123",
    "shareType": 1,
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

## Testing

### Test Endpoints
Use the following test data for development:

```json
{
  "propertyId": "test-property-123",
  "sellerId": "test-seller-123",
  "buyerName": "Test Buyer",
  "buyerEmail": "<EMAIL>",
  "buyerPhone": "+1234567890",
  "message": "Test contact share message",
  "shareType": 1
}
```

### Mock Responses
In development mode, the API can return mock responses for testing purposes.
