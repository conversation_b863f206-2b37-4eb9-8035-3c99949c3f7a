using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MicroSaasWebApi.Tests.Configuration;
using System.Diagnostics;

namespace MicroSaasWebApi.Tests.Infrastructure
{
    /// <summary>
    /// Manages the test environment lifecycle and configuration
    /// </summary>
    public class TestEnvironmentManager : IDisposable
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<TestEnvironmentManager> _logger;
        private readonly TestDatabaseSetup _databaseSetup;
        private readonly List<IDisposable> _disposables;
        private bool _disposed = false;
        private bool _initialized = false;

        public TestEnvironmentManager()
        {
            _configuration = TestConfiguration.CreateTestConfiguration();
            _disposables = new List<IDisposable>();
            
            // Setup logging
            var serviceProvider = new ServiceCollection()
                .AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information))
                .BuildServiceProvider();
            
            _logger = serviceProvider.GetRequiredService<ILogger<TestEnvironmentManager>>();
            _disposables.Add(serviceProvider);
            
            // Setup database manager
            _databaseSetup = new TestDatabaseSetup(_configuration, 
                serviceProvider.GetRequiredService<ILogger<TestDatabaseSetup>>());
            _disposables.Add(_databaseSetup);
        }

        /// <summary>
        /// Initializes the test environment
        /// </summary>
        public async Task InitializeAsync()
        {
            if (_initialized)
            {
                return;
            }

            try
            {
                _logger.LogInformation("Initializing test environment...");

                // Validate configuration
                ValidateConfiguration();

                // Initialize database
                await _databaseSetup.InitializeAsync();

                // Setup mock services
                await SetupMockServicesAsync();

                // Verify external dependencies
                await VerifyExternalDependenciesAsync();

                _initialized = true;
                _logger.LogInformation("Test environment initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize test environment");
                throw;
            }
        }

        /// <summary>
        /// Cleans up the test environment
        /// </summary>
        public async Task CleanupAsync()
        {
            try
            {
                _logger.LogInformation("Cleaning up test environment...");

                // Cleanup database
                await _databaseSetup.CleanupAsync();

                // Cleanup mock services
                await CleanupMockServicesAsync();

                _logger.LogInformation("Test environment cleanup completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to cleanup test environment");
                throw;
            }
        }

        /// <summary>
        /// Resets the test environment to a clean state
        /// </summary>
        public async Task ResetAsync()
        {
            try
            {
                _logger.LogInformation("Resetting test environment...");

                // Reset database
                await _databaseSetup.ResetAsync();

                // Reset mock services
                await ResetMockServicesAsync();

                _logger.LogInformation("Test environment reset completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to reset test environment");
                throw;
            }
        }

        /// <summary>
        /// Gets the test configuration
        /// </summary>
        public IConfiguration GetConfiguration() => _configuration;

        /// <summary>
        /// Checks if the test environment is healthy
        /// </summary>
        public async Task<bool> IsHealthyAsync()
        {
            try
            {
                // Check database connectivity
                var dbHealthy = await CheckDatabaseHealthAsync();
                if (!dbHealthy)
                {
                    _logger.LogWarning("Database health check failed");
                    return false;
                }

                // Check external services (if not mocked)
                var servicesHealthy = await CheckExternalServicesHealthAsync();
                if (!servicesHealthy)
                {
                    _logger.LogWarning("External services health check failed");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed");
                return false;
            }
        }

        /// <summary>
        /// Gets test environment information
        /// </summary>
        public TestEnvironmentInfo GetEnvironmentInfo()
        {
            return new TestEnvironmentInfo
            {
                IsInitialized = _initialized,
                DatabaseConnectionString = _configuration.GetConnectionString("SupabaseConnection"),
                UseInMemoryDatabase = _configuration.GetValue<bool>("TestSettings:UseInMemoryDatabase"),
                MockExternalServices = _configuration.GetValue<bool>("TestSettings:MockExternalServices"),
                EnableDetailedLogging = _configuration.GetValue<bool>("TestSettings:EnableDetailedLogging"),
                ParallelExecution = _configuration.GetValue<bool>("TestSettings:ParallelExecution"),
                TestTimeout = _configuration.GetValue<int>("TestSettings:TestTimeout"),
                Environment = _configuration["Environment"] ?? "Test",
            };
        }

        /// <summary>
        /// Validates the test configuration
        /// </summary>
        private void ValidateConfiguration()
        {
            _logger.LogInformation("Validating test configuration...");

            TestConfiguration.ValidateTestEnvironment(_configuration);

            // Additional validation
            var requiredSettings = new[]
            {
                "TestSettings:UseInMemoryDatabase",
                "TestSettings:MockExternalServices",
                "TestSettings:SeedTestData"
            };

            var missingSettings = requiredSettings
                .Where(setting => string.IsNullOrEmpty(_configuration[setting]))
                .ToList();

            if (missingSettings.Any())
            {
                throw new InvalidOperationException(
                    $"Missing test configuration settings: {string.Join(", ", missingSettings)}");
            }

            _logger.LogInformation("Test configuration validation completed");
        }

        /// <summary>
        /// Sets up mock services
        /// </summary>
        private async Task SetupMockServicesAsync()
        {
            if (!_configuration.GetValue<bool>("TestSettings:MockExternalServices"))
            {
                _logger.LogInformation("External services mocking is disabled");
                return;
            }

            _logger.LogInformation("Setting up mock services...");

            try
            {
                // Setup mock HTTP clients
                await SetupMockHttpClientsAsync();

                // Setup mock external APIs
                await SetupMockExternalApisAsync();

                _logger.LogInformation("Mock services setup completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to setup mock services");
                throw;
            }
        }

        /// <summary>
        /// Sets up mock HTTP clients
        /// </summary>
        private async Task SetupMockHttpClientsAsync()
        {
            // Mock HTTP clients for external services
            await Task.CompletedTask; // Placeholder for actual implementation
        }

        /// <summary>
        /// Sets up mock external APIs
        /// </summary>
        private async Task SetupMockExternalApisAsync()
        {
            // Mock external API responses
            await Task.CompletedTask; // Placeholder for actual implementation
        }

        /// <summary>
        /// Verifies external dependencies
        /// </summary>
        private async Task VerifyExternalDependenciesAsync()
        {
            _logger.LogInformation("Verifying external dependencies...");

            var dependencies = new List<string>();

            // Check if API should be available
            var apiUrl = _configuration["Api:BaseUrl"];
            if (!string.IsNullOrEmpty(apiUrl))
            {
                dependencies.Add($"API at {apiUrl}");
            }

            // Check database
            if (!_configuration.GetValue<bool>("TestSettings:UseInMemoryDatabase"))
            {
                dependencies.Add("PostgreSQL database");
            }

            foreach (var dependency in dependencies)
            {
                _logger.LogInformation("Checking dependency: {Dependency}", dependency);
            }

            _logger.LogInformation("External dependencies verification completed");
        }

        /// <summary>
        /// Checks database health
        /// </summary>
        private async Task<bool> CheckDatabaseHealthAsync()
        {
            try
            {
                // Simple database connectivity check
                using var connection = new Npgsql.NpgsqlConnection(
                    _configuration.GetConnectionString("SupabaseConnection"));
                
                await connection.OpenAsync();
                
                using var command = new Npgsql.NpgsqlCommand("SELECT 1", connection);
                var result = await command.ExecuteScalarAsync();
                
                return result != null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Database health check failed");
                return false;
            }
        }

        /// <summary>
        /// Checks external services health
        /// </summary>
        private async Task<bool> CheckExternalServicesHealthAsync()
        {
            if (_configuration.GetValue<bool>("TestSettings:MockExternalServices"))
            {
                // All external services are mocked, so they're always "healthy"
                return true;
            }

            try
            {
                // Check actual external services if not mocked
                // This would include Clerk, email services, etc.
                await Task.CompletedTask; // Placeholder
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "External services health check failed");
                return false;
            }
        }

        /// <summary>
        /// Cleans up mock services
        /// </summary>
        private async Task CleanupMockServicesAsync()
        {
            _logger.LogInformation("Cleaning up mock services...");
            await Task.CompletedTask; // Placeholder
        }

        /// <summary>
        /// Resets mock services
        /// </summary>
        private async Task ResetMockServicesAsync()
        {
            _logger.LogInformation("Resetting mock services...");
            await Task.CompletedTask; // Placeholder
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                foreach (var disposable in _disposables)
                {
                    disposable?.Dispose();
                }
                _disposables.Clear();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Test environment information
    /// </summary>
    public class TestEnvironmentInfo
    {
        public bool IsInitialized { get; set; }
        public string? DatabaseConnectionString { get; set; }
        public bool UseInMemoryDatabase { get; set; }
        public bool MockExternalServices { get; set; }
        public bool EnableDetailedLogging { get; set; }
        public bool ParallelExecution { get; set; }
        public int TestTimeout { get; set; }
        public string Environment { get; set; } = "Test";
    }
}
