using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace MicroSaasWebApi.Models.SoNoBrokers
{

    [Table("Advertiser", Schema = "public")]
    public class Advertiser
    {
        [Key]
        [Column("id")]
        public string Id { get; set; } = string.Empty;

        [Required]
        [Column("userId")]
        public string UserId { get; set; } = string.Empty;

        [Column("serviceProviderId")]
        public string? ServiceProviderId { get; set; }

        [Required]
        [Column("businessName")]
        public string BusinessName { get; set; } = string.Empty;

        [Column("contactName")]
        public string? ContactName { get; set; }

        [Required]
        [Column("email")]
        public string Email { get; set; } = string.Empty;

        [Column("phone")]
        public string? Phone { get; set; }

        [Column("website")]
        public string? Website { get; set; }

        [Column("description")]
        public string? Description { get; set; }

        [Required]
        [Column("serviceType")]
        public ServiceType ServiceType { get; set; }

        [Column("serviceAreas")]
        public string[] ServiceAreas { get; set; } = Array.Empty<string>();

        [Column("licenseNumber")]
        public string? LicenseNumber { get; set; }

        [Column("plan")]
        public AdvertiserPlan Plan { get; set; } = AdvertiserPlan.basic;

        [Column("status")]
        public AdvertiserStatus Status { get; set; } = AdvertiserStatus.pending;

        [Column("featuredUntil")]
        public DateTime? FeaturedUntil { get; set; }

        [Column("isPremium")]
        public bool IsPremium { get; set; } = false;

        [Column("isVerified")]
        public bool IsVerified { get; set; } = false;

        [Column("images")]
        public string[] Images { get; set; } = Array.Empty<string>();

        [Column("metadata")]
        public JsonDocument? Metadata { get; set; }

        [Column("createdAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("updatedAt")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public User? User { get; set; }
        public ServiceProvider? ServiceProvider { get; set; }
        public ICollection<AdvertiserSubscription> Subscriptions { get; set; } = new List<AdvertiserSubscription>();
    }
}
