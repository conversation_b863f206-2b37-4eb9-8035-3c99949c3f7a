using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Tests.Common;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Controllers
{
    public class AIPropertyControllerTests : IClassFixture<TestWebApplicationFactory<Program>>, IAsyncLifetime
    {
        private readonly TestWebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;
        private readonly ITestOutputHelper _output;

        public AIPropertyControllerTests(TestWebApplicationFactory<Program> factory, ITestOutputHelper output)
        {
            _factory = factory;
            _output = output;
            _client = _factory.CreateClient();
        }

        public async Task InitializeAsync()
        {
            await _factory.SeedTestDataAsync();
        }

        public async Task DisposeAsync()
        {
            await _factory.CleanupTestDataAsync();
        }

        [Fact]
        public async Task ImportPropertyData_WithValidAddress_ReturnsSuccess()
        {
            // Arrange
            var request = new AIPropertyImportRequest
            {
                Address = "123 Main Street, Toronto, ON"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/ai/property-import", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AIPropertyImportResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Status.Should().Be("success");
            result.Message.Should().Be("Property data imported successfully");
            result.PropertyDetails.Should().NotBeNull();
            result.PropertyDetails.Address.Should().Be(request.Address);
            result.PropertyDetails.PropertyType.Should().NotBeNullOrEmpty();
            result.PropertyDetails.Bedrooms.Should().BeGreaterThan(0);
            result.PropertyDetails.Bathrooms.Should().BeGreaterThan(0);
            result.PropertyDetails.SquareFeet.Should().BeGreaterThan(0);
        }

        [Fact]
        public async Task ImportPropertyData_WithEmptyAddress_ReturnsBadRequest()
        {
            // Arrange
            var request = new AIPropertyImportRequest
            {
                Address = ""
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/ai/property-import", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task GetPropertyValuation_WithValidRequest_ReturnsValuation()
        {
            // Arrange
            var request = new AIPropertyValuationRequest
            {
                Address = "456 Oak Avenue, Vancouver, BC",
                Country = "CA"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/ai/property-valuation", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AIPropertyValuationResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Status.Should().Be("success");
            result.Message.Should().Be("Property valuation completed successfully");
            
            // Verify valuation data
            result.Valuation.Should().NotBeNull();
            result.Valuation.EstimatedValue.Should().BeGreaterThan(0);
            result.Valuation.LowEstimate.Should().BeGreaterThan(0);
            result.Valuation.HighEstimate.Should().BeGreaterThan(0);
            result.Valuation.Currency.Should().Be("CAD");
            result.Valuation.PricePerSquareFoot.Should().BeGreaterThan(0);
            
            // Verify property info
            result.PropertyInfo.Should().NotBeNull();
            result.PropertyInfo.Address.Should().Be(request.Address);
            
            // Verify market analysis
            result.MarketAnalysis.Should().NotBeNull();
            result.MarketAnalysis.MedianPrice.Should().BeGreaterThan(0);
            
            // Verify comparable properties
            result.ComparableProperties.Should().NotBeNull();
            result.ComparableProperties.Should().HaveCount(3);
        }

        [Theory]
        [InlineData("CA", "CAD")]
        [InlineData("US", "USD")]
        [InlineData("UAE", "AED")]
        public async Task GetPropertyValuation_WithDifferentCountries_ReturnsCorrectCurrency(string country, string expectedCurrency)
        {
            // Arrange
            var request = new AIPropertyValuationRequest
            {
                Address = "123 Test Street",
                Country = country
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/ai/property-valuation", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AIPropertyValuationResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Valuation.Currency.Should().Be(expectedCurrency);
        }

        [Fact]
        public async Task GetPropertyValuation_WithMissingAddress_ReturnsBadRequest()
        {
            // Arrange
            var request = new AIPropertyValuationRequest
            {
                Address = "",
                Country = "CA"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/ai/property-valuation", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task GetPropertyValuation_WithMissingCountry_ReturnsBadRequest()
        {
            // Arrange
            var request = new AIPropertyValuationRequest
            {
                Address = "123 Test Street",
                Country = ""
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/ai/property-valuation", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task GenerateDescription_WithValidRequest_ReturnsDescription()
        {
            // Arrange
            var request = new GenerateDescriptionRequest
            {
                Prompt = "Generate a description for a beautiful 3-bedroom house with modern amenities",
                UserId = "test-user-123"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/ai/generate-description", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<GenerateDescriptionResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Status.Should().Be("success");
            result.Message.Should().Be("Description generated successfully");
            result.Description.Should().NotBeNullOrEmpty();
            result.Description.Length.Should().BeGreaterThan(10);
        }

        [Fact]
        public async Task GenerateDescription_WithEmptyPrompt_ReturnsBadRequest()
        {
            // Arrange
            var request = new GenerateDescriptionRequest
            {
                Prompt = "",
                UserId = "test-user-123"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/ai/generate-description", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task GenerateDescription_WithEmptyUserId_ReturnsBadRequest()
        {
            // Arrange
            var request = new GenerateDescriptionRequest
            {
                Prompt = "Generate a description",
                UserId = ""
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/ai/generate-description", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task ImportPropertyData_WithUrbanAddress_ReturnsDetailedData()
        {
            // Arrange
            var request = new AIPropertyImportRequest
            {
                Address = "100 King Street West, Toronto, ON"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/ai/property-import", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AIPropertyImportResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.PropertyDetails.Features.Should().NotBeEmpty();
            result.PropertyDetails.Amenities.Should().NotBeEmpty();
            result.PropertyDetails.Description.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task GetPropertyValuation_WithUrbanAddress_ReturnsHigherValues()
        {
            // Arrange
            var urbanRequest = new AIPropertyValuationRequest
            {
                Address = "123 Bay Street, Toronto, ON",
                Country = "CA"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/ai/property-valuation", urbanRequest);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AIPropertyValuationResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Valuation.EstimatedValue.Should().BeGreaterThan(0);
            result.MarketAnalysis.MarketCondition.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task AllAIEndpoints_ProcessingTime_CompletesWithinReasonableTime()
        {
            // Arrange
            var importRequest = new AIPropertyImportRequest { Address = "123 Test St" };
            var valuationRequest = new AIPropertyValuationRequest { Address = "123 Test St", Country = "CA" };
            var descriptionRequest = new GenerateDescriptionRequest { Prompt = "Test prompt", UserId = "test-user" };

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act
            var importTask = _client.PostAsJsonAsync("/api/sonobrokers/ai/property-import", importRequest);
            var valuationTask = _client.PostAsJsonAsync("/api/sonobrokers/ai/property-valuation", valuationRequest);
            var descriptionTask = _client.PostAsJsonAsync("/api/sonobrokers/ai/generate-description", descriptionRequest);

            await Task.WhenAll(importTask, valuationTask, descriptionTask);

            stopwatch.Stop();

            // Assert
            var importResponse = await importTask;
            var valuationResponse = await valuationTask;
            var descriptionResponse = await descriptionTask;

            importResponse.StatusCode.Should().Be(HttpStatusCode.OK);
            valuationResponse.StatusCode.Should().Be(HttpStatusCode.OK);
            descriptionResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // All requests should complete within 5 seconds (considering the mock delays)
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000);
        }

        [Theory]
        [InlineData("123 Main St, Toronto")]
        [InlineData("456 Oak Ave, Vancouver")]
        [InlineData("789 Pine Rd, Calgary")]
        public async Task ImportPropertyData_WithDifferentAddresses_ReturnsSuccess(string address)
        {
            // Arrange
            var request = new AIPropertyImportRequest { Address = address };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/ai/property-import", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AIPropertyImportResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.PropertyDetails.Address.Should().Be(address);
        }
    }
}
