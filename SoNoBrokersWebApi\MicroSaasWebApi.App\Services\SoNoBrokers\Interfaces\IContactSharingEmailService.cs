using MicroSaasWebApi.Models.SoNoBrokers.ContactSharing;

namespace MicroSaasWebApi.Services.SoNoBrokers.Interfaces
{
    /// <summary>
    /// Interface for contact sharing email service
    /// </summary>
    public interface IContactSharingEmailService
    {
        /// <summary>
        /// Send contact sharing email to seller
        /// </summary>
        Task<bool> SendContactShareEmailAsync(ContactShareResponse contactShare);

        /// <summary>
        /// Send property offer email to seller
        /// </summary>
        Task<bool> SendPropertyOfferEmailAsync(ContactShareResponse contactShare);

        /// <summary>
        /// Send visit scheduling email to seller
        /// </summary>
        Task<bool> SendVisitSchedulingEmailAsync(ContactShareResponse contactShare);

        /// <summary>
        /// Send combined offer and visit email to seller
        /// </summary>
        Task<bool> SendOfferWithVisitEmailAsync(ContactShareResponse contactShare);

        /// <summary>
        /// Send confirmation email to buyer
        /// </summary>
        Task<bool> SendBuyerConfirmationEmailAsync(ContactShareResponse contactShare);

        /// <summary>
        /// Send seller response email to buyer
        /// </summary>
        Task<bool> SendSellerResponseEmailAsync(ContactShareResponse contactShare, string sellerResponse);

        /// <summary>
        /// Send reminder email to seller for pending requests
        /// </summary>
        Task<bool> SendSellerReminderEmailAsync(ContactShareResponse contactShare);

        /// <summary>
        /// Generate email tracking ID
        /// </summary>
        string GenerateEmailTrackingId();

        /// <summary>
        /// Track email open
        /// </summary>
        Task TrackEmailOpenAsync(string trackingId);

        /// <summary>
        /// Track email click
        /// </summary>
        Task TrackEmailClickAsync(string trackingId, string linkType);
    }
}
