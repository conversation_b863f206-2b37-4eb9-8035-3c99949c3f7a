import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { Country } from '@/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Settings,
  Users,
  Database,
  Mail,
  BarChart3,
  Shield,
  Globe,
  Server,
  AlertTriangle
} from 'lucide-react'

interface PageProps {
  params: Promise<{
    country: string
  }>
}

export default async function AdminSettingsPage({ params }: PageProps) {
  const { userId } = await auth()

  if (!userId) {
    redirect('/sign-in')
  }

  const resolvedParams = await params

  // Validate country
  const validCountries = Object.values(Country)
  const countryParam = resolvedParams.country.toLowerCase()
  const countryEnum = countryParam.toUpperCase() as Country

  if (!validCountries.includes(countryEnum)) {
    redirect('/ca/admin/settings')
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="mb-8">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-6 w-6 text-primary" />
              <h1 className="text-3xl font-bold">Admin Settings</h1>
              <Badge variant="destructive">Admin Only</Badge>
            </div>
            <p className="text-muted-foreground">
              System administration and configuration for {countryEnum === 'CA' ? 'Canada' : 'United States'}.
            </p>
          </div>

          <div className="grid gap-6">
            {/* User Management */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  <CardTitle>User Management</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Manage Users</h4>
                    <p className="text-sm text-muted-foreground">
                      View, edit, and manage user accounts and roles
                    </p>
                  </div>
                  <Button>Manage Users</Button>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Role Assignment</h4>
                    <p className="text-sm text-muted-foreground">
                      Assign roles and permissions to users
                    </p>
                  </div>
                  <Button variant="outline">Assign Roles</Button>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Bulk Operations</h4>
                    <p className="text-sm text-muted-foreground">
                      Perform bulk actions on user accounts
                    </p>
                  </div>
                  <Button variant="outline">Bulk Actions</Button>
                </div>
              </CardContent>
            </Card>

            {/* System Configuration */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  <CardTitle>System Configuration</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Application Settings</h4>
                    <p className="text-sm text-muted-foreground">
                      Configure global application settings
                    </p>
                  </div>
                  <Button variant="outline">Configure</Button>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Feature Flags</h4>
                    <p className="text-sm text-muted-foreground">
                      Enable or disable application features
                    </p>
                  </div>
                  <Button variant="outline">Manage Flags</Button>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">API Configuration</h4>
                    <p className="text-sm text-muted-foreground">
                      Configure external API integrations
                    </p>
                  </div>
                  <Button variant="outline">Configure APIs</Button>
                </div>
              </CardContent>
            </Card>

            {/* Database Management */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  <CardTitle>Database Management</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Database Health</h4>
                    <p className="text-sm text-muted-foreground">
                      Monitor database performance and health
                    </p>
                  </div>
                  <Button variant="outline">View Status</Button>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Backup Management</h4>
                    <p className="text-sm text-muted-foreground">
                      Schedule and manage database backups
                    </p>
                  </div>
                  <Button variant="outline">Manage Backups</Button>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Data Migration</h4>
                    <p className="text-sm text-muted-foreground">
                      Run database migrations and updates
                    </p>
                  </div>
                  <Button variant="outline">Run Migrations</Button>
                </div>
              </CardContent>
            </Card>

            {/* Communication */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Mail className="h-5 w-5" />
                  <CardTitle>Communication</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Bulk Email</h4>
                    <p className="text-sm text-muted-foreground">
                      Send bulk emails to users
                    </p>
                  </div>
                  <Button variant="outline">Send Email</Button>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Notifications</h4>
                    <p className="text-sm text-muted-foreground">
                      Manage system notifications and alerts
                    </p>
                  </div>
                  <Button variant="outline">Manage Notifications</Button>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Email Templates</h4>
                    <p className="text-sm text-muted-foreground">
                      Create and manage email templates
                    </p>
                  </div>
                  <Button variant="outline">Manage Templates</Button>
                </div>
              </CardContent>
            </Card>

            {/* Analytics & Reporting */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  <CardTitle>Analytics & Reporting</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">System Analytics</h4>
                    <p className="text-sm text-muted-foreground">
                      View comprehensive system analytics
                    </p>
                  </div>
                  <Button variant="outline">View Analytics</Button>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">User Reports</h4>
                    <p className="text-sm text-muted-foreground">
                      Generate detailed user activity reports
                    </p>
                  </div>
                  <Button variant="outline">Generate Reports</Button>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Performance Metrics</h4>
                    <p className="text-sm text-muted-foreground">
                      Monitor application performance metrics
                    </p>
                  </div>
                  <Button variant="outline">View Metrics</Button>
                </div>
              </CardContent>
            </Card>

            {/* System Status */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Server className="h-5 w-5" />
                  <CardTitle>System Status</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-green-600">99.9%</div>
                    <div className="text-sm text-muted-foreground">Uptime</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">1,234</div>
                    <div className="text-sm text-muted-foreground">Active Users</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">5.6GB</div>
                    <div className="text-sm text-muted-foreground">Database Size</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Danger Zone */}
            <Card className="border-destructive">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-destructive" />
                  <CardTitle className="text-destructive">Danger Zone</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">System Maintenance</h4>
                    <p className="text-sm text-muted-foreground">
                      Put the system in maintenance mode
                    </p>
                  </div>
                  <Button variant="destructive" size="sm">Enable Maintenance</Button>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Clear Cache</h4>
                    <p className="text-sm text-muted-foreground">
                      Clear all system caches (use with caution)
                    </p>
                  </div>
                  <Button variant="destructive" size="sm">Clear Cache</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
