import { Inter, Outfit } from 'next/font/google'
import { ClerkProvider } from '@clerk/nextjs'

const inter = Inter({ subsets: ['latin'] })
const outfit = Outfit({ 
  subsets: ['latin'],
  variable: '--font-outfit'
})

interface StandaloneLayoutProps {
  children: React.ReactNode
  className?: string
}

/**
 * Standalone layout for pages that don't need header/footer
 * Used by: waiting-list, unsupported-region, launch pages
 */
export function StandaloneLayout({ children, className = '' }: StandaloneLayoutProps) {
  return (
    <html lang="en" className={`${inter.className} ${outfit.variable}`}>
      <body className={`min-h-screen bg-background font-sans antialiased ${className}`}>
        <ClerkProvider>
          <main className="min-h-screen">
            {children}
          </main>
        </ClerkProvider>
      </body>
    </html>
  )
}

/**
 * Standalone layout component for use in layout.tsx files
 */
export default function StandaloneLayoutWrapper({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <StandaloneLayout>
      {children}
    </StandaloneLayout>
  )
}
