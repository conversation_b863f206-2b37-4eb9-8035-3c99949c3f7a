import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function FirstTimeBuyerPage({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us', 'uae']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/resources/first-time-buyer')
  }

  const userType = resolvedSearchParams.userType || 'buyer'

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8">
            First-Time Buyer Program
          </h1>

          <div className="bg-gradient-to-r from-primary/10 to-blue-500/10 rounded-lg p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-4">Welcome to Homeownership!</h2>
            <p className="text-muted-foreground mb-6">
              Take advantage of exclusive programs and incentives designed specifically for first-time home buyers in {country.toUpperCase()}.
              We'll guide you through every step of your homeownership journey.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <div className="bg-card rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Exclusive Benefits</h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <div>
                    <div className="font-semibold">Lower Down Payments</div>
                    <div className="text-sm text-muted-foreground">As low as 3% down payment options</div>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <div>
                    <div className="font-semibold">Reduced Interest Rates</div>
                    <div className="text-sm text-muted-foreground">Special rates for qualified first-time buyers</div>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <div>
                    <div className="font-semibold">Tax Credits & Rebates</div>
                    <div className="text-sm text-muted-foreground">Government incentives and tax benefits</div>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <div>
                    <div className="font-semibold">Flexible Qualification</div>
                    <div className="text-sm text-muted-foreground">Alternative income verification options</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Qualification Requirements</h3>
              <div className="space-y-3">
                <div className="p-3 bg-muted rounded-lg">
                  <div className="font-semibold text-sm">First-Time Buyer Status</div>
                  <div className="text-xs text-muted-foreground">Haven't owned a home in the past 3-4 years</div>
                </div>

                <div className="p-3 bg-muted rounded-lg">
                  <div className="font-semibold text-sm">Income Limits</div>
                  <div className="text-xs text-muted-foreground">Varies by region and program</div>
                </div>

                <div className="p-3 bg-muted rounded-lg">
                  <div className="font-semibold text-sm">Credit Score</div>
                  <div className="text-xs text-muted-foreground">Minimum 580-620 depending on program</div>
                </div>

                <div className="p-3 bg-muted rounded-lg">
                  <div className="font-semibold text-sm">Primary Residence</div>
                  <div className="text-xs text-muted-foreground">Must be your primary home</div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6 mb-8">
            <h3 className="text-xl font-semibold mb-4">Available Programs</h3>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="border border-border rounded-lg p-4">
                <h4 className="font-semibold mb-2">FHA/CMHC Program</h4>
                <div className="space-y-1 text-sm">
                  <div>• 3.5% down payment</div>
                  <div>• Flexible credit requirements</div>
                  <div>• Mortgage insurance included</div>
                </div>
              </div>

              <div className="border border-border rounded-lg p-4">
                <h4 className="font-semibold mb-2">VA/Military Program</h4>
                <div className="space-y-1 text-sm">
                  <div>• 0% down payment</div>
                  <div>• No mortgage insurance</div>
                  <div>• Competitive rates</div>
                </div>
              </div>

              <div className="border border-border rounded-lg p-4">
                <h4 className="font-semibold mb-2">State/Provincial</h4>
                <div className="space-y-1 text-sm">
                  <div>• Down payment assistance</div>
                  <div>• Closing cost help</div>
                  <div>• Local incentives</div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6 mb-8">
            <h3 className="text-xl font-semibold mb-4">Your First-Time Buyer Journey</h3>
            <div className="space-y-4">
              <div className="flex gap-4">
                <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold">1</div>
                <div>
                  <h4 className="font-semibold">Get Pre-Qualified</h4>
                  <p className="text-sm text-muted-foreground">
                    Understand your budget and explore program options.
                  </p>
                </div>
              </div>

              <div className="flex gap-4">
                <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold">2</div>
                <div>
                  <h4 className="font-semibold">Complete Homebuyer Education</h4>
                  <p className="text-sm text-muted-foreground">
                    Required course for many first-time buyer programs.
                  </p>
                </div>
              </div>

              <div className="flex gap-4">
                <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold">3</div>
                <div>
                  <h4 className="font-semibold">Find Your Home</h4>
                  <p className="text-sm text-muted-foreground">
                    Search within program price limits and guidelines.
                  </p>
                </div>
              </div>

              <div className="flex gap-4">
                <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold">4</div>
                <div>
                  <h4 className="font-semibold">Apply for Benefits</h4>
                  <p className="text-sm text-muted-foreground">
                    Submit applications for all eligible programs.
                  </p>
                </div>
              </div>

              <div className="flex gap-4">
                <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold">5</div>
                <div>
                  <h4 className="font-semibold">Close on Your Home</h4>
                  <p className="text-sm text-muted-foreground">
                    Complete the purchase with your program benefits.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4">Ready to Get Started?</h3>
            <p className="text-muted-foreground mb-4">
              Connect with our first-time buyer specialists to explore your options and start your homeownership journey.
            </p>
            <div className="flex gap-4">
              <button className="bg-primary text-primary-foreground px-6 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors">
                Schedule Consultation
              </button>
              <button className="border border-border px-6 py-3 rounded-lg font-semibold hover:bg-muted transition-colors">
                Download Guide
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'buyer'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `First-Time Home Buyer Program for ${country} | SoNoBrokers`,
    description: `Special programs and incentives for first-time home buyers in ${country}. Lower down payments, reduced rates, and expert guidance.`,
    keywords: `first time buyer, home buyer programs, down payment assistance, ${country}, homeownership`,
  }
}
