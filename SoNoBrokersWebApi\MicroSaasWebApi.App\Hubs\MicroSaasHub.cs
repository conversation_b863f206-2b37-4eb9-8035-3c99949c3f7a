using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace MicroSaasWebApi.Hubs
{
    /// <summary>
    /// SignalR hub for real-time communication in MicroSaaS application
    /// </summary>
    [Authorize]
    public class MicroSaasHub : Hub
    {
        private readonly ILogger<MicroSaasHub> _logger;

        public MicroSaasHub(ILogger<MicroSaasHub> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Handle client connection
        /// </summary>
        public override async Task OnConnectedAsync()
        {
            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var userEmail = Context.User?.FindFirst(ClaimTypes.Email)?.Value;

            _logger.LogInformation("User connected: {UserId} ({Email}) - Connection: {ConnectionId}",
                userId, userEmail, Context.ConnectionId);

            // Add user to their personal group
            if (!string.IsNullOrEmpty(userId))
            {
                await Groups.AddToGroupAsync(Context.ConnectionId, $"user_{userId}");
            }

            // Notify user of successful connection
            await Clients.Caller.SendAsync("Connected", new
            {
                ConnectionId = Context.ConnectionId,
                Message = "Successfully connected to MicroSaaS Hub"
            });

            await base.OnConnectedAsync();
        }

        /// <summary>
        /// Handle client disconnection
        /// </summary>
        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            _logger.LogInformation("User disconnected: {UserId} - Connection: {ConnectionId}",
                userId, Context.ConnectionId);

            if (exception != null)
            {
                _logger.LogError(exception, "User disconnected with error: {UserId}", userId);
            }

            await base.OnDisconnectedAsync(exception);
        }

        /// <summary>
        /// Join a specific group (e.g., organization, project)
        /// </summary>
        public async Task JoinGroup(string groupName)
        {
            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            // Validate group access (implement your authorization logic here)
            if (await CanJoinGroup(userId, groupName))
            {
                await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
                await Clients.Caller.SendAsync("JoinedGroup", groupName);

                _logger.LogInformation("User {UserId} joined group: {GroupName}", userId, groupName);
            }
            else
            {
                await Clients.Caller.SendAsync("Error", "Access denied to group: " + groupName);
            }
        }

        /// <summary>
        /// Leave a specific group
        /// </summary>
        public async Task LeaveGroup(string groupName)
        {
            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
            await Clients.Caller.SendAsync("LeftGroup", groupName);

            _logger.LogInformation("User {UserId} left group: {GroupName}", userId, groupName);
        }

        /// <summary>
        /// Send a message to a specific group
        /// </summary>
        public async Task SendMessageToGroup(string groupName, string message)
        {
            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var userEmail = Context.User?.FindFirst(ClaimTypes.Email)?.Value;

            // Validate user can send to this group
            if (await CanSendToGroup(userId, groupName))
            {
                await Clients.Group(groupName).SendAsync("ReceiveMessage", new
                {
                    UserId = userId,
                    UserEmail = userEmail,
                    Message = message,
                    Timestamp = DateTime.UtcNow,
                    GroupName = groupName
                });

                _logger.LogInformation("User {UserId} sent message to group {GroupName}", userId, groupName);
            }
            else
            {
                await Clients.Caller.SendAsync("Error", "Cannot send message to group: " + groupName);
            }
        }

        /// <summary>
        /// Send a private message to a specific user
        /// </summary>
        public async Task SendPrivateMessage(string targetUserId, string message)
        {
            var senderId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var senderEmail = Context.User?.FindFirst(ClaimTypes.Email)?.Value;

            await Clients.Group($"user_{targetUserId}").SendAsync("ReceivePrivateMessage", new
            {
                SenderId = senderId,
                SenderEmail = senderEmail,
                Message = message,
                Timestamp = DateTime.UtcNow
            });

            _logger.LogInformation("User {SenderId} sent private message to {TargetUserId}", senderId, targetUserId);
        }

        /// <summary>
        /// Broadcast system notification to all connected users
        /// </summary>
        public async Task BroadcastSystemNotification(string message, string type = "info")
        {
            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            // Only allow admin users to broadcast system notifications
            if (await IsAdminUser(userId))
            {
                await Clients.All.SendAsync("SystemNotification", new
                {
                    Message = message,
                    Type = type,
                    Timestamp = DateTime.UtcNow
                });

                _logger.LogInformation("Admin {UserId} broadcasted system notification", userId);
            }
            else
            {
                await Clients.Caller.SendAsync("Error", "Insufficient permissions for system broadcast");
            }
        }

        /// <summary>
        /// Send payment status update to user
        /// </summary>
        public async Task SendPaymentUpdate(string userId, object paymentData)
        {
            await Clients.Group($"user_{userId}").SendAsync("PaymentUpdate", paymentData);
        }

        /// <summary>
        /// Send subscription status update to user
        /// </summary>
        public async Task SendSubscriptionUpdate(string userId, object subscriptionData)
        {
            await Clients.Group($"user_{userId}").SendAsync("SubscriptionUpdate", subscriptionData);
        }

        /// <summary>
        /// Send real-time analytics update to admin users
        /// </summary>
        public async Task SendAnalyticsUpdate(object analyticsData)
        {
            await Clients.Group("admins").SendAsync("AnalyticsUpdate", analyticsData);
        }

        #region Private Helper Methods

        private Task<bool> CanJoinGroup(string? userId, string groupName)
        {
            // Implement your group access authorization logic here
            // For example, check if user belongs to organization, has proper role, etc.

            if (string.IsNullOrEmpty(userId))
                return Task.FromResult(false);

            // Basic validation - you should implement proper authorization
            return Task.FromResult(!string.IsNullOrEmpty(groupName) && groupName.Length > 0);
        }

        private async Task<bool> CanSendToGroup(string? userId, string groupName)
        {
            // Implement your message sending authorization logic here
            return await CanJoinGroup(userId, groupName);
        }

        private Task<bool> IsAdminUser(string? userId)
        {
            // Implement admin user check
            // Check user roles, permissions, etc.

            if (string.IsNullOrEmpty(userId))
                return Task.FromResult(false);

            var userRoles = Context.User?.FindAll(ClaimTypes.Role)?.Select(c => c.Value);
            return Task.FromResult(userRoles?.Contains("Admin") == true);
        }

        #endregion
    }
}
