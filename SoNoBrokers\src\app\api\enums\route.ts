import { NextResponse } from 'next/server';
// MIGRATED: Use enum-actions instead of direct service
import { getEnumValuesAction } from '@/lib/actions/enum-actions';

export async function GET() {
  try {
    const result = await getEnumValuesAction();

    if (!result.success) {
      throw new Error(result.error || 'Failed to fetch enum values');
    }

    return NextResponse.json(result.data, {
      headers: {
        'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600', // Cache for 5 minutes
      },
    });
  } catch (error) {
    console.error('Failed to fetch enum values:', error);

    return NextResponse.json(
      { error: 'Unable to load application configuration from database' },
      { status: 500 }
    );
  }
}
