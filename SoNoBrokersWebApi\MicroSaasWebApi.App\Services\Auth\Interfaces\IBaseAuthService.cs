using MicroSaasWebApi.Models.Auth.Clerk;

namespace MicroSaasWebApi.Services.Auth.Interfaces
{
    public interface IBaseAuthService
    {
        Task<AuthResponse> RegisterAsync(RegisterRequest request);
        Task<AuthResponse> LoginAsync(LoginRequest request);
        Task<AuthResponse> RefreshTokenAsync(string refreshToken);
        Task LogoutAsync(string userId);
        Task RequestPasswordResetAsync(string email);
        Task<bool> VerifyEmailAsync(string token);
        Task<bool> UpdatePasswordAsync(string userId, string currentPassword, string newPassword);
    }
}
