using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace MicroSaasWebApi.Health
{
    /// <summary>
    /// Health Check Configuration for .NET 9 Web API
    /// Following best practices from: https://medium.com/@jeslu<PERSON>hman/implementing-health-checks-in-net-8-c3ba10af83c3
    /// Adapted for PostgreSQL/Supabase instead of SQL Server
    /// </summary>
    public static class HealthCheckConfiguration
    {
        /// <summary>
        /// Configure comprehensive health checks for the MicroSaaS Web API
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="configuration">Application configuration</param>
        public static void ConfigureHealthChecks(this IServiceCollection services, IConfiguration configuration)
        {
            // Get connection string for Supabase PostgreSQL
            var connectionString = configuration.GetConnectionString("DefaultConnection")
                ?? throw new InvalidOperationException("DefaultConnection string is not configured");

            // Configure health checks
            services.AddHealthChecks()
                // 1. PostgreSQL/Supabase Database Health Check
                .AddNpgSql(
                    connectionString: connectionString,
                    healthQuery: "SELECT 1;", // Simple query to test database connectivity
                    name: "PostgreSQL Database (Supabase)",
                    failureStatus: HealthStatus.Unhealthy,
                    tags: new[] { "MicroSaaS", "Database", "PostgreSQL", "Supabase" })

                // 2. Custom Remote Endpoints Health Check
                .AddCheck<RemoteEndpointHealthCheck>(
                    "Remote Endpoints Health Check",
                    failureStatus: HealthStatus.Unhealthy,
                    tags: new[] { "MicroSaaS", "External" })

                // 3. Memory Health Check
                .AddCheck<MemoryHealthCheck>(
                    "MicroSaaS Memory Health Check",
                    failureStatus: HealthStatus.Unhealthy,
                    tags: new[] { "MicroSaaS", "Memory" })

                // 4. Application URL Health Check (self-check) - Use HTTPS for production
                .AddUrlGroup(
                    uri: new Uri($"{GetHealthCheckUrl(configuration)}/api/sonobrokers/test/ping"),
                    name: "Application Base URL",
                    failureStatus: HealthStatus.Unhealthy,
                    tags: new[] { "MicroSaaS", "Self-Check" });

            // Configure Health Checks UI
            services.AddHealthChecksUI(options =>
            {
                options.SetEvaluationTimeInSeconds(30); // Check every 30 seconds
                options.MaximumHistoryEntriesPerEndpoint(100); // Keep 100 history entries
                options.SetApiMaxActiveRequests(2); // Allow 2 concurrent requests

                // Use appropriate URL based on environment
                var healthCheckUrl = GetHealthCheckUrl(configuration);
                options.AddHealthCheckEndpoint("MicroSaaS API", $"{healthCheckUrl}/api/health"); // Map health check endpoint
                options.SetMinimumSecondsBetweenFailureNotifications(60); // Throttle notifications
            })
            .AddInMemoryStorage(); // Use in-memory storage for health check history

            // Register custom health check dependencies
            services.Configure<MemoryCheckOptions>(configuration.GetSection("HealthChecks:Memory"));

            // Configure HttpClient for health checks with SSL certificate bypass in development
            services.AddHttpClient("HealthCheck", client =>
            {
                client.Timeout = TimeSpan.FromSeconds(10);
            })
            .ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();

                // In development, ignore SSL certificate errors
                if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development")
                {
                    handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
                }

                return handler;
            });

            services.AddHttpClient(); // Required for RemoteEndpointHealthCheck
        }

        /// <summary>
        /// Configure health check middleware and endpoints
        /// </summary>
        /// <param name="app">Web application</param>
        public static void UseHealthCheckMiddleware(this WebApplication app)
        {
            // Map health check endpoints
            app.MapHealthChecks("/api/health", new HealthCheckOptions
            {
                Predicate = _ => true, // Include all health checks
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse, // Use UI response writer
                ResultStatusCodes =
                {
                    [HealthStatus.Healthy] = StatusCodes.Status200OK,
                    [HealthStatus.Degraded] = StatusCodes.Status200OK,
                    [HealthStatus.Unhealthy] = StatusCodes.Status503ServiceUnavailable
                }
            });

            // Map specific health check endpoints for granular monitoring
            app.MapHealthChecks("/api/health/ready", new HealthCheckOptions
            {
                Predicate = check => check.Tags.Contains("ready"),
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
            });

            app.MapHealthChecks("/api/health/live", new HealthCheckOptions
            {
                Predicate = check => check.Tags.Contains("live"),
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
            });

            // Simple ping endpoint for basic availability check
            app.MapGet("/api/health/ping", () => Results.Ok(new
            {
                Status = "Healthy",
                Timestamp = DateTime.UtcNow,
                Service = "MicroSaaS Web API"
            }));

            // Configure Health Checks UI
            if (app.Environment.IsDevelopment() || app.Environment.IsStaging())
            {
                app.UseHealthChecksUI(options =>
                {
                    options.UIPath = "/health-ui"; // Health check dashboard
                    options.ApiPath = "/health-api"; // Health check API endpoint
                    options.UseRelativeApiPath = false;
                    options.UseRelativeResourcesPath = false;
                    options.AddCustomStylesheet("./Health/healthcheck-custom.css");
                });
            }
        }

        /// <summary>
        /// Get the appropriate health check URL based on environment
        /// </summary>
        /// <param name="configuration">Configuration</param>
        /// <returns>Health check URL</returns>
        private static string GetHealthCheckUrl(IConfiguration configuration)
        {
            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

            // In development, use HTTP port to avoid SSL certificate issues
            if (environment == "Development")
            {
                return "http://localhost:5005";
            }

            // For Docker and production environments, use HTTPS
            return configuration["ApplicationUrl"] ?? "https://localhost:7164";
        }
    }
}
