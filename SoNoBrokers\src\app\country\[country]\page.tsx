import { redirect } from 'next/navigation'
import { Metadata } from 'next'
import { Country } from '@/lib/geo'
import { getSupportedCountries } from '@/lib/geo'

interface CountryPageProps {
  params: Promise<{
    country: string
  }>
}

export async function generateMetadata({ params }: CountryPageProps): Promise<Metadata> {
  const resolvedParams = await params
  const country = resolvedParams.country.toUpperCase()
  const countryName = country === 'CA' ? 'Canada' : country === 'US' ? 'United States' : 'Unknown'

  return {
    title: `SoNo Brokers - ${countryName}`,
    description: `Find your perfect home in ${countryName} with SoNo Brokers`,
  }
}

export default async function CountryPage({ params }: CountryPageProps) {
  const resolvedParams = await params
  const country = resolvedParams.country.toUpperCase()

  // Validate country against database enum
  try {
    const supportedCountries = await getSupportedCountries()
    if (!supportedCountries.includes(country)) {
      redirect(`/unsupported-region?country=${country}`)
    }
  } catch (error) {
    console.error('Failed to validate country:', error)
    redirect(`/unsupported-region?country=${country}`)
  }

  // Redirect to home page for now - this is where country-specific content would go
  redirect('/')
}
