import React from 'react'
import { ServiceLayout } from '@/components/shared/services/ServiceLayout'

interface ProfessionalAppraisalServiceProps {
  userType: 'buyer' | 'seller'
  isSignedIn: boolean
  country: string
}

// Mock data for professional appraisers
function getMockAppraisers(country: string) {
  const baseAppraisers = [
    {
      id: 'appr-001',
      name: '<PERSON>',
      businessName: 'Thompson Property Appraisals',
      serviceType: 'Certified Property Appraiser',
      location: country === 'CA' ? 'Toronto, ON' : country === 'US' ? 'New York, NY' : 'Dubai, UAE',
      distance: '2.3 km',
      rating: 4.9,
      reviewCount: 127,
      price: country === 'CA' ? '$450-650' : country === 'US' ? '$350-550' : 'AED 1,200-1,800',
      specialties: ['Residential Appraisal', 'Commercial Properties', 'Estate Valuations'],
      verified: true,
      isAdvertiser: true,
      isPremium: true,
      image: '/api/placeholder/300/200',
      phone: country === 'CA' ? '+****************' : country === 'US' ? '+****************' : '+971 4 555 0123',
      email: '<EMAIL>',
      website: 'https://thompsonappraisals.com',
      description: 'Certified property appraiser with 15+ years experience in residential and commercial valuations.',
      coordinates: { lat: 43.6532, lng: -79.3832 },
      advertiser: {
        plan: 'premium' as const,
        status: 'active' as const,
        featuredUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        isPremium: true
      }
    },
    {
      id: 'appr-002',
      name: 'Sarah Chen',
      businessName: 'Precision Appraisal Services',
      serviceType: 'Licensed Real Estate Appraiser',
      location: country === 'CA' ? 'Mississauga, ON' : country === 'US' ? 'Los Angeles, CA' : 'Abu Dhabi, UAE',
      distance: '5.1 km',
      rating: 4.8,
      reviewCount: 89,
      price: country === 'CA' ? '$400-600' : country === 'US' ? '$300-500' : 'AED 1,000-1,600',
      specialties: ['Residential Properties', 'Condo Valuations', 'Investment Properties'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/300/200',
      phone: country === 'CA' ? '+****************' : country === 'US' ? '+****************' : '+971 2 555 0456',
      email: '<EMAIL>',
      website: 'https://precisionappraisal.com',
      description: 'Specialized in residential property appraisals with focus on accuracy and quick turnaround.',
      coordinates: { lat: 43.5890, lng: -79.6441 },
      advertiser: {
        plan: 'basic' as const,
        status: 'active' as const,
        isPremium: false
      }
    },
    {
      id: 'appr-003',
      name: 'David Rodriguez',
      businessName: 'Elite Property Valuations',
      serviceType: 'Senior Property Appraiser',
      location: country === 'CA' ? 'Vaughan, ON' : country === 'US' ? 'Chicago, IL' : 'Sharjah, UAE',
      distance: '8.7 km',
      rating: 4.7,
      reviewCount: 156,
      price: country === 'CA' ? '$500-750' : country === 'US' ? '$400-650' : 'AED 1,400-2,000',
      specialties: ['Luxury Properties', 'Commercial Appraisals', 'Estate Planning'],
      verified: true,
      isAdvertiser: true,
      isPremium: true,
      image: '/api/placeholder/300/200',
      phone: country === 'CA' ? '+****************' : country === 'US' ? '+****************' : '+971 6 555 0789',
      email: '<EMAIL>',
      website: 'https://elitevaluations.com',
      description: 'Senior appraiser specializing in high-value residential and commercial properties.',
      coordinates: { lat: 43.8361, lng: -79.4985 },
      advertiser: {
        plan: 'premium' as const,
        status: 'active' as const,
        featuredUntil: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000),
        isPremium: true
      }
    },
    {
      id: 'appr-004',
      name: 'Jennifer Walsh',
      businessName: 'Walsh Appraisal Group',
      serviceType: 'Certified Residential Appraiser',
      location: country === 'CA' ? 'Oakville, ON' : country === 'US' ? 'Miami, FL' : 'Ajman, UAE',
      distance: '12.4 km',
      rating: 4.6,
      reviewCount: 73,
      price: country === 'CA' ? '$425-575' : country === 'US' ? '$325-475' : 'AED 1,100-1,500',
      specialties: ['Residential Homes', 'Townhouses', 'Mortgage Appraisals'],
      verified: true,
      isAdvertiser: false,
      isPremium: false,
      image: '/api/placeholder/300/200',
      phone: country === 'CA' ? '+****************' : country === 'US' ? '+****************' : '+971 6 555 0321',
      email: '<EMAIL>',
      description: 'Experienced residential appraiser with expertise in suburban properties.',
      coordinates: { lat: 43.4675, lng: -79.6877 }
    },
    {
      id: 'appr-005',
      name: 'Robert Kim',
      businessName: 'Metro Appraisal Solutions',
      serviceType: 'Commercial Property Appraiser',
      location: country === 'CA' ? 'Markham, ON' : country === 'US' ? 'Seattle, WA' : 'Ras Al Khaimah, UAE',
      distance: '15.2 km',
      rating: 4.8,
      reviewCount: 94,
      price: country === 'CA' ? '$600-900' : country === 'US' ? '$500-800' : 'AED 1,800-2,500',
      specialties: ['Commercial Buildings', 'Industrial Properties', 'Investment Analysis'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/300/200',
      phone: country === 'CA' ? '+****************' : country === 'US' ? '+****************' : '+971 7 555 0654',
      email: '<EMAIL>',
      website: 'https://metroappraisal.com',
      description: 'Specialized in commercial and industrial property valuations with 20+ years experience.',
      coordinates: { lat: 43.8561, lng: -79.3370 },
      advertiser: {
        plan: 'basic' as const,
        status: 'active' as const,
        isPremium: false
      }
    }
  ]

  return baseAppraisers
}

export function ProfessionalAppraisalService({
  userType,
  isSignedIn,
  country
}: ProfessionalAppraisalServiceProps) {
  // Get providers data
  const mockAppraisers = getMockAppraisers(country)

  // Sort by distance and premium status
  const appraisers = mockAppraisers.sort((a, b) => {
    if (a.isPremium && !b.isPremium) return -1
    if (!a.isPremium && b.isPremium) return 1
    if (a.isAdvertiser && !b.isAdvertiser) return -1
    if (!a.isAdvertiser && b.isAdvertiser) return 1
    return parseFloat(a.distance) - parseFloat(b.distance)
  })

  const serviceDescription = userType === 'seller'
    ? 'Professional property appraisal services for accurate market valuations. Our certified appraisers provide comprehensive reports for selling, refinancing, estate planning, and legal purposes.'
    : 'Professional property appraisal services for informed buying decisions. Get independent valuations to ensure fair pricing and secure financing for your property purchase.'

  // Check environment variable for Google providers
  const showGoogleProviders = process.env.NEXT_PUBLIC_ENABLE_GOOGLE_PROVIDERS !== 'false'

  return (
    <ServiceLayout
      userType={userType}
      isSignedIn={isSignedIn}
      serviceTitle="Professional Appraisal Services"
      serviceDescription={serviceDescription}
      country={country}
      providers={appraisers}
      showGoogleProviders={showGoogleProviders}
    />
  )
}
