using Dapper;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using System.Text.Json;

namespace MicroSaasWebApi.Services.SoNoBrokers
{
    public class AdvertiserService : IAdvertiserService
    {
        private readonly MicroSaasWebApi.App.Context.IDapperDbContext _dbContext;
        private readonly ILogger<AdvertiserService> _logger;

        public AdvertiserService(MicroSaasWebApi.App.Context.IDapperDbContext dbContext, ILogger<AdvertiserService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<AdvertiserSearchResponse> GetAdvertisersAsync(AdvertiserSearchRequest request)
        {
            try
            {
                var sql = @"
                    SELECT a.*, u.full_name as user_name, u.avatar_url as user_avatar
                    FROM public.""Advertiser"" a
                    LEFT JOIN public.""User"" u ON a.""userId"" = u.id
                    WHERE 1=1";

                var parameters = new DynamicParameters();
                var conditions = new List<string>();

                if (request.ServiceType.HasValue)
                {
                    conditions.Add(@"a.""serviceType"" = @serviceType");
                    parameters.Add("serviceType", request.ServiceType.Value.ToString());
                }

                if (!string.IsNullOrEmpty(request.Location))
                {
                    conditions.Add(@"@location = ANY(a.""serviceAreas"")");
                    parameters.Add("location", request.Location);
                }

                if (request.Verified.HasValue)
                {
                    conditions.Add(@"a.""isVerified"" = @verified");
                    parameters.Add("verified", request.Verified.Value);
                }

                if (request.Premium.HasValue)
                {
                    conditions.Add(@"a.""isPremium"" = @premium");
                    parameters.Add("premium", request.Premium.Value);
                }

                if (conditions.Any())
                {
                    sql += " AND " + string.Join(" AND ", conditions);
                }

                // Add ordering
                sql += request.SortBy?.ToLower() switch
                {
                    "premium" => @" ORDER BY a.""isPremium"" DESC, a.""isVerified"" DESC, a.""createdAt"" DESC",
                    "name" => @" ORDER BY a.""businessName"" ASC",
                    "newest" => @" ORDER BY a.""createdAt"" DESC",
                    _ => @" ORDER BY a.""isPremium"" DESC, a.""isVerified"" DESC, a.""createdAt"" DESC"
                };

                // Add pagination
                var offset = (request.Page - 1) * request.Limit;
                sql += " LIMIT @limit OFFSET @offset";
                parameters.Add("limit", request.Limit);
                parameters.Add("offset", offset);

                var advertisers = await _dbContext.QueryAsync<Advertiser>(sql, parameters);

                // Get total count
                var countSql = @"
                    SELECT COUNT(*)
                    FROM public.""Advertiser"" a
                    WHERE 1=1";

                if (conditions.Any())
                {
                    countSql += " AND " + string.Join(" AND ", conditions);
                }

                var total = await _dbContext.QuerySingleAsync<int>(countSql, parameters);

                var response = new AdvertiserSearchResponse
                {
                    Advertisers = advertisers.Select(MapToResponse).ToList(),
                    Total = total,
                    Page = request.Page,
                    TotalPages = (int)Math.Ceiling((double)total / request.Limit),
                    HasMore = (request.Page * request.Limit) < total
                };

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching advertisers with request: {@Request}", request);
                throw;
            }
        }

        public async Task<AdvertiserResponse?> GetAdvertiserByIdAsync(string id)
        {
            try
            {
                const string sql = @"
                    SELECT a.*, u.full_name as user_name, u.avatar_url as user_avatar
                    FROM public.""Advertiser"" a
                    LEFT JOIN public.""User"" u ON a.""userId"" = u.id
                    WHERE a.id = @id";

                var advertiser = await _dbContext.QueryFirstOrDefaultAsync<Advertiser>(sql, new { id });
                return advertiser != null ? MapToResponse(advertiser) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting advertiser by ID: {Id}", id);
                throw;
            }
        }

        public async Task<AdvertiserResponse?> GetAdvertiserByUserIdAsync(string userId)
        {
            try
            {
                const string sql = @"
                    SELECT a.*, u.full_name as user_name, u.avatar_url as user_avatar
                    FROM public.""Advertiser"" a
                    LEFT JOIN public.""User"" u ON a.""userId"" = u.id
                    WHERE a.""userId"" = @userId";

                var advertiser = await _dbContext.QueryFirstOrDefaultAsync<Advertiser>(sql, new { userId });
                return advertiser != null ? MapToResponse(advertiser) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting advertiser by user ID: {UserId}", userId);
                throw;
            }
        }

        public async Task<AdvertiserResponse> CreateAdvertiserAsync(CreateAdvertiserRequest request, string userId)
        {
            try
            {
                var id = Guid.NewGuid().ToString();
                var now = DateTime.UtcNow;

                const string sql = @"
                    INSERT INTO public.""Advertiser"" (
                        id, ""userId"", ""businessName"", ""contactName"", email, phone, website, 
                        description, ""serviceType"", ""serviceAreas"", ""licenseNumber"", plan, 
                        status, ""isPremium"", ""isVerified"", images, metadata, ""createdAt"", ""updatedAt""
                    ) VALUES (
                        @id, @userId, @businessName, @contactName, @email, @phone, @website,
                        @description, @serviceType, @serviceAreas, @licenseNumber, @plan,
                        @status, @isPremium, @isVerified, @images, @metadata, @createdAt, @updatedAt
                    )";

                var parameters = new
                {
                    id,
                    userId,
                    businessName = request.BusinessName,
                    contactName = request.ContactName,
                    email = request.Email,
                    phone = request.Phone,
                    website = request.Website,
                    description = request.Description,
                    serviceType = request.ServiceType.ToString(),
                    serviceAreas = request.ServiceAreas,
                    licenseNumber = request.LicenseNumber,
                    plan = request.Plan.ToString(),
                    status = AdvertiserStatus.pending.ToString(),
                    isPremium = request.Plan != AdvertiserPlan.basic,
                    isVerified = false,
                    images = request.Images,
                    metadata = "{}",
                    createdAt = now,
                    updatedAt = now
                };

                await _dbContext.ExecuteAsync(sql, parameters);

                var created = await GetAdvertiserByIdAsync(id);
                return created ?? throw new InvalidOperationException("Failed to retrieve created advertiser");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating advertiser for user: {UserId}", userId);
                throw;
            }
        }

        public async Task<AdvertiserResponse?> UpdateAdvertiserAsync(UpdateAdvertiserRequest request)
        {
            try
            {
                var updates = new List<string>();
                var parameters = new DynamicParameters();
                parameters.Add("id", request.Id);
                parameters.Add("updatedAt", DateTime.UtcNow);

                if (!string.IsNullOrEmpty(request.BusinessName))
                {
                    updates.Add(@"""businessName"" = @businessName");
                    parameters.Add("businessName", request.BusinessName);
                }

                if (request.ContactName != null)
                {
                    updates.Add(@"""contactName"" = @contactName");
                    parameters.Add("contactName", request.ContactName);
                }

                if (!string.IsNullOrEmpty(request.Email))
                {
                    updates.Add(@"email = @email");
                    parameters.Add("email", request.Email);
                }

                if (request.Phone != null)
                {
                    updates.Add(@"phone = @phone");
                    parameters.Add("phone", request.Phone);
                }

                if (request.Website != null)
                {
                    updates.Add(@"website = @website");
                    parameters.Add("website", request.Website);
                }

                if (request.Description != null)
                {
                    updates.Add(@"description = @description");
                    parameters.Add("description", request.Description);
                }

                if (request.ServiceType.HasValue)
                {
                    updates.Add(@"""serviceType"" = @serviceType");
                    parameters.Add("serviceType", request.ServiceType.Value.ToString());
                }

                if (request.ServiceAreas != null)
                {
                    updates.Add(@"""serviceAreas"" = @serviceAreas");
                    parameters.Add("serviceAreas", request.ServiceAreas);
                }

                if (request.LicenseNumber != null)
                {
                    updates.Add(@"""licenseNumber"" = @licenseNumber");
                    parameters.Add("licenseNumber", request.LicenseNumber);
                }

                if (request.Plan.HasValue)
                {
                    updates.Add(@"plan = @plan");
                    parameters.Add("plan", request.Plan.Value.ToString());

                    updates.Add(@"""isPremium"" = @isPremium");
                    parameters.Add("isPremium", request.Plan.Value != AdvertiserPlan.basic);
                }

                if (request.Images != null)
                {
                    updates.Add(@"images = @images");
                    parameters.Add("images", request.Images);
                }

                if (!updates.Any())
                {
                    return await GetAdvertiserByIdAsync(request.Id);
                }

                updates.Add(@"""updatedAt"" = @updatedAt");

                var sql = $@"
                    UPDATE public.""Advertiser""
                    SET {string.Join(", ", updates)}
                    WHERE id = @id";

                await _dbContext.ExecuteAsync(sql, parameters);

                return await GetAdvertiserByIdAsync(request.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating advertiser: {Id}", request.Id);
                throw;
            }
        }

        public async Task<bool> DeleteAdvertiserAsync(string id)
        {
            try
            {
                const string sql = @"DELETE FROM public.""Advertiser"" WHERE id = @id";
                var rowsAffected = await _dbContext.ExecuteAsync(sql, new { id });
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting advertiser: {Id}", id);
                throw;
            }
        }

        public async Task<bool> VerifyAdvertiserAsync(string id, bool isVerified)
        {
            try
            {
                const string sql = @"
                    UPDATE public.""Advertiser""
                    SET ""isVerified"" = @isVerified, ""updatedAt"" = @updatedAt
                    WHERE id = @id";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, new
                {
                    id,
                    isVerified,
                    updatedAt = DateTime.UtcNow
                });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying advertiser: {Id}", id);
                throw;
            }
        }

        public async Task<bool> UpdateAdvertiserStatusAsync(string id, AdvertiserStatus status)
        {
            try
            {
                const string sql = @"
                    UPDATE public.""Advertiser""
                    SET status = @status, ""updatedAt"" = @updatedAt
                    WHERE id = @id";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, new
                {
                    id,
                    status = status.ToString(),
                    updatedAt = DateTime.UtcNow
                });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating advertiser status: {Id}", id);
                throw;
            }
        }

        public Task<List<AdvertiserPlanFeatures>> GetAdvertiserPlansAsync()
        {
            // Return static plan features - these could be stored in database if needed
            return Task.FromResult(new List<AdvertiserPlanFeatures>
            {
                new()
                {
                    Id = AdvertiserPlan.basic,
                    Name = "Basic Listing",
                    Price = "$49",
                    Period = "/month",
                    Description = "Get listed in our service directory",
                    Features = new[]
                    {
                        "Basic business listing",
                        "Contact information display",
                        "Service area coverage",
                        "Customer reviews",
                        "Mobile-friendly profile"
                    }
                },
                new()
                {
                    Id = AdvertiserPlan.premium,
                    Name = "Premium Listing",
                    Price = "$99",
                    Period = "/month",
                    Description = "Stand out with premium features",
                    Features = new[]
                    {
                        "Everything in Basic",
                        "Featured placement in search",
                        "Premium badge",
                        "Photo gallery (up to 10 images)",
                        "Detailed business description",
                        "Priority customer support",
                        "Analytics dashboard"
                    },
                    Popular = true
                }
            });
        }

        private static AdvertiserResponse MapToResponse(Advertiser advertiser)
        {
            return new AdvertiserResponse
            {
                Id = advertiser.Id,
                UserId = advertiser.UserId,
                ServiceProviderId = advertiser.ServiceProviderId,
                BusinessName = advertiser.BusinessName,
                ContactName = advertiser.ContactName,
                Email = advertiser.Email,
                Phone = advertiser.Phone,
                Website = advertiser.Website,
                Description = advertiser.Description,
                ServiceType = advertiser.ServiceType,
                ServiceAreas = advertiser.ServiceAreas,
                LicenseNumber = advertiser.LicenseNumber,
                Plan = advertiser.Plan,
                Status = advertiser.Status,
                FeaturedUntil = advertiser.FeaturedUntil,
                IsPremium = advertiser.IsPremium,
                IsVerified = advertiser.IsVerified,
                Images = advertiser.Images,
                CreatedAt = advertiser.CreatedAt,
                UpdatedAt = advertiser.UpdatedAt
            };
        }
    }
}
