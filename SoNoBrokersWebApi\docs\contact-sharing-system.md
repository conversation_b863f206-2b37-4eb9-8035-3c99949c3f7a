# Contact Sharing System Documentation

## Overview

The Contact Sharing System is a comprehensive feature that enables secure communication between property buyers and sellers on the SoNoBrokers platform. It facilitates contact information sharing, property offers, and visit scheduling while maintaining privacy and security.

## Features

### Core Functionality
- **Contact Information Sharing**: Buyers can share their contact details with sellers
- **Property Offers**: Submit and manage property purchase offers
- **Visit Scheduling**: Request and schedule property viewings
- **Offer with Visit**: Combined offer submission and visit scheduling
- **Status Tracking**: Real-time status updates for all interactions
- **Email Notifications**: Automated email notifications for all parties

### Security Features
- **User Authentication**: Clerk-based authentication required
- **Access Control**: Users can only access their own contact shares
- **Data Validation**: Comprehensive input validation and sanitization
- **Rate Limiting**: Prevents spam and abuse
- **Privacy Protection**: Secure handling of personal information

## Architecture

### Database Schema

#### ContactShare Table
```sql
CREATE TABLE "ContactShare" (
    "id" TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
    "propertyId" TEXT NOT NULL,
    "buyerId" TEXT NOT NULL,
    "sellerId" TEXT NOT NULL,
    "buyerName" TEXT NOT NULL,
    "buyerEmail" TEXT NOT NULL,
    "buyerPhone" TEXT,
    "message" TEXT,
    "shareType" INTEGER NOT NULL,
    "offerAmount" DECIMAL(12,2),
    "schedulingPreference" TEXT,
    "preferredVisitDate" DATE,
    "preferredVisitTime" TIME,
    "status" INTEGER NOT NULL DEFAULT 1,
    "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    "respondedAt" TIMESTAMP WITH TIME ZONE,
    "sellerResponse" TEXT,
    "emailSent" BOOLEAN DEFAULT FALSE,
    "emailSentAt" TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY ("propertyId") REFERENCES "Property"("id"),
    FOREIGN KEY ("buyerId") REFERENCES "User"("id"),
    FOREIGN KEY ("sellerId") REFERENCES "User"("id")
);
```

#### Indexes
```sql
CREATE INDEX "idx_contactshare_propertyid" ON "ContactShare"("propertyId");
CREATE INDEX "idx_contactshare_buyerid" ON "ContactShare"("buyerId");
CREATE INDEX "idx_contactshare_sellerid" ON "ContactShare"("sellerId");
CREATE INDEX "idx_contactshare_status" ON "ContactShare"("status");
CREATE INDEX "idx_contactshare_createdat" ON "ContactShare"("createdAt");
```

### API Endpoints

#### Public Endpoints (Authenticated Users)

**POST /api/sonobrokers/contact-sharing**
- Create a new contact share request
- Body: `CreateContactShareRequest`
- Returns: `ContactShareResponse`

**GET /api/sonobrokers/contact-sharing**
- Get contact shares for current user
- Query params: `ContactShareSearchParams`
- Returns: `ContactShareSearchResponse`

**GET /api/sonobrokers/contact-sharing/{id}**
- Get specific contact share by ID
- Returns: `ContactShareResponse`

**PUT /api/sonobrokers/contact-sharing/{id}/respond**
- Seller response to contact share
- Body: `ContactShareSellerResponse`
- Returns: Success message

**GET /api/sonobrokers/contact-sharing/stats**
- Get contact share statistics for current user
- Returns: `ContactShareStats`

**GET /api/sonobrokers/contact-sharing/property/{propertyId}**
- Get contact shares for a specific property
- Returns: `ContactShareSearchResponse`

**DELETE /api/sonobrokers/contact-sharing/{id}**
- Delete a contact share
- Returns: Success message

#### Admin Endpoints

**GET /api/admin/contact-sharing**
- Get all contact shares (admin only)
- Returns: `ContactShareSearchResponse`

**GET /api/admin/contact-sharing/stats**
- Get system-wide contact share statistics
- Returns: `ContactShareStats`

**POST /api/admin/contact-sharing/send-reminders**
- Send reminder emails for pending contact shares
- Returns: Success message

### Data Models

#### ContactShareType Enum
```csharp
public enum ContactShareType
{
    ContactRequest = 1,
    PropertyOffer = 2,
    ScheduleVisit = 3,
    OfferWithVisit = 4
}
```

#### ContactShareStatus Enum
```csharp
public enum ContactShareStatus
{
    Sent = 1,
    Delivered = 2,
    Viewed = 3,
    Responded = 4,
    Accepted = 5,
    Declined = 6,
    Expired = 7
}
```

#### CreateContactShareRequest
```csharp
public class CreateContactShareRequest
{
    public string PropertyId { get; set; }
    public string SellerId { get; set; }
    public string BuyerName { get; set; }
    public string BuyerEmail { get; set; }
    public string? BuyerPhone { get; set; }
    public string? Message { get; set; }
    public ContactShareType ShareType { get; set; }
    public decimal? OfferAmount { get; set; }
    public string? SchedulingPreference { get; set; }
    public string? PreferredVisitDate { get; set; }
    public string? PreferredVisitTime { get; set; }
}
```

#### ContactShareResponse
```csharp
public class ContactShareResponse
{
    public string Id { get; set; }
    public string PropertyId { get; set; }
    public string PropertyTitle { get; set; }
    public string PropertyAddress { get; set; }
    public decimal PropertyPrice { get; set; }
    public string BuyerId { get; set; }
    public string BuyerName { get; set; }
    public string BuyerEmail { get; set; }
    public string? BuyerPhone { get; set; }
    public string SellerId { get; set; }
    public string SellerName { get; set; }
    public string SellerEmail { get; set; }
    public string? Message { get; set; }
    public ContactShareType ShareType { get; set; }
    public string ShareTypeDisplay { get; set; }
    public decimal? OfferAmount { get; set; }
    public string? SchedulingPreference { get; set; }
    public string? PreferredVisitDate { get; set; }
    public string? PreferredVisitTime { get; set; }
    public ContactShareStatus Status { get; set; }
    public string StatusDisplay { get; set; }
    public string CreatedAt { get; set; }
    public string? RespondedAt { get; set; }
    public string? SellerResponse { get; set; }
    public bool EmailSent { get; set; }
    public string? EmailSentAt { get; set; }
}
```

## React Components

### ContactShareButton
Main button component for initiating contact sharing.

**Props:**
- `propertyId`: Property identifier
- `sellerId`: Seller user identifier
- `sellerName`: Seller display name
- `propertyTitle`: Property title
- `propertyPrice`: Property price
- `propertyAddress`: Property address
- `variant`: Button variant (default, outline, ghost)
- `size`: Button size (sm, default, lg)
- `showText`: Whether to show button text

**Usage:**
```tsx
<ContactShareButton
  propertyId="prop-123"
  sellerId="seller-123"
  sellerName="John Seller"
  propertyTitle="Beautiful Family Home"
  propertyPrice={500000}
  propertyAddress="123 Main St, City, State"
/>
```

### PropertyQuickActions
Quick action buttons for property cards.

**Usage:**
```tsx
<PropertyQuickActions
  propertyId="prop-123"
  sellerId="seller-123"
  sellerName="John Seller"
  propertyTitle="Beautiful Family Home"
  propertyPrice={500000}
  propertyAddress="123 Main St, City, State"
/>
```

### ContactShareModal
Modal component for contact sharing forms.

**Features:**
- Tabbed interface for different share types
- Form validation
- Real-time feedback
- Success/error handling

### ContactSharesDashboard
Dashboard for managing contact shares.

**Features:**
- Statistics overview
- Filterable contact share list
- Search functionality
- Status management
- Pagination

## Server Actions

### Contact Sharing Actions
Located in `/src/lib/actions/contact-sharing-actions.ts`

- `shareContactAction`: Share contact information
- `submitOfferAction`: Submit property offer
- `scheduleVisitAction`: Schedule property visit
- `submitOfferWithVisitAction`: Submit offer with visit
- `sellerRespondAction`: Seller response to contact share
- `deleteContactShareAction`: Delete contact share

## Email Notifications

### Email Templates
- **Contact Request**: Notifies seller of buyer interest
- **Property Offer**: Notifies seller of offer submission
- **Visit Scheduling**: Notifies seller of visit request
- **Seller Response**: Notifies buyer of seller response

### Email Service
Uses Resend API for reliable email delivery with:
- Template-based emails
- Delivery tracking
- Bounce handling
- Unsubscribe management

## Configuration

### Environment Variables
```env
# Contact Sharing Settings
CONTACT_SHARING_MAX_PER_DAY=10
CONTACT_SHARING_MAX_OFFERS_PER_PROPERTY=5
CONTACT_SHARING_EMAIL_COOLDOWN_MINUTES=60
CONTACT_SHARING_AUTO_EXPIRE_DAYS=30

# Email Settings
RESEND_API_KEY=your_resend_api_key
FROM_EMAIL=<EMAIL>
FROM_NAME=SoNoBrokers

# Feature Flags
ENABLE_CONTACT_SHARING=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_PROPERTY_OFFERS=true
ENABLE_VISIT_SCHEDULING=true
```

### Database Configuration
Ensure proper database setup with:
- ContactShare table created
- Indexes applied
- Foreign key constraints
- Proper permissions

## Testing

### Unit Tests
- Service layer tests
- Validation tests
- Business logic tests
- Email service tests

### Integration Tests
- API endpoint tests
- Database integration tests
- Authentication tests
- End-to-end workflows

### React Component Tests
- Component rendering tests
- User interaction tests
- Form validation tests
- Modal behavior tests

## Security Considerations

### Data Protection
- Personal information encryption
- Secure data transmission
- Access control enforcement
- Audit logging

### Rate Limiting
- Per-user contact share limits
- Per-property offer limits
- Email sending cooldowns
- API rate limiting

### Input Validation
- Server-side validation
- Client-side validation
- SQL injection prevention
- XSS protection

## Performance Optimization

### Database Optimization
- Proper indexing
- Query optimization
- Connection pooling
- Caching strategies

### API Performance
- Response compression
- Pagination
- Efficient queries
- Caching headers

### Frontend Performance
- Component lazy loading
- Optimistic updates
- Error boundaries
- Loading states

## Monitoring and Analytics

### Metrics to Track
- Contact share creation rate
- Offer submission rate
- Visit scheduling rate
- Response rate
- Email delivery rate
- User engagement

### Logging
- API request/response logging
- Error logging
- Performance logging
- Security event logging

## Deployment

### Prerequisites
- .NET 9 runtime
- PostgreSQL database
- Resend API account
- Clerk authentication setup

### Deployment Steps
1. Deploy database schema
2. Configure environment variables
3. Deploy API application
4. Deploy React frontend
5. Configure monitoring
6. Test functionality

## Troubleshooting

### Common Issues
- Email delivery failures
- Authentication errors
- Database connection issues
- Rate limiting triggers

### Debug Tools
- Application logs
- Database query logs
- Email delivery logs
- Performance metrics

## Future Enhancements

### Planned Features
- Real-time notifications
- Advanced scheduling
- Offer negotiation
- Video call integration
- Mobile app support

### Technical Improvements
- GraphQL API
- Real-time updates
- Advanced caching
- Microservices architecture
- Enhanced analytics
