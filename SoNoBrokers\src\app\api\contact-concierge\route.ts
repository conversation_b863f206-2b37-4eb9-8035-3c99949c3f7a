import { NextRequest, NextResponse } from 'next/server'
import { resendService } from '@/lib/resend'

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const {
      name,
      email,
      phone,
      propertyAddress,
      propertyValue,
      timeline,
      requirements,
      packageInterest,
      country,
      selectedPackage
    } = body

    if (!name || !email) {
      return NextResponse.json(
        { error: 'Name and email are required' },
        { status: 400 }
      )
    }

    // Create email content
    const emailContent = `
New Concierge Service Inquiry

Contact Information:
- Name: ${name}
- Email: ${email}
- Phone: ${phone || 'Not provided'}
- Country: ${country}

Property Details:
- Address: ${propertyAddress || 'Not provided'}
- Estimated Value: ${propertyValue || 'Not provided'}
- Timeline: ${timeline || 'Not provided'}

Service Interest:
- Package: ${packageInterest || 'Not specified'}
- Selected Package ID: ${selectedPackage || 'None'}

Requirements & Questions:
${requirements || 'No additional requirements specified'}

---
This inquiry was submitted through the SoNoBrokers Concierge Services page.
Please respond within 24 hours.
    `.trim()

    // Send email to support
    await resendService.sendConciergeInquiry(
      '<EMAIL>',
      `New Concierge Inquiry from ${name}`,
      emailContent
    )

    // Send confirmation email to user
    await resendService.sendConciergeConfirmation(
      email,
      name,
      packageInterest || 'Concierge Services'
    )

    return NextResponse.json(
      { message: 'Inquiry sent successfully' },
      { status: 200 }
    )
  } catch (error) {
    console.error('Error processing concierge inquiry:', error)
    return NextResponse.json(
      { error: 'Failed to send inquiry' },
      { status: 500 }
    )
  }
}
