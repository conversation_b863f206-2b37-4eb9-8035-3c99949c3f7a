'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestPropertiesPage() {
  const [properties, setProperties] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const testFetch = async () => {
    setLoading(true);
    setError('');
    
    try {
      const response = await fetch('/api/properties/search');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      setProperties(data);
      console.log('Fetched properties:', data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      console.error('Error fetching properties:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testFetch();
  }, []);

  return (
    <div className="container mx-auto p-8">
      <Card>
        <CardHeader>
          <CardTitle>Property Search API Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={testFetch} disabled={loading}>
            {loading ? 'Loading...' : 'Test Fetch Properties'}
          </Button>
          
          {error && (
            <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded">
              Error: {error}
            </div>
          )}
          
          <div>
            <h3 className="text-lg font-semibold mb-2">
              Properties Found: {properties.length}
            </h3>
            
            {properties.length > 0 ? (
              <div className="space-y-2">
                {properties.slice(0, 3).map((property: any) => (
                  <div key={property.id} className="p-3 border rounded">
                    <h4 className="font-medium">{property.title}</h4>
                    <p className="text-sm text-gray-600">
                      Price: ${property.price?.toLocaleString() || 'N/A'}
                    </p>
                    <p className="text-sm text-gray-600">
                      Type: {property.property_type || property.propertyType || 'N/A'}
                    </p>
                  </div>
                ))}
                {properties.length > 3 && (
                  <p className="text-sm text-gray-500">
                    ... and {properties.length - 3} more properties
                  </p>
                )}
              </div>
            ) : (
              !loading && <p className="text-gray-500">No properties found</p>
            )}
          </div>
          
          <details className="mt-4">
            <summary className="cursor-pointer text-sm font-medium">
              Raw API Response (click to expand)
            </summary>
            <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto max-h-64">
              {JSON.stringify(properties, null, 2)}
            </pre>
          </details>
        </CardContent>
      </Card>
    </div>
  );
}
