import { NextRequest, NextResponse } from 'next/server'
import { getAuthUserAPI } from '@/lib/auth'
import { UserService } from '@/services/userService'

// GET /api/user/profile - Get current user profile
export async function GET() {
  try {
    const user = await getAuthUserAPI()

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    return NextResponse.json({
      success: true,
      data: user
    })
  } catch (error) {
    console.error('Error fetching user profile:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch profile' },
      { status: 500 }
    )
  }
}

// PATCH /api/user/profile - Update current user profile
export async function PATCH(request: NextRequest) {
  try {
    const user = await getAuthUserAPI()

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()

    const {
      fullName,
      firstName,
      lastName,
      phone,
      userType
    } = body

    // Validate userType if provided (only buyer, seller allowed for regular users)
    if (userType && !['Buyer', 'Seller'].includes(userType)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user type. Only buyer and seller are allowed.' },
        { status: 400 }
      )
    }

    const updatedUser = await UserService.updateUser(user.id, {
      fullName,
      firstName,
      lastName,
      phone,
      userType
    })

    if (!updatedUser) {
      return NextResponse.json(
        { success: false, error: 'Failed to update profile' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: updatedUser,
      message: 'Profile updated successfully'
    })
  } catch (error) {
    console.error('Error updating user profile:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update profile' },
      { status: 500 }
    )
  }
}
