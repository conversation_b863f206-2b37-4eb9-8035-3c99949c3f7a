import { NextRequest, NextResponse } from 'next/server'
import { UserService } from '@/services/userService'
import { requireAdminAPI } from '@/lib/auth'
import { UserRole, SnbUserType } from '@/types'

// GET /api/admin/users - Get all users (Admin only)
export async function GET(request: NextRequest) {
  try {
    // Require admin authentication
    const adminUser = await requireAdminAPI()

    if (!adminUser) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }
    
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const role = searchParams.get('role') as UserRole | null

    const result = await UserService.getAllUsers(page, limit, role || undefined)
    
    return NextResponse.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

// POST /api/admin/users - Create new user (Admin only)
export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    const adminUser = await requireAdminAPI()

    if (!adminUser) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }
    
    const body = await request.json()
    const {
      email,
      fullName,
      firstName,
      lastName,
      phone,
      role,
      userType
    } = body

    // Validate required fields
    if (!email || !fullName) {
      return NextResponse.json(
        { success: false, error: 'Email and full name are required' },
        { status: 400 }
      )
    }

    // Validate role
    if (role && !Object.values(UserRole).includes(role)) {
      return NextResponse.json(
        { success: false, error: 'Invalid role' },
        { status: 400 }
      )
    }

    // Validate userType
    if (userType && !Object.values(SnbUserType).includes(userType)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user type' },
        { status: 400 }
      )
    }

    const newUser = await UserService.createUser({
      email,
      fullName,
      firstName,
      lastName,
      phone,
      role: role || UserRole.USER,
      userType: userType || SnbUserType.Buyer
    }, adminUser.id)

    return NextResponse.json({
      success: true,
      data: newUser
    })
  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create user' },
      { status: 500 }
    )
  }
}
