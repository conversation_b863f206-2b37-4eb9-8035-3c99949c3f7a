{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=sonobrokers_test;Username=********;Password=********;", "SupabaseConnection": "Host=localhost;Port=5432;Database=sonobrokers_test;Username=********;Password=********;"}, "Clerk": {"SecretKey": "test-secret-key", "PublishableKey": "test-publishable-key", "WebhookSecret": "test-webhook-secret", "ApiUrl": "https://api.clerk.dev/v1"}, "Stripe": {"SecretKey": "sk_test_test_key", "PublishableKey": "pk_test_test_key", "WebhookSecret": "whsec_test_secret"}, "Azure": {"BlobStorage": {"ConnectionString": "UseDevelopmentStorage=true", "ContainerName": "test-container"}}, "ExternalServices": {"GeoLocationApi": "https://ipapi.co", "EmailService": {"ApiKey": "test-api-key", "FromEmail": "<EMAIL>"}}, "TestSettings": {"UseInMemoryDatabase": true, "UseTestContainers": false, "SeedTestData": true, "CleanupAfterTests": true, "EnableDetailedLogging": false, "MockExternalServices": true, "ParallelExecution": false, "TestTimeout": 300}, "FeatureFlags": {"EnableAIPropertyImport": true, "EnablePropertyValuation": true, "EnableConciergeService": true, "EnableAdvancedSearch": true, "EnablePerformanceTests": true}, "MockServices": {"DelayMs": 100, "FailureRate": 0.0, "EnableRandomFailures": false, "SimulateNetworkLatency": false, "MaxRetries": 3}, "Performance": {"EnableCaching": false, "CacheExpirationMinutes": 5, "MaxPageSize": 100, "DefaultPageSize": 10, "RequestTimeoutSeconds": 30}}