import React from 'react'
import { ServiceLayout } from '@/components/shared/services/ServiceLayout'

interface InsuranceServiceProps {
  userType: 'buyer' | 'seller'
  isSignedIn: boolean
  country: string
}

// Mock data - in real implementation, this would come from your database and Google Places API
const getMockProviders = (country: string) => {
  const baseProviders = [
    {
      id: '1',
      name: '<PERSON>',
      businessName: 'Foster Insurance Group',
      serviceType: 'Insurance Broker',
      location: country === 'CA' ? 'Toronto, ON' : 'New York, NY',
      distance: '1.9 km',
      rating: 4.9,
      reviewCount: 178,
      price: 'Free Quotes',
      specialties: ['Home Insurance', 'Condo Insurance', 'Tenant Insurance', 'Umbrella Policies'],
      verified: true,
      isAdvertiser: true,
      isPremium: true,
      image: '/api/placeholder/80/80',
      phone: '******-0131',
      email: '<EMAIL>',
      website: 'https://fosterinsurance.com',
      description: 'Licensed insurance broker with access to 20+ top-rated insurers. Competitive rates and personalized service.',
      coordinates: country === 'CA' ? { lat: 43.6532, lng: -79.3832 } : { lat: 40.7128, lng: -74.0060 }
    },
    {
      id: '2',
      name: 'James Liu',
      businessName: 'Liu Insurance Solutions',
      serviceType: 'Insurance Agent',
      location: country === 'CA' ? 'Vancouver, BC' : 'Los Angeles, CA',
      distance: '3.1 km',
      rating: 4.8,
      reviewCount: 134,
      price: 'Competitive Rates',
      specialties: ['Property Insurance', 'Liability Coverage', 'Flood Insurance', 'Claims Support'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/80/80',
      phone: '******-0132',
      description: 'Experienced insurance agent specializing in property protection and risk management.',
      coordinates: country === 'CA' ? { lat: 49.2827, lng: -123.1207 } : { lat: 34.0522, lng: -118.2437 }
    },
    {
      id: '3',
      name: 'Patricia Williams',
      businessName: 'Williams Insurance Agency',
      serviceType: 'Insurance Specialist',
      location: country === 'CA' ? 'Calgary, AB' : 'Chicago, IL',
      distance: '4.7 km',
      rating: 4.7,
      reviewCount: 98,
      price: 'Best Value',
      specialties: ['First-Time Buyers', 'Multi-Policy Discounts', 'Claims Assistance', 'Risk Assessment'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/80/80',
      description: 'Dedicated insurance specialist helping homeowners find the right coverage at the best price.',
      coordinates: country === 'CA' ? { lat: 51.0447, lng: -114.0719 } : { lat: 41.8781, lng: -87.6298 }
    }
  ]

  // Add Google API providers (non-registered)
  const googleProviders = [
    {
      id: 'g1',
      name: 'National Insurance Co',
      businessName: 'National Insurance Company',
      serviceType: 'Insurance Provider',
      location: country === 'CA' ? 'Mississauga, ON' : 'Brooklyn, NY',
      distance: '7.5 km',
      rating: 4.4,
      reviewCount: 67,
      price: 'Standard Rates',
      specialties: ['Basic Coverage', 'Online Quotes'],
      verified: false,
      isAdvertiser: false,
      isPremium: false,
      description: 'Large insurance company offering standard home insurance policies.',
      coordinates: country === 'CA' ? { lat: 43.5890, lng: -79.6441 } : { lat: 40.6782, lng: -73.9442 }
    },
    {
      id: 'g2',
      name: 'Quick Insurance Pro',
      businessName: 'Quick Insurance Pro',
      serviceType: 'Insurance Agent',
      location: country === 'CA' ? 'Markham, ON' : 'Queens, NY',
      distance: '9.2 km',
      rating: 4.2,
      reviewCount: 45,
      price: 'Budget Friendly',
      specialties: ['Fast Quotes', 'Online Service'],
      verified: false,
      isAdvertiser: false,
      isPremium: false,
      description: 'Quick online insurance quotes and basic coverage options.',
      coordinates: country === 'CA' ? { lat: 43.8561, lng: -79.3370 } : { lat: 40.7282, lng: -73.7949 }
    }
  ]

  return [...baseProviders, ...googleProviders]
}

export function InsuranceService({
  userType,
  isSignedIn,
  country
}: InsuranceServiceProps) {
  // Get providers data (this would be async in real implementation)
  const mockProviders = getMockProviders(country)

  // Sort by distance and premium status
  const providers = mockProviders.sort((a, b) => {
    if (a.isPremium && !b.isPremium) return -1
    if (!a.isPremium && b.isPremium) return 1
    if (a.isAdvertiser && !b.isAdvertiser) return -1
    if (!a.isAdvertiser && b.isAdvertiser) return 1
    return parseFloat(a.distance) - parseFloat(b.distance)
  })

  const serviceDescription = userType === 'buyer'
    ? 'Professional home insurance services to protect your property investment. Our licensed brokers and agents help you find comprehensive coverage at competitive rates from top-rated insurance companies.'
    : 'Protect your property investment with comprehensive insurance coverage. Our insurance specialists help property owners and landlords find the right policies for maximum protection and peace of mind.'

  // Check environment variable for Google providers
  const showGoogleProviders = process.env.NEXT_PUBLIC_ENABLE_GOOGLE_PROVIDERS !== 'false'

  return (
    <ServiceLayout
      userType={userType}
      isSignedIn={isSignedIn}
      serviceTitle="Insurance Services"
      serviceDescription={serviceDescription}
      country={country}
      providers={providers}
      showGoogleProviders={showGoogleProviders}
    />
  )
}
