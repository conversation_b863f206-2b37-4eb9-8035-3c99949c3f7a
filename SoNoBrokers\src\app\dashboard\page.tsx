'use client'

import { useAppContext } from '@/contexts/AppContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { getDashboardUrl } from '@/lib/geo';

export default function DashboardPage() {
	const { isSignedIn, country } = useAppContext();
	const router = useRouter();
	const [isRedirecting, setIsRedirecting] = useState(false);

	useEffect(() => {
		// If user is not authenticated, redirect to sign-in
		if (!isSignedIn) {
			router.push('/sign-in');
			return;
		}

		// If already redirecting, don't do it again
		if (isRedirecting) return;

		const redirectToDashboard = async () => {
			setIsRedirecting(true);

			try {
				// Use existing geo utility to get dashboard URL
				const dashboardUrl = getDashboardUrl(country);

				// Redirect to country-specific dashboard
				router.push(dashboardUrl);
			} catch (error) {
				console.error('Error redirecting to dashboard:', error);
				// Fallback to Canada dashboard
				router.push('/ca/dashboard');
			}
		};

		redirectToDashboard();
	}, [isSignedIn, country, router, isRedirecting]);

	// Show loading while checking authentication and redirecting
	return (
		<div className="flex items-center justify-center min-h-screen">
			<div className="text-center">
				<div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
				<p className="text-gray-600">Redirecting to your dashboard...</p>
			</div>
		</div>
	);
}
