using System.ComponentModel.DataAnnotations;

namespace MicroSaasWebApi.Models.Payment
{
    /// <summary>
    /// Subscription plan model
    /// </summary>
    public class SubscriptionPlan
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public string Currency { get; set; } = "USD";
        public string Interval { get; set; } = "month"; // month, year
        public int IntervalCount { get; set; } = 1;
        public bool IsActive { get; set; } = true;
        public List<string> Features { get; set; } = new();
        public int? UserLimit { get; set; }
        public int? StorageLimit { get; set; } // in GB
        public bool HasTrial { get; set; } = false;
        public int TrialDays { get; set; } = 0;
    }

    /// <summary>
    /// Subscription request model
    /// </summary>
    public class CreateSubscriptionRequest
    {
        [Required]
        public string CustomerId { get; set; } = string.Empty;

        [Required]
        public string PriceId { get; set; } = string.Empty;

        public string? PaymentMethodId { get; set; }

        public int TrialPeriodDays { get; set; } = 0;

        public Dictionary<string, string>? Metadata { get; set; }
    }

    /// <summary>
    /// One-time payment request model
    /// </summary>
    public class OneTimePaymentRequest
    {
        [Required]
        public string CustomerId { get; set; } = string.Empty;

        [Required]
        public decimal Amount { get; set; }

        [Required]
        public string Currency { get; set; } = "USD";

        [Required]
        public string Description { get; set; } = string.Empty;

        public string? PaymentMethodId { get; set; }

        public Dictionary<string, string>? Metadata { get; set; }
    }

    /// <summary>
    /// Payment response model
    /// </summary>
    public class PaymentResponse
    {
        public bool Success { get; set; }
        public string? SubscriptionId { get; set; }
        public string? PaymentIntentId { get; set; }
        public string? ClientSecret { get; set; }
        public string? Status { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Checkout session request model
    /// </summary>
    public class CheckoutSessionRequest
    {
        public string CustomerId { get; set; } = string.Empty;
        public List<CheckoutLineItem> LineItems { get; set; } = new();
        public string Mode { get; set; } = "payment"; // payment, subscription, setup
        public string SuccessUrl { get; set; } = string.Empty;
        public string CancelUrl { get; set; } = string.Empty;
        public int TrialPeriodDays { get; set; } = 0;
        public Dictionary<string, string>? Metadata { get; set; }
    }

    /// <summary>
    /// Checkout line item model
    /// </summary>
    public class CheckoutLineItem
    {
        public string PriceId { get; set; } = string.Empty;
        public long Quantity { get; set; } = 1;
    }

    /// <summary>
    /// Subscription status model
    /// </summary>
    public class SubscriptionStatus
    {
        public string Id { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string PlanId { get; set; } = string.Empty;
        public string PlanName { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public DateTime CurrentPeriodStart { get; set; }
        public DateTime CurrentPeriodEnd { get; set; }
        public bool CancelAtPeriodEnd { get; set; }
        public DateTime? CanceledAt { get; set; }
        public DateTime? TrialStart { get; set; }
        public DateTime? TrialEnd { get; set; }
        public bool IsTrialActive => TrialEnd.HasValue && TrialEnd.Value > DateTime.UtcNow;
        public bool IsActive => Status == "active" || Status == "trialing";
    }

    /// <summary>
    /// Webhook event model for Clerk
    /// </summary>
    public class ClerkWebhookEvent
    {
        public string Type { get; set; } = string.Empty;
        public object Data { get; set; } = new();
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// Invoice model
    /// </summary>
    public class Invoice
    {
        public string Id { get; set; } = string.Empty;
        public string CustomerId { get; set; } = string.Empty;
        public string SubscriptionId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? PaidAt { get; set; }
        public string? InvoiceUrl { get; set; }
        public string? Description { get; set; }
    }
}
