using FluentAssertions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using MicroSaasWebApi.Authorization;
using MicroSaasWebApi.Models.Auth;
using MicroSaasWebApi.Tests.Common;
using System.Security.Claims;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Authentication
{
    public class RoleBasedAuthorizationTests : TestBase
    {
        private readonly IAuthorizationService _authorizationService;

        public RoleBasedAuthorizationTests(ITestOutputHelper output) : base(output)
        {
            var services = new ServiceCollection();
            services.AddAuthorization(options =>
            {
                options.AddPolicy("AdminOnly", policy => policy.RequireRole("ADMIN"));
                options.AddPolicy("UserOrAdmin", policy => policy.RequireRole("USER", "ADMIN"));
                options.AddPolicy("ProductManager", policy => policy.RequireRole("PRODUCT"));
                options.AddPolicy("OperatorOrAdmin", policy => policy.RequireRole("OPERATOR", "ADMIN"));
            });
            services.AddSingleton<IAuthorizationHandler, RoleAuthorizationHandler>();
            
            var serviceProvider = services.BuildServiceProvider();
            _authorizationService = serviceProvider.GetRequiredService<IAuthorizationService>();
        }

        [Theory]
        [InlineData("ADMIN", "AdminOnly", true)]
        [InlineData("USER", "AdminOnly", false)]
        [InlineData("PRODUCT", "AdminOnly", false)]
        [InlineData("OPERATOR", "AdminOnly", false)]
        public async Task AuthorizeAsync_WithAdminOnlyPolicy_ReturnsExpectedResult(string userRole, string policy, bool expectedSuccess)
        {
            // Arrange
            var user = CreateUserWithRole(userRole);

            // Act
            var result = await _authorizationService.AuthorizeAsync(user, policy);

            // Assert
            result.Succeeded.Should().Be(expectedSuccess);
        }

        [Theory]
        [InlineData("ADMIN", "UserOrAdmin", true)]
        [InlineData("USER", "UserOrAdmin", true)]
        [InlineData("PRODUCT", "UserOrAdmin", false)]
        [InlineData("OPERATOR", "UserOrAdmin", false)]
        public async Task AuthorizeAsync_WithUserOrAdminPolicy_ReturnsExpectedResult(string userRole, string policy, bool expectedSuccess)
        {
            // Arrange
            var user = CreateUserWithRole(userRole);

            // Act
            var result = await _authorizationService.AuthorizeAsync(user, policy);

            // Assert
            result.Succeeded.Should().Be(expectedSuccess);
        }

        [Theory]
        [InlineData("PRODUCT", "ProductManager", true)]
        [InlineData("ADMIN", "ProductManager", false)]
        [InlineData("USER", "ProductManager", false)]
        [InlineData("OPERATOR", "ProductManager", false)]
        public async Task AuthorizeAsync_WithProductManagerPolicy_ReturnsExpectedResult(string userRole, string policy, bool expectedSuccess)
        {
            // Arrange
            var user = CreateUserWithRole(userRole);

            // Act
            var result = await _authorizationService.AuthorizeAsync(user, policy);

            // Assert
            result.Succeeded.Should().Be(expectedSuccess);
        }

        [Theory]
        [InlineData("OPERATOR", "OperatorOrAdmin", true)]
        [InlineData("ADMIN", "OperatorOrAdmin", true)]
        [InlineData("USER", "OperatorOrAdmin", false)]
        [InlineData("PRODUCT", "OperatorOrAdmin", false)]
        public async Task AuthorizeAsync_WithOperatorOrAdminPolicy_ReturnsExpectedResult(string userRole, string policy, bool expectedSuccess)
        {
            // Arrange
            var user = CreateUserWithRole(userRole);

            // Act
            var result = await _authorizationService.AuthorizeAsync(user, policy);

            // Assert
            result.Succeeded.Should().Be(expectedSuccess);
        }

        [Fact]
        public async Task AuthorizeAsync_WithAnonymousUser_ReturnsFailed()
        {
            // Arrange
            var user = new ClaimsPrincipal(new ClaimsIdentity());

            // Act
            var result = await _authorizationService.AuthorizeAsync(user, "AdminOnly");

            // Assert
            result.Succeeded.Should().BeFalse();
        }

        [Fact]
        public async Task AuthorizeAsync_WithUserWithoutRoleClaim_ReturnsFailed()
        {
            // Arrange
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, "user_123"),
                new(ClaimTypes.Email, "<EMAIL>")
                // No role claim
            };
            var identity = new ClaimsIdentity(claims, "Test");
            var user = new ClaimsPrincipal(identity);

            // Act
            var result = await _authorizationService.AuthorizeAsync(user, "AdminOnly");

            // Assert
            result.Succeeded.Should().BeFalse();
        }

        [Fact]
        public async Task AuthorizeAsync_WithMultipleRoles_ReturnsSuccessForAnyMatchingRole()
        {
            // Arrange
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, "user_123"),
                new(ClaimTypes.Email, "<EMAIL>"),
                new(ClaimTypes.Role, "USER"),
                new(ClaimTypes.Role, "ADMIN") // Multiple roles
            };
            var identity = new ClaimsIdentity(claims, "Test");
            var user = new ClaimsPrincipal(identity);

            // Act
            var adminResult = await _authorizationService.AuthorizeAsync(user, "AdminOnly");
            var userResult = await _authorizationService.AuthorizeAsync(user, "UserOrAdmin");

            // Assert
            adminResult.Succeeded.Should().BeTrue();
            userResult.Succeeded.Should().BeTrue();
        }

        [Fact]
        public async Task AuthorizeAsync_WithInvalidPolicy_ReturnsFailed()
        {
            // Arrange
            var user = CreateUserWithRole("ADMIN");

            // Act
            var result = await _authorizationService.AuthorizeAsync(user, "NonExistentPolicy");

            // Assert
            result.Succeeded.Should().BeFalse();
        }

        [Fact]
        public void UserRole_EnumValues_AreCorrect()
        {
            // Assert
            Enum.GetNames<UserRole>().Should().Contain(new[] { "USER", "ADMIN", "PRODUCT", "OPERATOR" });
        }

        [Theory]
        [InlineData(UserRole.USER, "USER")]
        [InlineData(UserRole.ADMIN, "ADMIN")]
        [InlineData(UserRole.PRODUCT, "PRODUCT")]
        [InlineData(UserRole.OPERATOR, "OPERATOR")]
        public void UserRole_ToString_ReturnsCorrectString(UserRole role, string expectedString)
        {
            // Act & Assert
            role.ToString().Should().Be(expectedString);
        }

        [Fact]
        public void AuthorizeAttribute_WithRoles_IsConfiguredCorrectly()
        {
            // Arrange
            var authorizeAttribute = new AuthorizeAttribute();
            authorizeAttribute.Roles = "ADMIN,USER";

            // Assert
            authorizeAttribute.Roles.Should().Be("ADMIN,USER");
        }

        [Fact]
        public void AuthorizeAttribute_WithPolicy_IsConfiguredCorrectly()
        {
            // Arrange
            var authorizeAttribute = new AuthorizeAttribute();
            authorizeAttribute.Policy = "AdminOnly";

            // Assert
            authorizeAttribute.Policy.Should().Be("AdminOnly");
        }

        [Theory]
        [InlineData("ADMIN")]
        [InlineData("USER")]
        [InlineData("PRODUCT")]
        [InlineData("OPERATOR")]
        public async Task AuthorizeAsync_WithValidRoles_SucceedsForCorrectPolicies(string role)
        {
            // Arrange
            var user = CreateUserWithRole(role);

            // Act & Assert based on role
            switch (role)
            {
                case "ADMIN":
                    (await _authorizationService.AuthorizeAsync(user, "AdminOnly")).Succeeded.Should().BeTrue();
                    (await _authorizationService.AuthorizeAsync(user, "UserOrAdmin")).Succeeded.Should().BeTrue();
                    (await _authorizationService.AuthorizeAsync(user, "OperatorOrAdmin")).Succeeded.Should().BeTrue();
                    (await _authorizationService.AuthorizeAsync(user, "ProductManager")).Succeeded.Should().BeFalse();
                    break;
                case "USER":
                    (await _authorizationService.AuthorizeAsync(user, "AdminOnly")).Succeeded.Should().BeFalse();
                    (await _authorizationService.AuthorizeAsync(user, "UserOrAdmin")).Succeeded.Should().BeTrue();
                    (await _authorizationService.AuthorizeAsync(user, "OperatorOrAdmin")).Succeeded.Should().BeFalse();
                    (await _authorizationService.AuthorizeAsync(user, "ProductManager")).Succeeded.Should().BeFalse();
                    break;
                case "PRODUCT":
                    (await _authorizationService.AuthorizeAsync(user, "AdminOnly")).Succeeded.Should().BeFalse();
                    (await _authorizationService.AuthorizeAsync(user, "UserOrAdmin")).Succeeded.Should().BeFalse();
                    (await _authorizationService.AuthorizeAsync(user, "OperatorOrAdmin")).Succeeded.Should().BeFalse();
                    (await _authorizationService.AuthorizeAsync(user, "ProductManager")).Succeeded.Should().BeTrue();
                    break;
                case "OPERATOR":
                    (await _authorizationService.AuthorizeAsync(user, "AdminOnly")).Succeeded.Should().BeFalse();
                    (await _authorizationService.AuthorizeAsync(user, "UserOrAdmin")).Succeeded.Should().BeFalse();
                    (await _authorizationService.AuthorizeAsync(user, "OperatorOrAdmin")).Succeeded.Should().BeTrue();
                    (await _authorizationService.AuthorizeAsync(user, "ProductManager")).Succeeded.Should().BeFalse();
                    break;
            }
        }

        [Fact]
        public async Task AuthorizeAsync_WithCaseInsensitiveRole_ReturnsExpectedResult()
        {
            // Arrange
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, "user_123"),
                new(ClaimTypes.Email, "<EMAIL>"),
                new(ClaimTypes.Role, "admin") // Lowercase role
            };
            var identity = new ClaimsIdentity(claims, "Test");
            var user = new ClaimsPrincipal(identity);

            // Act
            var result = await _authorizationService.AuthorizeAsync(user, "AdminOnly");

            // Assert
            // This depends on how the authorization is configured - typically case-sensitive
            result.Succeeded.Should().BeFalse();
        }

        [Fact]
        public async Task AuthorizeAsync_WithEmptyRole_ReturnsFailed()
        {
            // Arrange
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, "user_123"),
                new(ClaimTypes.Email, "<EMAIL>"),
                new(ClaimTypes.Role, "") // Empty role
            };
            var identity = new ClaimsIdentity(claims, "Test");
            var user = new ClaimsPrincipal(identity);

            // Act
            var result = await _authorizationService.AuthorizeAsync(user, "AdminOnly");

            // Assert
            result.Succeeded.Should().BeFalse();
        }

        private ClaimsPrincipal CreateUserWithRole(string role)
        {
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, "user_123"),
                new(ClaimTypes.Email, "<EMAIL>"),
                new(ClaimTypes.Role, role)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            return new ClaimsPrincipal(identity);
        }
    }

    /// <summary>
    /// Custom authorization handler for testing role-based authorization
    /// </summary>
    public class RoleAuthorizationHandler : AuthorizationHandler<RolesAuthorizationRequirement>
    {
        protected override Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            RolesAuthorizationRequirement requirement)
        {
            if (context.User?.Identity?.IsAuthenticated == true)
            {
                var userRoles = context.User.FindAll(ClaimTypes.Role).Select(c => c.Value);
                if (requirement.AllowedRoles.Any(role => userRoles.Contains(role)))
                {
                    context.Succeed(requirement);
                }
            }

            return Task.CompletedTask;
        }
    }
}
