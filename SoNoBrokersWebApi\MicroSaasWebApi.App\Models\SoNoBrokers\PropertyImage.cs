using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    [Table("PropertyImage", Schema = "public")]
    public class PropertyImage
    {
        [Key]
        [Column("id")]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [Column("propertyId")]
        public string PropertyId { get; set; } = string.Empty;

        [Required]
        [Column("url")]
        public string Url { get; set; } = string.Empty;

        [Column("alt")]
        public string? Alt { get; set; }

        [Column("order")]
        public int Order { get; set; } = 0;

        [Column("isPrimary")]
        public bool IsPrimary { get; set; } = false;

        [Column("createdAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Azure Blob Storage path: Container "SoNoBrokers" / folder "propertyId/Images/" or "propertyId/Videos/"
        [NotMapped]
        public string StoragePath { get; set; } = string.Empty;

        // Helper method to generate storage path
        public string GetStoragePath(string? userEmail = null, bool isVideo = false)
        {
            var folder = isVideo ? "Videos" : "Images";
            return !string.IsNullOrEmpty(userEmail)
                ? $"SoNoBrokers/{userEmail}/{PropertyId}/{folder}/"
                : $"SoNoBrokers/{PropertyId}/{folder}/";
        }

        // Navigation properties
        [ForeignKey("PropertyId")]
        public virtual Property Property { get; set; } = null!;
    }
}
