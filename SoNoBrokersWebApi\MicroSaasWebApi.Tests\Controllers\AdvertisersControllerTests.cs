using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Tests.Common;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Controllers
{
    public class AdvertisersControllerTests : IClassFixture<TestWebApplicationFactory<Program>>, IAsyncLifetime
    {
        private readonly TestWebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;
        private readonly ITestOutputHelper _output;

        public AdvertisersControllerTests(TestWebApplicationFactory<Program> factory, ITestOutputHelper output)
        {
            _factory = factory;
            _output = output;
            _client = _factory.CreateClient();
        }

        public async Task InitializeAsync()
        {
            await _factory.SeedTestDataAsync();
        }

        public async Task DisposeAsync()
        {
            await _factory.CleanupTestDataAsync();
        }

        [Fact]
        public async Task GetAdvertisers_ReturnsSuccessWithAdvertisers()
        {
            // Act
            var response = await _client.GetAsync("/api/sonobrokers/advertisers");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AdvertiserSearchResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Advertisers.Should().NotBeNull();
            result.Total.Should().BeGreaterOrEqualTo(0);
        }

        [Fact]
        public async Task GetAdvertisers_WithFilters_ReturnsFilteredResults()
        {
            // Act
            var response = await _client.GetAsync("/api/sonobrokers/advertisers?serviceType=photographer&page=1&limit=5");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AdvertiserSearchResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Page.Should().Be(1);
        }

        [Fact]
        public async Task GetAdvertiserById_WithValidId_ReturnsAdvertiser()
        {
            // Arrange - Use test advertiser ID from seeded data
            var advertiserId = "test-adv-1";

            // Act
            var response = await _client.GetAsync($"/api/sonobrokers/advertisers/{advertiserId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AdvertiserResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Id.Should().Be(advertiserId);
        }

        [Fact]
        public async Task GetAdvertiserById_WithInvalidId_ReturnsNotFound()
        {
            // Act
            var response = await _client.GetAsync("/api/sonobrokers/advertisers/non-existent-id");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task CreateAdvertiser_WithValidData_ReturnsCreated()
        {
            // Arrange
            var request = TestDataBuilders.Advertisers.CreateAdvertiserRequest.Generate();
            
            // Add authentication header (using test auth)
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/advertisers", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Created);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AdvertiserResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.BusinessName.Should().Be(request.BusinessName);
            result.Email.Should().Be(request.Email);
            result.ServiceType.Should().Be(request.ServiceType);
        }

        [Fact]
        public async Task CreateAdvertiser_WithoutAuth_ReturnsUnauthorized()
        {
            // Arrange
            var request = TestDataBuilders.Advertisers.CreateAdvertiserRequest.Generate();

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/advertisers", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        }

        [Fact]
        public async Task CreateAdvertiser_WithInvalidData_ReturnsBadRequest()
        {
            // Arrange
            var request = new CreateAdvertiserRequest(); // Empty request
            
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/advertisers", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task UpdateAdvertiser_WithValidData_ReturnsUpdated()
        {
            // Arrange
            var advertiserId = "test-adv-1";
            var request = new UpdateAdvertiserRequest
            {
                Id = advertiserId,
                BusinessName = "Updated Business Name",
                Email = "<EMAIL>"
            };
            
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

            // Act
            var response = await _client.PutAsJsonAsync($"/api/sonobrokers/advertisers/{advertiserId}", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AdvertiserResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Id.Should().Be(advertiserId);
        }

        [Fact]
        public async Task DeleteAdvertiser_WithValidId_ReturnsNoContent()
        {
            // Arrange
            var advertiserId = "test-adv-2";
            
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

            // Act
            var response = await _client.DeleteAsync($"/api/sonobrokers/advertisers/{advertiserId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NoContent);
        }

        [Fact]
        public async Task GetMyAdvertiser_WithAuth_ReturnsAdvertiser()
        {
            // Arrange
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

            // Act
            var response = await _client.GetAsync("/api/sonobrokers/advertisers/me");

            // Assert
            // This might return NotFound if the test user doesn't have an advertiser profile
            response.StatusCode.Should().BeOneOf(HttpStatusCode.OK, HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task GetMyAdvertiser_WithoutAuth_ReturnsUnauthorized()
        {
            // Act
            var response = await _client.GetAsync("/api/sonobrokers/advertisers/me");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        }

        [Fact]
        public async Task GetAdvertiserPlans_ReturnsPlans()
        {
            // Act
            var response = await _client.GetAsync("/api/sonobrokers/advertisers/plans");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<List<AdvertiserPlanFeatures>>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.Should().Contain(p => p.Id == AdvertiserPlan.basic);
            result.Should().Contain(p => p.Id == AdvertiserPlan.premium);
        }

        [Fact]
        public async Task VerifyAdvertiser_WithValidId_ReturnsSuccess()
        {
            // Arrange
            var advertiserId = "test-adv-1";
            
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "admin-token");

            // Act
            var response = await _client.PatchAsync($"/api/sonobrokers/advertisers/{advertiserId}/verify", 
                JsonContent.Create(true));

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        [Fact]
        public async Task UpdateAdvertiserStatus_WithValidId_ReturnsSuccess()
        {
            // Arrange
            var advertiserId = "test-adv-1";
            var status = AdvertiserStatus.suspended;
            
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "admin-token");

            // Act
            var response = await _client.PatchAsync($"/api/sonobrokers/advertisers/{advertiserId}/status", 
                JsonContent.Create(status));

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        [Theory]
        [InlineData("photographer")]
        [InlineData("lawyer")]
        [InlineData("inspector")]
        public async Task GetAdvertisers_WithDifferentServiceTypes_ReturnsResults(string serviceType)
        {
            // Act
            var response = await _client.GetAsync($"/api/sonobrokers/advertisers?serviceType={serviceType}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            content.Should().NotBeNullOrEmpty();
        }

        [Theory]
        [InlineData(1, 5)]
        [InlineData(2, 10)]
        [InlineData(1, 20)]
        public async Task GetAdvertisers_WithPagination_ReturnsCorrectPage(int page, int limit)
        {
            // Act
            var response = await _client.GetAsync($"/api/sonobrokers/advertisers?page={page}&limit={limit}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AdvertiserSearchResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Page.Should().Be(page);
        }
    }
}
