import { notFound } from 'next/navigation';
import { createServerSupabaseClient } from '@/lib/supabase';
import Image from 'next/image';
import Link from 'next/link';
import { OpenHouse } from '@/components/shared/properties/OpenHouse';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Share, Download, Calendar, MapPin, Sparkles } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface OpenHousePageProps {
    params: Promise<{
        id: string;
    }>;
}

export default async function OpenHousePage({ params }: OpenHousePageProps) {
    const resolvedParams = await params;
    const supabase = await createServerSupabaseClient();

    const { data: property } = await supabase
        .from('properties')
        .select('*, seller:profiles(*)')
        .eq('id', resolvedParams.id)
        .single();

    if (!property) {
        notFound();
    }

    const {
        data: { user },
    } = await supabase.auth.getUser();

    const isOwner = user?.id === property.seller_id;

    return (
        <div className="min-h-screen bg-gradient-to-br from-background to-muted/50">
            <div className="container mx-auto py-8 space-y-8">
                {/* Header with navigation */}
                <div className="flex items-center justify-between">
                    <Link href={`/properties/${property.id}`}>
                        <Button variant="ghost" className="gap-2">
                            <ArrowLeft className="h-4 w-4" />
                            Back to Property
                        </Button>
                    </Link>
                    <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="bg-primary/10 text-primary gap-1">
                            <Sparkles className="h-3 w-3" />
                            AI-Powered
                        </Badge>
                    </div>
                </div>

                {/* Hero Section */}
                <Card className="overflow-hidden">
                    <div className="relative">
                        {/* Property Images Background */}
                        {property.images?.[0] && (
                            <div className="relative h-64 md:h-80 overflow-hidden">
                                <Image
                                    src={property.images[0].url}
                                    alt={property.title}
                                    fill
                                    className="object-cover"
                                    priority
                                />
                                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
                            </div>
                        )}

                        {/* Overlay Content */}
                        <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                            <div className="flex flex-col md:flex-row justify-between items-start md:items-end gap-4">
                                <div>
                                    <h1 className="text-3xl md:text-4xl font-bold mb-2">{property.title}</h1>
                                    <div className="flex items-center gap-2 mb-2">
                                        <MapPin className="h-4 w-4" />
                                        <span className="text-sm opacity-90">
                                            {typeof property.address === 'string'
                                                ? property.address
                                                : `${property.address?.street || ''}, ${property.address?.city || ''}`}
                                        </span>
                                    </div>
                                    <div className="flex items-center gap-4">
                                        <span className="text-2xl font-bold">{formatCurrency(property.price)}</span>
                                        <div className="flex items-center gap-2 text-sm">
                                            <span>{property.bedrooms} bed</span>
                                            <span>•</span>
                                            <span>{property.bathrooms} bath</span>
                                            <span>•</span>
                                            <span>{property.square_footage} sq ft</span>
                                        </div>
                                    </div>
                                </div>
                                <div className="flex gap-2">
                                    <Button variant="secondary" size="sm" className="gap-2">
                                        <Share className="h-4 w-4" />
                                        Share
                                    </Button>
                                    <Button variant="secondary" size="sm" className="gap-2">
                                        <Download className="h-4 w-4" />
                                        Download
                                    </Button>
                                    <Button size="sm" className="gap-2">
                                        <Calendar className="h-4 w-4" />
                                        Schedule Tour
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </Card>

                {/* Quick Property Info */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                        <CardContent className="p-4 text-center">
                            <div className="text-2xl font-bold text-primary">{property.bedrooms}</div>
                            <div className="text-sm text-muted-foreground">Bedrooms</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-4 text-center">
                            <div className="text-2xl font-bold text-primary">{property.bathrooms}</div>
                            <div className="text-sm text-muted-foreground">Bathrooms</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-4 text-center">
                            <div className="text-2xl font-bold text-primary">{property.square_footage}</div>
                            <div className="text-sm text-muted-foreground">Sq Ft</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-4 text-center">
                            <div className="text-2xl font-bold text-primary">{property.property_type}</div>
                            <div className="text-sm text-muted-foreground">Property Type</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Main Open House Component */}
                <OpenHouse property={property} />

                {/* Contact Section */}
                <Card>
                    <CardHeader>
                        <CardTitle>Interested in this property?</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-col md:flex-row items-center justify-between gap-6">
                            <div className="flex items-center gap-4">
                                {property.seller.avatar_url && (
                                    <div className="relative w-16 h-16 rounded-full overflow-hidden">
                                        <Image
                                            src={property.seller.avatar_url}
                                            alt={property.seller.full_name}
                                            fill
                                            className="object-cover"
                                        />
                                    </div>
                                )}
                                <div>
                                    <h3 className="font-semibold text-lg">{property.seller.full_name}</h3>
                                    <p className="text-muted-foreground">{property.seller.role}</p>
                                    <p className="text-sm text-muted-foreground">Listed by owner</p>
                                </div>
                            </div>
                            <div className="flex gap-3">
                                <Button variant="outline">Send Message</Button>
                                <Button className="gap-2">
                                    <Calendar className="h-4 w-4" />
                                    Schedule Viewing
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Footer */}
                <div className="text-center py-8 border-t">
                    <p className="text-sm text-muted-foreground">
                        This AI-powered open house was generated using advanced neighborhood analysis and property insights.
                    </p>
                    <p className="text-xs text-muted-foreground mt-2">
                        © {new Date().getFullYear()} SoNo Brokers. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    );
}

export async function generateMetadata({ params }: OpenHousePageProps) {
    const resolvedParams = await params;
    const supabase = await createServerSupabaseClient();

    const { data: property } = await supabase
        .from('properties')
        .select('title, description, price, address, images')
        .eq('id', resolvedParams.id)
        .single();

    if (!property) {
        return {
            title: 'Property Not Found'
        };
    }

    const address = typeof property.address === 'string'
        ? property.address
        : `${property.address?.city || ''}, ${property.address?.state || ''}`;

    return {
        title: `AI Open House - ${property.title} | SoNo Brokers`,
        description: `Explore this ${formatCurrency(property.price)} property in ${address} with AI-generated neighborhood insights, local amenities, and comprehensive property reports.`,
        openGraph: {
            title: `AI Open House - ${property.title}`,
            description: property.description?.substring(0, 160) + '...',
            images: property.images?.[0]?.url ? [property.images[0].url] : [],
        },
    };
}
