# 🚀 SoNoBrokers Web API - Quick Start Guide

## ⚡ 5-Minute Setup

### 1. Prerequisites Check

```powershell
# Verify .NET 9 is installed
dotnet --version  # Should show 9.x.x

# PostgreSQL client tools (optional, for database management)
psql --version
```

### 2. Navigate to Project

```powershell
# Navigate to the SoNoBrokers Web API project
cd C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App
```

### 3. Environment Setup

Create `.env` file in the project root:

```env
# Database Configuration
DATABASE_URL=***************************************************************************/postgres

# Clerk Authentication
CLERK_SECRET_KEY=sk_test_qMWT9IyzEIVMz6bjQ7P4ZBVHsiy9ZERxHbGKipVHcl
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_ZGlzY3JldGUtaGFyZS03My5jbGVyay5hY2NvdW50cy5kZXYk

# Stripe Payments
STRIPE_SECRET_KEY=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
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_51Qfy9dP82YH9JfOlFmwTk0RHvE7l6sow7ImJBI4c1GzcbhFydoVpb3DMevP8B3C4gj95qCoEise8vEhgFgWxhLEN000RfInKNH
```

### 4. Build Project

```bash
# Build the SoNoBrokers API
dotnet restore
dotnet build
```

**With Package Cache Clear (if needed):**

```bash
# Clear cache and build
dotnet nuget locals all --clear
dotnet restore
dotnet build
```

### 5. Database Setup

The database migrations run automatically on application startup. You can also run them manually:

```bash
# Check migration status
cd Migrations
.\Migration-Status.ps1

# Run pending migrations
.\Run-Migrations.ps1

# OR run all migrations manually
psql "***************************************************************************/postgres"
\i 0001_create_enums.sql
\i 0002_create_core_tables.sql
# ... etc
```

### 6. Run Application

```bash
# Run the SoNoBrokers API
dotnet run

# The application will start on:
# - HTTPS: https://localhost:7163
# - HTTP: http://localhost:5163

# With hot reload for development
dotnet watch run
```

### 7. Verify Setup

Open your browser and visit:

- **API Documentation**: https://localhost:7163/scalar/v1
- **Health Check**: https://localhost:7163/health
- **Health Dashboard**: https://localhost:7163/healthchecks-ui
- **Test Endpoint**: https://localhost:7163/api/sonobrokers/test/ping

---

## 🎯 Three Execution Options

### Option 1: Solution-Level (Recommended for Full Development)

**When to use**: Working with the complete solution, running tests, managing multiple projects.

```powershell
# Navigate to solution root
cd C:\Projects\MicroSaasWebApiRoot

# Complete build pipeline
.\build-solution.ps1 -All

# Individual operations
.\build-solution.ps1 -Clean    # Clean solution
.\build-solution.ps1 -Restore  # Restore packages
.\build-solution.ps1 -Build    # Build solution
.\build-solution.ps1 -Test     # Run tests
.\build-solution.ps1 -Run      # Run application

# Combined operations
.\build-solution.ps1 -Clean -Restore -Build -Test
```

**Advantages**:

- ✅ Builds entire solution including tests
- ✅ Runs all unit tests
- ✅ Manages dependencies automatically
- ✅ Best for CI/CD pipelines

### Option 2: Direct dotnet Commands (Manual Control)

**When to use**: Need precise control over build process, debugging build issues, custom configurations.

```powershell
# Navigate to solution root
cd C:\Projects\MicroSaasWebApiRoot

# Manual build process
dotnet nuget locals all --clear           # Clear cache
dotnet clean MicroSaasWebApiRoot.sln      # Clean solution
dotnet restore MicroSaasWebApiRoot.sln    # Restore packages
dotnet build MicroSaasWebApiRoot.sln      # Build solution
dotnet test MicroSaasWebApiRoot.sln       # Run tests

# Run with specific options
dotnet run --project MicroSaasWebApi.Full\MicroSaasWebApi.csproj
dotnet run --project MicroSaasWebApi.Full\MicroSaasWebApi.csproj --configuration Release
dotnet run --project MicroSaasWebApi.Full\MicroSaasWebApi.csproj --environment Production
```

**Advantages**:

- ✅ Full control over each step
- ✅ Can specify configurations
- ✅ Better for troubleshooting
- ✅ Works in any environment

### Option 3: Project-Level (Quick Development)

**When to use**: Rapid development, working only on the API, quick testing of changes.

```powershell
# Navigate to API project
cd C:\Projects\MicroSaasWebApiRoot\MicroSaasWebApi.Full

# Using project build script
.\build.ps1 -All              # Complete build
.\build.ps1 -Clean -Build     # Clean and build
.\build.ps1 -Run              # Run application

# Direct dotnet commands
dotnet clean                  # Clean project
dotnet restore                # Restore packages
dotnet build                  # Build project
dotnet run                    # Run application
dotnet watch run              # Run with hot reload
```

**Advantages**:

- ✅ Fastest for development
- ✅ Hot reload support
- ✅ Immediate feedback
- ✅ Less overhead

---

## 🔧 Development Workflows

### Daily Development Workflow

```powershell
# 1. Start of day
cd C:\Projects\MicroSaasWebApiRoot
git pull origin main
.\build-solution.ps1 -Restore -Build

# 2. Development (choose one)
# Option A: Hot reload development
cd MicroSaasWebApi.Full
dotnet watch run

# Option B: Manual restarts
.\build-solution.ps1 -Run
```

### Feature Development Workflow

```powershell
# 1. Create feature branch
git checkout -b feature/new-feature

# 2. Make changes, then test
.\build-solution.ps1 -Build -Test

# 3. Run application to verify
.\build-solution.ps1 -Run

# 4. Commit and push
git add .
git commit -m "Add new feature"
git push origin feature/new-feature
```

### Database Development Workflow

```powershell
# 1. Make model changes
# 2. Create migration
cd MicroSaasWebApi.Full
dotnet ef migrations add DescriptiveMigrationName

# 3. Review migration files
# 4. Update database
dotnet ef database update

# 5. Test application
dotnet run
```

---

## 🌐 Application Access Points

After running the application:

| Service          | URL                               | Description                         |
| ---------------- | --------------------------------- | ----------------------------------- |
| **API (HTTPS)**  | https://localhost:7000            | Main API endpoint                   |
| **API (HTTP)**   | http://localhost:5000             | HTTP endpoint                       |
| **Swagger UI**   | https://localhost:7000/swagger    | Interactive API documentation       |
| **ReDoc**        | https://localhost:7000/redoc      | Alternative API documentation       |
| **Health Check** | https://localhost:7000/health     | Application health status           |
| **Hangfire**     | https://localhost:7000/hangfire   | Background job dashboard (dev only) |
| **SignalR Hub**  | wss://localhost:7000/microsaashub | Real-time communication             |

### Key API Endpoints

```powershell
# Test these endpoints after startup
curl https://localhost:7000/api/configuration/info
curl https://localhost:7000/health
curl https://localhost:7000/api/payment/plans
```

---

## 🆘 Quick Troubleshooting

### Build Fails

```powershell
# Nuclear option - clean everything
dotnet nuget locals all --clear
.\build-solution.ps1 -Clean -Restore -Build
```

### Database Issues

```powershell
# Reset database
cd MicroSaasWebApi.Full
dotnet ef database drop --force
dotnet ef database update
```

### Port Already in Use

```powershell
# Kill processes on ports 5000/7000
netstat -ano | findstr :5000
netstat -ano | findstr :7000
# Kill the process ID shown
taskkill /PID <ProcessID> /F
```

### Package Conflicts

```powershell
# Check for outdated packages
dotnet list package --outdated

# Update specific package
dotnet add package PackageName --version LatestVersion
```

---

## 📚 Next Steps

1. **Explore the API**: Visit https://localhost:7000/swagger
2. **Check Configuration**: Visit https://localhost:7000/api/configuration/info
3. **Review Documentation**: See [PROJECT-EXECUTION-GUIDE.md](./PROJECT-EXECUTION-GUIDE.md)
4. **Setup Frontend**: Navigate to `MicroSaasWebApiUI` directory
5. **Configure Authentication**: Update Clerk settings in `.env`
6. **Setup Payments**: Configure Stripe webhooks

---

## 🎉 Success Indicators

✅ **Build Success**: No compilation errors  
✅ **Tests Pass**: All unit tests green  
✅ **Application Starts**: Listening on ports 5000/7000  
✅ **Swagger Loads**: API documentation accessible  
✅ **Database Connected**: Health check shows healthy  
✅ **Authentication Ready**: Clerk integration working  
✅ **Payments Ready**: Stripe integration configured

**You're ready to start developing your MicroSaaS application!** 🚀
