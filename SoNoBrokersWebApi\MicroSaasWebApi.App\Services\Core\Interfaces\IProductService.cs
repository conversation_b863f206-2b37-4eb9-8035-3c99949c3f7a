using MicroSaasWebApi.Models.Core;

namespace MicroSaasWebApi.Services.Core.Interfaces
{
    /// <summary>
    /// Product service interface for product management operations
    /// </summary>
    public interface IProductService
    {
        /// <summary>
        /// Get products with pagination and filtering
        /// </summary>
        Task<IEnumerable<Product>> GetProductsAsync(
            int page,
            int pageSize,
            string? search = null,
            string? category = null,
            bool? isActive = null,
            decimal? minPrice = null,
            decimal? maxPrice = null);

        /// <summary>
        /// Get product by ID
        /// </summary>
        Task<Product?> GetProductByIdAsync(Guid id);

        /// <summary>
        /// Get product by SKU
        /// </summary>
        Task<Product?> GetProductBySkuAsync(string sku);

        /// <summary>
        /// Get products by category
        /// </summary>
        Task<IEnumerable<Product>> GetProductsByCategoryAsync(string category, int page = 1, int pageSize = 10);

        /// <summary>
        /// Create new product
        /// </summary>
        Task<Product> CreateProductAsync(Product product);

        /// <summary>
        /// Update existing product
        /// </summary>
        Task<Product> UpdateProductAsync(Product product);

        /// <summary>
        /// Delete product (soft delete)
        /// </summary>
        Task<bool> DeleteProductAsync(Guid id);

        /// <summary>
        /// Update product stock
        /// </summary>
        Task<Product?> UpdateStockAsync(Guid id, int quantity, string operation = "set");

        /// <summary>
        /// Check if product exists by SKU
        /// </summary>
        Task<bool> ProductExistsBySkuAsync(string sku);

        /// <summary>
        /// Get product count
        /// </summary>
        Task<int> GetProductCountAsync(bool? isActive = null);

        /// <summary>
        /// Get products with low stock
        /// </summary>
        Task<IEnumerable<Product>> GetLowStockProductsAsync(int threshold = 10);

        /// <summary>
        /// Get featured products
        /// </summary>
        Task<IEnumerable<Product>> GetFeaturedProductsAsync(int count = 10);

        /// <summary>
        /// Search products by name or description
        /// </summary>
        Task<IEnumerable<Product>> SearchProductsAsync(string searchTerm, int page = 1, int pageSize = 10);

        /// <summary>
        /// Get products by price range
        /// </summary>
        Task<IEnumerable<Product>> GetProductsByPriceRangeAsync(decimal minPrice, decimal maxPrice, int page = 1, int pageSize = 10);

        /// <summary>
        /// Get all product categories
        /// </summary>
        Task<IEnumerable<string>> GetCategoriesAsync();

        /// <summary>
        /// Activate/deactivate product
        /// </summary>
        Task<bool> SetProductActiveStatusAsync(Guid id, bool isActive);
    }
}
