'use client'

import { useState, useEffect } from 'react'
import { useFormState } from 'react-dom'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Mail,
  Phone,
  DollarSign,
  Calendar,
  Clock,
  Home,
  User,
  Send,
  X
} from 'lucide-react'
import {
  shareContactAction,
  submitOfferAction,
  scheduleVisitAction,
  submitOfferWithVisitAction
} from '@/lib/actions/contact-sharing-actions'
// TODO: Move types to separate file to avoid server-only import
// import { ContactShareType } from '@/lib/api/contact-sharing-api'

// Temporary type definition (should be moved to @/types)
enum ContactShareType {
  ContactRequest = 'ContactRequest',
  PropertyOffer = 'PropertyOffer',
  ScheduleVisit = 'ScheduleVisit',
  OfferWithVisit = 'OfferWithVisit'
}

interface ContactShareModalProps {
  isOpen: boolean
  onClose: () => void
  propertyId: string
  sellerId: string
  sellerName: string
  propertyTitle: string
  propertyPrice: number
  propertyAddress: string
  defaultBuyerName: string
  defaultBuyerEmail: string
  defaultShareType?: 'contact' | 'offer' | 'visit' | 'offer-visit'
}

const initialState = {
  success: false,
  error: null as string | null,
  fieldErrors: {} as Record<string, string[]>,
}

export function ContactShareModal({
  isOpen,
  onClose,
  propertyId,
  sellerId,
  sellerName,
  propertyTitle,
  propertyPrice,
  propertyAddress,
  defaultBuyerName,
  defaultBuyerEmail,
  defaultShareType = 'contact'
}: ContactShareModalProps) {
  const [activeTab, setActiveTab] = useState(defaultShareType)
  const [contactState, contactAction] = useFormState(shareContactAction, initialState)
  const [offerState, offerAction] = useFormState(submitOfferAction, initialState)
  const [visitState, visitAction] = useFormState(scheduleVisitAction, initialState)
  const [offerVisitState, offerVisitAction] = useFormState(submitOfferWithVisitAction, initialState)

  // Close modal on successful submission
  useEffect(() => {
    if (contactState.success || offerState.success || visitState.success || offerVisitState.success) {
      setTimeout(() => {
        onClose()
      }, 2000)
    }
  }, [contactState.success, offerState.success, visitState.success, offerVisitState.success, onClose])

  const getCurrentState = () => {
    switch (activeTab) {
      case 'contact': return contactState
      case 'offer': return offerState
      case 'visit': return visitState
      case 'offer-visit': return offerVisitState
      default: return contactState
    }
  }

  const getCurrentAction = () => {
    switch (activeTab) {
      case 'contact': return contactAction
      case 'offer': return offerAction
      case 'visit': return visitAction
      case 'offer-visit': return offerVisitAction
      default: return contactAction
    }
  }

  const getShareType = () => {
    switch (activeTab) {
      case 'contact': return ContactShareType.ContactRequest
      case 'offer': return ContactShareType.PropertyOffer
      case 'visit': return ContactShareType.ScheduleVisit
      case 'offer-visit': return ContactShareType.OfferWithVisit
      default: return ContactShareType.ContactRequest
    }
  }

  const currentState = getCurrentState()
  const currentAction = getCurrentAction()

  if (!isOpen) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Home className="h-5 w-5" />
            Contact Seller
          </DialogTitle>
          <DialogDescription>
            Connect with {sellerName} about "{propertyTitle}"
          </DialogDescription>
        </DialogHeader>

        {/* Property Info */}
        <Card className="mb-4">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">{propertyTitle}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">{propertyAddress}</p>
              </div>
              <Badge variant="secondary" className="text-lg font-bold">
                ${propertyPrice.toLocaleString()}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Success Message */}
        {currentState.success && (
          <Alert className="mb-4">
            <Send className="h-4 w-4" />
            <AlertDescription>
              Your request has been sent successfully! {sellerName} will receive an email with your contact information.
            </AlertDescription>
          </Alert>
        )}

        {/* Error Message */}
        {currentState.error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{currentState.error}</AlertDescription>
          </Alert>
        )}

        {/* Tabs for different actions */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as typeof activeTab)} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="contact" className="text-xs">
              <Mail className="h-4 w-4 mr-1" />
              Contact
            </TabsTrigger>
            <TabsTrigger value="offer" className="text-xs">
              <DollarSign className="h-4 w-4 mr-1" />
              Offer
            </TabsTrigger>
            <TabsTrigger value="visit" className="text-xs">
              <Calendar className="h-4 w-4 mr-1" />
              Visit
            </TabsTrigger>
            <TabsTrigger value="offer-visit" className="text-xs">
              <DollarSign className="h-4 w-4 mr-1" />
              Offer + Visit
            </TabsTrigger>
          </TabsList>

          {/* Contact Tab */}
          <TabsContent value="contact">
            <ContactForm
              propertyId={propertyId}
              sellerId={sellerId}
              sellerName={sellerName}
              defaultBuyerName={defaultBuyerName}
              defaultBuyerEmail={defaultBuyerEmail}
              shareType={ContactShareType.ContactRequest}
              action={contactAction}
              state={contactState}
            />
          </TabsContent>

          {/* Offer Tab */}
          <TabsContent value="offer">
            <OfferForm
              propertyId={propertyId}
              sellerId={sellerId}
              sellerName={sellerName}
              propertyPrice={propertyPrice}
              defaultBuyerName={defaultBuyerName}
              defaultBuyerEmail={defaultBuyerEmail}
              shareType={ContactShareType.PropertyOffer}
              action={offerAction}
              state={offerState}
            />
          </TabsContent>

          {/* Visit Tab */}
          <TabsContent value="visit">
            <VisitForm
              propertyId={propertyId}
              sellerId={sellerId}
              sellerName={sellerName}
              defaultBuyerName={defaultBuyerName}
              defaultBuyerEmail={defaultBuyerEmail}
              shareType={ContactShareType.ScheduleVisit}
              action={visitAction}
              state={visitState}
            />
          </TabsContent>

          {/* Offer + Visit Tab */}
          <TabsContent value="offer-visit">
            <OfferWithVisitForm
              propertyId={propertyId}
              sellerId={sellerId}
              sellerName={sellerName}
              propertyPrice={propertyPrice}
              defaultBuyerName={defaultBuyerName}
              defaultBuyerEmail={defaultBuyerEmail}
              shareType={ContactShareType.OfferWithVisit}
              action={offerVisitAction}
              state={offerVisitState}
            />
          </TabsContent>
        </Tabs>

        {/* Footer */}
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-xs text-gray-500">
            <p>• Your information will be sent directly to the seller</p>
            <p>• All communications are secure and private</p>
          </div>
          <Button variant="outline" onClick={onClose}>
            <X className="h-4 w-4 mr-2" />
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Form Components
interface FormProps {
  propertyId: string
  sellerId: string
  sellerName: string
  defaultBuyerName: string
  defaultBuyerEmail: string
  shareType: ContactShareType
  action: any
  state: any
  propertyPrice?: number
}

function ContactForm({ propertyId, sellerId, sellerName, defaultBuyerName, defaultBuyerEmail, shareType, action, state }: FormProps) {
  return (
    <form action={action} className="space-y-4">
      <input type="hidden" name="propertyId" value={propertyId} />
      <input type="hidden" name="sellerId" value={sellerId} />
      <input type="hidden" name="shareType" value={shareType} />

      <BuyerInfoFields
        defaultBuyerName={defaultBuyerName}
        defaultBuyerEmail={defaultBuyerEmail}
        state={state}
      />

      <MessageField
        placeholder={`Hi ${sellerName}, I'm interested in your property. Could you please contact me to discuss further details?`}
        state={state}
      />

      <SubmitButton>
        <Mail className="h-4 w-4 mr-2" />
        Share Contact Information
      </SubmitButton>
    </form>
  )
}

function OfferForm({ propertyId, sellerId, sellerName, propertyPrice, defaultBuyerName, defaultBuyerEmail, shareType, action, state }: FormProps) {
  return (
    <form action={action} className="space-y-4">
      <input type="hidden" name="propertyId" value={propertyId} />
      <input type="hidden" name="sellerId" value={sellerId} />
      <input type="hidden" name="shareType" value={shareType} />

      <BuyerInfoFields
        defaultBuyerName={defaultBuyerName}
        defaultBuyerEmail={defaultBuyerEmail}
        state={state}
      />

      <OfferAmountField propertyPrice={propertyPrice} state={state} />

      <MessageField
        placeholder={`Hi ${sellerName}, I would like to submit an offer for your property. Please review my offer and contact me to discuss.`}
        state={state}
      />

      <SubmitButton>
        <DollarSign className="h-4 w-4 mr-2" />
        Submit Offer
      </SubmitButton>
    </form>
  )
}

function VisitForm({ propertyId, sellerId, sellerName, defaultBuyerName, defaultBuyerEmail, shareType, action, state }: FormProps) {
  return (
    <form action={action} className="space-y-4">
      <input type="hidden" name="propertyId" value={propertyId} />
      <input type="hidden" name="sellerId" value={sellerId} />
      <input type="hidden" name="shareType" value={shareType} />

      <BuyerInfoFields
        defaultBuyerName={defaultBuyerName}
        defaultBuyerEmail={defaultBuyerEmail}
        state={state}
      />

      <VisitSchedulingFields state={state} />

      <MessageField
        placeholder={`Hi ${sellerName}, I would like to schedule a visit to view your property. Please let me know your availability.`}
        state={state}
      />

      <SubmitButton>
        <Calendar className="h-4 w-4 mr-2" />
        Request Visit
      </SubmitButton>
    </form>
  )
}

function OfferWithVisitForm({ propertyId, sellerId, sellerName, propertyPrice, defaultBuyerName, defaultBuyerEmail, shareType, action, state }: FormProps) {
  return (
    <form action={action} className="space-y-4">
      <input type="hidden" name="propertyId" value={propertyId} />
      <input type="hidden" name="sellerId" value={sellerId} />
      <input type="hidden" name="shareType" value={shareType} />

      <BuyerInfoFields
        defaultBuyerName={defaultBuyerName}
        defaultBuyerEmail={defaultBuyerEmail}
        state={state}
      />

      <OfferAmountField propertyPrice={propertyPrice} state={state} />

      <VisitSchedulingFields state={state} />

      <MessageField
        placeholder={`Hi ${sellerName}, I would like to submit an offer and schedule a visit to view your property. Please review my offer and let me know your availability.`}
        state={state}
      />

      <SubmitButton>
        <DollarSign className="h-4 w-4 mr-2" />
        Submit Offer & Request Visit
      </SubmitButton>
    </form>
  )
}

// Field Components
function BuyerInfoFields({ defaultBuyerName, defaultBuyerEmail, state }: { defaultBuyerName: string, defaultBuyerEmail: string, state: any }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="space-y-2">
        <Label htmlFor="buyerName">Your Name *</Label>
        <Input
          id="buyerName"
          name="buyerName"
          defaultValue={defaultBuyerName}
          placeholder="Enter your full name"
          className={state.fieldErrors?.buyerName ? 'border-red-500' : ''}
          required
        />
        {state.fieldErrors?.buyerName && (
          <p className="text-sm text-red-500">{state.fieldErrors.buyerName[0]}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="buyerEmail">Your Email *</Label>
        <Input
          id="buyerEmail"
          name="buyerEmail"
          type="email"
          defaultValue={defaultBuyerEmail}
          placeholder="Enter your email address"
          className={state.fieldErrors?.buyerEmail ? 'border-red-500' : ''}
          required
        />
        {state.fieldErrors?.buyerEmail && (
          <p className="text-sm text-red-500">{state.fieldErrors.buyerEmail[0]}</p>
        )}
      </div>

      <div className="space-y-2 md:col-span-2">
        <Label htmlFor="buyerPhone">Your Phone (Optional)</Label>
        <Input
          id="buyerPhone"
          name="buyerPhone"
          type="tel"
          placeholder="Enter your phone number"
          className={state.fieldErrors?.buyerPhone ? 'border-red-500' : ''}
        />
        {state.fieldErrors?.buyerPhone && (
          <p className="text-sm text-red-500">{state.fieldErrors.buyerPhone[0]}</p>
        )}
      </div>
    </div>
  )
}

function OfferAmountField({ propertyPrice, state }: { propertyPrice?: number, state: any }) {
  return (
    <div className="space-y-2">
      <Label htmlFor="offerAmount">Offer Amount *</Label>
      <div className="relative">
        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          id="offerAmount"
          name="offerAmount"
          type="number"
          min="0"
          step="1000"
          placeholder={propertyPrice ? propertyPrice.toString() : "Enter your offer amount"}
          className={`pl-10 ${state.fieldErrors?.offerAmount ? 'border-red-500' : ''}`}
          required
        />
      </div>
      {propertyPrice && (
        <p className="text-sm text-gray-500">
          Listed price: ${propertyPrice.toLocaleString()}
        </p>
      )}
      {state.fieldErrors?.offerAmount && (
        <p className="text-sm text-red-500">{state.fieldErrors.offerAmount[0]}</p>
      )}
    </div>
  )
}

function VisitSchedulingFields({ state }: { state: any }) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="preferredVisitDate">Preferred Date</Label>
          <Input
            id="preferredVisitDate"
            name="preferredVisitDate"
            type="date"
            min={new Date().toISOString().split('T')[0]}
            className={state.fieldErrors?.preferredVisitDate ? 'border-red-500' : ''}
          />
          {state.fieldErrors?.preferredVisitDate && (
            <p className="text-sm text-red-500">{state.fieldErrors.preferredVisitDate[0]}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="preferredVisitTime">Preferred Time</Label>
          <Input
            id="preferredVisitTime"
            name="preferredVisitTime"
            type="time"
            className={state.fieldErrors?.preferredVisitTime ? 'border-red-500' : ''}
          />
          {state.fieldErrors?.preferredVisitTime && (
            <p className="text-sm text-red-500">{state.fieldErrors.preferredVisitTime[0]}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="schedulingPreference">Scheduling Preference</Label>
        <Textarea
          id="schedulingPreference"
          name="schedulingPreference"
          placeholder="Any specific scheduling preferences or availability notes..."
          rows={2}
          className={state.fieldErrors?.schedulingPreference ? 'border-red-500' : ''}
        />
        {state.fieldErrors?.schedulingPreference && (
          <p className="text-sm text-red-500">{state.fieldErrors.schedulingPreference[0]}</p>
        )}
      </div>
    </div>
  )
}

function MessageField({ placeholder, state }: { placeholder: string, state: any }) {
  return (
    <div className="space-y-2">
      <Label htmlFor="message">Message</Label>
      <Textarea
        id="message"
        name="message"
        placeholder={placeholder}
        rows={3}
        className={state.fieldErrors?.message ? 'border-red-500' : ''}
      />
      {state.fieldErrors?.message && (
        <p className="text-sm text-red-500">{state.fieldErrors.message[0]}</p>
      )}
    </div>
  )
}

function SubmitButton({ children }: { children: React.ReactNode }) {
  return (
    <Button type="submit" className="w-full">
      {children}
    </Button>
  )
}
