/**
 * Users API Service
 * Handles all user-related API calls to the .NET Web API
 */

import { apiClient } from '@/lib/api-client'
import type {
  ApiResponse,
  UserProfile,
  CreateUserRequest,
  UpdateUserRequest,
  UserSearchParams,
  UserSearchResponse
} from '@/types'

export const usersApi = {
  /**
   * Get current user profile
   */
  getCurrentUser: async (): Promise<UserProfile> => {
    const response = await apiClient.get<ApiResponse<UserProfile>>('/api/sonobrokers/users/me')
    if (!response.success || !response.data) {
      throw new Error(response.message || 'Failed to get current user')
    }
    return response.data
  },

  /**
   * Get user by ID
   */
  getUserById: async (id: string): Promise<UserProfile> => {
    const response = await apiClient.get<ApiResponse<UserProfile>>(`/api/sonobrokers/users/${id}`)
    if (!response.success || !response.data) {
      throw new Error(response.message || 'Failed to get user')
    }
    return response.data
  },

  /**
   * Get user by Clerk ID
   */
  getUserByClerkId: async (clerkId: string): Promise<UserProfile> => {
    const response = await apiClient.get<ApiResponse<UserProfile>>(`/api/sonobrokers/users/clerk/${clerkId}`)
    if (!response.success || !response.data) {
      throw new Error(response.message || 'Failed to get user by Clerk ID')
    }
    return response.data
  },

  /**
   * Create new user
   */
  createUser: async (userData: CreateUserRequest): Promise<UserProfile> => {
    const response = await apiClient.post<ApiResponse<UserProfile>>('/api/sonobrokers/users', userData)
    if (!response.success || !response.data) {
      throw new Error(response.message || 'Failed to create user')
    }
    return response.data
  },

  /**
   * Update user profile
   */
  updateUser: async (id: string, userData: UpdateUserRequest): Promise<UserProfile> => {
    const response = await apiClient.put<ApiResponse<UserProfile>>(`/api/sonobrokers/users/${id}`, userData)
    if (!response.success || !response.data) {
      throw new Error(response.message || 'Failed to update user')
    }
    return response.data
  },

  /**
   * Update current user profile
   */
  updateCurrentUser: async (userData: UpdateUserRequest): Promise<UserProfile> => {
    const response = await apiClient.put<ApiResponse<UserProfile>>('/api/sonobrokers/users/me', userData)
    if (!response.success || !response.data) {
      throw new Error(response.message || 'Failed to update profile')
    }
    return response.data
  },

  /**
   * Delete user
   */
  deleteUser: async (id: string): Promise<void> => {
    const response = await apiClient.delete<ApiResponse<void>>(`/api/sonobrokers/users/${id}`)
    if (!response.success) {
      throw new Error(response.message || 'Failed to delete user')
    }
  },

  /**
   * Search users
   */
  searchUsers: async (params: UserSearchParams): Promise<UserSearchResponse> => {
    const response = await apiClient.get<ApiResponse<UserSearchResponse>>('/api/sonobrokers/users/search', params)
    if (!response.success || !response.data) {
      throw new Error(response.message || 'Failed to search users')
    }
    return response.data
  },

  /**
   * Get user login statistics
   */
  getUserLoginStats: async (id: string): Promise<{ isLoggedIn: boolean; lastLoginAt?: string; loginCount?: number }> => {
    const response = await apiClient.get<ApiResponse<any>>(`/api/sonobrokers/users/${id}/login-stats`)
    if (!response.success || !response.data) {
      throw new Error(response.message || 'Failed to get login stats')
    }
    return response.data
  },

  /**
   * Update user login status
   */
  updateLoginStatus: async (id: string, loggedIn: boolean): Promise<void> => {
    const response = await apiClient.patch<ApiResponse<void>>(`/api/sonobrokers/users/${id}/login-status`, { loggedIn })
    if (!response.success) {
      throw new Error(response.message || 'Failed to update login status')
    }
  },

  /**
   * Get users by type (Buyer/Seller)
   */
  getUsersByType: async (userType: 'Buyer' | 'Seller'): Promise<UserProfile[]> => {
    const response = await apiClient.get<ApiResponse<UserProfile[]>>('/api/sonobrokers/users/by-type', { userType })
    if (!response.success || !response.data) {
      throw new Error(response.message || 'Failed to get users by type')
    }
    return response.data
  },

  /**
   * Verify user email
   */
  verifyEmail: async (id: string): Promise<void> => {
    const response = await apiClient.post<ApiResponse<void>>(`/api/sonobrokers/users/${id}/verify-email`)
    if (!response.success) {
      throw new Error(response.message || 'Failed to verify email')
    }
  },

  /**
   * Verify user phone
   */
  verifyPhone: async (id: string): Promise<void> => {
    const response = await apiClient.post<ApiResponse<void>>(`/api/sonobrokers/users/${id}/verify-phone`)
    if (!response.success) {
      throw new Error(response.message || 'Failed to verify phone')
    }
  },

  /**
   * Get user activity
   */
  getUserActivity: async (id: string, page = 1, limit = 20): Promise<any[]> => {
    const response = await apiClient.get<ApiResponse<any[]>>(`/api/sonobrokers/users/${id}/activity`, { page, limit })
    if (!response.success || !response.data) {
      throw new Error(response.message || 'Failed to get user activity')
    }
    return response.data
  },

  /**
   * Get user notifications
   */
  getUserNotifications: async (id: string, page = 1, limit = 20): Promise<any[]> => {
    const response = await apiClient.get<ApiResponse<any[]>>(`/api/sonobrokers/users/${id}/notifications`, { page, limit })
    if (!response.success || !response.data) {
      throw new Error(response.message || 'Failed to get notifications')
    }
    return response.data
  },

  /**
   * Mark notification as read
   */
  markNotificationAsRead: async (userId: string, notificationId: string): Promise<void> => {
    const response = await apiClient.patch<ApiResponse<void>>(`/api/sonobrokers/users/${userId}/notifications/${notificationId}/read`)
    if (!response.success) {
      throw new Error(response.message || 'Failed to mark notification as read')
    }
  }
}

export default usersApi
