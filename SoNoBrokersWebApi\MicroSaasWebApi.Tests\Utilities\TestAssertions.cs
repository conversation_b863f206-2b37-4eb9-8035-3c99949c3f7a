using FluentAssertions;
using FluentAssertions.Execution;
using FluentAssertions.Primitives;
using MicroSaasWebApi.Models.Auth;
using MicroSaasWebApi.Models.SoNoBrokers;
using System.Net;
using System.Security.Claims;
using System.Text.Json;

namespace MicroSaasWebApi.Tests.Utilities
{
    /// <summary>
    /// Custom assertion extensions for domain-specific testing
    /// </summary>
    public static class TestAssertions
    {
        #region General Assertions

        /// <summary>
        /// Asserts that a string is a valid GUID
        /// </summary>
        public static AndConstraint<StringAssertions> BeValidGuid(this StringAssertions assertions, string because = "", params object[] becauseArgs)
        {
            Execute.Assertion
                .ForCondition(Guid.TryParse(assertions.Subject, out _))
                .BecauseOf(because, becauseArgs)
                .FailWith("Expected {context:string} to be a valid GUID{reason}, but found {0}.", assertions.Subject);

            return new AndConstraint<StringAssertions>(assertions);
        }

        /// <summary>
        /// Asserts that a string is a valid email address
        /// </summary>
        public static AndConstraint<StringAssertions> BeValidEmail(this StringAssertions assertions, string because = "", params object[] becauseArgs)
        {
            Execute.Assertion
                .ForCondition(IsValidEmail(assertions.Subject))
                .BecauseOf(because, becauseArgs)
                .FailWith("Expected {context:string} to be a valid email{reason}, but found {0}.", assertions.Subject);

            return new AndConstraint<StringAssertions>(assertions);
        }

        /// <summary>
        /// Asserts that a string is a valid URL
        /// </summary>
        public static AndConstraint<StringAssertions> BeValidUrl(this StringAssertions assertions, string because = "", params object[] becauseArgs)
        {
            Execute.Assertion
                .ForCondition(Uri.TryCreate(assertions.Subject, UriKind.Absolute, out _))
                .BecauseOf(because, becauseArgs)
                .FailWith("Expected {context:string} to be a valid URL{reason}, but found {0}.", assertions.Subject);

            return new AndConstraint<StringAssertions>(assertions);
        }

        /// <summary>
        /// Asserts that a DateTime is recent (within specified timespan)
        /// </summary>
        public static AndConstraint<DateTimeAssertions> BeRecent(this DateTimeAssertions assertions, TimeSpan? within = null, string because = "", params object[] becauseArgs)
        {
            var timespan = within ?? TimeSpan.FromMinutes(5);
            var cutoff = DateTime.UtcNow.Subtract(timespan);

            Execute.Assertion
                .ForCondition(assertions.Subject >= cutoff)
                .BecauseOf(because, becauseArgs)
                .FailWith("Expected {context:DateTime} to be recent (within {0}){reason}, but found {1}.", timespan, assertions.Subject);

            return new AndConstraint<DateTimeAssertions>(assertions);
        }

        #endregion

        #region HTTP Response Assertions

        /// <summary>
        /// Asserts that HTTP response has successful status code
        /// </summary>
        public static AndConstraint<ObjectAssertions> BeSuccessfulResponse(this ObjectAssertions assertions, string because = "", params object[] becauseArgs)
        {
            if (assertions.Subject is HttpResponseMessage response)
            {
                Execute.Assertion
                    .ForCondition(response.IsSuccessStatusCode)
                    .BecauseOf(because, becauseArgs)
                    .FailWith("Expected HTTP response to be successful{reason}, but found status {0}.", response.StatusCode);
            }

            return new AndConstraint<ObjectAssertions>(assertions);
        }

        /// <summary>
        /// Asserts that HTTP response has specific status code
        /// </summary>
        public static AndConstraint<ObjectAssertions> HaveStatusCode(this ObjectAssertions assertions, HttpStatusCode expectedStatusCode, string because = "", params object[] becauseArgs)
        {
            if (assertions.Subject is HttpResponseMessage response)
            {
                Execute.Assertion
                    .ForCondition(response.StatusCode == expectedStatusCode)
                    .BecauseOf(because, becauseArgs)
                    .FailWith("Expected HTTP response to have status {0}{reason}, but found {1}.", expectedStatusCode, response.StatusCode);
            }

            return new AndConstraint<ObjectAssertions>(assertions);
        }

        #endregion

        #region Domain Model Assertions

        /// <summary>
        /// Asserts that a User object is valid
        /// </summary>
        public static AndConstraint<ObjectAssertions> BeValidUser(this ObjectAssertions assertions, string because = "", params object[] becauseArgs)
        {
            if (assertions.Subject is User user)
            {
                Execute.Assertion
                    .ForCondition(!string.IsNullOrEmpty(user.Id))
                    .BecauseOf(because, becauseArgs)
                    .FailWith("Expected User to have valid ID{reason}, but found {0}.", user.Id);

                Execute.Assertion
                    .ForCondition(IsValidEmail(user.Email))
                    .BecauseOf(because, becauseArgs)
                    .FailWith("Expected User to have valid email{reason}, but found {0}.", user.Email);

                Execute.Assertion
                    .ForCondition(!string.IsNullOrEmpty(user.FullName))
                    .BecauseOf(because, becauseArgs)
                    .FailWith("Expected User to have valid full name{reason}, but found {0}.", user.FullName);
            }

            return new AndConstraint<ObjectAssertions>(assertions);
        }

        /// <summary>
        /// Asserts that a Property object is valid
        /// </summary>
        public static AndConstraint<ObjectAssertions> BeValidProperty(this ObjectAssertions assertions, string because = "", params object[] becauseArgs)
        {
            if (assertions.Subject is Property property)
            {
                Execute.Assertion
                    .ForCondition(!string.IsNullOrEmpty(property.Id))
                    .BecauseOf(because, becauseArgs)
                    .FailWith("Expected Property to have valid ID{reason}, but found {0}.", property.Id);

                Execute.Assertion
                    .ForCondition(!string.IsNullOrEmpty(property.Title))
                    .BecauseOf(because, becauseArgs)
                    .FailWith("Expected Property to have valid title{reason}, but found {0}.", property.Title);

                Execute.Assertion
                    .ForCondition(property.Price > 0)
                    .BecauseOf(because, becauseArgs)
                    .FailWith("Expected Property to have positive price{reason}, but found {0}.", property.Price);

                Execute.Assertion
                    .ForCondition(property.Bedrooms > 0)
                    .BecauseOf(because, becauseArgs)
                    .FailWith("Expected Property to have positive bedrooms{reason}, but found {0}.", property.Bedrooms);
            }

            return new AndConstraint<ObjectAssertions>(assertions);
        }

        /// <summary>
        /// Asserts that an Advertiser object is valid
        /// </summary>
        public static AndConstraint<ObjectAssertions> BeValidAdvertiser(this ObjectAssertions assertions, string because = "", params object[] becauseArgs)
        {
            if (assertions.Subject is Advertiser advertiser)
            {
                Execute.Assertion
                    .ForCondition(!string.IsNullOrEmpty(advertiser.Id))
                    .BecauseOf(because, becauseArgs)
                    .FailWith("Expected Advertiser to have valid ID{reason}, but found {0}.", advertiser.Id);

                Execute.Assertion
                    .ForCondition(!string.IsNullOrEmpty(advertiser.BusinessName))
                    .BecauseOf(because, becauseArgs)
                    .FailWith("Expected Advertiser to have valid business name{reason}, but found {0}.", advertiser.BusinessName);

                Execute.Assertion
                    .ForCondition(IsValidEmail(advertiser.Email))
                    .BecauseOf(because, becauseArgs)
                    .FailWith("Expected Advertiser to have valid email{reason}, but found {0}.", advertiser.Email);
            }

            return new AndConstraint<ObjectAssertions>(assertions);
        }

        #endregion

        #region Authentication Assertions

        /// <summary>
        /// Asserts that ClaimsPrincipal is authenticated
        /// </summary>
        public static AndConstraint<ObjectAssertions> BeAuthenticated(this ObjectAssertions assertions, string because = "", params object[] becauseArgs)
        {
            if (assertions.Subject is ClaimsPrincipal principal)
            {
                Execute.Assertion
                    .ForCondition(principal.Identity?.IsAuthenticated == true)
                    .BecauseOf(because, becauseArgs)
                    .FailWith("Expected ClaimsPrincipal to be authenticated{reason}.");
            }

            return new AndConstraint<ObjectAssertions>(assertions);
        }

        /// <summary>
        /// Asserts that ClaimsPrincipal has specific role
        /// </summary>
        public static AndConstraint<ObjectAssertions> HaveRole(this ObjectAssertions assertions, string role, string because = "", params object[] becauseArgs)
        {
            if (assertions.Subject is ClaimsPrincipal principal)
            {
                Execute.Assertion
                    .ForCondition(principal.IsInRole(role))
                    .BecauseOf(because, becauseArgs)
                    .FailWith("Expected ClaimsPrincipal to have role {0}{reason}.", role);
            }

            return new AndConstraint<ObjectAssertions>(assertions);
        }

        /// <summary>
        /// Asserts that ClaimsPrincipal has specific claim
        /// </summary>
        public static AndConstraint<ObjectAssertions> HaveClaim(this ObjectAssertions assertions, string claimType, string claimValue, string because = "", params object[] becauseArgs)
        {
            if (assertions.Subject is ClaimsPrincipal principal)
            {
                var claim = principal.FindFirst(claimType);
                Execute.Assertion
                    .ForCondition(claim?.Value == claimValue)
                    .BecauseOf(because, becauseArgs)
                    .FailWith("Expected ClaimsPrincipal to have claim {0} with value {1}{reason}, but found {2}.", claimType, claimValue, claim?.Value);
            }

            return new AndConstraint<ObjectAssertions>(assertions);
        }

        #endregion

        #region JSON Assertions

        /// <summary>
        /// Asserts that string is valid JSON
        /// </summary>
        public static AndConstraint<StringAssertions> BeValidJson(this StringAssertions assertions, string because = "", params object[] becauseArgs)
        {
            Execute.Assertion
                .ForCondition(IsValidJson(assertions.Subject))
                .BecauseOf(because, becauseArgs)
                .FailWith("Expected {context:string} to be valid JSON{reason}, but parsing failed.");

            return new AndConstraint<StringAssertions>(assertions);
        }

        /// <summary>
        /// Asserts that JSON string contains specific property
        /// </summary>
        public static AndConstraint<StringAssertions> ContainJsonProperty(this StringAssertions assertions, string propertyName, string because = "", params object[] becauseArgs)
        {
            if (IsValidJson(assertions.Subject))
            {
                var jsonDoc = JsonDocument.Parse(assertions.Subject);
                Execute.Assertion
                    .ForCondition(jsonDoc.RootElement.TryGetProperty(propertyName, out _))
                    .BecauseOf(because, becauseArgs)
                    .FailWith("Expected JSON to contain property {0}{reason}.", propertyName);
            }

            return new AndConstraint<StringAssertions>(assertions);
        }

        #endregion

        #region Collection Assertions

        /// <summary>
        /// Asserts that collection has items and all items satisfy condition
        /// </summary>
        public static AndConstraint<GenericCollectionAssertions<T>> HaveItemsAndAllSatisfy<T>(
            this GenericCollectionAssertions<T> assertions, 
            Action<T> inspector, 
            string because = "", 
            params object[] becauseArgs)
        {
            Execute.Assertion
                .ForCondition(assertions.Subject.Any())
                .BecauseOf(because, becauseArgs)
                .FailWith("Expected collection to have items{reason}, but found empty collection.");

            foreach (var item in assertions.Subject)
            {
                inspector(item);
            }

            return new AndConstraint<GenericCollectionAssertions<T>>(assertions);
        }

        /// <summary>
        /// Asserts that collection is properly paginated
        /// </summary>
        public static AndConstraint<ObjectAssertions> BePaginatedCorrectly(
            this ObjectAssertions assertions, 
            int expectedPage, 
            int expectedLimit, 
            int expectedTotal,
            string because = "", 
            params object[] becauseArgs)
        {
            // This would be used with pagination response objects
            // Implementation depends on your pagination response structure
            return new AndConstraint<ObjectAssertions>(assertions);
        }

        #endregion

        #region Performance Assertions

        /// <summary>
        /// Asserts that operation completed within time limit
        /// </summary>
        public static AndConstraint<TimeSpanAssertions> BeWithinPerformanceLimit(
            this TimeSpanAssertions assertions, 
            TimeSpan limit, 
            string because = "", 
            params object[] becauseArgs)
        {
            Execute.Assertion
                .ForCondition(assertions.Subject <= limit)
                .BecauseOf(because, becauseArgs)
                .FailWith("Expected operation to complete within {0}{reason}, but took {1}.", limit, assertions.Subject);

            return new AndConstraint<TimeSpanAssertions>(assertions);
        }

        #endregion

        #region Private Helper Methods

        private static bool IsValidEmail(string? email)
        {
            if (string.IsNullOrEmpty(email)) return false;
            
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private static bool IsValidJson(string? json)
        {
            if (string.IsNullOrEmpty(json)) return false;
            
            try
            {
                JsonDocument.Parse(json);
                return true;
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }

    /// <summary>
    /// Fluent assertion extensions for test scenarios
    /// </summary>
    public static class ScenarioAssertions
    {
        /// <summary>
        /// Asserts that a test scenario is properly set up
        /// </summary>
        public static void ShouldBeValidScenario(this UserScenario scenario)
        {
            scenario.Should().NotBeNull();
            scenario.User.Should().BeValidUser();
            
            if (scenario.Properties.Any())
            {
                scenario.Properties.Should().AllSatisfy(p => p.Should().BeValidProperty());
            }
            
            if (scenario.Advertisers.Any())
            {
                scenario.Advertisers.Should().AllSatisfy(a => a.Should().BeValidAdvertiser());
            }
        }

        /// <summary>
        /// Asserts that API response contains expected data structure
        /// </summary>
        public static void ShouldHaveValidApiStructure<T>(this ApiResponse<T> response)
        {
            response.Should().NotBeNull();
            response.IsSuccess.Should().BeTrue();
            response.Data.Should().NotBeNull();
            response.Error.Should().BeNullOrEmpty();
        }
    }
}
