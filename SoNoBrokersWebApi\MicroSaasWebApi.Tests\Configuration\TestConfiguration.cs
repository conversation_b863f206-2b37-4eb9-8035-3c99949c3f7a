using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MicroSaasWebApi.Tests.Common;

namespace MicroSaasWebApi.Tests.Configuration
{
    public static class TestConfiguration
    {
        /// <summary>
        /// Creates a test configuration with default values
        /// </summary>
        public static IConfiguration CreateTestConfiguration()
        {
            var configurationBuilder = new ConfigurationBuilder();

            // Add in-memory configuration
            configurationBuilder.AddInMemoryCollection(GetDefaultTestSettings());

            // Add environment-specific test settings if they exist
            configurationBuilder.AddJsonFile("appsettings.Test.json", optional: true);

            // Add environment variables for CI/CD scenarios
            configurationBuilder.AddEnvironmentVariables("TEST_");

            return configurationBuilder.Build();
        }

        /// <summary>
        /// Gets default test configuration settings
        /// </summary>
        public static Dictionary<string, string?> GetDefaultTestSettings()
        {
            return new Dictionary<string, string?>
            {
                // Database settings
                ["ConnectionStrings:DefaultConnection"] = TestHelpers.GetTestConnectionString(),
                ["ConnectionStrings:SupabaseConnection"] = TestHelpers.GetTestConnectionString(),

                // Clerk authentication settings
                ["Clerk:SecretKey"] = "test-secret-key",
                ["Clerk:PublishableKey"] = "test-publishable-key",
                ["Clerk:ApiUrl"] = "https://api.clerk.dev/v1",
                ["Clerk:WebhookSecret"] = "test-webhook-secret",

                // Test-specific settings
                ["TestSettings:UseInMemoryDatabase"] = "true",
                ["TestSettings:SeedTestData"] = "true",
                ["TestSettings:EnableDetailedLogging"] = "true",
                ["TestSettings:MockExternalServices"] = "true",

                // External service settings (mocked in tests)
                ["ExternalServices:GeoLocationApi"] = "https://test-geo-api.com",
                ["ExternalServices:PropertyValuationApi"] = "https://test-valuation-api.com",
                ["ExternalServices:EmailService"] = "https://test-email-api.com",

                // Feature flags for testing
                ["FeatureFlags:EnableAIPropertyImport"] = "true",
                ["FeatureFlags:EnablePropertyValuation"] = "true",
                ["FeatureFlags:EnableConciergeService"] = "true",
                ["FeatureFlags:EnableAdvancedSearch"] = "true",

                // Logging settings
                ["Logging:LogLevel:Default"] = "Information",
                ["Logging:LogLevel:Microsoft"] = "Warning",
                ["Logging:LogLevel:Microsoft.Hosting.Lifetime"] = "Information",
                ["Logging:LogLevel:MicroSaasWebApi"] = "Debug",

                // API settings
                ["Api:BaseUrl"] = "https://localhost:7001",
                ["Api:Version"] = "v1",
                ["Api:EnableSwagger"] = "true",
                ["Api:EnableCors"] = "true",

                // Security settings
                ["Security:RequireHttps"] = "false",
                ["Security:EnableRateLimiting"] = "false",
                ["Security:JwtExpirationMinutes"] = "60",

                // Performance settings
                ["Performance:EnableCaching"] = "false",
                ["Performance:CacheExpirationMinutes"] = "5",
                ["Performance:MaxPageSize"] = "100",

                // Test data settings
                ["TestData:DefaultUserId"] = "test-user-id",
                ["TestData:DefaultUserEmail"] = "<EMAIL>",
                ["TestData:DefaultAdminId"] = "test-admin-id",
                ["TestData:DefaultAdminEmail"] = "<EMAIL>",

                // Contact Sharing test data
                ["TestData:ContactSharing:TestBuyerId"] = "test-buyer-123",
                ["TestData:ContactSharing:TestSellerId"] = "test-seller-123",
                ["TestData:ContactSharing:TestPropertyId"] = "test-property-123",
                ["TestData:ContactSharing:TestContactShareId"] = "test-contact-share-123",
                ["TestData:ContactSharing:TestBuyerName"] = "Test Buyer",
                ["TestData:ContactSharing:TestSellerName"] = "Test Seller",
                ["TestData:ContactSharing:TestBuyerEmail"] = "<EMAIL>",
                ["TestData:ContactSharing:TestSellerEmail"] = "<EMAIL>",
                ["TestData:ContactSharing:TestPropertyTitle"] = "Test Property",
                ["TestData:ContactSharing:TestPropertyAddress"] = "123 Test Street, Test City, Test State",
                ["TestData:ContactSharing:TestPropertyPrice"] = "500000",
                ["TestData:ContactSharing:TestOfferAmount"] = "475000",
                ["TestData:ContactSharing:TestMessage"] = "I'm interested in this property for testing purposes.",
                ["TestData:ContactSharing:TestVisitDate"] = "2024-12-31",
                ["TestData:ContactSharing:TestVisitTime"] = "14:00",

                // Contact Sharing feature settings
                ["Features:ContactSharing:Enabled"] = "true",
                ["Features:ContactSharing:MaxContactSharesPerDay"] = "10",
                ["Features:ContactSharing:MaxOffersPerProperty"] = "5",
                ["Features:ContactSharing:EmailCooldownMinutes"] = "0",
                ["Features:ContactSharing:AutoExpireDays"] = "30",
                ["Features:ContactSharing:EnableEmailNotifications"] = "false",

                // Mock service settings
                ["MockServices:DelayMs"] = "100",
                ["MockServices:FailureRate"] = "0.0",
                ["MockServices:EnableRandomFailures"] = "false"
            };
        }

        /// <summary>
        /// Configures test services with appropriate mocks and overrides
        /// </summary>
        public static void ConfigureTestServices(IServiceCollection services, IConfiguration configuration)
        {
            // Configure logging for tests
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();

                if (configuration.GetValue<bool>("TestSettings:EnableDetailedLogging"))
                {
                    builder.SetMinimumLevel(LogLevel.Debug);
                }
                else
                {
                    builder.SetMinimumLevel(LogLevel.Information);
                }
            });

            // Add test-specific HTTP clients
            services.AddHttpClient("test-client", client =>
            {
                client.BaseAddress = new Uri(configuration["Api:BaseUrl"] ?? "https://localhost:7001");
                client.Timeout = TimeSpan.FromSeconds(30);
            });

            // Configure test database context if needed
            if (configuration.GetValue<bool>("TestSettings:UseInMemoryDatabase"))
            {
                // Database context configuration would go here
                // This is handled in the TestWebApplicationFactory
            }
        }

        /// <summary>
        /// Gets test environment variables for CI/CD
        /// </summary>
        public static Dictionary<string, string> GetCIEnvironmentVariables()
        {
            return new Dictionary<string, string>
            {
                ["TEST_DATABASE_URL"] = Environment.GetEnvironmentVariable("TEST_DATABASE_URL") ?? TestHelpers.GetTestConnectionString(),
                ["TEST_CLERK_SECRET_KEY"] = Environment.GetEnvironmentVariable("TEST_CLERK_SECRET_KEY") ?? "test-secret-key",
                ["TEST_ENABLE_INTEGRATION_TESTS"] = Environment.GetEnvironmentVariable("TEST_ENABLE_INTEGRATION_TESTS") ?? "true",
                ["TEST_PARALLEL_EXECUTION"] = Environment.GetEnvironmentVariable("TEST_PARALLEL_EXECUTION") ?? "false",
                ["TEST_TIMEOUT_SECONDS"] = Environment.GetEnvironmentVariable("TEST_TIMEOUT_SECONDS") ?? "300"
            };
        }

        /// <summary>
        /// Validates test configuration and environment
        /// </summary>
        public static void ValidateTestEnvironment(IConfiguration configuration)
        {
            var requiredSettings = new[]
            {
                "ConnectionStrings:SupabaseConnection",
                "Clerk:SecretKey",
                "TestSettings:UseInMemoryDatabase"
            };

            var missingSettings = new List<string>();

            foreach (var setting in requiredSettings)
            {
                if (string.IsNullOrEmpty(configuration[setting]))
                {
                    missingSettings.Add(setting);
                }
            }

            if (missingSettings.Any())
            {
                throw new InvalidOperationException(
                    $"Missing required test configuration settings: {string.Join(", ", missingSettings)}");
            }
        }

        /// <summary>
        /// Creates configuration for specific test scenarios
        /// </summary>
        public static IConfiguration CreateScenarioConfiguration(TestScenario scenario)
        {
            var baseSettings = GetDefaultTestSettings();

            switch (scenario)
            {
                case TestScenario.DatabaseIntegration:
                    baseSettings["TestSettings:UseInMemoryDatabase"] = "false";
                    baseSettings["TestSettings:SeedTestData"] = "true";
                    break;

                case TestScenario.ExternalServiceMocking:
                    baseSettings["TestSettings:MockExternalServices"] = "true";
                    baseSettings["MockServices:DelayMs"] = "50";
                    break;

                case TestScenario.PerformanceTesting:
                    baseSettings["Performance:EnableCaching"] = "true";
                    baseSettings["TestSettings:EnableDetailedLogging"] = "false";
                    baseSettings["MockServices:DelayMs"] = "0";
                    break;

                case TestScenario.SecurityTesting:
                    baseSettings["Security:RequireHttps"] = "true";
                    baseSettings["Security:EnableRateLimiting"] = "true";
                    baseSettings["Clerk:SecretKey"] = "secure-test-key";
                    break;

                case TestScenario.ErrorHandling:
                    baseSettings["MockServices:FailureRate"] = "0.1";
                    baseSettings["MockServices:EnableRandomFailures"] = "true";
                    break;
            }

            return new ConfigurationBuilder()
                .AddInMemoryCollection(baseSettings)
                .Build();
        }

        /// <summary>
        /// Gets test-specific connection strings for different databases
        /// </summary>
        public static Dictionary<string, string> GetTestConnectionStrings()
        {
            return new Dictionary<string, string>
            {
                ["PostgreSQL"] = "Host=localhost;Port=5432;Database=sonobrokers_test;Username=********;Password=********;",
                ["SQLite"] = "Data Source=:memory:",
                ["InMemory"] = "InMemory"
            };
        }
    }

    /// <summary>
    /// Enumeration of different test scenarios
    /// </summary>
    public enum TestScenario
    {
        Default,
        DatabaseIntegration,
        ExternalServiceMocking,
        PerformanceTesting,
        SecurityTesting,
        ErrorHandling
    }

    /// <summary>
    /// Test configuration extensions
    /// </summary>
    public static class TestConfigurationExtensions
    {
        public static bool IsIntegrationTestEnabled(this IConfiguration configuration)
        {
            return configuration.GetValue<bool>("TEST_ENABLE_INTEGRATION_TESTS", true);
        }

        public static bool IsParallelExecutionEnabled(this IConfiguration configuration)
        {
            return configuration.GetValue<bool>("TEST_PARALLEL_EXECUTION", false);
        }

        public static TimeSpan GetTestTimeout(this IConfiguration configuration)
        {
            var timeoutSeconds = configuration.GetValue<int>("TEST_TIMEOUT_SECONDS", 300);
            return TimeSpan.FromSeconds(timeoutSeconds);
        }

        public static string GetTestDatabaseUrl(this IConfiguration configuration)
        {
            return configuration["TEST_DATABASE_URL"] ?? TestHelpers.GetTestConnectionString();
        }
    }
}
