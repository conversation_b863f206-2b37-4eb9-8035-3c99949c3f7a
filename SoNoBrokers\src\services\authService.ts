// MIGRATED TO .NET WEB API - Use auth-actions.ts instead
// import prisma from '@/lib/prisma'
// import { User } from '@/types'

/**
 * Authentication Service for tracking user login/logout status
 * MIGRATED TO .NET WEB API - Use auth-actions.ts instead
 *
 * @deprecated Use auth-actions.ts Server Actions instead
 */
export class AuthService {
  /**
   * Track user login - updates lastLoginAt and loggedIn status
   * @deprecated Use trackLoginAction from auth-actions.ts instead
   */
  static async trackLogin(_userId: string): Promise<void> {
    throw new Error('MIGRATED TO .NET WEB API - Use trackLoginAction from auth-actions.ts instead');
  }

  /**
   * Track user logout - updates loggedIn status
   * @deprecated Use trackLogoutAction from auth-actions.ts instead
   */
  static async trackLogout(_userId: string): Promise<void> {
    throw new Error('MIGRATED TO .NET WEB API - Use trackLogoutAction from auth-actions.ts instead');
  }

  /**
   * Get user login status
   * @deprecated Use getLoginStatsAction from auth-actions.ts instead
   */
  static async getUserLoginStatus(_userId: string): Promise<boolean> {
    throw new Error('MIGRATED TO .NET WEB API - Use getLoginStatsAction from auth-actions.ts instead');
  }

  /**
   * Get last login time for user
   * @deprecated Use getLoginStatsAction from auth-actions.ts instead
   */
  static async getLastLoginTime(_userId: string): Promise<Date | null> {
    throw new Error('MIGRATED TO .NET WEB API - Use getLoginStatsAction from auth-actions.ts instead');
  }

  /**
   * Check if user is currently logged in
   * @deprecated Use getLoginStatsAction from auth-actions.ts instead
   */
  static async isUserLoggedIn(_userId: string): Promise<boolean> {
    throw new Error('MIGRATED TO .NET WEB API - Use getLoginStatsAction from auth-actions.ts instead');
  }

  /**
   * Get login statistics for user
   * @deprecated Use getLoginStatsAction from auth-actions.ts instead
   */
  static async getLoginStats(_userId: string): Promise<{
    isLoggedIn: boolean
    lastLoginAt: Date | null
    loginCount?: number
  }> {
    throw new Error('MIGRATED TO .NET WEB API - Use getLoginStatsAction from auth-actions.ts instead');
  }
}
