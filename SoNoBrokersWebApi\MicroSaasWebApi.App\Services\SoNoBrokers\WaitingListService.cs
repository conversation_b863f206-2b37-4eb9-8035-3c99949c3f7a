using Dapper;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using System.Text.Json;

namespace MicroSaasWebApi.Services.SoNoBrokers
{
    public class WaitingListService : IWaitingListService
    {
        private readonly MicroSaasWebApi.App.Context.IDapperDbContext _dbContext;
        private readonly ILogger<WaitingListService> _logger;
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;

        public WaitingListService(
            MicroSaasWebApi.App.Context.IDapperDbContext dbContext,
            ILogger<WaitingListService> logger,
            IConfiguration configuration,
            HttpClient httpClient)
        {
            _dbContext = dbContext;
            _logger = logger;
            _configuration = configuration;
            _httpClient = httpClient;
        }

        public async Task<WaitingList> AddToWaitingListAsync(string email)
        {
            try
            {
                // Check if email already exists
                var existingEntry = await GetWaitingListEntryByEmailAsync(email);
                if (existingEntry != null)
                {
                    _logger.LogInformation("Email {Email} already exists in waiting list", email);
                    return existingEntry;
                }

                // Create new waiting list entry
                var entry = new WaitingList
                {
                    Id = Guid.NewGuid().ToString(),
                    Email = email,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    Subscribed = true
                };

                var sql = @"
                    INSERT INTO snb.waiting_list (id, email, subscribed, created_at, updated_at)
                    VALUES (@Id, @Email, @Subscribed, @CreatedAt, @UpdatedAt)";

                await _dbContext.ExecuteAsync(sql, entry);

                // Try to add to external email service (like Resend)
                await TryAddToEmailServiceAsync(email);

                _logger.LogInformation("Added email {Email} to waiting list", email);
                return entry;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding email {Email} to waiting list", email);
                throw;
            }
        }

        public async Task<WaitingList?> GetWaitingListEntryByEmailAsync(string email)
        {
            try
            {
                var sql = "SELECT * FROM snb.waiting_list WHERE email = @email";

                return await _dbContext.QueryFirstOrDefaultAsync<WaitingList>(sql, new { email });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting waiting list entry for email {Email}", email);
                throw;
            }
        }

        public async Task<IEnumerable<WaitingList>> GetAllWaitingListEntriesAsync()
        {
            try
            {
                var sql = "SELECT * FROM snb.waiting_list ORDER BY created_at DESC";

                return await _dbContext.QueryAsync<WaitingList>(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all waiting list entries");
                throw;
            }
        }

        public async Task<bool> RemoveFromWaitingListAsync(string email)
        {
            try
            {
                var sql = "DELETE FROM snb.waiting_list WHERE email = @email";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, new { email });

                _logger.LogInformation("Removed email {Email} from waiting list", email);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing email {Email} from waiting list", email);
                throw;
            }
        }

        private async Task TryAddToEmailServiceAsync(string email)
        {
            try
            {
                var resendApiKey = _configuration["Resend:ApiKey"];
                var audienceId = _configuration["Resend:AudienceId"];

                if (string.IsNullOrEmpty(resendApiKey) || string.IsNullOrEmpty(audienceId))
                {
                    _logger.LogWarning("Resend configuration missing, skipping external email service");
                    return;
                }

                var requestBody = new
                {
                    email = email,
                    audience_id = audienceId
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {resendApiKey}");

                var response = await _httpClient.PostAsync("https://api.resend.com/audiences/contacts", content);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Successfully added email {Email} to external email service", email);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("Failed to add email {Email} to external service: {Error}", email, errorContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error adding email {Email} to external email service", email);
                // Don't throw - this is not critical for the core functionality
            }
        }
    }


}
