{"format": 1, "restore": {"C:\\Projects\\SoNoBrokersRoot\\SoNoBrokersWebApi\\MicroSaasWebApi.App\\MicroSaasWebApi.App.csproj": {}}, "projects": {"C:\\Projects\\SoNoBrokersRoot\\SoNoBrokersWebApi\\MicroSaasWebApi.App\\MicroSaasWebApi.App.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\SoNoBrokersRoot\\SoNoBrokersWebApi\\MicroSaasWebApi.App\\MicroSaasWebApi.App.csproj", "projectName": "MicroSaasWebApi.App", "projectPath": "C:\\Projects\\SoNoBrokersRoot\\SoNoBrokersWebApi\\MicroSaasWebApi.App\\MicroSaasWebApi.App.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\SoNoBrokersRoot\\SoNoBrokersWebApi\\MicroSaasWebApi.App\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"], "warnNotAsError": ["NU1605", "NU1902"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AspNetCore.HealthChecks.Npgsql": {"target": "Package", "version": "[8.0.2, )"}, "AspNetCore.HealthChecks.UI": {"target": "Package", "version": "[8.0.2, )"}, "AspNetCore.HealthChecks.UI.Client": {"target": "Package", "version": "[8.0.1, )"}, "AspNetCore.HealthChecks.UI.Core": {"target": "Package", "version": "[8.0.1, )"}, "AspNetCore.HealthChecks.UI.InMemory.Storage": {"target": "Package", "version": "[8.0.1, )"}, "AspNetCore.HealthChecks.Uris": {"target": "Package", "version": "[8.0.1, )"}, "AspNetCoreRateLimit": {"target": "Package", "version": "[4.0.2, )"}, "Azure.Extensions.AspNetCore.Configuration.Secrets": {"target": "Package", "version": "[1.3.1, )"}, "Azure.Identity": {"target": "Package", "version": "[1.12.0, )"}, "Azure.Security.KeyVault.Secrets": {"target": "Package", "version": "[4.6.0, )"}, "Azure.Storage.Blobs": {"target": "Package", "version": "[12.24.0, )"}, "Dapper": {"target": "Package", "version": "[2.1.35, )"}, "DotNetEnv": {"target": "Package", "version": "[3.0.0, )"}, "Hangfire.AspNetCore": {"target": "Package", "version": "[1.8.5, )"}, "Hangfire.Core": {"target": "Package", "version": "[1.8.5, )"}, "Hangfire.PostgreSql": {"target": "Package", "version": "[1.20.0, )"}, "Markdig": {"target": "Package", "version": "[0.34.0, )"}, "Microsoft.AspNetCore.Authentication.AzureADB2C.UI": {"target": "Package", "version": "[6.0.26, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.Authentication.OpenIdConnect": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.DataProtection": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Azure.AppConfiguration.AspNetCore": {"target": "Package", "version": "[7.1.0, )"}, "Microsoft.Azure.KeyVault": {"target": "Package", "version": "[3.0.5, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Configuration.AzureAppConfiguration": {"target": "Package", "version": "[7.1.0, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Graph": {"target": "Package", "version": "[5.80.0, )"}, "Microsoft.Identity.Web": {"target": "Package", "version": "[2.16.1, )"}, "Microsoft.Identity.Web.UI": {"target": "Package", "version": "[2.16.1, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.19.6, )"}, "NWebsec.AspNetCore.Middleware": {"target": "Package", "version": "[3.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.0, )"}, "QRCoder": {"target": "Package", "version": "[1.4.3, )"}, "RestSharp": {"target": "Package", "version": "[112.0.0, )"}, "Scalar.AspNetCore": {"target": "Package", "version": "[1.2.42, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "Stripe.net": {"target": "Package", "version": "[43.15.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.6.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}