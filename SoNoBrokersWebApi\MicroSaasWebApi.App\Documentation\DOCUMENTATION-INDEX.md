# 📚 SoNoBrokers Web API - Documentation Index

## 🎯 Start Here

Choose your path based on your immediate needs:

| I want to...                         | Go to...                                                      | Time needed |
| ------------------------------------ | ------------------------------------------------------------- | ----------- |
| **Get the SoNoBrokers API running ASAP** | [🚀 QUICK-START.md](./QUICK-START.md)                         | 5 minutes   |
| **Understand the API endpoints**     | [📋 SoNoBrokers-API.md](./SoNoBrokers-API.md)                | 15 minutes  |
| **Fix build/runtime issues**         | [🔧 SETUP-TROUBLESHOOTING.md](./SETUP-TROUBLESHOOTING.md)     | As needed   |
| **See the big picture**              | [📖 README.md](./README.md)                                   | 10 minutes  |

## 📋 Complete Documentation Structure

### 🚀 Getting Started

- **[QUICK-START.md](./QUICK-START.md)** - 5-minute setup with 3 execution options
- **[README.md](./README.md)** - Project overview and features
- **[PROJECT-EXECUTION-GUIDE.md](./PROJECT-EXECUTION-GUIDE.md)** - Comprehensive execution guide
- **[CROSS-PLATFORM-GUIDE.md](./CROSS-PLATFORM-GUIDE.md)** - Windows, Mac, Linux execution

### 🏗️ Architecture Documentation

- **[ARCHITECTURE-DOCUMENTATION.md](./ARCHITECTURE-DOCUMENTATION.md)** - Complete architecture overview
- **[CONTROLLER-ARCHITECTURE-GUIDE.md](./CONTROLLER-ARCHITECTURE-GUIDE.md)** - Controller architecture following SOLID principles
- **[MULTI-TENANT-IMPLEMENTATION-GUIDE.md](./MULTI-TENANT-IMPLEMENTATION-GUIDE.md)** - Multi-tenant setup guide
- **[MIDDLEWARE-PATTERNS-GUIDE.md](./MIDDLEWARE-PATTERNS-GUIDE.md)** - Middleware ordering patterns
- **[DOTNET9-PATTERNS-GUIDE.md](./DOTNET9-PATTERNS-GUIDE.md)** - Modern .NET 9 patterns

### 🔧 Setup & Configuration

- **[CONFIGURATION-STRATEGY-GUIDE.md](./CONFIGURATION-STRATEGY-GUIDE.md)** - Modern configuration management and deployment strategy
- **[SETUP-TROUBLESHOOTING.md](./SETUP-TROUBLESHOOTING.md)** - Common issues and solutions
- **[.env](./env.example)** - Environment variables template
- **[Directory.Build.props](./Directory.Build.props)** - Central package management

### 🛠 Build & Setup

- **Standard .NET Commands**: Use `dotnet build`, `dotnet run`, `dotnet test` for all operations
- **Database Setup**: Use Entity Framework commands: `dotnet ef database update`
- **Package Management**: Use `dotnet add package` and `dotnet remove package`
- **Cross-Platform**: All operations work on Windows, Mac, and Linux

### 🔧 Configuration & API Testing

- **[MicroSaasWebApi-Config/README.md](../MicroSaasWebApi-Config/README.md)** - Configuration project documentation
- **[Postman Collection](../MicroSaasWebApi-Config/Postman/MicroSaaS-API-Collection.postman_collection.json)** - Complete API testing collection
- **[Environment Templates](../MicroSaasWebApi-Config/Environments/)** - Environment-specific configurations
- **[API Endpoints](../MicroSaasWebApi-Config/Templates/api-endpoints.json)** - Complete API endpoint definitions

### 📚 SoNoBrokers API Documentation

- **[SoNoBrokers API](./SoNoBrokers-API.md)** - Complete SoNoBrokers API reference and integration guide
- **[API Reference](./API-REFERENCE.md)** - Detailed API endpoint reference with examples
- **[Contact Sharing API](./CONTACT-SHARING-API.md)** - Buyer-seller contact sharing system
- **[Property Scheduling API](./PROPERTY-SCHEDULING-API.md)** - Property visit scheduling and QR verification
- **[Interactive API Docs](https://localhost:7163/scalar/v1)** - Live API documentation (when running)
- **[React-API Integration Guide](../../SoNoBrokers/docs/feature-api-mapping.md)** - React components to API mapping

### 🚀 CI/CD Pipelines

- **[azure_pipelines/README.md](../azure_pipelines/README.md)** - Multi-tenant pipeline documentation
- **[dev-environment-pipeline.yml](../azure_pipelines/dev-environment-pipeline.yml)** - Development environment pipeline
- **[uat-environment-pipeline.yml](../azure_pipelines/uat-environment-pipeline.yml)** - UAT environment pipeline
- **[prod-environment-pipeline.yml](../azure_pipelines/prod-environment-pipeline.yml)** - Production environment pipeline
- **[multi-tenant-orchestrator.yml](../azure_pipelines/multi-tenant-orchestrator.yml)** - Multi-tenant deployment orchestrator
- **[tenant-configurations.yml](../azure_pipelines/tenant-configurations.yml)** - Tenant-specific configurations

### 📁 Project Structure

- **[MicroSaasWebApi.Full/](./MicroSaasWebApi.Full/)** - Main API project
- **[MicroSaasWebApi.Full.Tests.Unit/](./MicroSaasWebApi.Full.Tests.Unit/)** - Unit tests
- **[MicroSaasWebApiUI/](./MicroSaasWebApiUI/)** - Next.js frontend
- **[MicroSaasWebApi-Config/](./MicroSaasWebApi-Config/)** - Configuration & API testing
- **[azure_pipelines/](./azure_pipelines/)** - Multi-tenant CI/CD pipelines

## 🎯 Quick Navigation by Scenario

### 🆕 First Time Setup

1. [QUICK-START.md](./QUICK-START.md) - Follow Option 1 (Solution-Level)
2. Create `.env` file with your credentials
3. Run `.\build-solution.ps1 -All`
4. Run `.\setup-database.ps1`
5. Run `.\build-solution.ps1 -Run`

### 👨‍💻 Daily Development

1. [PROJECT-EXECUTION-GUIDE.md](./PROJECT-EXECUTION-GUIDE.md) - Daily Development Workflow
2. Use Option 3 (Project-Level) for fastest iteration
3. Run `cd MicroSaasWebApi.Full && dotnet watch run`

### 🐛 Troubleshooting

1. [SETUP-TROUBLESHOOTING.md](./SETUP-TROUBLESHOOTING.md) - Find your specific issue
2. Try the "Nuclear option": `dotnet nuget locals all --clear && .\build-solution.ps1 -Clean -Restore -Build`
3. Check logs and error messages

### 🚀 Production Deployment

1. [PROJECT-EXECUTION-GUIDE.md](./PROJECT-EXECUTION-GUIDE.md) - Deployment section
2. Use `dotnet publish --configuration Release`
3. Configure production environment variables

## 🔄 Execution Options Summary

### Option 1: Solution-Level (Recommended for Teams)

```powershell
# From: C:\Projects\MicroSaasWebApiRoot
.\build-solution.ps1 -All
.\build-solution.ps1 -Run
```

**Best for**: Complete builds, CI/CD, team development

### Option 2: Direct Commands (Manual Control)

```powershell
# From: C:\Projects\MicroSaasWebApiRoot
dotnet clean MicroSaasWebApiRoot.sln
dotnet restore MicroSaasWebApiRoot.sln
dotnet build MicroSaasWebApiRoot.sln
dotnet run --project MicroSaasWebApi.Full\MicroSaasWebApi.csproj
```

**Best for**: Debugging, custom configurations, learning

### Option 3: Project-Level (Fast Development)

```powershell
# From: C:\Projects\MicroSaasWebApiRoot\MicroSaasWebApi.Full
.\build.ps1 -All
.\build.ps1 -Run
# OR
dotnet watch run
```

**Best for**: Rapid development, hot reload, quick testing

## 🌐 Application Access Points

After successful startup:

| Service           | URL                                           | Purpose                    |
| ----------------- | --------------------------------------------- | -------------------------- |
| **Main API**      | https://localhost:7000                        | Primary API endpoint       |
| **Swagger UI**    | https://localhost:7000/swagger                | Interactive API docs       |
| **ReDoc**         | https://localhost:7000/redoc                  | Alternative API docs       |
| **Health Check**  | https://localhost:7000/health                 | System health status       |
| **Configuration** | https://localhost:7000/api/configuration/info | App configuration          |
| **Hangfire**      | https://localhost:7000/hangfire               | Background jobs (dev only) |

## 🎯 Success Checklist

✅ **Prerequisites Installed**

- [ ] .NET 9 SDK installed (`dotnet --version` shows 9.x.x)
- [ ] EF Core tools installed (`dotnet ef --version` works)
- [ ] PostgreSQL accessible (local or Supabase)

✅ **Environment Configured**

- [ ] `.env` file created with required variables
- [ ] Database connection string configured
- [ ] Clerk authentication keys set
- [ ] Stripe payment keys configured (if using payments)

✅ **Build Success**

- [ ] `dotnet restore` completes without errors
- [ ] `dotnet build` completes without errors
- [ ] All tests pass (`dotnet test`)

✅ **Runtime Success**

- [ ] Application starts without errors
- [ ] Swagger UI loads at https://localhost:7000/swagger
- [ ] Health check returns healthy status
- [ ] Database connection works

## 🆘 Emergency Quick Fixes

### Build Fails

```powershell
dotnet nuget locals all --clear
.\build-solution.ps1 -Clean -Restore -Build
```

### Database Issues

```powershell
cd MicroSaasWebApi.Full
dotnet ef database drop --force
dotnet ef database update
```

### Port Conflicts

```powershell
netstat -ano | findstr :5000
netstat -ano | findstr :7000
# Kill the process using: taskkill /PID <ProcessID> /F
```

### Package Conflicts

```powershell
dotnet list package --outdated
# Update specific packages as needed
```

## 📞 Getting Help

1. **Check Documentation**: Start with the relevant guide above
2. **Review Logs**: Check console output and log files
3. **Verify Environment**: Run `.\verify-dotnet9.ps1`
4. **Test Components**: Use individual API endpoints to isolate issues
5. **Reset Everything**: Use the "Nuclear option" commands above

## 🎉 What's Next?

After getting the application running:

1. **Explore the API**: Visit https://localhost:7000/swagger
2. **Test Authentication**: Configure Clerk and test login flows
3. **Setup Payments**: Configure Stripe and test payment flows
4. **Customize Features**: Modify controllers and services for your needs
5. **Deploy**: Follow the deployment guide for your target environment

---

**Happy coding! 🚀**
