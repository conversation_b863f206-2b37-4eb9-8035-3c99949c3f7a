/* Custom CSS for Health Check UI - MicroSaaS Web API */

:root {
    --primary-color: #2563eb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --background-color: #f8fafc;
    --card-background: #ffffff;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
}

/* Main container styling */
.health-checks-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-color);
    min-height: 100vh;
    padding: 20px;
}

/* Header styling */
.health-checks-header {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    color: white;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.health-checks-header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 700;
}

.health-checks-header p {
    margin: 10px 0 0 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

/* Status cards */
.health-check-card {
    background: var(--card-background);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border-left: 4px solid var(--border-color);
    transition: all 0.3s ease;
}

.health-check-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.health-check-card.healthy {
    border-left-color: var(--success-color);
}

.health-check-card.unhealthy {
    border-left-color: var(--danger-color);
}

.health-check-card.degraded {
    border-left-color: var(--warning-color);
}

/* Status indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-indicator.healthy {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-indicator.unhealthy {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.status-indicator.degraded {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

/* Metrics styling */
.health-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 20px;
}

.metric-item {
    background: var(--background-color);
    padding: 16px;
    border-radius: 8px;
    text-align: center;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.metric-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Responsive design */
@media (max-width: 768px) {
    .health-checks-container {
        padding: 10px;
    }
    
    .health-checks-header {
        padding: 20px;
    }
    
    .health-checks-header h1 {
        font-size: 2rem;
    }
    
    .health-check-card {
        padding: 16px;
    }
    
    .health-metrics {
        grid-template-columns: 1fr;
    }
}

/* Animation for status changes */
@keyframes statusChange {
    0% { opacity: 0.5; transform: scale(0.95); }
    100% { opacity: 1; transform: scale(1); }
}

.status-indicator {
    animation: statusChange 0.3s ease-in-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}
