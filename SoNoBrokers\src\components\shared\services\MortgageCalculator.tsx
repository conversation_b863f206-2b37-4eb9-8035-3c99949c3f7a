'use client'

import React, { useState } from 'react'
import { useUser } from '@clerk/nextjs'
import { HeroSection } from '@/components/shared/common/HeroSection'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Calculator, Home, DollarSign, Percent, Calendar } from 'lucide-react'

interface MortgageCalculatorProps {
  userType: 'buyer' | 'seller'
  country: string
}

export function MortgageCalculator({ userType, country }: MortgageCalculatorProps) {
  const { isSignedIn } = useUser()
  const [homePrice, setHomePrice] = useState('')
  const [downPayment, setDownPayment] = useState([20])
  const [interestRate, setInterestRate] = useState([5.5])
  const [loanTerm, setLoanTerm] = useState('30')
  const [propertyTax, setPropertyTax] = useState('')
  const [insurance, setInsurance] = useState('')
  const [calculations, setCalculations] = useState<any>(null)

  const calculateMortgage = () => {
    const price = parseFloat(homePrice)
    if (!price || price <= 0) return

    const downPaymentAmount = price * (downPayment[0] / 100)
    const loanAmount = price - downPaymentAmount
    const monthlyRate = interestRate[0] / 100 / 12
    const numberOfPayments = parseInt(loanTerm) * 12

    // Calculate monthly payment using mortgage formula
    const monthlyPayment = loanAmount *
      (monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments)) /
      (Math.pow(1 + monthlyRate, numberOfPayments) - 1)

    const monthlyPropertyTax = propertyTax ? parseFloat(propertyTax) / 12 : 0
    const monthlyInsurance = insurance ? parseFloat(insurance) / 12 : 0
    const totalMonthlyPayment = monthlyPayment + monthlyPropertyTax + monthlyInsurance

    const totalInterest = (monthlyPayment * numberOfPayments) - loanAmount
    const totalCost = price + totalInterest

    setCalculations({
      homePrice: price,
      downPaymentAmount,
      loanAmount,
      monthlyPayment,
      monthlyPropertyTax,
      monthlyInsurance,
      totalMonthlyPayment,
      totalInterest,
      totalCost,
      interestRate: interestRate[0],
      loanTerm: parseInt(loanTerm)
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: country === 'CA' ? 'CAD' : 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <HeroSection userType={userType} />

      {/* Calculator Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-4 flex items-center justify-center">
              <Calculator className="w-8 h-8 mr-3 text-primary" />
              Mortgage Calculator
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Calculate your monthly mortgage payments and see how different factors affect your home affordability.
              Plan your budget with accurate payment estimates for {country === 'CA' ? 'Canada' : 'United States'}.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Input Form */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Home className="w-5 h-5 mr-2" />
                  Loan Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label>Home Price</Label>
                  <Input
                    placeholder={`Enter home price in ${country === 'CA' ? 'CAD' : 'USD'}`}
                    value={homePrice}
                    onChange={(e) => setHomePrice(e.target.value)}
                    type="number"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Down Payment (%)</Label>
                  <div className="px-3">
                    <Slider
                      value={downPayment}
                      onValueChange={setDownPayment}
                      max={50}
                      min={5}
                      step={1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground mt-1">
                      <span>5%</span>
                      <span>{downPayment[0]}% ({homePrice ? formatCurrency(parseFloat(homePrice) * (downPayment[0] / 100)) : '$0'})</span>
                      <span>50%</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Interest Rate (%)</Label>
                  <div className="px-3">
                    <Slider
                      value={interestRate}
                      onValueChange={setInterestRate}
                      max={10}
                      min={2}
                      step={0.1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground mt-1">
                      <span>2%</span>
                      <span>{interestRate[0]}%</span>
                      <span>10%</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Loan Term</Label>
                  <Select value={loanTerm} onValueChange={setLoanTerm}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select loan term" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="15">15 years</SelectItem>
                      <SelectItem value="20">20 years</SelectItem>
                      <SelectItem value="25">25 years</SelectItem>
                      <SelectItem value="30">30 years</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Annual Property Tax</Label>
                    <Input
                      placeholder="Optional"
                      value={propertyTax}
                      onChange={(e) => setPropertyTax(e.target.value)}
                      type="number"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Annual Insurance</Label>
                    <Input
                      placeholder="Optional"
                      value={insurance}
                      onChange={(e) => setInsurance(e.target.value)}
                      type="number"
                    />
                  </div>
                </div>

                <Button
                  onClick={calculateMortgage}
                  className="w-full"
                  size="lg"
                  disabled={!homePrice || parseFloat(homePrice) <= 0}
                >
                  <Calculator className="w-4 h-4 mr-2" />
                  Calculate Payment
                </Button>
              </CardContent>
            </Card>

            {/* Results */}
            <div className="space-y-6">
              {calculations && (
                <>
                  <Card className="border-primary">
                    <CardHeader>
                      <CardTitle className="flex items-center text-primary">
                        <DollarSign className="w-5 h-5 mr-2" />
                        Monthly Payment
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center">
                        <div className="text-4xl font-bold text-primary mb-2">
                          {formatCurrency(calculations.totalMonthlyPayment)}
                        </div>
                        <p className="text-sm text-muted-foreground mb-4">
                          Total monthly payment including taxes and insurance
                        </p>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="text-center">
                            <div className="font-semibold">
                              {formatCurrency(calculations.monthlyPayment)}
                            </div>
                            <div className="text-muted-foreground">Principal & Interest</div>
                          </div>
                          <div className="text-center">
                            <div className="font-semibold">
                              {formatCurrency(calculations.monthlyPropertyTax + calculations.monthlyInsurance)}
                            </div>
                            <div className="text-muted-foreground">Taxes & Insurance</div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Percent className="w-5 h-5 mr-2" />
                        Loan Breakdown
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Home Price</span>
                          <span className="font-medium">{formatCurrency(calculations.homePrice)}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Down Payment ({downPayment[0]}%)</span>
                          <span className="font-medium">{formatCurrency(calculations.downPaymentAmount)}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Loan Amount</span>
                          <span className="font-medium">{formatCurrency(calculations.loanAmount)}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Interest Rate</span>
                          <span className="font-medium">{calculations.interestRate}%</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Loan Term</span>
                          <span className="font-medium">{calculations.loanTerm} years</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Calendar className="w-5 h-5 mr-2" />
                        Total Cost Over Time
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Total Interest Paid</span>
                          <span className="font-medium text-red-600">
                            {formatCurrency(calculations.totalInterest)}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Total Cost of Home</span>
                          <span className="font-medium">
                            {formatCurrency(calculations.totalCost)}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}

              <Card>
                <CardHeader>
                  <CardTitle>Affordability Tips</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <ul className="text-sm space-y-2">
                      <li>• Aim for a monthly payment under 28% of gross income</li>
                      <li>• Consider a larger down payment to reduce monthly costs</li>
                      <li>• Shop around for the best interest rates</li>
                      <li>• Factor in maintenance and utility costs</li>
                      <li>• Get pre-approved before house hunting</li>
                    </ul>
                    <Button variant="outline" className="w-full mt-4">
                      Find Mortgage Lenders
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
