{"AppInfo": {"Environment": "Production"}, "TenantSettings": {"EnableMultiTenantFeatures": true, "TenantCacheExpiry": "00:30:00", "MaxTenantsPerUser": 3, "EnableTenantIsolation": true}, "FeatureFlags": {"EnableSwagger": false, "EnableHangfire": false, "EnableDetailedLogging": false, "EnablePayments": true, "EnableAnalytics": true, "EnableCustomBranding": true, "EnableMultiRegion": true, "EnableAdvancedSecurity": true, "EnableRateLimiting": true, "EnableResponseCompression": true, "EnableHealthChecks": true}, "RateLimiting": {"EnableGlobalRateLimit": true, "GlobalRequestsPerMinute": 1000, "EnablePerTenantRateLimit": true, "TenantRequestsPerMinute": 100, "EnablePerUserRateLimit": true, "UserRequestsPerMinute": 60}, "Security": {"Cors": {"AllowedOrigins": ["https://localhost:3000", "https://www.api.sonobrokers.com", "https://sonobrokers.com", "https://localhost:7163"], "AllowCredentials": true}, "Headers": {"EnableSecurityHeaders": true, "EnableHsts": true, "HstsMaxAge": ********, "EnableContentTypeOptions": true, "EnableFrameOptions": true, "EnableXssProtection": true}}, "Monitoring": {"HealthChecks": {"EnableUI": false, "EnableDetailedErrors": false, "CacheDuration": "00:05:00"}}, "Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Error", "Microsoft.EntityFrameworkCore": "Error", "Microsoft.AspNetCore.Hosting": "Warning", "Microsoft.AspNetCore.Routing": "Error", "System.Net.Http.HttpClient": "Error", "Hangfire": "Error"}, "Console": {"IncludeScopes": false, "TimestampFormat": "yyyy-MM-dd HH:mm:ss "}}, "ExternalServices": {"Stripe": {"EnableTestMode": false}, "Azure": {"Storage": {"ContainerName": "microsaas-prod-files"}}, "Email": {"Smtp": {"Port": 587, "EnableSsl": true}}}}