using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.App.Repositories.Interfaces;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Models.Core;
using Microsoft.AspNetCore.Authorization;


namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/sonobrokers/properties")]
    [Tags("Properties")]
    public class PropertiesController : ControllerBase
    {
        private readonly MicroSaasWebApi.App.Repositories.Interfaces.IPropertyRepository _propertyRepository;
        private readonly ILogger<PropertiesController> _logger;

        public PropertiesController(MicroSaasWebApi.App.Repositories.Interfaces.IPropertyRepository propertyRepository, ILogger<PropertiesController> logger)
        {
            _propertyRepository = propertyRepository;
            _logger = logger;
        }

        /// <summary>
        /// Get all properties
        /// </summary>
        /// <param name="status">Filter by property status</param>
        /// <param name="propertyType">Filter by property type</param>
        /// <param name="minPrice">Minimum price filter</param>
        /// <param name="maxPrice">Maximum price filter</param>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>List of properties</returns>
        [HttpGet]
        [AllowAnonymous]
        public async Task<ActionResult<ApiResponse<object>>> GetProperties(
            [FromQuery] PropertyStatus? status = null,
            [FromQuery] string? propertyType = null,
            [FromQuery] decimal? minPrice = null,
            [FromQuery] decimal? maxPrice = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                // Use the repository's search method which handles all the filtering and pagination
                var (properties, totalCount) = await _propertyRepository.SearchAsync(
                    searchTerm: null,
                    city: null,
                    province: null,
                    minPrice: minPrice,
                    maxPrice: maxPrice,
                    bedrooms: null,
                    bathrooms: null,
                    status: status,
                    page: page,
                    pageSize: pageSize);

                var result = new
                {
                    Properties = properties,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                };

                return Ok(ApiResponse<object>.SuccessResult(result, "Properties retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving properties");
                return StatusCode(500, ApiResponse<object>.ErrorResult("Failed to retrieve properties"));
            }
        }

        /// <summary>
        /// Get property by ID
        /// </summary>
        /// <param name="id">Property ID</param>
        /// <returns>Property details</returns>
        [HttpGet("{id}")]
        [AllowAnonymous]
        public async Task<ActionResult<ApiResponse<Property>>> GetProperty(string id)
        {
            try
            {
                var property = await _propertyRepository.GetByIdAsync(id);

                if (property == null)
                {
                    return NotFound(ApiResponse<Property>.ErrorResult("Property not found"));
                }

                return Ok(ApiResponse<Property>.SuccessResult(property, "Property retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving property {PropertyId}", id);
                return StatusCode(500, ApiResponse<Property>.ErrorResult("Failed to retrieve property"));
            }
        }

        /// <summary>
        /// Create a new property
        /// </summary>
        /// <param name="property">Property data</param>
        /// <returns>Created property</returns>
        [HttpPost]



        public async Task<ActionResult<ApiResponse<Property>>> CreateProperty(Property property)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse<Property>.ErrorResult("Invalid property data"));
                }

                // Create the property using the repository
                var propertyId = await _propertyRepository.CreateAsync(property);

                // Get the created property
                var createdProperty = await _propertyRepository.GetByIdAsync(propertyId);

                return CreatedAtAction(nameof(GetProperty), new { id = propertyId },
                    ApiResponse<Property>.SuccessResult(createdProperty!, "Property created successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating property");
                return StatusCode(500, ApiResponse<Property>.ErrorResult("Failed to create property"));
            }
        }

        /// <summary>
        /// Update property
        /// </summary>
        /// <param name="id">Property ID</param>
        /// <param name="property">Updated property data</param>
        /// <returns>Updated property</returns>
        [HttpPut("{id}")]




        public async Task<ActionResult<ApiResponse<Property>>> UpdateProperty(string id, Property property)
        {
            try
            {
                if (id != property.Id)
                {
                    return BadRequest(ApiResponse<Property>.ErrorResult("Property ID mismatch"));
                }

                // Check if property exists
                var existingProperty = await _propertyRepository.GetByIdAsync(id);
                if (existingProperty == null)
                {
                    return NotFound(ApiResponse<Property>.ErrorResult("Property not found"));
                }

                // Update the property using the repository
                var success = await _propertyRepository.UpdateAsync(property);
                if (!success)
                {
                    return StatusCode(500, ApiResponse<Property>.ErrorResult("Failed to update property"));
                }

                // Get the updated property
                var updatedProperty = await _propertyRepository.GetByIdAsync(id);

                return Ok(ApiResponse<Property>.SuccessResult(updatedProperty!, "Property updated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating property {PropertyId}", id);
                return StatusCode(500, ApiResponse<Property>.ErrorResult("Failed to update property"));
            }
        }

        /// <summary>
        /// Delete property
        /// </summary>
        /// <param name="id">Property ID</param>
        /// <returns>Success message</returns>
        [HttpDelete("{id}")]



        public async Task<ActionResult<ApiResponse<object>>> DeleteProperty(string id)
        {
            try
            {
                // Check if property exists
                var property = await _propertyRepository.GetByIdAsync(id);
                if (property == null)
                {
                    return NotFound(ApiResponse<object>.ErrorResult("Property not found"));
                }

                // Delete the property using the repository
                var success = await _propertyRepository.DeleteAsync(id);
                if (!success)
                {
                    return StatusCode(500, ApiResponse<object>.ErrorResult("Failed to delete property"));
                }

                return Ok(ApiResponse<object>.SuccessResult(new { }, "Property deleted successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting property {PropertyId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResult("Failed to delete property"));
            }
        }

        /// <summary>
        /// Search properties
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="city">City filter</param>
        /// <param name="province">Province filter</param>
        /// <param name="minPrice">Minimum price</param>
        /// <param name="maxPrice">Maximum price</param>
        /// <param name="bedrooms">Number of bedrooms</param>
        /// <param name="bathrooms">Number of bathrooms</param>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Search results</returns>
        [HttpGet("search")]
        [AllowAnonymous]
        public async Task<ActionResult<ApiResponse<object>>> SearchProperties(
            [FromQuery] string? searchTerm = null,
            [FromQuery] string? city = null,
            [FromQuery] string? province = null,
            [FromQuery] decimal? minPrice = null,
            [FromQuery] decimal? maxPrice = null,
            [FromQuery] int? bedrooms = null,
            [FromQuery] int? bathrooms = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                // Use the repository's search method with all filters
                var (properties, totalCount) = await _propertyRepository.SearchAsync(
                    searchTerm: searchTerm,
                    city: city,
                    province: province,
                    minPrice: minPrice,
                    maxPrice: maxPrice,
                    bedrooms: bedrooms,
                    bathrooms: bathrooms,
                    status: PropertyStatus.active, // Only search active properties
                    page: page,
                    pageSize: pageSize);

                var result = new
                {
                    Properties = properties,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                };

                return Ok(ApiResponse<object>.SuccessResult(result, "Search completed successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching properties");
                return StatusCode(500, ApiResponse<object>.ErrorResult("Failed to search properties"));
            }
        }
    }
}
