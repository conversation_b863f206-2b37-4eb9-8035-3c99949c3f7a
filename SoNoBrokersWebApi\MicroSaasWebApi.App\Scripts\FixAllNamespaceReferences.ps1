# PowerShell script to fix all namespace references in one go
param(
    [string]$RootPath = ".",
    [switch]$WhatIf = $false
)

Write-Host "Fixing all namespace references..." -ForegroundColor Green

# Get all C# files in Controllers and Services
$files = Get-ChildItem -Path $RootPath -Include "*.cs" -Recurse | Where-Object {
    $_.FullName -like "*Controllers*" -or 
    $_.FullName -like "*Services*" -or
    $_.FullName -like "*Extensions*" -or
    $_.FullName -like "*Program.cs"
}

Write-Host "Found $($files.Count) files to process" -ForegroundColor Yellow

foreach ($file in $files) {
    Write-Host "Processing: $($file.Name)" -ForegroundColor Cyan
    
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Replace using statements
    $content = $content -replace 'using MicroSaasWebApi\.Data\.Repositories\.SoNoBrokers;', 'using MicroSaasWebApi.App.Context;'
    $content = $content -replace 'using MicroSaasWebApi\.Data\.Context;', 'using MicroSaasWebApi.App.Context;'
    $content = $content -replace 'using MicroSaasWebApi\.Data;', 'using MicroSaasWebApi.App.Context;'
    
    # Replace repository interface references
    $content = $content -replace 'using MicroSaasWebApi\.Data\.Repositories\.Interfaces;', 'using MicroSaasWebApi.App.Repositories.Interfaces;'
    
    # Replace full qualified names
    $content = $content -replace 'MicroSaasWebApi\.Data\.Repositories\.SoNoBrokers\.DapperDbContext', 'MicroSaasWebApi.App.Context.IDapperDbContext'
    $content = $content -replace 'MicroSaasWebApi\.Data\.Repositories\.SoNoBrokers\.IDapperDbContext', 'MicroSaasWebApi.App.Context.IDapperDbContext'
    $content = $content -replace 'MicroSaasWebApi\.Data\.Context\.DapperDbContext', 'MicroSaasWebApi.App.Context.IDapperDbContext'
    $content = $content -replace 'MicroSaasWebApi\.Data\.Context\.IDapperDbContext', 'MicroSaasWebApi.App.Context.IDapperDbContext'
    $content = $content -replace 'MicroSaasWebApi\.Data\.DapperDbContext', 'MicroSaasWebApi.App.Context.IDapperDbContext'
    $content = $content -replace 'MicroSaasWebApi\.Data\.IDapperDbContext', 'MicroSaasWebApi.App.Context.IDapperDbContext'
    
    # Replace simple type references
    $content = $content -replace '\bDapperDbContext\b(?!\s*\()', 'IDapperDbContext'
    $content = $content -replace 'DapperDbContext\s+(\w+)\s*\)', 'IDapperDbContext $1)'
    $content = $content -replace 'DapperDbContext\s+(\w+)\s*,', 'IDapperDbContext $1,'
    
    # Replace repository interface references
    $content = $content -replace 'MicroSaasWebApi\.Data\.Repositories\.SoNoBrokers\.IPropertyRepository', 'MicroSaasWebApi.App.Repositories.Interfaces.IPropertyRepository'
    $content = $content -replace '\bIPropertyRepository\b(?!\s*,|\s*>)', 'MicroSaasWebApi.App.Repositories.Interfaces.IPropertyRepository'
    
    # Add missing using statements if needed
    if ($content -match 'IDapperDbContext' -and $content -notmatch 'using MicroSaasWebApi\.App\.Context;') {
        # Find the last using statement and add our using after it
        $usingPattern = '(using [^;]+;)'
        $matches = [regex]::Matches($content, $usingPattern)
        if ($matches.Count -gt 0) {
            $lastUsing = $matches[$matches.Count - 1]
            $insertPosition = $lastUsing.Index + $lastUsing.Length
            $content = $content.Insert($insertPosition, "`nusing MicroSaasWebApi.App.Context;")
        }
    }
    
    if ($content -match 'IPropertyRepository' -and $content -notmatch 'using MicroSaasWebApi\.App\.Repositories\.Interfaces;') {
        # Find the last using statement and add our using after it
        $usingPattern = '(using [^;]+;)'
        $matches = [regex]::Matches($content, $usingPattern)
        if ($matches.Count -gt 0) {
            $lastUsing = $matches[$matches.Count - 1]
            $insertPosition = $lastUsing.Index + $lastUsing.Length
            $content = $content.Insert($insertPosition, "`nusing MicroSaasWebApi.App.Repositories.Interfaces;")
        }
    }
    
    if ($content -ne $originalContent) {
        if ($WhatIf) {
            Write-Host "  [WHAT-IF] Would update $($file.Name)" -ForegroundColor Magenta
        } else {
            Set-Content $file.FullName $content -NoNewline
            Write-Host "  Updated $($file.Name)" -ForegroundColor Green
        }
    } else {
        Write-Host "  No changes needed for $($file.Name)" -ForegroundColor Gray
    }
}

Write-Host "`nNamespace fix completed!" -ForegroundColor Green
Write-Host "Next step: Run 'dotnet build' to verify all references are correct" -ForegroundColor Yellow
