import { NextResponse, NextRequest } from "next/server";
import <PERSON><PERSON> from "stripe";
import { stripe, webhookSecret } from "@/constants/stripe";
import { processSubscriptonDelete, processInvoicePaid, processCheckoutSuccessWebhook } from "@/lib/stripe-actions";
import { EnhancedStripeService } from "@/services/stripeService";
import { AdvertiserService } from "@/services/advertiserService";
// This is where we receive Stripe webhook events. / It used to update the user data, send emails, etc...
// By default, it'll store the user in the database.
export async function POST(req: NextRequest) {
  const textParsedBody = await req.text();
  const headersList = req.headers;
  const signature = headersList.get("stripe-signature");
  let event: Stripe.Event;

  console.log('1 POST', textParsedBody)
  console.log('2 signature', signature)
  try {
    event = stripe.webhooks.constructEvent(textParsedBody, signature, webhookSecret);
  } catch (err) {
    console.error(`Webhook signature verification failed. ${err.message}`);
    return NextResponse.json({ error: err.message }, { status: 400 });
  }
  try {
    // Process with enhanced service (logs all events and handles comprehensive processing)
    await EnhancedStripeService.processWebhookEvent(event);

    // Keep existing logic for backward compatibility
    switch (event.type) {
      case "checkout.session.completed": {
        const session = event.data.object as Stripe.Checkout.Session;

        // Check if this is an advertiser subscription
        if (session.metadata?.advertiserId) {
          await handleAdvertiserSubscriptionCreated(session);
        } else {
          const body = JSON.parse(textParsedBody);
          return processCheckoutSuccessWebhook(body, event)
        }
        break;
      }
      case "checkout.session.expired": {
        // User didn't complete the transaction
        // You don't need to do anything here, by you can send an email to the user to remind him to complete the transaction, for instance
        break;
      }
      case "customer.subscription.updated": {
        const subscription = event.data.object as Stripe.Subscription;

        // Check if this is an advertiser subscription
        if (subscription.metadata?.advertiserId) {
          await handleAdvertiserSubscriptionUpdated(subscription);
        }
        // The customer might have changed the plan (higher or lower plan, cancel soon etc...)
        // You don't need to do anything here, because Stripe will let us know when the subscription is canceled for good (at the end of the billing cycle) in the "customer.subscription.deleted" event
        // You can update the user data to show a "Cancel soon" badge for instance
        break;
      }
      case "customer.subscription.deleted": {
        const subscription = event.data.object as Stripe.Subscription;

        // Check if this is an advertiser subscription
        if (subscription.metadata?.advertiserId) {
          await handleAdvertiserSubscriptionDeleted(subscription);
        } else {
          processSubscriptonDelete(event)
        }
        break;
      }
      case "invoice.paid": {
        const body = JSON.parse(textParsedBody);
        processInvoicePaid(body, event)
        break;
      }
      case "invoice.payment_failed":
        // A payment failed (for instance the customer does not have a valid payment method)
        // ❌ Revoke access to the product
        // ⏳ OR wait for the customer to pay (more friendly):
        //      - Stripe will automatically email the customer (Smart Retries)
        //      - We will receive a "customer.subscription.deleted" when all retries were made and the subscription has expired
        break;
      default:
      // Unhandled event type
    }
  } catch (e) {
    console.error("stripe error: ", e.message);
  }
  return NextResponse.json({});
}

// Advertiser subscription webhook handlers
async function handleAdvertiserSubscriptionCreated(session: Stripe.Checkout.Session) {
  try {
    const { advertiserId, plan, userId } = session.metadata!;

    // Get the subscription from Stripe
    const subscriptionResponse = await stripe.subscriptions.retrieve(session.subscription as string);
    const subscription = subscriptionResponse as Stripe.Subscription;

    // Create advertiser subscription in database
    await AdvertiserService.createSubscription({
      advertiserId,
      stripeSubscriptionId: subscription.id,
      stripePriceId: subscription.items.data[0].price.id,
      plan,
      currentPeriodStart: new Date((subscription as any).current_period_start * 1000),
      currentPeriodEnd: new Date((subscription as any).current_period_end * 1000)
    });

    console.log(`Advertiser subscription created for advertiser ${advertiserId}`);
  } catch (error) {
    console.error('Error handling advertiser subscription created:', error);
  }
}

async function handleAdvertiserSubscriptionUpdated(subscription: Stripe.Subscription) {
  try {
    await AdvertiserService.updateSubscriptionStatus(
      subscription.id,
      subscription.status,
      new Date((subscription as any).current_period_end * 1000)
    );

    console.log(`Advertiser subscription updated: ${subscription.id}`);
  } catch (error) {
    console.error('Error handling advertiser subscription updated:', error);
  }
}

async function handleAdvertiserSubscriptionDeleted(subscription: Stripe.Subscription) {
  try {
    await AdvertiserService.updateSubscriptionStatus(
      subscription.id,
      'cancelled'
    );

    console.log(`Advertiser subscription deleted: ${subscription.id}`);
  } catch (error) {
    console.error('Error handling advertiser subscription deleted:', error);
  }
}
