using System.ComponentModel.DataAnnotations;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    public class ProjectResponse
    {
        public string Id { get; set; } = string.Empty;
        public string ConnectionId { get; set; } = string.Empty;
        public string WebhookId { get; set; } = string.Empty;
        public string ScenarioId { get; set; } = string.Empty;
        public string UserClerkId { get; set; } = string.Empty;
        public string WebhookLink { get; set; } = string.Empty;
        public string? AssistantId { get; set; }
        public string Type { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class CreateProjectRequest
    {
        [Required]
        public string ConnectionId { get; set; } = string.Empty;

        [Required]
        public string WebhookId { get; set; } = string.Empty;

        [Required]
        public string ScenarioId { get; set; } = string.Empty;

        [Required]
        public string WebhookLink { get; set; } = string.Empty;

        public string? AssistantId { get; set; }

        [Required]
        public string Type { get; set; } = string.Empty;

        public string Status { get; set; } = "default";
    }

    public class UpdateProjectRequest
    {
        [Required]
        public string Id { get; set; } = string.Empty;

        public string? ConnectionId { get; set; }

        public string? WebhookId { get; set; }

        public string? ScenarioId { get; set; }

        public string? WebhookLink { get; set; }

        public string? AssistantId { get; set; }

        public string? Type { get; set; }

        public string? Status { get; set; }
    }

    public class ProjectSearchRequest
    {
        public string? Type { get; set; }
        public string? Status { get; set; }
        public int Page { get; set; } = 1;
        public int Limit { get; set; } = 20;
    }

    public class ProjectSearchResponse
    {
        public List<ProjectResponse> Projects { get; set; } = new();
        public int Total { get; set; }
        public int Page { get; set; }
        public int TotalPages { get; set; }
        public bool HasMore { get; set; }
    }
}
