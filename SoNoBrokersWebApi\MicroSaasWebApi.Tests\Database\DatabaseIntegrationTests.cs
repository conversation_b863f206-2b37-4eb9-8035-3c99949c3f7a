using FluentAssertions;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Tests.Common;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Database
{
    public class DatabaseIntegrationTests : DatabaseTestBase
    {
        public DatabaseIntegrationTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task Database_Connection_IsSuccessful()
        {
            // Act
            var result = await QuerySingleAsync<int>("SELECT 1");

            // Assert
            result.Should().Be(1);
        }

        [Fact]
        public async Task Database_AllRequiredTables_Exist()
        {
            // Arrange
            var requiredTables = new[]
            {
                "User",
                "Property",
                "PropertyImage",
                "Advertiser",
                "AdvertiserSubscription",
                "Project",
                "SubscriptionSnb",
                "RolePermission",
                "ContactShare",
                "PropertyVisitSchedule",
                "SellerAvailability",
                "PropertyQrCode",
                "VisitVerification"
            };

            // Act & Assert
            foreach (var table in requiredTables)
            {
                var exists = await TableExistsAsync(table);
                exists.Should().BeTrue($"Table '{table}' should exist in the database");
            }
        }

        [Fact]
        public async Task Database_StoredProcedures_Exist()
        {
            // Arrange
            var requiredProcedures = new[]
            {
                "search_properties_advanced",
                "get_property_analytics",
                "get_user_dashboard_stats",
                "create_property_with_images",
                "get_advertiser_performance",
                "sync_subscription_status",
                "get_admin_dashboard_counts"
            };

            // Act & Assert
            foreach (var procedure in requiredProcedures)
            {
                var exists = await StoredProcedureExistsAsync(procedure);
                exists.Should().BeTrue($"Stored procedure '{procedure}' should exist in the database");
            }
        }

        [Fact]
        public async Task User_CRUD_Operations_WorkCorrectly()
        {
            // Create
            var userId = TestHelpers.GenerateTestId("crud-user");
            var createSql = @"
                INSERT INTO public.""User"" (id, email, ""fullName"", role, ""userType"", ""isActive"", ""createdAt"", ""updatedAt"")
                VALUES (@id, @email, @fullName, @role, @userType, @isActive, @createdAt, @updatedAt)";

            var createParams = new
            {
                id = userId,
                email = "<EMAIL>",
                fullName = "CRUD Test User",
                role = "USER",
                userType = "Buyer",
                isActive = true,
                createdAt = DateTime.UtcNow,
                updatedAt = DateTime.UtcNow
            };

            var created = await ExecuteAsync(createSql, createParams);
            created.Should().Be(1);

            // Read
            var readSql = @"SELECT * FROM public.""User"" WHERE id = @id";
            var user = await QuerySingleOrDefaultAsync<User>(readSql, new { id = userId });

            user.Should().NotBeNull();
            user!.Email.Should().Be("<EMAIL>");
            user.FullName.Should().Be("CRUD Test User");

            // Update
            var updateSql = @"
                UPDATE public.""User"" 
                SET ""fullName"" = @fullName, ""updatedAt"" = @updatedAt 
                WHERE id = @id";

            var updated = await ExecuteAsync(updateSql, new
            {
                id = userId,
                fullName = "Updated CRUD User",
                updatedAt = DateTime.UtcNow
            });
            updated.Should().Be(1);

            // Verify update
            var updatedUser = await QuerySingleOrDefaultAsync<User>(readSql, new { id = userId });
            updatedUser!.FullName.Should().Be("Updated CRUD User");

            // Delete
            var deleteSql = @"DELETE FROM public.""User"" WHERE id = @id";
            var deleted = await ExecuteAsync(deleteSql, new { id = userId });
            deleted.Should().Be(1);

            // Verify deletion
            var deletedUser = await QuerySingleOrDefaultAsync<User>(readSql, new { id = userId });
            deletedUser.Should().BeNull();
        }

        [Fact]
        public async Task Property_CRUD_Operations_WorkCorrectly()
        {
            // Create
            var propertyId = TestHelpers.GenerateTestId("crud-prop");
            var createSql = @"
                INSERT INTO public.""Property"" (id, title, description, price, ""propertyType"", bedrooms, bathrooms, ""sellerId"", status, ""createdAt"", ""updatedAt"")
                VALUES (@id, @title, @description, @price, @propertyType, @bedrooms, @bathrooms, @sellerId, @status, @createdAt, @updatedAt)";

            var createParams = new
            {
                id = propertyId,
                title = "CRUD Test Property",
                description = "A property for CRUD testing",
                price = 500000m,
                propertyType = "Detached House",
                bedrooms = 3,
                bathrooms = 2.5m,
                sellerId = "db-test-user-2",
                status = "active",
                createdAt = DateTime.UtcNow,
                updatedAt = DateTime.UtcNow
            };

            var created = await ExecuteAsync(createSql, createParams);
            created.Should().Be(1);

            // Read
            var readSql = @"SELECT * FROM public.""Property"" WHERE id = @id";
            var property = await QuerySingleOrDefaultAsync<Property>(readSql, new { id = propertyId });

            property.Should().NotBeNull();
            property!.Title.Should().Be("CRUD Test Property");
            property.Price.Should().Be(500000m);

            // Update
            var updateSql = @"
                UPDATE public.""Property"" 
                SET price = @price, ""updatedAt"" = @updatedAt 
                WHERE id = @id";

            var updated = await ExecuteAsync(updateSql, new
            {
                id = propertyId,
                price = 550000m,
                updatedAt = DateTime.UtcNow
            });
            updated.Should().Be(1);

            // Verify update
            var updatedProperty = await QuerySingleOrDefaultAsync<Property>(readSql, new { id = propertyId });
            updatedProperty!.Price.Should().Be(550000m);

            // Delete
            var deleteSql = @"DELETE FROM public.""Property"" WHERE id = @id";
            var deleted = await ExecuteAsync(deleteSql, new { id = propertyId });
            deleted.Should().Be(1);
        }

        [Fact]
        public async Task Advertiser_CRUD_Operations_WorkCorrectly()
        {
            // Create
            var advertiserId = TestHelpers.GenerateTestId("crud-adv");
            var createSql = @"
                INSERT INTO public.""Advertiser"" (id, ""userId"", ""businessName"", email, ""serviceType"", plan, status, ""createdAt"", ""updatedAt"")
                VALUES (@id, @userId, @businessName, @email, @serviceType, @plan, @status, @createdAt, @updatedAt)";

            var createParams = new
            {
                id = advertiserId,
                userId = "db-test-user-1",
                businessName = "CRUD Test Business",
                email = "<EMAIL>",
                serviceType = "photographer",
                plan = "basic",
                status = "active",
                createdAt = DateTime.UtcNow,
                updatedAt = DateTime.UtcNow
            };

            var created = await ExecuteAsync(createSql, createParams);
            created.Should().Be(1);

            // Read
            var readSql = @"SELECT * FROM public.""Advertiser"" WHERE id = @id";
            var advertiser = await QuerySingleOrDefaultAsync<Advertiser>(readSql, new { id = advertiserId });

            advertiser.Should().NotBeNull();
            advertiser!.BusinessName.Should().Be("CRUD Test Business");
            advertiser.Email.Should().Be("<EMAIL>");

            // Update
            var updateSql = @"
                UPDATE public.""Advertiser"" 
                SET plan = @plan, ""updatedAt"" = @updatedAt 
                WHERE id = @id";

            var updated = await ExecuteAsync(updateSql, new
            {
                id = advertiserId,
                plan = "premium",
                updatedAt = DateTime.UtcNow
            });
            updated.Should().Be(1);

            // Delete
            var deleteSql = @"DELETE FROM public.""Advertiser"" WHERE id = @id";
            var deleted = await ExecuteAsync(deleteSql, new { id = advertiserId });
            deleted.Should().Be(1);
        }

        [Fact]
        public async Task StoredProcedure_SearchPropertiesAdvanced_WorksCorrectly()
        {
            // Act
            var sql = @"
                SELECT * FROM public.search_properties_advanced(
                    p_location := NULL,
                    p_property_type := 'Detached House',
                    p_min_price := 400000,
                    p_max_price := 600000,
                    p_bedrooms := 3,
                    p_bathrooms := 2,
                    p_user_id := NULL,
                    p_limit := 10,
                    p_offset := 0
                )";

            var results = await QueryAsync<dynamic>(sql);

            // Assert
            results.Should().NotBeNull();
            // Results may be empty if no matching properties exist, but the procedure should execute without error
        }

        [Fact]
        public async Task StoredProcedure_GetAdminDashboardCounts_WorksCorrectly()
        {
            // Act
            var sql = "SELECT * FROM public.get_admin_dashboard_counts()";
            var result = await QuerySingleOrDefaultAsync<dynamic>(sql);

            // Assert
            result.Should().NotBeNull();
            // The result should have the expected columns
            var resultDict = result as IDictionary<string, object>;
            resultDict.Should().ContainKey("total_users");
            resultDict.Should().ContainKey("active_users");
            resultDict.Should().ContainKey("total_properties");
            resultDict.Should().ContainKey("active_properties");
        }

        [Fact]
        public async Task Database_Constraints_AreEnforced()
        {
            // Test foreign key constraint
            var propertyId = TestHelpers.GenerateTestId("constraint-test");
            var createSql = @"
                INSERT INTO public.""Property"" (id, title, description, price, ""propertyType"", bedrooms, bathrooms, ""sellerId"", status, ""createdAt"", ""updatedAt"")
                VALUES (@id, @title, @description, @price, @propertyType, @bedrooms, @bathrooms, @sellerId, @status, @createdAt, @updatedAt)";

            var createParams = new
            {
                id = propertyId,
                title = "Constraint Test Property",
                description = "Testing constraints",
                price = 500000m,
                propertyType = "Detached House",
                bedrooms = 3,
                bathrooms = 2.5m,
                sellerId = "non-existent-user", // This should violate foreign key constraint
                status = "active",
                createdAt = DateTime.UtcNow,
                updatedAt = DateTime.UtcNow
            };

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() => ExecuteAsync(createSql, createParams));
            exception.Should().NotBeNull();
            // The exact exception type depends on the database constraints
        }

        [Fact]
        public async Task Database_Transactions_WorkCorrectly()
        {
            using var connection = await GetConnectionAsync();
            using var transaction = connection.BeginTransaction();

            try
            {
                // Insert a test user within transaction
                var userId = TestHelpers.GenerateTestId("trans-user");
                var insertSql = @"
                    INSERT INTO public.""User"" (id, email, ""fullName"", role, ""userType"", ""isActive"", ""createdAt"", ""updatedAt"")
                    VALUES (@id, @email, @fullName, @role, @userType, @isActive, @createdAt, @updatedAt)";

                await connection.ExecuteAsync(insertSql, new
                {
                    id = userId,
                    email = "<EMAIL>",
                    fullName = "Transaction Test User",
                    role = "USER",
                    userType = "Buyer",
                    isActive = true,
                    createdAt = DateTime.UtcNow,
                    updatedAt = DateTime.UtcNow
                }, transaction);

                // Verify user exists within transaction
                var selectSql = @"SELECT COUNT(*) FROM public.""User"" WHERE id = @id";
                var count = await connection.QuerySingleAsync<int>(selectSql, new { id = userId }, transaction);
                count.Should().Be(1);

                // Rollback transaction
                transaction.Rollback();

                // Verify user doesn't exist after rollback
                var countAfterRollback = await QuerySingleAsync<int>(selectSql, new { id = userId });
                countAfterRollback.Should().Be(0);
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        [Fact]
        public async Task Database_Indexes_ImprovePerformance()
        {
            // This test verifies that common queries use indexes
            // In a real scenario, you would use EXPLAIN ANALYZE to check query plans

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Query that should use email index
            var emailQuery = @"SELECT * FROM public.""User"" WHERE email = @email";
            await QuerySingleOrDefaultAsync<User>(emailQuery, new { email = "<EMAIL>" });

            stopwatch.Stop();

            // Query should complete quickly (under 100ms for indexed lookup)
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(100);
        }

        [Fact]
        public async Task ContactShare_CRUD_Operations_WorkCorrectly()
        {
            // Create
            var contactShareId = TestHelpers.GenerateTestId("crud-contact");
            var createSql = @"
                INSERT INTO public.""ContactShare"" (id, ""propertyId"", ""buyerId"", ""sellerId"", ""buyerName"", ""buyerEmail"", message, ""shareType"", status, ""createdAt"", ""updatedAt"")
                VALUES (@id, @propertyId, @buyerId, @sellerId, @buyerName, @buyerEmail, @message, @shareType, @status, @createdAt, @updatedAt)";

            var createParams = new
            {
                id = contactShareId,
                propertyId = "db-test-property-1",
                buyerId = "db-test-user-1",
                sellerId = "db-test-user-2",
                buyerName = "Test Buyer",
                buyerEmail = "<EMAIL>",
                message = "I'm interested in this property",
                shareType = 1, // ContactRequest
                status = 1, // Sent
                createdAt = DateTime.UtcNow,
                updatedAt = DateTime.UtcNow
            };

            var created = await ExecuteAsync(createSql, createParams);
            created.Should().Be(1);

            // Read
            var readSql = @"SELECT * FROM public.""ContactShare"" WHERE id = @id";
            var contactShare = await QuerySingleOrDefaultAsync<dynamic>(readSql, new { id = contactShareId });

            contactShare.Should().NotBeNull();
            ((string)contactShare.buyerName).Should().Be("Test Buyer");
            ((string)contactShare.buyerEmail).Should().Be("<EMAIL>");

            // Update
            var updateSql = @"
                UPDATE public.""ContactShare""
                SET status = @status, ""sellerResponse"" = @sellerResponse, ""respondedAt"" = @respondedAt, ""updatedAt"" = @updatedAt
                WHERE id = @id";

            var updated = await ExecuteAsync(updateSql, new
            {
                id = contactShareId,
                status = 5, // Accepted
                sellerResponse = "I'd be happy to show you the property!",
                respondedAt = DateTime.UtcNow,
                updatedAt = DateTime.UtcNow
            });
            updated.Should().Be(1);

            // Verify update
            var updatedContactShare = await QuerySingleOrDefaultAsync<dynamic>(readSql, new { id = contactShareId });
            ((int)updatedContactShare.status).Should().Be(5);

            // Delete
            var deleteSql = @"DELETE FROM public.""ContactShare"" WHERE id = @id";
            var deleted = await ExecuteAsync(deleteSql, new { id = contactShareId });
            deleted.Should().Be(1);
        }

        [Fact]
        public async Task PropertyVisitSchedule_CRUD_Operations_WorkCorrectly()
        {
            // Create
            var visitId = TestHelpers.GenerateTestId("crud-visit");
            var createSql = @"
                INSERT INTO public.""PropertyVisitSchedule"" (id, ""propertyId"", ""buyerId"", ""sellerId"", ""contactShareId"", ""requestedDate"", ""requestedTime"", status, ""visitType"", ""createdAt"", ""updatedAt"")
                VALUES (@id, @propertyId, @buyerId, @sellerId, @contactShareId, @requestedDate, @requestedTime, @status, @visitType, @createdAt, @updatedAt)";

            var createParams = new
            {
                id = visitId,
                propertyId = "db-test-property-1",
                buyerId = "db-test-user-1",
                sellerId = "db-test-user-2",
                contactShareId = "test-contact-share-1",
                requestedDate = DateTime.Today.AddDays(7),
                requestedTime = TimeSpan.FromHours(14),
                status = 1, // Pending
                visitType = 1, // InPerson
                createdAt = DateTime.UtcNow,
                updatedAt = DateTime.UtcNow
            };

            var created = await ExecuteAsync(createSql, createParams);
            created.Should().Be(1);

            // Read
            var readSql = @"SELECT * FROM public.""PropertyVisitSchedule"" WHERE id = @id";
            var visit = await QuerySingleOrDefaultAsync<dynamic>(readSql, new { id = visitId });

            visit.Should().NotBeNull();
            ((int)visit.status).Should().Be(1);
            ((int)visit.visitType).Should().Be(1);

            // Update - Confirm visit
            var updateSql = @"
                UPDATE public.""PropertyVisitSchedule""
                SET status = @status, ""confirmedDate"" = @confirmedDate, ""confirmedTime"" = @confirmedTime, ""respondedAt"" = @respondedAt, ""updatedAt"" = @updatedAt
                WHERE id = @id";

            var updated = await ExecuteAsync(updateSql, new
            {
                id = visitId,
                status = 2, // Confirmed
                confirmedDate = DateTime.Today.AddDays(7),
                confirmedTime = TimeSpan.FromHours(15),
                respondedAt = DateTime.UtcNow,
                updatedAt = DateTime.UtcNow
            });
            updated.Should().Be(1);

            // Delete
            var deleteSql = @"DELETE FROM public.""PropertyVisitSchedule"" WHERE id = @id";
            var deleted = await ExecuteAsync(deleteSql, new { id = visitId });
            deleted.Should().Be(1);
        }

        [Fact]
        public async Task SellerAvailability_CRUD_Operations_WorkCorrectly()
        {
            // Create
            var availabilityId = TestHelpers.GenerateTestId("crud-avail");
            var createSql = @"
                INSERT INTO public.""SellerAvailability"" (id, ""propertyId"", ""sellerId"", ""dayOfWeek"", ""startTime"", ""endTime"", ""isAvailable"", ""createdAt"", ""updatedAt"")
                VALUES (@id, @propertyId, @sellerId, @dayOfWeek, @startTime, @endTime, @isAvailable, @createdAt, @updatedAt)";

            var createParams = new
            {
                id = availabilityId,
                propertyId = "db-test-property-1",
                sellerId = "db-test-user-2",
                dayOfWeek = 1, // Monday
                startTime = TimeSpan.FromHours(9),
                endTime = TimeSpan.FromHours(17),
                isAvailable = true,
                createdAt = DateTime.UtcNow,
                updatedAt = DateTime.UtcNow
            };

            var created = await ExecuteAsync(createSql, createParams);
            created.Should().Be(1);

            // Read
            var readSql = @"SELECT * FROM public.""SellerAvailability"" WHERE id = @id";
            var availability = await QuerySingleOrDefaultAsync<dynamic>(readSql, new { id = availabilityId });

            availability.Should().NotBeNull();
            ((bool)availability.isAvailable).Should().BeTrue();
            ((int)availability.dayOfWeek).Should().Be(1);

            // Delete
            var deleteSql = @"DELETE FROM public.""SellerAvailability"" WHERE id = @id";
            var deleted = await ExecuteAsync(deleteSql, new { id = availabilityId });
            deleted.Should().Be(1);
        }
    }
}
