using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    [Table("waiting_list", Schema = "snb")]
    public class WaitingList
    {
        [Key]
        [Column("id")]
        public string Id { get; set; } = string.Empty;

        [Required]
        [Column("email")]
        [MaxLength(255)]
        public string Email { get; set; } = string.Empty;

        [Column("name")]
        [MaxLength(255)]
        public string? Name { get; set; }

        [Column("source")]
        [MaxLength(100)]
        public string? Source { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        [Column("subscribed")]
        public bool Subscribed { get; set; } = true;

        [Column("metadata", TypeName = "jsonb")]
        public string? Metadata { get; set; }
    }
}
