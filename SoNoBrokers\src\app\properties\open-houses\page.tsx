import { OpenHouseSearchClient } from '@/components/shared/properties/OpenHouseSearchClient';

export default async function OpenHousesPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const resolvedSearchParams = await searchParams;

  const queryParams = new URLSearchParams();

  // Date filters
  if (resolvedSearchParams.date_from) {
    queryParams.set('dateFrom', resolvedSearchParams.date_from as string);
  }
  if (resolvedSearchParams.date_to) {
    queryParams.set('dateTo', resolvedSearchParams.date_to as string);
  }

  // Location filters
  if (resolvedSearchParams.location) {
    queryParams.set('location', resolvedSearchParams.location as string);
  }
  if (resolvedSearchParams.distance) {
    queryParams.set('distance', resolvedSearchParams.distance as string);
  }

  // Property filters
  if (resolvedSearchParams.property_type) {
    queryParams.set('propertyType', resolvedSearchParams.property_type as string);
  }
  if (resolvedSearchParams.min_price) {
    queryParams.set('minPrice', resolvedSearchParams.min_price as string);
  }
  if (resolvedSearchParams.max_price) {
    queryParams.set('maxPrice', resolvedSearchParams.max_price as string);
  }
  if (resolvedSearchParams.min_bedrooms) {
    queryParams.set('minBedrooms', resolvedSearchParams.min_bedrooms as string);
  }
  if (resolvedSearchParams.max_bedrooms) {
    queryParams.set('maxBedrooms', resolvedSearchParams.max_bedrooms as string);
  }

  // Map bounds
  if (resolvedSearchParams.northEastLat) {
    queryParams.set('northEastLat', resolvedSearchParams.northEastLat as string);
  }
  if (resolvedSearchParams.northEastLng) {
    queryParams.set('northEastLng', resolvedSearchParams.northEastLng as string);
  }
  if (resolvedSearchParams.southWestLat) {
    queryParams.set('southWestLat', resolvedSearchParams.southWestLat as string);
  }
  if (resolvedSearchParams.southWestLng) {
    queryParams.set('southWestLng', resolvedSearchParams.southWestLng as string);
  }

  // For server-side rendering, we'll start with empty open houses
  // The client will fetch the data on mount
  const initialOpenHouses: any[] = [];

  return (
    <OpenHouseSearchClient initialOpenHouses={initialOpenHouses} />
  );
}
