'use client';

import { useState } from 'react';
import { AIPropertyService, OpenHouseData, PropertyReport } from '@/lib/ai-services';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
    Download,
    Import,
    Sparkles,
    CheckCircle2,
    FileText,
    Star,
    ShoppingBag,
    Utensils,
    Bus,
    Heart,
    Dumbbell,
    Camera,
    Landmark,
    Shield,
    Truck,
    Home,
    Footprints
} from 'lucide-react';

interface AIDataImporterProps {
    openHouseData: OpenHouseData;
    onImport: (selectedData: any) => void;
    className?: string;
}

export function AIDataImporter({ openHouseData, onImport, className = '' }: AIDataImporterProps) {
    const [selectedReports, setSelectedReports] = useState<string[]>([]);
    const [importDescription, setImportDescription] = useState(true);
    const [importing, setImporting] = useState(false);

    const reportIcons = {
        schools: Star,
        shopping: ShoppingBag,
        grocery: ShoppingBag,
        dining: Utensils,
        transit: Bus,
        healthcare: Heart,
        fitness: Dumbbell,
        entertainment: Camera,
        landmarks: Landmark,
        services: Shield,
        movein: Truck,
        neighborhood: Home,
        walkability: Footprints
    };

    const handleReportToggle = (reportId: string) => {
        setSelectedReports(prev =>
            prev.includes(reportId)
                ? prev.filter(id => id !== reportId)
                : [...prev, reportId]
        );
    };

    const handleSelectAll = () => {
        if (selectedReports.length === openHouseData.reports.length) {
            setSelectedReports([]);
        } else {
            setSelectedReports(openHouseData.reports.map(r => r.id));
        }
    };

    const handleImport = async () => {
        setImporting(true);

        const importData = {
            description: importDescription ? openHouseData.description : null,
            reports: openHouseData.reports.filter(r => selectedReports.includes(r.id)),
            importedAt: new Date()
        };

        // Simulate import process
        await new Promise(resolve => setTimeout(resolve, 2000));

        onImport(importData);
        setImporting(false);
    };

    const getReportIcon = (type: string) => {
        const IconComponent = reportIcons[type as keyof typeof reportIcons] || FileText;
        return <IconComponent className="h-4 w-4" />;
    };

    return (
        <div className={`space-y-6 ${className}`}>
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Import className="h-5 w-5 text-primary" />
                        Import AI-Generated Data
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                        Select which AI-generated data to import into your property listing.
                        This will enhance your listing with comprehensive neighborhood insights.
                    </p>
                </CardHeader>
            </Card>

            <Tabs defaultValue="select" className="space-y-6">
                <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="select">Select Data</TabsTrigger>
                    <TabsTrigger value="preview">Preview</TabsTrigger>
                </TabsList>

                <TabsContent value="select" className="space-y-6">
                    {/* Description Import */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Property Description</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-start gap-3">
                                <Checkbox
                                    checked={importDescription}
                                    onCheckedChange={(checked) => setImportDescription(checked === true)}
                                    className="mt-1"
                                />
                                <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                        <Sparkles className="h-4 w-4 text-primary" />
                                        <span className="font-medium">AI-Enhanced Description</span>
                                        <Badge variant="secondary">Premium</Badge>
                                    </div>
                                    <p className="text-sm text-muted-foreground mb-3">
                                        Replace your current description with AI-optimized content designed to attract buyers.
                                    </p>
                                    <div className="p-3 bg-muted rounded-lg">
                                        <p className="text-sm leading-relaxed">
                                            {openHouseData.description.substring(0, 200)}...
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Reports Selection */}
                    <Card>
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <CardTitle className="text-lg">Neighborhood Reports</CardTitle>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handleSelectAll}
                                >
                                    {selectedReports.length === openHouseData.reports.length ? 'Deselect All' : 'Select All'}
                                </Button>
                            </div>
                            <p className="text-sm text-muted-foreground">
                                Choose which neighborhood insights to include with your listing.
                            </p>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                {openHouseData.reports.map((report) => (
                                    <div
                                        key={report.id}
                                        className="flex items-start gap-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                                    >
                                        <Checkbox
                                            checked={selectedReports.includes(report.id)}
                                            onCheckedChange={() => handleReportToggle(report.id)}
                                            className="mt-1"
                                        />
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                                <div className="p-1 rounded bg-primary/10 text-primary">
                                                    {getReportIcon(report.type)}
                                                </div>
                                                <span className="font-medium text-sm">{report.title}</span>
                                                {report.score && (
                                                    <Badge variant="outline" className="text-xs">
                                                        {report.score}/100
                                                    </Badge>
                                                )}
                                            </div>
                                            <p className="text-xs text-muted-foreground">
                                                {report.description}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="preview" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Import Preview</CardTitle>
                            <p className="text-sm text-muted-foreground">
                                Review what will be imported into your property listing.
                            </p>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            {/* Description Preview */}
                            {importDescription && (
                                <div>
                                    <div className="flex items-center gap-2 mb-3">
                                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                                        <span className="font-medium">Enhanced Description</span>
                                    </div>
                                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                                        <p className="text-sm leading-relaxed">
                                            {openHouseData.description}
                                        </p>
                                    </div>
                                </div>
                            )}

                            {/* Selected Reports Preview */}
                            {selectedReports.length > 0 && (
                                <div>
                                    <div className="flex items-center gap-2 mb-3">
                                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                                        <span className="font-medium">
                                            {selectedReports.length} Neighborhood Reports
                                        </span>
                                    </div>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                        {openHouseData.reports
                                            .filter(r => selectedReports.includes(r.id))
                                            .map((report) => (
                                                <div key={report.id} className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                                                    <div className="p-1 rounded bg-primary/10 text-primary">
                                                        {getReportIcon(report.type)}
                                                    </div>
                                                    <div>
                                                        <p className="font-medium text-sm">{report.title}</p>
                                                        {report.score && (
                                                            <Badge variant="outline" className="text-xs mt-1">
                                                                Score: {report.score}/100
                                                            </Badge>
                                                        )}
                                                    </div>
                                                </div>
                                            ))
                                        }
                                    </div>
                                </div>
                            )}

                            {!importDescription && selectedReports.length === 0 && (
                                <div className="text-center py-8 text-muted-foreground">
                                    <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                    <p>No data selected for import</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>

            {/* Import Actions */}
            <div className="flex gap-3">
                <Button
                    onClick={handleImport}
                    disabled={!importDescription && selectedReports.length === 0 || importing}
                    className="gap-2"
                >
                    {importing ? (
                        <>
                            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                            Importing...
                        </>
                    ) : (
                        <>
                            <Download className="h-4 w-4" />
                            Import Selected Data
                        </>
                    )}
                </Button>

                <Button variant="outline" className="gap-2">
                    <FileText className="h-4 w-4" />
                    Export as PDF
                </Button>
            </div>

            {/* Import Summary */}
            {(importDescription || selectedReports.length > 0) && (
                <Card className="bg-blue-50 border-blue-200">
                    <CardContent className="pt-6">
                        <div className="flex items-start gap-3">
                            <Sparkles className="h-5 w-5 text-blue-600 mt-0.5" />
                            <div>
                                <h4 className="font-medium text-blue-900 mb-1">
                                    Ready to Import
                                </h4>
                                <div className="text-sm text-blue-700 space-y-1">
                                    {importDescription && (
                                        <p>• AI-enhanced property description</p>
                                    )}
                                    {selectedReports.length > 0 && (
                                        <p>• {selectedReports.length} neighborhood insight reports</p>
                                    )}
                                    <p className="mt-2">
                                        This will enhance your listing's appeal and provide valuable information to potential buyers.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
