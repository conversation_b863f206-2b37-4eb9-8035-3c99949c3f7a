using QRCoder;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;

namespace MicroSaasWebApi.Services.SoNoBrokers
{
    /// <summary>
    /// Service for generating and validating QR codes for property visits
    /// </summary>
    public class QrCodeService : IQrCodeService
    {
        private readonly ILogger<QrCodeService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _encryptionKey;

        public QrCodeService(ILogger<QrCodeService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _encryptionKey = configuration["Security:QrCodeEncryptionKey"] ?? "SoNoBrokers-QR-Key-2024";
        }

        /// <summary>
        /// Generate QR code as base64 string
        /// </summary>
        public Task<string> GenerateQrCodeAsync(string data)
        {
            try
            {
                using var qrGenerator = new QRCodeGenerator();
                using var qrCodeData = qrGenerator.CreateQrCode(data, QRCodeGenerator.ECCLevel.Q);
                using var qrCode = new PngByteQRCode(qrCodeData);
                var imageBytes = qrCode.GetGraphic(20);
                return Task.FromResult(Convert.ToBase64String(imageBytes));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate QR code for data: {Data}", data);
                throw;
            }
        }

        /// <summary>
        /// Generate QR code as byte array
        /// </summary>
        public Task<byte[]> GenerateQrCodeImageAsync(string data)
        {
            try
            {
                using var qrGenerator = new QRCodeGenerator();
                using var qrCodeData = qrGenerator.CreateQrCode(data, QRCodeGenerator.ECCLevel.Q);
                using var qrCode = new PngByteQRCode(qrCodeData);
                var imageBytes = qrCode.GetGraphic(20);
                return Task.FromResult(imageBytes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate QR code image for data: {Data}", data);
                throw;
            }
        }

        /// <summary>
        /// Validate QR code data against expected data
        /// </summary>
        public Task<bool> ValidateQrCodeAsync(string qrCodeData, string expectedData)
        {
            try
            {
                // Decrypt the QR code data
                var decryptedData = DecryptQrCodeData(qrCodeData);
                if (decryptedData == null)
                {
                    return Task.FromResult(false);
                }

                // Check if the QR code has expired
                if (decryptedData.ExpiresAt < DateTime.UtcNow)
                {
                    _logger.LogWarning("QR code has expired. Expires: {ExpiresAt}, Current: {Now}",
                        decryptedData.ExpiresAt, DateTime.UtcNow);
                    return Task.FromResult(false);
                }

                // Validate the signature
                var expectedSignature = GenerateSignature(decryptedData.PropertyId, decryptedData.SellerId, decryptedData.ExpiresAt);
                if (decryptedData.Signature != expectedSignature)
                {
                    _logger.LogWarning("QR code signature validation failed");
                    return Task.FromResult(false);
                }

                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to validate QR code");
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// Encrypt QR code data with property and seller information
        /// </summary>
        public string EncryptQrCodeData(string propertyId, string sellerId, DateTime expiresAt)
        {
            try
            {
                var signature = GenerateSignature(propertyId, sellerId, expiresAt);

                var qrData = new QrCodeData
                {
                    PropertyId = propertyId,
                    SellerId = sellerId,
                    ExpiresAt = expiresAt,
                    Signature = signature
                };

                var jsonData = JsonSerializer.Serialize(qrData);
                var encryptedData = EncryptString(jsonData);

                return encryptedData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to encrypt QR code data for property {PropertyId}", propertyId);
                throw;
            }
        }

        /// <summary>
        /// Decrypt QR code data
        /// </summary>
        public QrCodeData? DecryptQrCodeData(string encryptedData)
        {
            try
            {
                var decryptedJson = DecryptString(encryptedData);
                var qrData = JsonSerializer.Deserialize<QrCodeData>(decryptedJson);

                return qrData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to decrypt QR code data");
                return null;
            }
        }

        /// <summary>
        /// Generate signature for QR code validation
        /// </summary>
        private string GenerateSignature(string propertyId, string sellerId, DateTime expiresAt)
        {
            var data = $"{propertyId}|{sellerId}|{expiresAt:yyyy-MM-ddTHH:mm:ssZ}|{_encryptionKey}";

            using var sha256 = SHA256.Create();
            var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(data));
            return Convert.ToBase64String(hashBytes);
        }

        /// <summary>
        /// Encrypt string using AES
        /// </summary>
        private string EncryptString(string plainText)
        {
            using var aes = Aes.Create();
            aes.Key = GetKeyBytes();
            aes.IV = new byte[16]; // Use zero IV for simplicity (in production, use random IV)

            using var encryptor = aes.CreateEncryptor();
            using var msEncrypt = new MemoryStream();
            using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
            using var swEncrypt = new StreamWriter(csEncrypt);

            swEncrypt.Write(plainText);
            swEncrypt.Close();

            return Convert.ToBase64String(msEncrypt.ToArray());
        }

        /// <summary>
        /// Decrypt string using AES
        /// </summary>
        private string DecryptString(string cipherText)
        {
            using var aes = Aes.Create();
            aes.Key = GetKeyBytes();
            aes.IV = new byte[16]; // Use zero IV for simplicity (in production, use random IV)

            using var decryptor = aes.CreateDecryptor();
            using var msDecrypt = new MemoryStream(Convert.FromBase64String(cipherText));
            using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
            using var srDecrypt = new StreamReader(csDecrypt);

            return srDecrypt.ReadToEnd();
        }

        /// <summary>
        /// Get encryption key as byte array
        /// </summary>
        private byte[] GetKeyBytes()
        {
            using var sha256 = SHA256.Create();
            return sha256.ComputeHash(Encoding.UTF8.GetBytes(_encryptionKey));
        }
    }

    /// <summary>
    /// Calendar invite service for sending visit invitations
    /// </summary>
    public class CalendarInviteService : ICalendarInviteService
    {
        private readonly ILogger<CalendarInviteService> _logger;
        private readonly IConfiguration _configuration;

        public CalendarInviteService(ILogger<CalendarInviteService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        public async Task<bool> SendInviteAsync(string visitId, PropertyVisitScheduleResponse visit)
        {
            try
            {
                // Generate ICS file content
                var icsContent = await GenerateIcsFileAsync(visit);

                // Here you would integrate with your email service to send the calendar invite
                // For now, we'll just log the action
                _logger.LogInformation("Calendar invite sent for visit {VisitId}", visitId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send calendar invite for visit {VisitId}", visitId);
                return false;
            }
        }

        public async Task<bool> UpdateInviteAsync(string visitId, PropertyVisitScheduleResponse visit)
        {
            try
            {
                // Generate updated ICS file content
                var icsContent = await GenerateIcsFileAsync(visit);

                // Send updated invite
                _logger.LogInformation("Calendar invite updated for visit {VisitId}", visitId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update calendar invite for visit {VisitId}", visitId);
                return false;
            }
        }

        public Task<bool> CancelInviteAsync(string visitId, PropertyVisitScheduleResponse visit)
        {
            try
            {
                // Generate cancellation ICS file content
                var icsContent = GenerateCancellationIcs(visit);

                // Send cancellation
                _logger.LogInformation("Calendar invite cancelled for visit {VisitId}", visitId);

                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to cancel calendar invite for visit {VisitId}", visitId);
                return Task.FromResult(false);
            }
        }

        public Task<string> GenerateIcsFileAsync(PropertyVisitScheduleResponse visit)
        {
            var startDateTime = visit.ConfirmedDate?.Add(visit.ConfirmedTime ?? TimeSpan.Zero)
                               ?? visit.RequestedDate.Add(visit.RequestedTime);
            var endDateTime = startDateTime.AddHours(1); // Default 1-hour duration

            var icsContent = $@"BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//SoNoBrokers//Property Visit//EN
CALSCALE:GREGORIAN
METHOD:REQUEST
BEGIN:VEVENT
UID:{visit.Id}@sonobrokers.com
DTSTAMP:{DateTime.UtcNow:yyyyMMddTHHmmssZ}
DTSTART:{startDateTime:yyyyMMddTHHmmssZ}
DTEND:{endDateTime:yyyyMMddTHHmmssZ}
SUMMARY:Property Visit - {visit.PropertyTitle}
DESCRIPTION:Property visit scheduled for {visit.PropertyTitle} at {visit.PropertyAddress}.
LOCATION:{visit.PropertyAddress}
ORGANIZER:CN={visit.SellerName}:MAILTO:{visit.BuyerEmail}
ATTENDEE:CN={visit.BuyerName}:MAILTO:{visit.BuyerEmail}
STATUS:CONFIRMED
SEQUENCE:0
END:VEVENT
END:VCALENDAR";

            return Task.FromResult(icsContent);
        }

        private string GenerateCancellationIcs(PropertyVisitScheduleResponse visit)
        {
            var startDateTime = visit.ConfirmedDate?.Add(visit.ConfirmedTime ?? TimeSpan.Zero)
                               ?? visit.RequestedDate.Add(visit.RequestedTime);
            var endDateTime = startDateTime.AddHours(1);

            var icsContent = $@"BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//SoNoBrokers//Property Visit//EN
CALSCALE:GREGORIAN
METHOD:CANCEL
BEGIN:VEVENT
UID:{visit.Id}@sonobrokers.com
DTSTAMP:{DateTime.UtcNow:yyyyMMddTHHmmssZ}
DTSTART:{startDateTime:yyyyMMddTHHmmssZ}
DTEND:{endDateTime:yyyyMMddTHHmmssZ}
SUMMARY:CANCELLED: Property Visit - {visit.PropertyTitle}
DESCRIPTION:Property visit has been cancelled.
LOCATION:{visit.PropertyAddress}
ORGANIZER:CN={visit.SellerName}:MAILTO:{visit.BuyerEmail}
ATTENDEE:CN={visit.BuyerName}:MAILTO:{visit.BuyerEmail}
STATUS:CANCELLED
SEQUENCE:1
END:VEVENT
END:VCALENDAR";

            return icsContent;
        }
    }
}
