import { PhotographyService } from './PhotographyService'
// Removed auth import - no authentication required for browsing photography services
import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function PhotographyPage({ params, searchParams }: PageProps) {
  // No authentication required for browsing photography services
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/services/photography')
  }

  // Default to seller for photography services
  const userType = resolvedSearchParams.userType || 'seller'

  return (
    <PhotographyService
      userType={userType}
      isSignedIn={isSignedIn}
      country={country.toUpperCase()}
    />
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'seller'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Real Estate Photography Services for ${userType === 'buyer' ? 'Buyers' : 'Sellers'} in ${country} | SoNoBrokers`,
    description: `Professional real estate photography services in ${country}. High-quality photos, virtual tours, and drone footage for property listings. Compare photographers and book online.`,
    keywords: `real estate photography, property photos, virtual tours, ${userType}, ${country}, listing photos`,
  }
}
