'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import GoogleMap from '@/components/shared/properties/GoogleMap'
import { cn } from '@/lib/utils'
import {
  Star,
  MapPin,
  Phone,
  Mail,
  Globe,
  Shield,
  CheckCircle,
  Filter,
  X,
  Search,
  SlidersHorizontal,
  Grid,
  Map as MapIcon
} from 'lucide-react'
import { ServiceProvider, ServiceLayoutProps } from './ServiceLayout'

export function ServiceLayoutClient({
  userType,
  isSignedIn,
  serviceTitle,
  serviceDescription,
  country,
  providers,
  showGoogleProviders = true
}: ServiceLayoutProps) {
  // State for filters and UI
  const [searchQuery, setSearchQuery] = useState('')
  const [distanceRange, setDistanceRange] = useState([10])
  const [ratingFilter, setRatingFilter] = useState('all')
  const [priceRange, setPriceRange] = useState('all')
  const [serviceTypeFilter, setServiceTypeFilter] = useState('all')
  const [isFiltersVisible, setIsFiltersVisible] = useState(false)
  const [activeViews, setActiveViews] = useState<Set<'grid' | 'map'>>(new Set(['map', 'grid']))
  const [mapCenter, setMapCenter] = useState({ lat: 43.6532, lng: -79.3832 }) // Toronto default
  const [mapBounds, setMapBounds] = useState<any>(null)
  const [filteredProviders, setFilteredProviders] = useState(providers)

  // Filter providers based on search criteria
  useEffect(() => {
    let filtered = providers

    if (searchQuery) {
      filtered = filtered.filter(p =>
        p.businessName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        p.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
        p.specialties.some(s => s.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    if (ratingFilter !== 'all') {
      const minRating = parseFloat(ratingFilter)
      filtered = filtered.filter(p => p.rating >= minRating)
    }

    if (serviceTypeFilter !== 'all') {
      filtered = filtered.filter(p => p.serviceType.toLowerCase().includes(serviceTypeFilter.toLowerCase()))
    }

    // Sort by advertiser status, premium status, then by distance
    filtered.sort((a, b) => {
      // First priority: Enterprise advertisers
      const aIsEnterprise = a.advertiser?.plan === 'enterprise'
      const bIsEnterprise = b.advertiser?.plan === 'enterprise'
      if (aIsEnterprise && !bIsEnterprise) return -1
      if (!aIsEnterprise && bIsEnterprise) return 1

      // Second priority: Premium advertisers
      const aIsPremium = a.advertiser?.plan === 'premium' || a.isPremium
      const bIsPremium = b.advertiser?.plan === 'premium' || b.isPremium
      if (aIsPremium && !bIsPremium) return -1
      if (!aIsPremium && bIsPremium) return 1

      // Third priority: Any advertiser
      if (a.isAdvertiser && !b.isAdvertiser) return -1
      if (!a.isAdvertiser && b.isAdvertiser) return 1

      // Finally sort by rating, then distance
      if (a.rating !== b.rating) return b.rating - a.rating
      return parseFloat(a.distance) - parseFloat(b.distance)
    })

    setFilteredProviders(filtered)
  }, [providers, searchQuery, ratingFilter, serviceTypeFilter])

  const handleMapMove = (bounds: any) => {
    setMapBounds(bounds)
    // Filter providers based on map bounds if needed
  }

  const clearFilters = () => {
    setSearchQuery('')
    setDistanceRange([10])
    setRatingFilter('all')
    setPriceRange('all')
    setServiceTypeFilter('all')
  }

  const renderProviderCard = (provider: ServiceProvider) => (
    <Card key={provider.id} className={`relative ${provider.isPremium ? 'border-primary shadow-lg' : ''} hover:shadow-md transition-shadow`}>
      {provider.isPremium && (
        <Badge className="absolute -top-2 -right-2 bg-primary text-white">
          Premium
        </Badge>
      )}
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            {provider.image && (
              <img
                src={provider.image}
                alt={provider.name}
                className="w-12 h-12 rounded-full object-cover"
              />
            )}
            <div>
              <CardTitle className="text-lg">{provider.businessName || provider.name}</CardTitle>
              <p className="text-sm text-muted-foreground">{provider.serviceType}</p>
              <div className="flex items-center space-x-2 mt-1">
                <MapPin className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">{provider.location}</span>
                <span className="text-sm text-muted-foreground">• {provider.distance}</span>
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="flex items-center space-x-1">
              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
              <span className="font-medium">{provider.rating}</span>
              <span className="text-sm text-muted-foreground">({provider.reviewCount})</span>
            </div>
            <p className="text-lg font-semibold text-primary mt-1">{provider.price}</p>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {provider.description && (
          <p className="text-sm text-muted-foreground mb-3">{provider.description}</p>
        )}
        <div className="flex flex-wrap gap-1 mb-4">
          {provider.specialties.map((specialty, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {specialty}
            </Badge>
          ))}
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            {provider.verified && (
              <div className="flex items-center space-x-1">
                <Shield className="w-4 h-4 text-green-500" />
                <span>Verified</span>
              </div>
            )}
            {provider.advertiser?.plan === 'enterprise' && (
              <span className="px-2 py-1 text-xs font-bold bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full">
                ENTERPRISE
              </span>
            )}
            {provider.advertiser?.plan === 'premium' && (
              <span className="px-2 py-1 text-xs font-bold bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-full">
                PREMIUM
              </span>
            )}
            {provider.advertiser?.plan === 'basic' && (
              <span className="px-2 py-1 text-xs font-semibold bg-green-100 text-green-800 rounded-full">
                ADVERTISER
              </span>
            )}
            {provider.isAdvertiser && !provider.advertiser && (
              <div className="flex items-center space-x-1">
                <CheckCircle className="w-4 h-4 text-blue-500" />
                <span>Registered</span>
              </div>
            )}
          </div>
          <div className="flex space-x-2">
            {provider.phone && (
              <Button size="sm" variant="outline">
                <Phone className="w-4 h-4 mr-1" />
                Call
              </Button>
            )}
            <Button size="sm">
              Contact
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="flex flex-col h-screen bg-background">

      {/* Search and Filter Bar */}
      <div className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={`Search ${serviceTitle.toLowerCase()} providers...`}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button
                variant="outline"
                onClick={() => setIsFiltersVisible(!isFiltersVisible)}
                className="flex items-center gap-2"
              >
                <SlidersHorizontal className="h-4 w-4" />
                Filters
              </Button>
            </div>

            {/* View Toggle */}
            <div className="flex items-center gap-2">
              <Button
                variant={activeViews.has('map') ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  const newViews = new Set(activeViews)
                  if (newViews.has('map')) {
                    newViews.delete('map')
                  } else {
                    newViews.add('map')
                  }
                  if (newViews.size === 0) newViews.add('grid')
                  setActiveViews(newViews)
                }}
              >
                <MapIcon className="h-4 w-4" />
              </Button>
              <Button
                variant={activeViews.has('grid') ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  const newViews = new Set(activeViews)
                  if (newViews.has('grid')) {
                    newViews.delete('grid')
                  } else {
                    newViews.add('grid')
                  }
                  if (newViews.size === 0) newViews.add('map')
                  setActiveViews(newViews)
                }}
              >
                <Grid className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Filters Panel */}
      {isFiltersVisible && (
        <div className="border-b border-border bg-card">
          <div className="px-4 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="location">Location</Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="location"
                    placeholder="City, address, or postal code"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Distance</Label>
                <div className="px-3">
                  <Slider
                    value={distanceRange}
                    onValueChange={setDistanceRange}
                    max={50}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>1 km</span>
                    <span>{distanceRange[0]} km</span>
                    <span>50 km</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Minimum Rating</Label>
                <Select value={ratingFilter} onValueChange={setRatingFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Any rating" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Any rating</SelectItem>
                    <SelectItem value="4.5">4.5+ stars</SelectItem>
                    <SelectItem value="4.0">4.0+ stars</SelectItem>
                    <SelectItem value="3.5">3.5+ stars</SelectItem>
                    <SelectItem value="3.0">3.0+ stars</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Service Type</Label>
                <Select value={serviceTypeFilter} onValueChange={setServiceTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All types</SelectItem>
                    <SelectItem value="certified">Certified</SelectItem>
                    <SelectItem value="licensed">Licensed</SelectItem>
                    <SelectItem value="verified">Verified</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-between items-center mt-4 pt-4 border-t border-border">
              <Button variant="outline" onClick={clearFilters}>
                <X className="h-4 w-4 mr-2" />
                Clear All
              </Button>
              <Button onClick={() => setIsFiltersVisible(false)}>
                Apply Filters
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Main Content - Map and Grid Layout */}
      <div className="flex flex-1 overflow-hidden">
        <div className="flex w-full h-full">
          {/* Map Section */}
          {activeViews.has('map') && (
            <div className={cn(
              "h-full",
              !activeViews.has('grid') ? "w-full" : "w-1/2 border-r-2 border-border"
            )}>
              <GoogleMap
                properties={filteredProviders.map(provider => ({
                  id: provider.id,
                  title: provider.businessName,
                  address: provider.location,
                  price: 0, // Service providers don't have numeric prices
                  coordinates: provider.coordinates,
                  images: provider.image ? [{ url: provider.image, alt: provider.businessName }] : [],
                  bedrooms: 0,
                  bathrooms: 0,
                  propertyType: provider.serviceType,
                  listingType: provider.isAdvertiser ? 'Premium' : 'Standard',
                  description: provider.description || '',
                  yearBuilt: null as number | null,
                  squareFootage: null as number | null,
                  lotSize: null as number | null,
                  features: provider.specialties || [],
                  status: 'active',
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString()
                }))}
                onMapMove={handleMapMove}
                initialCenter={mapCenter}
                searchRadius={distanceRange[0]}
                showRadius={true}
                onPropertyClick={(property) => console.log('Provider clicked:', property)}
              />
            </div>
          )}

          {/* Grid Section */}
          {activeViews.has('grid') && (
            <div className={cn(
              "flex flex-col",
              !activeViews.has('map') ? "w-full" : "w-1/2"
            )}>
              <div className="p-4 border-b border-border bg-background">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold">{serviceTitle}</h2>
                  <p className="text-sm text-muted-foreground">
                    {filteredProviders.length} provider{filteredProviders.length !== 1 ? 's' : ''} found
                  </p>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  {serviceDescription}
                </p>
              </div>

              <div className="flex-1 overflow-y-auto p-4">
                {filteredProviders.length > 0 ? (
                  <div className="space-y-4">
                    {filteredProviders.map(renderProviderCard)}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <h3 className="text-xl font-semibold text-muted-foreground mb-2">
                      No providers found
                    </h3>
                    <p className="text-muted-foreground">
                      Try adjusting your search criteria or expanding your search area.
                    </p>
                    <Button onClick={clearFilters} className="mt-4">
                      Clear Filters
                    </Button>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
