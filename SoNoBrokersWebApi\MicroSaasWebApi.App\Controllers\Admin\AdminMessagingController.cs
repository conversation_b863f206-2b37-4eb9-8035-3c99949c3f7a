using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Extensions;
using MicroSaasWebApi.Models.SoNoBrokers.Messaging;
using MicroSaasWebApi.Services.SoNoBrokers;

namespace MicroSaasWebApi.Controllers.Admin
{
    /// <summary>
    /// Admin controller for managing and monitoring all conversations and messages
    /// </summary>
    [ApiController]
    [Route("api/admin/messaging")]
    [Authorize(Roles = "ADMIN")]
    public class AdminMessagingController : ControllerBase
    {
        private readonly IMessagingService _messagingService;
        private readonly ILogger<AdminMessagingController> _logger;

        public AdminMessagingController(IMessagingService messagingService, ILogger<AdminMessagingController> logger)
        {
            _messagingService = messagingService;
            _logger = logger;
        }

        /// <summary>
        /// Get all conversations in the system (admin only)
        /// </summary>
        [HttpGet("conversations")]
        public async Task<ActionResult<ConversationSearchResponse>> GetAllConversations([FromQuery] ConversationSearchParams searchParams)
        {
            try
            {
                var conversations = await _messagingService.GetAdminConversationsAsync(searchParams);
                return Ok(conversations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting admin conversations");
                return StatusCode(500, "An error occurred while retrieving conversations");
            }
        }

        /// <summary>
        /// Get a specific conversation with all details (admin only)
        /// </summary>
        [HttpGet("conversations/{conversationId}")]
        public async Task<ActionResult<ConversationResponse>> GetConversation(string conversationId)
        {
            try
            {
                // Admin can access any conversation, so we use a dummy user ID
                var conversation = await _messagingService.GetConversationAsync(conversationId, "admin");
                if (conversation == null)
                {
                    return NotFound("Conversation not found");
                }

                return Ok(conversation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting admin conversation {ConversationId}", conversationId);
                return StatusCode(500, "An error occurred while retrieving the conversation");
            }
        }

        /// <summary>
        /// Get all messages in a conversation (admin only)
        /// </summary>
        [HttpGet("conversations/{conversationId}/messages")]
        public async Task<ActionResult<MessageSearchResponse>> GetConversationMessages(string conversationId, [FromQuery] MessageSearchParams searchParams)
        {
            try
            {
                // Admin can access any conversation messages
                var messages = await _messagingService.GetMessagesAsync(conversationId, "admin", searchParams);
                return Ok(messages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting admin messages for conversation {ConversationId}", conversationId);
                return StatusCode(500, "An error occurred while retrieving messages");
            }
        }

        /// <summary>
        /// Get messaging statistics across the platform (admin only)
        /// </summary>
        [HttpGet("stats")]
        public async Task<ActionResult<AdminMessagingStats>> GetMessagingStats()
        {
            try
            {
                var stats = await GetAdminMessagingStatsAsync();
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting admin messaging stats");
                return StatusCode(500, "An error occurred while retrieving messaging statistics");
            }
        }

        /// <summary>
        /// Get messaging activity over time (admin only)
        /// </summary>
        [HttpGet("activity")]
        public async Task<ActionResult<List<MessagingActivity>>> GetMessagingActivity([FromQuery] DateTime? fromDate, [FromQuery] DateTime? toDate)
        {
            try
            {
                var activity = await GetMessagingActivityAsync(fromDate, toDate);
                return Ok(activity);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting messaging activity");
                return StatusCode(500, "An error occurred while retrieving messaging activity");
            }
        }

        /// <summary>
        /// Update conversation status (admin only)
        /// </summary>
        [HttpPut("conversations/{conversationId}")]
        public async Task<ActionResult> UpdateConversation(string conversationId, [FromBody] AdminUpdateConversationRequest request)
        {
            try
            {
                var updateRequest = new UpdateConversationRequest
                {
                    Subject = request.Subject,
                    IsActive = request.IsActive
                };

                // Admin can update any conversation
                var success = await _messagingService.UpdateConversationAsync(conversationId, "admin", updateRequest);
                if (!success)
                {
                    return NotFound("Conversation not found");
                }

                return Ok(new { success = true, message = "Conversation updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating admin conversation {ConversationId}", conversationId);
                return StatusCode(500, "An error occurred while updating the conversation");
            }
        }

        /// <summary>
        /// Delete conversation (admin only)
        /// </summary>
        [HttpDelete("conversations/{conversationId}")]
        public async Task<ActionResult> DeleteConversation(string conversationId)
        {
            try
            {
                // Admin can delete any conversation
                var success = await _messagingService.DeleteConversationAsync(conversationId, "admin");
                if (!success)
                {
                    return NotFound("Conversation not found");
                }

                return Ok(new { success = true, message = "Conversation deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting admin conversation {ConversationId}", conversationId);
                return StatusCode(500, "An error occurred while deleting the conversation");
            }
        }

        /// <summary>
        /// Get conversations by property (admin only)
        /// </summary>
        [HttpGet("properties/{propertyId}/conversations")]
        public async Task<ActionResult<ConversationSearchResponse>> GetPropertyConversations(string propertyId, [FromQuery] ConversationSearchParams searchParams)
        {
            try
            {
                searchParams.PropertyId = propertyId;
                var conversations = await _messagingService.GetAdminConversationsAsync(searchParams);
                return Ok(conversations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting conversations for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property conversations");
            }
        }

        /// <summary>
        /// Get conversations by user (admin only)
        /// </summary>
        [HttpGet("users/{userId}/conversations")]
        public async Task<ActionResult<ConversationSearchResponse>> GetUserConversations(string userId, [FromQuery] ConversationSearchParams searchParams)
        {
            try
            {
                searchParams.UserId = userId;
                var conversations = await _messagingService.GetAdminConversationsAsync(searchParams);
                return Ok(conversations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting conversations for user {UserId}", userId);
                return StatusCode(500, "An error occurred while retrieving user conversations");
            }
        }

        // Private helper methods for admin-specific functionality
        private Task<AdminMessagingStats> GetAdminMessagingStatsAsync()
        {
            // This would be implemented with additional database queries
            // For now, returning a placeholder
            return Task.FromResult(new AdminMessagingStats
            {
                TotalConversations = 0,
                ActiveConversations = 0,
                TotalMessages = 0,
                MessagesToday = 0,
                MessagesThisWeek = 0,
                MessagesThisMonth = 0,
                AverageResponseTime = TimeSpan.Zero,
                TopActiveUsers = new List<UserActivity>(),
                ConversationsByProperty = new List<PropertyConversationStats>()
            });
        }

        private Task<List<MessagingActivity>> GetMessagingActivityAsync(DateTime? fromDate, DateTime? toDate)
        {
            // This would be implemented with additional database queries
            // For now, returning a placeholder
            return Task.FromResult(new List<MessagingActivity>());
        }
    }

    /// <summary>
    /// Admin-specific DTOs
    /// </summary>
    public class AdminUpdateConversationRequest
    {
        public string? Subject { get; set; }
        public bool? IsActive { get; set; }
    }

    public class AdminMessagingStats
    {
        public int TotalConversations { get; set; }
        public int ActiveConversations { get; set; }
        public int TotalMessages { get; set; }
        public int MessagesToday { get; set; }
        public int MessagesThisWeek { get; set; }
        public int MessagesThisMonth { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public List<UserActivity> TopActiveUsers { get; set; } = new();
        public List<PropertyConversationStats> ConversationsByProperty { get; set; } = new();
    }

    public class UserActivity
    {
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public int MessageCount { get; set; }
        public int ConversationCount { get; set; }
        public DateTime LastActivity { get; set; }
    }

    public class PropertyConversationStats
    {
        public string PropertyId { get; set; } = string.Empty;
        public string PropertyTitle { get; set; } = string.Empty;
        public int ConversationCount { get; set; }
        public int MessageCount { get; set; }
        public DateTime LastActivity { get; set; }
    }

    public class MessagingActivity
    {
        public DateTime Date { get; set; }
        public int ConversationsStarted { get; set; }
        public int MessagesSent { get; set; }
        public int ActiveUsers { get; set; }
    }
}
