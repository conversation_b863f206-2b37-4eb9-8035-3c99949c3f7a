'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FilterBadgeProps {
  label: string
  value: string
  onRemove: () => void
  className?: string
}

export function FilterBadge({ label, value, onRemove, className }: FilterBadgeProps) {
  return (
    <Badge
      variant="secondary"
      className={cn("flex items-center gap-1 px-2 py-1 text-xs", className)}
    >
      <span className="font-medium">{label}:</span>
      <span>{value}</span>
      <Button
        variant="ghost"
        size="sm"
        className="h-4 w-4 p-0 hover:bg-transparent"
        onClick={onRemove}
      >
        <X className="h-3 w-3" />
      </Button>
    </Badge>
  )
}
