# PowerShell script to fix the malformed SaveChanges comments

param(
    [string]$RootPath = ".",
    [switch]$WhatIf = $false
)

Write-Host "Fixing malformed SaveChanges comments..." -ForegroundColor Green

# Define the folders to process
$foldersToProcess = @(
    "Controllers\SoNoBrokers",
    "Services\SoNoBrokers"
)

function Fix-File {
    param(
        [string]$FilePath
    )
    
    Write-Host "Processing: $FilePath" -ForegroundColor Yellow
    
    $content = Get-Content $FilePath -Raw
    $originalContent = $content
    
    # Fix the malformed SaveChanges comments
    $patterns = @{
        'await // SaveChanges converted to individual Execute callsAsync\(\);' = '// TODO: Convert to proper Dapper ExecuteAsync call'
        'await // SaveChanges converted to individual Execute calls\(\);' = '// TODO: Convert to proper Dapper Execute call'
        '// SaveChanges converted to individual Execute callsAsync\(\);' = '// TODO: Convert to proper Dapper ExecuteAsync call'
        '// SaveChanges converted to individual Execute calls\(\);' = '// TODO: Convert to proper Dapper Execute call'
    }
    
    $replacementCount = 0
    foreach ($find in $patterns.Keys) {
        $replace = $patterns[$find]
        if ($content -match [regex]::Escape($find)) {
            $content = $content -replace [regex]::Escape($find), $replace
            $replacementCount++
            Write-Host "  Fixed malformed SaveChanges comment" -ForegroundColor Cyan
        }
    }
    
    # Write results
    if ($replacementCount -gt 0) {
        if ($WhatIf) {
            Write-Host "  [WHAT-IF] Would make $replacementCount fixes" -ForegroundColor Magenta
        } else {
            Set-Content $FilePath $content -NoNewline
            Write-Host "  Made $replacementCount fixes" -ForegroundColor Green
        }
    }
    
    return $replacementCount -gt 0
}

function Get-FilesToProcess {
    $files = @()
    
    foreach ($folder in $foldersToProcess) {
        $fullPath = Join-Path $RootPath $folder
        if (Test-Path $fullPath) {
            $csFiles = Get-ChildItem $fullPath -Filter "*.cs" -Recurse
            $files += $csFiles
        } else {
            Write-Warning "Folder not found: $fullPath"
        }
    }
    
    return $files
}

# Main execution
try {
    $files = Get-FilesToProcess
    
    if ($files.Count -eq 0) {
        Write-Warning "No C# files found to process"
        exit 1
    }
    
    Write-Host "Found $($files.Count) C# files to process" -ForegroundColor Green
    
    if ($WhatIf) {
        Write-Host "Running in WHAT-IF mode - no files will be modified" -ForegroundColor Yellow
    }
    
    $processedFiles = 0
    $modifiedFiles = 0
    
    foreach ($file in $files) {
        $processedFiles++
        $result = Fix-File $file.FullName
        
        if ($result) {
            $modifiedFiles++
        }
    }
    
    # Summary
    Write-Host "`n=== FIX SUMMARY ===" -ForegroundColor Green
    Write-Host "Files processed: $processedFiles" -ForegroundColor White
    Write-Host "Files fixed: $modifiedFiles" -ForegroundColor Green
    
    if (!$WhatIf -and $modifiedFiles -gt 0) {
        Write-Host "`nRecommendation: Run 'dotnet build' to check for remaining compilation errors" -ForegroundColor Cyan
    }
    
} catch {
    Write-Error "Error during fix: $_"
    exit 1
}

Write-Host "`nFix script completed!" -ForegroundColor Green
