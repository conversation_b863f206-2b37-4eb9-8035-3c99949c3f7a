'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { AuthUser } from '@/lib/auth'
import {
  Users,
  UserCheck,
  UserCog,
  Building,
  Activity,
  TrendingUp,
  Settings,
  Shield
} from 'lucide-react'

interface User {
  id: string
  email: string
  fullName: string
  role: string
  userType: string
  isActive: boolean
  createdAt: string
  lastLoginAt?: string
}

interface DashboardStats {
  totalUsers: number
  adminUsers: number
  regularUsers: number
  productUsers: number
  operatorUsers: number
}

interface AdminDashboardProps {
  currentUser: AuthUser
  dashboardStats: DashboardStats
  recentUsers: User[]
}

export default function AdminDashboard({
  currentUser,
  dashboardStats,
  recentUsers
}: AdminDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview')

  const statCards = [
    {
      title: 'Total Users',
      value: dashboardStats.totalUsers,
      icon: Users,
      description: 'All registered users',
      color: 'text-blue-600'
    },
    {
      title: 'Admin Users',
      value: dashboardStats.adminUsers,
      icon: Shield,
      description: 'System administrators',
      color: 'text-red-600'
    },
    {
      title: 'Regular Users',
      value: dashboardStats.regularUsers,
      icon: UserCheck,
      description: 'Standard users',
      color: 'text-green-600'
    },
    {
      title: 'Product Users',
      value: dashboardStats.productUsers,
      icon: Building,
      description: 'Product team members',
      color: 'text-purple-600'
    }
  ]

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'bg-destructive/10 text-destructive'
      case 'PRODUCT': return 'bg-primary/10 text-primary'
      case 'OPERATOR': return 'bg-secondary/10 text-secondary-foreground'
      default: return 'bg-muted text-muted-foreground'
    }
  }

  const getUserTypeBadgeColor = (userType: string) => {
    switch (userType) {
      case 'buyer': return 'bg-primary/10 text-primary'
      case 'seller': return 'bg-secondary/10 text-secondary-foreground'
      case 'service_provider': return 'bg-accent/10 text-accent-foreground'
      default: return 'bg-muted text-muted-foreground'
    }
  }

  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => {
          const Icon = stat.icon
          return (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="users">User Management</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Users */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Users</CardTitle>
                <CardDescription>
                  Latest user registrations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentUsers.slice(0, 5).map((user) => (
                    <div key={user.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium">
                            {user.fullName?.charAt(0) || user.email.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <p className="text-sm font-medium">{user.fullName || user.email}</p>
                          <p className="text-xs text-gray-500">{user.email}</p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Badge className={getRoleBadgeColor(user.role)}>
                          {user.role}
                        </Badge>
                        <Badge className={getUserTypeBadgeColor(user.userType)}>
                          {user.userType}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common administrative tasks
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full justify-start" variant="outline">
                  <Users className="mr-2 h-4 w-4" />
                  Manage Users
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Building className="mr-2 h-4 w-4" />
                  View Properties
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Activity className="mr-2 h-4 w-4" />
                  System Analytics
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Settings className="mr-2 h-4 w-4" />
                  System Settings
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
              <CardDescription>
                Manage user accounts and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <UserCog className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">User Management</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Advanced user management features coming soon.
                </p>
                <div className="mt-6">
                  <Button>
                    <Users className="mr-2 h-4 w-4" />
                    View All Users
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Analytics</CardTitle>
              <CardDescription>
                Platform usage and performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <TrendingUp className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Analytics Dashboard</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Detailed analytics and reporting features coming soon.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Settings</CardTitle>
              <CardDescription>
                Configure system-wide settings and preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Settings className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">System Configuration</h3>
                <p className="mt-1 text-sm text-gray-500">
                  System settings and configuration options coming soon.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
