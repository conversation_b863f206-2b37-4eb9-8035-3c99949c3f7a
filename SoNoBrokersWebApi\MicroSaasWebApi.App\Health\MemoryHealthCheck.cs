using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;
using System.Diagnostics;

namespace MicroSaasWebApi.Health
{
    /// <summary>
    /// Health check for monitoring memory usage and garbage collection
    /// </summary>
    public class MemoryHealthCheck : IHealthCheck
    {
        private readonly IOptionsMonitor<MemoryCheckOptions> _options;
        private readonly ILogger<MemoryHealthCheck> _logger;

        public MemoryHealthCheck(IOptionsMonitor<MemoryCheckOptions> options, ILogger<MemoryHealthCheck> logger)
        {
            _options = options;
            _logger = logger;
        }

        public string Name => "memory_check";

        public Task<HealthCheckResult> CheckHealthAsync(
            HealthCheckContext context,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var options = _options.Get(context.Registration.Name);

                // Get memory information
                var allocated = GC.GetTotalMemory(forceFullCollection: false);
                var process = Process.GetCurrentProcess();
                var workingSet = process.WorkingSet64;
                var privateMemory = process.PrivateMemorySize64;

                // Include comprehensive memory and GC information
                var data = new Dictionary<string, object>
                {
                    // Memory Information
                    { "AllocatedBytes", allocated },
                    { "AllocatedMB", Math.Round(allocated / 1024.0 / 1024.0, 2) },
                    { "WorkingSetBytes", workingSet },
                    { "WorkingSetMB", Math.Round(workingSet / 1024.0 / 1024.0, 2) },
                    { "PrivateMemoryBytes", privateMemory },
                    { "PrivateMemoryMB", Math.Round(privateMemory / 1024.0 / 1024.0, 2) },
                    
                    // Garbage Collection Information
                    { "Gen0Collections", GC.CollectionCount(0) },
                    { "Gen1Collections", GC.CollectionCount(1) },
                    { "Gen2Collections", GC.CollectionCount(2) },
                    
                    // Thresholds
                    { "ThresholdBytes", options.Threshold },
                    { "ThresholdMB", Math.Round(options.Threshold / 1024.0 / 1024.0, 2) },
                    
                    // Additional Information
                    { "CheckedAt", DateTime.UtcNow },
                    { "ProcessId", process.Id },
                    { "ProcessName", process.ProcessName }
                };

                // Determine health status based on allocated memory
                var status = allocated < options.Threshold ? HealthStatus.Healthy : HealthStatus.Unhealthy;
                
                // Create detailed description
                var allocatedMB = Math.Round(allocated / 1024.0 / 1024.0, 2);
                var thresholdMB = Math.Round(options.Threshold / 1024.0 / 1024.0, 2);
                var workingSetMB = Math.Round(workingSet / 1024.0 / 1024.0, 2);

                var description = status == HealthStatus.Healthy
                    ? $"Memory usage is healthy. Allocated: {allocatedMB}MB, Working Set: {workingSetMB}MB (Threshold: {thresholdMB}MB)"
                    : $"Memory usage is above threshold. Allocated: {allocatedMB}MB, Working Set: {workingSetMB}MB (Threshold: {thresholdMB}MB)";

                // Log memory status
                if (status == HealthStatus.Unhealthy)
                {
                    _logger.LogWarning("Memory health check failed. Allocated: {AllocatedMB}MB, Threshold: {ThresholdMB}MB", 
                        allocatedMB, thresholdMB);
                }
                else
                {
                    _logger.LogDebug("Memory health check passed. Allocated: {AllocatedMB}MB, Working Set: {WorkingSetMB}MB", 
                        allocatedMB, workingSetMB);
                }

                return Task.FromResult(new HealthCheckResult(status, description, data: data));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Memory health check failed with exception");
                
                var errorData = new Dictionary<string, object>
                {
                    { "Error", ex.Message },
                    { "CheckedAt", DateTime.UtcNow }
                };

                return Task.FromResult(new HealthCheckResult(
                    HealthStatus.Unhealthy,
                    "Memory health check failed due to exception",
                    ex,
                    errorData));
            }
        }
    }

    /// <summary>
    /// Configuration options for memory health check
    /// </summary>
    public class MemoryCheckOptions
    {
        /// <summary>
        /// Memory status description
        /// </summary>
        public string MemoryStatus { get; set; } = "Monitoring application memory usage";

        /// <summary>
        /// Failure threshold in bytes (default: 1GB)
        /// </summary>
        public long Threshold { get; set; } = 1024L * 1024L * 1024L; // 1GB default

        /// <summary>
        /// Whether to force garbage collection before checking memory
        /// </summary>
        public bool ForceGarbageCollection { get; set; } = false;

        /// <summary>
        /// Warning threshold in bytes (default: 512MB)
        /// </summary>
        public long WarningThreshold { get; set; } = 512L * 1024L * 1024L; // 512MB default
    }
}
