using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.Core;

using System.Text.Json;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/sonobrokers/[controller]")]
    [Authorize]
    public class GenerateDescriptionController : ControllerBase
    {
        private readonly ILogger<GenerateDescriptionController> _logger;
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;

        public GenerateDescriptionController(
            ILogger<GenerateDescriptionController> logger,
            IConfiguration configuration,
            HttpClient httpClient)
        {
            _logger = logger;
            _configuration = configuration;
            _httpClient = httpClient;
        }

        /// <summary>
        /// Generate AI-powered property description
        /// </summary>
        /// <param name="request">Property details for description generation</param>
        /// <returns>Generated property description</returns>
        [HttpPost]




        public async Task<ActionResult<ApiResponse<GeneratedDescriptionResponse>>> GenerateDescription([FromBody] PropertyDescriptionRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.PropertyType))
                {
                    return BadRequest(ApiResponse<GeneratedDescriptionResponse>.ErrorResult("Property type is required"));
                }

                var openAiApiKey = _configuration["ExternalServices:OpenAI:ApiKey"];
                if (string.IsNullOrEmpty(openAiApiKey))
                {
                    _logger.LogError("OpenAI API key not configured");
                    return StatusCode(500, ApiResponse<GeneratedDescriptionResponse>.ErrorResult("AI service not configured"));
                }

                var prompt = BuildPrompt(request);
                var openAiResponse = await CallOpenAI(openAiApiKey, prompt);

                if (openAiResponse == null)
                {
                    return StatusCode(500, ApiResponse<GeneratedDescriptionResponse>.ErrorResult("Failed to generate description"));
                }

                var response = new GeneratedDescriptionResponse
                {
                    Description = openAiResponse.Description,
                    Highlights = openAiResponse.Highlights,
                    MarketingPoints = openAiResponse.MarketingPoints,
                    SuggestedTitle = openAiResponse.SuggestedTitle,
                    GeneratedAt = DateTime.UtcNow
                };

                return Ok(ApiResponse<GeneratedDescriptionResponse>.SuccessResult(response, "Description generated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating property description");
                return StatusCode(500, ApiResponse<GeneratedDescriptionResponse>.ErrorResult("Failed to generate description"));
            }
        }

        private string BuildPrompt(PropertyDescriptionRequest request)
        {
            var prompt = $@"
Generate a compelling real estate property description for the following property:

Property Type: {request.PropertyType}
Price: {(request.Price.HasValue ? $"${request.Price:N0}" : "Price upon request")}
Bedrooms: {request.Bedrooms ?? 0}
Bathrooms: {request.Bathrooms ?? 0}
Square Feet: {(request.Sqft.HasValue ? $"{request.Sqft:N0} sqft" : "Not specified")}
Year Built: {(request.YearBuilt?.ToString() ?? "Not specified")}
Lot Size: {(request.LotSize.HasValue ? $"{request.LotSize} {request.LotSizeUnit ?? "sqft"}" : "Not specified")}
Address: {request.Address ?? "Location details available upon request"}
Parking: {(request.ParkingTypes?.Any() == true ? string.Join(", ", request.ParkingTypes) : "Not specified")}
Features: {(request.ExtraFeatures?.Any() == true ? string.Join(", ", request.ExtraFeatures) : "Standard features")}
Current Description: {request.CurrentDescription ?? "None provided"}

Please generate:
1. A compelling property description (200-300 words)
2. Key highlights (3-5 bullet points)
3. Marketing points (3-4 selling points)
4. A suggested property title

Format the response as JSON with the following structure:
{{
    ""description"": ""[main description]"",
    ""highlights"": [""highlight1"", ""highlight2"", ""highlight3""],
    ""marketingPoints"": [""point1"", ""point2"", ""point3""],
    ""suggestedTitle"": ""[catchy title]""
}}

Make the description engaging, professional, and highlight the property's best features. Focus on lifestyle benefits and unique selling points.";

            return prompt;
        }

        private async Task<OpenAIDescriptionResponse?> CallOpenAI(string apiKey, string prompt)
        {
            try
            {
                var requestBody = new
                {
                    model = _configuration["ExternalServices:OpenAI:Model"] ?? "gpt-4",
                    messages = new[]
                    {
                        new { role = "system", content = "You are a professional real estate copywriter specializing in creating compelling property descriptions." },
                        new { role = "user", content = prompt }
                    },
                    max_tokens = int.Parse(_configuration["ExternalServices:OpenAI:MaxTokens"] ?? "2000"),
                    temperature = 0.7
                };

                var jsonContent = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");

                var response = await _httpClient.PostAsync("https://api.openai.com/v1/chat/completions", content);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("OpenAI API request failed. Status: {StatusCode}, Error: {Error}",
                        response.StatusCode, errorContent);
                    return null;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var openAiResponse = JsonSerializer.Deserialize<OpenAIResponse>(responseContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                if (openAiResponse?.Choices?.Any() != true)
                {
                    _logger.LogError("No choices returned from OpenAI API");
                    return null;
                }

                var messageContent = openAiResponse.Choices.First().Message?.Content;
                if (string.IsNullOrEmpty(messageContent))
                {
                    _logger.LogError("Empty message content from OpenAI API");
                    return null;
                }

                // Try to parse the JSON response from OpenAI
                try
                {
                    var descriptionResponse = JsonSerializer.Deserialize<OpenAIDescriptionResponse>(messageContent, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });
                    return descriptionResponse;
                }
                catch (JsonException)
                {
                    // If JSON parsing fails, create a response with the raw content
                    return new OpenAIDescriptionResponse
                    {
                        Description = messageContent,
                        Highlights = new List<string> { "AI-generated content" },
                        MarketingPoints = new List<string> { "Professional description" },
                        SuggestedTitle = "Beautiful Property"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling OpenAI API");
                return null;
            }
        }
    }

    public class PropertyDescriptionRequest
    {
        public string PropertyType { get; set; } = string.Empty;
        public decimal? Price { get; set; }
        public int? Bedrooms { get; set; }
        public int? Bathrooms { get; set; }
        public int? Sqft { get; set; }
        public int? YearBuilt { get; set; }
        public decimal? LotSize { get; set; }
        public string? LotSizeUnit { get; set; }
        public string? Address { get; set; }
        public string[]? ParkingTypes { get; set; }
        public string[]? ExtraFeatures { get; set; }
        public string? CurrentDescription { get; set; }
    }

    public class GeneratedDescriptionResponse
    {
        public string Description { get; set; } = string.Empty;
        public List<string> Highlights { get; set; } = new();
        public List<string> MarketingPoints { get; set; } = new();
        public string SuggestedTitle { get; set; } = string.Empty;
        public DateTime GeneratedAt { get; set; }
    }

    public class OpenAIResponse
    {
        public List<OpenAIChoice>? Choices { get; set; }
    }

    public class OpenAIChoice
    {
        public OpenAIMessage? Message { get; set; }
    }

    public class OpenAIMessage
    {
        public string? Content { get; set; }
    }

    public class OpenAIDescriptionResponse
    {
        public string Description { get; set; } = string.Empty;
        public List<string> Highlights { get; set; } = new();
        public List<string> MarketingPoints { get; set; } = new();
        public string SuggestedTitle { get; set; } = string.Empty;
    }
}
