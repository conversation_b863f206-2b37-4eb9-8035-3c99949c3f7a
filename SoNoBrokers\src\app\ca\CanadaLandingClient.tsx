'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'motion/react'
import {
  Search, Star, ArrowRight, Home, Users, MapPin, Shield, DollarSign,
  Clock, CheckCircle, TrendingUp, Award, Phone, Mail, Globe,
  Calculator, FileText, Camera, Gavel, Building, Zap
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useAppContext } from '@/contexts/AppContext'

export function CanadaLandingClient() {
  // Get authentication state from AppContext
  const { isSignedIn } = useAppContext()
  const [searchQuery, setSearchQuery] = useState('')
  const router = useRouter()

  const handleSearch = () => {
    if (searchQuery.trim()) {
      router.push(`/ca/properties?search=${encodeURIComponent(searchQuery)}`)
    }
  }

  const handleStartBrowsing = () => {
    router.push('/ca/properties')
  }

  const handleListProperty = () => {
    router.push('/ca/list-property')
  }

  return (
    <div className="bg-background text-foreground">
      {/* Hero Section */}
      <section className="relative py-12 overflow-hidden">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <Badge variant="secondary" className="mb-4 text-sm px-4 py-2">
              🇨🇦 Canada's #1 Commission-Free Platform
            </Badge>

            <h1 className="text-4xl md:text-6xl font-bold mb-4 leading-tight">
              Discover Your<br />
              <span className="text-primary">Dream Home</span> in Canada
            </h1>

            <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
              Skip the commission fees and connect directly with property owners across all 13 provinces and territories.
              Save thousands while finding your perfect Canadian home.
            </p>

            <div className="flex items-center max-w-2xl mx-auto mb-6 bg-card rounded-full shadow-md border overflow-hidden">
              <Input
                type="text"
                placeholder="Search Toronto, Vancouver, Montreal, Calgary..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 border-0 focus-visible:ring-0 text-base px-6 h-12 rounded-none bg-transparent"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button
                onClick={handleSearch}
                className="rounded-full bg-primary hover:bg-primary/90 text-primary-foreground px-6 h-10 m-1"
                size="sm"
              >
                <Search className="w-5 h-5" />
              </Button>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-center items-center mb-8">
              <Button
                onClick={handleStartBrowsing}
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 h-11 rounded-full font-semibold"
              >
                Browse Properties
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
              <Button
                onClick={handleListProperty}
                variant="outline"
                className="border-2 border-primary text-primary hover:bg-primary/10 px-8 h-11 rounded-full font-semibold"
              >
                List Your Property
              </Button>
            </div>

            <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-primary" />
                <span>No Commission Fees</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                <span>Verified Listings</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-primary" />
                <span>24/7 Support</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-8 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="space-y-2"
            >
              <div className="flex items-center justify-center mb-2">
                <Home className="w-6 h-6 text-primary mr-2" />
                <div className="text-3xl font-bold text-primary">50k+</div>
              </div>
              <div className="text-muted-foreground font-medium">Active Properties</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="space-y-2"
            >
              <div className="flex items-center justify-center mb-2">
                <Users className="w-6 h-6 text-primary mr-2" />
                <div className="text-3xl font-bold text-primary">25k+</div>
              </div>
              <div className="text-muted-foreground font-medium">Happy Clients</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="space-y-2"
            >
              <div className="flex items-center justify-center mb-2">
                <MapPin className="w-6 h-6 text-primary mr-2" />
                <div className="text-3xl font-bold text-primary">13</div>
              </div>
              <div className="text-muted-foreground font-medium">Provinces & Territories</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="space-y-2"
            >
              <div className="flex items-center justify-center mb-2">
                <DollarSign className="w-6 h-6 text-primary mr-2" />
                <div className="text-3xl font-bold text-primary">$2.5B+</div>
              </div>
              <div className="text-muted-foreground font-medium">Commission Saved</div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Why Choose SoNoBrokers Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-8"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Why Choose SoNoBrokers Canada?
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Canada's most trusted commission-free real estate platform, designed for modern property transactions.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                icon: DollarSign,
                title: "Zero Commission Fees",
                description: "Save thousands on traditional realtor commissions. Keep more money in your pocket for your new home or investment."
              },
              {
                icon: Shield,
                title: "Secure & Verified",
                description: "All listings are verified and secure. Our platform ensures safe transactions with built-in fraud protection."
              },
              {
                icon: Clock,
                title: "24/7 Platform Access",
                description: "Browse, list, and manage properties anytime. Our platform never sleeps, so you never miss an opportunity."
              },
              {
                icon: Users,
                title: "Direct Communication",
                description: "Connect directly with property owners and buyers. No middleman, no markup, just honest transactions."
              },
              {
                icon: TrendingUp,
                title: "Market Analytics",
                description: "Access real-time market data, price trends, and neighborhood insights to make informed decisions."
              },
              {
                icon: Award,
                title: "Professional Services",
                description: "Access vetted lawyers, inspectors, photographers, and other professionals when you need them."
              }
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-4">
                    <feature.icon className="h-10 w-10 text-primary mb-3" />
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Canadian Provinces Coverage */}
      <section className="py-8 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-6"
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-3">
              Coast to Coast Coverage
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              From the Atlantic to the Pacific, we serve all Canadian provinces and territories.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-3">
            {[
              { province: "Ontario", cities: ["Toronto", "Ottawa", "Hamilton"], properties: "15,000+" },
              { province: "British Columbia", cities: ["Vancouver", "Victoria", "Surrey"], properties: "12,000+" },
              { province: "Quebec", cities: ["Montreal", "Quebec City", "Laval"], properties: "8,500+" },
              { province: "Alberta", cities: ["Calgary", "Edmonton", "Red Deer"], properties: "7,200+" },
              { province: "Manitoba", cities: ["Winnipeg", "Brandon"], properties: "2,100+" },
              { province: "Saskatchewan", cities: ["Saskatoon", "Regina"], properties: "1,800+" },
              { province: "Nova Scotia", cities: ["Halifax", "Sydney"], properties: "1,500+" },
              { province: "New Brunswick", cities: ["Moncton", "Saint John"], properties: "900+" }
            ].map((region, index) => (
              <motion.div
                key={region.province}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">{region.province}</CardTitle>
                    <Badge variant="secondary" className="w-fit text-xs">
                      {region.properties}
                    </Badge>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-1">
                      {region.cities.map((city) => (
                        <div key={city} className="text-xs text-muted-foreground">
                          • {city}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Professional Services */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-6"
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-3">
              Professional Services Network
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Access Canada's largest network of verified real estate professionals.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              {
                icon: Gavel,
                title: "Legal Services",
                description: "Licensed Canadian lawyers specializing in real estate law and title transfers.",
                features: ["Contract Review", "Title Search", "Closing Support"]
              },
              {
                icon: Calculator,
                title: "Mortgage Services",
                description: "Connect with top Canadian mortgage brokers for the best rates.",
                features: ["Rate Comparison", "Pre-approval", "Refinancing"]
              },
              {
                icon: Camera,
                title: "Photography",
                description: "Professional real estate photography and virtual tours.",
                features: ["HDR Photography", "Virtual Tours", "Drone Footage"]
              }
            ].map((service, index) => (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <service.icon className="h-8 w-8 text-primary mb-2" />
                    <CardTitle className="text-lg">{service.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3 pt-0">
                    <p className="text-sm text-muted-foreground">
                      {service.description}
                    </p>
                    <div className="space-y-1">
                      {service.features.map((feature) => (
                        <div key={feature} className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-primary" />
                          <span className="text-xs">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-8 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-6"
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-3">
              How SoNoBrokers Works
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Simple, transparent, and commission-free real estate transactions.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-6">
            {[
              {
                step: "01",
                title: "Create Account",
                description: "Sign up for free and verify your identity. Choose buying, selling, or both.",
                icon: Users
              },
              {
                step: "02",
                title: "List or Browse",
                description: "Sellers list properties with photos. Buyers browse verified listings.",
                icon: Home
              },
              {
                step: "03",
                title: "Connect Directly",
                description: "Communicate directly with owners or buyers. No middlemen or commissions.",
                icon: Phone
              }
            ].map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="relative mb-4">
                  <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mx-auto mb-3">
                    <step.icon className="h-6 w-6 text-primary-foreground" />
                  </div>
                  <Badge variant="secondary" className="absolute -top-1 -right-1 text-xs font-bold">
                    {step.step}
                  </Badge>
                </div>
                <h3 className="text-lg font-bold mb-2">{step.title}</h3>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {step.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center bg-primary/5 rounded-xl p-6"
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-3">
              Ready to Save Thousands on Your Next Property?
            </h2>
            <p className="text-base text-muted-foreground mb-4 max-w-2xl mx-auto">
              Join thousands of Canadians who have already saved millions in commission fees.
            </p>

            <div className="flex flex-col sm:flex-row gap-3 justify-center items-center mb-4">
              <Button
                onClick={handleStartBrowsing}
                size="lg"
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-6"
              >
                Start Browsing Properties
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
              <Button
                onClick={handleListProperty}
                variant="outline"
                size="lg"
                className="border-2 border-primary text-primary hover:bg-primary/10 px-6"
              >
                List Your Property Free
              </Button>
            </div>

            <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                <span>No Hidden Fees</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                <span>Verified Listings</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                <span>Professional Support</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
