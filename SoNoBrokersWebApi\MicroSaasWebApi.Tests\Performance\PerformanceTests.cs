using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using MicroSaasWebApi.Tests.Common;
using System.Diagnostics;
using System.Net;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Performance
{
    public class PerformanceTests : IClassFixture<TestWebApplicationFactory<Program>>, IAsyncLifetime
    {
        private readonly TestWebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;
        private readonly ITestOutputHelper _output;

        public PerformanceTests(TestWebApplicationFactory<Program> factory, ITestOutputHelper output)
        {
            _factory = factory;
            _output = output;
            _client = _factory.CreateClient();
        }

        public async Task InitializeAsync()
        {
            await _factory.SeedTestDataAsync();
        }

        public async Task DisposeAsync()
        {
            await _factory.CleanupTestDataAsync();
        }

        [Fact]
        public async Task GetAdvertisers_ResponseTime_ShouldBeFast()
        {
            // Arrange
            var stopwatch = Stopwatch.StartNew();

            // Act
            var response = await _client.GetAsync("/api/sonobrokers/advertisers");

            // Assert
            stopwatch.Stop();
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000, "API should respond within 1 second");
            
            _output.WriteLine($"GetAdvertisers response time: {stopwatch.ElapsedMilliseconds}ms");
        }

        [Fact]
        public async Task GetAdvertisers_ConcurrentRequests_ShouldHandleLoad()
        {
            // Arrange
            const int concurrentRequests = 10;
            var tasks = new List<Task<HttpResponseMessage>>();
            var stopwatch = Stopwatch.StartNew();

            // Act
            for (int i = 0; i < concurrentRequests; i++)
            {
                tasks.Add(_client.GetAsync("/api/sonobrokers/advertisers"));
            }

            var responses = await Task.WhenAll(tasks);
            stopwatch.Stop();

            // Assert
            responses.Should().HaveCount(concurrentRequests);
            responses.Should().OnlyContain(r => r.StatusCode == HttpStatusCode.OK);
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000, "Concurrent requests should complete within 5 seconds");
            
            _output.WriteLine($"Concurrent requests ({concurrentRequests}) completed in: {stopwatch.ElapsedMilliseconds}ms");
            _output.WriteLine($"Average response time: {stopwatch.ElapsedMilliseconds / concurrentRequests}ms");
        }

        [Fact]
        public async Task AIPropertyImport_ResponseTime_ShouldBeReasonable()
        {
            // Arrange
            var request = new { Address = "123 Test Street, Toronto, ON" };
            var stopwatch = Stopwatch.StartNew();

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/ai/property-import", request);

            // Assert
            stopwatch.Stop();
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000, "AI import should complete within 5 seconds");
            
            _output.WriteLine($"AI Property Import response time: {stopwatch.ElapsedMilliseconds}ms");
        }

        [Fact]
        public async Task PropertyValuation_ResponseTime_ShouldBeReasonable()
        {
            // Arrange
            var request = new { Address = "123 Test Street", Country = "CA" };
            var stopwatch = Stopwatch.StartNew();

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/ai/property-valuation", request);

            // Assert
            stopwatch.Stop();
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000, "Property valuation should complete within 5 seconds");
            
            _output.WriteLine($"Property Valuation response time: {stopwatch.ElapsedMilliseconds}ms");
        }

        [Fact]
        public async Task GetEnumValues_ResponseTime_ShouldBeFast()
        {
            // Arrange
            var stopwatch = Stopwatch.StartNew();

            // Act
            var response = await _client.GetAsync("/api/sonobrokers/enums");

            // Assert
            stopwatch.Stop();
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(500, "Enum values should load quickly");
            
            _output.WriteLine($"Get Enum Values response time: {stopwatch.ElapsedMilliseconds}ms");
        }

        [Fact]
        public async Task HealthCheck_ResponseTime_ShouldBeVeryFast()
        {
            // Arrange
            var stopwatch = Stopwatch.StartNew();

            // Act
            var response = await _client.GetAsync("/health");

            // Assert
            stopwatch.Stop();
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(100, "Health check should be very fast");
            
            _output.WriteLine($"Health Check response time: {stopwatch.ElapsedMilliseconds}ms");
        }

        [Fact]
        public async Task MixedWorkload_ShouldMaintainPerformance()
        {
            // Arrange
            var tasks = new List<Task<(string endpoint, long elapsedMs, HttpStatusCode statusCode)>>();
            var endpoints = new[]
            {
                "/api/sonobrokers/advertisers",
                "/api/sonobrokers/enums",
                "/health",
                "/api/sonobrokers/geo"
            };

            // Act
            foreach (var endpoint in endpoints)
            {
                for (int i = 0; i < 3; i++) // 3 requests per endpoint
                {
                    tasks.Add(MeasureEndpointPerformance(endpoint));
                }
            }

            var results = await Task.WhenAll(tasks);

            // Assert
            results.Should().OnlyContain(r => r.statusCode == HttpStatusCode.OK);
            
            var averageResponseTime = results.Average(r => r.elapsedMs);
            averageResponseTime.Should().BeLessThan(2000, "Average response time should be reasonable under mixed load");

            _output.WriteLine($"Mixed workload results:");
            _output.WriteLine($"Total requests: {results.Length}");
            _output.WriteLine($"Average response time: {averageResponseTime:F2}ms");
            _output.WriteLine($"Max response time: {results.Max(r => r.elapsedMs)}ms");
            _output.WriteLine($"Min response time: {results.Min(r => r.elapsedMs)}ms");

            // Log per-endpoint statistics
            var groupedResults = results.GroupBy(r => r.endpoint);
            foreach (var group in groupedResults)
            {
                var avg = group.Average(r => r.elapsedMs);
                var max = group.Max(r => r.elapsedMs);
                _output.WriteLine($"{group.Key}: avg={avg:F2}ms, max={max}ms");
            }
        }

        [Fact]
        public async Task LargePayload_ShouldHandleEfficiently()
        {
            // Arrange
            var largeRequest = new
            {
                Name = "Test User",
                Email = "<EMAIL>",
                PropertyAddress = new string('A', 1000), // Large address string
                Requirements = new string('B', 2000), // Large requirements string
                Country = "CA"
            };

            var stopwatch = Stopwatch.StartNew();

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/contact-concierge", largeRequest);

            // Assert
            stopwatch.Stop();
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(3000, "Large payload should be handled efficiently");
            
            _output.WriteLine($"Large payload response time: {stopwatch.ElapsedMilliseconds}ms");
        }

        [Fact]
        public async Task MemoryUsage_ShouldNotLeak()
        {
            // Arrange
            var initialMemory = GC.GetTotalMemory(true);

            // Act - Perform multiple operations
            for (int i = 0; i < 50; i++)
            {
                var response = await _client.GetAsync("/api/sonobrokers/advertisers");
                response.StatusCode.Should().Be(HttpStatusCode.OK);
                
                // Dispose response to free memory
                response.Dispose();
            }

            // Force garbage collection
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var finalMemory = GC.GetTotalMemory(false);

            // Assert
            var memoryIncrease = finalMemory - initialMemory;
            var memoryIncreaseKB = memoryIncrease / 1024.0;
            
            _output.WriteLine($"Initial memory: {initialMemory / 1024.0:F2} KB");
            _output.WriteLine($"Final memory: {finalMemory / 1024.0:F2} KB");
            _output.WriteLine($"Memory increase: {memoryIncreaseKB:F2} KB");

            // Memory increase should be reasonable (less than 10MB for 50 requests)
            memoryIncreaseKB.Should().BeLessThan(10240, "Memory usage should not increase significantly");
        }

        [Theory]
        [InlineData(1)]
        [InlineData(5)]
        [InlineData(10)]
        [InlineData(20)]
        public async Task GetAdvertisers_WithPagination_ShouldScaleWell(int pageSize)
        {
            // Arrange
            var stopwatch = Stopwatch.StartNew();

            // Act
            var response = await _client.GetAsync($"/api/sonobrokers/advertisers?page=1&limit={pageSize}");

            // Assert
            stopwatch.Stop();
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            // Response time should not increase significantly with page size
            var expectedMaxTime = Math.Max(1000, pageSize * 50); // Base 1s + 50ms per item
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(expectedMaxTime);
            
            _output.WriteLine($"Page size {pageSize}: {stopwatch.ElapsedMilliseconds}ms");
        }

        private async Task<(string endpoint, long elapsedMs, HttpStatusCode statusCode)> MeasureEndpointPerformance(string endpoint)
        {
            var stopwatch = Stopwatch.StartNew();
            var response = await _client.GetAsync(endpoint);
            stopwatch.Stop();
            
            return (endpoint, stopwatch.ElapsedMilliseconds, response.StatusCode);
        }
    }

    /// <summary>
    /// Performance testing utilities
    /// </summary>
    public static class PerformanceTestHelpers
    {
        public static async Task<PerformanceResult> MeasureAsync(Func<Task> action, int iterations = 1)
        {
            var results = new List<long>();
            var stopwatch = new Stopwatch();

            for (int i = 0; i < iterations; i++)
            {
                stopwatch.Restart();
                await action();
                stopwatch.Stop();
                results.Add(stopwatch.ElapsedMilliseconds);
            }

            return new PerformanceResult
            {
                Iterations = iterations,
                TotalTime = results.Sum(),
                AverageTime = results.Average(),
                MinTime = results.Min(),
                MaxTime = results.Max(),
                MedianTime = results.OrderBy(x => x).Skip(results.Count / 2).First()
            };
        }

        public static async Task<T> MeasureAsync<T>(Func<Task<T>> action, out long elapsedMs)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = await action();
            stopwatch.Stop();
            elapsedMs = stopwatch.ElapsedMilliseconds;
            return result;
        }
    }

    public class PerformanceResult
    {
        public int Iterations { get; set; }
        public long TotalTime { get; set; }
        public double AverageTime { get; set; }
        public long MinTime { get; set; }
        public long MaxTime { get; set; }
        public long MedianTime { get; set; }

        public override string ToString()
        {
            return $"Iterations: {Iterations}, Avg: {AverageTime:F2}ms, Min: {MinTime}ms, Max: {MaxTime}ms, Median: {MedianTime}ms";
        }
    }
}
