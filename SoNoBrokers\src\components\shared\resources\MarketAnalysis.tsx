'use client'

import React, { useState, useEffect } from 'react'
import { HeroSection } from '@/components/country-specific/ca/HeroSection'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  TrendingUp,
  TrendingDown,
  Home,
  DollarSign,
  Calendar,
  MapPin,
  BarChart3,
  PieChart,
  LineChart
} from 'lucide-react'

interface MarketAnalysisProps {
  userType: 'buyer' | 'seller'
  isSignedIn: boolean
  country: string
}

interface MarketData {
  region: string
  averagePrice: number
  priceChange: number
  daysOnMarket: number
  salesVolume: number
  inventory: number
  pricePerSqFt: number
}

const mockMarketData: Record<string, MarketData[]> = {
  CA: [
    {
      region: 'Toronto, ON',
      averagePrice: 1250000,
      priceChange: -2.3,
      daysOnMarket: 18,
      salesVolume: 1247,
      inventory: 3456,
      pricePerSqFt: 1150
    },
    {
      region: 'Vancouver, BC',
      averagePrice: 1450000,
      priceChange: 1.2,
      daysOnMarket: 22,
      salesVolume: 892,
      inventory: 2134,
      pricePerSqFt: 1280
    },
    {
      region: 'Calgary, AB',
      averagePrice: 485000,
      priceChange: 3.8,
      daysOnMarket: 28,
      salesVolume: 567,
      inventory: 1789,
      pricePerSqFt: 420
    }
  ],
  US: [
    {
      region: 'New York, NY',
      averagePrice: 950000,
      priceChange: -1.8,
      daysOnMarket: 35,
      salesVolume: 2134,
      inventory: 4567,
      pricePerSqFt: 1450
    },
    {
      region: 'Los Angeles, CA',
      averagePrice: 875000,
      priceChange: 0.5,
      daysOnMarket: 28,
      salesVolume: 1789,
      inventory: 3245,
      pricePerSqFt: 1200
    },
    {
      region: 'Chicago, IL',
      averagePrice: 425000,
      priceChange: 2.1,
      daysOnMarket: 32,
      salesVolume: 1456,
      inventory: 2890,
      pricePerSqFt: 380
    }
  ]
}

export function MarketAnalysis({ userType, isSignedIn, country }: MarketAnalysisProps) {
  const [selectedRegion, setSelectedRegion] = useState<string>('')
  const [marketData, setMarketData] = useState<MarketData[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setMarketData(mockMarketData[country] || [])
      setLoading(false)
    }, 1000)
  }, [country])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: country === 'CA' ? 'CAD' : 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? '+' : ''
    return `${sign}${value.toFixed(1)}%`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading market analysis...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <HeroSection userType={userType} />

      {/* Market Analysis Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-4">Real Estate Market Analysis</h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Get comprehensive market insights and trends for {country === 'CA' ? 'Canada' : 'United States'}.
              Make informed decisions with real-time data and analytics.
            </p>
          </div>

          {/* Market Overview Cards */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg. Home Price</CardTitle>
                <Home className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(marketData.reduce((sum, data) => sum + data.averagePrice, 0) / marketData.length)}
                </div>
                <p className="text-xs text-muted-foreground">
                  National average
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Price Change</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {formatPercentage(marketData.reduce((sum, data) => sum + data.priceChange, 0) / marketData.length)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Year over year
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Days on Market</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.round(marketData.reduce((sum, data) => sum + data.daysOnMarket, 0) / marketData.length)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Average days
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Listings</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {marketData.reduce((sum, data) => sum + data.inventory, 0).toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground">
                  Total inventory
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Regional Data */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MapPin className="w-5 h-5" />
                <span>Regional Market Data</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2">Region</th>
                      <th className="text-right py-2">Avg. Price</th>
                      <th className="text-right py-2">Price Change</th>
                      <th className="text-right py-2">Days on Market</th>
                      <th className="text-right py-2">Sales Volume</th>
                      <th className="text-right py-2">Price/Sq Ft</th>
                    </tr>
                  </thead>
                  <tbody>
                    {marketData.map((data, index) => (
                      <tr key={index} className="border-b hover:bg-muted/50">
                        <td className="py-3 font-medium">{data.region}</td>
                        <td className="text-right py-3">{formatCurrency(data.averagePrice)}</td>
                        <td className={`text-right py-3 ${data.priceChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          <div className="flex items-center justify-end space-x-1">
                            {data.priceChange >= 0 ?
                              <TrendingUp className="w-4 h-4" /> :
                              <TrendingDown className="w-4 h-4" />
                            }
                            <span>{formatPercentage(data.priceChange)}</span>
                          </div>
                        </td>
                        <td className="text-right py-3">{data.daysOnMarket} days</td>
                        <td className="text-right py-3">{data.salesVolume.toLocaleString()}</td>
                        <td className="text-right py-3">{formatCurrency(data.pricePerSqFt)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Market Insights */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Market Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">Inventory levels are stabilizing</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span className="text-sm">Interest rates affecting buyer demand</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm">New construction permits increasing</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>
                  {userType === 'seller' ? 'Seller Insights' : 'Buyer Insights'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {userType === 'seller' ? (
                    <>
                      <p className="text-sm text-muted-foreground">
                        • Price competitively based on recent comparable sales
                      </p>
                      <p className="text-sm text-muted-foreground">
                        • Consider staging and professional photography
                      </p>
                      <p className="text-sm text-muted-foreground">
                        • Spring market typically shows increased activity
                      </p>
                    </>
                  ) : (
                    <>
                      <p className="text-sm text-muted-foreground">
                        • Get pre-approved to strengthen your offers
                      </p>
                      <p className="text-sm text-muted-foreground">
                        • Consider properties that have been on market longer
                      </p>
                      <p className="text-sm text-muted-foreground">
                        • Factor in potential rate changes in your timeline
                      </p>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
