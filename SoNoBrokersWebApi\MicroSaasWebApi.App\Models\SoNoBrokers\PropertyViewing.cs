using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    [Table("PropertyViewing", Schema = "snb")]
    public class PropertyViewing
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string PropertyId { get; set; } = string.Empty;

        [Required]
        public string BuyerId { get; set; } = string.Empty;

        public string Status { get; set; } = "requested";

        [Column(TypeName = "jsonb")]
        public string? ProposedTimes { get; set; }

        public DateTime? ConfirmedTime { get; set; }

        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        [ForeignKey("PropertyId")]
        public virtual Property Property { get; set; } = null!;

        [ForeignKey("BuyerId")]
        public virtual User Buyer { get; set; } = null!;

        // Helper methods for JSON fields
        public T? GetProposedTimesAs<T>() where T : class
        {
            if (string.IsNullOrEmpty(ProposedTimes)) return null;
            try
            {
                return JsonSerializer.Deserialize<T>(ProposedTimes);
            }
            catch
            {
                return null;
            }
        }

        public void SetProposedTimes<T>(T proposedTimes) where T : class
        {
            ProposedTimes = proposedTimes != null ? JsonSerializer.Serialize(proposedTimes) : null;
        }
    }
}
