using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MicroSaasWebApi.Models.Core
{
    /// <summary>
    /// Document model for file management
    /// </summary>
    [Table("documents", Schema = "core")]
    public class Document
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        [MaxLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [MaxLength(255)]
        public string OriginalFileName { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string ContentType { get; set; } = string.Empty;

        public long Size { get; set; }

        [Required]
        [MaxLength(500)]
        public string FilePath { get; set; } = string.Empty;

        [MaxLength(255)]
        public string? SharePointId { get; set; }

        [MaxLength(255)]
        public string? SiteId { get; set; }

        [MaxLength(255)]
        public string? LibraryName { get; set; }

        [MaxLength(500)]
        public string? Description { get; set; }

        [MaxLength(100)]
        public string? Category { get; set; }

        [MaxLength(500)]
        public string? Tags { get; set; }

        public bool IsPublic { get; set; } = false;
        public bool IsActive { get; set; } = true;

        [MaxLength(64)]
        public string? FileHash { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }
        public DateTime? UploadedAt { get; set; }

        public Guid UploadedBy { get; set; }
        public Guid? UpdatedBy { get; set; }

        // Navigation properties
        public virtual User Uploader { get; set; } = null!;
        public virtual User? Updater { get; set; }
    }
}
