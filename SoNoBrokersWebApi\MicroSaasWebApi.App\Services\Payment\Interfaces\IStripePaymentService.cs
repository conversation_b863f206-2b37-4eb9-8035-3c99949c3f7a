using Stripe;
using MicroSaasWebApi.Models.Payment;

namespace MicroSaasWebApi.Services.Payment.Interfaces
{
    /// <summary>
    /// Interface for Stripe payment service
    /// Follows Interface Segregation Principle
    /// </summary>
    public interface IStripePaymentService
    {
        // Customer Management
        Task<Customer?> CreateCustomerAsync(string email, string name, string? description = null);
        Task<Customer?> GetCustomerAsync(string customerId);
        Task<Customer?> UpdateCustomerAsync(string customerId, string? email = null, string? name = null, string? description = null);
        Task<bool> DeleteCustomerAsync(string customerId);
        Task<IEnumerable<Customer>> ListCustomersAsync(int limit = 10, string? startingAfter = null);

        // Payment Methods
        Task<PaymentMethod?> CreatePaymentMethodAsync(string customerId, string type = "card", Dictionary<string, object>? cardDetails = null);
        Task<PaymentMethod?> GetPaymentMethodAsync(string paymentMethodId);
        Task<PaymentMethod?> AttachPaymentMethodAsync(string paymentMethodId, string customerId);
        Task<PaymentMethod?> DetachPaymentMethodAsync(string paymentMethodId);
        Task<IEnumerable<PaymentMethod>> ListCustomerPaymentMethodsAsync(string customerId, string type = "card");

        // Payment Intents
        Task<PaymentIntent?> CreatePaymentIntentAsync(long amount, string currency, string customerId, string? paymentMethodId = null, bool confirmImmediately = false);
        Task<PaymentIntent?> GetPaymentIntentAsync(string paymentIntentId);
        Task<PaymentIntent?> UpdatePaymentIntentAsync(string paymentIntentId, long? amount = null, string? currency = null);
        Task<PaymentIntent?> ConfirmPaymentIntentAsync(string paymentIntentId, string? paymentMethodId = null);
        Task<PaymentIntent?> CancelPaymentIntentAsync(string paymentIntentId);

        // Subscriptions
        Task<Subscription?> CreateSubscriptionAsync(string customerId, string priceId, int? trialPeriodDays = null);
        Task<Subscription?> GetSubscriptionAsync(string subscriptionId);
        Task<Subscription?> UpdateSubscriptionAsync(string subscriptionId, string? priceId = null, int? quantity = null);
        Task<Subscription?> CancelSubscriptionAsync(string subscriptionId, bool cancelImmediately = false);
        Task<IEnumerable<Subscription>> ListCustomerSubscriptionsAsync(string customerId);

        // Products and Prices
        Task<Product?> CreateProductAsync(string name, string? description = null, Dictionary<string, string>? metadata = null);
        Task<Product?> GetProductAsync(string productId);
        Task<Product?> UpdateProductAsync(string productId, string? name = null, string? description = null);
        Task<IEnumerable<Product>> ListProductsAsync(int limit = 10);

        Task<Price?> CreatePriceAsync(string productId, long unitAmount, string currency, string interval = "month");
        Task<Price?> GetPriceAsync(string priceId);
        Task<IEnumerable<Price>> ListPricesAsync(string? productId = null, int limit = 10);

        // Invoices
        Task<Stripe.Invoice?> CreateInvoiceAsync(string customerId, string? subscriptionId = null);
        Task<Stripe.Invoice?> GetInvoiceAsync(string invoiceId);
        Task<Stripe.Invoice?> FinalizeInvoiceAsync(string invoiceId);
        Task<Stripe.Invoice?> PayInvoiceAsync(string invoiceId);
        Task<IEnumerable<Stripe.Invoice>> ListCustomerInvoicesAsync(string customerId, int limit = 10);

        // Webhooks
        Task<Event?> ConstructWebhookEventAsync(string payload, string signature, string endpointSecret);
        Task<bool> HandleWebhookEventAsync(Event stripeEvent);

        // Charges
        Task<Charge?> CreateChargeAsync(long amount, string currency, string source, string? description = null);
        Task<Charge?> GetChargeAsync(string chargeId);
        Task<Refund?> RefundChargeAsync(string chargeId, long? amount = null, string? reason = null);

        // Setup Intents (for saving payment methods)
        Task<SetupIntent?> CreateSetupIntentAsync(string customerId, string? paymentMethodId = null);
        Task<SetupIntent?> GetSetupIntentAsync(string setupIntentId);
        Task<SetupIntent?> ConfirmSetupIntentAsync(string setupIntentId, string? paymentMethodId = null);

        // Coupons and Discounts
        Task<Coupon?> CreateCouponAsync(string id, int? percentOff = null, long? amountOff = null, string? currency = null, int? durationInMonths = null);
        Task<Coupon?> GetCouponAsync(string couponId);
        Task<bool> DeleteCouponAsync(string couponId);

        // Balance and Transfers
        Task<Balance?> GetBalanceAsync();
        Task<IEnumerable<BalanceTransaction>> ListBalanceTransactionsAsync(int limit = 10);
    }
}
