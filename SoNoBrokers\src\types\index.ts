export * from "./config";

// Country enum (previously from Prisma)
export enum Country {
  CA = 'CA',
  US = 'US',
  UAE = 'UAE'
}

// User role enum (previously from Prisma)
export enum UserRole {
  USER = 'USER',
  ADMIN = 'ADMIN',
  SELLER = 'SELLER',
  BUYER = 'BUYER'
}

// User type enum (previously from Prisma)
export enum SnbUserType {
  USER = 'USER',
  ADMIN = 'ADMIN',
  SELLER = 'SELLER',
  BUYER = 'BUYER'
}

// User interface (previously from Prisma)
export interface User {
  id: string
  clerkId: string
  email: string
  fullName: string
  role: UserRole
  status: SnbUserType
  createdAt: Date
  updatedAt: Date
}
