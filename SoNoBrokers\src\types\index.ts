export * from "./config";

// Country enum (matching database values)
export enum Country {
  CA = 'CA',
  US = 'US',
  UAE = 'UAE'
}

// User role enum (matching database values)
export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER',
  PRODUCT = 'PRODUCT',
  OPERATOR = 'OPERATOR',
  SERVICE_PROVIDER = 'service_provider'
}

// User type enum (matching database values - lowercase)
export enum SnbUserType {
  buyer = 'buyer',
  seller = 'seller'
}

// User interface (previously from Prisma)
export interface User {
  id: string
  clerkId: string
  email: string
  fullName: string
  role: UserRole
  status: SnbUserType
  createdAt: Date
  updatedAt: Date
}
