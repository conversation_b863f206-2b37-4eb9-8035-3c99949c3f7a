export * from "./config";

// Country enum (previously from Prisma)
export enum Country {
  CA = 'CA',
  US = 'US',
  UAE = 'UAE'
}

// User role enum (previously from Prisma)
export enum UserRole {
  User = 'USER',
  Admin = 'ADMIN',
  Seller = 'SELLER',
  Buyer = 'BUYER'
}

// User type enum (previously from Prisma)
export enum SnbUserType {
  User = 'USER',
  Admin = 'ADMIN',
  Seller = 'SELLER',
  Buyer = 'BUYER'
}

// User interface (previously from Prisma)
export interface User {
  id: string
  clerkId: string
  email: string
  fullName: string
  role: UserRole
  status: SnbUserType
  createdAt: Date
  updatedAt: Date
}
