import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
// TODO: Migrate to .NET Web API - temporarily disabled
// import prisma from '@/lib/prisma';

export async function POST(request: Request) {
  // TODO: Migrate to .NET Web API
  return NextResponse.json(
    { error: 'API route temporarily disabled - migrating to .NET Web API' },
    { status: 503 }
  )
}

/* DISABLED - MIGRATE TO .NET WEB API
export async function POST_DISABLED(request: Request) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      title,
      description,
      price,
      propertyType,
      adType,
      bedrooms,
      bathrooms,
      sqft,
      storeys,
      yearBuilt,
      lotSize,
      lotSizeUnit,
      address,
      parkingTypes,
      extraFeatures,
      mlsNumber,
      virtualTour,
      openHouse
    } = body;

    // Find the user in our database
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return NextResponse.json({ message: 'User not found' }, { status: 404 });
    }

    // Create the property listing
    const property = await prisma.property.create({
      data: {
        sellerId: user.id,
        title,
        description,
        price: parseFloat(price),
        propertyType,
        bedrooms: bedrooms ? parseInt(bedrooms) : null,
        bathrooms: bathrooms ? parseFloat(bathrooms) : null,
        squareFeet: sqft ? parseInt(sqft) : null,
        yearBuilt: yearBuilt ? parseInt(yearBuilt) : null,
        lotSize: lotSize ? parseFloat(lotSize) : null,
        address: {
          street: address.street,
          city: address.city,
          province: address.province,
          postalCode: address.postalCode,
          country: address.country,
          fullAddress: `${address.street}, ${address.city}, ${address.province} ${address.postalCode}, ${address.country}`
        },
        features: extraFeatures || [],
        virtualTourUrl: virtualTour,
        status: 'pending' // Buyer listings start as pending for review
      }
    });

    // Create buyer listing record
    const buyerListing = await prisma.buyerListings.create({
      data: {
        buyerId: user.id,
        propertyId: property.id
      }
    });

    return NextResponse.json({
      message: 'Property listing created successfully',
      property,
      buyerListing
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating buyer listing:', error);
    return NextResponse.json(
      { message: 'Error creating listing', error: error.message },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Get all buyer listings for the current user
    const buyerListings = await prisma.buyerListings.findMany({
      where: {
        buyerId: userId
      },
      include: {
        property: {
          include: {
            images: true
          }
        }
      },
      orderBy: {
        savedAt: 'desc'
      }
    });

    return NextResponse.json(buyerListings);

  } catch (error) {
    console.error('Error fetching buyer listings:', error);
    return NextResponse.json(
      { message: 'Error fetching listings', error: error.message },
      { status: 500 }
    );
  }
}
*/
