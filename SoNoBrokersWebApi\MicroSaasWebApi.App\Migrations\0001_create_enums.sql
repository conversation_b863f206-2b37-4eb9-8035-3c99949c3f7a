-- Migration: 0001_create_enums.sql
-- Description: Create all database enums for SoNoBrokers
-- Date: 2024-12-25
-- Author: Migration System
-- Dependencies: None

-- =====================================================
-- ENUMS CREATION
-- =====================================================

-- User related enums
DO $$ BEGIN
    CREATE TYPE "UserRole" AS ENUM (
      'ADMIN',
      'USER',
      'PRODUCT',
      'OPERATOR',
      'service_provider'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "UserStatus" AS ENUM (
      'ACTIVE',
      'INACTIVE',
      'SUSPENDED',
      'PENDING'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Property related enums
DO $$ BEGIN
    CREATE TYPE "PropertyType" AS ENUM (
      'Detached House',
      'Semi-Detached House',
      'Townhouse',
      'Condominium',
      'Apartment',
      'Duplex',
      'Triplex',
      'Fourplex',
      'Mobile Home',
      'Vacant Land',
      'Commercial',
      'Industrial',
      'Investment',
      'Other'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "PropertyStatus" AS ENUM (
      'ACTIVE',
      'SOLD',
      'PENDING',
      'WITHDRAWN',
      'EXPIRED',
      'DRAFT'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "ListingType" AS ENUM (
      'FOR_SALE',
      'FOR_RENT',
      'SOLD',
      'RENTED'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Subscription related enums
DO $$ BEGIN
    CREATE TYPE "SubscriptionStatus" AS ENUM (
      'ACTIVE',
      'INACTIVE',
      'CANCELLED',
      'EXPIRED',
      'PENDING'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "SubscriptionType" AS ENUM (
      'BASIC',
      'PREMIUM',
      'ENTERPRISE',
      'FREE_TRIAL'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Service related enums
DO $$ BEGIN
    CREATE TYPE "ServiceType" AS ENUM (
      'Photography',
      'HomeInspection',
      'LegalServices',
      'MortgageBroker',
      'RealEstateAgent',
      'Cleaning',
      'Staging',
      'MovingServices',
      'Insurance',
      'Appraisal',
      'Other'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Contact Sharing related enums
DO $$ BEGIN
    CREATE TYPE "ContactShareType" AS ENUM (
      'ContactRequest',
      'PropertyOffer',
      'ScheduleVisit',
      'OfferWithVisit'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "ContactShareStatus" AS ENUM (
      'Pending',
      'Approved',
      'Rejected',
      'Expired',
      'Cancelled'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Property Scheduling related enums
DO $$ BEGIN
    CREATE TYPE "VisitStatus" AS ENUM (
      'Pending',
      'Confirmed',
      'Rescheduled',
      'Cancelled',
      'Completed',
      'NoShow',
      'Expired'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "VisitType" AS ENUM (
      'InPerson',
      'Virtual',
      'SelfGuided'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "VerificationMethod" AS ENUM (
      'QrCode',
      'SellerPresent',
      'KeyBox',
      'PropertyManager',
      'Other'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- =====================================================
-- EXTENSIONS
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ Migration 0001_create_enums.sql completed successfully';
    RAISE NOTICE 'Created enums:';
    RAISE NOTICE '- User: UserRole, UserStatus';
    RAISE NOTICE '- Property: PropertyType, PropertyStatus, ListingType';
    RAISE NOTICE '- Subscription: SubscriptionStatus, SubscriptionType';
    RAISE NOTICE '- Service: ServiceType';
    RAISE NOTICE '- Contact Sharing: ContactShareType, ContactShareStatus';
    RAISE NOTICE '- Property Scheduling: VisitStatus, VisitType, VerificationMethod';
    RAISE NOTICE '- Extensions: uuid-ossp, postgis';
    RAISE NOTICE '';
END $$;
