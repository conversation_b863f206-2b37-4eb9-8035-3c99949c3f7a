# Quick fix for malformed SaveChanges comments

$folders = @("Controllers\SoNoBrokers", "Services\SoNoBrokers")

foreach ($folder in $folders) {
    if (Test-Path $folder) {
        Get-ChildItem -Path $folder -Filter "*.cs" -Recurse | ForEach-Object {
            $content = Get-Content $_.FullName -Raw
            $originalContent = $content
            
            # Fix the malformed patterns
            $content = $content -replace 'await // SaveChanges converted to individual Execute callsAsync\(\);', '// TODO: Convert to Dapper ExecuteAsync'
            $content = $content -replace '// SaveChanges converted to individual Execute calls', '// TODO: Convert to Dapper Execute'
            
            if ($content -ne $originalContent) {
                Set-Content $_.FullName $content -NoNewline
                Write-Host "Fixed: $($_.Name)" -ForegroundColor Green
            }
        }
    }
}

Write-Host "Quick fix completed!" -ForegroundColor Cyan
