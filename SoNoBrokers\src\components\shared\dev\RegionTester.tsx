'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

/**
 * RegionTester Component
 *
 * Development utility for testing region detection.
 * Now as a standalone page component.
 */
export function RegionTester() {
  const router = useRouter()
  const [currentCountry, setCurrentCountry] = useState<string | null>(null)
  const [testCountry, setTestCountry] = useState<string | null>(null)

  useEffect(() => {
    setCurrentCountry(localStorage.getItem('userCountry'))
    setTestCountry(localStorage.getItem('testCountry'))
  }, [])

  const setTestRegion = (country: string) => {
    localStorage.setItem('testCountry', country)

    // Set countryValid based on whether it's a supported region
    const SUPPORTED_REGIONS = ['US', 'CA', 'UAE']
    if (SUPPORTED_REGIONS.includes(country.toUpperCase())) {
      localStorage.setItem('countryValid', 'true')
    } else {
      localStorage.setItem('countryValid', 'false')
    }

    // Redirect to home page to see the effect
    router.push('/')
  }

  const clearTest = () => {
    localStorage.removeItem('testCountry')
    // Reset to localhost default (Canada)
    localStorage.setItem('userCountry', 'CA')
    localStorage.setItem('countryValid', 'true')
    // Redirect to home page to see the effect
    router.push('/')
  }

  const clearAll = () => {
    localStorage.removeItem('testCountry')
    localStorage.removeItem('userCountry')
    localStorage.removeItem('countryValid')
    // Redirect to home page to see the effect
    router.push('/')
  }

  const debugData = () => {
    const data = {
      userCountry: localStorage.getItem('userCountry'),
      countryValid: localStorage.getItem('countryValid'),
      testCountry: localStorage.getItem('testCountry')
    }
    console.log('🔍 Current Region Data:', data)
    alert(`Region Data:\n${JSON.stringify(data, null, 2)}`)
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">🧪 Region Tester</h1>
        <p className="text-muted-foreground">
          Development utility for testing region detection and routing behavior.
        </p>
      </div>

      <div className="space-y-6">
        {/* Current Status */}
        <Card>
          <CardHeader>
            <CardTitle>Current Status</CardTitle>
            <CardDescription>
              Current region detection values
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Current Country:</label>
                <div className="text-lg font-mono bg-muted p-2 rounded">
                  {currentCountry || 'None'}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium">Test Override:</label>
                <div className="text-lg font-mono bg-muted p-2 rounded">
                  {testCountry || 'None'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Supported Regions */}
        <Card>
          <CardHeader>
            <CardTitle>Test Supported Regions</CardTitle>
            <CardDescription>
              Test with supported countries (CA, US, UAE). These should redirect to country-specific routes.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4">
              <Button
                onClick={() => setTestRegion('CA')}
                className="h-16 text-lg"
                variant="outline"
              >
                🇨🇦 Test Canada (CA)
              </Button>
              <Button
                onClick={() => setTestRegion('US')}
                className="h-16 text-lg"
                variant="outline"
              >
                🇺🇸 Test United States (US)
              </Button>
              <Button
                onClick={() => setTestRegion('UAE')}
                className="h-16 text-lg"
                variant="outline"
              >
                🇦🇪 Test UAE (UAE)
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Unsupported Regions */}
        <Card>
          <CardHeader>
            <CardTitle>Test Unsupported Regions</CardTitle>
            <CardDescription>
              Test with unsupported countries. These should redirect to the unsupported region page.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <Button
                onClick={() => setTestRegion('FR')}
                className="h-16 text-lg"
                variant="outline"
              >
                🇫🇷 Test France (FR)
              </Button>
              <Button
                onClick={() => setTestRegion('GB')}
                className="h-16 text-lg"
                variant="outline"
              >
                🇬🇧 Test United Kingdom (GB)
              </Button>
              <Button
                onClick={() => setTestRegion('DE')}
                className="h-16 text-lg"
                variant="outline"
              >
                🇩🇪 Test Germany (DE)
              </Button>
              <Button
                onClick={() => setTestRegion('JP')}
                className="h-16 text-lg"
                variant="outline"
              >
                🇯🇵 Test Japan (JP)
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Clear Options */}
        <Card>
          <CardHeader>
            <CardTitle>Reset Options</CardTitle>
            <CardDescription>
              Clear test data and return to normal behavior
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4">
              <Button
                onClick={clearTest}
                variant="secondary"
                className="h-16 text-lg"
              >
                🧹 Clear Test Override
              </Button>
              <Button
                onClick={clearAll}
                variant="destructive"
                className="h-16 text-lg"
              >
                🗑️ Clear All Data
              </Button>
              <Button
                onClick={debugData}
                variant="outline"
                className="h-16 text-lg"
              >
                🔍 Debug Data
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Information */}
        <Card>
          <CardHeader>
            <CardTitle>Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm text-muted-foreground">
            <div>• <strong>Localhost behavior:</strong> Automatically defaults to Canada (CA)</div>
            <div>• <strong>Supported regions:</strong> US (United States), CA (Canada)</div>
            <div>• <strong>Test priority:</strong> Test overrides take precedence over all other detection</div>
            <div>• <strong>After testing:</strong> You'll be redirected to the home page to see the effect</div>
            <div>• <strong>Console:</strong> Check browser console for region detection messages</div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
