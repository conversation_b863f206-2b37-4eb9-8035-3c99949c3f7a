'use client'

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import Image from 'next/image'

interface ShowcaseCardProps {
  title: string
  description: string
  image?: string
  badge?: string
  badgeVariant?: 'default' | 'secondary' | 'destructive' | 'outline'
  action?: {
    label: string
    onClick: () => void
  }
  className?: string
  children?: React.ReactNode
}

export function ShowcaseCard({
  title,
  description,
  image,
  badge,
  badgeVariant = 'default',
  action,
  className,
  children
}: ShowcaseCardProps) {
  return (
    <Card className={cn("overflow-hidden", className)}>
      {image && (
        <div className="relative h-48 w-full">
          <Image
            src={image}
            alt={title}
            fill
            className="object-cover"
          />
          {badge && (
            <div className="absolute top-2 right-2">
              <Badge variant={badgeVariant}>{badge}</Badge>
            </div>
          )}
        </div>
      )}
      
      <CardHeader>
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg font-semibold line-clamp-2">
            {title}
          </CardTitle>
          {badge && !image && (
            <Badge variant={badgeVariant}>{badge}</Badge>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <p className="text-muted-foreground text-sm leading-relaxed">
          {description}
        </p>
        
        {children}
        
        {action && (
          <Button onClick={action.onClick} className="w-full">
            {action.label}
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
