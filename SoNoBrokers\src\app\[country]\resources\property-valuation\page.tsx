import { redirect } from 'next/navigation'
import { HeroSection } from '@/components/country-specific/ca/HeroSection'
import { PropertyValuationClient } from '@/components/shared/resources/PropertyValuationClient'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function PropertyValuationPage({ params, searchParams }: PageProps) {
  // Resources pages don't require authentication
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us', 'uae']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/resources/property-valuation')
  }

  const userType = resolvedSearchParams.userType || 'seller'

  return (
    <div className="min-h-screen bg-background">
      <PropertyValuationClient
        country={country.toUpperCase()}
        userType={userType}
      />

      {/* AI Property Valuation Tool Hero Section - Moved to bottom */}
      <HeroSection userType={userType} />
    </div>
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'seller'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `AI Property Valuation Tool for ${country} | SoNoBrokers`,
    description: `Get instant property valuations using AI and real-time market data in ${country}. Free property value estimates with detailed reports.`,
    keywords: `property valuation, home value, AI valuation, ${country}, property appraisal`,
  }
}
