'use client'

import React, { useState } from 'react'
import { HeroSection } from '@/components/country-specific/ca/HeroSection'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Calculator, Home, DollarSign, TrendingUp, MapPin } from 'lucide-react'

interface PricingCalculatorProps {
  userType: 'buyer' | 'seller'
  isSignedIn: boolean
  country: string
}

export function PricingCalculator({ userType, isSignedIn, country }: PricingCalculatorProps) {
  const [propertyType, setPropertyType] = useState('')
  const [bedrooms, setBedrooms] = useState('')
  const [bathrooms, setBathrooms] = useState('')
  const [squareFootage, setSquareFootage] = useState('')
  const [location, setLocation] = useState('')
  const [condition, setCondition] = useState('')
  const [yearBuilt, setYearBuilt] = useState('')
  const [lotSize, setLotSize] = useState([5000])
  const [estimatedValue, setEstimatedValue] = useState<number | null>(null)
  const [loading, setLoading] = useState(false)

  const calculatePrice = async () => {
    setLoading(true)

    // Simulate API call for property valuation
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Mock calculation based on inputs
    let basePrice = 500000 // Base price

    // Adjust for property type
    if (propertyType === 'detached') basePrice *= 1.3
    if (propertyType === 'townhouse') basePrice *= 1.1
    if (propertyType === 'condo') basePrice *= 0.8

    // Adjust for size
    if (squareFootage) {
      const sqft = parseInt(squareFootage)
      basePrice = sqft * (country === 'CA' ? 600 : 400) // Price per sqft
    }

    // Adjust for bedrooms
    if (bedrooms) {
      basePrice += parseInt(bedrooms) * 25000
    }

    // Adjust for condition
    if (condition === 'excellent') basePrice *= 1.15
    if (condition === 'good') basePrice *= 1.05
    if (condition === 'fair') basePrice *= 0.95
    if (condition === 'poor') basePrice *= 0.85

    // Add some randomness for realism
    const variance = 0.1 // 10% variance
    const randomFactor = 1 + (Math.random() - 0.5) * variance
    basePrice *= randomFactor

    setEstimatedValue(Math.round(basePrice))
    setLoading(false)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: country === 'CA' ? 'CAD' : 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Calculator Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-4 flex items-center justify-center">
              <Calculator className="w-8 h-8 mr-3 text-primary" />
              Property Pricing Calculator
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Get an instant estimate of your property's market value using our advanced pricing algorithm.
              Based on recent sales data and market trends in {country === 'CA' ? 'Canada' : 'United States'}.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Input Form */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Home className="w-5 h-5 mr-2" />
                  Property Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Property Type</Label>
                    <Select value={propertyType} onValueChange={setPropertyType}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="detached">Detached House</SelectItem>
                        <SelectItem value="townhouse">Townhouse</SelectItem>
                        <SelectItem value="condo">Condominium</SelectItem>
                        <SelectItem value="duplex">Duplex</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Location</Label>
                    <Input
                      placeholder="City, Province/State"
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>Bedrooms</Label>
                    <Select value={bedrooms} onValueChange={setBedrooms}>
                      <SelectTrigger>
                        <SelectValue placeholder="Beds" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1</SelectItem>
                        <SelectItem value="2">2</SelectItem>
                        <SelectItem value="3">3</SelectItem>
                        <SelectItem value="4">4</SelectItem>
                        <SelectItem value="5">5+</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Bathrooms</Label>
                    <Select value={bathrooms} onValueChange={setBathrooms}>
                      <SelectTrigger>
                        <SelectValue placeholder="Baths" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1</SelectItem>
                        <SelectItem value="1.5">1.5</SelectItem>
                        <SelectItem value="2">2</SelectItem>
                        <SelectItem value="2.5">2.5</SelectItem>
                        <SelectItem value="3">3</SelectItem>
                        <SelectItem value="3.5">3.5</SelectItem>
                        <SelectItem value="4">4+</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Square Footage</Label>
                    <Input
                      placeholder="sq ft"
                      value={squareFootage}
                      onChange={(e) => setSquareFootage(e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Condition</Label>
                    <Select value={condition} onValueChange={setCondition}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select condition" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="excellent">Excellent</SelectItem>
                        <SelectItem value="good">Good</SelectItem>
                        <SelectItem value="fair">Fair</SelectItem>
                        <SelectItem value="poor">Needs Work</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Year Built</Label>
                    <Input
                      placeholder="YYYY"
                      value={yearBuilt}
                      onChange={(e) => setYearBuilt(e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Lot Size (sq ft)</Label>
                  <div className="px-3">
                    <Slider
                      value={lotSize}
                      onValueChange={setLotSize}
                      max={20000}
                      min={1000}
                      step={500}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground mt-1">
                      <span>1,000</span>
                      <span>{lotSize[0].toLocaleString()} sq ft</span>
                      <span>20,000</span>
                    </div>
                  </div>
                </div>

                <Button
                  onClick={calculatePrice}
                  className="w-full"
                  size="lg"
                  disabled={loading || !propertyType || !location}
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Calculating...
                    </>
                  ) : (
                    <>
                      <Calculator className="w-4 h-4 mr-2" />
                      Calculate Property Value
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Results */}
            <div className="space-y-6">
              {estimatedValue && (
                <Card className="border-primary">
                  <CardHeader>
                    <CardTitle className="flex items-center text-primary">
                      <DollarSign className="w-5 h-5 mr-2" />
                      Estimated Market Value
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center">
                      <div className="text-4xl font-bold text-primary mb-2">
                        {formatCurrency(estimatedValue)}
                      </div>
                      <p className="text-sm text-muted-foreground mb-4">
                        Estimated market value based on current market conditions
                      </p>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="text-center">
                          <div className="font-semibold text-green-600">
                            {formatCurrency(estimatedValue * 0.95)}
                          </div>
                          <div className="text-muted-foreground">Low Estimate</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-red-600">
                            {formatCurrency(estimatedValue * 1.05)}
                          </div>
                          <div className="text-muted-foreground">High Estimate</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2" />
                    Market Insights
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Market Trend</span>
                      <span className="text-sm font-medium text-green-600">+2.3% YoY</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Days on Market</span>
                      <span className="text-sm font-medium">28 days</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Price per Sq Ft</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(country === 'CA' ? 600 : 400)}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Next Steps</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <p className="text-sm text-muted-foreground">
                      This is an automated estimate. For a more accurate valuation:
                    </p>
                    <ul className="text-sm space-y-2">
                      <li>• Get a professional appraisal</li>
                      <li>• Compare with recent sales in your area</li>
                      <li>• Consult with a local real estate agent</li>
                      <li>• Consider market conditions and timing</li>
                    </ul>
                    <Button variant="outline" className="w-full mt-4">
                      Get Professional Appraisal
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section - Moved to bottom */}
      <HeroSection userType={userType} />
    </div>
  )
}
