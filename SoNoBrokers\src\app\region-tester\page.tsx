import { RegionTester } from '@/components/shared/dev/RegionTester'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Region Tester | SoNo Brokers',
  description: 'Development utility for testing region detection and routing behavior',
}

export default function RegionTesterPage() {
  // Only show in development mode
  if (process.env.NODE_ENV !== 'development') {
    return (
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">🚫 Not Available</h1>
          <p className="text-muted-foreground">
            The Region Tester is only available in development mode.
          </p>
        </div>
      </div>
    )
  }

  return <RegionTester />
}
