using DotNetEnv;

namespace MicroSaasWebApi.Services.Configuration
{
    /// <summary>
    /// Service for loading environment variables from .env file
    /// </summary>
    public static class EnvironmentConfigurationService
    {
        /// <summary>
        /// Load environment variables from .env file
        /// </summary>
        public static void LoadEnvironmentVariables()
        {
            try
            {
                // Load .env file from the root directory
                var envPath = Path.Combine(Directory.GetCurrentDirectory(), ".env");
                if (File.Exists(envPath))
                {
                    Env.Load(envPath);
                    Console.WriteLine("Environment variables loaded from .env file");
                }
                else
                {
                    Console.WriteLine("No .env file found, using system environment variables");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading .env file: {ex.Message}");
            }
        }

        /// <summary>
        /// Get environment variable with fallback
        /// </summary>
        /// <param name="key">Environment variable key</param>
        /// <param name="defaultValue">Default value if not found</param>
        /// <returns>Environment variable value or default</returns>
        public static string GetEnvironmentVariable(string key, string defaultValue = "")
        {
            return Environment.GetEnvironmentVariable(key) ?? defaultValue;
        }

        /// <summary>
        /// Validate required environment variables
        /// </summary>
        /// <param name="requiredVariables">List of required environment variable keys</param>
        /// <returns>List of missing variables</returns>
        public static List<string> ValidateRequiredVariables(params string[] requiredVariables)
        {
            var missingVariables = new List<string>();

            foreach (var variable in requiredVariables)
            {
                if (string.IsNullOrEmpty(Environment.GetEnvironmentVariable(variable)))
                {
                    missingVariables.Add(variable);
                }
            }

            return missingVariables;
        }
    }
}
