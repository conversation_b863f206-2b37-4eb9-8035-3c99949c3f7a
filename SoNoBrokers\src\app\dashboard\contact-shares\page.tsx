import { Suspense } from 'react'
import { Metada<PERSON> } from 'next'
import { redirect } from 'next/navigation'
import { getCurrentUserProfile } from '@/lib/api/auth-api'
import { getContactShares, getContactShareStats } from '@/lib/api/contact-sharing-api'
import { ContactSharesDashboard } from '@/components/contact-sharing/ContactSharesDashboard'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

/**
 * Contact Shares Dashboard Page - Server Component
 * Shows user's contact sharing activity, offers, and visit requests
 */

export const metadata: Metadata = {
  title: 'Contact Shares | Dashboard | SoNoBrokers',
  description: 'Manage your property contact shares, offers, and visit requests',
}

export default async function ContactSharesPage() {
  // Check authentication
  const userProfile = await getCurrentUserProfile()
  if (!userProfile) {
    redirect('/sign-in')
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Contact Shares & Offers
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-300">
          Manage your property inquiries, offers, and visit requests
        </p>
      </div>

      {/* Content */}
      <Suspense fallback={<ContactSharesLoading />}>
        <ContactSharesContent />
      </Suspense>
    </div>
  )
}

/**
 * Contact Shares Content Server Component
 */
async function ContactSharesContent() {
  try {
    // Fetch contact shares and stats in parallel
    const [contactSharesResponse, stats] = await Promise.all([
      getContactShares({
        page: 1,
        limit: 20,
        // sortBy: 'createdAt', // TODO: Add to ContactShareSearchParams interface
        // sortOrder: 'desc'
      }),
      getContactShareStats()
    ])

    return (
      <ContactSharesDashboard
        initialContactShares={contactSharesResponse.contactShares.map(response => ({
          ...response,
          emailSent: true, // Default for legacy compatibility
          emailSentAt: new Date(response.createdAt),
          createdAt: new Date(response.createdAt),
          updatedAt: new Date(response.updatedAt),
          respondedAt: undefined as Date | undefined,
          sellerResponse: undefined as string | undefined,
          preferredVisitDate: response.preferredVisitDate,
          preferredVisitTime: response.preferredVisitTime,
          schedulingPreference: response.schedulingPreference
        }))}
        stats={stats}
        pagination={{
          page: contactSharesResponse.page,
          totalPages: contactSharesResponse.totalPages,
          total: contactSharesResponse.total,
          hasMore: contactSharesResponse.hasMore
        }}
      />
    )
  } catch (error) {
    console.error('Failed to load contact shares:', error)

    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <div className="text-center">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Unable to load contact shares
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              There was an error loading your contact sharing data. Please try again.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }
}

/**
 * Loading Component
 */
function ContactSharesLoading() {
  return (
    <div className="space-y-6">
      {/* Stats Loading */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-3 w-20" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Tabs Loading */}
      <div className="space-y-4">
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-10 flex-1" />
          ))}
        </div>

        {/* Content Loading */}
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-2">
                      <Skeleton className="h-5 w-48" />
                      <Skeleton className="h-4 w-32" />
                    </div>
                    <Skeleton className="h-6 w-20" />
                  </div>
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                  <div className="flex justify-between items-center">
                    <Skeleton className="h-4 w-24" />
                    <div className="flex space-x-2">
                      <Skeleton className="h-8 w-20" />
                      <Skeleton className="h-8 w-16" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
