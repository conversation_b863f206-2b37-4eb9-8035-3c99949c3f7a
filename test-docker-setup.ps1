# =============================================================================
# SoNoBrokers Docker Test Script
# =============================================================================
# Tests both React and Web API running in separate Docker containers
# =============================================================================

param(
    [switch]$StopOnly,
    [switch]$StartOnly,
    [switch]$TestOnly
)

$ErrorActionPreference = "Stop"

Write-Host "SoNoBrokers Docker Test Script" -ForegroundColor Cyan
Write-Host "=" * 40

# Paths
$rootPath = $PSScriptRoot
$webApiPath = Join-Path $rootPath "SoNoBrokersWebApi\MicroSaasWebApi.App"
$reactPath = Join-Path $rootPath "SoNoBrokers"

# Function to check if Docker is running
function Test-DockerRunning {
    try {
        docker version | Out-Null
        return $true
    } catch {
        Write-Host "ERROR: Docker is not running or not installed" -ForegroundColor Red
        return $false
    }
}

# Function to stop containers
function Stop-Containers {
    Write-Host "Stopping existing containers..." -ForegroundColor Yellow
    
    # Stop Web API
    Push-Location $webApiPath
    try {
        docker-compose down --remove-orphans
        Write-Host "  Web API containers stopped" -ForegroundColor Green
    } catch {
        Write-Host "  Web API: No containers to stop" -ForegroundColor Gray
    }
    Pop-Location
    
    # Stop React App
    Push-Location $reactPath
    try {
        docker-compose down --remove-orphans
        Write-Host "  React containers stopped" -ForegroundColor Green
    } catch {
        Write-Host "  React: No containers to stop" -ForegroundColor Gray
    }
    Pop-Location
}

# Function to start containers
function Start-Containers {
    Write-Host "Starting containers..." -ForegroundColor Yellow
    
    # Start Web API first
    Write-Host "  Starting Web API..." -ForegroundColor Cyan
    Push-Location $webApiPath
    try {
        docker-compose up -d --build
        Write-Host "  Web API started successfully" -ForegroundColor Green
    } catch {
        Write-Host "  ERROR: Failed to start Web API" -ForegroundColor Red
        Pop-Location
        return $false
    }
    Pop-Location
    
    # Wait a bit for Web API to start
    Write-Host "  Waiting for Web API to initialize..." -ForegroundColor Gray
    Start-Sleep -Seconds 10
    
    # Start React App
    Write-Host "  Starting React App..." -ForegroundColor Cyan
    Push-Location $reactPath
    try {
        docker-compose up -d --build
        Write-Host "  React App started successfully" -ForegroundColor Green
    } catch {
        Write-Host "  ERROR: Failed to start React App" -ForegroundColor Red
        Pop-Location
        return $false
    }
    Pop-Location
    
    return $true
}

# Function to test endpoints
function Test-Endpoints {
    Write-Host "Testing endpoints..." -ForegroundColor Yellow
    
    # Test Web API
    Write-Host "  Testing Web API health..." -ForegroundColor Cyan
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:7163/api/health/ping" -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "    Web API health check: PASSED" -ForegroundColor Green
        } else {
            Write-Host "    Web API health check: FAILED (Status: $($response.StatusCode))" -ForegroundColor Red
        }
    } catch {
        Write-Host "    Web API health check: FAILED (Error: $($_.Exception.Message))" -ForegroundColor Red
    }
    
    # Test Web API SoNoBrokers endpoint
    Write-Host "  Testing Web API SoNoBrokers endpoint..." -ForegroundColor Cyan
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:7163/api/sonobrokers/test/ping" -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "    SoNoBrokers test endpoint: PASSED" -ForegroundColor Green
        } else {
            Write-Host "    SoNoBrokers test endpoint: FAILED (Status: $($response.StatusCode))" -ForegroundColor Red
        }
    } catch {
        Write-Host "    SoNoBrokers test endpoint: FAILED (Error: $($_.Exception.Message))" -ForegroundColor Red
    }
    
    # Test React App
    Write-Host "  Testing React App..." -ForegroundColor Cyan
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000" -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "    React App: PASSED" -ForegroundColor Green
        } else {
            Write-Host "    React App: FAILED (Status: $($response.StatusCode))" -ForegroundColor Red
        }
    } catch {
        Write-Host "    React App: FAILED (Error: $($_.Exception.Message))" -ForegroundColor Red
    }
    
    # Test React test-api page
    Write-Host "  Testing React test-api page..." -ForegroundColor Cyan
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000/test-api" -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "    React test-api page: PASSED" -ForegroundColor Green
        } else {
            Write-Host "    React test-api page: FAILED (Status: $($response.StatusCode))" -ForegroundColor Red
        }
    } catch {
        Write-Host "    React test-api page: FAILED (Error: $($_.Exception.Message))" -ForegroundColor Red
    }
}

# Function to show container status
function Show-ContainerStatus {
    Write-Host "Container Status:" -ForegroundColor Yellow
    docker ps --filter "name=sonobrokers" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

# Main execution
if (-not (Test-DockerRunning)) {
    exit 1
}

if ($StopOnly) {
    Stop-Containers
    exit 0
}

if ($TestOnly) {
    Test-Endpoints
    Show-ContainerStatus
    exit 0
}

if (-not $StartOnly) {
    Stop-Containers
}

if (Start-Containers) {
    Write-Host "Waiting for services to be ready..." -ForegroundColor Gray
    Start-Sleep -Seconds 15
    
    Test-Endpoints
    Show-ContainerStatus
    
    Write-Host "`nServices are running!" -ForegroundColor Green
    Write-Host "  Web API: http://localhost:7163" -ForegroundColor Cyan
    Write-Host "  React App: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "  Test API Page: http://localhost:3000/test-api" -ForegroundColor Cyan
    Write-Host "  API Documentation: http://localhost:7163/scalar/v1" -ForegroundColor Cyan
} else {
    Write-Host "Failed to start services" -ForegroundColor Red
    exit 1
}
