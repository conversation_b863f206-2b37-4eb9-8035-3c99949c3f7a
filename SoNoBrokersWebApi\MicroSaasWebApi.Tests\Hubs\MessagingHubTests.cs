using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using MicroSaasWebApi.Hubs;
using MicroSaasWebApi.Services.SoNoBrokers;
using MicroSaasWebApi.Models.SoNoBrokers.Messaging;
using System.Security.Claims;
using Microsoft.AspNetCore.SignalR.Client;

namespace MicroSaasWebApi.Tests.Hubs
{
    [TestFixture]
    public class MessagingHubTests
    {
        private Mock<IMessagingService> _mockMessagingService;
        private Mock<ILogger<MessagingHub>> _mockLogger;
        private Mock<HubCallerContext> _mockContext;
        private Mock<IHubCallerClients> _mockClients;
        private Mock<IGroupManager> _mockGroups;
        private Mock<IClientProxy> _mockClientProxy;
        private MessagingHub _messagingHub;

        [SetUp]
        public void Setup()
        {
            _mockMessagingService = new Mock<IMessagingService>();
            _mockLogger = new Mock<ILogger<MessagingHub>>();
            _mockContext = new Mock<HubCallerContext>();
            _mockClients = new Mock<IHubCallerClients>();
            _mockGroups = new Mock<IGroupManager>();
            _mockClientProxy = new Mock<IClientProxy>();

            _messagingHub = new MessagingHub(_mockMessagingService.Object, _mockLogger.Object);

            // Setup hub context
            _messagingHub.Context = _mockContext.Object;
            _messagingHub.Clients = _mockClients.Object;
            _messagingHub.Groups = _mockGroups.Object;

            // Setup default context values
            _mockContext.Setup(x => x.ConnectionId).Returns("test-connection-id");
            _mockClients.Setup(x => x.Caller).Returns(_mockClientProxy.Object);
            _mockClients.Setup(x => x.Others).Returns(_mockClientProxy.Object);
            _mockClients.Setup(x => x.OthersInGroup(It.IsAny<string>())).Returns(_mockClientProxy.Object);
        }

        [Test]
        public async Task JoinConversation_WithValidUser_JoinsGroup()
        {
            // Arrange
            var userId = "test-user-123";
            var conversationId = "test-conversation-123";
            
            var claims = new List<Claim>
            {
                new Claim("sub", userId),
                new Claim("user_id", userId)
            };
            var identity = new ClaimsIdentity(claims);
            var principal = new ClaimsPrincipal(identity);

            _mockContext.Setup(x => x.User).Returns(principal);
            _mockMessagingService.Setup(x => x.CanUserAccessConversationAsync(userId, conversationId))
                .ReturnsAsync(true);

            // Act
            await _messagingHub.JoinConversation(conversationId);

            // Assert
            _mockGroups.Verify(x => x.AddToGroupAsync("test-connection-id", $"conversation_{conversationId}", default), Times.Once);
            _mockClientProxy.Verify(x => x.SendCoreAsync("JoinedConversation", 
                It.Is<object[]>(args => args.Length == 1 && args[0].ToString() == conversationId), 
                default), Times.Once);
        }

        [Test]
        public async Task JoinConversation_WithoutAccess_SendsError()
        {
            // Arrange
            var userId = "test-user-123";
            var conversationId = "test-conversation-123";
            
            var claims = new List<Claim>
            {
                new Claim("sub", userId),
                new Claim("user_id", userId)
            };
            var identity = new ClaimsIdentity(claims);
            var principal = new ClaimsPrincipal(identity);

            _mockContext.Setup(x => x.User).Returns(principal);
            _mockMessagingService.Setup(x => x.CanUserAccessConversationAsync(userId, conversationId))
                .ReturnsAsync(false);

            // Act
            await _messagingHub.JoinConversation(conversationId);

            // Assert
            _mockGroups.Verify(x => x.AddToGroupAsync(It.IsAny<string>(), It.IsAny<string>(), default), Times.Never);
            _mockClientProxy.Verify(x => x.SendCoreAsync("Error", 
                It.Is<object[]>(args => args.Length == 1 && args[0].ToString() == "Access denied to conversation"), 
                default), Times.Once);
        }

        [Test]
        public async Task LeaveConversation_RemovesFromGroup()
        {
            // Arrange
            var conversationId = "test-conversation-123";

            // Act
            await _messagingHub.LeaveConversation(conversationId);

            // Assert
            _mockGroups.Verify(x => x.RemoveFromGroupAsync("test-connection-id", $"conversation_{conversationId}", default), Times.Once);
            _mockClientProxy.Verify(x => x.SendCoreAsync("LeftConversation", 
                It.Is<object[]>(args => args.Length == 1 && args[0].ToString() == conversationId), 
                default), Times.Once);
        }

        [Test]
        public async Task SendTypingIndicator_WithValidAccess_SendsToOthers()
        {
            // Arrange
            var userId = "test-user-123";
            var conversationId = "test-conversation-123";
            var userName = "Test User";
            
            var claims = new List<Claim>
            {
                new Claim("sub", userId),
                new Claim("user_id", userId),
                new Claim("name", userName)
            };
            var identity = new ClaimsIdentity(claims, "test");
            identity.AddClaim(new Claim(ClaimTypes.Name, userName));
            var principal = new ClaimsPrincipal(identity);

            _mockContext.Setup(x => x.User).Returns(principal);
            _mockMessagingService.Setup(x => x.CanUserAccessConversationAsync(userId, conversationId))
                .ReturnsAsync(true);

            // Act
            await _messagingHub.SendTypingIndicator(conversationId, true);

            // Assert
            _mockClients.Verify(x => x.OthersInGroup($"conversation_{conversationId}"), Times.Once);
            _mockClientProxy.Verify(x => x.SendCoreAsync("TypingIndicator", 
                It.Is<object[]>(args => args.Length == 1), 
                default), Times.Once);
        }

        [Test]
        public async Task MarkMessagesAsRead_WithValidAccess_UpdatesAndNotifies()
        {
            // Arrange
            var userId = "test-user-123";
            var conversationId = "test-conversation-123";
            var messageIds = new List<string> { "msg-1", "msg-2" };
            
            var claims = new List<Claim>
            {
                new Claim("sub", userId),
                new Claim("user_id", userId)
            };
            var identity = new ClaimsIdentity(claims);
            var principal = new ClaimsPrincipal(identity);

            _mockContext.Setup(x => x.User).Returns(principal);
            _mockMessagingService.Setup(x => x.MarkMessagesAsReadAsync(userId, conversationId, messageIds))
                .ReturnsAsync(true);

            // Act
            await _messagingHub.MarkMessagesAsRead(conversationId, messageIds);

            // Assert
            _mockMessagingService.Verify(x => x.MarkMessagesAsReadAsync(userId, conversationId, messageIds), Times.Once);
            _mockClients.Verify(x => x.OthersInGroup($"conversation_{conversationId}"), Times.Once);
            _mockClientProxy.Verify(x => x.SendCoreAsync("MessagesRead", 
                It.IsAny<object[]>(), 
                default), Times.Once);
            _mockClientProxy.Verify(x => x.SendCoreAsync("MessagesMarkedAsRead", 
                It.Is<object[]>(args => args.Length == 1 && args[0].ToString() == conversationId), 
                default), Times.Once);
        }

        [Test]
        public async Task GetOnlineUsers_ReturnsUserList()
        {
            // Act
            await _messagingHub.GetOnlineUsers();

            // Assert
            _mockClientProxy.Verify(x => x.SendCoreAsync("OnlineUsers", 
                It.Is<object[]>(args => args.Length == 1), 
                default), Times.Once);
        }

        [Test]
        public void SendMessageNotification_WithValidNotification_SendsToGroup()
        {
            // Arrange
            var mockHubContext = new Mock<IHubContext<MessagingHub>>();
            var mockClients = new Mock<IHubCallerClients<MessagingHub>>();
            var mockClientProxy = new Mock<IClientProxy>();

            mockHubContext.Setup(x => x.Clients).Returns(mockClients.Object);
            mockClients.Setup(x => x.Group(It.IsAny<string>())).Returns(mockClientProxy.Object);

            var notification = new MessageNotification
            {
                MessageId = "msg-123",
                ConversationId = "conv-123",
                SenderId = "user-123",
                SenderName = "Test User",
                Content = "Test message",
                CreatedAt = DateTime.UtcNow,
                RecipientIds = new List<string> { "user-456" }
            };

            // Act
            var task = MessagingHub.SendMessageNotification(mockHubContext.Object, notification);

            // Assert
            Assert.That(task, Is.Not.Null);
            // Note: In a real test, we'd need to await the task and verify the calls
            // This is a simplified test due to static method limitations
        }

        [Test]
        public void SendConversationUpdate_WithValidUpdate_SendsToGroup()
        {
            // Arrange
            var mockHubContext = new Mock<IHubContext<MessagingHub>>();
            var mockClients = new Mock<IHubCallerClients<MessagingHub>>();
            var mockClientProxy = new Mock<IClientProxy>();

            mockHubContext.Setup(x => x.Clients).Returns(mockClients.Object);
            mockClients.Setup(x => x.Group(It.IsAny<string>())).Returns(mockClientProxy.Object);

            var conversationId = "conv-123";
            var update = new { Subject = "Updated Subject", IsActive = true };

            // Act
            var task = MessagingHub.SendConversationUpdate(mockHubContext.Object, conversationId, update);

            // Assert
            Assert.That(task, Is.Not.Null);
        }

        [TearDown]
        public void TearDown()
        {
            _mockMessagingService?.Reset();
            _mockLogger?.Reset();
            _mockContext?.Reset();
            _mockClients?.Reset();
            _mockGroups?.Reset();
            _mockClientProxy?.Reset();
        }
    }
}
