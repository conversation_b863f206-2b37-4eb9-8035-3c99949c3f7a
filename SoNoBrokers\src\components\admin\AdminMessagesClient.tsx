'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  MessageCircle, 
  Search, 
  Filter, 
  Users, 
  Home, 
  Clock,
  TrendingUp,
  Eye,
  Archive,
  Trash2,
  User,
  Mail,
  Phone
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import type { ConversationListItem } from '@/lib/api/messaging-api'

interface AdminMessagesClientProps {
  initialConversations: ConversationListItem[]
  stats: {
    totalConversations: number
    activeConversations: number
    totalMessages: number
    unreadMessages: number
  }
}

export function AdminMessagesClient({ 
  initialConversations, 
  stats 
}: AdminMessagesClientProps) {
  const [conversations, setConversations] = useState(initialConversations)
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [activeTab, setActiveTab] = useState('all')
  const [filteredConversations, setFilteredConversations] = useState(initialConversations)

  useEffect(() => {
    let filtered = conversations

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(conv => 
        conv.propertyTitle?.toLowerCase().includes(query) ||
        conv.otherParticipantName?.toLowerCase().includes(query) ||
        conv.subject?.toLowerCase().includes(query) ||
        conv.lastMessageContent?.toLowerCase().includes(query)
      )
    }

    // Apply tab filter
    switch (activeTab) {
      case 'active':
        filtered = filtered.filter(conv => conv.isActive)
        break
      case 'archived':
        filtered = filtered.filter(conv => !conv.isActive)
        break
      case 'unread':
        filtered = filtered.filter(conv => conv.unreadCount > 0)
        break
    }

    setFilteredConversations(filtered)
  }, [conversations, searchQuery, activeTab])

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatLastMessageTime = (dateString?: string) => {
    if (!dateString) return ''
    
    try {
      const date = new Date(dateString)
      return formatDistanceToNow(date, { addSuffix: true })
    } catch {
      return ''
    }
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Conversations</CardTitle>
            <MessageCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalConversations}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Conversations</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeConversations}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Messages</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalMessages}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unread Messages</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.unreadMessages}</div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Conversations List */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>All Conversations</CardTitle>
              <CardDescription>
                Monitor and manage all user conversations
              </CardDescription>
              
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search conversations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              {/* Tabs */}
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="active">Active</TabsTrigger>
                  <TabsTrigger value="archived">Archived</TabsTrigger>
                  <TabsTrigger value="unread">Unread</TabsTrigger>
                </TabsList>
              </Tabs>
            </CardHeader>
            
            <CardContent className="p-0">
              <div className="divide-y divide-gray-200 dark:divide-gray-700 max-h-96 overflow-y-auto">
                {filteredConversations.map((conversation) => (
                  <div
                    key={conversation.id}
                    className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors ${
                      selectedConversation === conversation.id 
                        ? 'bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-500' 
                        : ''
                    }`}
                    onClick={() => setSelectedConversation(conversation.id)}
                  >
                    <div className="flex items-start gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 text-xs">
                          {conversation.otherParticipantName ? getInitials(conversation.otherParticipantName) : <User className="h-4 w-4" />}
                        </AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {conversation.otherParticipantName || 'Unknown User'}
                          </h4>
                          {conversation.unreadCount > 0 && (
                            <Badge variant="default" className="text-xs">
                              {conversation.unreadCount}
                            </Badge>
                          )}
                        </div>
                        
                        {conversation.propertyTitle && (
                          <div className="flex items-center gap-1 mb-1">
                            <Home className="h-3 w-3 text-gray-400" />
                            <span className="text-xs text-gray-600 dark:text-gray-400 truncate">
                              {conversation.propertyTitle}
                            </span>
                          </div>
                        )}
                        
                        <p className="text-xs text-gray-500 dark:text-gray-500 truncate">
                          {conversation.lastMessageContent || conversation.subject}
                        </p>
                        
                        <div className="flex items-center justify-between mt-1">
                          <span className="text-xs text-gray-400 flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {formatLastMessageTime(conversation.lastMessageAt)}
                          </span>
                          
                          <div className="flex items-center gap-1">
                            {!conversation.isActive && (
                              <Badge variant="secondary" className="text-xs">
                                Archived
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                
                {filteredConversations.length === 0 && (
                  <div className="p-8 text-center">
                    <MessageCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-600 dark:text-gray-400">
                      {searchQuery ? `No conversations found matching "${searchQuery}"` : 'No conversations found'}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Conversation Details */}
        <div className="lg:col-span-2">
          {selectedConversation ? (
            <ConversationDetails conversationId={selectedConversation} />
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <MessageCircle className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Select a conversation
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-center">
                  Choose a conversation from the list to view details and messages
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

function ConversationDetails({ conversationId }: { conversationId: string }) {
  // This would fetch conversation details and messages
  // For now, showing a placeholder
  return (
    <Card>
      <CardHeader>
        <CardTitle>Conversation Details</CardTitle>
        <CardDescription>
          View and manage conversation {conversationId}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Status:</span>
            <Badge variant="default">Active</Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Participants:</span>
            <span className="text-sm">2 users</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Messages:</span>
            <span className="text-sm">5 messages</span>
          </div>
          
          <div className="flex gap-2">
            <Button size="sm" variant="outline">
              <Eye className="h-4 w-4 mr-2" />
              View Full Conversation
            </Button>
            <Button size="sm" variant="outline">
              <Archive className="h-4 w-4 mr-2" />
              Archive
            </Button>
            <Button size="sm" variant="outline">
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
