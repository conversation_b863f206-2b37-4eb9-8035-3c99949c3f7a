using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Tests.Common;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Controllers
{
    public class CommunicationControllerTests : IClassFixture<TestWebApplicationFactory<Program>>, IAsyncLifetime
    {
        private readonly TestWebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;
        private readonly ITestOutputHelper _output;

        public CommunicationControllerTests(TestWebApplicationFactory<Program> factory, ITestOutputHelper output)
        {
            _factory = factory;
            _output = output;
            _client = _factory.CreateClient();
        }

        public async Task InitializeAsync()
        {
            await _factory.SeedTestDataAsync();
        }

        public async Task DisposeAsync()
        {
            await _factory.CleanupTestDataAsync();
        }

        [Fact]
        public async Task ContactConcierge_WithValidRequest_ReturnsSuccess()
        {
            // Arrange
            var request = new ContactConciergeRequest
            {
                Name = "John <PERSON>e",
                Email = "<EMAIL>",
                Phone = "************",
                PropertyAddress = "123 Main Street, Toronto, ON",
                PropertyValue = "500000",
                Timeline = "3-6 months",
                Requirements = "Need help with selling my property",
                Country = "CA",
                SelectedPackage = "Premium"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/contact-concierge", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<ContactConciergeResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Message.Should().Be("Inquiry sent successfully");
        }

        [Fact]
        public async Task ContactConcierge_WithMissingName_ReturnsBadRequest()
        {
            // Arrange
            var request = new ContactConciergeRequest
            {
                Name = "",
                Email = "<EMAIL>",
                Country = "CA"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/contact-concierge", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task ContactConcierge_WithMissingEmail_ReturnsBadRequest()
        {
            // Arrange
            var request = new ContactConciergeRequest
            {
                Name = "John Doe",
                Email = "",
                Country = "CA"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/contact-concierge", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task AddToWaitingList_WithValidEmail_ReturnsSuccess()
        {
            // Arrange
            var request = new WaitingListRequest
            {
                Email = "<EMAIL>"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/waiting-list", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<WaitingListResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Message.Should().Be("Successfully added to waiting list");
            result.ContactId.Should().NotBeNullOrEmpty();
            result.ContactId.ShouldBeValidGuid();
        }

        [Fact]
        public async Task AddToWaitingList_WithInvalidEmail_ReturnsBadRequest()
        {
            // Arrange
            var request = new WaitingListRequest
            {
                Email = ""
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/waiting-list", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task GetGeoLocation_WithoutIP_ReturnsDefaultLocation()
        {
            // Act
            var response = await _client.GetAsync("/api/sonobrokers/geo");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<GeoLocationResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Country.Should().NotBeNullOrEmpty();
            result.CountryName.Should().NotBeNullOrEmpty();
            result.City.Should().NotBeNullOrEmpty();
            result.Region.Should().NotBeNullOrEmpty();
            result.Timezone.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task GetGeoLocation_WithValidIP_ReturnsLocationData()
        {
            // Act
            var response = await _client.GetAsync("/api/sonobrokers/geo?ip=*******");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<GeoLocationResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Country.Should().NotBeNullOrEmpty();
            result.CountryName.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task GetEnumValues_ReturnsAllEnumValues()
        {
            // Act
            var response = await _client.GetAsync("/api/sonobrokers/enums");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<EnumValuesResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Enums.Should().NotBeEmpty();
            result.Enums.Should().ContainKey("countries");
            result.Enums.Should().ContainKey("serviceTypes");
            result.Enums.Should().ContainKey("userTypes");
            result.Enums.Should().ContainKey("propertyStatuses");
            result.Enums.Should().ContainKey("advertiserPlans");
            result.Enums.Should().ContainKey("advertiserStatuses");
        }

        [Theory]
        [InlineData("CA")]
        [InlineData("US")]
        [InlineData("UAE")]
        public async Task ContactConcierge_WithDifferentCountries_ReturnsSuccess(string country)
        {
            // Arrange
            var request = new ContactConciergeRequest
            {
                Name = "Test User",
                Email = "<EMAIL>",
                Country = country,
                PropertyAddress = "123 Test Street"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/contact-concierge", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<ContactConciergeResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
        }

        [Theory]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        public async Task AddToWaitingList_WithDifferentEmails_ReturnsSuccess(string email)
        {
            // Arrange
            var request = new WaitingListRequest { Email = email };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/waiting-list", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<WaitingListResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.ContactId.ShouldBeValidGuid();
        }

        [Fact]
        public async Task ContactConcierge_WithCompleteRequest_ReturnsSuccess()
        {
            // Arrange
            var request = new ContactConciergeRequest
            {
                Name = "Jane Smith",
                Email = "<EMAIL>",
                Phone = "************",
                PropertyAddress = "456 Oak Avenue, Vancouver, BC",
                PropertyValue = "750000",
                Timeline = "1-3 months",
                Requirements = "Looking for comprehensive selling package with staging and marketing",
                PackageInterest = "Premium Package",
                Country = "CA",
                SelectedPackage = "Premium"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/contact-concierge", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<ContactConciergeResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Message.Should().Be("Inquiry sent successfully");
        }

        [Fact]
        public async Task GetGeoLocation_WithInvalidIP_ReturnsDefaultLocation()
        {
            // Act
            var response = await _client.GetAsync("/api/sonobrokers/geo?ip=invalid-ip");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<GeoLocationResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            // Should return default values when IP lookup fails
            result!.Country.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task GetEnumValues_HasCacheHeaders()
        {
            // Act
            var response = await _client.GetAsync("/api/sonobrokers/enums");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            // Check if response has cache control headers (if implemented)
            response.Headers.Should().NotBeNull();
        }

        [Fact]
        public async Task ContactConcierge_WithMinimalData_ReturnsSuccess()
        {
            // Arrange
            var request = new ContactConciergeRequest
            {
                Name = "Minimal User",
                Email = "<EMAIL>",
                Country = "CA"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/contact-concierge", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<ContactConciergeResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
        }
    }
}
