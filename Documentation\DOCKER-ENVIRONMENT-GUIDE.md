# 🐳 Docker Environment Configuration Guide

This guide explains how to use the environment-specific Docker configurations for SoNoBrokers.

## 📁 Environment Files Structure

### **React Application (SoNoBrokers/)**
```
SoNoBrokers/
├── .env                           # Base Next.js environment (local development)
├── .env.Development               # Development overrides (local development)
├── .env.Development.local         # Local development overrides (gitignored)
├── .env.docker                    # Base Docker configuration
├── .env.docker.development        # Development Docker configuration
├── .env.docker.production         # Production Docker configuration
├── docker-compose.development.yml # Development Docker Compose
└── docker-compose.production.yml  # Production Docker Compose
```

### **Web API (SoNoBrokersWebApi/MicroSaasWebApi.App/)**
```
MicroSaasWebApi.App/
├── .env                           # Base Web API environment (local development)
├── .env.docker.development        # Development Docker configuration
└── .env.docker.production         # Production Docker configuration
```

## 🔄 Environment File Loading Order

### **Next.js Local Development**
1. `.env` (base configuration)
2. `.env.local` (local overrides, if exists)
3. `.env.Development` (development-specific)
4. `.env.Development.local` (local development overrides)

### **Docker Development**
1. `.env.docker` (base Docker configuration)
2. `.env.docker.development` (development Docker overrides)

### **Docker Production**
1. `.env.docker` (base Docker configuration)
2. `.env.docker.production` (production Docker overrides)

## 🚀 Usage Commands

### **Development Environment**

#### React Frontend
```powershell
# Navigate to React folder
cd C:\Projects\SoNoBrokersRoot\SoNoBrokers

# Build development image
docker build -t sonobrokers-frontend:dev .

# Run with development environment
docker run -d `
  --name sonobrokers-frontend-dev `
  --env-file .env.docker.development `
  -p 3000:3000 `
  sonobrokers-frontend:dev

# Or use Docker Compose
docker-compose -f docker-compose.development.yml up --build -d
```

#### Web API
```powershell
# Navigate to Web API folder
cd C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi

# Build development image
docker build -t sonobrokers-api:dev .

# Run with development environment
docker run -d `
  --name sonobrokers-api-dev `
  --env-file MicroSaasWebApi.App/.env.docker.development `
  -p 7163:8080 `
  sonobrokers-api:dev
```

### **Production Environment**

#### React Frontend
```powershell
# Navigate to React folder
cd C:\Projects\SoNoBrokersRoot\SoNoBrokers

# Build production image
docker build -t sonobrokers-frontend:prod .

# Run with production environment
docker run -d `
  --name sonobrokers-frontend-prod `
  --env-file .env.docker.production `
  -p 3000:3000 `
  sonobrokers-frontend:prod

# Or use Docker Compose
docker-compose -f docker-compose.production.yml up --build -d
```

#### Web API
```powershell
# Navigate to Web API folder
cd C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi

# Build production image
docker build -t sonobrokers-api:prod .

# Run with production environment
docker run -d `
  --name sonobrokers-api-prod `
  --env-file MicroSaasWebApi.App/.env.docker.production `
  -p 7163:8080 `
  sonobrokers-api:prod
```

## 🔧 Key Differences Between Environments

### **Development vs Production Settings**

| Setting | Development | Production |
|---------|-------------|------------|
| **NODE_ENV** | development | production |
| **App URL** | http://localhost:3000 | https://www.sonobrokers.com |
| **API URL** | http://localhost:7163 | https://api.sonobrokers.com |
| **Clerk Keys** | Test keys (pk_test_...) | Live keys (pk_live_...) |
| **Debug Mode** | Enabled | Disabled |
| **Hot Reload** | Enabled | Disabled |
| **Logging** | Verbose | Minimal |
| **Feature Flags** | More features enabled | Production-ready features only |

### **Development Features**
- ✅ **Hot reload** enabled for faster development
- ✅ **Debug information** visible
- ✅ **Development tools** enabled
- ✅ **Test API keys** for safe testing
- ✅ **Verbose logging** for troubleshooting

### **Production Features**
- ✅ **Optimized builds** for performance
- ✅ **Security hardening** with production keys
- ✅ **Minimal logging** for performance
- ✅ **Resource limits** configured
- ✅ **Health checks** enabled

## 📊 Environment Variables Override Examples

### **Development Overrides**
```bash
# Development enables more features
NEXT_PUBLIC_DEV_MODE=true
NEXT_PUBLIC_SHOW_DEBUG_INFO=true
NEXT_PUBLIC_LAUNCH_MODE=true
NEXT_PUBLIC_ENABLE_REGION_TESTER=true

# Development uses test keys
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...
```

### **Production Overrides**
```bash
# Production disables debug features
NEXT_PUBLIC_DEV_MODE=false
NEXT_PUBLIC_SHOW_DEBUG_INFO=false
NEXT_PUBLIC_LAUNCH_MODE=false
NEXT_PUBLIC_ENABLE_REGION_TESTER=false

# Production uses live keys
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_...
CLERK_SECRET_KEY=sk_live_...
```

## 🔒 Security Best Practices

### **Environment File Security**
1. **Never commit** `.env.docker.local` files
2. **Use different keys** for development and production
3. **Rotate secrets** regularly in production
4. **Use Azure Key Vault** or similar for production secrets
5. **Validate environment variables** in application startup

### **Docker Security**
1. **Use non-root users** in containers
2. **Limit container resources** in production
3. **Enable health checks** for monitoring
4. **Use multi-stage builds** to reduce image size
5. **Scan images** for vulnerabilities

## 🎯 Quick Reference Commands

### **Check Environment Files**
```powershell
# List all environment files
Get-ChildItem -Path "SoNoBrokers" -Filter ".env*"
Get-ChildItem -Path "SoNoBrokersWebApi\MicroSaasWebApi.App" -Filter ".env*"

# View specific environment file
Get-Content "SoNoBrokers\.env.docker.development" | Select-Object -First 10
Get-Content "SoNoBrokers\.env.docker.production" | Select-Object -First 10
```

### **Container Management**
```powershell
# Stop all containers
docker stop $(docker ps -q)

# Remove all containers
docker rm $(docker ps -aq)

# Clean up images
docker image prune -f

# View container logs
docker logs sonobrokers-frontend-dev
docker logs sonobrokers-api-dev
```

### **Environment Testing**
```powershell
# Test development environment
curl http://localhost:3000
curl http://localhost:7163/health

# Check container environment variables
docker exec sonobrokers-frontend-dev env | findstr NEXT_PUBLIC
docker exec sonobrokers-api-dev env | findstr ASPNETCORE
```

---

**Environment Configuration Complete! 🎉**
