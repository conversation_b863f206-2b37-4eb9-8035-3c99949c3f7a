import Stripe from 'stripe'
// TODO: Migrate to .NET Web API - temporarily disabled
// import prisma from '@/lib/prisma'
import { UserService } from './userService'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-05-28.basil',
  typescript: true,
})

export class EnhancedStripeService {
  // Customer Management
  static async createOrUpdateCustomer(userId: string, customerData: {
    email: string
    name?: string
    phone?: string
    address?: any
    metadata?: Record<string, string>
  }) {
    try {
      // Check if customer already exists in our database
      const existingCustomer = await prisma.stripeCustomer.findFirst({
        where: { userId }
      })

      let stripeCustomer: Stripe.Customer

      if (existingCustomer) {
        // Update existing Stripe customer
        stripeCustomer = await stripe.customers.update(existingCustomer.stripeCustomerId, {
          email: customerData.email,
          name: customerData.name,
          phone: customerData.phone,
          address: customerData.address,
          metadata: customerData.metadata
        })

        // Update in our database
        await prisma.stripeCustomer.update({
          where: { id: existingCustomer.id },
          data: {
            email: customerData.email,
            name: customerData.name,
            phone: customerData.phone,
            address: customerData.address,
            metadata: customerData.metadata,
            updatedAt: new Date()
          }
        })
      } else {
        // Create new Stripe customer
        stripeCustomer = await stripe.customers.create({
          email: customerData.email,
          name: customerData.name,
          phone: customerData.phone,
          address: customerData.address,
          metadata: { userId, ...customerData.metadata }
        })

        // Store in our database
        await prisma.stripeCustomer.create({
          data: {
            userId,
            stripeCustomerId: stripeCustomer.id,
            email: customerData.email,
            name: customerData.name,
            phone: customerData.phone,
            address: customerData.address,
            metadata: customerData.metadata
          }
        })
      }

      return stripeCustomer
    } catch (error) {
      console.error('Error creating/updating customer:', error)
      throw error
    }
  }

  // Payment Processing
  static async createPaymentIntent(data: {
    amount: number
    currency?: string
    customerId: string
    description?: string
    metadata?: Record<string, string>
  }) {
    try {
      const paymentIntent = await stripe.paymentIntents.create({
        amount: data.amount,
        currency: data.currency || 'usd',
        customer: data.customerId,
        description: data.description,
        metadata: data.metadata || {},
        automatic_payment_methods: { enabled: true }
      })

      return paymentIntent
    } catch (error) {
      console.error('Error creating payment intent:', error)
      throw error
    }
  }

  // Subscription Management
  static async createSubscription(data: {
    customerId: string
    priceId: string
    metadata?: Record<string, string>
    trialPeriodDays?: number
  }) {
    try {
      const subscription = await stripe.subscriptions.create({
        customer: data.customerId,
        items: [{ price: data.priceId }],
        metadata: data.metadata || {},
        trial_period_days: data.trialPeriodDays,
        expand: ['latest_invoice.payment_intent']
      })

      return subscription
    } catch (error) {
      console.error('Error creating subscription:', error)
      throw error
    }
  }

  // Product and Price Management
  static async syncProductsAndPrices() {
    try {
      // Sync products
      const products = await stripe.products.list({ active: true, limit: 100 })
      
      for (const product of products.data) {
        await prisma.stripeProduct.upsert({
          where: { stripeProductId: product.id },
          update: {
            name: product.name,
            description: product.description,
            isActive: product.active,
            metadata: product.metadata,
            updatedAt: new Date()
          },
          create: {
            stripeProductId: product.id,
            name: product.name,
            description: product.description,
            isActive: product.active,
            metadata: product.metadata
          }
        })
      }

      // Sync prices
      const prices = await stripe.prices.list({ active: true, limit: 100 })
      
      for (const price of prices.data) {
        await prisma.stripePrice.upsert({
          where: { stripePriceId: price.id },
          update: {
            unitAmount: price.unit_amount || 0,
            currency: price.currency,
            recurringInterval: price.recurring?.interval,
            recurringIntervalCount: price.recurring?.interval_count,
            type: price.type,
            isActive: price.active,
            metadata: price.metadata,
            updatedAt: new Date()
          },
          create: {
            stripePriceId: price.id,
            stripeProductId: price.product as string,
            unitAmount: price.unit_amount || 0,
            currency: price.currency,
            recurringInterval: price.recurring?.interval,
            recurringIntervalCount: price.recurring?.interval_count,
            type: price.type,
            isActive: price.active,
            metadata: price.metadata
          }
        })
      }

      console.log('Products and prices synced successfully')
    } catch (error) {
      console.error('Error syncing products and prices:', error)
      throw error
    }
  }

  // Webhook Event Processing
  static async processWebhookEvent(event: Stripe.Event) {
    try {
      // Log the webhook event
      await prisma.stripeWebhookEvent.create({
        data: {
          stripeEventId: event.id,
          eventType: event.type,
          objectId: (event.data.object as any).id,
          objectType: event.data.object.object,
          livemode: event.livemode,
          apiVersion: event.api_version,
          data: event.data as any,
          processingStatus: 'pending'
        }
      })

      // Process different event types
      switch (event.type) {
        case 'customer.created':
        case 'customer.updated':
          await this.handleCustomerEvent(event)
          break
        
        case 'payment_intent.succeeded':
          await this.handlePaymentSucceeded(event)
          break
        
        case 'invoice.paid':
          await this.handleInvoicePaid(event)
          break
        
        case 'customer.subscription.created':
        case 'customer.subscription.updated':
        case 'customer.subscription.deleted':
          await this.handleSubscriptionEvent(event)
          break
        
        default:
          console.log(`Unhandled event type: ${event.type}`)
      }

      // Mark as processed
      await prisma.stripeWebhookEvent.updateMany({
        where: { stripeEventId: event.id },
        data: {
          processingStatus: 'processed',
          processedAt: new Date()
        }
      })

    } catch (error) {
      console.error('Error processing webhook event:', error)
      
      // Mark as failed
      await prisma.stripeWebhookEvent.updateMany({
        where: { stripeEventId: event.id },
        data: {
          processingStatus: 'failed',
          errorMessage: error.message,
          processedAt: new Date()
        }
      })
      
      throw error
    }
  }

  // Event Handlers
  private static async handleCustomerEvent(event: Stripe.Event) {
    const customer = event.data.object as Stripe.Customer
    
    // Find user by email
    const user = await UserService.getUserByEmail(customer.email!)
    
    if (user) {
      await prisma.stripeCustomer.upsert({
        where: { stripeCustomerId: customer.id },
        update: {
          email: customer.email!,
          name: customer.name,
          phone: customer.phone,
          address: customer.address as any,
          metadata: customer.metadata,
          updatedAt: new Date()
        },
        create: {
          userId: user.id,
          stripeCustomerId: customer.id,
          email: customer.email!,
          name: customer.name,
          phone: customer.phone,
          address: customer.address as any,
          metadata: customer.metadata
        }
      })
    }
  }

  private static async handlePaymentSucceeded(event: Stripe.Event) {
    const paymentIntent = event.data.object as Stripe.PaymentIntent
    
    // Find customer
    const stripeCustomer = await prisma.stripeCustomer.findUnique({
      where: { stripeCustomerId: paymentIntent.customer as string }
    })

    if (stripeCustomer) {
      await prisma.stripePayment.create({
        data: {
          stripePaymentIntentId: paymentIntent.id,
          stripeCustomerId: paymentIntent.customer as string,
          userId: stripeCustomer.userId,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
          status: paymentIntent.status,
          paymentMethod: paymentIntent.payment_method as string,
          description: paymentIntent.description,
          metadata: paymentIntent.metadata,
          receiptUrl: (paymentIntent as any).charges?.data?.[0]?.receipt_url || null
        }
      })
    }
  }

  private static async handleInvoicePaid(event: Stripe.Event) {
    const invoice = event.data.object as Stripe.Invoice
    
    // Find customer
    const stripeCustomer = await prisma.stripeCustomer.findUnique({
      where: { stripeCustomerId: invoice.customer as string }
    })

    if (stripeCustomer) {
      await prisma.stripeInvoice.upsert({
        where: { stripeInvoiceId: invoice.id },
        update: {
          amountPaid: invoice.amount_paid,
          amountRemaining: invoice.amount_remaining,
          status: invoice.status!,
          paidAt: invoice.status_transitions.paid_at ? new Date(invoice.status_transitions.paid_at * 1000) : null,
          hostedInvoiceUrl: invoice.hosted_invoice_url,
          invoicePdf: invoice.invoice_pdf,
          updatedAt: new Date()
        },
        create: {
          stripeInvoiceId: invoice.id,
          stripeCustomerId: invoice.customer as string,
          stripeSubscriptionId: ((invoice as any).subscription as string) || null,
          userId: stripeCustomer.userId,
          amountDue: invoice.amount_due,
          amountPaid: invoice.amount_paid,
          amountRemaining: invoice.amount_remaining,
          currency: invoice.currency,
          status: invoice.status!,
          dueDate: invoice.due_date ? new Date(invoice.due_date * 1000) : null,
          paidAt: invoice.status_transitions.paid_at ? new Date(invoice.status_transitions.paid_at * 1000) : null,
          hostedInvoiceUrl: invoice.hosted_invoice_url,
          invoicePdf: invoice.invoice_pdf,
          metadata: invoice.metadata
        }
      })
    }
  }

  private static async handleSubscriptionEvent(event: Stripe.Event) {
    const subscription = event.data.object as Stripe.Subscription
    
    // Find customer
    const stripeCustomer = await prisma.stripeCustomer.findUnique({
      where: { stripeCustomerId: subscription.customer as string }
    })

    if (stripeCustomer) {
      // Update the existing subscription in SubscriptionSnb table
      const subscriptionData = {
        stripeSubscriptionId: subscription.id,
        status: subscription.status === 'active' ? 'active' as const : 'inactive' as const,
        planType: subscription.items.data[0]?.price.metadata.plan_type || 'default',
        startsAt: new Date((subscription as any).current_period_start * 1000),
        endsAt: new Date((subscription as any).current_period_end * 1000)
      }

      if (event.type === 'customer.subscription.deleted') {
        await prisma.subscriptionSnb.updateMany({
          where: { 
            userId: stripeCustomer.userId,
            stripeSubscriptionId: subscription.id 
          },
          data: { status: 'inactive' }
        })
      } else {
        await prisma.subscriptionSnb.upsert({
          where: { 
            userId_stripeSubscriptionId: {
              userId: stripeCustomer.userId!,
              stripeSubscriptionId: subscription.id
            }
          },
          update: subscriptionData,
          create: {
            userId: stripeCustomer.userId!,
            ...subscriptionData
          }
        })
      }
    }
  }

  // Utility Methods
  static async getCustomerByUserId(userId: string) {
    return prisma.stripeCustomer.findFirst({
      where: { userId }
    })
  }

  static async getUserPayments(userId: string) {
    return prisma.stripePayment.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    })
  }

  static async getUserInvoices(userId: string) {
    return prisma.stripeInvoice.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    })
  }
}
