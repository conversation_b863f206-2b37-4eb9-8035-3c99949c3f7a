# =============================================================================
# PRODUCTION ENVIRONMENT SECRETS
# =============================================================================
# ⚠️  DO NOT COMMIT THIS FILE TO SOURCE CONTROL
# ⚠️  PRODUCTION USES AZURE KEY VAULT - THIS FILE IS FOR REFERENCE/BACKUP ONLY
# Build pipeline will replace these with Azure Key Vault references
# =============================================================================

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Single database connection using DATABASE_URL (consolidated configuration)
DATABASE_URL=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=database-url)

# =============================================================================
# API ENDPOINTS (Production URLs)
# =============================================================================
ApiEndpoints__DataApi__BaseUrl=https://api.microsaas.com/data-api/v1
ApiEndpoints__DocumentApi__BaseUrl=https://api.microsaas.com/document-api/v1
ApiEndpoints__PermissionApi__BaseUrl=https://api.microsaas.com/permission-api/v1

# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================
# Clerk Authentication (Production)
Authentication__Clerk__PublishableKey=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=clerk-publishable-key)
Authentication__Clerk__SecretKey=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=clerk-secret-key)
Authentication__Clerk__WebhookSecret=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=clerk-webhook-secret)
Authentication__Clerk__JwtIssuer=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=clerk-jwt-issuer)

# JWT Configuration (Production)
Authentication__Jwt__Issuer=https://api.microsaas.com
Authentication__Jwt__SigningKey=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=jwt-signing-key)

# =============================================================================
# EXTERNAL SERVICES (Production)
# =============================================================================
# Stripe Payment Service (Live Mode)
ExternalServices__Stripe__PublishableKey=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=stripe-publishable-key)
ExternalServices__Stripe__SecretKey=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=stripe-secret-key)
ExternalServices__Stripe__WebhookSecret=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=stripe-webhook-secret)

# Azure Services (Production)
ExternalServices__Azure__Storage__ConnectionString=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=azure-storage-connection-string)
ExternalServices__Azure__ServiceBus__ConnectionString=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=azure-servicebus-connection-string)
ExternalServices__Azure__KeyVault__Uri=https://microsaas-prod-kv.vault.azure.net/
ExternalServices__Azure__KeyVault__ClientId=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=azure-client-id)
ExternalServices__Azure__KeyVault__ClientSecret=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=azure-client-secret)
ExternalServices__Azure__KeyVault__TenantId=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=azure-tenant-id)

# Email Services (Production)
ExternalServices__Email__SendGrid__ApiKey=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=sendgrid-api-key)
ExternalServices__Email__Smtp__Host=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=smtp-host)
ExternalServices__Email__Smtp__Username=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=smtp-username)
ExternalServices__Email__Smtp__Password=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=smtp-password)
ExternalServices__Email__Resend__ApiKey=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=resend-api-key)

# =============================================================================
# EXTERNAL API INTEGRATIONS (Production)
# =============================================================================
# Make.com Integration
MAKE_ORGANIZATION_ID=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=make-organization-id)
MAKE_TEAM_ID=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=make-team-id)
MAKE_API_KEY=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=make-api-key)
MAKE_API_URL=https://us1.make.com/api/v2

# N8N Integration
N8N_API_KEY=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=n8n-api-key)
N8N_API_URL=https://n8n.microsaas.com/api/v1
N8N_WEBHOOK_URL=https://n8n.microsaas.com/webhook

# =============================================================================
# LEGACY SYSTEMS (Production)
# =============================================================================
LEGACY_DATABASE_URL=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=legacy-database-url)
WORDPRESS_DB_USER=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=wordpress-db-user)
WORDPRESS_DB_PASSWORD=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=wordpress-db-password)
WORDPRESS_DB_NAME=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=wordpress-db-name)
WP_REST_ENDPOINT=@Microsoft.KeyVault(VaultName=microsaas-prod-kv;SecretName=wp-rest-endpoint)
