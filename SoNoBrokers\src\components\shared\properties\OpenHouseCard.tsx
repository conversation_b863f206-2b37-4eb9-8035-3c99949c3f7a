'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Calendar, Clock, MapPin, Users } from 'lucide-react'
import { cn } from '@/lib/utils'

interface OpenHouseCardProps {
  openHouse: {
    id: string
    propertyId?: string
    property?: {
      id: string
      title: string
      address: string
      price?: number
      bedrooms?: number
      bathrooms?: number
      sqft?: number
      propertyType?: string
      coordinates?: {
        lat: number
        lng: number
      }
      images?: { url: string; alt?: string }[]
    }
    // Direct properties for backward compatibility
    title?: string
    address?: string
    date: string
    startTime: string
    endTime: string
    maxAttendees: number
    registeredCount: number
    description?: string
    type: 'sale' | 'rent' | 'viewing' | 'lease'
    hostName?: string
    hostPhone?: string
    hostEmail?: string
  }
  className?: string
  onRegister?: (id: string) => void
  onViewProperty?: (propertyId: string) => void
  onContactHost?: (openHouse: any) => void
  onShowRegistrations?: (id: string) => void
  isUserRegistered?: boolean
  isAuthenticated?: boolean
  currentUserId?: string
}

export function OpenHouseCard({
  openHouse,
  className,
  onRegister,
  onViewProperty,
  onContactHost,
  onShowRegistrations,
  isUserRegistered,
  isAuthenticated,
  currentUserId
}: OpenHouseCardProps) {
  // Get title and address from either nested property or direct properties
  const title = openHouse.property?.title || openHouse.title || 'Open House'
  const address = openHouse.property?.address || openHouse.address || 'Address not available'
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTime = (timeString: string) => {
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  const isUpcoming = () => {
    const now = new Date()
    const openHouseDate = new Date(`${openHouse.date}T${openHouse.startTime}`)
    return openHouseDate > now
  }

  const getAvailabilityColor = () => {
    const percentage = (openHouse.registeredCount / openHouse.maxAttendees) * 100
    if (percentage >= 90) return 'text-destructive'
    if (percentage >= 70) return 'text-primary'
    return 'text-secondary-foreground'
  }

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg font-semibold line-clamp-2">
            {title}
          </CardTitle>
          <Badge variant={isUpcoming() ? "default" : "secondary"}>
            {isUpcoming() ? 'Upcoming' : 'Past'}
          </Badge>
        </div>
        <div className="flex items-center text-sm text-muted-foreground">
          <MapPin className="w-4 h-4 mr-1" />
          {address}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="bg-muted rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-semibold text-foreground flex items-center">
              <Calendar className="w-4 h-4 mr-1" />
              Open House
            </h4>
            <Badge variant="outline">
              {openHouse.type.charAt(0).toUpperCase() + openHouse.type.slice(1)}
            </Badge>
          </div>

          <div className="text-sm text-muted-foreground mb-1">
            {formatDate(openHouse.date)}
          </div>
          <div className="flex items-center text-sm text-muted-foreground">
            <Clock className="w-4 h-4 mr-1" />
            {formatTime(openHouse.startTime)} - {formatTime(openHouse.endTime)}
          </div>

          {openHouse.description && (
            <p className="text-sm text-muted-foreground mt-2">
              {openHouse.description}
            </p>
          )}
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center text-sm">
            <Users className="w-4 h-4 mr-1" />
            <span className={getAvailabilityColor()}>
              {openHouse.registeredCount}/{openHouse.maxAttendees} registered
            </span>
          </div>

          {isUpcoming() && onRegister && (
            <Button
              size="sm"
              onClick={() => onRegister(openHouse.id)}
              disabled={openHouse.registeredCount >= openHouse.maxAttendees}
            >
              {openHouse.registeredCount >= openHouse.maxAttendees ? 'Full' : 'Register'}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
