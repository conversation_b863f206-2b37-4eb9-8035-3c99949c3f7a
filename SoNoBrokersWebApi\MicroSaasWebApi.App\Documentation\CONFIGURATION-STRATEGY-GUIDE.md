# 🔧 Configuration Strategy Guide

> **📁 Configuration Project**: All environment-specific configuration files and secrets are now organized in the separate [`MicroSaasWebApi.Config`](../../MicroSaasWebApi.Config/) project for enterprise-grade configuration management and security compliance.

## 🎯 **NEW CONFIGURATION ARCHITECTURE**

### **✅ Implemented Strategy**

- **appsettings.json** (Main Project) - Complete configuration structure with empty secret placeholders
- **MicroSaasWebApi.Config** - Environment-specific configurations and secrets
- **Build Pipeline Integration** - Automated deployment with token replacement

## 📋 Table of Contents

- [Configuration Architecture](#configuration-architecture)
- [Recommended Approach](#recommended-approach)
- [Implementation Examples](#implementation-examples)
- [Security Best Practices](#security-best-practices)
- [Environment-Specific Configuration](#environment-specific-configuration)
- [Configuration Project Structure](#configuration-project-structure)
- [Deployment Strategy](#deployment-strategy)
- [Best Practices](#best-practices)

## 🏗️ Configuration Architecture

### **Hybrid Configuration Strategy**

Based on your current setup with both `appsettings_MICROSAAS_DEV.json` and `.env` files, here's the recommended approach:

```
Configuration Priority (Highest to Lowest):
1. Azure Key Vault (Production secrets)
2. Environment Variables (Container/Azure App Service)
3. .env file (Local development only)
4. appsettings.{Environment}.json (Environment-specific)
5. appsettings.json (Base configuration)
```

### **Configuration Types**

| **Configuration Type**   | **Storage Location**     | **Use Case**                                 |
| ------------------------ | ------------------------ | -------------------------------------------- |
| **Public Settings**      | `appsettings.json`       | API endpoints, feature flags, logging levels |
| **Environment Settings** | `appsettings.{env}.json` | Environment-specific URLs, timeouts          |
| **Development Secrets**  | `.env` file              | Local development, testing                   |
| **Production Secrets**   | Azure Key Vault          | Connection strings, API keys, certificates   |
| **Tenant Settings**      | Database/Cache           | Tenant-specific configuration                |

## 🎯 Recommended Approach

### **1. Primary: appsettings.json Structure**

```json
{
  "AppInfo": {
    "Name": "MicroSaaS Web API",
    "Version": "2.0.0",
    "Environment": "Development"
  },
  "TenantSettings": {
    "DefaultConnectionString": "Server=localhost;Database=MicroSaas_Default;Integrated Security=true;",
    "EnableMultiTenantFeatures": true,
    "TenantCacheExpiry": "00:30:00",
    "MaxTenantsPerUser": 5,
    "SupportedTenantTypes": ["demo", "startup", "enterprise", "enterprise-plus"]
  },
  "ApiEndpoints": {
    "DataApi": {
      "BaseUrl": "https://api.microsaas.com/data-api/v1",
      "Timeout": "00:02:00"
    },
    "DocumentApi": {
      "BaseUrl": "https://api.microsaas.com/document-api/v1",
      "Timeout": "00:05:00"
    },
    "PermissionApi": {
      "BaseUrl": "https://api.microsaas.com/permission-api/v1",
      "Timeout": "00:01:00"
    }
  },
  "FeatureFlags": {
    "EnableSwagger": true,
    "EnableHangfire": true,
    "EnableDetailedLogging": true,
    "EnablePayments": false,
    "EnableAnalytics": true
  },
  "Authentication": {
    "Clerk": {
      "PublishableKey": "pk_test_...", // Public key, safe in appsettings
      "JwtIssuer": "https://clerk.microsaas.com",
      "JwtAudience": "microsaas-api"
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Warning"
    }
  }
}
```

### **2. Environment-Specific: appsettings.Development.json**

```json
{
  "AppInfo": {
    "Environment": "Development"
  },
  "TenantSettings": {
    "EnableMultiTenantFeatures": true,
    "TenantCacheExpiry": "00:05:00"
  },
  "ApiEndpoints": {
    "DataApi": {
      "BaseUrl": "https://dev-api.microsaas.com/data-api/v1"
    },
    "DocumentApi": {
      "BaseUrl": "https://dev-api.microsaas.com/document-api/v1"
    }
  },
  "FeatureFlags": {
    "EnableSwagger": true,
    "EnableHangfire": true,
    "EnableDetailedLogging": true
  },
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.AspNetCore": "Information"
    }
  }
}
```

### **3. Local Development: .env file**

```bash
# Local Development Environment Variables
# These override appsettings for local development only

# Database
MICROSAAS_CONNECTION_STRING=Server=localhost;Database=MicroSaas_Dev;Integrated Security=true;

# Clerk Authentication (Development)
CLERK_SECRET_KEY=sk_test_your_development_secret_key_here
CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# External APIs (Development)
STRIPE_SECRET_KEY=sk_test_your_stripe_test_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here

# Azure Services (Development)
AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=devstorage;AccountKey=...
AZURE_SERVICE_BUS_CONNECTION_STRING=Endpoint=sb://dev-servicebus.servicebus.windows.net/;...

# Email Services (Development)
SENDGRID_API_KEY=SG.your_sendgrid_api_key_here
SMTP_PASSWORD=your_smtp_password_here

# Feature Flags (Development Overrides)
ENABLE_PAYMENTS=false
ENABLE_ANALYTICS=true
ENABLE_DETAILED_LOGGING=true
```

### **4. Production: Azure Key Vault**

```csharp
// Key Vault Secret Names (Production)
microsaas-connection-string
clerk-secret-key
clerk-webhook-secret
stripe-secret-key
stripe-webhook-secret
azure-storage-connection-string
sendgrid-api-key
jwt-signing-key
encryption-key
```

## 🔧 Implementation Examples

### **1. Configuration Service**

```csharp
public interface IConfigurationService
{
    T GetValue<T>(string key, T defaultValue = default);
    string GetConnectionString(string name);
    TenantConfiguration GetTenantConfiguration(string tenantId);
    ApiEndpointConfiguration GetApiEndpoint(string apiName);
    bool IsFeatureEnabled(string featureName, string tenantId = null);
}

public class ConfigurationService : IConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly IKeyVaultService _keyVault;
    private readonly ITenantProviderService _tenantProvider;
    private readonly IMemoryCache _cache;

    public ConfigurationService(
        IConfiguration configuration,
        IKeyVaultService keyVault,
        ITenantProviderService tenantProvider,
        IMemoryCache cache)
    {
        _configuration = configuration;
        _keyVault = keyVault;
        _tenantProvider = tenantProvider;
        _cache = cache;
    }

    public T GetValue<T>(string key, T defaultValue = default)
    {
        // 1. Try environment variables first
        var envValue = Environment.GetEnvironmentVariable(key.Replace(":", "_").ToUpper());
        if (!string.IsNullOrEmpty(envValue))
        {
            return (T)Convert.ChangeType(envValue, typeof(T));
        }

        // 2. Try configuration (appsettings)
        var configValue = _configuration.GetValue<T>(key);
        if (configValue != null && !configValue.Equals(default(T)))
        {
            return configValue;
        }

        return defaultValue;
    }

    public string GetConnectionString(string name)
    {
        var tenant = _tenantProvider.GetCurrentTenant();

        // For multi-tenant, get tenant-specific connection string
        if (tenant != null)
        {
            var tenantConnectionString = GetTenantConnectionString(tenant.Id, name);
            if (!string.IsNullOrEmpty(tenantConnectionString))
            {
                return tenantConnectionString;
            }
        }

        // Fallback to default connection string
        return GetSecretValue($"ConnectionStrings:{name}") ??
               _configuration.GetConnectionString(name);
    }

    private string GetTenantConnectionString(string tenantId, string name)
    {
        var cacheKey = $"tenant-connection-{tenantId}-{name}";

        return _cache.GetOrCreate(cacheKey, factory =>
        {
            factory.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30);

            // Try Key Vault first for production
            var secretName = $"tenant-{tenantId}-{name}-connection-string";
            var keyVaultValue = GetSecretValue(secretName);
            if (!string.IsNullOrEmpty(keyVaultValue))
            {
                return keyVaultValue;
            }

            // Fallback to configuration
            return _configuration.GetValue<string>($"Tenants:{tenantId}:ConnectionStrings:{name}");
        });
    }

    private string GetSecretValue(string secretName)
    {
        try
        {
            return _keyVault?.GetSecretAsync(secretName).Result;
        }
        catch
        {
            // Key Vault not available (development environment)
            return null;
        }
    }
}
```

### **2. Strongly-Typed Configuration Classes**

```csharp
public class TenantSettings
{
    public const string SectionName = "TenantSettings";

    public string DefaultConnectionString { get; set; } = string.Empty;
    public bool EnableMultiTenantFeatures { get; set; } = true;
    public TimeSpan TenantCacheExpiry { get; set; } = TimeSpan.FromMinutes(30);
    public int MaxTenantsPerUser { get; set; } = 5;
    public List<string> SupportedTenantTypes { get; set; } = new();
}

public class ApiEndpointConfiguration
{
    public string BaseUrl { get; set; } = string.Empty;
    public TimeSpan Timeout { get; set; } = TimeSpan.FromMinutes(2);
    public int RetryCount { get; set; } = 3;
    public bool EnableCircuitBreaker { get; set; } = true;
}

public class FeatureFlags
{
    public const string SectionName = "FeatureFlags";

    public bool EnableSwagger { get; set; } = false;
    public bool EnableHangfire { get; set; } = false;
    public bool EnableDetailedLogging { get; set; } = false;
    public bool EnablePayments { get; set; } = false;
    public bool EnableAnalytics { get; set; } = true;
}
```

### **3. Configuration Registration**

```csharp
public static class ConfigurationExtensions
{
    public static IServiceCollection AddConfigurationServices(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Register strongly-typed configurations
        services.Configure<TenantSettings>(
            configuration.GetSection(TenantSettings.SectionName));

        services.Configure<FeatureFlags>(
            configuration.GetSection(FeatureFlags.SectionName));

        // Register API endpoint configurations
        services.Configure<Dictionary<string, ApiEndpointConfiguration>>(
            configuration.GetSection("ApiEndpoints"));

        // Register configuration service
        services.AddScoped<IConfigurationService, ConfigurationService>();

        // Add Key Vault if in production
        if (!configuration.GetValue<bool>("Development"))
        {
            services.AddScoped<IKeyVaultService, KeyVaultService>();

            var keyVaultUrl = configuration["KeyVault:Url"];
            if (!string.IsNullOrEmpty(keyVaultUrl))
            {
                configuration.AddAzureKeyVault(keyVaultUrl);
            }
        }

        return services;
    }
}
```

## 🔒 Security Best Practices

### **1. Secret Management**

```csharp
// ❌ DON'T: Store secrets in appsettings.json
{
  "ConnectionStrings": {
    "Default": "Server=prod;Database=MicroSaas;User=sa;Password=MyPassword123;"
  }
}

// ✅ DO: Use placeholders in appsettings.json
{
  "ConnectionStrings": {
    "Default": "#{ConnectionString}#" // Replaced by deployment pipeline
  }
}

// ✅ DO: Use Key Vault references in Azure App Service
{
  "ConnectionStrings": {
    "Default": "@Microsoft.KeyVault(VaultName=microsaas-kv;SecretName=connection-string)"
  }
}
```

### **2. Environment Variable Naming**

```bash
# ✅ Consistent naming convention
MICROSAAS_CONNECTION_STRING
MICROSAAS_CLERK_SECRET_KEY
MICROSAAS_STRIPE_SECRET_KEY
MICROSAAS_AZURE_STORAGE_CONNECTION_STRING

# ✅ Tenant-specific secrets
MICROSAAS_TENANT_CLIENTA_CONNECTION_STRING
MICROSAAS_TENANT_CLIENTB_CONNECTION_STRING
```

### **3. Configuration Validation**

```csharp
public class TenantSettingsValidator : IValidateOptions<TenantSettings>
{
    public ValidateOptionsResult Validate(string name, TenantSettings options)
    {
        var errors = new List<string>();

        if (string.IsNullOrEmpty(options.DefaultConnectionString))
        {
            errors.Add("DefaultConnectionString is required");
        }

        if (options.MaxTenantsPerUser <= 0)
        {
            errors.Add("MaxTenantsPerUser must be greater than 0");
        }

        if (options.TenantCacheExpiry <= TimeSpan.Zero)
        {
            errors.Add("TenantCacheExpiry must be greater than zero");
        }

        return errors.Count > 0
            ? ValidateOptionsResult.Fail(errors)
            : ValidateOptionsResult.Success;
    }
}

// Registration
services.AddSingleton<IValidateOptions<TenantSettings>, TenantSettingsValidator>();
```

## 🌍 Environment-Specific Configuration

### **Development Environment**

```json
{
  "TenantSettings": {
    "EnableMultiTenantFeatures": true,
    "TenantCacheExpiry": "00:05:00"
  },
  "FeatureFlags": {
    "EnableSwagger": true,
    "EnableHangfire": true,
    "EnableDetailedLogging": true,
    "EnablePayments": false
  },
  "Logging": {
    "LogLevel": {
      "Default": "Debug"
    }
  }
}
```

### **UAT Environment**

```json
{
  "TenantSettings": {
    "EnableMultiTenantFeatures": true,
    "TenantCacheExpiry": "00:15:00"
  },
  "FeatureFlags": {
    "EnableSwagger": true,
    "EnableHangfire": false,
    "EnableDetailedLogging": false,
    "EnablePayments": true
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information"
    }
  }
}
```

### **Production Environment**

```json
{
  "TenantSettings": {
    "EnableMultiTenantFeatures": true,
    "TenantCacheExpiry": "00:30:00"
  },
  "FeatureFlags": {
    "EnableSwagger": false,
    "EnableHangfire": false,
    "EnableDetailedLogging": false,
    "EnablePayments": true
  },
  "Logging": {
    "LogLevel": {
      "Default": "Warning"
    }
  }
}
```

## 🔄 Migration from Legacy

### **Step 1: Create New Configuration Structure**

```csharp
// Create modern configuration classes
public class ModernTenantConfiguration
{
    public string TenantId { get; set; } = string.Empty;
    public string TenantName { get; set; } = string.Empty;
    public Guid TenantGuid { get; set; }
    public TenantAuthenticationSettings Authentication { get; set; } = new();
    public TenantEmailSettings Email { get; set; } = new();
    public TenantDocumentSettings Documents { get; set; } = new();
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}
```

### **Step 2: Configuration Migration Service**

```csharp
public class ConfigurationMigrationService
{
    public ModernTenantConfiguration MigrateLegacyConfiguration(LegacyTenantConfiguration legacy)
    {
        return new ModernTenantConfiguration
        {
            TenantId = legacy.TenantId,
            TenantName = legacy.TenantName,
            TenantGuid = legacy.TenantGuid,
            Authentication = new TenantAuthenticationSettings
            {
                ClerkPublishableKey = legacy.Settings.AzureAdB2C.ClientId,
                JwtIssuer = legacy.Settings.AzureAdB2C.Issuer,
                // Map other authentication settings
            },
            Email = new TenantEmailSettings
            {
                FromAddress = legacy.Settings.Emails.SendGridEmailFrom,
                FromName = legacy.Settings.Emails.SendGridEmailFromName,
                // Map other email settings
            }
            // Continue mapping...
        };
    }
}
```

### **Step 3: Gradual Migration**

```csharp
public class HybridConfigurationService : IConfigurationService
{
    private readonly ILegacyConfigurationService _legacyService;
    private readonly IModernConfigurationService _modernService;
    private readonly IFeatureFlags _featureFlags;

    public T GetValue<T>(string key, T defaultValue = default)
    {
        // Check if modern configuration is enabled for this tenant
        if (_featureFlags.IsEnabled("UseModernConfiguration"))
        {
            return _modernService.GetValue(key, defaultValue);
        }

        // Fallback to legacy configuration
        return _legacyService.GetValue(key, defaultValue);
    }
}
```

## 📋 Summary

### **✅ Recommended Configuration Strategy:**

1. **Primary**: Use `appsettings.json` for structured, strongly-typed configuration
2. **Environment-specific**: Use `appsettings.{Environment}.json` for environment overrides
3. **Local development**: Use `.env` file for local secrets and overrides
4. **Production secrets**: Use Azure Key Vault for sensitive data
5. **Tenant-specific**: Store in database with caching for performance

### **✅ Benefits:**

- **Type safety** with strongly-typed configuration classes
- **Environment isolation** with proper secret management
- **Developer productivity** with local .env file support
- **Production security** with Azure Key Vault integration
- **Multi-tenant support** with tenant-specific configurations
- **Validation** with built-in configuration validation
- **Caching** for performance optimization

This approach provides the best of both worlds: the structure and type safety of appsettings.json with the flexibility and security of environment variables and Key Vault for secrets.

## 📁 Configuration Project Structure

All configuration files, API collections, and environment templates are now organized in the separate [`MicroSaasWebApi-Config`](../MicroSaasWebApi-Config/) project:

```
MicroSaasWebApi-Config/
├── README.md                                    # Configuration project documentation
├── Postman/                                     # API testing collections
│   └── MicroSaaS-API-Collection.postman_collection.json
├── Environments/                                # Environment configurations
│   ├── development.json                        # Development environment config
│   ├── production.json                         # Production environment config
│   ├── Development.postman_environment.json    # Postman dev environment
│   └── Production.postman_environment.json     # Postman prod environment
└── Templates/                                   # Configuration templates
    └── api-endpoints.json                      # Complete API endpoint definitions
```

### **🎯 Benefits of Dedicated Configuration Project**

#### **✅ Centralized Management**

- **Single Source of Truth**: All configuration files in one location
- **Consistent Structure**: Standardized configuration format across environments
- **Easy Maintenance**: Update configuration without touching application code
- **Clear Ownership**: Configuration team can manage independently

#### **✅ Enhanced Development Workflow**

- **Version Control**: Track configuration changes with proper git history
- **Environment Parity**: Ensure consistency across dev, UAT, and production
- **Team Collaboration**: Multiple teams can work on configuration simultaneously
- **Rollback Capability**: Easy to revert configuration changes

#### **✅ Production-Ready Features**

- **Environment Isolation**: Separate configurations prevent cross-environment issues
- **Security Management**: Proper separation of public and secret configuration
- **Deployment Integration**: Ready for CI/CD pipeline integration
- **Monitoring Support**: Configuration for health checks and monitoring

### **Environment-Specific Configurations**

#### **Development Environment** ([`development.json`](../MicroSaasWebApi-Config/Environments/development.json))

- **Features**: Swagger enabled, detailed logging, Hangfire dashboard
- **Rate Limits**: Relaxed for development (2000 requests/minute)
- **Security**: CORS enabled for localhost
- **Caching**: Memory cache with 5-minute expiry
- **Logging**: Debug level with detailed information

#### **Production Environment** ([`production.json`](../MicroSaasWebApi-Config/Environments/production.json))

- **Features**: Minimal logging, no debug tools
- **Rate Limits**: Strict production limits (500 requests/minute)
- **Security**: HTTPS only, restricted CORS
- **Caching**: Redis with 30-minute expiry
- **Logging**: Warning level only

### **API Testing Collections**

The configuration project includes comprehensive Postman collections:

#### **[MicroSaaS API Collection](../MicroSaasWebApi-Config/Postman/MicroSaaS-API-Collection.postman_collection.json)**

- **Multi-Tenant Management** endpoints
- **Authentication & Authorization** endpoints
- **Data API** endpoints (profiles, securities, transactions)
- **Document API** endpoints (upload, download, search)
- **Permission API** endpoints (users, roles, permissions)
- **Workflow API** endpoints (workflows, instances)
- **Health & Monitoring** endpoints

#### **Environment Files**

- **[Development Environment](../MicroSaasWebApi-Config/Environments/Development.postman_environment.json)** - For local testing
- **[Production Environment](../MicroSaasWebApi-Config/Environments/Production.postman_environment.json)** - For production testing

### **Getting Started with Configuration Project**

1. **Import Postman Collection**: Import the collection from `MicroSaasWebApi-Config/Postman/`
2. **Import Environment**: Choose development or production environment
3. **Configure Variables**: Set `base_url`, `tenant_id`, and `auth_token`
4. **Test Endpoints**: Run requests to validate API functionality
5. **Environment Templates**: Use JSON templates for deployment configuration

## 🚀 Deployment Strategy

### **🏗️ CI/CD Pipeline Integration**

The configuration project is designed to integrate seamlessly with modern CI/CD pipelines:

#### **Azure DevOps Pipeline Example**

```yaml
# azure-pipelines.yml
trigger:
  branches:
    include:
      - main
  paths:
    include:
      - MicroSaasWebApi-Config/*

variables:
  configProjectPath: "MicroSaasWebApi-Config"

stages:
  - stage: ValidateConfiguration
    displayName: "Validate Configuration"
    jobs:
      - job: ValidateConfig
        displayName: "Validate Configuration Files"
        steps:
          - task: PowerShell@2
            displayName: "Validate JSON Configuration"
            inputs:
              targetType: "inline"
              script: |
                # Validate all JSON files in configuration project
                Get-ChildItem -Path "$(configProjectPath)" -Filter "*.json" -Recurse | ForEach-Object {
                  Write-Host "Validating: $($_.FullName)"
                  Get-Content $_.FullName | ConvertFrom-Json | Out-Null
                  Write-Host "✅ Valid JSON: $($_.Name)"
                }

  - stage: DeployDevelopment
    displayName: "Deploy to Development"
    dependsOn: ValidateConfiguration
    condition: succeeded()
    jobs:
      - deployment: DeployDev
        displayName: "Deploy Development Configuration"
        environment: "development"
        strategy:
          runOnce:
            deploy:
              steps:
                - task: FileTransform@1
                  displayName: "Transform Configuration"
                  inputs:
                    folderPath: "$(Pipeline.Workspace)"
                    fileType: "json"
                    targetFiles: "**/appsettings.json"

                - task: AzureKeyVault@2
                  displayName: "Get Secrets from Key Vault"
                  inputs:
                    azureSubscription: "$(azureSubscription)"
                    KeyVaultName: "$(keyVaultName)"
                    SecretsFilter: "*"
                    RunAsPreJob: false

  - stage: DeployProduction
    displayName: "Deploy to Production"
    dependsOn: DeployDevelopment
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - deployment: DeployProd
        displayName: "Deploy Production Configuration"
        environment: "production"
        strategy:
          runOnce:
            deploy:
              steps:
                - task: AzureAppServiceSettings@1
                  displayName: "Update App Service Configuration"
                  inputs:
                    azureSubscription: "$(azureSubscription)"
                    appName: "$(appServiceName)"
                    resourceGroupName: "$(resourceGroupName)"
                    appSettings: |
                      ASPNETCORE_ENVIRONMENT=Production
                      KeyVault__Url=$(keyVaultUrl)
                      TenantSettings__DefaultTenantId=$(defaultTenantId)
```

#### **GitHub Actions Example**

```yaml
# .github/workflows/deploy-config.yml
name: Deploy Configuration

on:
  push:
    branches: [main]
    paths: ["MicroSaasWebApi-Config/**"]
  pull_request:
    branches: [main]
    paths: ["MicroSaasWebApi-Config/**"]

jobs:
  validate-config:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Validate JSON Configuration
        run: |
          find MicroSaasWebApi-Config -name "*.json" -exec echo "Validating {}" \; -exec jq empty {} \;

      - name: Test Postman Collection
        uses: matt-ball/newman-action@master
        with:
          collection: MicroSaasWebApi-Config/Postman/MicroSaaS-API-Collection.postman_collection.json
          environment: MicroSaasWebApi-Config/Environments/Development.postman_environment.json
          reporters: cli

  deploy-development:
    needs: validate-config
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: development
    steps:
      - uses: actions/checkout@v3

      - name: Deploy to Development
        uses: azure/webapps-deploy@v2
        with:
          app-name: ${{ secrets.AZURE_WEBAPP_NAME_DEV }}
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE_DEV }}
          package: .

  deploy-production:
    needs: deploy-development
    runs-on: ubuntu-latest
    environment: production
    steps:
      - uses: actions/checkout@v3

      - name: Deploy to Production
        uses: azure/webapps-deploy@v2
        with:
          app-name: ${{ secrets.AZURE_WEBAPP_NAME_PROD }}
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE_PROD }}
          package: .
```

### **🌍 Environment-Specific Deployment**

#### **Development Environment Deployment**

```bash
# Local Development Setup
# 1. Copy environment template
cp MicroSaasWebApi-Config/Environments/development.json MicroSaasWebApi.Full/appsettings.Development.json

# 2. Create local .env file
cp MicroSaasWebApi.Full/.env.example MicroSaasWebApi.Full/.env

# 3. Update with local values
# Edit .env file with your local database, API keys, etc.

# 4. Run application
dotnet run --project MicroSaasWebApi.Full --environment Development
```

#### **UAT Environment Deployment**

```bash
# UAT Deployment Script
#!/bin/bash

# Set environment variables
export ASPNETCORE_ENVIRONMENT=UAT
export MICROSAAS_CONNECTION_STRING="${UAT_CONNECTION_STRING}"
export CLERK_SECRET_KEY="${UAT_CLERK_SECRET_KEY}"
export SENDGRID_API_KEY="${UAT_SENDGRID_API_KEY}"

# Deploy configuration
az webapp config appsettings set \
  --resource-group "${RESOURCE_GROUP}" \
  --name "${UAT_APP_NAME}" \
  --settings @MicroSaasWebApi-Config/Environments/uat.json

# Deploy application
az webapp deployment source config-zip \
  --resource-group "${RESOURCE_GROUP}" \
  --name "${UAT_APP_NAME}" \
  --src "${BUILD_ARTIFACT_PATH}"
```

#### **Production Environment Deployment**

```bash
# Production Deployment with Blue-Green Strategy
#!/bin/bash

# 1. Deploy to staging slot
az webapp deployment slot create \
  --resource-group "${RESOURCE_GROUP}" \
  --name "${PROD_APP_NAME}" \
  --slot staging

# 2. Configure staging slot with production settings
az webapp config appsettings set \
  --resource-group "${RESOURCE_GROUP}" \
  --name "${PROD_APP_NAME}" \
  --slot staging \
  --settings @MicroSaasWebApi-Config/Environments/production.json

# 3. Deploy application to staging
az webapp deployment source config-zip \
  --resource-group "${RESOURCE_GROUP}" \
  --name "${PROD_APP_NAME}" \
  --slot staging \
  --src "${BUILD_ARTIFACT_PATH}"

# 4. Run health checks on staging
curl -f "https://${PROD_APP_NAME}-staging.azurewebsites.net/health" || exit 1

# 5. Swap staging to production
az webapp deployment slot swap \
  --resource-group "${RESOURCE_GROUP}" \
  --name "${PROD_APP_NAME}" \
  --slot staging \
  --target-slot production
```

### **🔐 Secret Management in Deployment**

#### **Azure Key Vault Integration**

```bash
# Create Key Vault secrets from configuration
az keyvault secret set \
  --vault-name "${KEY_VAULT_NAME}" \
  --name "clerk-secret-key" \
  --value "${CLERK_SECRET_KEY}"

az keyvault secret set \
  --vault-name "${KEY_VAULT_NAME}" \
  --name "default-connection-string" \
  --value "${CONNECTION_STRING}"

az keyvault secret set \
  --vault-name "${KEY_VAULT_NAME}" \
  --name "sendgrid-api-key" \
  --value "${SENDGRID_API_KEY}"
```

#### **Environment Variable Replacement**

```bash
# Replace configuration placeholders in CI/CD
envsubst < MicroSaasWebApi-Config/Environments/production.json > appsettings.Production.json

# Or use PowerShell for Windows environments
(Get-Content MicroSaasWebApi-Config/Environments/production.json) `
  -replace '#{DefaultConnectionString}#', $env:CONNECTION_STRING `
  -replace '#{ClerkSecretKey}#', $env:CLERK_SECRET_KEY `
  | Set-Content appsettings.Production.json
```

### **📊 Multi-Tenant Deployment Strategy**

#### **Tenant-Specific Configuration**

```bash
# Deploy configuration for specific tenant
TENANT_ID="client-a"
TENANT_CONFIG="MicroSaasWebApi-Config/Environments/tenants/${TENANT_ID}.json"

# Create tenant-specific app service
az webapp create \
  --resource-group "${RESOURCE_GROUP}" \
  --plan "${APP_SERVICE_PLAN}" \
  --name "${TENANT_ID}-api" \
  --runtime "DOTNETCORE|8.0"

# Configure tenant-specific settings
az webapp config appsettings set \
  --resource-group "${RESOURCE_GROUP}" \
  --name "${TENANT_ID}-api" \
  --settings @"${TENANT_CONFIG}"
```

#### **Tenant Database Deployment**

```bash
# Create tenant-specific database
az sql db create \
  --resource-group "${RESOURCE_GROUP}" \
  --server "${SQL_SERVER_NAME}" \
  --name "microsaas_${TENANT_ID}" \
  --service-objective S1

# Update connection string for tenant
TENANT_CONNECTION_STRING="Server=${SQL_SERVER_NAME}.database.windows.net;Database=microsaas_${TENANT_ID};..."

az webapp config connection-string set \
  --resource-group "${RESOURCE_GROUP}" \
  --name "${TENANT_ID}-api" \
  --connection-string-type SQLAzure \
  --settings DefaultConnection="${TENANT_CONNECTION_STRING}"
```

## 🎯 Best Practices

### **📋 Configuration Management Best Practices**

#### **✅ Security Best Practices**

```bash
# 1. Never commit secrets to source control
echo "*.env" >> .gitignore
echo "appsettings.*.json" >> .gitignore  # Only if they contain secrets

# 2. Use different Key Vaults per environment
DEV_KEY_VAULT="microsaas-dev-kv"
UAT_KEY_VAULT="microsaas-uat-kv"
PROD_KEY_VAULT="microsaas-prod-kv"

# 3. Rotate secrets regularly
az keyvault secret set \
  --vault-name "${PROD_KEY_VAULT}" \
  --name "clerk-secret-key" \
  --value "${NEW_CLERK_SECRET_KEY}" \
  --expires "2024-12-31T23:59:59Z"

# 4. Use managed identities in Azure
az webapp identity assign \
  --resource-group "${RESOURCE_GROUP}" \
  --name "${APP_NAME}"
```

#### **✅ Environment Consistency**

```json
// Use consistent naming across environments
{
  "TenantSettings": {
    "DefaultTenantId": "#{DefaultTenantId}#", // Placeholder for replacement
    "EnableMultiTenantFeatures": true, // Same across environments
    "TenantCacheExpiry": "#{TenantCacheExpiry}#" // Environment-specific
  },
  "FeatureFlags": {
    "EnableSwagger": "#{EnableSwagger}#", // Environment-specific
    "EnablePayments": true, // Same across environments
    "EnableAnalytics": true // Same across environments
  }
}
```

#### **✅ Configuration Validation**

```csharp
// Add configuration validation in Startup
public void ConfigureServices(IServiceCollection services)
{
    // Validate configuration at startup
    services.AddOptions<TenantSettings>()
        .Bind(Configuration.GetSection("TenantSettings"))
        .ValidateDataAnnotations()
        .ValidateOnStart();

    services.AddOptions<FeatureFlags>()
        .Bind(Configuration.GetSection("FeatureFlags"))
        .ValidateDataAnnotations()
        .ValidateOnStart();
}

// Configuration model with validation
public class TenantSettings
{
    [Required]
    [MinLength(1)]
    public string DefaultTenantId { get; set; } = string.Empty;

    [Required]
    public bool EnableMultiTenantFeatures { get; set; }

    [Range(typeof(TimeSpan), "00:01:00", "24:00:00")]
    public TimeSpan TenantCacheExpiry { get; set; } = TimeSpan.FromMinutes(30);
}
```

### **🔄 Deployment Best Practices**

#### **✅ Blue-Green Deployment Pattern**

```bash
# 1. Deploy to staging slot
az webapp deployment slot create \
  --resource-group "${RESOURCE_GROUP}" \
  --name "${APP_NAME}" \
  --slot staging

# 2. Test staging thoroughly
newman run MicroSaasWebApi-Config/Postman/MicroSaaS-API-Collection.postman_collection.json \
  --environment MicroSaasWebApi-Config/Environments/Production.postman_environment.json \
  --env-var "base_url=https://${APP_NAME}-staging.azurewebsites.net"

# 3. Swap only if tests pass
if [ $? -eq 0 ]; then
  az webapp deployment slot swap \
    --resource-group "${RESOURCE_GROUP}" \
    --name "${APP_NAME}" \
    --slot staging \
    --target-slot production
else
  echo "Tests failed, not swapping slots"
  exit 1
fi
```

#### **✅ Configuration Drift Detection**

```bash
# Monitor configuration drift
#!/bin/bash

# Get current app service configuration
az webapp config appsettings list \
  --resource-group "${RESOURCE_GROUP}" \
  --name "${APP_NAME}" \
  --output json > current-config.json

# Compare with expected configuration
diff -u MicroSaasWebApi-Config/Environments/production.json current-config.json

# Alert if differences found
if [ $? -ne 0 ]; then
  echo "⚠️ Configuration drift detected!"
  # Send alert to monitoring system
fi
```

#### **✅ Rollback Strategy**

```bash
# Quick rollback script
#!/bin/bash

ROLLBACK_VERSION="${1:-previous}"

# Rollback to previous deployment slot
az webapp deployment slot swap \
  --resource-group "${RESOURCE_GROUP}" \
  --name "${APP_NAME}" \
  --slot production \
  --target-slot staging

# Or rollback to specific version
az webapp deployment source config-zip \
  --resource-group "${RESOURCE_GROUP}" \
  --name "${APP_NAME}" \
  --src "artifacts/${ROLLBACK_VERSION}.zip"
```

### **🧪 Testing Best Practices**

#### **✅ Automated Configuration Testing**

```bash
# Test configuration in CI/CD pipeline
#!/bin/bash

# 1. Validate JSON syntax
find MicroSaasWebApi-Config -name "*.json" -exec jq empty {} \;

# 2. Test Postman collection against each environment
for env in development production; do
  echo "Testing ${env} environment..."
  newman run \
    MicroSaasWebApi-Config/Postman/MicroSaaS-API-Collection.postman_collection.json \
    --environment "MicroSaasWebApi-Config/Environments/${env}.postman_environment.json" \
    --reporters cli,json \
    --reporter-json-export "test-results-${env}.json"
done

# 3. Validate configuration schema
ajv validate \
  --schema configuration-schema.json \
  --data MicroSaasWebApi-Config/Environments/*.json
```

#### **✅ Environment Smoke Tests**

```csharp
// Automated smoke tests for each environment
[Test]
public async Task SmokeTest_HealthEndpoint_ShouldReturnHealthy()
{
    var client = new HttpClient();
    var response = await client.GetAsync($"{BaseUrl}/health");

    response.StatusCode.Should().Be(HttpStatusCode.OK);

    var content = await response.Content.ReadAsStringAsync();
    content.Should().Contain("Healthy");
}

[Test]
public async Task SmokeTest_ConfigurationEndpoint_ShouldReturnTenantConfig()
{
    var client = new HttpClient();
    client.DefaultRequestHeaders.Add("X-Tenant-Id", TenantId);
    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AuthToken);

    var response = await client.GetAsync($"{BaseUrl}/api/configuration/tenant");

    response.StatusCode.Should().Be(HttpStatusCode.OK);

    var config = await response.Content.ReadFromJsonAsync<TenantConfiguration>();
    config.TenantId.Should().Be(TenantId);
}
```

### **📊 Monitoring and Observability**

#### **✅ Configuration Monitoring**

```csharp
// Monitor configuration changes
public class ConfigurationMonitoringService : IHostedService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ConfigurationMonitoringService> _logger;
    private readonly IChangeToken _changeToken;

    public ConfigurationMonitoringService(
        IConfiguration configuration,
        ILogger<ConfigurationMonitoringService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _changeToken = configuration.GetReloadToken();
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        _changeToken.RegisterChangeCallback(OnConfigurationChanged, null);
        return Task.CompletedTask;
    }

    private void OnConfigurationChanged(object state)
    {
        _logger.LogWarning("Configuration changed at {Timestamp}", DateTime.UtcNow);

        // Log specific configuration values that changed
        var tenantSettings = _configuration.GetSection("TenantSettings");
        _logger.LogInformation("Current tenant settings: {@TenantSettings}", tenantSettings.Get<TenantSettings>());

        // Register for next change
        _configuration.GetReloadToken().RegisterChangeCallback(OnConfigurationChanged, null);
    }

    public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
}
```

#### **✅ Health Checks for Configuration**

```csharp
// Add configuration health checks
public void ConfigureServices(IServiceCollection services)
{
    services.AddHealthChecks()
        .AddCheck<ConfigurationHealthCheck>("configuration")
        .AddCheck<DatabaseConnectionHealthCheck>("database")
        .AddCheck<ExternalApiHealthCheck>("external-apis");
}

public class ConfigurationHealthCheck : IHealthCheck
{
    private readonly IConfigurationService _configService;

    public ConfigurationHealthCheck(IConfigurationService configService)
    {
        _configService = configService;
    }

    public Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate critical configuration values
            var connectionString = _configService.GetConnectionString("DefaultConnection");
            if (string.IsNullOrEmpty(connectionString))
            {
                return Task.FromResult(HealthCheckResult.Unhealthy("Database connection string not configured"));
            }

            var tenantConfig = _configService.GetTenantConfiguration("default");
            if (tenantConfig == null)
            {
                return Task.FromResult(HealthCheckResult.Unhealthy("Default tenant configuration not found"));
            }

            return Task.FromResult(HealthCheckResult.Healthy("Configuration is valid"));
        }
        catch (Exception ex)
        {
            return Task.FromResult(HealthCheckResult.Unhealthy("Configuration validation failed", ex));
        }
    }
}
```

### **📚 Documentation Best Practices**

#### **✅ Configuration Documentation**

```markdown
# Configuration Documentation Template

## Environment: Production

### Required Environment Variables

| Variable                      | Description                 | Example       | Required |
| ----------------------------- | --------------------------- | ------------- | -------- |
| `CLERK_SECRET_KEY`            | Clerk authentication secret | `sk_live_...` | Yes      |
| `MICROSAAS_CONNECTION_STRING` | Database connection         | `Server=...`  | Yes      |
| `SENDGRID_API_KEY`            | Email service API key       | `SG.xxx`      | Yes      |

### Feature Flags

| Flag             | Description               | Default | Environment Override |
| ---------------- | ------------------------- | ------- | -------------------- |
| `EnableSwagger`  | Enable Swagger UI         | `false` | `ENABLE_SWAGGER`     |
| `EnablePayments` | Enable payment processing | `true`  | `ENABLE_PAYMENTS`    |

### API Endpoints

| Service      | Base URL                                    | Timeout   | Retry Count |
| ------------ | ------------------------------------------- | --------- | ----------- |
| Data API     | `https://api.microsaas.com/data-api/v1`     | 2 minutes | 3           |
| Document API | `https://api.microsaas.com/document-api/v1` | 5 minutes | 3           |
```

This comprehensive configuration strategy provides a solid foundation for managing configuration in a scalable, secure, and maintainable way across all environments! 🚀
