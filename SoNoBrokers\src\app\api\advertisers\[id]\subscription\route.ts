import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { AdvertiserService } from '@/services/advertiserService'
import { stripe } from '@/constants/stripe'

interface RouteParams {
  params: Promise<{ id: string }>
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const resolvedParams = await params
    const { plan, stripePriceId, successUrl, cancelUrl } = await request.json()
    
    // Check if advertiser exists and user owns it
    const advertiser = await AdvertiserService.getAdvertiser(resolvedParams.id)
    if (!advertiser) {
      return NextResponse.json({ error: 'Advertiser not found' }, { status: 404 })
    }

    // TODO: Add proper authorization check
    // if (advertiser.userId !== userId && !isAdmin) {
    //   return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    // }

    // Create Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      mode: 'subscription',
      payment_method_types: ['card'],
      line_items: [
        {
          price: stripePriceId,
          quantity: 1,
        },
      ],
      metadata: {
        advertiserId: resolvedParams.id,
        plan,
        userId
      },
      success_url: successUrl,
      cancel_url: cancelUrl,
      customer_email: advertiser.email,
    })

    return NextResponse.json({ 
      sessionId: session.id,
      url: session.url 
    })
  } catch (error) {
    console.error('Error creating subscription:', error)
    return NextResponse.json(
      { error: 'Failed to create subscription' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const resolvedParams = await params
    const { action, stripeSubscriptionId } = await request.json()
    
    // Check if advertiser exists and user owns it
    const advertiser = await AdvertiserService.getAdvertiser(resolvedParams.id)
    if (!advertiser) {
      return NextResponse.json({ error: 'Advertiser not found' }, { status: 404 })
    }

    // TODO: Add proper authorization check
    // if (advertiser.userId !== userId && !isAdmin) {
    //   return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    // }

    if (action === 'cancel') {
      // Cancel the subscription at period end
      await stripe.subscriptions.update(stripeSubscriptionId, {
        cancel_at_period_end: true,
      })

      // Update in database
      await AdvertiserService.updateSubscriptionStatus(
        stripeSubscriptionId,
        'cancelled'
      )

      return NextResponse.json({ message: 'Subscription cancelled successfully' })
    }

    if (action === 'reactivate') {
      // Reactivate the subscription
      await stripe.subscriptions.update(stripeSubscriptionId, {
        cancel_at_period_end: false,
      })

      // Update in database
      await AdvertiserService.updateSubscriptionStatus(
        stripeSubscriptionId,
        'active'
      )

      return NextResponse.json({ message: 'Subscription reactivated successfully' })
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
  } catch (error) {
    console.error('Error updating subscription:', error)
    return NextResponse.json(
      { error: 'Failed to update subscription' },
      { status: 500 }
    )
  }
}
