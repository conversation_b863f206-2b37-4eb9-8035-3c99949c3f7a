# =============================================================================
# SoNoBrokers .NET Web API - Development Override
# Use: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
# =============================================================================

version: '3.8'

services:
  # ===========================================================================
  # Development API with Hot Reload
  # ===========================================================================
  api:
    build:
      target: development
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - DOTNET_USE_POLLING_FILE_WATCHER=true
      - DOTNET_WATCH_RESTART_ON_RUDE_EDIT=true
    volumes:
      - .:/src:cached
      - api-dev-packages:/root/.nuget/packages
    command: ["dotnet", "watch", "run", "--project", "MicroSaasWebApi.App", "--urls", "http://0.0.0.0:8080"]
    stdin_open: true
    tty: true
    ports:
      - "${API_PORT:-8080}:8080"

# =============================================================================
# Development Volumes
# =============================================================================
volumes:
  api-dev-packages:
    driver: local
