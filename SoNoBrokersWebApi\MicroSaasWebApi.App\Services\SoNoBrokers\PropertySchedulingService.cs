using Dapper;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using System.Data;

namespace MicroSaasWebApi.Services.SoNoBrokers
{
    /// <summary>
    /// Service for managing property visit scheduling and verification
    /// </summary>
    public class PropertySchedulingService : IPropertySchedulingService
    {
        private readonly MicroSaasWebApi.App.Context.IDapperDbContext _context;
        private readonly ILogger<PropertySchedulingService> _logger;
        private readonly ICalendarInviteService _calendarService;
        private readonly IQrCodeService _qrCodeService;

        public PropertySchedulingService(
            IDapperDbContext context,
            ILogger<PropertySchedulingService> logger,
            ICalendarInviteService calendarService,
            IQrCodeService qrCodeService)
        {
            _context = context;
            _logger = logger;
            _calendarService = calendarService;
            _qrCodeService = qrCodeService;
        }

        #region Seller Availability Management

        public async Task<SellerAvailabilityResponse> CreateSellerAvailabilityAsync(string sellerId, CreateSellerAvailabilityRequest request)
        {
            try
            {
                using var connection = _context.CreateConnection();
                using var transaction = connection.BeginTransaction();

                var availabilityId = Guid.NewGuid().ToString();

                var insertQuery = @"
                    INSERT INTO ""SellerAvailability"" 
                    (""id"", ""propertyId"", ""sellerId"", ""dayOfWeek"", ""startTime"", ""endTime"", ""isAvailable"", ""notes"", ""createdAt"", ""updatedAt"")
                    VALUES (@Id, @PropertyId, @SellerId, @DayOfWeek, @StartTime, @EndTime, @IsAvailable, @Notes, @CreatedAt, @UpdatedAt)";

                await connection.ExecuteAsync(insertQuery, new
                {
                    Id = availabilityId,
                    PropertyId = request.PropertyId,
                    SellerId = sellerId,
                    DayOfWeek = (int)request.DayOfWeek,
                    StartTime = request.StartTime,
                    EndTime = request.EndTime,
                    IsAvailable = request.IsAvailable,
                    Notes = request.Notes,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }, transaction);

                transaction.Commit();

                // Return the created availability
                var availability = await GetSellerAvailabilityByIdAsync(availabilityId);
                return availability!;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create seller availability for seller {SellerId}", sellerId);
                throw;
            }
        }

        public async Task<List<SellerAvailabilityResponse>> GetSellerAvailabilityAsync(string sellerId, string? propertyId = null)
        {
            try
            {
                using var connection = _context.CreateConnection();

                var query = @"
                    SELECT sa.*, p.""title"" as PropertyTitle
                    FROM ""SellerAvailability"" sa
                    LEFT JOIN ""Property"" p ON sa.""propertyId"" = p.""id""
                    WHERE sa.""sellerId"" = @SellerId";

                var parameters = new DynamicParameters();
                parameters.Add("SellerId", sellerId);

                if (!string.IsNullOrEmpty(propertyId))
                {
                    query += " AND sa.\"propertyId\" = @PropertyId";
                    parameters.Add("PropertyId", propertyId);
                }

                query += " ORDER BY sa.\"dayOfWeek\", sa.\"startTime\"";

                var results = await connection.QueryAsync(query, parameters);

                return results.Select(MapToSellerAvailabilityResponse).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get seller availability for seller {SellerId}", sellerId);
                throw;
            }
        }

        public async Task<SellerAvailabilityResponse?> GetSellerAvailabilityByIdAsync(string availabilityId)
        {
            try
            {
                using var connection = _context.CreateConnection();

                var query = @"
                    SELECT sa.*, p.""title"" as PropertyTitle
                    FROM ""SellerAvailability"" sa
                    LEFT JOIN ""Property"" p ON sa.""propertyId"" = p.""id""
                    WHERE sa.""id"" = @AvailabilityId";

                var result = await connection.QuerySingleOrDefaultAsync(query, new { AvailabilityId = availabilityId });

                return result != null ? MapToSellerAvailabilityResponse(result) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get seller availability {AvailabilityId}", availabilityId);
                throw;
            }
        }

        public async Task<SellerAvailabilityResponse?> UpdateSellerAvailabilityAsync(string availabilityId, UpdateSellerAvailabilityRequest request)
        {
            try
            {
                using var connection = _context.CreateConnection();
                using var transaction = connection.BeginTransaction();

                var updateFields = new List<string>();
                var parameters = new DynamicParameters();
                parameters.Add("AvailabilityId", availabilityId);
                parameters.Add("UpdatedAt", DateTime.UtcNow);

                if (request.StartTime.HasValue)
                {
                    updateFields.Add("\"startTime\" = @StartTime");
                    parameters.Add("StartTime", request.StartTime.Value);
                }

                if (request.EndTime.HasValue)
                {
                    updateFields.Add("\"endTime\" = @EndTime");
                    parameters.Add("EndTime", request.EndTime.Value);
                }

                if (request.IsAvailable.HasValue)
                {
                    updateFields.Add("\"isAvailable\" = @IsAvailable");
                    parameters.Add("IsAvailable", request.IsAvailable.Value);
                }

                if (request.Notes != null)
                {
                    updateFields.Add("\"notes\" = @Notes");
                    parameters.Add("Notes", request.Notes);
                }

                if (updateFields.Count == 0)
                {
                    return await GetSellerAvailabilityByIdAsync(availabilityId);
                }

                updateFields.Add("\"updatedAt\" = @UpdatedAt");

                var updateQuery = $@"
                    UPDATE ""SellerAvailability""
                    SET {string.Join(", ", updateFields)}
                    WHERE ""id"" = @AvailabilityId";

                var rowsAffected = await connection.ExecuteAsync(updateQuery, parameters, transaction);

                if (rowsAffected == 0)
                {
                    transaction.Rollback();
                    return null;
                }

                transaction.Commit();

                return await GetSellerAvailabilityByIdAsync(availabilityId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update seller availability {AvailabilityId}", availabilityId);
                throw;
            }
        }

        public async Task<bool> DeleteSellerAvailabilityAsync(string availabilityId)
        {
            try
            {
                using var connection = _context.CreateConnection();

                var deleteQuery = @"DELETE FROM ""SellerAvailability"" WHERE ""id"" = @AvailabilityId";

                var rowsAffected = await connection.ExecuteAsync(deleteQuery, new { AvailabilityId = availabilityId });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete seller availability {AvailabilityId}", availabilityId);
                throw;
            }
        }

        public async Task<List<SellerAvailabilityResponse>> GetPropertyAvailabilityAsync(string propertyId)
        {
            try
            {
                using var connection = _context.CreateConnection();

                var query = @"
                    SELECT sa.*, p.""title"" as PropertyTitle
                    FROM ""SellerAvailability"" sa
                    LEFT JOIN ""Property"" p ON sa.""propertyId"" = p.""id""
                    WHERE sa.""propertyId"" = @PropertyId AND sa.""isAvailable"" = true
                    ORDER BY sa.""dayOfWeek"", sa.""startTime""";

                var results = await connection.QueryAsync(query, new { PropertyId = propertyId });

                return results.Select(MapToSellerAvailabilityResponse).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get property availability for property {PropertyId}", propertyId);
                throw;
            }
        }

        #endregion

        #region Visit Scheduling

        public async Task<PropertyVisitScheduleResponse> CreateVisitScheduleAsync(string buyerId, CreateVisitScheduleRequest request)
        {
            try
            {
                using var connection = _context.CreateConnection();
                using var transaction = connection.BeginTransaction();

                var visitId = Guid.NewGuid().ToString();

                var insertQuery = @"
                    INSERT INTO ""PropertyVisitSchedule"" 
                    (""id"", ""propertyId"", ""buyerId"", ""sellerId"", ""contactShareId"", ""requestedDate"", ""requestedTime"", 
                     ""status"", ""visitType"", ""buyerNotes"", ""createdAt"", ""updatedAt"")
                    VALUES (@Id, @PropertyId, @BuyerId, @SellerId, @ContactShareId, @RequestedDate, @RequestedTime, 
                            @Status, @VisitType, @BuyerNotes, @CreatedAt, @UpdatedAt)";

                await connection.ExecuteAsync(insertQuery, new
                {
                    Id = visitId,
                    PropertyId = request.PropertyId,
                    BuyerId = buyerId,
                    SellerId = request.SellerId,
                    ContactShareId = request.ContactShareId,
                    RequestedDate = request.RequestedDate.Date,
                    RequestedTime = request.RequestedTime,
                    Status = (int)VisitStatus.Pending,
                    VisitType = (int)request.VisitType,
                    BuyerNotes = request.BuyerNotes,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }, transaction);

                transaction.Commit();

                // Send notification to seller
                await SendVisitRequestNotificationAsync(visitId);

                // Return the created visit schedule
                var visit = await GetVisitScheduleAsync(visitId);
                return visit!;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create visit schedule for buyer {BuyerId}", buyerId);
                throw;
            }
        }

        public async Task<PropertyVisitScheduleResponse?> GetVisitScheduleAsync(string visitId)
        {
            try
            {
                using var connection = _context.CreateConnection();

                var query = @"
                    SELECT vs.*, 
                           p.""title"" as PropertyTitle, p.""address"" as PropertyAddress,
                           bu.""fullName"" as BuyerName, bu.""email"" as BuyerEmail,
                           su.""fullName"" as SellerName
                    FROM ""PropertyVisitSchedule"" vs
                    LEFT JOIN ""Property"" p ON vs.""propertyId"" = p.""id""
                    LEFT JOIN ""User"" bu ON vs.""buyerId"" = bu.""id""
                    LEFT JOIN ""User"" su ON vs.""sellerId"" = su.""id""
                    WHERE vs.""id"" = @VisitId";

                var result = await connection.QuerySingleOrDefaultAsync(query, new { VisitId = visitId });

                return result != null ? MapToPropertyVisitScheduleResponse(result) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get visit schedule {VisitId}", visitId);
                throw;
            }
        }

        // Placeholder implementations for remaining methods
        public Task<VisitScheduleSearchResponse> GetVisitSchedulesAsync(string userId, VisitScheduleSearchParams searchParams)
        {
            // Implementation placeholder
            return Task.FromResult(new VisitScheduleSearchResponse());
        }

        public Task<VisitScheduleSearchResponse> GetPropertyVisitSchedulesAsync(string propertyId, VisitScheduleSearchParams searchParams)
        {
            // Implementation placeholder
            return Task.FromResult(new VisitScheduleSearchResponse());
        }

        public Task<PropertyVisitScheduleResponse?> RespondToVisitRequestAsync(string visitId, RespondToVisitRequest request)
        {
            // Implementation placeholder
            return Task.FromResult<PropertyVisitScheduleResponse?>(null);
        }

        public Task<bool> CancelVisitScheduleAsync(string visitId, string reason)
        {
            // Implementation placeholder
            return Task.FromResult(false);
        }

        public Task<bool> MarkVisitCompletedAsync(string visitId)
        {
            // Implementation placeholder
            return Task.FromResult(false);
        }

        #endregion

        #region QR Code Management

        public Task<PropertyQrCodeResponse> GeneratePropertyQrCodeAsync(string propertyId, string sellerId)
        {
            // Implementation placeholder
            return Task.FromResult(new PropertyQrCodeResponse());
        }

        public Task<PropertyQrCodeResponse?> GetPropertyQrCodeAsync(string propertyId)
        {
            // Implementation placeholder
            return Task.FromResult<PropertyQrCodeResponse?>(null);
        }

        public Task<bool> RegeneratePropertyQrCodeAsync(string propertyId, string sellerId)
        {
            // Implementation placeholder
            return Task.FromResult(false);
        }

        public Task<bool> DeactivatePropertyQrCodeAsync(string propertyId, string sellerId)
        {
            // Implementation placeholder
            return Task.FromResult(false);
        }

        #endregion

        #region Visit Verification

        public Task<VisitVerificationResponse> VerifyVisitAsync(string visitId, VerifyVisitRequest request)
        {
            // Implementation placeholder
            return Task.FromResult(new VisitVerificationResponse());
        }

        public Task<List<VisitVerificationResponse>> GetVisitVerificationsAsync(string visitId)
        {
            // Implementation placeholder
            return Task.FromResult(new List<VisitVerificationResponse>());
        }

        public Task<bool> IsQrCodeValidAsync(string qrCodeData)
        {
            // Implementation placeholder
            return Task.FromResult(false);
        }

        #endregion

        #region Calendar Integration

        public Task<bool> SendCalendarInviteAsync(string visitId)
        {
            // Implementation placeholder
            return Task.FromResult(false);
        }

        public Task<bool> UpdateCalendarInviteAsync(string visitId)
        {
            // Implementation placeholder
            return Task.FromResult(false);
        }

        public Task<bool> CancelCalendarInviteAsync(string visitId)
        {
            // Implementation placeholder
            return Task.FromResult(false);
        }

        #endregion

        #region Access Control and Notifications

        public Task<bool> CanUserAccessVisitScheduleAsync(string userId, string visitId)
        {
            // Implementation placeholder
            return Task.FromResult(false);
        }

        public Task<bool> CanUserManagePropertySchedulingAsync(string userId, string propertyId)
        {
            // Implementation placeholder
            return Task.FromResult(false);
        }

        public Task<VisitSchedulingStats> GetVisitSchedulingStatsAsync(string userId)
        {
            // Implementation placeholder
            return Task.FromResult(new VisitSchedulingStats());
        }

        public Task<VisitSchedulingStats> GetPropertyVisitStatsAsync(string propertyId)
        {
            // Implementation placeholder
            return Task.FromResult(new VisitSchedulingStats());
        }

        public Task<VisitSchedulingStats> GetAdminVisitStatsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            // Implementation placeholder
            return Task.FromResult(new VisitSchedulingStats());
        }

        public Task<bool> SendVisitRequestNotificationAsync(string visitId)
        {
            // Implementation placeholder
            return Task.FromResult(false);
        }

        public Task<bool> SendVisitConfirmationNotificationAsync(string visitId)
        {
            // Implementation placeholder
            return Task.FromResult(false);
        }

        public Task<bool> SendVisitReminderNotificationAsync(string visitId)
        {
            // Implementation placeholder
            return Task.FromResult(false);
        }

        public Task<bool> SendVisitCancellationNotificationAsync(string visitId, string reason)
        {
            // Implementation placeholder
            return Task.FromResult(false);
        }

        public Task<bool> SetWeeklyAvailabilityAsync(string sellerId, string propertyId, List<CreateSellerAvailabilityRequest> availability)
        {
            // Implementation placeholder
            return Task.FromResult(false);
        }

        public Task<List<PropertyVisitScheduleResponse>> GetUpcomingVisitsAsync(string userId, int days = 7)
        {
            // Implementation placeholder
            return Task.FromResult(new List<PropertyVisitScheduleResponse>());
        }

        public Task<bool> SendDailyVisitRemindersAsync()
        {
            // Implementation placeholder
            return Task.FromResult(false);
        }

        #endregion

        #region Helper Methods

        private SellerAvailabilityResponse MapToSellerAvailabilityResponse(dynamic result)
        {
            var dayOfWeek = (DayOfWeek)result.dayOfWeek;
            var startTime = (TimeSpan)result.startTime;
            var endTime = (TimeSpan)result.endTime;

            return new SellerAvailabilityResponse
            {
                Id = result.id,
                PropertyId = result.propertyId,
                PropertyTitle = result.PropertyTitle ?? "",
                DayOfWeek = dayOfWeek,
                DayOfWeekDisplay = dayOfWeek.ToString(),
                StartTime = startTime,
                EndTime = endTime,
                TimeRange = $"{startTime:hh\\:mm} - {endTime:hh\\:mm}",
                IsAvailable = result.isAvailable,
                Notes = result.notes,
                CreatedAt = result.createdAt,
                UpdatedAt = result.updatedAt
            };
        }

        private PropertyVisitScheduleResponse MapToPropertyVisitScheduleResponse(dynamic result)
        {
            var status = (VisitStatus)result.status;
            var visitType = (VisitType)result.visitType;
            var requestedDate = (DateTime)result.requestedDate;
            var requestedTime = (TimeSpan)result.requestedTime;

            return new PropertyVisitScheduleResponse
            {
                Id = result.id,
                PropertyId = result.propertyId,
                PropertyTitle = result.PropertyTitle ?? "",
                PropertyAddress = result.PropertyAddress ?? "",
                BuyerId = result.buyerId,
                BuyerName = result.BuyerName ?? "",
                BuyerEmail = result.BuyerEmail ?? "",
                SellerId = result.sellerId,
                SellerName = result.SellerName ?? "",
                ContactShareId = result.contactShareId,
                RequestedDate = requestedDate,
                RequestedTime = requestedTime,
                RequestedDateTime = $"{requestedDate:yyyy-MM-dd} {requestedTime:hh\\:mm}",
                ConfirmedDate = result.confirmedDate,
                ConfirmedTime = result.confirmedTime,
                ConfirmedDateTime = result.confirmedDate != null && result.confirmedTime != null
                    ? $"{((DateTime)result.confirmedDate):yyyy-MM-dd} {((TimeSpan)result.confirmedTime):hh\\:mm}"
                    : null,
                Status = status,
                StatusDisplay = GetStatusDisplay(status),
                VisitType = visitType,
                VisitTypeDisplay = GetVisitTypeDisplay(visitType),
                BuyerNotes = result.buyerNotes,
                SellerNotes = result.sellerNotes,
                SellerResponse = result.sellerResponse,
                QrCode = result.qrCode,
                QrCodeGenerated = result.qrCodeGenerated,
                VisitVerified = result.visitVerified,
                VisitVerifiedAt = result.visitVerifiedAt,
                CalendarInviteSent = result.calendarInviteSent,
                CreatedAt = result.createdAt,
                RespondedAt = result.respondedAt
            };
        }

        private string GetStatusDisplay(VisitStatus status)
        {
            return status switch
            {
                VisitStatus.Pending => "Pending",
                VisitStatus.Confirmed => "Confirmed",
                VisitStatus.Rescheduled => "Rescheduled",
                VisitStatus.Cancelled => "Cancelled",
                VisitStatus.Completed => "Completed",
                VisitStatus.NoShow => "No Show",
                VisitStatus.Expired => "Expired",
                _ => "Unknown"
            };
        }

        private string GetVisitTypeDisplay(VisitType visitType)
        {
            return visitType switch
            {
                VisitType.InPerson => "In-Person",
                VisitType.Virtual => "Virtual",
                VisitType.SelfGuided => "Self-Guided",
                _ => "Unknown"
            };
        }

        #endregion
    }
}
