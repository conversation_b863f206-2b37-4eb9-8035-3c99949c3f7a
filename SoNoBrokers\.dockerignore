# =============================================================================
# SoNoBrokers React App Docker Ignore File
# Excludes unnecessary files from Docker build context
# =============================================================================

# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
.next/
out/
dist/
build/

# Environment files (security)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
docs/
memory-bank/

# Testing
coverage/
.nyc_output
.jest/

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Prisma
prisma/migrations/

# Temporary files
tmp/
temp/

# Docker
Dockerfile*
docker-compose*
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
azure-pipelines.yml

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.rar

# Large media files (if any)
*.mp4
*.avi
*.mov
*.wmv

# Development tools
.eslintrc*
.prettierrc*
.editorconfig
tsconfig.json
next.config.js
tailwind.config.js
postcss.config.js

# Scripts (development only)
scripts/
terminal-command.sh
test-db-connection.js

# Chains and automation
chains/

# Translation files (keep only compiled)
src/locales/
public/locales/

# Archive folders
src/components/_archive/
