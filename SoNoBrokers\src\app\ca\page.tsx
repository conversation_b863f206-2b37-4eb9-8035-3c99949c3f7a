import { CanadaLandingClient } from './CanadaLandingClient'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'SoNoBrokers Canada | For Sale By Owner Real Estate Platform',
  description: 'Sell your property directly to buyers in Canada with SoNoBrokers. Save on commissions, access professional services, and connect with verified buyers across Canadian markets.',
  keywords: 'for sale by owner Canada, FSBO Canada, real estate Canada, property sale Canada, no commission real estate',
  openGraph: {
    title: 'SoNoBrokers Canada | For Sale By Owner Real Estate',
    description: 'The leading FSBO platform in Canada. Sell directly, save commissions, access professional services.',
    url: 'https://www.sonobrokers.com/ca',
    siteName: 'SoNoBrokers',
    locale: 'en_CA',
    type: 'website',
  },
}

export default function CanadaLandingPage() {
  return (
    <div>
      <CanadaLandingClient />
    </div>
  )
}
