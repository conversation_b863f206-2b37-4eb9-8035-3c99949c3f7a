'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { MapPin, Phone, Mail, Star, Clock, DollarSign } from 'lucide-react'

interface Lawyer {
  id: string
  name: string
  firm: string
  specialties: string[]
  rating: number
  reviews: number
  hourlyRate: string
  location: string
  phone: string
  email: string
  experience: string
  languages: string[]
  availability: string
}

interface CALegalServicesClientProps {
  userType: 'buyer' | 'seller'
}

export function CALegalServicesClient({ userType }: CALegalServicesClientProps) {
  const [selectedSpecialty, setSelectedSpecialty] = useState<string>('all')

  // Sample Canadian lawyers data
  const lawyers: Lawyer[] = [
    {
      id: '1',
      name: '<PERSON>',
      firm: 'Mitchell & Associates Law',
      specialties: ['Real Estate Law', 'Property Transactions', 'Commercial Real Estate'],
      rating: 4.9,
      reviews: 127,
      hourlyRate: '$350-450',
      location: 'Toronto, ON',
      phone: '+****************',
      email: '<EMAIL>',
      experience: '15+ years',
      languages: ['English', 'French'],
      availability: 'Available this week'
    },
    {
      id: '2',
      name: 'David Chen',
      firm: 'Chen Legal Group',
      specialties: ['Residential Real Estate', 'Condo Law', 'First-Time Buyers'],
      rating: 4.8,
      reviews: 89,
      hourlyRate: '$275-350',
      location: 'Vancouver, BC',
      phone: '+****************',
      email: '<EMAIL>',
      experience: '12+ years',
      languages: ['English', 'Mandarin', 'Cantonese'],
      availability: 'Available next week'
    },
    {
      id: '3',
      name: 'Marie Dubois',
      firm: 'Dubois Avocats',
      specialties: ['Real Estate Law', 'Property Development', 'Investment Properties'],
      rating: 4.7,
      reviews: 156,
      hourlyRate: '$300-400',
      location: 'Montreal, QC',
      phone: '+****************',
      email: '<EMAIL>',
      experience: '18+ years',
      languages: ['French', 'English'],
      availability: 'Available today'
    }
  ]

  const specialties = ['all', 'Real Estate Law', 'Property Transactions', 'Commercial Real Estate', 'Residential Real Estate', 'Condo Law']

  const filteredLawyers = selectedSpecialty === 'all' 
    ? lawyers 
    : lawyers.filter(lawyer => lawyer.specialties.includes(selectedSpecialty))

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">
          Legal Services in Canada
        </h1>
        <p className="text-muted-foreground text-lg">
          {userType === 'buyer' 
            ? 'Find experienced real estate lawyers to help with your property purchase in Canada.'
            : 'Connect with legal professionals specializing in property sales and transactions.'
          }
        </p>
      </div>

      {/* Specialty Filter */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Filter by Specialty</h3>
        <div className="flex flex-wrap gap-2">
          {specialties.map((specialty) => (
            <Button
              key={specialty}
              variant={selectedSpecialty === specialty ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedSpecialty(specialty)}
              className="capitalize"
            >
              {specialty === 'all' ? 'All Specialties' : specialty}
            </Button>
          ))}
        </div>
      </div>

      {/* Lawyers Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredLawyers.map((lawyer) => (
          <Card key={lawyer.id} className="h-full">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-xl">{lawyer.name}</CardTitle>
                  <CardDescription className="font-medium">{lawyer.firm}</CardDescription>
                </div>
                <Badge variant="secondary" className="text-green-600">
                  <Clock className="w-3 h-3 mr-1" />
                  {lawyer.availability}
                </Badge>
              </div>
              
              <div className="flex items-center gap-2">
                <div className="flex items-center">
                  <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  <span className="font-medium ml-1">{lawyer.rating}</span>
                </div>
                <span className="text-muted-foreground">({lawyer.reviews} reviews)</span>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Specialties</h4>
                <div className="flex flex-wrap gap-1">
                  {lawyer.specialties.map((specialty) => (
                    <Badge key={specialty} variant="outline" className="text-xs">
                      {specialty}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-muted-foreground" />
                  <span>{lawyer.hourlyRate}/hour</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-muted-foreground" />
                  <span>{lawyer.location}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-muted-foreground" />
                  <span>{lawyer.phone}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-muted-foreground" />
                  <span className="truncate">{lawyer.email}</span>
                </div>
              </div>

              <div>
                <p className="text-sm text-muted-foreground">
                  <strong>Experience:</strong> {lawyer.experience}
                </p>
                <p className="text-sm text-muted-foreground">
                  <strong>Languages:</strong> {lawyer.languages.join(', ')}
                </p>
              </div>

              <div className="flex gap-2 pt-4">
                <Button className="flex-1" size="sm">
                  Contact Lawyer
                </Button>
                <Button variant="outline" size="sm">
                  View Profile
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredLawyers.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No lawyers found for the selected specialty.</p>
          <Button 
            variant="outline" 
            onClick={() => setSelectedSpecialty('all')}
            className="mt-4"
          >
            Show All Lawyers
          </Button>
        </div>
      )}
    </div>
  )
}
