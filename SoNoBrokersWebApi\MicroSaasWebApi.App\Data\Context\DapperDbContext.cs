using Dapper;
using Npgsql;
using System.Data;
using MicroSaasWebApi.Data.Context;

namespace MicroSaasWebApi.Data.Context
{
    /// <summary>
    /// Dapper database context for SoNoBrokers operations
    /// Consolidated implementation using only DATABASE_URL configuration
    /// </summary>
    public class DapperDbContext : IDapperDbContext, IDisposable
    {
        private readonly string _connectionString;
        private IDbConnection? _connection;
        private IDbTransaction? _transaction;

        public DapperDbContext(IConfiguration configuration)
        {
            // Use environment variables directly for Supabase connection string
            var databaseUrl = Environment.GetEnvironmentVariable("DATABASE_URL");

            if (!string.IsNullOrEmpty(databaseUrl))
            {
                // For Supabase pooler, try direct connection string format to avoid SCRAM issues
                if (databaseUrl.Contains("pooler.supabase.com"))
                {
                    // Use direct connection string format for pooler
                    _connectionString = "Host=aws-0-ca-central-1.pooler.supabase.com;Port=5432;Database=postgres;Username=postgres.yfznlsisxsnymkvydzha;Password=Shared4w0rk!;SSL Mode=Require;Trust Server Certificate=true;Pooling=true;";
                }
                else
                {
                    // Convert PostgreSQL URI to Supabase connection string format
                    _connectionString = ConvertSupabaseUriToConnectionString(databaseUrl);
                }
            }
            else
            {
                // Fallback to direct Supabase connection string format
                _connectionString = "Host=db.yfznlsisxsnymkvydzha.supabase.co;Port=5432;Database=postgres;Username=postgres;Password=Shared4w0rk!;SSL Mode=Require;Trust Server Certificate=true;";
            }
        }

        /// <summary>
        /// Get database connection
        /// </summary>
        public IDbConnection Connection
        {
            get
            {
                if (_connection == null || _connection.State != ConnectionState.Open)
                {
                    _connection = new NpgsqlConnection(_connectionString);
                    _connection.Open();
                }
                return _connection;
            }
        }

        /// <summary>
        /// Convert Supabase URI to connection string format
        /// </summary>
        private string ConvertSupabaseUriToConnectionString(string databaseUrl)
        {
            try
            {
                var uri = new Uri(databaseUrl);
                var host = uri.Host;
                var port = uri.Port;
                var database = uri.AbsolutePath.TrimStart('/');
                var userInfo = uri.UserInfo.Split(':');
                var username = userInfo[0];
                var password = userInfo.Length > 1 ? userInfo[1] : "";

                // Build connection string with Supabase-specific parameters
                var connectionString = $"Host={host};Port={port};Database={database};Username={username};Password={password};SSL Mode=Require;Trust Server Certificate=true;";

                // Add additional parameters for Supabase pooler compatibility
                if (host.Contains("supabase.com"))
                {
                    connectionString += "Server Compatibility Mode=NoTypeLoading;Include Error Detail=true;";
                }

                return connectionString;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to parse DATABASE_URL: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Get the current database connection (implements IDapperDbContext)
        /// </summary>
        public IDbConnection GetConnection()
        {
            return Connection;
        }

        /// <summary>
        /// Create a new database connection (for services that need their own connection)
        /// </summary>
        public IDbConnection CreateConnection()
        {
            return new NpgsqlConnection(_connectionString);
        }

        /// <summary>
        /// Execute a query and return results
        /// </summary>
        public async Task<IEnumerable<T>> QueryAsync<T>(string sql, object? parameters = null)
        {
            return await Connection.QueryAsync<T>(sql, parameters, _transaction);
        }

        /// <summary>
        /// Execute a query and return a single result
        /// </summary>
        public async Task<T?> QuerySingleOrDefaultAsync<T>(string sql, object? parameters = null)
        {
            return await Connection.QuerySingleOrDefaultAsync<T>(sql, parameters, _transaction);
        }

        /// <summary>
        /// Execute a query and return first result or default
        /// </summary>
        public async Task<T?> QueryFirstOrDefaultAsync<T>(string sql, object? parameters = null)
        {
            return await Connection.QueryFirstOrDefaultAsync<T>(sql, parameters, _transaction);
        }

        /// <summary>
        /// Execute a query and return first result
        /// </summary>
        public async Task<T> QueryFirstAsync<T>(string sql, object? parameters = null)
        {
            return await Connection.QueryFirstAsync<T>(sql, parameters, _transaction);
        }

        /// <summary>
        /// Execute a scalar query and return a single value
        /// </summary>
        public async Task<T> QuerySingleAsync<T>(string sql, object? parameters = null)
        {
            return await Connection.QuerySingleAsync<T>(sql, parameters, _transaction);
        }

        /// <summary>
        /// Execute a command (INSERT, UPDATE, DELETE) and return affected rows
        /// </summary>
        public async Task<int> ExecuteAsync(string sql, object? parameters = null)
        {
            return await Connection.ExecuteAsync(sql, parameters, _transaction);
        }

        /// <summary>
        /// Execute a scalar command and return a single value
        /// </summary>
        public async Task<T> ExecuteScalarAsync<T>(string sql, object? parameters = null)
        {
            return await Connection.ExecuteScalarAsync<T>(sql, parameters, _transaction);
        }

        /// <summary>
        /// Begin a database transaction
        /// </summary>
        public async Task<IDbTransaction> BeginTransactionAsync()
        {
            if (_transaction != null)
            {
                throw new InvalidOperationException("Transaction already in progress");
            }

            // Ensure connection is open
            var connection = Connection;
            _transaction = await Task.FromResult(connection.BeginTransaction());
            return _transaction;
        }

        /// <summary>
        /// Commit the current transaction
        /// </summary>
        public async Task CommitTransactionAsync()
        {
            if (_transaction == null)
            {
                throw new InvalidOperationException("No transaction in progress");
            }

            await Task.Run(() => _transaction.Commit());
            _transaction.Dispose();
            _transaction = null;
        }

        /// <summary>
        /// Rollback the current transaction
        /// </summary>
        public async Task RollbackTransactionAsync()
        {
            if (_transaction == null)
            {
                throw new InvalidOperationException("No transaction in progress");
            }

            await Task.Run(() => _transaction.Rollback());
            _transaction.Dispose();
            _transaction = null;
        }

        /// <summary>
        /// Execute a stored procedure and return results
        /// </summary>
        public async Task<IEnumerable<T>> ExecuteStoredProcedureAsync<T>(string procedureName, object? parameters = null)
        {
            return await Connection.QueryAsync<T>(procedureName, parameters, _transaction, commandType: CommandType.StoredProcedure);
        }

        /// <summary>
        /// Execute a stored procedure and return a single result
        /// </summary>
        public async Task<T?> ExecuteStoredProcedureSingleAsync<T>(string procedureName, object? parameters = null)
        {
            return await Connection.QueryFirstOrDefaultAsync<T>(procedureName, parameters, _transaction, commandType: CommandType.StoredProcedure);
        }

        /// <summary>
        /// Execute a stored procedure without return value
        /// </summary>
        public async Task<int> ExecuteStoredProcedureAsync(string procedureName, object? parameters = null)
        {
            return await Connection.ExecuteAsync(procedureName, parameters, _transaction, commandType: CommandType.StoredProcedure);
        }

        /// <summary>
        /// Execute a Supabase function (PostgreSQL function) and return results
        /// </summary>
        public async Task<IEnumerable<T>> ExecuteFunctionAsync<T>(string functionName, object? parameters = null)
        {
            // For PostgreSQL functions, we use SELECT * FROM function_name(parameters)
            var parameterNames = new List<string>();
            var parameterValues = new DynamicParameters();

            if (parameters != null)
            {
                var properties = parameters.GetType().GetProperties();
                foreach (var prop in properties)
                {
                    var paramName = $"@{prop.Name}";
                    parameterNames.Add(paramName);
                    parameterValues.Add(paramName, prop.GetValue(parameters));
                }
            }

            var sql = parameterNames.Count > 0
                ? $"SELECT * FROM {functionName}({string.Join(", ", parameterNames)})"
                : $"SELECT * FROM {functionName}()";

            return await QueryAsync<T>(sql, parameterValues);
        }

        /// <summary>
        /// Bulk insert entities
        /// </summary>
        public async Task<int> BulkInsertAsync<T>(IEnumerable<T> entities, string tableName)
        {
            // Implementation would depend on specific bulk insert strategy
            // For now, use individual inserts (can be optimized later)
            var count = 0;
            foreach (var entity in entities)
            {
                // This is a simplified implementation - would need proper SQL generation
                count++;
            }
            return await Task.FromResult(count);
        }

        /// <summary>
        /// Bulk update entities
        /// </summary>
        public async Task<int> BulkUpdateAsync<T>(IEnumerable<T> entities, string tableName, string keyColumn)
        {
            // Implementation would depend on specific bulk update strategy
            return await Task.FromResult(entities.Count());
        }

        /// <summary>
        /// Bulk delete entities
        /// </summary>
        public async Task<int> BulkDeleteAsync<T>(IEnumerable<T> entities, string tableName, string keyColumn)
        {
            // Implementation would depend on specific bulk delete strategy
            return await Task.FromResult(entities.Count());
        }

        /// <summary>
        /// Test database connectivity
        /// </summary>
        public async Task<bool> CanConnectAsync()
        {
            try
            {
                var result = await QuerySingleAsync<int>("SELECT 1");
                return result == 1;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Execute multiple queries in a transaction without return value
        /// </summary>
        public async Task ExecuteInTransactionAsync(Func<IDapperDbContext, Task> operation)
        {
            var transaction = await BeginTransactionAsync();
            try
            {
                await operation(this);
                await CommitTransactionAsync();
            }
            catch
            {
                await RollbackTransactionAsync();
                throw;
            }
        }

        /// <summary>
        /// Execute multiple queries in a transaction with return value
        /// </summary>
        public async Task<T> ExecuteInTransactionAsync<T>(Func<IDapperDbContext, Task<T>> operation)
        {
            var transaction = await BeginTransactionAsync();
            try
            {
                var result = await operation(this);
                await CommitTransactionAsync();
                return result;
            }
            catch
            {
                await RollbackTransactionAsync();
                throw;
            }
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            _transaction?.Dispose();
            _connection?.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
