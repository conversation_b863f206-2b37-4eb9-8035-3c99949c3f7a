using Hangfire.Dashboard;

namespace MicroSaasWebApi.Filters
{
    /// <summary>
    /// Authorization filter for Hangfire dashboard
    /// Only allows access in development and staging environments
    /// </summary>
    public class HangfireAuthorizationFilter : IDashboardAuthorizationFilter
    {
        public bool Authorize(DashboardContext context)
        {
            var httpContext = context.GetHttpContext();

            // Only allow access in development and staging environments
            var environment = httpContext.RequestServices.GetRequiredService<IWebHostEnvironment>();

            return environment.IsDevelopment() || environment.IsStaging();
        }
    }
}
