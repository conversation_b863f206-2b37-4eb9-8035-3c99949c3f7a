import { ConfigProps } from '@/types'

const config: ConfigProps = {
	// REQUIRED
	appName: 'SoNoBrokers',
	// REQUIRED: a short description of your app for SEO tags (can be overwritten)
	appDescription: 'Commission-free real estate platform for direct property sales',
	// REQUIRED (no https://, not trialing slash at the end, just the naked domain)
	domainName: process.env.NEXT_PUBLIC_DOMAIN_NAME || 'https://www.sonobrokers.com',
	stripe: {
		// Create multiple products in your Stripe dashboard, then add them here. You can add as many plans as you want, just make sure to add the priceId
		products: [
			{
				type: 'one-time', // one-time, subscription
				title: 'your-title',
				productId: 'your-product-id',
				subtitle: 'Per month price',
				price: 25,
				isBest: true,
				linkTitle: 'PAY MOTHERFUCKER',
				featuresTitle: 'Features',
				priceId: 'your-price-id',
				features: [
					{
						title: 'Feature 1',
						disabled: false,
					},
					{
						title: 'Feature 2',
						disabled: true,
					},
				],
			},
			{
				type: 'subscription',
				period: 'year',
				productId: 'your-product-id',
				title: 'Year',
				subtitle: 'Per year price',
				price: 25,
				linkTitle: 'PAY MOTHERFUCKER YEAR',
				featuresTitle: 'Features VIP',
				priceId: 'your-price-id',
				features: [
					{
						title: 'Feature 1',
						disabled: false,
					},
					{
						title: 'Feature 2',
						disabled: false,
					},
				],
			},
		],
	},
	colors: {
		// REQUIRED — The theme to use (light & dark mode supported)
		theme: 'light',
		// REQUIRED — This color will be reflected on the whole app outside of the document (loading bar, Chrome tabs, etc..)
		// Use a custom color: main: "#f37055". HEX only.
		main: 'oklch(0.795 0.184 86.047)', // Primary color from theme
	},
	resend: {
		// REQUIRED — Email 'From' field to be used when sending other emails, like abandoned carts, updates etc..
		fromAdmin: `Dennis at MicroSassFast <<EMAIL>>`,
		// Email shown to customer if need support. Leave empty if not needed => if empty, set up Crisp above, otherwise you won't be able to offer customer support."
		supportEmail: '<EMAIL>',
		// When someone replies to supportEmail sent by the app, forward it to the email below (otherwise it's lost). If you set supportEmail to empty, this will be ignored.
		forwardRepliesTo: '<EMAIL>',
		subjects: {
			thankYou: 'Welcome to MicroSaaSFast',
		},
	},
	supportedRegions: ['US', 'CA'],
	socialLinks: {
		twitter: 'https://twitter.com/sonobrokers',
		facebook: 'https://facebook.com/sonobrokers',
		linkedin: 'https://linkedin.com/company/sonobrokers',
	},
	contact: {
		email: '<EMAIL>',
		phone: '6477715300',
	},
	seo: {
		defaultImage: '/images/og-image.jpg',
		defaultLocale: 'en_US',
		twitterHandle: '@sonobrokers',
	},
}

export default config
