import { sendOpenAi } from '@/lib/gpt';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { prompt, userId } = await request.json();

    const generatedDescription = await sendOpenAi(
      [{ role: 'user', content: prompt }],
      userId
    );

    if (generatedDescription) {
      return NextResponse.json({ description: generatedDescription });
    } else {
      return new NextResponse('Failed to generate description', { status: 500 });
    }
  } catch (error) {
    console.error('Error generating AI description:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
