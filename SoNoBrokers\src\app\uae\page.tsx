import { UAELandingClient } from './UAELandingClient'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'SoNoBrokers UAE | For Sale By Owner Real Estate Platform',
  description: 'Sell your property directly to buyers in the United Arab Emirates with SoNoBrokers. Save on commissions, access professional services, and connect with verified buyers across UAE markets.',
  keywords: 'for sale by owner UAE, FSBO UAE, real estate UAE, property sale UAE, Dubai real estate, Abu Dhabi properties',
  openGraph: {
    title: 'SoNoBrokers UAE | For Sale By Owner Real Estate',
    description: 'The leading FSBO platform in the UAE. Sell directly, save commissions, access professional services.',
    url: 'https://www.sonobrokers.com/uae',
    siteName: 'SoNoBrokers',
    locale: 'en_AE',
    type: 'website',
  },
}

export default function UAELandingPage() {
  return (
    <div>
      <UAELandingClient />
    </div>
  )
}
