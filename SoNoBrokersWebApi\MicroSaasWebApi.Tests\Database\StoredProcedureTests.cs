using FluentAssertions;
using MicroSaasWebApi.Tests.Common;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Database
{
    public class StoredProcedureTests : DatabaseTestBase
    {
        public StoredProcedureTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task SearchPropertiesAdvanced_WithNoFilters_ReturnsAllActiveProperties()
        {
            // Act
            var sql = @"
                SELECT * FROM public.search_properties_advanced(
                    p_location := NULL,
                    p_property_type := NULL,
                    p_min_price := NULL,
                    p_max_price := NULL,
                    p_bedrooms := NULL,
                    p_bathrooms := NULL,
                    p_user_id := NULL,
                    p_limit := 100,
                    p_offset := 0
                )";

            var results = await QueryAsync<dynamic>(sql);

            // Assert
            results.Should().NotBeNull();
            // Should return at least the test properties we seeded
            results.Count().Should().BeGreaterOrEqualTo(0);
        }

        [Fact]
        public async Task SearchPropertiesAdvanced_WithPriceFilter_ReturnsFilteredResults()
        {
            // Act
            var sql = @"
                SELECT * FROM public.search_properties_advanced(
                    p_location := NULL,
                    p_property_type := NULL,
                    p_min_price := 400000,
                    p_max_price := 600000,
                    p_bedrooms := NULL,
                    p_bathrooms := NULL,
                    p_user_id := NULL,
                    p_limit := 100,
                    p_offset := 0
                )";

            var results = await QueryAsync<dynamic>(sql);

            // Assert
            results.Should().NotBeNull();
            foreach (var result in results)
            {
                var price = (decimal)result.price;
                price.Should().BeGreaterOrEqualTo(400000);
                price.Should().BeLessOrEqualTo(600000);
            }
        }

        [Fact]
        public async Task SearchPropertiesAdvanced_WithPropertyTypeFilter_ReturnsFilteredResults()
        {
            // Act
            var sql = @"
                SELECT * FROM public.search_properties_advanced(
                    p_location := NULL,
                    p_property_type := 'Detached House',
                    p_min_price := NULL,
                    p_max_price := NULL,
                    p_bedrooms := NULL,
                    p_bathrooms := NULL,
                    p_user_id := NULL,
                    p_limit := 100,
                    p_offset := 0
                )";

            var results = await QueryAsync<dynamic>(sql);

            // Assert
            results.Should().NotBeNull();
            foreach (var result in results)
            {
                var propertyType = (string)result.property_type;
                propertyType.Should().Be("Detached House");
            }
        }

        [Fact]
        public async Task SearchPropertiesAdvanced_WithPagination_ReturnsCorrectPage()
        {
            // Act - Get first page
            var firstPageSql = @"
                SELECT * FROM public.search_properties_advanced(
                    p_location := NULL,
                    p_property_type := NULL,
                    p_min_price := NULL,
                    p_max_price := NULL,
                    p_bedrooms := NULL,
                    p_bathrooms := NULL,
                    p_user_id := NULL,
                    p_limit := 1,
                    p_offset := 0
                )";

            var firstPage = await QueryAsync<dynamic>(firstPageSql);

            // Act - Get second page
            var secondPageSql = @"
                SELECT * FROM public.search_properties_advanced(
                    p_location := NULL,
                    p_property_type := NULL,
                    p_min_price := NULL,
                    p_max_price := NULL,
                    p_bedrooms := NULL,
                    p_bathrooms := NULL,
                    p_user_id := NULL,
                    p_limit := 1,
                    p_offset := 1
                )";

            var secondPage = await QueryAsync<dynamic>(secondPageSql);

            // Assert
            firstPage.Should().NotBeNull();
            secondPage.Should().NotBeNull();
            
            if (firstPage.Any() && secondPage.Any())
            {
                var firstId = (string)firstPage.First().id;
                var secondId = (string)secondPage.First().id;
                firstId.Should().NotBe(secondId);
            }
        }

        [Fact]
        public async Task GetPropertyAnalytics_WithValidPropertyId_ReturnsAnalytics()
        {
            // Arrange
            var propertyId = "db-test-prop-1";

            // Act
            var sql = "SELECT * FROM public.get_property_analytics(@propertyId)";
            var result = await QuerySingleOrDefaultAsync<dynamic>(sql, new { propertyId });

            // Assert
            result.Should().NotBeNull();
            var resultDict = result as IDictionary<string, object>;
            resultDict.Should().ContainKey("property_id");
            resultDict.Should().ContainKey("view_count");
            resultDict.Should().ContainKey("favorite_count");
            resultDict.Should().ContainKey("inquiry_count");
        }

        [Fact]
        public async Task GetUserDashboardStats_WithValidUserId_ReturnsStats()
        {
            // Arrange
            var userId = "db-test-user-2"; // User who has properties

            // Act
            var sql = "SELECT * FROM public.get_user_dashboard_stats(@userId)";
            var result = await QuerySingleOrDefaultAsync<dynamic>(sql, new { userId });

            // Assert
            result.Should().NotBeNull();
            var resultDict = result as IDictionary<string, object>;
            resultDict.Should().ContainKey("total_properties");
            resultDict.Should().ContainKey("active_properties");
            resultDict.Should().ContainKey("sold_properties");
            resultDict.Should().ContainKey("total_views");
            resultDict.Should().ContainKey("total_inquiries");
            resultDict.Should().ContainKey("recent_activity_count");

            // User should have at least the test properties we created
            var totalProperties = (int)resultDict["total_properties"];
            totalProperties.Should().BeGreaterOrEqualTo(2);
        }

        [Fact]
        public async Task CreatePropertyWithImages_CreatesPropertyAndImages()
        {
            // Arrange
            var propertyId = TestHelpers.GenerateTestId("sp-prop");
            var imageUrls = new[] { "https://example.com/image1.jpg", "https://example.com/image2.jpg" };

            // Act
            var sql = @"
                SELECT public.create_property_with_images(
                    p_title := @title,
                    p_description := @description,
                    p_price := @price,
                    p_property_type := @propertyType,
                    p_bedrooms := @bedrooms,
                    p_bathrooms := @bathrooms,
                    p_sqft := @sqft,
                    p_address := @address,
                    p_seller_id := @sellerId,
                    p_image_urls := @imageUrls
                )";

            var createdPropertyId = await QuerySingleAsync<string>(sql, new
            {
                title = "SP Test Property",
                description = "Created via stored procedure",
                price = 600000m,
                propertyType = "Condominium",
                bedrooms = 2,
                bathrooms = 2.0m,
                sqft = 1200,
                address = """{"street": "123 SP Test St", "city": "Test City"}""",
                sellerId = "db-test-user-2",
                imageUrls
            });

            // Assert
            createdPropertyId.Should().NotBeNullOrEmpty();
            createdPropertyId.ShouldBeValidGuid();

            // Verify property was created
            var propertyExists = await QuerySingleAsync<bool>(
                @"SELECT EXISTS(SELECT 1 FROM public.""Property"" WHERE id = @id)",
                new { id = createdPropertyId });
            propertyExists.Should().BeTrue();

            // Verify images were created
            var imageCount = await QuerySingleAsync<int>(
                @"SELECT COUNT(*) FROM public.""PropertyImage"" WHERE ""propertyId"" = @propertyId",
                new { propertyId = createdPropertyId });
            imageCount.Should().Be(2);

            // Cleanup
            await ExecuteAsync(@"DELETE FROM public.""PropertyImage"" WHERE ""propertyId"" = @propertyId", new { propertyId = createdPropertyId });
            await ExecuteAsync(@"DELETE FROM public.""Property"" WHERE id = @id", new { id = createdPropertyId });
        }

        [Fact]
        public async Task GetAdvertiserPerformance_WithValidAdvertiserId_ReturnsMetrics()
        {
            // Arrange
            var advertiserId = "db-test-adv-1";

            // Act
            var sql = "SELECT * FROM public.get_advertiser_performance(@advertiserId, @days)";
            var result = await QuerySingleOrDefaultAsync<dynamic>(sql, new { advertiserId, days = 30 });

            // Assert
            result.Should().NotBeNull();
            var resultDict = result as IDictionary<string, object>;
            resultDict.Should().ContainKey("advertiser_id");
            resultDict.Should().ContainKey("profile_views");
            resultDict.Should().ContainKey("contact_clicks");
            resultDict.Should().ContainKey("website_clicks");
            resultDict.Should().ContainKey("conversion_rate");
            resultDict.Should().ContainKey("avg_rating");
            resultDict.Should().ContainKey("total_reviews");

            var returnedAdvertiserId = (string)resultDict["advertiser_id"];
            returnedAdvertiserId.Should().Be(advertiserId);
        }

        [Fact]
        public async Task SyncSubscriptionStatus_UpdatesSubscription()
        {
            // Arrange - First create a test subscription
            var subscriptionId = TestHelpers.GenerateTestId("sp-sub");
            var stripeSubscriptionId = "sub_test_123";

            await ExecuteAsync(@"
                INSERT INTO public.""SubscriptionSnb"" (id, ""stripeSubscriptionId"", status, ""currentPeriodStart"", ""currentPeriodEnd"", ""createdAt"", ""updatedAt"")
                VALUES (@id, @stripeSubscriptionId, 'active', @start, @end, @created, @updated)",
                new
                {
                    id = subscriptionId,
                    stripeSubscriptionId,
                    start = DateTime.UtcNow,
                    end = DateTime.UtcNow.AddMonths(1),
                    created = DateTime.UtcNow,
                    updated = DateTime.UtcNow
                });

            // Act
            var sql = @"
                SELECT public.sync_subscription_status(
                    p_stripe_subscription_id := @stripeSubscriptionId,
                    p_status := @status,
                    p_current_period_start := @periodStart,
                    p_current_period_end := @periodEnd
                )";

            var newPeriodStart = DateTime.UtcNow.AddDays(1);
            var newPeriodEnd = DateTime.UtcNow.AddMonths(1).AddDays(1);

            var result = await QuerySingleAsync<bool>(sql, new
            {
                stripeSubscriptionId,
                status = "cancelled",
                periodStart = newPeriodStart,
                periodEnd = newPeriodEnd
            });

            // Assert
            result.Should().BeTrue();

            // Verify the subscription was updated
            var updatedSubscription = await QuerySingleOrDefaultAsync<dynamic>(
                @"SELECT * FROM public.""SubscriptionSnb"" WHERE ""stripeSubscriptionId"" = @stripeSubscriptionId",
                new { stripeSubscriptionId });

            updatedSubscription.Should().NotBeNull();
            var status = (string)updatedSubscription.status;
            status.Should().Be("cancelled");

            // Cleanup
            await ExecuteAsync(@"DELETE FROM public.""SubscriptionSnb"" WHERE id = @id", new { id = subscriptionId });
        }

        [Fact]
        public async Task GetAdminDashboardCounts_ReturnsAccurateCounts()
        {
            // Act
            var sql = "SELECT * FROM public.get_admin_dashboard_counts()";
            var result = await QuerySingleOrDefaultAsync<dynamic>(sql);

            // Assert
            result.Should().NotBeNull();
            var resultDict = result as IDictionary<string, object>;
            
            resultDict.Should().ContainKey("total_users");
            resultDict.Should().ContainKey("active_users");
            resultDict.Should().ContainKey("total_properties");
            resultDict.Should().ContainKey("active_properties");
            resultDict.Should().ContainKey("total_advertisers");
            resultDict.Should().ContainKey("active_advertisers");
            resultDict.Should().ContainKey("total_subscriptions");
            resultDict.Should().ContainKey("active_subscriptions");

            // Verify counts are non-negative
            var totalUsers = (int)resultDict["total_users"];
            var activeUsers = (int)resultDict["active_users"];
            var totalProperties = (int)resultDict["total_properties"];
            var activeProperties = (int)resultDict["active_properties"];

            totalUsers.Should().BeGreaterOrEqualTo(0);
            activeUsers.Should().BeGreaterOrEqualTo(0);
            totalProperties.Should().BeGreaterOrEqualTo(0);
            activeProperties.Should().BeGreaterOrEqualTo(0);

            // Active counts should not exceed total counts
            activeUsers.Should().BeLessOrEqualTo(totalUsers);
            activeProperties.Should().BeLessOrEqualTo(totalProperties);
        }
    }
}
