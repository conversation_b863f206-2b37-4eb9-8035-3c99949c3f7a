using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Npgsql;
using System.Data;

namespace MicroSaasWebApi.Tests.Infrastructure
{
    /// <summary>
    /// Handles test database setup, cleanup, and management
    /// </summary>
    public class TestDatabaseSetup : IDisposable
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<TestDatabaseSetup> _logger;
        private readonly string _connectionString;
        private readonly string _testDatabaseName;
        private readonly string _masterConnectionString;
        private bool _disposed = false;

        public TestDatabaseSetup(IConfiguration configuration, ILogger<TestDatabaseSetup> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _testDatabaseName = "sonobrokers_test";
            _connectionString = GetTestConnectionString();
            _masterConnectionString = GetMasterConnectionString();
        }

        /// <summary>
        /// Initializes the test database
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing test database: {DatabaseName}", _testDatabaseName);

                // Create test database if it doesn't exist
                await CreateTestDatabaseIfNotExistsAsync();

                // Run migrations
                await RunMigrationsAsync();

                // Seed test data if enabled
                if (_configuration.GetValue<bool>("TestSettings:SeedTestData", true))
                {
                    await SeedTestDataAsync();
                }

                _logger.LogInformation("Test database initialization completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize test database");
                throw;
            }
        }

        /// <summary>
        /// Cleans up the test database
        /// </summary>
        public async Task CleanupAsync()
        {
            try
            {
                if (_configuration.GetValue<bool>("TestSettings:CleanupAfterTests", true))
                {
                    _logger.LogInformation("Cleaning up test database: {DatabaseName}", _testDatabaseName);
                    await ClearAllTablesAsync();
                    _logger.LogInformation("Test database cleanup completed");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to cleanup test database");
                throw;
            }
        }

        /// <summary>
        /// Resets the test database to a clean state
        /// </summary>
        public async Task ResetAsync()
        {
            try
            {
                _logger.LogInformation("Resetting test database: {DatabaseName}", _testDatabaseName);
                
                await ClearAllTablesAsync();
                
                if (_configuration.GetValue<bool>("TestSettings:SeedTestData", true))
                {
                    await SeedTestDataAsync();
                }
                
                _logger.LogInformation("Test database reset completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to reset test database");
                throw;
            }
        }

        /// <summary>
        /// Creates a test database if it doesn't exist
        /// </summary>
        private async Task CreateTestDatabaseIfNotExistsAsync()
        {
            using var connection = new NpgsqlConnection(_masterConnectionString);
            await connection.OpenAsync();

            // Check if database exists
            var checkDbQuery = "SELECT 1 FROM pg_database WHERE datname = @dbName";
            using var checkCommand = new NpgsqlCommand(checkDbQuery, connection);
            checkCommand.Parameters.AddWithValue("@dbName", _testDatabaseName);

            var exists = await checkCommand.ExecuteScalarAsync();

            if (exists == null)
            {
                // Create database
                var createDbQuery = $"CREATE DATABASE \"{_testDatabaseName}\"";
                using var createCommand = new NpgsqlCommand(createDbQuery, connection);
                await createCommand.ExecuteNonQueryAsync();
                
                _logger.LogInformation("Created test database: {DatabaseName}", _testDatabaseName);
            }
            else
            {
                _logger.LogInformation("Test database already exists: {DatabaseName}", _testDatabaseName);
            }
        }

        /// <summary>
        /// Runs database migrations
        /// </summary>
        private async Task RunMigrationsAsync()
        {
            _logger.LogInformation("Running database migrations for test database");

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            // Create tables if they don't exist
            await CreateTablesAsync(connection);

            _logger.LogInformation("Database migrations completed");
        }

        /// <summary>
        /// Creates database tables
        /// </summary>
        private async Task CreateTablesAsync(NpgsqlConnection connection)
        {
            var createTablesScript = @"
                -- Users table
                CREATE TABLE IF NOT EXISTS users (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    clerk_user_id VARCHAR(255) UNIQUE NOT NULL,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    full_name VARCHAR(255),
                    first_name VARCHAR(100),
                    last_name VARCHAR(100),
                    phone VARCHAR(20),
                    role VARCHAR(50) DEFAULT 'USER',
                    user_type VARCHAR(50) DEFAULT 'Buyer',
                    is_active BOOLEAN DEFAULT true,
                    logged_in BOOLEAN DEFAULT false,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    last_login_at TIMESTAMP WITH TIME ZONE
                );

                -- Properties table
                CREATE TABLE IF NOT EXISTS properties (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    title VARCHAR(255) NOT NULL,
                    description TEXT,
                    price DECIMAL(12,2) NOT NULL,
                    property_type VARCHAR(100) NOT NULL,
                    bedrooms INTEGER DEFAULT 0,
                    bathrooms INTEGER DEFAULT 0,
                    sqft INTEGER,
                    address VARCHAR(500),
                    city VARCHAR(100),
                    province VARCHAR(100),
                    country VARCHAR(100),
                    postal_code VARCHAR(20),
                    latitude DECIMAL(10,8),
                    longitude DECIMAL(11,8),
                    seller_id UUID NOT NULL REFERENCES users(id),
                    status VARCHAR(50) DEFAULT 'active',
                    features TEXT[],
                    images TEXT[],
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );

                -- Advertisers table
                CREATE TABLE IF NOT EXISTS advertisers (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    user_id UUID NOT NULL REFERENCES users(id),
                    business_name VARCHAR(255) NOT NULL,
                    contact_name VARCHAR(255) NOT NULL,
                    email VARCHAR(255) NOT NULL,
                    phone VARCHAR(20) NOT NULL,
                    website VARCHAR(500),
                    description TEXT NOT NULL,
                    service_type VARCHAR(100) NOT NULL,
                    service_areas TEXT[] NOT NULL,
                    license_number VARCHAR(100),
                    plan VARCHAR(50) DEFAULT 'basic',
                    status VARCHAR(50) DEFAULT 'active',
                    is_premium BOOLEAN DEFAULT false,
                    is_verified BOOLEAN DEFAULT false,
                    images TEXT[],
                    rating DECIMAL(3,2),
                    review_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );

                -- Create indexes
                CREATE INDEX IF NOT EXISTS idx_users_clerk_user_id ON users(clerk_user_id);
                CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
                CREATE INDEX IF NOT EXISTS idx_properties_seller_id ON properties(seller_id);
                CREATE INDEX IF NOT EXISTS idx_properties_status ON properties(status);
                CREATE INDEX IF NOT EXISTS idx_properties_city ON properties(city);
                CREATE INDEX IF NOT EXISTS idx_properties_price ON properties(price);
                CREATE INDEX IF NOT EXISTS idx_advertisers_user_id ON advertisers(user_id);
                CREATE INDEX IF NOT EXISTS idx_advertisers_service_type ON advertisers(service_type);
                CREATE INDEX IF NOT EXISTS idx_advertisers_status ON advertisers(status);
            ";

            using var command = new NpgsqlCommand(createTablesScript, connection);
            await command.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// Seeds test data
        /// </summary>
        private async Task SeedTestDataAsync()
        {
            _logger.LogInformation("Seeding test data");

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            // Insert test users
            await SeedTestUsersAsync(connection);

            // Insert test properties
            await SeedTestPropertiesAsync(connection);

            // Insert test advertisers
            await SeedTestAdvertisersAsync(connection);

            _logger.LogInformation("Test data seeding completed");
        }

        /// <summary>
        /// Seeds test users
        /// </summary>
        private async Task SeedTestUsersAsync(NpgsqlConnection connection)
        {
            var insertUserQuery = @"
                INSERT INTO users (id, clerk_user_id, email, full_name, first_name, last_name, role, user_type)
                VALUES 
                    ('11111111-1111-1111-1111-111111111111', 'test-user-1', '<EMAIL>', 'Test User', 'Test', 'User', 'USER', 'Buyer'),
                    ('22222222-2222-2222-2222-222222222222', 'test-admin-1', '<EMAIL>', 'Test Admin', 'Test', 'Admin', 'ADMIN', 'Buyer'),
                    ('33333333-3333-3333-3333-333333333333', 'test-seller-1', '<EMAIL>', 'Test Seller', 'Test', 'Seller', 'USER', 'Seller')
                ON CONFLICT (clerk_user_id) DO NOTHING;
            ";

            using var command = new NpgsqlCommand(insertUserQuery, connection);
            await command.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// Seeds test properties
        /// </summary>
        private async Task SeedTestPropertiesAsync(NpgsqlConnection connection)
        {
            var insertPropertyQuery = @"
                INSERT INTO properties (title, description, price, property_type, bedrooms, bathrooms, sqft, city, province, country, seller_id, status)
                VALUES 
                    ('Beautiful Family Home', 'Spacious 3-bedroom home in great neighborhood', 450000, 'House', 3, 2, 1800, 'Toronto', 'ON', 'CA', '33333333-3333-3333-3333-333333333333', 'active'),
                    ('Modern Condo Downtown', 'Luxury condo with city views', 650000, 'Condo', 2, 2, 1200, 'Vancouver', 'BC', 'CA', '33333333-3333-3333-3333-333333333333', 'active'),
                    ('Cozy Starter Home', 'Perfect for first-time buyers', 320000, 'House', 2, 1, 1000, 'Calgary', 'AB', 'CA', '33333333-3333-3333-3333-333333333333', 'active')
                ON CONFLICT DO NOTHING;
            ";

            using var command = new NpgsqlCommand(insertPropertyQuery, connection);
            await command.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// Seeds test advertisers
        /// </summary>
        private async Task SeedTestAdvertisersAsync(NpgsqlConnection connection)
        {
            var insertAdvertiserQuery = @"
                INSERT INTO advertisers (user_id, business_name, contact_name, email, phone, description, service_type, service_areas, plan, status)
                VALUES 
                    ('11111111-1111-1111-1111-111111111111', 'Test Photography Studio', 'John Photographer', '<EMAIL>', '555-0001', 'Professional real estate photography', 'photographer', ARRAY['Toronto', 'Mississauga'], 'basic', 'active'),
                    ('22222222-2222-2222-2222-222222222222', 'Test Legal Services', 'Jane Lawyer', '<EMAIL>', '555-0002', 'Real estate legal services', 'lawyer', ARRAY['Toronto', 'York'], 'premium', 'active')
                ON CONFLICT DO NOTHING;
            ";

            using var command = new NpgsqlCommand(insertAdvertiserQuery, connection);
            await command.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// Clears all tables
        /// </summary>
        private async Task ClearAllTablesAsync()
        {
            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            var clearTablesQuery = @"
                TRUNCATE TABLE advertisers CASCADE;
                TRUNCATE TABLE properties CASCADE;
                TRUNCATE TABLE users CASCADE;
            ";

            using var command = new NpgsqlCommand(clearTablesQuery, connection);
            await command.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// Gets the test database connection string
        /// </summary>
        private string GetTestConnectionString()
        {
            return _configuration.GetConnectionString("SupabaseConnection") 
                ?? "Host=localhost;Port=5432;Database=sonobrokers_test;Username=********;Password=********;";
        }

        /// <summary>
        /// Gets the master database connection string (for creating databases)
        /// </summary>
        private string GetMasterConnectionString()
        {
            var builder = new NpgsqlConnectionStringBuilder(GetTestConnectionString())
            {
                Database = "********"
            };
            return builder.ToString();
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                // Cleanup resources if needed
                _disposed = true;
            }
        }
    }
}
