# Contact Sharing API Documentation

## Overview

The Contact Sharing API enables secure communication between property buyers and sellers. It provides endpoints for sharing contact information, submitting offers, scheduling visits, and managing responses.

## Authentication

All endpoints require authentication using Clerk JWT tokens.

```http
Authorization: Bearer <jwt_token>
```

## Base URL

```
https://api.sonobrokers.com/api/sonobrokers/contact-sharing
```

## Endpoints

### Create Contact Share

Creates a new contact share request.

**Endpoint:** `POST /api/sonobrokers/contact-sharing`

**Request Body:**
```json
{
  "propertyId": "string",
  "sellerId": "string",
  "buyerName": "string",
  "buyerEmail": "string",
  "buyerPhone": "string (optional)",
  "message": "string (optional)",
  "shareType": 1,
  "offerAmount": 500000.00,
  "schedulingPreference": "string (optional)",
  "preferredVisitDate": "2024-12-31",
  "preferredVisitTime": "14:00"
}
```

**Share Types:**
- `1` - Contact Request
- `2` - Property Offer
- `3` - Schedule Visit
- `4` - Offer with Visit

**Response:**
```json
{
  "id": "contact-share-123",
  "propertyId": "property-123",
  "propertyTitle": "Beautiful Family Home",
  "propertyAddress": "123 Main St, City, State",
  "propertyPrice": 500000.00,
  "buyerId": "buyer-123",
  "buyerName": "John Buyer",
  "buyerEmail": "<EMAIL>",
  "buyerPhone": "+1234567890",
  "sellerId": "seller-123",
  "sellerName": "Jane Seller",
  "sellerEmail": "<EMAIL>",
  "message": "I'm interested in this property",
  "shareType": 2,
  "shareTypeDisplay": "Property Offer",
  "offerAmount": 500000.00,
  "schedulingPreference": null,
  "preferredVisitDate": null,
  "preferredVisitTime": null,
  "status": 1,
  "statusDisplay": "Sent",
  "createdAt": "2024-01-15T10:30:00Z",
  "respondedAt": null,
  "sellerResponse": null,
  "emailSent": true,
  "emailSentAt": "2024-01-15T10:30:05Z"
}
```

**Status Codes:**
- `201` - Created successfully
- `400` - Bad request (validation errors)
- `401` - Unauthorized
- `500` - Internal server error

### Get Contact Shares

Retrieves contact shares for the current user.

**Endpoint:** `GET /api/sonobrokers/contact-sharing`

**Query Parameters:**
- `page` (optional) - Page number (default: 1)
- `limit` (optional) - Items per page (default: 20, max: 100)
- `sortBy` (optional) - Sort field (default: createdAt)
- `sortOrder` (optional) - Sort order: asc/desc (default: desc)
- `shareType` (optional) - Filter by share type
- `status` (optional) - Filter by status
- `fromDate` (optional) - Filter from date (ISO 8601)
- `toDate` (optional) - Filter to date (ISO 8601)
- `search` (optional) - Search term

**Example:**
```http
GET /api/sonobrokers/contact-sharing?page=1&limit=10&shareType=2&status=1
```

**Response:**
```json
{
  "contactShares": [
    {
      "id": "contact-share-123",
      "propertyTitle": "Beautiful Family Home",
      "shareType": 2,
      "shareTypeDisplay": "Property Offer",
      "status": 1,
      "statusDisplay": "Sent",
      "createdAt": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 25,
  "page": 1,
  "totalPages": 3,
  "hasMore": true
}
```

### Get Contact Share by ID

Retrieves a specific contact share by ID.

**Endpoint:** `GET /api/sonobrokers/contact-sharing/{id}`

**Response:** Same as Create Contact Share response

**Status Codes:**
- `200` - Success
- `401` - Unauthorized
- `403` - Forbidden (no access to this contact share)
- `404` - Not found

### Respond to Contact Share

Allows sellers to respond to contact share requests.

**Endpoint:** `PUT /api/sonobrokers/contact-sharing/{id}/respond`

**Request Body:**
```json
{
  "status": 5,
  "response": "I'd be happy to show you the property!",
  "counterOfferAmount": 485000.00,
  "alternativeVisitDate": "2024-01-20",
  "alternativeVisitTime": "15:00"
}
```

**Status Values:**
- `4` - Responded
- `5` - Accepted
- `6` - Declined

**Response:**
```json
{
  "message": "Response recorded successfully"
}
```

### Get Contact Share Statistics

Retrieves statistics for the current user's contact shares.

**Endpoint:** `GET /api/sonobrokers/contact-sharing/stats`

**Response:**
```json
{
  "totalContactShares": 15,
  "contactRequests": 8,
  "propertyOffers": 5,
  "scheduleRequests": 2,
  "pendingResponses": 3,
  "acceptedRequests": 7,
  "declinedRequests": 2,
  "averageOfferAmount": 475000.00,
  "highestOffer": 520000.00,
  "lastContactShare": "2024-01-15T10:30:00Z",
  "propertyStats": [
    {
      "propertyId": "property-123",
      "propertyTitle": "Beautiful Family Home",
      "contactCount": 5,
      "offerCount": 3,
      "highestOffer": 520000.00,
      "lastContact": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### Get Property Contact Shares

Retrieves contact shares for a specific property.

**Endpoint:** `GET /api/sonobrokers/contact-sharing/property/{propertyId}`

**Query Parameters:** Same as Get Contact Shares

**Response:** Same as Get Contact Shares response

### Get Property Contact Share Statistics

Retrieves statistics for a specific property.

**Endpoint:** `GET /api/sonobrokers/contact-sharing/property/{propertyId}/stats`

**Response:** Same as Get Contact Share Statistics response

### Delete Contact Share

Deletes a contact share.

**Endpoint:** `DELETE /api/sonobrokers/contact-sharing/{id}`

**Response:**
```json
{
  "message": "Contact share deleted successfully"
}
```

**Status Codes:**
- `200` - Deleted successfully
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not found

## Admin Endpoints

### Get All Contact Shares (Admin)

**Endpoint:** `GET /api/admin/contact-sharing`

Requires admin role. Returns all contact shares in the system.

### Get System Statistics (Admin)

**Endpoint:** `GET /api/admin/contact-sharing/stats`

Returns system-wide contact sharing statistics.

### Send Reminder Emails (Admin)

**Endpoint:** `POST /api/admin/contact-sharing/send-reminders`

Sends reminder emails for pending contact shares.

## Error Handling

### Error Response Format

```json
{
  "error": "Error message",
  "details": "Detailed error information",
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/api/sonobrokers/contact-sharing"
}
```

### Common Error Codes

- `400` - Bad Request
  - Invalid input data
  - Validation errors
  - Missing required fields

- `401` - Unauthorized
  - Missing or invalid JWT token
  - Token expired

- `403` - Forbidden
  - Insufficient permissions
  - Access denied to resource

- `404` - Not Found
  - Resource not found
  - Invalid ID

- `429` - Too Many Requests
  - Rate limit exceeded
  - Cooldown period active

- `500` - Internal Server Error
  - Database errors
  - Service unavailable

## Rate Limiting

### Limits
- **Contact Shares:** 10 per day per user
- **Property Offers:** 5 per property per user
- **API Requests:** 100 per minute per user

### Headers
Rate limit information is included in response headers:

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248600
```

## Webhooks

### Contact Share Events

The API can send webhooks for contact share events:

- `contact_share.created`
- `contact_share.responded`
- `contact_share.accepted`
- `contact_share.declined`

### Webhook Payload

```json
{
  "event": "contact_share.created",
  "data": {
    "contactShare": {
      "id": "contact-share-123",
      "propertyId": "property-123",
      "shareType": 2,
      "status": 1,
      "createdAt": "2024-01-15T10:30:00Z"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## SDK Examples

### JavaScript/TypeScript

```typescript
import { ContactSharingAPI } from '@sonobrokers/api-client';

const api = new ContactSharingAPI({
  baseUrl: 'https://api.sonobrokers.com',
  token: 'your-jwt-token'
});

// Create contact share
const contactShare = await api.createContactShare({
  propertyId: 'property-123',
  sellerId: 'seller-123',
  buyerName: 'John Buyer',
  buyerEmail: '<EMAIL>',
  shareType: ContactShareType.PropertyOffer,
  offerAmount: 500000
});

// Get contact shares
const response = await api.getContactShares({
  page: 1,
  limit: 10,
  shareType: ContactShareType.PropertyOffer
});
```

### cURL Examples

```bash
# Create contact share
curl -X POST https://api.sonobrokers.com/api/sonobrokers/contact-sharing \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "propertyId": "property-123",
    "sellerId": "seller-123",
    "buyerName": "John Buyer",
    "buyerEmail": "<EMAIL>",
    "shareType": 2,
    "offerAmount": 500000
  }'

# Get contact shares
curl -X GET "https://api.sonobrokers.com/api/sonobrokers/contact-sharing?page=1&limit=10" \
  -H "Authorization: Bearer your-jwt-token"

# Respond to contact share
curl -X PUT https://api.sonobrokers.com/api/sonobrokers/contact-sharing/contact-share-123/respond \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "status": 5,
    "response": "I would be happy to show you the property!"
  }'
```

## Testing

### Test Environment

Base URL: `https://api-staging.sonobrokers.com`

### Test Data

Use the following test data for development:

```json
{
  "testPropertyId": "test-property-123",
  "testSellerId": "test-seller-123",
  "testBuyerId": "test-buyer-123"
}
```

### Postman Collection

A Postman collection is available for testing all endpoints:
[Download Collection](./postman/contact-sharing-api.json)

## Support

For API support and questions:
- Email: <EMAIL>
- Documentation: https://docs.sonobrokers.com
- Status Page: https://status.sonobrokers.com
