# =============================================================================
# MicroSaasWebApi.Tests - Docker Compose Configuration
# =============================================================================
# Test runner for SoNoBrokers Web API
# Runs unit tests, integration tests, and E2E tests
# =============================================================================

version: '3.8'

services:
  sonobrokers-tests:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sonobrokers-tests
    environment:
      - ASPNETCORE_ENVIRONMENT=Test
      - DOTNET_ENVIRONMENT=Test
      # Test Database (use separate test database)
      - DATABASE_URL=${TEST_DATABASE_URL:-***************************************************************************/postgres_test}
      # Authentication (test keys)
      - CLERK_SECRET_KEY=${TEST_CLERK_SECRET_KEY:-sk_test_VpPsw9paA18Uka7jxuB6PeE3bLvzKVEESGaDuysAgL}
      - CLERK_PUBLISHABLE_KEY=${TEST_CLERK_PUBLISHABLE_KEY:-pk_test_YmV0dGVyLXBvc3N1bS01OC5jbGVyay5hY2NvdW50cy5kZXYk}
      # External Services (test/mock endpoints)
      - STRIPE_SECRET_KEY=${TEST_STRIPE_SECRET_KEY:-sk_test_mock_key}
      - RESEND_API_KEY=${TEST_RESEND_API_KEY:-re_test_mock_key}
      # Test Configuration
      - TEST_API_BASE_URL=https://localhost:7163
      - TEST_FRONTEND_URL=https://localhost:3000
    volumes:
      - test-results:/app/test-results
      - ./test-data:/app/test-data:ro
    networks:
      - sonobrokers-test-network
    depends_on:
      - test-database
    labels:
      - "com.sonobrokers.service=tests"
      - "com.sonobrokers.version=1.0.0"

  # Optional: Separate test database
  test-database:
    image: postgres:15-alpine
    container_name: sonobrokers-test-db
    environment:
      - POSTGRES_DB=postgres_test
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Shared4w0rk!
    volumes:
      - test-db-data:/var/lib/postgresql/data
      - ./test-data/sql:/docker-entrypoint-initdb.d:ro
    networks:
      - sonobrokers-test-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    labels:
      - "com.sonobrokers.service=test-database"

volumes:
  test-results:
    driver: local
  test-db-data:
    driver: local

networks:
  sonobrokers-test-network:
    driver: bridge
    name: sonobrokers-test-network
