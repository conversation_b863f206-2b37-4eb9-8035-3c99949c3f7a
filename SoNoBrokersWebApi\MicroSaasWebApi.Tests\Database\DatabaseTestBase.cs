using Dapper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MicroSaasWebApi.Data;
using MicroSaasWebApi.Tests.Common;
using Npgsql;
using System.Data;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Database
{
    public abstract class DatabaseTestBase : TestBase, IAsyncLifetime
    {
        protected readonly DapperDbContext DbContext;
        protected readonly string ConnectionString;

        protected DatabaseTestBase(ITestOutputHelper output) : base(output)
        {
            ConnectionString = Configuration.GetConnectionString("SupabaseConnection")
                ?? TestHelpers.GetTestConnectionString();

            var logger = GetService<ILogger<DapperDbContext>>();
            DbContext = new DapperDbContext(Configuration, logger);
        }

        public virtual async Task InitializeAsync()
        {
            await EnsureTestDatabaseExistsAsync();
            await SeedTestDataAsync();
        }

        public virtual async Task DisposeAsync()
        {
            await CleanupTestDataAsync();
        }

        protected async Task<IDbConnection> GetConnectionAsync()
        {
            var connection = new NpgsqlConnection(ConnectionString);
            await connection.OpenAsync();
            return connection;
        }

        protected virtual async Task EnsureTestDatabaseExistsAsync()
        {
            try
            {
                using var connection = await GetConnectionAsync();

                // Test connection by running a simple query
                await connection.QuerySingleAsync<int>("SELECT 1");

                Output.WriteLine("Database connection successful");
            }
            catch (Exception ex)
            {
                Output.WriteLine($"Database connection failed: {ex.Message}");
                throw;
            }
        }

        protected virtual async Task SeedTestDataAsync()
        {
            using var connection = await GetConnectionAsync();

            // Create test users
            await connection.ExecuteAsync(@"
                INSERT INTO public.""User"" (id, email, ""fullName"", ""firstName"", ""lastName"", role, ""userType"", ""isActive"", ""createdAt"", ""updatedAt"")
                VALUES 
                ('db-test-user-1', '<EMAIL>', 'DB Test User 1', 'DB Test', 'User1', 'USER', 'Buyer', true, NOW(), NOW()),
                ('db-test-user-2', '<EMAIL>', 'DB Test User 2', 'DB Test', 'User2', 'USER', 'Seller', true, NOW(), NOW()),
                ('db-test-admin-1', '<EMAIL>', 'DB Test Admin', 'DB Test', 'Admin', 'ADMIN', 'Seller', true, NOW(), NOW())
                ON CONFLICT (id) DO NOTHING;
            ");

            // Create test properties
            await connection.ExecuteAsync(@"
                INSERT INTO public.""Property"" (id, title, description, price, ""propertyType"", bedrooms, bathrooms, ""sellerId"", status, ""createdAt"", ""updatedAt"")
                VALUES 
                ('db-test-prop-1', 'DB Test Property 1', 'A test property for database tests', 500000, 'Detached House', 3, 2, 'db-test-user-2', 'active', NOW(), NOW()),
                ('db-test-prop-2', 'DB Test Property 2', 'Another test property', 750000, 'Townhouse', 4, 3, 'db-test-user-2', 'active', NOW(), NOW())
                ON CONFLICT (id) DO NOTHING;
            ");

            // Create test advertisers
            await connection.ExecuteAsync(@"
                INSERT INTO public.""Advertiser"" (id, ""userId"", ""businessName"", email, ""serviceType"", plan, status, ""createdAt"", ""updatedAt"")
                VALUES 
                ('db-test-adv-1', 'db-test-user-1', 'DB Test Photography', '<EMAIL>', 'photographer', 'basic', 'active', NOW(), NOW()),
                ('db-test-adv-2', 'db-test-user-2', 'DB Test Legal Services', '<EMAIL>', 'lawyer', 'premium', 'active', NOW(), NOW())
                ON CONFLICT (id) DO NOTHING;
            ");

            // Create test projects
            await connection.ExecuteAsync(@"
                INSERT INTO public.""Project"" (id, ""connectionId"", ""webhookId"", ""scenarioId"", ""userClerkId"", ""webhookLink"", type, status, ""createdAt"", ""updatedAt"")
                VALUES 
                ('db-test-proj-1', 'conn-123', 'hook-123', 'scenario-123', 'db-test-user-1', 'https://test.webhook.com', 'automation', 'active', NOW(), NOW()),
                ('db-test-proj-2', 'conn-456', 'hook-456', 'scenario-456', 'db-test-user-2', 'https://test2.webhook.com', 'integration', 'inactive', NOW(), NOW())
                ON CONFLICT (id) DO NOTHING;
            ");

            Output.WriteLine("Test data seeded successfully");
        }

        protected virtual async Task CleanupTestDataAsync()
        {
            try
            {
                using var connection = await GetConnectionAsync();

                // Clean up test data in reverse order of dependencies
                await connection.ExecuteAsync(@"
                    DELETE FROM public.""Project"" WHERE id LIKE 'db-test-%';
                    DELETE FROM public.""Advertiser"" WHERE id LIKE 'db-test-%';
                    DELETE FROM public.""Property"" WHERE id LIKE 'db-test-%';
                    DELETE FROM public.""User"" WHERE id LIKE 'db-test-%';
                ");

                Output.WriteLine("Test data cleaned up successfully");
            }
            catch (Exception ex)
            {
                Output.WriteLine($"Cleanup failed: {ex.Message}");
                // Don't throw during cleanup
            }
        }

        protected async Task<T?> QuerySingleOrDefaultAsync<T>(string sql, object? parameters = null)
        {
            using var connection = await GetConnectionAsync();
            return await connection.QuerySingleOrDefaultAsync<T>(sql, parameters);
        }

        protected async Task<IEnumerable<T>> QueryAsync<T>(string sql, object? parameters = null)
        {
            using var connection = await GetConnectionAsync();
            return await connection.QueryAsync<T>(sql, parameters);
        }

        protected async Task<int> ExecuteAsync(string sql, object? parameters = null)
        {
            using var connection = await GetConnectionAsync();
            return await connection.ExecuteAsync(sql, parameters);
        }

        protected async Task<T> QuerySingleAsync<T>(string sql, object? parameters = null)
        {
            using var connection = await GetConnectionAsync();
            return await connection.QuerySingleAsync<T>(sql, parameters);
        }

        protected async Task<bool> TableExistsAsync(string tableName)
        {
            const string sql = @"
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = @tableName
                );";

            return await QuerySingleAsync<bool>(sql, new { tableName });
        }

        protected async Task<bool> StoredProcedureExistsAsync(string procedureName)
        {
            const string sql = @"
                SELECT EXISTS (
                    SELECT FROM information_schema.routines 
                    WHERE routine_schema = 'public' 
                    AND routine_name = @procedureName
                    AND routine_type = 'FUNCTION'
                );";

            return await QuerySingleAsync<bool>(sql, new { procedureName });
        }

        protected async Task<int> GetTableRowCountAsync(string tableName)
        {
            var sql = $@"SELECT COUNT(*) FROM public.""{tableName}""";
            return await QuerySingleAsync<int>(sql);
        }

        protected override void ConfigureTestServices(IServiceCollection services)
        {
            base.ConfigureTestServices(services);

            // Add database context
            services.AddScoped<DapperDbContext>(provider =>
            {
                var configuration = provider.GetRequiredService<IConfiguration>();
                var logger = provider.GetRequiredService<ILogger<DapperDbContext>>();
                return new DapperDbContext(configuration, logger);
            });
        }
    }
}
