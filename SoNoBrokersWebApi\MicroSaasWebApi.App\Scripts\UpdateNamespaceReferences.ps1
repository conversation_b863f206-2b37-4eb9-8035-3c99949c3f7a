# PowerShell script to update all namespace references to use the new App structure
param(
    [string]$RootPath = ".",
    [switch]$WhatIf = $false
)

Write-Host "Updating namespace references to new App structure..." -ForegroundColor Green

# Define the folders to process
$foldersToProcess = @(
    "Controllers",
    "Services"
)

# Define replacement patterns
$replacements = @{
    # Update using statements for old Data namespace
    'using MicroSaasWebApi\.Data\.Repositories\.SoNoBrokers;' = 'using MicroSaasWebApi.App.Context;'
    'using MicroSaasWebApi\.Data\.Context;' = 'using MicroSaasWebApi.App.Context;'
    'using MicroSaasWebApi\.Data\.Repositories\.Interfaces;' = 'using MicroSaasWebApi.App.Repositories.Interfaces;'
    
    # Update DapperDbContext references
    'MicroSaasWebApi\.Data\.Repositories\.SoNoBrokers\.DapperDbContext' = 'MicroSaasWebApi.App.Context.IDapperDbContext'
    'MicroSaasWebApi\.Data\.Repositories\.SoNoBrokers\.IDapperDbContext' = 'MicroSaasWebApi.App.Context.IDapperDbContext'
    'MicroSaasWebApi\.Data\.Context\.DapperDbContext' = 'MicroSaasWebApi.App.Context.IDapperDbContext'
    'MicroSaasWebApi\.Data\.Context\.IDapperDbContext' = 'MicroSaasWebApi.App.Context.IDapperDbContext'
    
    # Update simple references in constructor parameters and field declarations
    '\bDapperDbContext\b(?!\s*\()' = 'IDapperDbContext'
    'DapperDbContext\s+(\w+)\s*\)' = 'IDapperDbContext $1)'
    'DapperDbContext\s+(\w+)\s*,' = 'IDapperDbContext $1,'
    
    # Update repository interface references
    'MicroSaasWebApi\.Data\.Repositories\.SoNoBrokers\.IPropertyRepository' = 'MicroSaasWebApi.App.Repositories.Interfaces.IPropertyRepository'
    'IPropertyRepository(?!\s*,|\s*>)' = 'MicroSaasWebApi.App.Repositories.Interfaces.IPropertyRepository'
}

function Process-File {
    param(
        [string]$FilePath
    )
    
    Write-Host "Processing: $FilePath" -ForegroundColor Yellow
    
    $content = Get-Content $FilePath -Raw
    $originalContent = $content
    $replacementCount = 0
    
    # Apply replacements
    foreach ($find in $replacements.Keys) {
        $replace = $replacements[$find]
        if ($content -match $find) {
            $content = $content -replace $find, $replace
            $replacementCount++
            Write-Host "  Applied replacement: $find -> $replace" -ForegroundColor Cyan
        }
    }
    
    # Write results
    if ($replacementCount -gt 0) {
        if ($WhatIf) {
            Write-Host "  [WHAT-IF] Would make $replacementCount replacements" -ForegroundColor Magenta
        } else {
            Set-Content $FilePath $content -NoNewline
            Write-Host "  Made $replacementCount replacements" -ForegroundColor Green
        }
        return $true
    }
    
    return $false
}

function Get-FilesToProcess {
    $files = @()
    
    foreach ($folder in $foldersToProcess) {
        $fullPath = Join-Path $RootPath $folder
        if (Test-Path $fullPath) {
            $csFiles = Get-ChildItem $fullPath -Filter "*.cs" -Recurse
            $files += $csFiles
        } else {
            Write-Warning "Folder not found: $fullPath"
        }
    }
    
    return $files
}

# Main execution
$files = Get-FilesToProcess
$processedFiles = @()
$skippedFiles = @()

Write-Host "`nProcessing $($files.Count) files..." -ForegroundColor Green

foreach ($file in $files) {
    $wasProcessed = Process-File -FilePath $file.FullName
    
    if ($wasProcessed) {
        $processedFiles += $file.Name
    } else {
        $skippedFiles += $file.Name
    }
}

# Summary
Write-Host "`n=== SUMMARY ===" -ForegroundColor Green
Write-Host "Processed files: $($processedFiles.Count)" -ForegroundColor Green
Write-Host "Skipped files: $($skippedFiles.Count)" -ForegroundColor Yellow

if ($processedFiles.Count -gt 0) {
    Write-Host "`nProcessed files:" -ForegroundColor Green
    foreach ($file in $processedFiles) {
        Write-Host "  - $file" -ForegroundColor Green
    }
}

if ($WhatIf) {
    Write-Host "`nThis was a dry run. Use without -WhatIf to apply changes." -ForegroundColor Magenta
} else {
    Write-Host "`nNamespace references updated successfully!" -ForegroundColor Green
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Build the project to verify all references are correct" -ForegroundColor White
    Write-Host "2. Test the application to ensure everything works" -ForegroundColor White
}
