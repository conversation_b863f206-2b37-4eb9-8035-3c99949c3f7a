using FluentAssertions;
using Microsoft.Extensions.Logging;
using MicroSaasWebApi.Config.Database;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers;
using MicroSaasWebApi.Tests.Common;
using Moq;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Services
{
    public class AdminServiceTests : TestBase
    {
        private readonly Mock<DapperDbContext> _mockDbContext;
        private readonly Mock<ILogger<AdminService>> _mockLogger;
        private readonly AdminService _adminService;

        public AdminServiceTests(ITestOutputHelper output) : base(output)
        {
            _mockDbContext = new Mock<DapperDbContext>();
            _mockLogger = new Mock<ILogger<AdminService>>();
            _adminService = new AdminService(_mockDbContext.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task GetDashboardDataAsync_ReturnsSuccessResponse()
        {
            // Arrange
            var userStatsData = new
            {
                total_users = 100,
                active_users = 85,
                admin_users = 5,
                regular_users = 90,
                product_users = 3,
                operator_users = 2,
                buyers = 60,
                sellers = 40
            };

            var recentUsersData = new { recent_users = 15 };
            var loginStatsData = new { recent_logins = 45 };
            var subscriptionStatsData = new[]
            {
                new { status = "active", count = 25 },
                new { status = "inactive", count = 10 },
                new { status = "cancelled", count = 5 }
            };

            _mockDbContext.SetupSequence(x => x.QueryFirstOrDefaultAsync<dynamic>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(userStatsData)
                .ReturnsAsync(recentUsersData)
                .ReturnsAsync(loginStatsData);

            _mockDbContext.Setup(x => x.QueryAsync<dynamic>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(subscriptionStatsData);

            // Act
            var result = await _adminService.GetDashboardDataAsync();

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            
            result.Data!.Users.Total.Should().Be(100);
            result.Data.Users.Active.Should().Be(85);
            result.Data.Users.Recent.Should().Be(15);
            result.Data.Users.ByRole.Admin.Should().Be(5);
            result.Data.Users.ByRole.User.Should().Be(90);
            result.Data.Users.ByType.Buyers.Should().Be(60);
            result.Data.Users.ByType.Sellers.Should().Be(40);
            
            result.Data.Activity.RecentLogins.Should().Be(45);
            
            result.Data.Subscriptions.Should().ContainKey("active");
            result.Data.Subscriptions["active"].Should().Be(25);
        }

        [Fact]
        public async Task GetDashboardDataAsync_WithDatabaseError_ReturnsErrorResponse()
        {
            // Arrange
            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<dynamic>(It.IsAny<string>(), It.IsAny<object>()))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _adminService.GetDashboardDataAsync();

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeFalse();
            result.Error.Should().Be("Failed to fetch dashboard data");
            result.Data.Should().BeNull();
        }

        [Fact]
        public async Task GetUsersAsync_ReturnsAllUsers()
        {
            // Arrange
            var usersData = new[]
            {
                new
                {
                    id = "user-1",
                    email = "<EMAIL>",
                    fullName = "User One",
                    firstName = "User",
                    lastName = "One",
                    phone = "************",
                    role = "USER",
                    userType = "Buyer",
                    isActive = true,
                    loggedIn = false,
                    lastLoginAt = DateTime.UtcNow.AddDays(-1),
                    createdAt = DateTime.UtcNow.AddDays(-30),
                    updatedAt = DateTime.UtcNow.AddDays(-1)
                },
                new
                {
                    id = "user-2",
                    email = "<EMAIL>",
                    fullName = "Admin User",
                    firstName = "Admin",
                    lastName = "User",
                    phone = "************",
                    role = "ADMIN",
                    userType = "Seller",
                    isActive = true,
                    loggedIn = true,
                    lastLoginAt = DateTime.UtcNow,
                    createdAt = DateTime.UtcNow.AddDays(-60),
                    updatedAt = DateTime.UtcNow
                }
            };

            _mockDbContext.Setup(x => x.QueryAsync<dynamic>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(usersData);

            // Act
            var result = await _adminService.GetUsersAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            
            var firstUser = result.First();
            firstUser.Id.Should().Be("user-1");
            firstUser.Email.Should().Be("<EMAIL>");
            firstUser.Role.Should().Be(UserRole.USER);
            firstUser.UserType.Should().Be(SnbUserType.Buyer);
        }

        [Fact]
        public async Task GetUserByIdAsync_WithValidId_ReturnsUser()
        {
            // Arrange
            var userId = "user-123";
            var userData = new
            {
                id = userId,
                email = "<EMAIL>",
                fullName = "Test User",
                firstName = "Test",
                lastName = "User",
                phone = "************",
                role = "USER",
                userType = "Buyer",
                isActive = true,
                loggedIn = false,
                lastLoginAt = DateTime.UtcNow.AddDays(-1),
                createdAt = DateTime.UtcNow.AddDays(-30),
                updatedAt = DateTime.UtcNow.AddDays(-1)
            };

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<dynamic>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(userData);

            // Act
            var result = await _adminService.GetUserByIdAsync(userId);

            // Assert
            result.Should().NotBeNull();
            result!.Id.Should().Be(userId);
            result.Email.Should().Be("<EMAIL>");
            result.Role.Should().Be(UserRole.USER);
        }

        [Fact]
        public async Task GetUserByIdAsync_WithInvalidId_ReturnsNull()
        {
            // Arrange
            var userId = "non-existent-id";

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<dynamic>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync((dynamic?)null);

            // Act
            var result = await _adminService.GetUserByIdAsync(userId);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task UpdateUserRoleAsync_WithValidRequest_ReturnsTrue()
        {
            // Arrange
            var request = new UpdateUserRoleRequest
            {
                UserId = "user-123",
                Role = UserRole.ADMIN
            };

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            // Act
            var result = await _adminService.UpdateUserRoleAsync(request);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task UpdateUserStatusAsync_WithValidRequest_ReturnsTrue()
        {
            // Arrange
            var request = new UpdateUserStatusRequest
            {
                UserId = "user-123",
                IsActive = false
            };

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            // Act
            var result = await _adminService.UpdateUserStatusAsync(request);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task GetRolePermissionsAsync_ReturnsAllPermissions()
        {
            // Arrange
            var permissionsData = new[]
            {
                new
                {
                    id = "perm-1",
                    role = "ADMIN",
                    permission = "read",
                    resource = "users",
                    createdAt = DateTime.UtcNow
                },
                new
                {
                    id = "perm-2",
                    role = "USER",
                    permission = "read",
                    resource = "properties",
                    createdAt = DateTime.UtcNow
                }
            };

            _mockDbContext.Setup(x => x.QueryAsync<dynamic>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(permissionsData);

            // Act
            var result = await _adminService.GetRolePermissionsAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            
            var firstPermission = result.First();
            firstPermission.Id.Should().Be("perm-1");
            firstPermission.Role.Should().Be(UserRole.ADMIN);
            firstPermission.Permission.Should().Be("read");
            firstPermission.Resource.Should().Be("users");
        }

        [Fact]
        public async Task CreateRolePermissionAsync_WithValidRequest_ReturnsCreatedPermission()
        {
            // Arrange
            var request = new CreateRolePermissionRequest
            {
                Role = UserRole.ADMIN,
                Permission = "write",
                Resource = "users"
            };

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            // Act
            var result = await _adminService.CreateRolePermissionAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Role.Should().Be(UserRole.ADMIN);
            result.Permission.Should().Be("write");
            result.Resource.Should().Be("users");
            result.Id.ShouldBeValidGuid();
        }

        [Fact]
        public async Task UpdateRolePermissionAsync_WithValidRequest_ReturnsTrue()
        {
            // Arrange
            var request = new UpdateRolePermissionRequest
            {
                Id = "perm-123",
                Role = UserRole.USER,
                Permission = "read"
            };

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            // Act
            var result = await _adminService.UpdateRolePermissionAsync(request);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task DeleteRolePermissionAsync_WithValidId_ReturnsTrue()
        {
            // Arrange
            var permissionId = "perm-123";

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            // Act
            var result = await _adminService.DeleteRolePermissionAsync(permissionId);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task SyncStripeDataAsync_WithValidRequest_ReturnsSuccessResponse()
        {
            // Arrange
            var request = new StripeSyncRequest
            {
                ForceSync = true,
                CustomerId = "cus_123"
            };

            // Act
            var result = await _adminService.SyncStripeDataAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Message.Should().Be("Stripe data sync completed successfully");
            result.SyncedCustomers.Should().Be(10);
            result.SyncedSubscriptions.Should().Be(5);
            result.SyncedPayments.Should().Be(15);
            result.Errors.Should().BeEmpty();
        }

        [Theory]
        [InlineData(UserRole.ADMIN)]
        [InlineData(UserRole.USER)]
        [InlineData(UserRole.PRODUCT)]
        public async Task UpdateUserRoleAsync_WithDifferentRoles_UpdatesCorrectly(UserRole role)
        {
            // Arrange
            var request = new UpdateUserRoleRequest
            {
                UserId = "user-123",
                Role = role
            };

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            // Act
            var result = await _adminService.UpdateUserRoleAsync(request);

            // Assert
            result.Should().BeTrue();
            
            // Verify the correct role was passed to the database
            _mockDbContext.Verify(x => x.ExecuteAsync(
                It.IsAny<string>(),
                It.Is<object>(p => p.GetType().GetProperty("role")!.GetValue(p)!.ToString() == role.ToString())),
                Times.Once);
        }
    }
}
