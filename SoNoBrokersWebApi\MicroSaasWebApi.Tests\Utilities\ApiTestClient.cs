using FluentAssertions;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;

namespace MicroSaasWebApi.Tests.Utilities
{
    /// <summary>
    /// Enhanced HTTP client for API testing with built-in assertions and utilities
    /// </summary>
    public class ApiTestClient : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly JsonSerializerOptions _jsonOptions;

        public ApiTestClient(HttpClient httpClient)
        {
            _httpClient = httpClient;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
        }

        #region Authentication Methods

        /// <summary>
        /// Sets the authorization header with Bearer token
        /// </summary>
        public ApiTestClient WithBearerToken(string token)
        {
            _httpClient.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            return this;
        }

        /// <summary>
        /// Sets the authorization header for admin user
        /// </summary>
        public ApiTestClient AsAdmin()
        {
            return WithBearerToken("admin-test-token");
        }

        /// <summary>
        /// Sets the authorization header for regular user
        /// </summary>
        public ApiTestClient AsUser()
        {
            return WithBearerToken("user-test-token");
        }

        /// <summary>
        /// Removes authorization header (anonymous user)
        /// </summary>
        public ApiTestClient AsAnonymous()
        {
            _httpClient.DefaultRequestHeaders.Authorization = null;
            return this;
        }

        #endregion

        #region GET Methods

        /// <summary>
        /// Performs GET request and expects success response
        /// </summary>
        public async Task<ApiResponse<T>> GetAsync<T>(string endpoint)
        {
            var response = await _httpClient.GetAsync(endpoint);
            return await ProcessResponse<T>(response);
        }

        /// <summary>
        /// Performs GET request and expects specific status code
        /// </summary>
        public async Task<ApiResponse<T>> GetAsync<T>(string endpoint, HttpStatusCode expectedStatusCode)
        {
            var response = await _httpClient.GetAsync(endpoint);
            response.StatusCode.Should().Be(expectedStatusCode);
            return await ProcessResponse<T>(response);
        }

        /// <summary>
        /// Performs GET request expecting success and returns data directly
        /// </summary>
        public async Task<T> GetSuccessAsync<T>(string endpoint)
        {
            var response = await GetAsync<T>(endpoint);
            response.IsSuccess.Should().BeTrue();
            return response.Data!;
        }

        #endregion

        #region POST Methods

        /// <summary>
        /// Performs POST request with JSON body
        /// </summary>
        public async Task<ApiResponse<T>> PostAsync<T>(string endpoint, object? data = null)
        {
            var response = await _httpClient.PostAsJsonAsync(endpoint, data, _jsonOptions);
            return await ProcessResponse<T>(response);
        }

        /// <summary>
        /// Performs POST request expecting specific status code
        /// </summary>
        public async Task<ApiResponse<T>> PostAsync<T>(string endpoint, object? data, HttpStatusCode expectedStatusCode)
        {
            var response = await _httpClient.PostAsJsonAsync(endpoint, data, _jsonOptions);
            response.StatusCode.Should().Be(expectedStatusCode);
            return await ProcessResponse<T>(response);
        }

        /// <summary>
        /// Performs POST request expecting success and returns data directly
        /// </summary>
        public async Task<T> PostSuccessAsync<T>(string endpoint, object? data = null)
        {
            var response = await PostAsync<T>(endpoint, data);
            response.IsSuccess.Should().BeTrue();
            return response.Data!;
        }

        /// <summary>
        /// Performs POST request expecting Created status
        /// </summary>
        public async Task<T> PostCreatedAsync<T>(string endpoint, object data)
        {
            var response = await PostAsync<T>(endpoint, data, HttpStatusCode.Created);
            return response.Data!;
        }

        #endregion

        #region PUT Methods

        /// <summary>
        /// Performs PUT request with JSON body
        /// </summary>
        public async Task<ApiResponse<T>> PutAsync<T>(string endpoint, object data)
        {
            var response = await _httpClient.PutAsJsonAsync(endpoint, data, _jsonOptions);
            return await ProcessResponse<T>(response);
        }

        /// <summary>
        /// Performs PUT request expecting success and returns data directly
        /// </summary>
        public async Task<T> PutSuccessAsync<T>(string endpoint, object data)
        {
            var response = await PutAsync<T>(endpoint, data);
            response.IsSuccess.Should().BeTrue();
            return response.Data!;
        }

        #endregion

        #region DELETE Methods

        /// <summary>
        /// Performs DELETE request
        /// </summary>
        public async Task<ApiResponse<T>> DeleteAsync<T>(string endpoint)
        {
            var response = await _httpClient.DeleteAsync(endpoint);
            return await ProcessResponse<T>(response);
        }

        /// <summary>
        /// Performs DELETE request expecting NoContent status
        /// </summary>
        public async Task DeleteSuccessAsync(string endpoint)
        {
            var response = await _httpClient.DeleteAsync(endpoint);
            response.StatusCode.Should().Be(HttpStatusCode.NoContent);
        }

        #endregion

        #region Assertion Methods

        /// <summary>
        /// Expects unauthorized response
        /// </summary>
        public async Task ExpectUnauthorizedAsync(string endpoint, HttpMethod? method = null, object? data = null)
        {
            method ??= HttpMethod.Get;
            
            var response = method.Method switch
            {
                "GET" => await _httpClient.GetAsync(endpoint),
                "POST" => await _httpClient.PostAsJsonAsync(endpoint, data, _jsonOptions),
                "PUT" => await _httpClient.PutAsJsonAsync(endpoint, data, _jsonOptions),
                "DELETE" => await _httpClient.DeleteAsync(endpoint),
                _ => throw new ArgumentException($"Unsupported HTTP method: {method}")
            };

            response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        }

        /// <summary>
        /// Expects forbidden response
        /// </summary>
        public async Task ExpectForbiddenAsync(string endpoint, HttpMethod? method = null, object? data = null)
        {
            method ??= HttpMethod.Get;
            
            var response = method.Method switch
            {
                "GET" => await _httpClient.GetAsync(endpoint),
                "POST" => await _httpClient.PostAsJsonAsync(endpoint, data, _jsonOptions),
                "PUT" => await _httpClient.PutAsJsonAsync(endpoint, data, _jsonOptions),
                "DELETE" => await _httpClient.DeleteAsync(endpoint),
                _ => throw new ArgumentException($"Unsupported HTTP method: {method}")
            };

            response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
        }

        /// <summary>
        /// Expects bad request response
        /// </summary>
        public async Task<string> ExpectBadRequestAsync(string endpoint, HttpMethod? method = null, object? data = null)
        {
            method ??= HttpMethod.Post;
            
            var response = method.Method switch
            {
                "GET" => await _httpClient.GetAsync(endpoint),
                "POST" => await _httpClient.PostAsJsonAsync(endpoint, data, _jsonOptions),
                "PUT" => await _httpClient.PutAsJsonAsync(endpoint, data, _jsonOptions),
                "DELETE" => await _httpClient.DeleteAsync(endpoint),
                _ => throw new ArgumentException($"Unsupported HTTP method: {method}")
            };

            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
            return await response.Content.ReadAsStringAsync();
        }

        /// <summary>
        /// Expects not found response
        /// </summary>
        public async Task ExpectNotFoundAsync(string endpoint)
        {
            var response = await _httpClient.GetAsync(endpoint);
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Measures response time for performance testing
        /// </summary>
        public async Task<(T data, TimeSpan responseTime)> GetWithTimingAsync<T>(string endpoint)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = await GetSuccessAsync<T>(endpoint);
            stopwatch.Stop();
            return (result, stopwatch.Elapsed);
        }

        /// <summary>
        /// Performs multiple concurrent requests
        /// </summary>
        public async Task<List<ApiResponse<T>>> GetConcurrentAsync<T>(string endpoint, int concurrentRequests)
        {
            var tasks = Enumerable.Range(0, concurrentRequests)
                .Select(_ => GetAsync<T>(endpoint))
                .ToArray();

            return (await Task.WhenAll(tasks)).ToList();
        }

        /// <summary>
        /// Polls endpoint until condition is met or timeout
        /// </summary>
        public async Task<T> PollUntilAsync<T>(string endpoint, Func<T, bool> condition, TimeSpan timeout, TimeSpan interval = default)
        {
            if (interval == default) interval = TimeSpan.FromSeconds(1);
            
            var endTime = DateTime.UtcNow.Add(timeout);
            
            while (DateTime.UtcNow < endTime)
            {
                try
                {
                    var result = await GetSuccessAsync<T>(endpoint);
                    if (condition(result))
                        return result;
                }
                catch
                {
                    // Continue polling on errors
                }
                
                await Task.Delay(interval);
            }
            
            throw new TimeoutException($"Condition not met within {timeout} for endpoint {endpoint}");
        }

        #endregion

        #region Private Methods

        private async Task<ApiResponse<T>> ProcessResponse<T>(HttpResponseMessage response)
        {
            var content = await response.Content.ReadAsStringAsync();
            
            var apiResponse = new ApiResponse<T>
            {
                StatusCode = response.StatusCode,
                IsSuccess = response.IsSuccessStatusCode,
                RawContent = content
            };

            if (response.IsSuccessStatusCode && !string.IsNullOrEmpty(content))
            {
                try
                {
                    apiResponse.Data = JsonSerializer.Deserialize<T>(content, _jsonOptions);
                }
                catch (JsonException ex)
                {
                    apiResponse.Error = $"Failed to deserialize response: {ex.Message}";
                }
            }
            else if (!response.IsSuccessStatusCode)
            {
                apiResponse.Error = content;
            }

            return apiResponse;
        }

        #endregion

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    /// <summary>
    /// API response wrapper for testing
    /// </summary>
    public class ApiResponse<T>
    {
        public HttpStatusCode StatusCode { get; set; }
        public bool IsSuccess { get; set; }
        public T? Data { get; set; }
        public string? Error { get; set; }
        public string RawContent { get; set; } = string.Empty;
    }

    /// <summary>
    /// Extension methods for ApiTestClient
    /// </summary>
    public static class ApiTestClientExtensions
    {
        /// <summary>
        /// Creates an ApiTestClient from HttpClient
        /// </summary>
        public static ApiTestClient AsApiClient(this HttpClient httpClient)
        {
            return new ApiTestClient(httpClient);
        }

        /// <summary>
        /// Adds custom headers to the client
        /// </summary>
        public static ApiTestClient WithHeader(this ApiTestClient client, string name, string value)
        {
            // Note: This would require access to the internal HttpClient
            // For now, this is a placeholder for the pattern
            return client;
        }

        /// <summary>
        /// Sets content type header
        /// </summary>
        public static ApiTestClient WithContentType(this ApiTestClient client, string contentType)
        {
            // Note: This would require access to the internal HttpClient
            return client;
        }
    }
}
