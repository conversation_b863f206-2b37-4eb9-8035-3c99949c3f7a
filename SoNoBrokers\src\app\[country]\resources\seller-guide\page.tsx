import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function SellerGuidePage({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us', 'uae']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/resources/seller-guide')
  }

  // Default to seller for this page
  const userType = resolvedSearchParams.userType || 'seller'

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8">
            Complete Seller's Guide
          </h1>

          <div className="prose prose-lg max-w-none">
            <div className="bg-card rounded-lg p-6 mb-8">
              <h2 className="text-2xl font-semibold mb-4">Getting Started</h2>
              <p className="text-muted-foreground mb-4">
                Selling your home can be a complex process, but with the right guidance and tools,
                you can maximize your property's value and minimize stress. This comprehensive guide
                will walk you through every step of the selling process.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-6 mb-8">
              <div className="bg-card rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-3">Preparation Phase</h3>
                <ul className="space-y-2 text-sm">
                  <li>• Property valuation and pricing strategy</li>
                  <li>• Home staging and improvements</li>
                  <li>• Professional photography</li>
                  <li>• Legal document preparation</li>
                </ul>
              </div>

              <div className="bg-card rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-3">Marketing Phase</h3>
                <ul className="space-y-2 text-sm">
                  <li>• Online listing optimization</li>
                  <li>• Social media marketing</li>
                  <li>• Open house scheduling</li>
                  <li>• Buyer screening process</li>
                </ul>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6">
              <h2 className="text-2xl font-semibold mb-4">Key Steps to Success</h2>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold">1. Price Your Home Competitively</h4>
                  <p className="text-sm text-muted-foreground">
                    Use our pricing calculator and market analysis tools to set the right price.
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold">2. Enhance Your Property's Appeal</h4>
                  <p className="text-sm text-muted-foreground">
                    Consider staging services and professional photography to showcase your home.
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold">3. Market Effectively</h4>
                  <p className="text-sm text-muted-foreground">
                    Leverage our marketing services to reach qualified buyers quickly.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'seller'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Complete Seller's Guide for ${country} | SoNoBrokers`,
    description: `Comprehensive guide to selling your property in ${country}. Expert tips, strategies, and tools to maximize your sale price and minimize stress.`,
    keywords: `seller guide, property selling, real estate tips, ${country}, home selling guide`,
  }
}
