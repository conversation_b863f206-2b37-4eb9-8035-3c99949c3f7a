using MicroSaasWebApi.Models.SoNoBrokers;

namespace MicroSaasWebApi.Services.SoNoBrokers.Interfaces
{
    public interface IPropertySearchService
    {
        Task<IEnumerable<Property>> SearchPropertiesAsync(PropertySearchRequest request);
        Task<Property?> GetPropertyByIdAsync(string propertyId);
    }

    public class PropertySearchRequest
    {
        public string? Location { get; set; }
        public string? MlsNumber { get; set; }
        public string? PropertyType { get; set; }
        public string? AdType { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public int? MinYear { get; set; }
        public int? MaxYear { get; set; }
        public int? Bedrooms { get; set; }
        public decimal? Bathrooms { get; set; }
        public int? Storeys { get; set; }
        public string? ParkingTypes { get; set; }
        public string? ExtraFeatures { get; set; }
    }
}
