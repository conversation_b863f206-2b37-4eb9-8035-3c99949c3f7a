using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.Core;

using System.Text.Json;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/sonobrokers/geo")]
    [Tags("Geolocation")]
    [AllowAnonymous]
    public class GeoController : ControllerBase
    {
        private readonly ILogger<GeoController> _logger;
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;

        public GeoController(ILogger<GeoController> logger, IConfiguration configuration, HttpClient httpClient)
        {
            _logger = logger;
            _configuration = configuration;
            _httpClient = httpClient;
        }

        /// <summary>
        /// Get geolocation data for an address
        /// </summary>
        /// <param name="address">Address to geocode</param>
        /// <returns>Geolocation data</returns>
        [HttpGet("geocode")]




        public async Task<ActionResult<ApiResponse<GeocodeResponse>>> GeocodeAddress([FromQuery] string address)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(address))
                {
                    return BadRequest(ApiResponse<GeocodeResponse>.ErrorResult("Address is required"));
                }

                var mapboxApiKey = _configuration["Mapbox:ApiKey"];
                if (string.IsNullOrEmpty(mapboxApiKey))
                {
                    _logger.LogError("Mapbox API key not configured");
                    return StatusCode(500, ApiResponse<GeocodeResponse>.ErrorResult("Geocoding service not configured"));
                }

                var encodedAddress = Uri.EscapeDataString(address);
                var url = $"https://api.mapbox.com/geocoding/v5/mapbox.places/{encodedAddress}.json?access_token={mapboxApiKey}&limit=1";

                var response = await _httpClient.GetAsync(url);
                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("Mapbox API request failed with status: {StatusCode}", response.StatusCode);
                    return StatusCode(500, ApiResponse<GeocodeResponse>.ErrorResult("Geocoding service error"));
                }

                var jsonContent = await response.Content.ReadAsStringAsync();
                var mapboxResponse = JsonSerializer.Deserialize<MapboxGeocodeResponse>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                if (mapboxResponse?.Features == null || !mapboxResponse.Features.Any())
                {
                    return NotFound(ApiResponse<GeocodeResponse>.ErrorResult("Address not found"));
                }

                var feature = mapboxResponse.Features.First();
                var coordinates = feature.Geometry?.Coordinates;

                if (coordinates == null || coordinates.Length < 2)
                {
                    return StatusCode(500, ApiResponse<GeocodeResponse>.ErrorResult("Invalid coordinates received"));
                }

                var geocodeResponse = new GeocodeResponse
                {
                    Address = feature.PlaceName ?? address,
                    Latitude = coordinates[1], // Mapbox returns [longitude, latitude]
                    Longitude = coordinates[0],
                    FormattedAddress = feature.PlaceName,
                    PlaceId = feature.Id,
                    Context = feature.Context?.Select(c => new AddressComponent
                    {
                        Type = c.Id?.Split('.').FirstOrDefault() ?? "unknown",
                        Value = c.Text ?? ""
                    }).ToList()
                };

                return Ok(ApiResponse<GeocodeResponse>.SuccessResult(geocodeResponse, "Address geocoded successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error geocoding address: {Address}", address);
                return StatusCode(500, ApiResponse<GeocodeResponse>.ErrorResult("Failed to geocode address"));
            }
        }

        /// <summary>
        /// Reverse geocode coordinates to get address
        /// </summary>
        /// <param name="latitude">Latitude</param>
        /// <param name="longitude">Longitude</param>
        /// <returns>Address information</returns>
        [HttpGet("reverse")]




        public async Task<ActionResult<ApiResponse<GeocodeResponse>>> ReverseGeocode(
            [FromQuery] double latitude,
            [FromQuery] double longitude)
        {
            try
            {
                if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180)
                {
                    return BadRequest(ApiResponse<GeocodeResponse>.ErrorResult("Invalid coordinates"));
                }

                var mapboxApiKey = _configuration["Mapbox:ApiKey"];
                if (string.IsNullOrEmpty(mapboxApiKey))
                {
                    _logger.LogError("Mapbox API key not configured");
                    return StatusCode(500, ApiResponse<GeocodeResponse>.ErrorResult("Geocoding service not configured"));
                }

                var url = $"https://api.mapbox.com/geocoding/v5/mapbox.places/{longitude},{latitude}.json?access_token={mapboxApiKey}&limit=1";

                var response = await _httpClient.GetAsync(url);
                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("Mapbox API request failed with status: {StatusCode}", response.StatusCode);
                    return StatusCode(500, ApiResponse<GeocodeResponse>.ErrorResult("Geocoding service error"));
                }

                var jsonContent = await response.Content.ReadAsStringAsync();
                var mapboxResponse = JsonSerializer.Deserialize<MapboxGeocodeResponse>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                if (mapboxResponse?.Features == null || !mapboxResponse.Features.Any())
                {
                    return NotFound(ApiResponse<GeocodeResponse>.ErrorResult("Address not found for coordinates"));
                }

                var feature = mapboxResponse.Features.First();
                var geocodeResponse = new GeocodeResponse
                {
                    Address = feature.PlaceName ?? $"{latitude}, {longitude}",
                    Latitude = latitude,
                    Longitude = longitude,
                    FormattedAddress = feature.PlaceName,
                    PlaceId = feature.Id,
                    Context = feature.Context?.Select(c => new AddressComponent
                    {
                        Type = c.Id?.Split('.').FirstOrDefault() ?? "unknown",
                        Value = c.Text ?? ""
                    }).ToList()
                };

                return Ok(ApiResponse<GeocodeResponse>.SuccessResult(geocodeResponse, "Coordinates reverse geocoded successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reverse geocoding coordinates: {Latitude}, {Longitude}", latitude, longitude);
                return StatusCode(500, ApiResponse<GeocodeResponse>.ErrorResult("Failed to reverse geocode coordinates"));
            }
        }
    }

    public class GeocodeResponse
    {
        public string Address { get; set; } = string.Empty;
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string? FormattedAddress { get; set; }
        public string? PlaceId { get; set; }
        public List<AddressComponent>? Context { get; set; }
    }

    public class AddressComponent
    {
        public string Type { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
    }

    public class MapboxGeocodeResponse
    {
        public List<MapboxFeature>? Features { get; set; }
    }

    public class MapboxFeature
    {
        public string? Id { get; set; }
        public string? PlaceName { get; set; }
        public MapboxGeometry? Geometry { get; set; }
        public List<MapboxContext>? Context { get; set; }
    }

    public class MapboxGeometry
    {
        public double[]? Coordinates { get; set; }
    }

    public class MapboxContext
    {
        public string? Id { get; set; }
        public string? Text { get; set; }
    }
}
