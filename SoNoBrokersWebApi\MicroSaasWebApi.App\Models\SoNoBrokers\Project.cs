using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    [Table("Project", Schema = "snb")]
    public class Project
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string ConnectionId { get; set; } = string.Empty;

        [Required]
        public string WebhookId { get; set; } = string.Empty;

        [Required]
        public string ScenarioId { get; set; } = string.Empty;

        [Required]
        public string UserClerkId { get; set; } = string.Empty;

        [Required]
        public string WebhookLink { get; set; } = string.Empty;

        public string? AssistantId { get; set; }

        [Required]
        public string Type { get; set; } = string.Empty;

        public string Status { get; set; } = "default";

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
