using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Services.Payment.Interfaces;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.Core;

using Stripe;
using Stripe.Checkout;
using System.Security.Claims;
using StripePortal = Stripe.BillingPortal;

namespace MicroSaasWebApi.Controllers.Core.Stripe
{
    /// <summary>
    /// Stripe payments controller implementing SOLID principles
    /// Handles Stripe payment operations including one-time payments and subscriptions
    /// </summary>
    [Route("api/stripe/payments")]
    [ApiController]
    [Authorize]
    public class StripePaymentsController : ControllerBase
    {
        private readonly IStripePaymentService _stripeService;
        private readonly ILogger<StripePaymentsController> _logger;
        private readonly IDapperDbContext _dbContext;
        private readonly IConfiguration _configuration;

        public StripePaymentsController(
            IStripePaymentService stripeService,
            ILogger<StripePaymentsController> logger,
            IDapperDbContext dbContext,
            IConfiguration configuration)
        {
            _stripeService = stripeService;
            _logger = logger;
            _dbContext = dbContext;
            _configuration = configuration;
        }

        #region Customer Management

        /// <summary>
        /// Create a new Stripe customer
        /// </summary>
        [HttpPost("customers")]

        public async Task<IActionResult> CreateCustomer([FromBody] CreateCustomerRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var customer = await _stripeService.CreateCustomerAsync(
                    request.Email,
                    request.Name,
                    request.Description);

                if (customer == null)
                {
                    return BadRequest("Failed to create customer");
                }

                return Ok(customer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating Stripe customer: {Email}", request.Email);
                return StatusCode(500, "Failed to create customer");
            }
        }

        /// <summary>
        /// Get customer by ID
        /// </summary>
        [HttpGet("customers/{customerId}")]

        public async Task<IActionResult> GetCustomer(string customerId)
        {
            try
            {
                var customer = await _stripeService.GetCustomerAsync(customerId);

                if (customer == null)
                {
                    return NotFound("Customer not found");
                }

                return Ok(customer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting Stripe customer: {CustomerId}", customerId);
                return StatusCode(500, "Failed to get customer");
            }
        }

        /// <summary>
        /// Update customer
        /// </summary>
        [HttpPut("customers/{customerId}")]

        public async Task<IActionResult> UpdateCustomer(string customerId, [FromBody] UpdateCustomerRequest request)
        {
            try
            {
                var customer = await _stripeService.UpdateCustomerAsync(
                    customerId,
                    request.Email,
                    request.Name,
                    request.Description);

                if (customer == null)
                {
                    return BadRequest("Failed to update customer");
                }

                return Ok(customer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating Stripe customer: {CustomerId}", customerId);
                return StatusCode(500, "Failed to update customer");
            }
        }

        /// <summary>
        /// Delete customer
        /// </summary>
        [HttpDelete("customers/{customerId}")]
        [Authorize(Policy = "CanProcessPayments")]

        public async Task<IActionResult> DeleteCustomer(string customerId)
        {
            try
            {
                var result = await _stripeService.DeleteCustomerAsync(customerId);

                if (!result)
                {
                    return BadRequest("Failed to delete customer");
                }

                return Ok("Customer deleted successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting Stripe customer: {CustomerId}", customerId);
                return StatusCode(500, "Failed to delete customer");
            }
        }

        #endregion

        #region Payment Intents

        /// <summary>
        /// Create payment intent
        /// </summary>
        [HttpPost("payment-intents")]
        [Authorize(Policy = "CanProcessPayments")]

        public async Task<IActionResult> CreatePaymentIntent([FromBody] CreatePaymentIntentRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var paymentIntent = await _stripeService.CreatePaymentIntentAsync(
                    request.Amount,
                    request.Currency,
                    request.CustomerId,
                    request.PaymentMethodId,
                    request.ConfirmImmediately);

                if (paymentIntent == null)
                {
                    return BadRequest("Failed to create payment intent");
                }

                return Ok(paymentIntent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating payment intent for customer: {CustomerId}", request.CustomerId);
                return StatusCode(500, "Failed to create payment intent");
            }
        }

        /// <summary>
        /// Confirm payment intent
        /// </summary>
        [HttpPost("payment-intents/{paymentIntentId}/confirm")]
        [Authorize(Policy = "CanProcessPayments")]

        public async Task<IActionResult> ConfirmPaymentIntent(string paymentIntentId, [FromBody] ConfirmPaymentIntentRequest request)
        {
            try
            {
                var paymentIntent = await _stripeService.ConfirmPaymentIntentAsync(paymentIntentId, request.PaymentMethodId);

                if (paymentIntent == null)
                {
                    return BadRequest("Failed to confirm payment intent");
                }

                return Ok(paymentIntent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error confirming payment intent: {PaymentIntentId}", paymentIntentId);
                return StatusCode(500, "Failed to confirm payment intent");
            }
        }

        #endregion

        #region Subscriptions

        /// <summary>
        /// Create subscription
        /// </summary>
        [HttpPost("subscriptions")]
        [Authorize(Policy = "CanProcessPayments")]

        public async Task<IActionResult> CreateSubscription([FromBody] CreateSubscriptionRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var subscription = await _stripeService.CreateSubscriptionAsync(
                    request.CustomerId,
                    request.PriceId,
                    request.TrialPeriodDays);

                if (subscription == null)
                {
                    return BadRequest("Failed to create subscription");
                }

                return Ok(subscription);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating subscription for customer: {CustomerId}", request.CustomerId);
                return StatusCode(500, "Failed to create subscription");
            }
        }

        /// <summary>
        /// Cancel subscription
        /// </summary>
        [HttpPost("subscriptions/{subscriptionId}/cancel")]
        [Authorize(Policy = "CanProcessPayments")]

        public async Task<IActionResult> CancelSubscription(string subscriptionId, [FromBody] CancelSubscriptionRequest request)
        {
            try
            {
                var subscription = await _stripeService.CancelSubscriptionAsync(subscriptionId, request.CancelImmediately);

                if (subscription == null)
                {
                    return BadRequest("Failed to cancel subscription");
                }

                return Ok(subscription);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error canceling subscription: {SubscriptionId}", subscriptionId);
                return StatusCode(500, "Failed to cancel subscription");
            }
        }

        #endregion

        #region Webhooks

        /// <summary>
        /// Handle Stripe webhooks
        /// </summary>
        [HttpPost("webhooks")]
        [AllowAnonymous]

        public async Task<IActionResult> HandleWebhook()
        {
            try
            {
                var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
                var signature = Request.Headers["Stripe-Signature"];
                var endpointSecret = Environment.GetEnvironmentVariable("STRIPE_WEBHOOK_SECRET") ?? "";

                var stripeEvent = await _stripeService.ConstructWebhookEventAsync(json, signature, endpointSecret);

                if (stripeEvent == null)
                {
                    return BadRequest("Invalid webhook signature");
                }

                var handled = await _stripeService.HandleWebhookEventAsync(stripeEvent);

                if (!handled)
                {
                    _logger.LogWarning("Unhandled webhook event type: {EventType}", stripeEvent.Type);
                }

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling Stripe webhook");
                return StatusCode(500, "Webhook handling failed");
            }
        }

        #endregion

        #region SoNoBrokers Specific Operations

        /// <summary>
        /// Create Stripe checkout session for SoNoBrokers subscription
        /// </summary>
        /// <param name="request">Checkout session details</param>
        /// <returns>Checkout session URL</returns>
        [HttpPost("sonobrokers/create-checkout")]





        public async Task<ActionResult<ApiResponse<SoNoBrokersCheckoutSessionResponse>>> CreateSoNoBrokersCheckoutSession([FromBody] SoNoBrokersCreateCheckoutRequest request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(ApiResponse<SoNoBrokersCheckoutSessionResponse>.ErrorResult("User not authenticated"));
                }

                if (string.IsNullOrWhiteSpace(request.PriceId))
                {
                    return BadRequest(ApiResponse<SoNoBrokersCheckoutSessionResponse>.ErrorResult("Price ID is required"));
                }

                if (string.IsNullOrWhiteSpace(request.Email))
                {
                    return BadRequest(ApiResponse<SoNoBrokersCheckoutSessionResponse>.ErrorResult("Email is required"));
                }

                var stripeSecretKey = _configuration["ExternalServices:Stripe:SecretKey"];
                if (string.IsNullOrEmpty(stripeSecretKey))
                {
                    _logger.LogError("Stripe secret key not configured");
                    return StatusCode(500, ApiResponse<SoNoBrokersCheckoutSessionResponse>.ErrorResult("Payment service not configured"));
                }

                var appUrl = _configuration["SoNoBrokers:AppUrl"] ?? "http://localhost:3000";

                var options = new SessionCreateOptions
                {
                    PaymentMethodTypes = new List<string> { "card" },
                    LineItems = new List<SessionLineItemOptions>
                    {
                        new SessionLineItemOptions
                        {
                            Price = request.PriceId,
                            Quantity = 1,
                        },
                    },
                    Mode = "subscription",
                    SuccessUrl = $"{appUrl}/dashboard?session_id={{CHECKOUT_SESSION_ID}}",
                    CancelUrl = $"{appUrl}/pricing",
                    CustomerEmail = request.Email,
                    Metadata = new Dictionary<string, string>
                    {
                        { "userId", userId },
                        { "planType", request.PlanType ?? "unknown" }
                    },
                    SubscriptionData = new SessionSubscriptionDataOptions
                    {
                        Metadata = new Dictionary<string, string>
                        {
                            { "userId", userId },
                            { "planType", request.PlanType ?? "unknown" }
                        }
                    }
                };

                var service = new SessionService();
                var session = await service.CreateAsync(options);

                var response = new SoNoBrokersCheckoutSessionResponse
                {
                    SessionId = session.Id,
                    Url = session.Url,
                    PublishableKey = _configuration["ExternalServices:Stripe:PublishableKey"] ?? ""
                };

                return Ok(ApiResponse<SoNoBrokersCheckoutSessionResponse>.SuccessResult(response, "Checkout session created successfully"));
            }
            catch (StripeException ex)
            {
                _logger.LogError(ex, "Stripe error creating checkout session");
                return StatusCode(500, ApiResponse<SoNoBrokersCheckoutSessionResponse>.ErrorResult($"Payment service error: {ex.Message}"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating checkout session");
                return StatusCode(500, ApiResponse<SoNoBrokersCheckoutSessionResponse>.ErrorResult("Failed to create checkout session"));
            }
        }

        /// <summary>
        /// Create Stripe customer portal session for SoNoBrokers
        /// </summary>
        /// <returns>Customer portal URL</returns>
        [HttpPost("sonobrokers/create-portal")]





        public async Task<ActionResult<ApiResponse<SoNoBrokersCustomerPortalResponse>>> CreateSoNoBrokersCustomerPortal()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(ApiResponse<SoNoBrokersCustomerPortalResponse>.ErrorResult("User not authenticated"));
                }

                var stripeSecretKey = _configuration["ExternalServices:Stripe:SecretKey"];
                if (string.IsNullOrEmpty(stripeSecretKey))
                {
                    _logger.LogError("Stripe secret key not configured");
                    return StatusCode(500, ApiResponse<SoNoBrokersCustomerPortalResponse>.ErrorResult("Payment service not configured"));
                }

                // Find user's subscription
                const string sql = @"
                    SELECT * FROM snb.subscriptions
                    WHERE user_id = @userId
                    ORDER BY created_at DESC
                    LIMIT 1";

                var subscription = await _dbContext.QueryFirstOrDefaultAsync<Models.SoNoBrokers.Subscription>(sql, new { userId });

                if (subscription == null || string.IsNullOrEmpty(subscription.StripeSubscriptionId))
                {
                    return BadRequest(ApiResponse<SoNoBrokersCustomerPortalResponse>.ErrorResult(
                        "You don't have a billing account yet. Make a purchase first."));
                }

                var appUrl = _configuration["SoNoBrokers:AppUrl"] ?? "http://localhost:3000";

                var options = new StripePortal.SessionCreateOptions
                {
                    Customer = subscription.StripeCustomerId,
                    ReturnUrl = $"{appUrl}/dashboard",
                };

                var service = new StripePortal.SessionService();
                var portalSession = await service.CreateAsync(options);

                var response = new SoNoBrokersCustomerPortalResponse
                {
                    Url = portalSession.Url
                };

                return Ok(ApiResponse<SoNoBrokersCustomerPortalResponse>.SuccessResult(response, "Customer portal created successfully"));
            }
            catch (StripeException ex)
            {
                _logger.LogError(ex, "Stripe error creating customer portal");
                return StatusCode(500, ApiResponse<SoNoBrokersCustomerPortalResponse>.ErrorResult($"Payment service error: {ex.Message}"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating customer portal");
                return StatusCode(500, ApiResponse<SoNoBrokersCustomerPortalResponse>.ErrorResult("Failed to create customer portal"));
            }
        }

        /// <summary>
        /// Get subscription status for current SoNoBrokers user
        /// </summary>
        /// <returns>Subscription status</returns>
        [HttpGet("sonobrokers/subscription-status")]



        public async Task<ActionResult<ApiResponse<SoNoBrokersSubscriptionStatusResponse>>> GetSoNoBrokersSubscriptionStatus()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            try
            {
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(ApiResponse<SoNoBrokersSubscriptionStatusResponse>.ErrorResult("User not authenticated"));
                }

                const string sql = @"
                    SELECT * FROM snb.subscriptions
                    WHERE user_id = @userId
                    ORDER BY created_at DESC
                    LIMIT 1";

                var subscription = await _dbContext.QueryFirstOrDefaultAsync<Models.SoNoBrokers.Subscription>(sql, new { userId });

                var response = new SoNoBrokersSubscriptionStatusResponse
                {
                    HasActiveSubscription = subscription != null && subscription.Status == "active",
                    Status = subscription?.Status ?? "none",
                    PlanType = subscription?.PlanType ?? "none",
                    CurrentPeriodEnd = subscription?.EndsAt,
                    StripeSubscriptionId = subscription?.StripeSubscriptionId
                };

                return Ok(ApiResponse<SoNoBrokersSubscriptionStatusResponse>.SuccessResult(response, "Subscription status retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving subscription status for user: {UserId}", userId ?? "unknown");
                return StatusCode(500, ApiResponse<SoNoBrokersSubscriptionStatusResponse>.ErrorResult("Failed to retrieve subscription status"));
            }
        }

        #endregion
    }

    #region Request Models

    public class CreateCustomerRequest
    {
        public string Email { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
    }

    public class UpdateCustomerRequest
    {
        public string? Email { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
    }

    public class CreatePaymentIntentRequest
    {
        public long Amount { get; set; }
        public string Currency { get; set; } = "usd";
        public string CustomerId { get; set; } = string.Empty;
        public string? PaymentMethodId { get; set; }
        public bool ConfirmImmediately { get; set; } = false;
    }

    public class ConfirmPaymentIntentRequest
    {
        public string? PaymentMethodId { get; set; }
    }

    public class CreateSubscriptionRequest
    {
        public string CustomerId { get; set; } = string.Empty;
        public string PriceId { get; set; } = string.Empty;
        public int? TrialPeriodDays { get; set; }
    }

    public class CancelSubscriptionRequest
    {
        public bool CancelImmediately { get; set; } = false;
    }

    // SoNoBrokers specific models
    public class SoNoBrokersCreateCheckoutRequest
    {
        public string PriceId { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? PlanType { get; set; }
    }

    public class SoNoBrokersCheckoutSessionResponse
    {
        public string SessionId { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public string PublishableKey { get; set; } = string.Empty;
    }

    public class SoNoBrokersCustomerPortalResponse
    {
        public string Url { get; set; } = string.Empty;
    }

    public class SoNoBrokersSubscriptionStatusResponse
    {
        public bool HasActiveSubscription { get; set; }
        public string Status { get; set; } = string.Empty;
        public string PlanType { get; set; } = string.Empty;
        public DateTime? CurrentPeriodEnd { get; set; }
        public string? StripeSubscriptionId { get; set; }
    }

    // Property Listing Payment Request
    public class PropertyListingPaymentRequest
    {
        public string PropertyId { get; set; } = string.Empty;
        public string PropertyType { get; set; } = string.Empty; // Residential, Commercial, Land, Rental
        public string Country { get; set; } = string.Empty; // USA, Canada, UAE
        public string Email { get; set; } = string.Empty;
    }

    // Advertiser Subscription Request
    public class AdvertiserSubscriptionRequest
    {
        public string SubscriptionPlan { get; set; } = string.Empty; // basic, premium, enterprise
        public string Country { get; set; } = string.Empty; // USA, Canada, UAE
        public string Email { get; set; } = string.Empty;
    }

    // Concierge Services Payment Request
    public class ConciergeServicePaymentRequest
    {
        public string ServiceType { get; set; } = string.Empty; // consultation, property_management, legal_assistance, etc.
        public string Country { get; set; } = string.Empty; // USA, Canada, UAE
        public string Email { get; set; } = string.Empty;
        public string? ProjectId { get; set; } // Optional project/service ID
    }

    #endregion
}
