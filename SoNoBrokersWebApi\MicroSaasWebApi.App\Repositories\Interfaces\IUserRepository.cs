using MicroSaasWebApi.Models.SoNoBrokers;

namespace MicroSaasWebApi.App.Repositories.Interfaces
{
    /// <summary>
    /// Interface for user repository operations
    /// </summary>
    public interface IUserRepository
    {
        // Basic CRUD operations
        Task<IEnumerable<User>> GetAllAsync();
        Task<User?> GetByIdAsync(string id);
        Task<User?> GetByEmailAsync(string email);
        Task<User?> GetByClerkIdAsync(string clerkId);
        Task<string> CreateAsync(User user);
        Task<bool> UpdateAsync(User user);
        Task<bool> DeleteAsync(string id);

        // User-specific operations
        Task<IEnumerable<User>> GetByRoleAsync(UserRole role);
        Task<IEnumerable<User>> GetByUserTypeAsync(SnbUserType userType);
        Task<bool> UpdateLastLoginAsync(string id);
        Task<bool> UpdateStatusAsync(string id, bool isActive);
        Task<int> GetCountByRoleAsync(UserRole role);
        Task<int> GetActiveUsersCountAsync();

        // Search and filtering
        Task<(IEnumerable<User> Users, int TotalCount)> SearchAsync(
            string? searchTerm = null,
            UserRole? role = null,
            SnbUserType? userType = null,
            bool? isActive = null,
            int page = 1,
            int pageSize = 20);
    }
}
