import { auth } from '@clerk/nextjs/server'
import { apiClient } from '@/lib/api-client'

/**
 * Contact Sharing API Service
 * Handles buyer-seller contact sharing and property offers
 */

// Types
export interface ContactShare {
  id: string
  propertyId: string
  propertyTitle: string
  propertyAddress: string
  propertyPrice: number
  buyerId: string
  buyerName: string
  buyerEmail: string
  buyerPhone?: string
  sellerId: string
  sellerName: string
  sellerEmail: string
  message?: string
  shareType: ContactShareType
  shareTypeDisplay: string
  offerAmount?: number
  schedulingPreference?: string
  preferredVisitDate?: string
  preferredVisitTime?: string
  status: ContactShareStatus
  statusDisplay: string
  createdAt: string
  respondedAt?: string
  sellerResponse?: string
  emailSent: boolean
  emailSentAt?: string
}

export enum ContactShareType {
  ContactRequest = 1,
  PropertyOffer = 2,
  ScheduleVisit = 3,
  OfferWithVisit = 4
}

export enum ContactShareStatus {
  Sent = 1,
  Delivered = 2,
  Viewed = 3,
  Responded = 4,
  Accepted = 5,
  Declined = 6,
  Expired = 7
}

export interface CreateContactShareRequest {
  propertyId: string
  sellerId: string
  buyerName: string
  buyerEmail: string
  buyerPhone?: string
  message?: string
  shareType: ContactShareType
  offerAmount?: number
  schedulingPreference?: string
  preferredVisitDate?: string
  preferredVisitTime?: string
}

export interface ContactShareSellerResponse {
  contactShareId: string
  status: ContactShareStatus
  response?: string
  counterOfferAmount?: number
  alternativeVisitDate?: string
  alternativeVisitTime?: string
}

export interface ContactShareSearchParams {
  propertyId?: string
  buyerId?: string
  sellerId?: string
  shareType?: ContactShareType
  status?: ContactShareStatus
  fromDate?: string
  toDate?: string
  minOfferAmount?: number
  maxOfferAmount?: number
  search?: string
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: string
}

export interface ContactShareSearchResponse {
  contactShares: ContactShare[]
  total: number
  page: number
  totalPages: number
  hasMore: boolean
}

export interface ContactShareStats {
  totalContactShares: number
  contactRequests: number
  propertyOffers: number
  scheduleRequests: number
  pendingResponses: number
  acceptedRequests: number
  declinedRequests: number
  averageOfferAmount: number
  highestOffer: number
  lastContactShare?: string
  propertyStats: ContactSharePropertyStats[]
}

export interface ContactSharePropertyStats {
  propertyId: string
  propertyTitle: string
  contactCount: number
  offerCount: number
  highestOffer?: number
  lastContact?: string
}

/**
 * Server Action: Create a new contact share request
 */
export async function createContactShare(request: CreateContactShareRequest): Promise<ContactShare> {
  try {
    const { getToken } = auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const contactShare = await apiClient.post<ContactShare>('/api/sonobrokers/contact-sharing', request)
    return contactShare
  } catch (error) {
    console.error('Failed to create contact share:', error)
    throw error
  }
}

/**
 * Server Action: Get contact share by ID
 */
export async function getContactShare(contactShareId: string): Promise<ContactShare | null> {
  try {
    const { getToken } = auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const contactShare = await apiClient.get<ContactShare>(`/api/sonobrokers/contact-sharing/${contactShareId}`)
    return contactShare
  } catch (error) {
    console.error('Failed to get contact share:', error)
    return null
  }
}

/**
 * Server Action: Get contact shares for current user
 */
export async function getContactShares(searchParams: ContactShareSearchParams = {}): Promise<ContactShareSearchResponse> {
  try {
    const { getToken } = auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const queryParams = new URLSearchParams()
    Object.entries(searchParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value))
      }
    })

    const response = await apiClient.get<ContactShareSearchResponse>(`/api/sonobrokers/contact-sharing?${queryParams}`)
    return response
  } catch (error) {
    console.error('Failed to get contact shares:', error)
    throw error
  }
}

/**
 * Server Action: Get contact shares for a property
 */
export async function getPropertyContactShares(propertyId: string, searchParams: ContactShareSearchParams = {}): Promise<ContactShareSearchResponse> {
  try {
    const { getToken } = auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const queryParams = new URLSearchParams()
    Object.entries(searchParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value))
      }
    })

    const response = await apiClient.get<ContactShareSearchResponse>(`/api/sonobrokers/contact-sharing/property/${propertyId}?${queryParams}`)
    return response
  } catch (error) {
    console.error('Failed to get property contact shares:', error)
    throw error
  }
}

/**
 * Server Action: Respond to contact share (seller)
 */
export async function respondToContactShare(contactShareId: string, response: Omit<ContactShareSellerResponse, 'contactShareId'>): Promise<boolean> {
  try {
    const { getToken } = auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    await apiClient.put(`/api/sonobrokers/contact-sharing/${contactShareId}/respond`, response)
    return true
  } catch (error) {
    console.error('Failed to respond to contact share:', error)
    return false
  }
}

/**
 * Server Action: Get contact share statistics
 */
export async function getContactShareStats(): Promise<ContactShareStats> {
  try {
    const { getToken } = auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const stats = await apiClient.get<ContactShareStats>('/api/sonobrokers/contact-sharing/stats')
    return stats
  } catch (error) {
    console.error('Failed to get contact share stats:', error)
    throw error
  }
}

/**
 * Server Action: Get property contact share statistics
 */
export async function getPropertyContactShareStats(propertyId: string): Promise<ContactShareStats> {
  try {
    const { getToken } = auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const stats = await apiClient.get<ContactShareStats>(`/api/sonobrokers/contact-sharing/property/${propertyId}/stats`)
    return stats
  } catch (error) {
    console.error('Failed to get property contact share stats:', error)
    throw error
  }
}

/**
 * Server Action: Delete contact share
 */
export async function deleteContactShare(contactShareId: string): Promise<boolean> {
  try {
    const { getToken } = auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    await apiClient.delete(`/api/sonobrokers/contact-sharing/${contactShareId}`)
    return true
  } catch (error) {
    console.error('Failed to delete contact share:', error)
    return false
  }
}
