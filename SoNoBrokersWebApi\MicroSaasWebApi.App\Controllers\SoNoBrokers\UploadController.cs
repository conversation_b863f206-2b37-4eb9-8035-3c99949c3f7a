using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.Core;

using System.Security.Claims;
using Azure.Storage.Blobs;
using Azure.Storage.Sas;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/sonobrokers/[controller]")]
    [Authorize]
    public class UploadController : ControllerBase
    {
        private readonly ILogger<UploadController> _logger;
        private readonly IConfiguration _configuration;

        public UploadController(ILogger<UploadController> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Get SAS token for Azure Blob storage upload
        /// </summary>
        /// <param name="request">Upload request details</param>
        /// <returns>SAS token and upload URL</returns>
        [HttpPost("get-sas-token")]





        public Task<ActionResult<ApiResponse<SasTokenResponse>>> GetSasToken([FromBody] SasTokenRequest request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Task.FromResult<ActionResult<ApiResponse<SasTokenResponse>>>(Unauthorized(ApiResponse<SasTokenResponse>.ErrorResult("User not authenticated")));
                }

                if (string.IsNullOrWhiteSpace(request.FileName))
                {
                    return Task.FromResult<ActionResult<ApiResponse<SasTokenResponse>>>(BadRequest(ApiResponse<SasTokenResponse>.ErrorResult("File name is required")));
                }

                if (string.IsNullOrWhiteSpace(request.ContentType))
                {
                    return Task.FromResult<ActionResult<ApiResponse<SasTokenResponse>>>(BadRequest(ApiResponse<SasTokenResponse>.ErrorResult("Content type is required")));
                }

                // Validate file type
                if (!IsAllowedFileType(request.ContentType))
                {
                    return Task.FromResult<ActionResult<ApiResponse<SasTokenResponse>>>(BadRequest(ApiResponse<SasTokenResponse>.ErrorResult("File type not allowed")));
                }

                // Validate file size
                var maxFileSize = _configuration.GetValue<long>("SoNoBrokers:MaxImageUploadSize", 10485760); // 10MB default
                if (request.FileSize > maxFileSize)
                {
                    return Task.FromResult<ActionResult<ApiResponse<SasTokenResponse>>>(BadRequest(ApiResponse<SasTokenResponse>.ErrorResult($"File size exceeds maximum allowed size of {maxFileSize / 1024 / 1024}MB")));
                }

                var storageConnectionString = _configuration["ExternalServices:Azure:Storage:ConnectionString"];
                if (string.IsNullOrEmpty(storageConnectionString))
                {
                    _logger.LogError("Azure Storage connection string not configured");
                    return Task.FromResult<ActionResult<ApiResponse<SasTokenResponse>>>(StatusCode(500, ApiResponse<SasTokenResponse>.ErrorResult("Storage service not configured")));
                }

                var containerName = request.ContainerName ?? "property-images";
                var blobName = GenerateBlobName(userId, request.FileName, request.PropertyId);

                var blobServiceClient = new BlobServiceClient(storageConnectionString);
                var containerClient = blobServiceClient.GetBlobContainerClient(containerName);
                var blobClient = containerClient.GetBlobClient(blobName);

                // Generate SAS token
                var sasBuilder = new BlobSasBuilder
                {
                    BlobContainerName = containerName,
                    BlobName = blobName,
                    Resource = "b",
                    ExpiresOn = DateTimeOffset.UtcNow.AddHours(1) // Token expires in 1 hour
                };

                sasBuilder.SetPermissions(BlobSasPermissions.Create | BlobSasPermissions.Write);

                var sasToken = blobClient.GenerateSasUri(sasBuilder);

                var response = new SasTokenResponse
                {
                    SasToken = sasToken.ToString(),
                    BlobName = blobName,
                    ContainerName = containerName,
                    ExpiresAt = sasBuilder.ExpiresOn.DateTime,
                    UploadUrl = sasToken.ToString()
                };

                return Task.FromResult<ActionResult<ApiResponse<SasTokenResponse>>>(Ok(ApiResponse<SasTokenResponse>.SuccessResult(response, "SAS token generated successfully")));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating SAS token");
                return Task.FromResult<ActionResult<ApiResponse<SasTokenResponse>>>(StatusCode(500, ApiResponse<SasTokenResponse>.ErrorResult("Failed to generate SAS token")));
            }
        }

        /// <summary>
        /// Get upload URL for Supabase storage (alternative to Azure)
        /// </summary>
        /// <param name="request">Upload request details</param>
        /// <returns>Upload URL and metadata</returns>
        [HttpPost("get-upload-url")]




        public Task<ActionResult<ApiResponse<UploadUrlResponse>>> GetUploadUrl([FromBody] UploadUrlRequest request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Task.FromResult<ActionResult<ApiResponse<UploadUrlResponse>>>(Unauthorized(ApiResponse<UploadUrlResponse>.ErrorResult("User not authenticated")));
                }

                if (string.IsNullOrWhiteSpace(request.FileName))
                {
                    return Task.FromResult<ActionResult<ApiResponse<UploadUrlResponse>>>(BadRequest(ApiResponse<UploadUrlResponse>.ErrorResult("File name is required")));
                }

                // Validate file type
                if (!IsAllowedFileType(request.ContentType))
                {
                    return Task.FromResult<ActionResult<ApiResponse<UploadUrlResponse>>>(BadRequest(ApiResponse<UploadUrlResponse>.ErrorResult("File type not allowed")));
                }

                var supabaseUrl = _configuration["Supabase:Url"];
                var supabaseServiceKey = _configuration["Supabase:ServiceRoleKey"];

                if (string.IsNullOrEmpty(supabaseUrl) || string.IsNullOrEmpty(supabaseServiceKey))
                {
                    _logger.LogError("Supabase configuration not found");
                    return Task.FromResult<ActionResult<ApiResponse<UploadUrlResponse>>>(StatusCode(500, ApiResponse<UploadUrlResponse>.ErrorResult("Storage service not configured")));
                }

                var bucketName = request.BucketName ?? "property-images";
                var filePath = GenerateFilePath(userId, request.FileName, request.PropertyId);

                var response = new UploadUrlResponse
                {
                    UploadUrl = $"{supabaseUrl}/storage/v1/object/{bucketName}/{filePath}",
                    FilePath = filePath,
                    BucketName = bucketName,
                    Headers = new Dictionary<string, string>
                    {
                        { "Authorization", $"Bearer {supabaseServiceKey}" },
                        { "Content-Type", request.ContentType ?? "application/octet-stream" }
                    },
                    ExpiresAt = DateTime.UtcNow.AddHours(1)
                };

                return Task.FromResult<ActionResult<ApiResponse<UploadUrlResponse>>>(Ok(ApiResponse<UploadUrlResponse>.SuccessResult(response, "Upload URL generated successfully")));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating upload URL");
                return Task.FromResult<ActionResult<ApiResponse<UploadUrlResponse>>>(StatusCode(500, ApiResponse<UploadUrlResponse>.ErrorResult("Failed to generate upload URL")));
            }
        }

        private bool IsAllowedFileType(string? contentType)
        {
            if (string.IsNullOrEmpty(contentType))
                return false;

            var allowedTypes = _configuration.GetSection("SoNoBrokers:AllowedImageTypes").Get<string[]>()
                ?? new[] { "image/jpeg", "image/png", "image/webp", "image/gif" };

            return allowedTypes.Contains(contentType.ToLower());
        }

        private static string GenerateBlobName(string userId, string fileName, string? propertyId = null)
        {
            var fileExtension = Path.GetExtension(fileName);
            var uniqueId = Guid.NewGuid().ToString();

            if (!string.IsNullOrEmpty(propertyId))
            {
                return $"properties/{propertyId}/{uniqueId}{fileExtension}";
            }

            return $"users/{userId}/{uniqueId}{fileExtension}";
        }

        private static string GenerateFilePath(string userId, string fileName, string? propertyId = null)
        {
            var fileExtension = Path.GetExtension(fileName);
            var uniqueId = Guid.NewGuid().ToString();

            if (!string.IsNullOrEmpty(propertyId))
            {
                return $"properties/{propertyId}/{uniqueId}{fileExtension}";
            }

            return $"users/{userId}/{uniqueId}{fileExtension}";
        }
    }

    public class SasTokenRequest
    {
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string? PropertyId { get; set; }
        public string? ContainerName { get; set; }
    }

    public class SasTokenResponse
    {
        public string SasToken { get; set; } = string.Empty;
        public string BlobName { get; set; } = string.Empty;
        public string ContainerName { get; set; } = string.Empty;
        public string UploadUrl { get; set; } = string.Empty;
        public DateTime ExpiresAt { get; set; }
    }

    public class UploadUrlRequest
    {
        public string FileName { get; set; } = string.Empty;
        public string? ContentType { get; set; }
        public string? PropertyId { get; set; }
        public string? BucketName { get; set; }
    }

    public class UploadUrlResponse
    {
        public string UploadUrl { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string BucketName { get; set; } = string.Empty;
        public Dictionary<string, string> Headers { get; set; } = new();
        public DateTime ExpiresAt { get; set; }
    }
}
