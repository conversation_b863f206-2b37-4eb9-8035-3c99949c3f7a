'use client'

import { useUser } from '@clerk/nextjs'
import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'


export default function VerifyPhonePage() {
  const { user } = useUser()
  const [phoneNumber, setPhoneNumber] = useState('')
  const [verificationCode, setVerificationCode] = useState('')
  const [isVerifying, setIsVerifying] = useState(false)

  const handleSendCode = async () => {
    try {
      // Send verification code
      await user?.createPhoneNumber({
        phoneNumber,
      })
      setIsVerifying(true)
    } catch (error) {
      console.error('Error sending verification code:', error)
    }
  }

  const handleVerifyCode = async () => {
    try {
      // Verify the code
      await user?.phoneNumbers[0]?.attemptVerification({
        code: verificationCode,
      })
      // Redirect to profile after successful verification
      window.location.href = '/profile'
    } catch (error) {
      console.error('Error verifying code:', error)
    }
  }

  return (
    <div className="min-h-screen py-8 flex justify-center items-center">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center">Verify Your Phone</CardTitle>
          <p className="text-center text-muted-foreground">
            {isVerifying
              ? 'Enter the verification code sent to your phone'
              : 'Add your phone number to receive important updates'}
          </p>
        </CardHeader>
        <CardContent>
          {!isVerifying ? (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  placeholder="+****************"
                />
              </div>
              <Button onClick={handleSendCode} className="w-full">
                Send Verification Code
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="code">Verification Code</Label>
                <Input
                  id="code"
                  type="text"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                  placeholder="Enter 6-digit code"
                />
              </div>
              <Button onClick={handleVerifyCode} className="w-full">
                Verify Code
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
