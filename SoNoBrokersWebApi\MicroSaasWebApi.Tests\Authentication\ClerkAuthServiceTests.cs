using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MicroSaasWebApi.Models.Auth;
using MicroSaasWebApi.Services.Auth.Clerk;
using MicroSaasWebApi.Tests.Common;
using Moq;
using Moq.Protected;
using System.Net;
using System.Security.Claims;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Authentication
{
    public class ClerkAuthServiceTests : TestBase
    {
        private readonly Mock<ILogger<ClerkAuthService>> _mockLogger;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private readonly HttpClient _httpClient;
        private readonly ClerkAuthService _clerkAuthService;

        public ClerkAuthServiceTests(ITestOutputHelper output) : base(output)
        {
            _mockLogger = new Mock<ILogger<ClerkAuthService>>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);
            
            // Setup configuration
            _mockConfiguration.Setup(x => x["Clerk:SecretKey"]).Returns("test-secret-key");
            _mockConfiguration.Setup(x => x["Clerk:PublishableKey"]).Returns("test-publishable-key");
            _mockConfiguration.Setup(x => x["Clerk:ApiUrl"]).Returns("https://api.clerk.dev/v1");
            
            _clerkAuthService = new ClerkAuthService(_mockConfiguration.Object, _mockLogger.Object, _httpClient);
        }

        [Fact]
        public async Task GetUserIdAsync_WithValidHttpContext_ReturnsUserId()
        {
            // Arrange
            var expectedUserId = "user_123456";
            var httpContext = TestHelpers.CreateMockHttpContext(expectedUserId);

            // Act
            var result = await _clerkAuthService.GetUserIdAsync(httpContext);

            // Assert
            result.Should().Be(expectedUserId);
        }

        [Fact]
        public async Task GetUserIdAsync_WithAnonymousUser_ReturnsNull()
        {
            // Arrange
            var httpContext = TestHelpers.CreateAnonymousHttpContext();

            // Act
            var result = await _clerkAuthService.GetUserIdAsync(httpContext);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task GetUserIdAsync_WithSubClaim_ReturnsUserId()
        {
            // Arrange
            var expectedUserId = "user_789012";
            var context = new DefaultHttpContext();
            
            var claims = new List<Claim>
            {
                new("sub", expectedUserId) // Using 'sub' claim instead of NameIdentifier
            };

            var identity = new ClaimsIdentity(claims, "Test");
            var principal = new ClaimsPrincipal(identity);
            context.User = principal;

            // Act
            var result = await _clerkAuthService.GetUserIdAsync(context);

            // Assert
            result.Should().Be(expectedUserId);
        }

        [Fact]
        public async Task GetUserProfileAsync_WithValidHttpContext_ReturnsUserProfile()
        {
            // Arrange
            var userId = "user_123456";
            var email = "<EMAIL>";
            var httpContext = TestHelpers.CreateMockHttpContext(userId, email);

            var mockClerkUser = new
            {
                id = userId,
                email_addresses = new[]
                {
                    new { email_address = email, verification = new { status = "verified" } }
                },
                first_name = "Test",
                last_name = "User",
                profile_image_url = "https://example.com/avatar.jpg"
            };

            var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(JsonSerializer.Serialize(mockClerkUser))
            };

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(httpResponse);

            // Act
            var result = await _clerkAuthService.GetUserProfileAsync(httpContext);

            // Assert
            result.Should().NotBeNull();
            result!.Email.Should().Be(email);
            result.FirstName.Should().Be("Test");
            result.LastName.Should().Be("User");
            result.IsEmailVerified.Should().BeTrue();
            result.ProfileImageUrl.Should().Be("https://example.com/avatar.jpg");
        }

        [Fact]
        public async Task GetUserProfileAsync_WithAnonymousUser_ReturnsNull()
        {
            // Arrange
            var httpContext = TestHelpers.CreateAnonymousHttpContext();

            // Act
            var result = await _clerkAuthService.GetUserProfileAsync(httpContext);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task LoginAsync_WithValidCredentials_ReturnsSuccessResponse()
        {
            // Arrange
            var request = new LoginRequest
            {
                Email = "<EMAIL>",
                Password = "TestPassword123!"
            };

            var mockResponse = new
            {
                client = new
                {
                    sessions = new[]
                    {
                        new
                        {
                            id = "sess_123",
                            user = new
                            {
                                id = "user_123",
                                email_addresses = new[]
                                {
                                    new { email_address = request.Email }
                                },
                                first_name = "Test",
                                last_name = "User"
                            }
                        }
                    }
                }
            };

            var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(JsonSerializer.Serialize(mockResponse))
            };

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(httpResponse);

            // Act
            var result = await _clerkAuthService.LoginAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Token.Should().NotBeNullOrEmpty();
            result.User.Should().NotBeNull();
            result.User!.Email.Should().Be(request.Email);
        }

        [Fact]
        public async Task LoginAsync_WithInvalidCredentials_ReturnsFailureResponse()
        {
            // Arrange
            var request = new LoginRequest
            {
                Email = "<EMAIL>",
                Password = "WrongPassword"
            };

            var httpResponse = new HttpResponseMessage(HttpStatusCode.Unauthorized)
            {
                Content = new StringContent(JsonSerializer.Serialize(new { error = "Invalid credentials" }))
            };

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(httpResponse);

            // Act
            var result = await _clerkAuthService.LoginAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeFalse();
            result.Message.Should().Contain("Invalid credentials");
        }

        [Fact]
        public async Task RegisterAsync_WithValidData_ReturnsSuccessResponse()
        {
            // Arrange
            var request = new RegisterRequest
            {
                Email = "<EMAIL>",
                Password = "NewPassword123!",
                FirstName = "New",
                LastName = "User"
            };

            var mockResponse = new
            {
                id = "user_new123",
                email_addresses = new[]
                {
                    new { email_address = request.Email }
                },
                first_name = request.FirstName,
                last_name = request.LastName
            };

            var httpResponse = new HttpResponseMessage(HttpStatusCode.Created)
            {
                Content = new StringContent(JsonSerializer.Serialize(mockResponse))
            };

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(httpResponse);

            // Act
            var result = await _clerkAuthService.RegisterAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Token.Should().NotBeNullOrEmpty();
            result.User.Should().NotBeNull();
            result.User!.Email.Should().Be(request.Email);
            result.User.FirstName.Should().Be(request.FirstName);
            result.User.LastName.Should().Be(request.LastName);
        }

        [Fact]
        public async Task RegisterAsync_WithExistingEmail_ReturnsFailureResponse()
        {
            // Arrange
            var request = new RegisterRequest
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                FirstName = "Test",
                LastName = "User"
            };

            var httpResponse = new HttpResponseMessage(HttpStatusCode.BadRequest)
            {
                Content = new StringContent(JsonSerializer.Serialize(new { error = "Email already exists" }))
            };

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(httpResponse);

            // Act
            var result = await _clerkAuthService.RegisterAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeFalse();
            result.Message.Should().Contain("Email already exists");
        }

        [Fact]
        public async Task RefreshTokenAsync_WithValidToken_ReturnsNewToken()
        {
            // Arrange
            var refreshToken = "valid_refresh_token";

            var mockResponse = new
            {
                jwt = "new_jwt_token",
                user = new
                {
                    id = "user_123",
                    email_addresses = new[]
                    {
                        new { email_address = "<EMAIL>" }
                    }
                }
            };

            var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(JsonSerializer.Serialize(mockResponse))
            };

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(httpResponse);

            // Act
            var result = await _clerkAuthService.RefreshTokenAsync(refreshToken);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Token.Should().Be("new_jwt_token");
        }

        [Fact]
        public async Task LogoutAsync_WithValidUserId_ReturnsTrue()
        {
            // Arrange
            var userId = "user_123456";

            var httpResponse = new HttpResponseMessage(HttpStatusCode.OK);

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(httpResponse);

            // Act
            var result = await _clerkAuthService.LogoutAsync(userId);

            // Assert
            result.Should().BeTrue();
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        public async Task GetUserIdAsync_WithInvalidHttpContext_ReturnsNull(string? userId)
        {
            // Arrange
            var context = new DefaultHttpContext();
            if (!string.IsNullOrEmpty(userId))
            {
                var claims = new List<Claim> { new(ClaimTypes.NameIdentifier, userId) };
                var identity = new ClaimsIdentity(claims, "Test");
                context.User = new ClaimsPrincipal(identity);
            }

            // Act
            var result = await _clerkAuthService.GetUserIdAsync(context);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task GetUserAsync_WithValidUserId_ReturnsUser()
        {
            // Arrange
            var userId = "user_123456";

            var mockUser = new
            {
                id = userId,
                email_addresses = new[]
                {
                    new { email_address = "<EMAIL>", verification = new { status = "verified" } }
                },
                first_name = "Test",
                last_name = "User",
                profile_image_url = "https://example.com/avatar.jpg"
            };

            var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(JsonSerializer.Serialize(mockUser))
            };

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(httpResponse);

            // Act
            var result = await _clerkAuthService.GetUserAsync(userId);

            // Assert
            result.Should().NotBeNull();
            result!.Id.Should().Be(userId);
            result.EmailAddress.Should().Be("<EMAIL>");
            result.FirstName.Should().Be("Test");
            result.LastName.Should().Be("User");
            result.EmailVerified.Should().BeTrue();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _httpClient?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
