'use client'

import { useState, useEffect, useRef } from 'react'
import { useFormState } from 'react-dom'
import { useUser } from '@clerk/nextjs'
import { useMessagingHub, type MessageNotification, type TypingIndicator } from '@/lib/signalr/messaging-hub'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Send,
  User,
  Home,
  Clock,
  MoreVertical,
  ArrowLeft,
  Phone,
  Mail
} from 'lucide-react'
import { formatDistanceToNow, format } from 'date-fns'
import { sendMessageAction, markMessagesAsReadAction } from '@/lib/actions/messaging-actions'
import type { Conversation, Message } from '@/lib/api/messaging-api'

interface ChatInterfaceProps {
  conversation: Conversation
  messages: Message[]
  onBack?: () => void
  className?: string
}

const initialState = {
  success: false,
  error: null as string | null,
  fieldErrors: {} as Record<string, string[]>,
}

export function ChatInterface({
  conversation,
  messages,
  onBack,
  className = ''
}: ChatInterfaceProps) {
  const { user } = useUser()
  const [messageContent, setMessageContent] = useState('')
  const [state, formAction] = useFormState(sendMessageAction, initialState)
  const [localMessages, setLocalMessages] = useState(messages)
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set())
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const typingTimeoutRef = useRef<NodeJS.Timeout>()

  // SignalR integration
  const {
    client,
    connect,
    disconnect,
    joinConversation,
    leaveConversation,
    sendTypingIndicator,
    markMessagesAsRead,
    connected
  } = useMessagingHub()

  // Initialize SignalR connection
  useEffect(() => {
    const initializeSignalR = async () => {
      if (user) {
        await connect()
        if (connected) {
          await joinConversation(conversation.id)
        }
      }
    }

    initializeSignalR()

    return () => {
      if (connected) {
        leaveConversation(conversation.id)
      }
    }
  }, [user, conversation.id, connect, joinConversation, leaveConversation, connected])

  // Set up SignalR event handlers
  useEffect(() => {
    // Handle new messages
    client.setOnMessageReceived((notification: MessageNotification) => {
      if (notification.conversationId === conversation.id) {
        const newMessage = {
          id: notification.messageId,
          conversationId: notification.conversationId,
          senderId: notification.senderId,
          senderName: notification.senderName,
          senderEmail: '',
          content: notification.content,
          messageType: 'text',
          isRead: false,
          createdAt: notification.createdAt,
          isOwnMessage: notification.senderId === user?.id
        }
        setLocalMessages(prev => [...prev, newMessage])
      }
    })

    // Handle typing indicators
    client.setOnTypingIndicator((indicator: TypingIndicator) => {
      if (indicator.conversationId === conversation.id && indicator.userId !== user?.id) {
        setTypingUsers(prev => {
          const newSet = new Set(prev)
          if (indicator.isTyping) {
            newSet.add(indicator.userName)
          } else {
            newSet.delete(indicator.userName)
          }
          return newSet
        })
      }
    })

    // Handle messages read
    client.setOnMessagesRead((data) => {
      if (data.conversationId === conversation.id && data.userId !== user?.id) {
        setLocalMessages(prev =>
          prev.map(msg =>
            data.messageIds?.includes(msg.id) || !data.messageIds
              ? { ...msg, isRead: true }
              : msg
          )
        )
      }
    })
  }, [client, conversation.id, user?.id])

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [localMessages])

  // Mark messages as read when conversation is opened
  useEffect(() => {
    const unreadMessages = localMessages.filter(msg => !msg.isRead && !msg.isOwnMessage)
    if (unreadMessages.length > 0) {
      markMessagesAsReadAction(conversation.id, unreadMessages.map(msg => msg.id))
      if (connected) {
        markMessagesAsRead(conversation.id, unreadMessages.map(msg => msg.id))
      }
    }
  }, [conversation.id, localMessages, connected, markMessagesAsRead])

  // Clear form on successful send
  useEffect(() => {
    if (state.success) {
      setMessageContent('')
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto'
      }
    }
  }, [state.success])

  const handleSubmit = async (formData: FormData) => {
    if (!messageContent.trim()) return

    formData.set('conversationId', conversation.id)
    formData.set('content', messageContent.trim())

    await formAction(formData)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      if (messageContent.trim()) {
        const form = e.currentTarget.closest('form')
        if (form) {
          const formData = new FormData(form)
          handleSubmit(formData)
        }
      }
    }
  }

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessageContent(e.target.value)

    // Auto-resize textarea
    e.target.style.height = 'auto'
    e.target.style.height = `${Math.min(e.target.scrollHeight, 120)}px`

    // Send typing indicator
    if (connected) {
      sendTypingIndicator(conversation.id, true)

      // Clear previous timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }

      // Set timeout to stop typing indicator
      typingTimeoutRef.current = setTimeout(() => {
        sendTypingIndicator(conversation.id, false)
      }, 2000)
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatMessageTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return format(date, 'HH:mm')
    } else if (diffInHours < 168) { // 7 days
      return format(date, 'EEE HH:mm')
    } else {
      return format(date, 'MMM d, HH:mm')
    }
  }

  const otherParticipant = conversation.buyerId === user?.id
    ? { id: conversation.sellerId, name: conversation.sellerName, email: conversation.sellerEmail }
    : { id: conversation.buyerId, name: conversation.buyerName, email: conversation.buyerEmail }

  return (
    <Card className={className}>
      {/* Header */}
      <CardHeader className="border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {onBack && (
              <Button variant="ghost" size="sm" onClick={onBack}>
                <ArrowLeft className="h-4 w-4" />
              </Button>
            )}

            <Avatar className="h-10 w-10">
              <AvatarFallback className="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400">
                {otherParticipant.name ? getInitials(otherParticipant.name) : <User className="h-5 w-5" />}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1">
              <CardTitle className="text-lg">
                {otherParticipant.name || 'Unknown User'}
              </CardTitle>
              <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                {conversation.propertyTitle && (
                  <div className="flex items-center gap-1">
                    <Home className="h-3 w-3" />
                    <span>{conversation.propertyTitle}</span>
                    {conversation.propertyPrice && (
                      <span className="font-medium text-green-600 dark:text-green-400">
                        ${conversation.propertyPrice.toLocaleString()}
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Connection status indicator */}
            <div className={`w-2 h-2 rounded-full ${connected ? 'bg-green-500' : 'bg-red-500'}`}
              title={connected ? 'Real-time messaging connected' : 'Real-time messaging disconnected'} />

            {otherParticipant.email && (
              <Button variant="ghost" size="sm" title="Send Email">
                <Mail className="h-4 w-4" />
              </Button>
            )}
            <Button variant="ghost" size="sm">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {conversation.subject && (
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
            {conversation.subject}
          </p>
        )}
      </CardHeader>

      {/* Messages */}
      <CardContent className="p-0">
        <div className="h-96 overflow-y-auto p-4 space-y-4">
          {localMessages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center">
              <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-4 mb-4">
                <User className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                Start the conversation
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Send your first message to {otherParticipant.name}
              </p>
            </div>
          ) : (
            <>
              {localMessages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.isOwnMessage ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`flex gap-2 max-w-[70%] ${message.isOwnMessage ? 'flex-row-reverse' : 'flex-row'}`}>
                    <Avatar className="h-8 w-8 flex-shrink-0">
                      <AvatarFallback className={`text-xs ${message.isOwnMessage
                        ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                        : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400'
                        }`}>
                        {message.senderName ? getInitials(message.senderName) : <User className="h-4 w-4" />}
                      </AvatarFallback>
                    </Avatar>

                    <div className={`space-y-1 ${message.isOwnMessage ? 'items-end' : 'items-start'} flex flex-col`}>
                      <div className={`rounded-lg px-3 py-2 ${message.isOwnMessage
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white'
                        }`}>
                        <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                      </div>

                      <div className="flex items-center gap-2 text-xs text-gray-500">
                        <span>{formatMessageTime(message.createdAt)}</span>
                        {message.isOwnMessage && (
                          <span className={message.isRead ? 'text-blue-500' : 'text-gray-400'}>
                            {message.isRead ? 'Read' : 'Sent'}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {/* Typing indicators */}
              {typingUsers.size > 0 && (
                <div className="flex justify-start">
                  <div className="flex gap-2 max-w-[70%]">
                    <Avatar className="h-8 w-8 flex-shrink-0">
                      <AvatarFallback className="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 text-xs">
                        ...
                      </AvatarFallback>
                    </Avatar>
                    <div className="bg-gray-100 dark:bg-gray-800 rounded-lg px-3 py-2">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Message Input */}
        <div className="border-t p-4">
          {state.error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{state.error}</AlertDescription>
            </Alert>
          )}

          <form action={handleSubmit} className="flex gap-2">
            <Textarea
              ref={textareaRef}
              name="content"
              value={messageContent}
              onChange={handleTextareaChange}
              onKeyPress={handleKeyPress}
              placeholder="Type your message..."
              className="flex-1 min-h-[40px] max-h-[120px] resize-none"
              rows={1}
            />
            <Button
              type="submit"
              disabled={!messageContent.trim()}
              className="self-end"
            >
              <Send className="h-4 w-4" />
            </Button>
          </form>

          <p className="text-xs text-gray-500 mt-2">
            Press Enter to send, Shift+Enter for new line
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
