// TODO: Migrate to .NET Web API - temporarily disabled
// import prisma from '@/lib/prisma'
import { 
  Advertiser, 
  AdvertiserSubscription, 
  CreateAdvertiserRequest, 
  UpdateAdvertiserRequest,
  AdvertiserDashboardStats,
  AdvertiserAnalytics,
  ServiceSearchParams,
  ServiceSearchResult,
  ServiceProviderWithAdvertiser
} from '@/types/advertiser'

// TODO: Migrate to .NET Web API - temporarily disabled entire service
export class AdvertiserService {
  // All methods temporarily disabled - migrating to .NET Web API
  static async createAdvertiser(): Promise<any> {
    throw new Error('Service temporarily disabled - migrating to .NET Web API');
  }
  static async updateAdvertiser(): Promise<any> {
    throw new Error('Service temporarily disabled - migrating to .NET Web API');
  }
  static async getAdvertiser(): Promise<any> {
    throw new Error('Service temporarily disabled - migrating to .NET Web API');
  }
  static async getAdvertiserByUserId(): Promise<any> {
    throw new Error('Service temporarily disabled - migrating to .NET Web API');
  }
  static async searchServiceProviders(): Promise<any> {
    throw new Error('Service temporarily disabled - migrating to .NET Web API');
  }
  static async getAdvertiserStats(): Promise<any> {
    throw new Error('Service temporarily disabled - migrating to .NET Web API');
  }
  static async createSubscription(): Promise<any> {
    throw new Error('Service temporarily disabled - migrating to .NET Web API');
  }
  static async updateSubscriptionStatus(): Promise<any> {
    throw new Error('Service temporarily disabled - migrating to .NET Web API');
  }
  static async getAllAdvertisers(): Promise<any> {
    throw new Error('Service temporarily disabled - migrating to .NET Web API');
  }
  static async deleteAdvertiser(): Promise<any> {
    throw new Error('Service temporarily disabled - migrating to .NET Web API');
  }
}

/* DISABLED - MIGRATE TO .NET WEB API
export class AdvertiserService_DISABLED {
  // Create a new advertiser
  static async createAdvertiser(data: CreateAdvertiserRequest & { userId: string }): Promise<Advertiser> {
    // TODO: Migrate to .NET Web API
    throw new Error('Service temporarily disabled - migrating to .NET Web API');

    /* DISABLED - MIGRATE TO .NET WEB API
    try {
      const advertiser = await prisma.advertiser.create({
        data: {
          ...data,
          metadata: {},
          images: data.images || []
        }
      })
      return advertiser as Advertiser
    } catch (error) {
      console.error('Error creating advertiser:', error)
      throw error
    }
  }

  // Update an advertiser
  static async updateAdvertiser(id: string, data: Partial<UpdateAdvertiserRequest>): Promise<Advertiser> {
    // TODO: Migrate to .NET Web API
    throw new Error('Service temporarily disabled - migrating to .NET Web API');

    /* DISABLED - MIGRATE TO .NET WEB API
    try {
      const advertiser = await prisma.advertiser.update({
        where: { id },
        data: {
          ...data,
          updatedAt: new Date()
        }
      })
      return advertiser as Advertiser
    } catch (error) {
      console.error('Error updating advertiser:', error)
      throw error
    }
  }

  // Get advertiser by ID
  static async getAdvertiser(id: string): Promise<Advertiser | null> {
    // TODO: Migrate to .NET Web API
    throw new Error('Service temporarily disabled - migrating to .NET Web API');

    /* DISABLED - MIGRATE TO .NET WEB API
    try {
      const advertiser = await prisma.advertiser.findUnique({
        where: { id },
        include: {
          user: true,
          serviceProvider: true,
          subscriptions: {
            where: { status: 'active' },
            orderBy: { createdAt: 'desc' },
            take: 1
          }
        }
      })
      return advertiser as Advertiser | null
    } catch (error) {
      console.error('Error getting advertiser:', error)
      throw error
    }
  }

  // Get advertiser by user ID
  static async getAdvertiserByUserId(userId: string): Promise<Advertiser | null> {
    // TODO: Migrate to .NET Web API
    throw new Error('Service temporarily disabled - migrating to .NET Web API');

    /* DISABLED - MIGRATE TO .NET WEB API
    try {
      const advertiser = await prisma.advertiser.findUnique({
        where: { userId },
        include: {
          subscriptions: {
            where: { status: 'active' },
            orderBy: { createdAt: 'desc' },
            take: 1
          }
        }
      })
      return advertiser as Advertiser | null
    } catch (error) {
      console.error('Error getting advertiser by user ID:', error)
      throw error
    }
  }

  // Search service providers with advertiser data
  static async searchServiceProviders(params: ServiceSearchParams): Promise<ServiceSearchResult> {
    try {
      const { query, filters, sortBy, page, limit } = params
      const skip = (page - 1) * limit

      // Build where clause
      const where: any = {}
      
      if (filters.serviceType) {
        where.serviceType = filters.serviceType
      }
      
      if (filters.isVerified !== undefined) {
        where.isVerified = filters.isVerified
      }

      if (query) {
        where.OR = [
          { businessName: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } }
        ]
      }

      // Build order by clause
      let orderBy: any = {}
      switch (sortBy) {
        case 'rating':
          orderBy = { rating: 'desc' }
          break
        case 'price':
          orderBy = { hourlyRate: 'asc' }
          break
        case 'premium':
          orderBy = [
            { advertiser: { isPremium: 'desc' } },
            { rating: 'desc' }
          ]
          break
        default:
          orderBy = { createdAt: 'desc' }
      }

      const [providers, total] = await Promise.all([
        prisma.serviceProvider.findMany({
          where,
          include: {
            user: true,
            advertisers: {
              where: { status: 'active' },
              include: {
                subscriptions: {
                  where: { status: 'active' },
                  orderBy: { createdAt: 'desc' },
                  take: 1
                }
              }
            }
          },
          orderBy,
          skip,
          take: limit
        }),
        prisma.serviceProvider.count({ where })
      ])

      // Transform to ServiceProviderWithAdvertiser format
      const transformedProviders: ServiceProviderWithAdvertiser[] = providers.map(provider => {
        const advertiser = provider.advertisers?.[0]
        return {
          id: provider.id,
          name: provider.user.fullName,
          businessName: provider.businessName,
          serviceType: provider.serviceType as any,
          location: 'Toronto, ON', // TODO: Get from serviceAreas
          distance: '2.5 km', // TODO: Calculate based on user location
          rating: Number(provider.rating) || 0,
          reviewCount: 0, // TODO: Calculate from actual reviews
          price: provider.hourlyRate ? `$${provider.hourlyRate}/hr` : 'Contact for pricing',
          specialties: [] as string[], // TODO: Extract from metadata
          verified: provider.isVerified || false,
          isAdvertiser: !!advertiser,
          isPremium: advertiser?.isPremium || false,
          phone: provider.user.phone,
          email: provider.user.email,
          description: provider.businessName,
          coordinates: { lat: 43.6532, lng: -79.3832 }, // TODO: Get real coordinates
          advertiser: advertiser ? {
            plan: advertiser.plan as any,
            status: advertiser.status as any,
            featuredUntil: advertiser.featuredUntil,
            isPremium: advertiser.isPremium
          } : undefined
        }
      })

      return {
        providers: transformedProviders,
        total,
        page,
        totalPages: Math.ceil(total / limit),
        hasMore: skip + limit < total
      }
    } catch (error) {
      console.error('Error searching service providers:', error)
      throw error
    }
  }

  // Get advertiser dashboard stats
  static async getAdvertiserStats(advertiserId: string): Promise<AdvertiserDashboardStats> {
    try {
      const advertiser = await prisma.advertiser.findUnique({
        where: { id: advertiserId },
        include: {
          subscriptions: {
            where: { status: 'active' },
            orderBy: { createdAt: 'desc' },
            take: 1
          }
        }
      })

      if (!advertiser) {
        throw new Error('Advertiser not found')
      }

      // TODO: Implement actual analytics queries
      const stats: AdvertiserDashboardStats = {
        totalViews: 0,
        totalInquiries: 0,
        totalBookings: 0,
        conversionRate: 0,
        planType: advertiser.plan as any,
        subscriptionStatus: advertiser.subscriptions[0]?.status || 'inactive',
        featuredDaysRemaining: advertiser.featuredUntil 
          ? Math.max(0, Math.ceil((advertiser.featuredUntil.getTime() - Date.now()) / (1000 * 60 * 60 * 24)))
          : undefined
      }

      return stats
    } catch (error) {
      console.error('Error getting advertiser stats:', error)
      throw error
    }
  }

  // Create advertiser subscription
  static async createSubscription(data: {
    advertiserId: string
    stripeSubscriptionId: string
    stripePriceId: string
    plan: string
    currentPeriodStart: Date
    currentPeriodEnd: Date
  }): Promise<AdvertiserSubscription> {
    try {
      const subscription = await prisma.advertiserSubscription.create({
        data: {
          ...data,
          plan: data.plan as any,
          status: 'active',
          metadata: {}
        }
      })
      return subscription as AdvertiserSubscription
    } catch (error) {
      console.error('Error creating advertiser subscription:', error)
      throw error
    }
  }

  // Update subscription status
  static async updateSubscriptionStatus(
    stripeSubscriptionId: string, 
    status: string,
    currentPeriodEnd?: Date
  ): Promise<AdvertiserSubscription> {
    try {
      const subscription = await prisma.advertiserSubscription.update({
        where: { stripeSubscriptionId },
        data: {
          status: status as any,
          currentPeriodEnd,
          updatedAt: new Date()
        }
      })
      return subscription as AdvertiserSubscription
    } catch (error) {
      console.error('Error updating subscription status:', error)
      throw error
    }
  }

  // Get all advertisers (admin function)
  static async getAllAdvertisers(page: number = 1, limit: number = 20) {
    try {
      const skip = (page - 1) * limit
      
      const [advertisers, total] = await Promise.all([
        prisma.advertiser.findMany({
          include: {
            user: true,
            subscriptions: {
              where: { status: 'active' },
              orderBy: { createdAt: 'desc' },
              take: 1
            }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit
        }),
        prisma.advertiser.count()
      ])

      return {
        advertisers,
        total,
        page,
        totalPages: Math.ceil(total / limit),
        hasMore: skip + limit < total
      }
    } catch (error) {
      console.error('Error getting all advertisers:', error)
      throw error
    }
  }

  // Delete advertiser
  static async deleteAdvertiser(id: string): Promise<void> {
    try {
      await prisma.advertiser.delete({
        where: { id }
      })
    } catch (error) {
      console.error('Error deleting advertiser:', error)
      throw error
    }
  }
}
*/
