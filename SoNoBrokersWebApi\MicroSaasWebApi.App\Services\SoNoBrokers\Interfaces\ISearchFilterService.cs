using MicroSaasWebApi.Models.SoNoBrokers;

namespace MicroSaasWebApi.Services.SoNoBrokers.Interfaces
{
    public interface ISearchFilterService
    {
        Task<IEnumerable<SearchFilter>> GetUserSearchFiltersAsync(string userId);
        Task<SearchFilter?> GetSearchFilterByIdAsync(string id);
        Task<SearchFilter> CreateSearchFilterAsync(SearchFilter searchFilter);
        Task<SearchFilter> UpdateSearchFilterAsync(SearchFilter searchFilter);
        Task<bool> DeleteSearchFilterAsync(string id);
        Task<bool> SetDefaultSearchFilterAsync(string userId, string filterId);
    }
}
