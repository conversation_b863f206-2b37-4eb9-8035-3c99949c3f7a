'use client';

import { SignInButton, useUser } from '@clerk/nextjs';
import { ReactNode, useEffect, useState } from 'react';

interface SafeSignInButtonProps {
  children: ReactNode;
  mode?: 'modal' | 'redirect';
  redirectUrl?: string;
}

export function SafeSignInButton({
  children,
  mode = 'modal',
  redirectUrl = '/dashboard'
}: SafeSignInButtonProps) {
  const { isSignedIn, isLoaded } = useUser();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render during SSR or while loading
  if (!mounted || !isLoaded) {
    return <>{children}</>;
  }

  // Don't render SignInButton if user is already signed in
  if (isSignedIn) {
    return <>{children}</>;
  }

  return (
    <SignInButton mode={mode} forceRedirectUrl={redirectUrl}>
      {children}
    </SignInButton>
  );
}
