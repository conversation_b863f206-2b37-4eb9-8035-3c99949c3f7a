"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Suspense } from "react";
import { Header } from "@/components/shared/layout/Header";
import Hero from "@/components/_archive/Hero";
import Features from "@/components/_archive/Features";
import Pricing from "@/components/_archive/Pricing";
import Faq from "@/components/_archive/FAQ";
import CTA from "@/components/_archive/CTA";
import { Footer } from "@/components/shared/layout/Footer";
import Comparison from "@/components/_archive/Comparison";
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const GEO_API = "https://ipapi.co/json/"; // You can swap for another API if needed

const SUPPORTED_COUNTRIES: Record<string, string> = {
  CA: "/ca",
  US: "/us",
};

export default function HomePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [geoError, setGeoError] = useState(false);
  const [manual, setManual] = useState(false);

  useEffect(() => {
    // Check if we're in development mode (localhost)
    const isLocalhost = typeof window !== 'undefined' &&
      (window.location.hostname === 'localhost' ||
        window.location.hostname === '127.0.0.1' ||
        window.location.hostname.includes('192.168.'));

    // For localhost, always redirect to Canada without showing selection
    if (isLocalhost) {
      router.replace('/ca');
      return;
    }

    // For production, try to detect location
    fetch(GEO_API)
      .then((res) => res.json())
      .then((data) => {
        const country = data?.country_code as string;
        if (country && SUPPORTED_COUNTRIES[country]) {
          router.replace(SUPPORTED_COUNTRIES[country]);
        } else if (country) {
          // Only show unsupported region for non-localhost
          router.replace("/unsupported-region");
        } else {
          // If country detection fails in production, default to Canada
          router.replace('/ca');
        }
      })
      .catch(() => {
        // On error, default to Canada instead of showing manual selection
        router.replace('/ca');
      })
      .finally(() => setLoading(false));
  }, [router]);

  if (loading && !manual) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="animate-pulse text-3xl font-bold text-foreground tracking-wide">Loading SoNoBrokers...</div>
      </div>
    );
  }

  if (manual) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-background">
        <div className="mb-8 text-4xl font-extrabold text-foreground tracking-tight">Select Your Country</div>
        <div className="flex gap-6">
          <button
            className="px-8 py-4 rounded-xl bg-card hover:bg-accent text-card-foreground hover:text-accent-foreground text-xl font-semibold shadow-lg transition-all duration-200 border border-border backdrop-blur"
            onClick={() => router.replace("/ca")}
          >
            Canada
          </button>
          <button
            className="px-8 py-4 rounded-xl bg-card hover:bg-accent text-card-foreground hover:text-accent-foreground text-xl font-semibold shadow-lg transition-all duration-200 border border-border backdrop-blur"
            onClick={() => router.replace("/us")}
          >
            USA
          </button>
        </div>
        {geoError && (
          <div className="mt-8 text-destructive text-lg">Could not detect your country automatically.</div>
        )}
      </div>
    );
  }

  return null;
}