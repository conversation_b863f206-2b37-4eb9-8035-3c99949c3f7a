using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace MicroSaasWebApi.Models.SoNoBrokers
{

    [Table("ServiceProvider", Schema = "snb")]
    public class ServiceProvider
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        public ServiceType ServiceType { get; set; }

        [Required]
        public string BusinessName { get; set; } = string.Empty;

        public string? Description { get; set; }

        public string? LicenseNumber { get; set; }

        [Column(TypeName = "jsonb")]
        public string? Regions { get; set; }

        [Column(TypeName = "jsonb")]
        public string? Rates { get; set; }

        [Column(TypeName = "decimal(3,2)")]
        public decimal? Rating { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        public virtual ICollection<ServiceBooking> Bookings { get; set; } = new List<ServiceBooking>();

        // Helper methods for JSON fields
        public T? GetRegionsAs<T>() where T : class
        {
            if (string.IsNullOrEmpty(Regions)) return null;
            try
            {
                return JsonSerializer.Deserialize<T>(Regions);
            }
            catch
            {
                return null;
            }
        }

        public void SetRegions<T>(T regions) where T : class
        {
            Regions = regions != null ? JsonSerializer.Serialize(regions) : null;
        }

        public T? GetRatesAs<T>() where T : class
        {
            if (string.IsNullOrEmpty(Rates)) return null;
            try
            {
                return JsonSerializer.Deserialize<T>(Rates);
            }
            catch
            {
                return null;
            }
        }

        public void SetRates<T>(T rates) where T : class
        {
            Rates = rates != null ? JsonSerializer.Serialize(rates) : null;
        }
    }
}
