using Microsoft.Extensions.Diagnostics.HealthChecks;
using System.Diagnostics;

namespace MicroSaasWebApi.Health
{
    /// <summary>
    /// Health check for monitoring remote endpoints and external services
    /// </summary>
    public class RemoteEndpointHealthCheck : IHealthCheck
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<RemoteEndpointHealthCheck> _logger;
        private readonly IConfiguration _configuration;

        public RemoteEndpointHealthCheck(
            IHttpClientFactory httpClientFactory,
            ILogger<RemoteEndpointHealthCheck> logger,
            IConfiguration configuration)
        {
            _httpClientFactory = httpClientFactory;
            _logger = logger;
            _configuration = configuration;
        }

        public async Task<HealthCheckResult> CheckHealthAsync(
            HealthCheckContext context,
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var healthData = new Dictionary<string, object>();

            try
            {
                using var httpClient = _httpClientFactory.CreateClient("HealthCheck");
                httpClient.Timeout = TimeSpan.FromSeconds(10); // 10 second timeout

                // List of endpoints to check
                var endpointsToCheck = new[]
                {
                    new { Name = "IP Check Service", Url = "https://api.ipify.org", ExpectedStatus = 200 },
                    new { Name = "Supabase Health", Url = GetSupabaseHealthUrl() ?? "", ExpectedStatus = 200 },
                    new { Name = "Stripe API", Url = "https://api.stripe.com/v1", ExpectedStatus = 401 } // 401 is expected without auth
                };

                var results = new List<object>();
                var allHealthy = true;

                foreach (var endpoint in endpointsToCheck)
                {
                    if (string.IsNullOrEmpty(endpoint.Url))
                    {
                        results.Add(new { endpoint.Name, Status = "Skipped", Reason = "URL not configured" });
                        continue;
                    }

                    try
                    {
                        var endpointStopwatch = Stopwatch.StartNew();
                        var response = await httpClient.GetAsync(endpoint.Url, cancellationToken);
                        endpointStopwatch.Stop();

                        var isHealthy = (int)response.StatusCode == endpoint.ExpectedStatus;
                        if (!isHealthy) allHealthy = false;

                        results.Add(new
                        {
                            endpoint.Name,
                            Status = isHealthy ? "Healthy" : "Unhealthy",
                            StatusCode = (int)response.StatusCode,
                            ExpectedStatusCode = endpoint.ExpectedStatus,
                            ResponseTime = $"{endpointStopwatch.ElapsedMilliseconds}ms",
                            Url = endpoint.Url
                        });

                        _logger.LogDebug("Health check for {EndpointName}: {Status} ({StatusCode}) in {ResponseTime}ms",
                            endpoint.Name, isHealthy ? "Healthy" : "Unhealthy", (int)response.StatusCode, endpointStopwatch.ElapsedMilliseconds);
                    }
                    catch (Exception ex)
                    {
                        allHealthy = false;
                        results.Add(new
                        {
                            endpoint.Name,
                            Status = "Unhealthy",
                            Error = ex.Message,
                            Url = endpoint.Url
                        });

                        _logger.LogWarning(ex, "Health check failed for {EndpointName}", endpoint.Name);
                    }
                }

                stopwatch.Stop();
                healthData["TotalResponseTime"] = $"{stopwatch.ElapsedMilliseconds}ms";
                healthData["EndpointResults"] = results;
                healthData["CheckedAt"] = DateTime.UtcNow;

                var status = allHealthy ? HealthStatus.Healthy : HealthStatus.Unhealthy;
                var description = allHealthy
                    ? "All remote endpoints are healthy"
                    : "One or more remote endpoints are unhealthy";

                return new HealthCheckResult(status, description, data: healthData);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                healthData["TotalResponseTime"] = $"{stopwatch.ElapsedMilliseconds}ms";
                healthData["Error"] = ex.Message;
                healthData["CheckedAt"] = DateTime.UtcNow;

                _logger.LogError(ex, "Remote endpoint health check failed");
                return new HealthCheckResult(HealthStatus.Unhealthy, "Remote endpoint health check failed", ex, healthData);
            }
        }

        private string? GetSupabaseHealthUrl()
        {
            var supabaseUrl = _configuration["Supabase:Url"];
            return string.IsNullOrEmpty(supabaseUrl) ? null : $"{supabaseUrl}/rest/v1/";
        }
    }
}
