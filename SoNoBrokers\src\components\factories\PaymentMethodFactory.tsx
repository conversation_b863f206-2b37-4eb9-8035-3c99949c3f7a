import { Country } from '@/lib/geo';
import { lazy } from 'react';

// Lazy load country-specific payment components
const CAPaymentMethods = lazy(() => import('@/components/country-specific/ca/payment/CAPaymentMethods'));
const USPaymentMethods = lazy(() => import('@/components/country-specific/us/payment/USPaymentMethods'));
const UAEPaymentMethods = lazy(() => import('@/components/country-specific/uae/payment/UAEPaymentMethods'));

interface PaymentMethodFactoryProps {
  country: Country;
  amount: number;
  currency: string;
  onSuccess?: (paymentId: string) => void;
  onError?: (error: string) => void;
  [key: string]: any;
}

export function PaymentMethodFactory({
  country,
  amount,
  currency,
  onSuccess,
  onError,
  ...props
}: PaymentMethodFactoryProps) {
  const paymentComponentMap = {
    CA: CAPaymentMethods,
    US: USPaymentMethods,
    UAE: UAEPaymentMethods,
  };

  const PaymentComponent = paymentComponentMap[country];

  if (!PaymentComponent) {
    console.warn(`Payment component not found for country ${country}`);
    return (
      <div className="p-4 border border-red-200 rounded-lg bg-red-50">
        <p className="text-red-600">Payment methods not available for {country}</p>
      </div>
    );
  }

  return (
    <PaymentComponent
      amount={amount}
      currency={currency}
      onSuccess={onSuccess}
      onError={onError}
      {...props}
    />
  );
}

export default PaymentMethodFactory;
