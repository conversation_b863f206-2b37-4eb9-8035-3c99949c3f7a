import { Country } from '@/lib/geo';
import { lazy } from 'react';

// Lazy load country-specific payment components
const CAPaymentMethods = lazy(() => import('@/components/country-specific/ca/payment/CAPaymentMethods'));
const USPaymentMethods = lazy(() => import('@/components/country-specific/us/payment/USPaymentMethods'));
const UAEPaymentMethods = lazy(() => import('@/components/country-specific/uae/payment/UAEPaymentMethods'));

interface PaymentMethodFactoryProps {
  country: Country;
  paymentType: 'property_listing' | 'advertiser_subscription' | 'concierge_service';
  // For advertiser subscriptions - only prop needed since Web API handles pricing
  subscriptionPlan?: 'Basic' | 'Premium';
  // All other payment details are handled by Web API based on country + paymentType
  onSuccess?: (paymentId: string) => void;
  onError?: (error: string) => void;
  [key: string]: any; // Pass through any additional props to country-specific components
}

export function PaymentMethodFactory({
  country,
  paymentType,
  subscriptionPlan,
  onSuccess,
  onError,
  ...props
}: PaymentMethodFactoryProps) {
  const paymentComponentMap = {
    CA: CAPaymentMethods,
    US: USPaymentMethods,
    UAE: UAEPaymentMethods,
  };

  const PaymentComponent = paymentComponentMap[country];

  if (!PaymentComponent) {
    console.warn(`Payment component not found for country ${country}`);
    return (
      <div className="p-4 border border-red-200 rounded-lg bg-red-50">
        <p className="text-red-600">Payment methods not available for {country}</p>
      </div>
    );
  }

  // Pass minimal props - Web API will handle all pricing logic
  // TODO: Update country-specific components to call Web API directly
  // For now, pass dummy amount/currency to satisfy interface
  const componentProps = {
    ...props,
    country,
    paymentType,
    subscriptionPlan, // Only needed for advertiser subscriptions
    amount: 0, // Temporary - Web API will determine actual amount
    currency: 'USD', // Temporary - Web API will determine actual currency
    onSuccess,
    onError
  };

  return <PaymentComponent {...componentProps} />;
}

export default PaymentMethodFactory;
