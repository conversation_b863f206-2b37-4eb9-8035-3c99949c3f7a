import { Country } from '@/lib/geo';
import { lazy } from 'react';
import {
  getPropertyListingPrice,
  getAdvertiserSubscriptionPrice,
  getConciergeServicePrice,
  type Country as StripeCountry,
  type PropertyType,
  type AdvertiserPlan,
  type ConciergeService
} from '@/lib/stripe-price-config';

// Lazy load country-specific payment components
const CAPaymentMethods = lazy(() => import('@/components/country-specific/ca/payment/CAPaymentMethods'));
const USPaymentMethods = lazy(() => import('@/components/country-specific/us/payment/USPaymentMethods'));
const UAEPaymentMethods = lazy(() => import('@/components/country-specific/uae/payment/UAEPaymentMethods'));

// Convert geo Country enum to Stripe Country format
function convertToStripeCountry(geoCountry: Country): StripeCountry {
  switch (geoCountry) {
    case Country.CA:
      return 'Canada';
    case Country.US:
      return 'USA';
    case Country.UAE:
      return 'UAE';
    default:
      return 'USA'; // Default fallback
  }
}

interface PaymentMethodFactoryProps {
  country: Country;
  paymentType: 'property_listing' | 'advertiser_subscription' | 'concierge_service';
  // For property listings
  propertyType?: PropertyType;
  propertyId?: string;
  // For advertiser subscriptions
  subscriptionPlan?: AdvertiserPlan;
  // For concierge services
  serviceType?: ConciergeService;
  projectId?: string;
  // Common props
  email: string;
  onSuccess?: (paymentId: string) => void;
  onError?: (error: string) => void;
  [key: string]: any;
}

export function PaymentMethodFactory({
  country,
  paymentType,
  email,
  onSuccess,
  onError,
  ...props
}: PaymentMethodFactoryProps) {
  // Convert country code to Stripe country format and get price configuration
  let priceConfig = null;
  try {
    const stripeCountry = convertToStripeCountry(country);

    switch (paymentType) {
      case 'property_listing':
        if (props.propertyType) {
          priceConfig = getPropertyListingPrice(stripeCountry, props.propertyType);
        }
        break;
      case 'advertiser_subscription':
        if (props.subscriptionPlan) {
          priceConfig = getAdvertiserSubscriptionPrice(stripeCountry, props.subscriptionPlan);
        }
        break;
      case 'concierge_service':
        if (props.serviceType) {
          priceConfig = getConciergeServicePrice(stripeCountry, props.serviceType);
        }
        break;
    }
  } catch (error) {
    console.error('PaymentMethodFactory price configuration error:', error);
  }

  const paymentComponentMap = {
    CA: CAPaymentMethods,
    US: USPaymentMethods,
    UAE: UAEPaymentMethods,
  };

  const PaymentComponent = paymentComponentMap[country];

  if (!PaymentComponent) {
    console.warn(`Payment component not found for country ${country}`);
    return (
      <div className="p-4 border border-red-200 rounded-lg bg-red-50">
        <p className="text-red-600">Payment methods not available for {country}</p>
      </div>
    );
  }

  // Enhanced props with price configuration
  const enhancedProps = {
    ...props,
    country,
    paymentType,
    email,
    priceConfig,
    amount: priceConfig?.amount,
    currency: priceConfig?.currency,
    stripeCountry: priceConfig ? convertToStripeCountry(country) : null
  };

  return (
    <PaymentComponent
      onSuccess={onSuccess}
      onError={onError}
      {...enhancedProps}
    />
  );
}

export default PaymentMethodFactory;
