import { Country } from '@prisma/client';
import { lazy, ComponentType } from 'react';

// Lazy load country-specific service provider components
const CALegalServices = lazy(() => import('@/components/country-specific/ca/legal/CALegalServices'));
const USLegalServices = lazy(() => import('@/components/country-specific/us/legal/USLegalServices'));
const UAELegalServices = lazy(() => import('@/components/country-specific/uae/legal/UAELegalServices'));

const CAMortgageCalculator = lazy(() => import('@/components/country-specific/ca/mortgage/CAMortgageCalculator'));
const USMortgageCalculator = lazy(() => import('@/components/country-specific/us/mortgage/USMortgageCalculator'));
const UAEMortgageCalculator = lazy(() => import('@/components/country-specific/uae/mortgage/UAEMortgageCalculator'));

interface ServiceProviderFactoryProps {
  country: Country;
  serviceType: 'legal' | 'mortgage' | 'photography' | 'inspection';
  [key: string]: any;
}

export function ServiceProviderFactory({
  country,
  serviceType,
  ...props
}: ServiceProviderFactoryProps) {
  const serviceComponentMap: Record<Country, Record<string, ComponentType<any> | null>> = {
    CA: {
      legal: CALegalServices,
      mortgage: CAMortgageCalculator,
      photography: null, // To be implemented
      inspection: null, // To be implemented
    },
    US: {
      legal: USLegalServices,
      mortgage: USMortgageCalculator,
      photography: null, // To be implemented
      inspection: null, // To be implemented
    },
    UAE: {
      legal: UAELegalServices,
      mortgage: UAEMortgageCalculator,
      photography: null, // To be implemented
      inspection: null, // To be implemented
    },
  };

  const ServiceComponent = serviceComponentMap[country]?.[serviceType];

  if (!ServiceComponent) {
    console.warn(`Service component ${serviceType} not found for country ${country}`);
    return (
      <div className="p-4 border border-yellow-200 rounded-lg bg-yellow-50">
        <p className="text-yellow-600">
          {serviceType} services coming soon for {country}
        </p>
      </div>
    );
  }

  return <ServiceComponent {...props} />;
}

export default ServiceProviderFactory;
