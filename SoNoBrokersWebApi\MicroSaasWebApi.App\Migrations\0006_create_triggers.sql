-- Migration: 0006_create_triggers.sql
-- Description: Create database triggers for automatic timestamp updates
-- Date: 2024-12-25
-- Author: Migration System
-- Dependencies: 0005_create_indexes.sql

-- =====================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- =====================================================

-- Function to update the updatedAt timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply update triggers to core tables with updatedAt columns
DROP TRIGGER IF EXISTS update_user_updated_at ON public."User";
CREATE TRIGGER update_user_updated_at 
    BEFORE UPDATE ON public."User" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_property_updated_at ON public."Property";
CREATE TRIGGER update_property_updated_at 
    BEFORE UPDATE ON public."Property" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_conversation_updated_at ON public."Conversation";
CREATE TRIGGER update_conversation_updated_at 
    BEFORE UPDATE ON public."Conversation" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_buyer_offer_updated_at ON public."BuyerOffer";
CREATE TRIGGER update_buyer_offer_updated_at 
    BEFORE UPDATE ON public."BuyerOffer" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_property_viewing_updated_at ON public."PropertyViewing";
CREATE TRIGGER update_property_viewing_updated_at 
    BEFORE UPDATE ON public."PropertyViewing" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Apply update triggers to subscription tables
DROP TRIGGER IF EXISTS update_subscription_updated_at ON public."SubscriptionSnb";
CREATE TRIGGER update_subscription_updated_at 
    BEFORE UPDATE ON public."SubscriptionSnb" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_advertiser_updated_at ON public."Advertiser";
CREATE TRIGGER update_advertiser_updated_at 
    BEFORE UPDATE ON public."Advertiser" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_advertiser_subscription_updated_at ON public."AdvertiserSubscription";
CREATE TRIGGER update_advertiser_subscription_updated_at 
    BEFORE UPDATE ON public."AdvertiserSubscription" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Apply update triggers to service provider tables
DROP TRIGGER IF EXISTS update_service_provider_updated_at ON public."ServiceProvider";
CREATE TRIGGER update_service_provider_updated_at 
    BEFORE UPDATE ON public."ServiceProvider" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_service_booking_updated_at ON public."ServiceBooking";
CREATE TRIGGER update_service_booking_updated_at 
    BEFORE UPDATE ON public."ServiceBooking" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Apply update triggers to contact sharing and scheduling tables
DROP TRIGGER IF EXISTS update_contactshare_updated_at ON public."ContactShare";
CREATE TRIGGER update_contactshare_updated_at 
    BEFORE UPDATE ON public."ContactShare" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_selleravailability_updated_at ON public."SellerAvailability";
CREATE TRIGGER update_selleravailability_updated_at 
    BEFORE UPDATE ON public."SellerAvailability" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_propertyvisitschedule_updated_at ON public."PropertyVisitSchedule";
CREATE TRIGGER update_propertyvisitschedule_updated_at 
    BEFORE UPDATE ON public."PropertyVisitSchedule" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Apply update triggers to Stripe tables
DROP TRIGGER IF EXISTS update_stripe_customers_updated_at ON public."stripe_customers";
CREATE TRIGGER update_stripe_customers_updated_at 
    BEFORE UPDATE ON public."stripe_customers" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_stripe_products_updated_at ON public."stripe_products";
CREATE TRIGGER update_stripe_products_updated_at 
    BEFORE UPDATE ON public."stripe_products" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- ADDITIONAL UTILITY FUNCTIONS
-- =====================================================

-- Function to generate a short UUID for text primary keys
CREATE OR REPLACE FUNCTION generate_short_uuid()
RETURNS TEXT AS $$
BEGIN
    RETURN SUBSTRING(gen_random_uuid()::TEXT FROM 1 FOR 8);
END;
$$ LANGUAGE plpgsql;

-- Function to validate email format
CREATE OR REPLACE FUNCTION is_valid_email(email TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$';
END;
$$ LANGUAGE plpgsql;

-- Function to validate phone number format (basic)
CREATE OR REPLACE FUNCTION is_valid_phone(phone TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- Basic phone validation - digits, spaces, dashes, parentheses, plus sign
    RETURN phone ~* '^[\+]?[0-9\s\-\(\)]{10,15}$';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ Migration 0006_create_triggers.sql completed successfully';
    RAISE NOTICE 'Created triggers for automatic updatedAt timestamp updates on:';
    RAISE NOTICE '- User, Property, Conversation, BuyerOffer, PropertyViewing';
    RAISE NOTICE '- SubscriptionSnb, Advertiser, AdvertiserSubscription';
    RAISE NOTICE '- ServiceProvider, ServiceBooking';
    RAISE NOTICE '- ContactShare, SellerAvailability, PropertyVisitSchedule';
    RAISE NOTICE '- stripe_customers, stripe_products';
    RAISE NOTICE '';
    RAISE NOTICE 'Created utility functions:';
    RAISE NOTICE '- generate_short_uuid() for text primary keys';
    RAISE NOTICE '- is_valid_email() for email validation';
    RAISE NOTICE '- is_valid_phone() for phone validation';
    RAISE NOTICE '';
END $$;
