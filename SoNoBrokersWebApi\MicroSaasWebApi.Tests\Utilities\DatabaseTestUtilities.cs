using Dapper;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Tests.Common;
using Npgsql;
using System.Data;

namespace MicroSaasWebApi.Tests.Utilities
{
    /// <summary>
    /// Database testing utilities for easier database operations and assertions
    /// </summary>
    public class DatabaseTestUtilities : IDisposable
    {
        private readonly string _connectionString;
        private readonly List<IDbConnection> _connections = new();

        public DatabaseTestUtilities(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("SupabaseConnection") 
                ?? TestHelpers.GetTestConnectionString();
        }

        #region Connection Management

        /// <summary>
        /// Gets a new database connection
        /// </summary>
        public async Task<IDbConnection> GetConnectionAsync()
        {
            var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();
            _connections.Add(connection);
            return connection;
        }

        /// <summary>
        /// Executes action within a transaction that gets rolled back
        /// </summary>
        public async Task<T> WithTransactionAsync<T>(Func<IDbConnection, IDbTransaction, Task<T>> action)
        {
            using var connection = await GetConnectionAsync();
            using var transaction = connection.BeginTransaction();
            
            try
            {
                var result = await action(connection, transaction);
                transaction.Rollback(); // Always rollback for tests
                return result;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        #endregion

        #region Data Seeding

        /// <summary>
        /// Seeds test users into the database
        /// </summary>
        public async Task<List<User>> SeedUsersAsync(int count = 5)
        {
            var users = TestDataGenerator.Scenarios.NewUser().User;
            var userList = new List<User>();

            using var connection = await GetConnectionAsync();

            for (int i = 0; i < count; i++)
            {
                var user = TestDataGenerator.Scenarios.NewUser().User;
                user.Id = TestHelpers.GenerateTestId("seed-user");
                
                await connection.ExecuteAsync(@"
                    INSERT INTO public.""User"" (id, email, ""fullName"", ""firstName"", ""lastName"", role, ""userType"", ""isActive"", ""createdAt"", ""updatedAt"")
                    VALUES (@Id, @Email, @FullName, @FirstName, @LastName, @Role, @UserType, @IsActive, @CreatedAt, @UpdatedAt)",
                    user);
                
                userList.Add(user);
            }

            return userList;
        }

        /// <summary>
        /// Seeds test properties into the database
        /// </summary>
        public async Task<List<Property>> SeedPropertiesAsync(string sellerId, int count = 3)
        {
            var properties = new List<Property>();

            using var connection = await GetConnectionAsync();

            for (int i = 0; i < count; i++)
            {
                var property = TestDataGenerator.GenerateRealisticProperty(sellerId);
                property.Id = TestHelpers.GenerateTestId("seed-prop");
                
                await connection.ExecuteAsync(@"
                    INSERT INTO public.""Property"" (id, title, description, price, ""propertyType"", bedrooms, bathrooms, ""sellerId"", status, ""createdAt"", ""updatedAt"")
                    VALUES (@Id, @Title, @Description, @Price, @PropertyType, @Bedrooms, @Bathrooms, @SellerId, @Status, @CreatedAt, @UpdatedAt)",
                    property);
                
                properties.Add(property);
            }

            return properties;
        }

        /// <summary>
        /// Seeds test advertisers into the database
        /// </summary>
        public async Task<List<Advertiser>> SeedAdvertisersAsync(int count = 3)
        {
            var advertisers = new List<Advertiser>();

            using var connection = await GetConnectionAsync();

            for (int i = 0; i < count; i++)
            {
                var advertiser = TestDataGenerator.GenerateRealisticAdvertiser();
                advertiser.Id = TestHelpers.GenerateTestId("seed-adv");
                
                await connection.ExecuteAsync(@"
                    INSERT INTO public.""Advertiser"" (id, ""userId"", ""businessName"", email, ""serviceType"", plan, status, ""createdAt"", ""updatedAt"")
                    VALUES (@Id, @UserId, @BusinessName, @Email, @ServiceType, @Plan, @Status, @CreatedAt, @UpdatedAt)",
                    advertiser);
                
                advertisers.Add(advertiser);
            }

            return advertisers;
        }

        /// <summary>
        /// Seeds a complete user scenario with related data
        /// </summary>
        public async Task<UserScenario> SeedUserScenarioAsync(UserRole role = UserRole.USER, SnbUserType userType = SnbUserType.Buyer)
        {
            var scenario = TestDataGenerator.GenerateUserScenario(role, userType);
            scenario.User.Id = TestHelpers.GenerateTestId("scenario-user");

            using var connection = await GetConnectionAsync();

            // Seed user
            await connection.ExecuteAsync(@"
                INSERT INTO public.""User"" (id, email, ""fullName"", ""firstName"", ""lastName"", role, ""userType"", ""isActive"", ""createdAt"", ""updatedAt"")
                VALUES (@Id, @Email, @FullName, @FirstName, @LastName, @Role, @UserType, @IsActive, @CreatedAt, @UpdatedAt)",
                scenario.User);

            // Seed properties if any
            foreach (var property in scenario.Properties)
            {
                property.Id = TestHelpers.GenerateTestId("scenario-prop");
                property.SellerId = scenario.User.Id;
                
                await connection.ExecuteAsync(@"
                    INSERT INTO public.""Property"" (id, title, description, price, ""propertyType"", bedrooms, bathrooms, ""sellerId"", status, ""createdAt"", ""updatedAt"")
                    VALUES (@Id, @Title, @Description, @Price, @PropertyType, @Bedrooms, @Bathrooms, @SellerId, @Status, @CreatedAt, @UpdatedAt)",
                    property);
            }

            // Seed advertisers if any
            foreach (var advertiser in scenario.Advertisers)
            {
                advertiser.Id = TestHelpers.GenerateTestId("scenario-adv");
                advertiser.UserId = scenario.User.Id;
                
                await connection.ExecuteAsync(@"
                    INSERT INTO public.""Advertiser"" (id, ""userId"", ""businessName"", email, ""serviceType"", plan, status, ""createdAt"", ""updatedAt"")
                    VALUES (@Id, @UserId, @BusinessName, @Email, @ServiceType, @Plan, @Status, @CreatedAt, @UpdatedAt)",
                    advertiser);
            }

            return scenario;
        }

        #endregion

        #region Data Cleanup

        /// <summary>
        /// Cleans up all test data with specific prefix
        /// </summary>
        public async Task CleanupTestDataAsync(string prefix = "test-")
        {
            using var connection = await GetConnectionAsync();

            // Clean up in reverse order of dependencies
            await connection.ExecuteAsync($@"DELETE FROM public.""Project"" WHERE id LIKE '{prefix}%'");
            await connection.ExecuteAsync($@"DELETE FROM public.""Advertiser"" WHERE id LIKE '{prefix}%'");
            await connection.ExecuteAsync($@"DELETE FROM public.""Property"" WHERE id LIKE '{prefix}%'");
            await connection.ExecuteAsync($@"DELETE FROM public.""User"" WHERE id LIKE '{prefix}%'");
        }

        /// <summary>
        /// Cleans up specific test data by IDs
        /// </summary>
        public async Task CleanupByIdsAsync(params string[] ids)
        {
            if (!ids.Any()) return;

            using var connection = await GetConnectionAsync();
            var idList = string.Join("','", ids);

            await connection.ExecuteAsync($@"DELETE FROM public.""Project"" WHERE id IN ('{idList}')");
            await connection.ExecuteAsync($@"DELETE FROM public.""Advertiser"" WHERE id IN ('{idList}')");
            await connection.ExecuteAsync($@"DELETE FROM public.""Property"" WHERE id IN ('{idList}')");
            await connection.ExecuteAsync($@"DELETE FROM public.""User"" WHERE id IN ('{idList}')");
        }

        #endregion

        #region Assertions

        /// <summary>
        /// Asserts that a record exists in the database
        /// </summary>
        public async Task AssertRecordExistsAsync<T>(string tableName, string id)
        {
            using var connection = await GetConnectionAsync();
            var exists = await connection.QuerySingleAsync<bool>(
                $@"SELECT EXISTS(SELECT 1 FROM public.""{tableName}"" WHERE id = @id)",
                new { id });
            
            exists.Should().BeTrue($"Record with ID {id} should exist in table {tableName}");
        }

        /// <summary>
        /// Asserts that a record does not exist in the database
        /// </summary>
        public async Task AssertRecordNotExistsAsync<T>(string tableName, string id)
        {
            using var connection = await GetConnectionAsync();
            var exists = await connection.QuerySingleAsync<bool>(
                $@"SELECT EXISTS(SELECT 1 FROM public.""{tableName}"" WHERE id = @id)",
                new { id });
            
            exists.Should().BeFalse($"Record with ID {id} should not exist in table {tableName}");
        }

        /// <summary>
        /// Asserts the count of records in a table
        /// </summary>
        public async Task AssertRecordCountAsync(string tableName, int expectedCount, string? whereClause = null)
        {
            using var connection = await GetConnectionAsync();
            var sql = $@"SELECT COUNT(*) FROM public.""{tableName}""";
            if (!string.IsNullOrEmpty(whereClause))
                sql += $" WHERE {whereClause}";

            var count = await connection.QuerySingleAsync<int>(sql);
            count.Should().Be(expectedCount, $"Table {tableName} should have {expectedCount} records");
        }

        /// <summary>
        /// Gets a record by ID and asserts it exists
        /// </summary>
        public async Task<T> GetRecordByIdAsync<T>(string tableName, string id)
        {
            using var connection = await GetConnectionAsync();
            var record = await connection.QueryFirstOrDefaultAsync<T>(
                $@"SELECT * FROM public.""{tableName}"" WHERE id = @id",
                new { id });
            
            record.Should().NotBeNull($"Record with ID {id} should exist in table {tableName}");
            return record!;
        }

        #endregion

        #region Query Helpers

        /// <summary>
        /// Executes a query and returns results
        /// </summary>
        public async Task<IEnumerable<T>> QueryAsync<T>(string sql, object? parameters = null)
        {
            using var connection = await GetConnectionAsync();
            return await connection.QueryAsync<T>(sql, parameters);
        }

        /// <summary>
        /// Executes a query and returns single result
        /// </summary>
        public async Task<T> QuerySingleAsync<T>(string sql, object? parameters = null)
        {
            using var connection = await GetConnectionAsync();
            return await connection.QuerySingleAsync<T>(sql, parameters);
        }

        /// <summary>
        /// Executes a command and returns affected rows
        /// </summary>
        public async Task<int> ExecuteAsync(string sql, object? parameters = null)
        {
            using var connection = await GetConnectionAsync();
            return await connection.ExecuteAsync(sql, parameters);
        }

        /// <summary>
        /// Executes a stored procedure
        /// </summary>
        public async Task<IEnumerable<T>> ExecuteStoredProcedureAsync<T>(string procedureName, object? parameters = null)
        {
            using var connection = await GetConnectionAsync();
            return await connection.QueryAsync<T>(procedureName, parameters, commandType: CommandType.StoredProcedure);
        }

        #endregion

        #region Performance Testing

        /// <summary>
        /// Measures query execution time
        /// </summary>
        public async Task<(T result, TimeSpan duration)> MeasureQueryAsync<T>(string sql, object? parameters = null)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = await QuerySingleAsync<T>(sql, parameters);
            stopwatch.Stop();
            return (result, stopwatch.Elapsed);
        }

        /// <summary>
        /// Tests query performance with multiple executions
        /// </summary>
        public async Task<PerformanceResult> TestQueryPerformanceAsync(string sql, object? parameters = null, int iterations = 10)
        {
            var durations = new List<TimeSpan>();

            for (int i = 0; i < iterations; i++)
            {
                var (_, duration) = await MeasureQueryAsync<object>(sql, parameters);
                durations.Add(duration);
            }

            return new PerformanceResult
            {
                Iterations = iterations,
                AverageDuration = TimeSpan.FromTicks((long)durations.Average(d => d.Ticks)),
                MinDuration = durations.Min(),
                MaxDuration = durations.Max(),
                TotalDuration = TimeSpan.FromTicks(durations.Sum(d => d.Ticks))
            };
        }

        #endregion

        public void Dispose()
        {
            foreach (var connection in _connections)
            {
                connection?.Dispose();
            }
            _connections.Clear();
        }
    }

    /// <summary>
    /// Performance test result
    /// </summary>
    public class PerformanceResult
    {
        public int Iterations { get; set; }
        public TimeSpan AverageDuration { get; set; }
        public TimeSpan MinDuration { get; set; }
        public TimeSpan MaxDuration { get; set; }
        public TimeSpan TotalDuration { get; set; }

        public override string ToString()
        {
            return $"Iterations: {Iterations}, Avg: {AverageDuration.TotalMilliseconds:F2}ms, " +
                   $"Min: {MinDuration.TotalMilliseconds:F2}ms, Max: {MaxDuration.TotalMilliseconds:F2}ms";
        }
    }
}
