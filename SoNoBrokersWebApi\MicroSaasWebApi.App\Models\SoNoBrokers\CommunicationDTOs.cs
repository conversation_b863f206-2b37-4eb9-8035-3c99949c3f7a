using System.ComponentModel.DataAnnotations;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    // Contact Concierge DTOs
    public class ContactConciergeRequest
    {
        [Required]
        public string Name { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        public string? Phone { get; set; }

        public string? PropertyAddress { get; set; }

        public string? PropertyValue { get; set; }

        public string? Timeline { get; set; }

        public string? Requirements { get; set; }

        public string? PackageInterest { get; set; }

        [Required]
        public string Country { get; set; } = string.Empty;

        public string? SelectedPackage { get; set; }
    }

    public class ContactConciergeResponse
    {
        public string Message { get; set; } = string.Empty;
        public bool Success { get; set; }
    }

    // Waiting List DTOs
    public class WaitingListRequest
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        public string? Name { get; set; }
        public string? Source { get; set; }
    }

    public class WaitingListResponse
    {
        public string Message { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? ContactId { get; set; }
    }

    // Geo Location DTOs
    public class GeoLocationRequest
    {
        public string? Ip { get; set; }
    }

    public class GeoLocationResponse
    {
        public string Country { get; set; } = string.Empty;
        public string CountryName { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string Region { get; set; } = string.Empty;
        public string Timezone { get; set; } = string.Empty;
    }

    // Enum Values DTOs
    public class EnumValuesResponse
    {
        public Dictionary<string, object> Enums { get; set; } = new();
    }

    public class CountryInfo
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Flag { get; set; } = string.Empty;
    }

    public class ServiceTypeInfo
    {
        public string Value { get; set; } = string.Empty;
        public string Label { get; set; } = string.Empty;
    }
}
