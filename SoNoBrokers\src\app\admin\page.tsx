import { requireAdmin } from '@/lib/auth'
import { UserService } from '@/services/userService'
import AdminDashboard from '@/components/_archive/admin/AdminDashboard'
import { UserRole } from '@/types'

// Force dynamic rendering for admin pages
export const dynamic = 'force-dynamic';

export default async function AdminPage() {
  // Require admin role to access this page
  const currentUser = await requireAdmin()

  // Get dashboard data
  const usersData = await UserService.getAllUsers(1, 10)

  // Get user counts by role
  const [adminCount, userCount, productCount, operatorCount] = await Promise.all([
    UserService.getAllUsers(1, 1, UserRole.ADMIN),
    UserService.getAllUsers(1, 1, UserRole.USER),
    UserService.getAllUsers(1, 1, UserRole.PRODUCT),
    UserService.getAllUsers(1, 1, UserRole.OPERATOR)
  ])

  const dashboardStats = {
    totalUsers: usersData.total,
    adminUsers: adminCount.total,
    regularUsers: userCount.total,
    productUsers: productCount.total,
    operatorUsers: operatorCount.total
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Admin Dashboard
            </h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Welcome back, {currentUser.fullName || currentUser.email}
            </p>
          </div>

          <AdminDashboard
            currentUser={currentUser}
            dashboardStats={dashboardStats}
            recentUsers={usersData.users}
          />
        </div>
      </div>
    </div>
  )
}
