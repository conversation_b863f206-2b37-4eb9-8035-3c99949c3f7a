using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MicroSaasWebApi.Models.Core
{
    /// <summary>
    /// Subscription model for payment management
    /// </summary>
    [Table("subscriptions", Schema = "core")]
    public class Subscription
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        public Guid UserId { get; set; }

        [Required]
        [MaxLength(100)]
        public string StripeCustomerId { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? StripeSubscriptionId { get; set; }

        [Required]
        [MaxLength(50)]
        public string PlanId { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string PlanName { get; set; } = string.Empty;

        [Required]
        [MaxLength(20)]
        public string Status { get; set; } = "inactive"; // active, inactive, canceled, past_due, trialing

        [Column(TypeName = "decimal(10,2)")]
        public decimal Amount { get; set; }

        [MaxLength(3)]
        public string Currency { get; set; } = "USD";

        [MaxLength(20)]
        public string Interval { get; set; } = "month"; // month, year

        public int IntervalCount { get; set; } = 1;

        public DateTime? TrialStart { get; set; }
        public DateTime? TrialEnd { get; set; }
        public DateTime? CurrentPeriodStart { get; set; }
        public DateTime? CurrentPeriodEnd { get; set; }
        public DateTime? CanceledAt { get; set; }
        public DateTime? EndedAt { get; set; }

        public bool CancelAtPeriodEnd { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual User User { get; set; } = null!;
    }
}
