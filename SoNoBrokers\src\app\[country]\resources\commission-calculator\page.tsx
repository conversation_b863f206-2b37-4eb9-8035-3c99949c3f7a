import { CommissionCalculator } from '@/components/shared/resources/CommissionCalculator'
import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function CommissionCalculatorPage({ params, searchParams }: PageProps) {
  // Resources pages don't require authentication
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us', 'uae']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/resources/commission-calculator')
  }

  // Default to seller for commission calculator
  const userType = resolvedSearchParams.userType || 'seller'

  return (
    <CommissionCalculator
      userType={userType}
      isSignedIn={isSignedIn}
      country={country.toUpperCase()}
    />
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'seller'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Real Estate Commission Calculator for ${country} | SoNoBrokers`,
    description: `Calculate how much you can save on real estate commissions in ${country}. Compare traditional agent fees vs SoNoBrokers' low commission structure.`,
    keywords: `commission calculator, real estate fees, agent commission, ${country}, savings calculator, SoNoBrokers`,
  }
}
