using MicroSaasWebApi.Models.SoNoBrokers;

namespace MicroSaasWebApi.Services.SoNoBrokers.Interfaces
{
    public interface IAIPropertyService
    {
        Task<AIPropertyImportResponse> ImportPropertyDataAsync(AIPropertyImportRequest request);
        Task<AIPropertyValuationResponse> GetPropertyValuationAsync(AIPropertyValuationRequest request);
        Task<GenerateDescriptionResponse> GeneratePropertyDescriptionAsync(GenerateDescriptionRequest request);
    }
}
