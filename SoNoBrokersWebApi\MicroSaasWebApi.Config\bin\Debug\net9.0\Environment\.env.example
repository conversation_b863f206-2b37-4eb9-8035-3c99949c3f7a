# =============================================================================
# ENVIRONMENT VARIABLES TEMPLATE
# =============================================================================
# Copy this file to .env.dev, .env.uat, or .env.prod and fill in the values
# This template shows all required environment variables for the application
# =============================================================================

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Single database connection using DATABASE_URL (consolidated configuration)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/sonobrokers

# =============================================================================
# API ENDPOINTS (Environment-specific URLs)
# =============================================================================
ApiEndpoints__DataApi__BaseUrl=https://your-environment-api.microsaas.com/data-api/v1
ApiEndpoints__DocumentApi__BaseUrl=https://your-environment-api.microsaas.com/document-api/v1
ApiEndpoints__PermissionApi__BaseUrl=https://your-environment-api.microsaas.com/permission-api/v1

# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================
# Clerk Authentication
Authentication__Clerk__PublishableKey=pk_test_your_clerk_publishable_key_here
Authentication__Clerk__SecretKey=sk_test_your_clerk_secret_key_here
Authentication__Clerk__WebhookSecret=whsec_your_clerk_webhook_secret_here
Authentication__Clerk__JwtIssuer=https://your-clerk-domain.clerk.accounts.dev

# JWT Configuration
Authentication__Jwt__Issuer=https://your-api-domain.com
Authentication__Jwt__SigningKey=your_jwt_signing_key_minimum_32_characters_long

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Stripe Payment Service
ExternalServices__Stripe__PublishableKey=pk_test_your_stripe_publishable_key_here
ExternalServices__Stripe__SecretKey=sk_test_your_stripe_secret_key_here
ExternalServices__Stripe__WebhookSecret=whsec_your_stripe_webhook_secret_here

# Azure Services
ExternalServices__Azure__Storage__ConnectionString=DefaultEndpointsProtocol=https;AccountName=your_storage_account;AccountKey=your_storage_key;EndpointSuffix=core.windows.net
ExternalServices__Azure__ServiceBus__ConnectionString=Endpoint=sb://your-servicebus.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your_key
ExternalServices__Azure__KeyVault__Uri=https://your-keyvault.vault.azure.net/
ExternalServices__Azure__KeyVault__ClientId=your_azure_client_id
ExternalServices__Azure__KeyVault__ClientSecret=your_azure_client_secret
ExternalServices__Azure__KeyVault__TenantId=your_azure_tenant_id

# Email Services
ExternalServices__Email__SendGrid__ApiKey=SG.your_sendgrid_api_key_here
ExternalServices__Email__Smtp__Host=smtp.your-provider.com
ExternalServices__Email__Smtp__Username=<EMAIL>
ExternalServices__Email__Smtp__Password=your_smtp_password
ExternalServices__Email__Resend__ApiKey=re_your_resend_api_key_here

# =============================================================================
# EXTERNAL API INTEGRATIONS
# =============================================================================
# Make.com Integration
ExternalServices__Make__OrganizationId=your_make_organization_id
ExternalServices__Make__TeamId=your_make_team_id
ExternalServices__Make__ApiKey=your_make_api_key
ExternalServices__Make__ApiUrl=https://us1.make.com/api/v2

# N8N Integration
ExternalServices__N8N__ApiKey=your_n8n_api_key
ExternalServices__N8N__ApiUrl=https://your-n8n-instance.com/api/v1
ExternalServices__N8N__WebhookUrl=https://your-n8n-instance.com/webhook

# =============================================================================
# DATABASE CONFIGURATION (Additional)
# =============================================================================
Database__Postgres__User=postgres
Database__Postgres__Password=your_postgres_password
Database__Postgres__DatabaseName=your_database_name
Database__Postgres__Host=your_database_host
Database__Postgres__Port=5432

# Legacy Database (Prisma)
Database__Legacy__DatabaseUrl=postgresql://user:password@host:port/database

# =============================================================================
# LEGACY SYSTEMS (Optional)
# =============================================================================
LEGACY_DATABASE_URL=postgresql://user:password@host:port/database

# WordPress Integration
ExternalServices__WordPress__DatabaseUser=wp_user
ExternalServices__WordPress__DatabasePassword=wp_password
ExternalServices__WordPress__DatabaseName=wp_database
ExternalServices__WordPress__RestEndpoint=https://your-wordpress-site.com
ExternalServices__WordPress__MySqlDatabase=wp_mysql_db
ExternalServices__WordPress__MySqlUser=mysql_user
ExternalServices__WordPress__MySqlPassword=mysql_password

# =============================================================================
# FRONTEND INTEGRATION
# =============================================================================
Frontend__NextJs__AppUrl=http://localhost:3000
Frontend__NextJs__SignInUrl=/sign-in
Frontend__NextJs__SignUpUrl=/sign-up

# Next.js Public Keys (for reference)
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up

# =============================================================================
# INSTRUCTIONS
# =============================================================================
# 1. Copy this file to .env.dev for development
# 2. Replace all placeholder values with actual values
# 3. Never commit .env.dev, .env.uat, or .env.prod files to source control
# 4. Use Azure Key Vault for production secrets
# 5. Build pipelines should replace tokens with actual values
