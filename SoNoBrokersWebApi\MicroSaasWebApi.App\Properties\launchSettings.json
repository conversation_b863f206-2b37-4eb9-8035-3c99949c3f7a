{"profiles": {"https": {"commandName": "Project", "launchBrowser": true, "launchUrl": "scalar/v1", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "APPCONFIGURATION_ENDPOINT": "", "APPCONFIGURATION_CONNECTIONSTRING": "", "APPCONFIGURATION_ENVIRONMENT_LABEL": "", "KEYVAULT_URI": "", "AZURE_CLIENT_ID": "", "AZURE_CLIENT_SECRET": "", "AZURE_TENANT_ID": ""}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7163;http://localhost:5005"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "scalar/v1", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/scalar/v1", "publishAllPorts": true, "useSSL": true}}, "$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:28931", "sslPort": 44384}}}