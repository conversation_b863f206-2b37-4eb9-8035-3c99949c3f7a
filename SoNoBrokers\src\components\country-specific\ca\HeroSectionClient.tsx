'use client'

import { But<PERSON> } from '@/components/ui/button'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerD<PERSON><PERSON>,
  DrawerFooter,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from '@/components/ui/drawer'
import { SignInButton } from '@clerk/nextjs'
import {
  ArrowRight,
  Home,
  DollarSign,
  Users,
  Shield,
  CheckCircle,
  Phone,
  Mail,
  Globe
} from 'lucide-react'
import { useState } from 'react'
import { useAppContext } from '@/contexts/AppContext'

interface HeroSectionProps {
  userType: 'buyer' | 'seller'
}

export function HeroSection({ userType }: HeroSectionProps) {
  // Get authentication state from AppContext
  const { isSignedIn } = useAppContext()
  const [isLearnMoreOpen, setIsLearnMoreOpen] = useState(false)

  const buyerContent = {
    title: "Find Your Dream Home in Canada",
    subtitle: "Commission-Free Property Marketplace",
    description: "Browse thousands of properties directly from sellers. No realtor fees, no hidden costs. Connect directly with property owners and save thousands on your next home purchase.",
    cta: isSignedIn ? "Browse Properties" : "Start Searching",
    ctaLink: isSignedIn ? "/ca/properties" : null,
    features: [
      { icon: DollarSign, text: "No Commission Fees", detail: "Save up to $25,000 on average home purchase" },
      { icon: Home, text: "Direct Seller Contact", detail: "Connect instantly with property owners" },
      { icon: Shield, text: "Verified Listings", detail: "All properties verified by our team" },
      { icon: Users, text: "Professional Services", detail: "Access to lawyers, inspectors & more" }
    ],
    stats: [
      { value: "15,000+", label: "Active Listings" },
      { value: "$0", label: "Buyer Fees" },
      { value: "24/7", label: "Support" },
      { value: "98%", label: "Satisfaction" }
    ]
  }

  const sellerContent = {
    title: "Sell Your Property Commission-Free",
    subtitle: "Keep More of Your Sale Price",
    description: "List your property directly to buyers and save thousands in realtor commissions. Get access to professional photography, legal services, and marketing tools to sell faster.",
    cta: isSignedIn ? "List Property" : "Start Selling",
    ctaLink: isSignedIn ? "/ca/list-property" : null,
    features: [
      { icon: DollarSign, text: "Save on Commissions", detail: "Keep 100% of your sale price" },
      { icon: Users, text: "Direct Buyer Access", detail: "Connect with serious buyers instantly" },
      { icon: Shield, text: "Professional Support", detail: "Full legal and marketing support" },
      { icon: Home, text: "Marketing Tools", detail: "Professional photos & virtual tours" }
    ],
    stats: [
      { value: "5%", label: "Commission Saved" },
      { value: "30", label: "Days Avg Sale" },
      { value: "2.5K+", label: "Sold Properties" },
      { value: "95%", label: "Success Rate" }
    ]
  }

  const content = userType === 'buyer' ? buyerContent : sellerContent

  const handleStartAction = () => {
    if (isSignedIn) {
      // User is signed in, redirect to appropriate page
      window.location.href = content.ctaLink!
    }
    // For non-signed-in users, the SignInButton will handle the modal
  }

  return (
    <section className="relative bg-gradient-to-br from-primary/5 via-background to-accent/10 pt-8 pb-4 lg:pt-12 lg:pb-6 border-b border-primary/20">
      <div className="container mx-auto px-1">
        <div className="max-w-4xl mx-auto">
          {/* Content */}
          <div className="space-y-3">
            <div className="space-y-1">
              <h1 className="text-2xl lg:text-4xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent leading-tight">
                {content.title}
              </h1>
              <p className="text-base text-primary font-semibold">
                {content.subtitle}
              </p>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {content.description}
              </p>
            </div>

            {/* Features Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {content.features.map((feature, index) => (
                <div key={index} className="flex items-start gap-2 p-2 rounded-xl bg-gradient-to-r from-primary/10 to-accent/5 border border-primary/20 shadow-sm hover:shadow-md hover:border-primary/30 transition-all duration-200">
                  <feature.icon className="h-4 w-4 text-primary flex-shrink-0 mt-0.5" />
                  <div className="flex-1 min-w-0">
                    <span className="text-sm font-medium text-foreground block">{feature.text}</span>
                    <span className="text-xs text-primary/70">{feature.detail}</span>
                  </div>
                </div>
              ))}
            </div>

            {/* CTA Buttons - Aligned with columns above */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {isSignedIn ? (
                <Button
                  onClick={handleStartAction}
                  className="text-sm px-2 py-1 h-8 justify-start bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-primary-foreground border-primary/20 shadow-md"
                >
                  {content.cta}
                  <ArrowRight className="ml-1 h-3 w-3" />
                </Button>
              ) : (
                <SignInButton mode="modal">
                  <Button className="text-sm px-2 py-1 h-8 justify-start bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-primary-foreground border-primary/20 shadow-md">
                    {content.cta}
                    <ArrowRight className="ml-1 h-3 w-3" />
                  </Button>
                </SignInButton>
              )}

              <Drawer open={isLearnMoreOpen} onOpenChange={setIsLearnMoreOpen}>
                <DrawerTrigger asChild>
                  <Button variant="outline" className="text-sm px-2 py-1 h-8 justify-start border-primary/30 text-primary hover:bg-primary/10 hover:border-primary/50 shadow-sm">
                    Learn More
                  </Button>
                </DrawerTrigger>
                <DrawerContent className="bg-background border-border">
                  <DrawerHeader className="bg-background border-b border-border">
                    <DrawerTitle className="text-xl font-bold text-foreground">
                      {userType === 'buyer' ? 'How SoNo Brokers Helps Buyers' : 'How SoNo Brokers Helps Sellers'}
                    </DrawerTitle>
                    <DrawerDescription className="text-base text-muted-foreground">
                      {userType === 'buyer'
                        ? 'Discover how our commission-free platform saves you thousands on your home purchase.'
                        : 'Learn how to sell your property directly to buyers and keep 100% of your sale price.'
                      }
                    </DrawerDescription>
                  </DrawerHeader>

                  <div className="px-4 pb-4 space-y-4 bg-background">
                    {/* Detailed Features */}
                    <div className="grid gap-3">
                      {content.features.map((feature, index) => (
                        <div key={index} className="flex items-start gap-3 p-3 rounded-xl bg-card border border-border shadow-sm hover:shadow-md hover:border-primary/30 transition-all duration-200">
                          <feature.icon className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                          <div>
                            <h4 className="font-semibold text-foreground text-base">{feature.text}</h4>
                            <p className="text-sm text-muted-foreground mt-1">{feature.detail}</p>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Contact Information */}
                    <div className="border-t border-border pt-4 bg-card rounded-xl p-3">
                      <h4 className="font-semibold mb-3 text-base text-foreground">Get Started Today</h4>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <div className="flex items-center gap-2 p-2 rounded-lg bg-background border border-border hover:border-primary/30 transition-colors">
                          <Phone className="h-4 w-4 text-primary" />
                          <span className="text-sm font-medium text-foreground">************</span>
                        </div>
                        <div className="flex items-center gap-2 p-2 rounded-lg bg-background border border-border hover:border-primary/30 transition-colors">
                          <Mail className="h-4 w-4 text-primary" />
                          <span className="text-sm font-medium text-foreground"><EMAIL></span>
                        </div>
                        <div className="flex items-center gap-2 p-2 rounded-lg bg-background border border-border hover:border-primary/30 transition-colors">
                          <Globe className="h-4 w-4 text-primary" />
                          <span className="text-sm font-medium text-foreground">Available 24/7</span>
                        </div>
                        <div className="flex items-center gap-2 p-2 rounded-lg bg-background border border-border hover:border-primary/30 transition-colors">
                          <CheckCircle className="h-4 w-4 text-primary" />
                          <span className="text-sm font-medium text-foreground">Free Consultation</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <DrawerFooter className="bg-background border-t border-border">
                    <DrawerClose asChild>
                      <Button variant="outline" className="h-10 text-base font-medium">Close</Button>
                    </DrawerClose>
                  </DrawerFooter>
                </DrawerContent>
              </Drawer>
            </div>
          </div>
        </div>
      </div>


    </section>
  )
}
