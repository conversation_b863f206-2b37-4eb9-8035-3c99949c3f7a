import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Scale, 
  Camera, 
  Home, 
  Shield, 
  Truck, 
  Calculator,
  FileText,
  MapPin,
  Star,
  Award
} from 'lucide-react';

interface ServiceProvidersProps {
  userType: 'buyer' | 'seller';
}

export default function UAEServiceProviders({ userType }: ServiceProvidersProps) {
  const services = [
    {
      icon: Scale,
      title: 'RERA Licensed Lawyers',
      description: 'UAE-licensed attorneys specializing in property law',
      features: ['RERA compliance', 'DLD registration', 'Ejari services', 'Contract review'],
      price: 'From AED 2,500',
      popular: true,
      emirate: 'All Emirates'
    },
    {
      icon: Calculator,
      title: 'Islamic & Conventional Mortgages',
      description: 'Sharia-compliant and conventional financing options',
      features: ['Islamic mortgages', 'Expat financing', 'Off-plan payment plans', 'Pre-approval'],
      price: 'Free consultation',
      popular: false,
      emirate: 'UAE-wide'
    },
    {
      icon: Camera,
      title: 'Luxury Property Photography',
      description: 'Premium photography for high-end properties',
      features: ['4K photography', '3D virtual tours', 'Drone permits included', 'Arabic/English marketing'],
      price: 'From AED 800',
      popular: false,
      emirate: 'Dubai & Abu Dhabi'
    },
    {
      icon: Home,
      title: 'Property Management',
      description: 'RERA-licensed property management services',
      features: ['Tenant placement', 'DEWA management', 'Maintenance services', 'Rent collection'],
      price: 'From 5% rental value',
      popular: true,
      emirate: 'All Emirates'
    },
    {
      icon: Shield,
      title: 'Property Insurance',
      description: 'Comprehensive property and contents insurance',
      features: ['Building insurance', 'Contents coverage', 'Landlord protection', 'Takaful options'],
      price: 'From AED 500/year',
      popular: false,
      emirate: 'UAE-wide'
    },
    {
      icon: Truck,
      title: 'International Moving',
      description: 'Global relocation and moving services',
      features: ['International shipping', 'Customs clearance', 'Pet relocation', 'Storage facilities'],
      price: 'From AED 5,000',
      popular: false,
      emirate: 'Dubai & Abu Dhabi'
    }
  ];

  return (
    <section className="py-16 bg-gradient-to-br from-emerald-50 via-gold-50 to-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Premium Service Providers Across the UAE
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Connect with RERA-licensed professionals and luxury service providers across all seven emirates
          </p>
          <div className="flex items-center justify-center mt-4 gap-2">
            <Badge variant="outline" className="text-emerald-600 border-emerald-200">
              🇦🇪 UAE Licensed
            </Badge>
            <Badge variant="outline" className="text-gold-600 border-gold-200">
              ⭐ Premium Quality
            </Badge>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card key={index} className={`relative transition-all duration-300 hover:shadow-lg hover:-translate-y-1 ${service.popular ? 'ring-2 ring-emerald-500' : ''}`}>
              {service.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-emerald-500 text-white">
                    <Star className="h-3 w-3 mr-1" />
                    Most Popular
                  </Badge>
                </div>
              )}
              
              <CardHeader className="text-center pb-4">
                <div className="mx-auto mb-4 p-3 bg-emerald-100 rounded-full w-fit">
                  <service.icon className="h-8 w-8 text-emerald-600" />
                </div>
                <CardTitle className="text-xl font-bold text-gray-900">
                  {service.title}
                </CardTitle>
                <p className="text-gray-600 text-sm">
                  {service.description}
                </p>
              </CardHeader>

              <CardContent className="space-y-4">
                <div className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                      {feature}
                    </div>
                  ))}
                </div>

                <div className="pt-4 border-t">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-lg font-bold text-emerald-600">
                      {service.price}
                    </span>
                    <div className="flex items-center text-sm text-gray-500">
                      <MapPin className="h-4 w-4 mr-1" />
                      {service.emirate}
                    </div>
                  </div>
                  
                  <Button className="w-full bg-emerald-600 hover:bg-emerald-700 text-white">
                    Find {service.title}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-gray-600 mb-6">
            Need guidance for your {userType === 'buyer' ? 'property purchase' : 'property sale'} in the UAE?
          </p>
          <Button variant="outline" className="border-emerald-500 text-emerald-600 hover:bg-emerald-50">
            <FileText className="h-4 w-4 mr-2" />
            Get Expert Consultation
          </Button>
        </div>

        <div className="mt-16 bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-2xl p-8 text-white text-center">
          <div className="flex items-center justify-center mb-4">
            <Award className="h-8 w-8 mr-2" />
            <h3 className="text-2xl font-bold">
              RERA Certified Excellence
            </h3>
          </div>
          <p className="text-emerald-100 mb-6 max-w-2xl mx-auto">
            All our service providers are RERA-licensed and comply with UAE regulations. 
            From Dubai's luxury market to Abu Dhabi's business district, we ensure premium service quality.
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold">7</div>
              <div className="text-emerald-200 text-sm">Emirates Covered</div>
            </div>
            <div>
              <div className="text-2xl font-bold">500+</div>
              <div className="text-emerald-200 text-sm">Licensed Professionals</div>
            </div>
            <div>
              <div className="text-2xl font-bold">24/7</div>
              <div className="text-emerald-200 text-sm">Arabic & English Support</div>
            </div>
            <div>
              <div className="text-2xl font-bold">4.9★</div>
              <div className="text-emerald-200 text-sm">Client Satisfaction</div>
            </div>
          </div>
        </div>

        <div className="mt-12 grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card className="bg-gradient-to-br from-gold-50 to-yellow-50 border-gold-200">
            <CardContent className="p-6">
              <h4 className="text-lg font-bold text-gold-800 mb-3">Golden Visa Services</h4>
              <p className="text-gold-700 text-sm mb-4">
                Specialized assistance for Golden Visa applications through property investment
              </p>
              <Button variant="outline" className="border-gold-500 text-gold-700 hover:bg-gold-100">
                Learn More
              </Button>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
            <CardContent className="p-6">
              <h4 className="text-lg font-bold text-blue-800 mb-3">Off-Plan Specialists</h4>
              <p className="text-blue-700 text-sm mb-4">
                Expert guidance for off-plan property purchases and payment plan management
              </p>
              <Button variant="outline" className="border-blue-500 text-blue-700 hover:bg-blue-100">
                Get Started
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
