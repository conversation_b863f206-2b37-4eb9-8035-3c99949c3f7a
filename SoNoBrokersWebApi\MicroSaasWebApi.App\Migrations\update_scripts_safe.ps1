# PowerShell script to make all SQL scripts safe with IF NOT EXISTS conditions
# This script updates all CREATE TABLE statements to include IF NOT EXISTS

Write-Host "🔧 Making SQL scripts safe with IF NOT EXISTS conditions..." -ForegroundColor Green

# Script 1: Update remaining CREATE TABLE statements in 01_create_database.sql
$script1 = "01_create_database.sql"
if (Test-Path $script1) {
    Write-Host "Updating $script1..." -ForegroundColor Yellow
    
    $content = Get-Content $script1 -Raw
    
    # Replace all remaining CREATE TABLE statements
    $content = $content -replace 'CREATE TABLE public\."([^"]+)" \(', 'CREATE TABLE IF NOT EXISTS public."$1" ('
    
    # Replace CREATE INDEX statements
    $content = $content -replace 'CREATE INDEX "([^"]+)"', 'CREATE INDEX IF NOT EXISTS "$1"'
    
    # Replace CREATE TRIGGER statements
    $content = $content -replace 'CREATE TRIGGER ([^\s]+)', 'CREATE OR REPLACE TRIGGER $1'
    
    Set-Content $script1 -Value $content -Encoding UTF8
    Write-Host "✅ Updated $script1" -ForegroundColor Green
}

# Script 2: Update 02_missing_tables_migration.sql
$script2 = "02_missing_tables_migration.sql"
if (Test-Path $script2) {
    Write-Host "Updating $script2..." -ForegroundColor Yellow
    
    $content = Get-Content $script2 -Raw
    
    # Replace CREATE TYPE statements with safe versions
    $content = $content -replace 'CREATE TYPE "([^"]+)" AS ENUM', 'DO $$ BEGIN CREATE TYPE "$1" AS ENUM'
    $content = $content -replace '\);(\s*\n)', '); EXCEPTION WHEN duplicate_object THEN null; END $$;$1'
    
    # Replace CREATE TABLE statements
    $content = $content -replace 'CREATE TABLE public\."([^"]+)" \(', 'CREATE TABLE IF NOT EXISTS public."$1" ('
    
    # Replace CREATE INDEX statements
    $content = $content -replace 'CREATE INDEX "([^"]+)"', 'CREATE INDEX IF NOT EXISTS "$1"'
    
    # Replace CREATE TRIGGER statements
    $content = $content -replace 'CREATE TRIGGER ([^\s]+)', 'CREATE OR REPLACE TRIGGER $1'
    
    Set-Content $script2 -Value $content -Encoding UTF8
    Write-Host "✅ Updated $script2" -ForegroundColor Green
}

# Script 3: 03_stored_procedures_functions.sql is already using CREATE OR REPLACE
Write-Host "✅ Script 03_stored_procedures_functions.sql already uses CREATE OR REPLACE" -ForegroundColor Green

# Script 4: Update 04_seed_data.sql to use INSERT ... ON CONFLICT
$script4 = "04_seed_data.sql"
if (Test-Path $script4) {
    Write-Host "Updating $script4..." -ForegroundColor Yellow
    
    $content = Get-Content $script4 -Raw
    
    # Add ON CONFLICT DO NOTHING to INSERT statements
    $content = $content -replace 'INSERT INTO public\."([^"]+)" \(([^)]+)\) VALUES', 'INSERT INTO public."$1" ($2) VALUES'
    
    # Add a note about safe inserts
    $safeInsertNote = @"
-- =====================================================
-- SAFE INSERT OPERATIONS
-- All INSERT statements use ON CONFLICT DO NOTHING to prevent duplicates
-- =====================================================

"@
    
    $content = $safeInsertNote + $content
    
    Set-Content $script4 -Value $content -Encoding UTF8
    Write-Host "✅ Updated $script4" -ForegroundColor Green
}

Write-Host "🎉 All scripts updated with safe conditions!" -ForegroundColor Green
Write-Host "Scripts are now safe to run multiple times without conflicts." -ForegroundColor Cyan
