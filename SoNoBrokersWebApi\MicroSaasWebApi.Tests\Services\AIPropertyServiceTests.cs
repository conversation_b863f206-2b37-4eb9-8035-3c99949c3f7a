using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers;
using MicroSaasWebApi.Tests.Common;
using Moq;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Services
{
    public class AIPropertyServiceTests : TestBase
    {
        private readonly Mock<ILogger<AIPropertyService>> _mockLogger;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly AIPropertyService _aiPropertyService;

        public AIPropertyServiceTests(ITestOutputHelper output) : base(output)
        {
            _mockLogger = new Mock<ILogger<AIPropertyService>>();
            _mockConfiguration = new Mock<IConfiguration>();
            _aiPropertyService = new AIPropertyService(_mockLogger.Object, _mockConfiguration.Object);
        }

        [Fact]
        public async Task ImportPropertyDataAsync_WithValidAddress_ReturnsSuccessResponse()
        {
            // Arrange
            var request = TestDataBuilders.AI.PropertyImportRequest.Generate();

            // Act
            var result = await _aiPropertyService.ImportPropertyDataAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Status.Should().Be("success");
            result.Message.Should().Be("Property data imported successfully");
            result.PropertyDetails.Should().NotBeNull();
            result.PropertyDetails.Address.Should().Be(request.Address);
            result.PropertyDetails.PropertyType.Should().NotBeNullOrEmpty();
            result.PropertyDetails.Bedrooms.Should().BeGreaterThan(0);
            result.PropertyDetails.Bathrooms.Should().BeGreaterThan(0);
            result.PropertyDetails.SquareFeet.Should().BeGreaterThan(0);
        }

        [Fact]
        public async Task GetPropertyValuationAsync_WithValidRequest_ReturnsValuationResponse()
        {
            // Arrange
            var request = TestDataBuilders.AI.PropertyValuationRequest.Generate();

            // Act
            var result = await _aiPropertyService.GetPropertyValuationAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Status.Should().Be("success");
            result.Message.Should().Be("Property valuation completed successfully");
            
            // Verify valuation data
            result.Valuation.Should().NotBeNull();
            result.Valuation.EstimatedValue.Should().BeGreaterThan(0);
            result.Valuation.LowEstimate.Should().BeGreaterThan(0);
            result.Valuation.HighEstimate.Should().BeGreaterThan(0);
            result.Valuation.LowEstimate.Should().BeLessThan(result.Valuation.EstimatedValue);
            result.Valuation.HighEstimate.Should().BeGreaterThan(result.Valuation.EstimatedValue);
            result.Valuation.Currency.Should().NotBeNullOrEmpty();
            result.Valuation.PricePerSquareFoot.Should().BeGreaterThan(0);
            
            // Verify property info
            result.PropertyInfo.Should().NotBeNull();
            result.PropertyInfo.Address.Should().Be(request.Address);
            result.PropertyInfo.PropertyType.Should().NotBeNullOrEmpty();
            result.PropertyInfo.Bedrooms.Should().BeGreaterThan(0);
            result.PropertyInfo.Bathrooms.Should().BeGreaterThan(0);
            result.PropertyInfo.SquareFeet.Should().BeGreaterThan(0);
            
            // Verify market analysis
            result.MarketAnalysis.Should().NotBeNull();
            result.MarketAnalysis.MedianPrice.Should().BeGreaterThan(0);
            result.MarketAnalysis.AveragePrice.Should().BeGreaterThan(0);
            result.MarketAnalysis.DaysOnMarket.Should().BeGreaterThan(0);
            result.MarketAnalysis.MarketTrend.Should().NotBeNullOrEmpty();
            
            // Verify comparable properties
            result.ComparableProperties.Should().NotBeNull();
            result.ComparableProperties.Should().HaveCount(3);
            
            foreach (var comp in result.ComparableProperties)
            {
                comp.Address.Should().NotBeNullOrEmpty();
                comp.SoldPrice.Should().BeGreaterThan(0);
                comp.SoldDate.Should().NotBeNullOrEmpty();
                comp.Bedrooms.Should().BeGreaterThan(0);
                comp.Bathrooms.Should().BeGreaterThan(0);
                comp.SquareFeet.Should().BeGreaterThan(0);
                comp.Distance.Should().BeGreaterThan(0);
                comp.PricePerSquareFoot.Should().BeGreaterThan(0);
            }
        }

        [Theory]
        [InlineData("CA")]
        [InlineData("US")]
        [InlineData("UAE")]
        public async Task GetPropertyValuationAsync_WithDifferentCountries_ReturnsCorrectCurrency(string country)
        {
            // Arrange
            var request = new AIPropertyValuationRequest
            {
                Address = "123 Test Street",
                Country = country
            };

            // Act
            var result = await _aiPropertyService.GetPropertyValuationAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Status.Should().Be("success");
            
            var expectedCurrency = country switch
            {
                "CA" => "CAD",
                "US" => "USD",
                "UAE" => "AED",
                _ => "CAD"
            };
            
            result.Valuation.Currency.Should().Be(expectedCurrency);
        }

        [Fact]
        public async Task GetPropertyValuationAsync_WithUrbanAddress_ReturnsHigherValues()
        {
            // Arrange
            var urbanRequest = new AIPropertyValuationRequest
            {
                Address = "123 Main Street, Toronto, ON",
                Country = "CA"
            };

            var suburbanRequest = new AIPropertyValuationRequest
            {
                Address = "123 Rural Road, Small Town, ON",
                Country = "CA"
            };

            // Act
            var urbanResult = await _aiPropertyService.GetPropertyValuationAsync(urbanRequest);
            var suburbanResult = await _aiPropertyService.GetPropertyValuationAsync(suburbanRequest);

            // Assert
            urbanResult.Should().NotBeNull();
            suburbanResult.Should().NotBeNull();
            
            // Urban properties should generally have higher values (this is based on the mock implementation)
            // The actual comparison depends on the mock logic, but we can verify the structure
            urbanResult.Valuation.EstimatedValue.Should().BeGreaterThan(0);
            suburbanResult.Valuation.EstimatedValue.Should().BeGreaterThan(0);
        }

        [Fact]
        public async Task GeneratePropertyDescriptionAsync_WithValidRequest_ReturnsDescription()
        {
            // Arrange
            var request = TestDataBuilders.AI.GenerateDescriptionRequest.Generate();

            // Act
            var result = await _aiPropertyService.GeneratePropertyDescriptionAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Status.Should().Be("success");
            result.Message.Should().Be("Description generated successfully");
            result.Description.Should().NotBeNullOrEmpty();
            result.Description.Length.Should().BeGreaterThan(10);
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        public async Task ImportPropertyDataAsync_WithInvalidAddress_HandlesGracefully(string? address)
        {
            // Arrange
            var request = new AIPropertyImportRequest
            {
                Address = address!
            };

            // Act & Assert
            // The service should handle invalid input gracefully
            // In a real implementation, this might return an error status
            var result = await _aiPropertyService.ImportPropertyDataAsync(request);
            
            // For the mock implementation, it will still return success
            // In a real implementation, you would test for proper error handling
            result.Should().NotBeNull();
        }

        [Fact]
        public async Task GeneratePropertyDescriptionAsync_WithEmptyPrompt_HandlesGracefully()
        {
            // Arrange
            var request = new GenerateDescriptionRequest
            {
                Prompt = "",
                UserId = TestHelpers.GenerateTestId("user")
            };

            // Act
            var result = await _aiPropertyService.GeneratePropertyDescriptionAsync(request);

            // Assert
            result.Should().NotBeNull();
            // The mock implementation returns success regardless
            // In a real implementation, you would test for proper validation
        }

        [Fact]
        public async Task ImportPropertyDataAsync_ProcessingDelay_CompletesWithinReasonableTime()
        {
            // Arrange
            var request = TestDataBuilders.AI.PropertyImportRequest.Generate();
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act
            var result = await _aiPropertyService.ImportPropertyDataAsync(request);

            // Assert
            stopwatch.Stop();
            result.Should().NotBeNull();
            result.Status.Should().Be("success");
            
            // The mock has a 2-second delay, so it should complete within 3 seconds
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(3000);
            stopwatch.ElapsedMilliseconds.Should().BeGreaterThan(1500); // Should take at least the delay time
        }

        [Fact]
        public async Task GetPropertyValuationAsync_ProcessingDelay_CompletesWithinReasonableTime()
        {
            // Arrange
            var request = TestDataBuilders.AI.PropertyValuationRequest.Generate();
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act
            var result = await _aiPropertyService.GetPropertyValuationAsync(request);

            // Assert
            stopwatch.Stop();
            result.Should().NotBeNull();
            result.Status.Should().Be("success");
            
            // The mock has a 2-second delay, so it should complete within 3 seconds
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(3000);
            stopwatch.ElapsedMilliseconds.Should().BeGreaterThan(1500);
        }

        [Fact]
        public async Task GeneratePropertyDescriptionAsync_ProcessingDelay_CompletesWithinReasonableTime()
        {
            // Arrange
            var request = TestDataBuilders.AI.GenerateDescriptionRequest.Generate();
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act
            var result = await _aiPropertyService.GeneratePropertyDescriptionAsync(request);

            // Assert
            stopwatch.Stop();
            result.Should().NotBeNull();
            result.Status.Should().Be("success");
            
            // The mock has a 1-second delay, so it should complete within 2 seconds
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(2000);
            stopwatch.ElapsedMilliseconds.Should().BeGreaterThan(500);
        }
    }
}
