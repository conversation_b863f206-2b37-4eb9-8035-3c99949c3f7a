using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MicroSaasWebApi.Data;
using MicroSaasWebApi.Services.Auth.Interfaces;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using Moq;
using System.Data;
using Npgsql;
using Dapper;

namespace MicroSaasWebApi.Tests.Common
{
    public class TestWebApplicationFactory<TStartup> : WebApplicationFactory<TStartup> where TStartup : class
    {
        private readonly string _connectionString;

        public TestWebApplicationFactory()
        {
            _connectionString = "Host=localhost;Port=5432;Database=sonobrokers_test;Username=********;Password=********;";
        }

        protected override void ConfigureWebHost(IWebHostBuilder builder)
        {
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.Test.json", optional: false);
                config.AddInMemoryCollection(new[]
                {
                    new KeyValuePair<string, string?>("ConnectionStrings:DefaultConnection", _connectionString),
                    new KeyValuePair<string, string?>("ConnectionStrings:SupabaseConnection", _connectionString),
                    new KeyValuePair<string, string?>("TestSettings:UseInMemoryDatabase", "true")
                });
            });

            builder.ConfigureServices(services =>
            {
                // Remove existing database context
                var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(DapperDbContext));
                if (descriptor != null)
                {
                    services.Remove(descriptor);
                }

                // Add test database context
                services.AddScoped<DapperDbContext>(provider =>
                {
                    var configuration = provider.GetRequiredService<IConfiguration>();
                    var logger = provider.GetRequiredService<ILogger<DapperDbContext>>();
                    return new DapperDbContext(configuration, logger);
                });

                // Mock external services for testing
                MockExternalServices(services);

                // Configure test logging
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.AddDebug();
                    builder.SetMinimumLevel(LogLevel.Information);
                });
            });

            builder.UseEnvironment("Test");
        }

        private static void MockExternalServices(IServiceCollection services)
        {
            // Mock Clerk Auth Service for testing
            var mockClerkAuth = new Mock<IClerkAuthService>();
            mockClerkAuth.Setup(x => x.GetUserIdAsync(It.IsAny<HttpContext>()))
                .ReturnsAsync("test-user-id");

            services.AddSingleton(mockClerkAuth.Object);

            // Mock external HTTP calls
            services.AddHttpClient("test", client =>
            {
                client.BaseAddress = new Uri("https://test.example.com/");
            });
        }

        public async Task<IDbConnection> GetTestDatabaseConnectionAsync()
        {
            var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();
            return connection;
        }

        public async Task SeedTestDataAsync()
        {
            using var connection = await GetTestDatabaseConnectionAsync();

            // Create test users
            await connection.ExecuteAsync(@"
                INSERT INTO public.""User"" (id, email, ""fullName"", ""firstName"", ""lastName"", role, ""userType"", ""isActive"", ""createdAt"", ""updatedAt"")
                VALUES 
                ('test-user-1', '<EMAIL>', 'Test User 1', 'Test', 'User1', 'USER', 'Buyer', true, NOW(), NOW()),
                ('test-user-2', '<EMAIL>', 'Test User 2', 'Test', 'User2', 'USER', 'Seller', true, NOW(), NOW()),
                ('test-admin-1', '<EMAIL>', 'Test Admin', 'Test', 'Admin', 'ADMIN', 'Seller', true, NOW(), NOW())
                ON CONFLICT (id) DO NOTHING;
            ");

            // Create test properties
            await connection.ExecuteAsync(@"
                INSERT INTO public.""Property"" (id, title, description, price, ""propertyType"", bedrooms, bathrooms, ""sellerId"", status, ""createdAt"", ""updatedAt"")
                VALUES 
                ('test-prop-1', 'Test Property 1', 'A beautiful test property', 500000, 'Detached House', 3, 2, 'test-user-2', 'active', NOW(), NOW()),
                ('test-prop-2', 'Test Property 2', 'Another test property', 750000, 'Townhouse', 4, 3, 'test-user-2', 'active', NOW(), NOW())
                ON CONFLICT (id) DO NOTHING;
            ");

            // Create test advertisers
            await connection.ExecuteAsync(@"
                INSERT INTO public.""Advertiser"" (id, ""userId"", ""businessName"", email, ""serviceType"", plan, status, ""createdAt"", ""updatedAt"")
                VALUES 
                ('test-adv-1', 'test-user-1', 'Test Photography', '<EMAIL>', 'photographer', 'basic', 'active', NOW(), NOW()),
                ('test-adv-2', 'test-user-2', 'Test Legal Services', '<EMAIL>', 'lawyer', 'premium', 'active', NOW(), NOW())
                ON CONFLICT (id) DO NOTHING;
            ");
        }

        public async Task CleanupTestDataAsync()
        {
            using var connection = await GetTestDatabaseConnectionAsync();

            // Clean up test data in reverse order of dependencies
            await connection.ExecuteAsync(@"
                DELETE FROM public.""Advertiser"" WHERE id LIKE 'test-%';
                DELETE FROM public.""Property"" WHERE id LIKE 'test-%';
                DELETE FROM public.""User"" WHERE id LIKE 'test-%';
            ");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Cleanup test data when factory is disposed
                try
                {
                    CleanupTestDataAsync().GetAwaiter().GetResult();
                }
                catch
                {
                    // Ignore cleanup errors during disposal
                }
            }

            base.Dispose(disposing);
        }
    }
}
