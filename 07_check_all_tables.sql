-- =====================================================
-- SoNoBrokers Complete Table Structure Checker
-- This script shows ALL tables and their complete structure
-- Run this to see exactly what's in your database
-- =====================================================

-- =====================================================
-- COMPLETE DATABASE OVERVIEW
-- =====================================================

DO $$
DECLARE
  table_count INTEGER;
  enum_count INTEGER;
  function_count INTEGER;
  index_count INTEGER;
BEGIN
  RAISE NOTICE '🎯 SONOBROKERS DATABASE COMPLETE OVERVIEW';
  RAISE NOTICE '==========================================';
  RAISE NOTICE '';
  
  -- Count all objects
  SELECT COUNT(*) INTO table_count FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
  SELECT COUNT(DISTINCT typname) INTO enum_count FROM pg_type WHERE typtype = 'e';
  SELECT COUNT(*) INTO function_count FROM pg_proc p JOIN pg_namespace n ON p.pronamespace = n.oid WHERE n.nspname = 'public' AND p.proname NOT LIKE 'pg_%';
  SELECT COUNT(*) INTO index_count FROM pg_indexes WHERE schemaname = 'public';
  
  RAISE NOTICE '📊 DATABASE STATISTICS:';
  RAISE NOTICE '  Tables: %', table_count;
  RAISE NOTICE '  Enums: %', enum_count;
  RAISE NOTICE '  Functions/Procedures: %', function_count;
  RAISE NOTICE '  Indexes: %', index_count;
  RAISE NOTICE '';
END $$;

-- =====================================================
-- ALL TABLES WITH COMPLETE COLUMN DETAILS
-- =====================================================

SELECT 
  '=== TABLE: ' || table_name || ' ===' as info
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- Show detailed table structure
SELECT 
  t.table_name,
  c.column_name,
  c.ordinal_position as pos,
  CASE 
    WHEN c.data_type = 'USER-DEFINED' THEN c.udt_name
    WHEN c.data_type = 'character varying' THEN 'varchar(' || COALESCE(c.character_maximum_length::text, '∞') || ')'
    WHEN c.data_type = 'character' THEN 'char(' || c.character_maximum_length || ')'
    WHEN c.data_type = 'numeric' THEN 'numeric(' || COALESCE(c.numeric_precision::text, '') || CASE WHEN c.numeric_scale > 0 THEN ',' || c.numeric_scale::text ELSE '' END || ')'
    ELSE c.data_type
  END as data_type,
  CASE WHEN c.is_nullable = 'YES' THEN 'NULL' ELSE 'NOT NULL' END as nullable,
  CASE 
    WHEN c.column_default IS NOT NULL THEN 
      CASE 
        WHEN length(c.column_default) > 50 THEN substring(c.column_default, 1, 47) || '...'
        ELSE c.column_default
      END
    ELSE ''
  END as default_value,
  -- Check if it's a foreign key
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY' 
      AND tc.table_schema = 'public'
      AND tc.table_name = t.table_name
      AND kcu.column_name = c.column_name
    ) THEN '🔗 FK'
    WHEN EXISTS (
      SELECT 1 FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
      WHERE tc.constraint_type = 'PRIMARY KEY' 
      AND tc.table_schema = 'public'
      AND tc.table_name = t.table_name
      AND kcu.column_name = c.column_name
    ) THEN '🔑 PK'
    ELSE ''
  END as key_type
FROM information_schema.tables t
JOIN information_schema.columns c ON t.table_name = c.table_name AND t.table_schema = c.table_schema
WHERE t.table_schema = 'public' 
AND t.table_type = 'BASE TABLE'
ORDER BY t.table_name, c.ordinal_position;

-- =====================================================
-- ALL ENUMS WITH VALUES
-- =====================================================

SELECT 
  '=== ENUM: ' || t.typname || ' ===' as info,
  string_agg(e.enumlabel, ', ' ORDER BY e.enumsortorder) as enum_values
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid  
WHERE t.typtype = 'e'
GROUP BY t.typname
ORDER BY t.typname;

-- =====================================================
-- ALL FOREIGN KEY RELATIONSHIPS
-- =====================================================

SELECT 
  '🔗 ' || tc.table_name || '.' || kcu.column_name || ' → ' || ccu.table_name || '.' || ccu.column_name as foreign_key_relationship,
  tc.constraint_name
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
  ON tc.constraint_name = kcu.constraint_name
  AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
  ON ccu.constraint_name = tc.constraint_name
  AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_schema = 'public'
ORDER BY tc.table_name, kcu.column_name;

-- =====================================================
-- ALL INDEXES BY TABLE
-- =====================================================

SELECT 
  tablename,
  COUNT(*) as index_count,
  string_agg(indexname, ', ' ORDER BY indexname) as indexes
FROM pg_indexes 
WHERE schemaname = 'public'
GROUP BY tablename
ORDER BY tablename;

-- =====================================================
-- ALL FUNCTIONS AND PROCEDURES
-- =====================================================

SELECT 
  p.proname as function_name,
  CASE 
    WHEN p.prorettype = 'void'::regtype THEN 'PROCEDURE'
    ELSE 'FUNCTION'
  END as type,
  pg_get_function_arguments(p.oid) as arguments
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
AND p.proname NOT LIKE 'pg_%'
AND p.proname NOT LIKE 'information_schema_%'
ORDER BY p.proname;

-- =====================================================
-- RECORD COUNT FOR ALL TABLES
-- =====================================================

DO $$
DECLARE
  table_record RECORD;
  table_count BIGINT;
  total_records BIGINT := 0;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== RECORD COUNTS FOR ALL TABLES ===';
  RAISE NOTICE '';
  
  FOR table_record IN 
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_type = 'BASE TABLE'
    ORDER BY table_name
  LOOP
    EXECUTE format('SELECT COUNT(*) FROM public."%I"', table_record.table_name) INTO table_count;
    total_records := total_records + table_count;
    
    RAISE NOTICE '📊 % : % records', table_record.table_name, table_count;
  END LOOP;
  
  RAISE NOTICE '';
  RAISE NOTICE '📈 TOTAL RECORDS ACROSS ALL TABLES: %', total_records;
END $$;

-- =====================================================
-- COMPLETION SUMMARY
-- =====================================================

DO $$
DECLARE
  table_count INTEGER;
  required_tables INTEGER := 23; -- Expected number of tables for complete SoNoBrokers
  completion_percentage DECIMAL;
BEGIN
  SELECT COUNT(*) INTO table_count 
  FROM information_schema.tables 
  WHERE table_schema = 'public' 
  AND table_type = 'BASE TABLE';
  
  completion_percentage := (table_count::DECIMAL / required_tables * 100);
  
  RAISE NOTICE '';
  RAISE NOTICE '🎯 DATABASE COMPLETION SUMMARY';
  RAISE NOTICE '==============================';
  RAISE NOTICE 'Tables found: % / % (%.1f%%)', table_count, required_tables, completion_percentage;
  
  IF completion_percentage >= 95 THEN
    RAISE NOTICE '🎉 DATABASE IS COMPLETE!';
    RAISE NOTICE 'Your SoNoBrokers database has all required tables.';
  ELSIF completion_percentage >= 80 THEN
    RAISE NOTICE '⚠️  DATABASE IS MOSTLY COMPLETE';
    RAISE NOTICE 'Some tables may be missing. Review the table list above.';
  ELSE
    RAISE NOTICE '🚨 DATABASE IS INCOMPLETE';
    RAISE NOTICE 'Many tables are missing. Run the complete setup scripts.';
  END IF;
  
  RAISE NOTICE '';
  RAISE NOTICE '✅ Complete table structure analysis finished!';
  RAISE NOTICE 'All tables, columns, relationships, and data counts shown above.';
END $$;
