using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using System.Security.Claims;
using MicroSaasWebApi.Controllers.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using MicroSaasWebApi.Models.SoNoBrokers.ContactSharing;

namespace MicroSaasWebApi.Tests.Authentication
{
    [TestFixture]
    public class AuthenticationTests
    {
        private Mock<IContactSharingService> _mockContactSharingService;
        private Mock<ILogger<ContactSharingController>> _mockLogger;
        private ContactSharingController _controller;

        [SetUp]
        public void Setup()
        {
            _mockContactSharingService = new Mock<IContactSharingService>();
            _mockLogger = new Mock<ILogger<ContactSharingController>>();
            _controller = new ContactSharingController(_mockContactSharingService.Object, _mockLogger.Object);
        }

        [Test]
        public void ClaimsExtensions_GetUserId_WithValidClaim_ReturnsUserId()
        {
            // Arrange
            var userId = "test-user-123";
            var claims = new[]
            {
                new Claim("sub", userId),
                new Claim("email", "<EMAIL>")
            };
            var identity = new ClaimsIdentity(claims, "test");
            var principal = new ClaimsPrincipal(identity);

            // Act
            var result = principal.GetUserId();

            // Assert
            Assert.That(result, Is.EqualTo(userId));
        }

        [Test]
        public void ClaimsExtensions_GetUserId_WithoutSubClaim_ReturnsNull()
        {
            // Arrange
            var claims = new[]
            {
                new Claim("email", "<EMAIL>"),
                new Claim("name", "Test User")
            };
            var identity = new ClaimsIdentity(claims, "test");
            var principal = new ClaimsPrincipal(identity);

            // Act
            var result = principal.GetUserId();

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public void ClaimsExtensions_GetUserEmail_WithValidClaim_ReturnsEmail()
        {
            // Arrange
            var email = "<EMAIL>";
            var claims = new[]
            {
                new Claim("sub", "test-user-123"),
                new Claim("email", email)
            };
            var identity = new ClaimsIdentity(claims, "test");
            var principal = new ClaimsPrincipal(identity);

            // Act
            var result = principal.GetUserEmail();

            // Assert
            Assert.That(result, Is.EqualTo(email));
        }

        [Test]
        public void ClaimsExtensions_GetUserName_WithValidClaim_ReturnsName()
        {
            // Arrange
            var name = "Test User";
            var claims = new[]
            {
                new Claim("sub", "test-user-123"),
                new Claim("name", name)
            };
            var identity = new ClaimsIdentity(claims, "test");
            var principal = new ClaimsPrincipal(identity);

            // Act
            var result = principal.GetUserName();

            // Assert
            Assert.That(result, Is.EqualTo(name));
        }

        [Test]
        public void ClaimsExtensions_IsAdmin_WithAdminRole_ReturnsTrue()
        {
            // Arrange
            var claims = new[]
            {
                new Claim("sub", "test-user-123"),
                new Claim(ClaimTypes.Role, "admin")
            };
            var identity = new ClaimsIdentity(claims, "test");
            var principal = new ClaimsPrincipal(identity);

            // Act
            var result = principal.IsAdmin();

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public void ClaimsExtensions_IsAdmin_WithoutAdminRole_ReturnsFalse()
        {
            // Arrange
            var claims = new[]
            {
                new Claim("sub", "test-user-123"),
                new Claim(ClaimTypes.Role, "user")
            };
            var identity = new ClaimsIdentity(claims, "test");
            var principal = new ClaimsPrincipal(identity);

            // Act
            var result = principal.IsAdmin();

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public async Task ContactSharingController_WithoutAuthentication_ReturnsUnauthorized()
        {
            // Arrange
            var request = new CreateContactShareRequest
            {
                PropertyId = "property-123",
                SellerId = "seller-123",
                BuyerName = "Test Buyer",
                BuyerEmail = "<EMAIL>",
                ShareType = ContactShareType.ContactRequest
            };

            // Setup controller without authentication
            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            };

            // Act
            var result = await _controller.CreateContactShare(request);

            // Assert
            Assert.That(result, Is.InstanceOf<UnauthorizedObjectResult>());
        }

        [Test]
        public async Task ContactSharingController_WithValidAuthentication_CallsService()
        {
            // Arrange
            var userId = "test-user-123";
            var request = new CreateContactShareRequest
            {
                PropertyId = "property-123",
                SellerId = "seller-123",
                BuyerName = "Test Buyer",
                BuyerEmail = "<EMAIL>",
                ShareType = ContactShareType.ContactRequest
            };

            var expectedResponse = new ContactShareResponse
            {
                Id = "contact-share-123",
                PropertyId = request.PropertyId,
                BuyerId = userId,
                SellerId = request.SellerId,
                BuyerName = request.BuyerName,
                BuyerEmail = request.BuyerEmail,
                ShareType = request.ShareType,
                Status = ContactShareStatus.Sent
            };

            // Setup authenticated user
            var claims = new[]
            {
                new Claim("sub", userId),
                new Claim("email", "<EMAIL>")
            };
            var identity = new ClaimsIdentity(claims, "test");
            var principal = new ClaimsPrincipal(identity);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext
                {
                    User = principal
                }
            };

            // Setup service mock
            _mockContactSharingService
                .Setup(x => x.CreateContactShareAsync(userId, request))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _controller.CreateContactShare(request);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.Value, Is.EqualTo(expectedResponse));

            // Verify service was called
            _mockContactSharingService.Verify(x => x.CreateContactShareAsync(userId, request), Times.Once);
        }

        [Test]
        public async Task ContactSharingController_GetContactShare_WithoutAccess_ReturnsForbidden()
        {
            // Arrange
            var userId = "test-user-123";
            var contactShareId = "contact-share-123";

            // Setup authenticated user
            var claims = new[]
            {
                new Claim("sub", userId),
                new Claim("email", "<EMAIL>")
            };
            var identity = new ClaimsIdentity(claims, "test");
            var principal = new ClaimsPrincipal(identity);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext
                {
                    User = principal
                }
            };

            // Setup service mock to deny access
            _mockContactSharingService
                .Setup(x => x.CanUserAccessContactShareAsync(userId, contactShareId))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.GetContactShare(contactShareId);

            // Assert
            Assert.That(result, Is.InstanceOf<ForbidResult>());
        }

        [Test]
        public async Task ContactSharingController_GetContactShare_WithAccess_ReturnsContactShare()
        {
            // Arrange
            var userId = "test-user-123";
            var contactShareId = "contact-share-123";

            var expectedResponse = new ContactShareResponse
            {
                Id = contactShareId,
                PropertyId = "property-123",
                BuyerId = userId,
                SellerId = "seller-123",
                BuyerName = "Test Buyer",
                BuyerEmail = "<EMAIL>",
                ShareType = ContactShareType.ContactRequest,
                Status = ContactShareStatus.Sent
            };

            // Setup authenticated user
            var claims = new[]
            {
                new Claim("sub", userId),
                new Claim("email", "<EMAIL>")
            };
            var identity = new ClaimsIdentity(claims, "test");
            var principal = new ClaimsPrincipal(identity);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext
                {
                    User = principal
                }
            };

            // Setup service mocks
            _mockContactSharingService
                .Setup(x => x.CanUserAccessContactShareAsync(userId, contactShareId))
                .ReturnsAsync(true);

            _mockContactSharingService
                .Setup(x => x.GetContactShareAsync(contactShareId))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _controller.GetContactShare(contactShareId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.Value, Is.EqualTo(expectedResponse));
        }

        [Test]
        public void AuthenticationMiddleware_ValidatesJwtToken()
        {
            // This would be an integration test that validates JWT token processing
            // For unit testing, we focus on the claims extraction logic

            // Arrange
            var validJwtClaims = new[]
            {
                new Claim("sub", "user-123"),
                new Claim("email", "<EMAIL>"),
                new Claim("iat", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString()),
                new Claim("exp", DateTimeOffset.UtcNow.AddHours(1).ToUnixTimeSeconds().ToString())
            };

            var identity = new ClaimsIdentity(validJwtClaims, "Bearer");
            var principal = new ClaimsPrincipal(identity);

            // Act & Assert
            Assert.That(principal.Identity?.IsAuthenticated, Is.True);
            Assert.That(principal.GetUserId(), Is.EqualTo("user-123"));
            Assert.That(principal.GetUserEmail(), Is.EqualTo("<EMAIL>"));
        }

        [Test]
        public void AuthenticationMiddleware_RejectsExpiredToken()
        {
            // Arrange
            var expiredJwtClaims = new[]
            {
                new Claim("sub", "user-123"),
                new Claim("email", "<EMAIL>"),
                new Claim("iat", DateTimeOffset.UtcNow.AddHours(-2).ToUnixTimeSeconds().ToString()),
                new Claim("exp", DateTimeOffset.UtcNow.AddHours(-1).ToUnixTimeSeconds().ToString()) // Expired
            };

            var identity = new ClaimsIdentity(expiredJwtClaims, "Bearer");
            var principal = new ClaimsPrincipal(identity);

            // In a real scenario, the middleware would reject this before it reaches the controller
            // This test validates the claims structure for expired tokens

            // Act & Assert
            var expClaim = principal.FindFirst("exp");
            Assert.That(expClaim, Is.Not.Null);

            var expTime = DateTimeOffset.FromUnixTimeSeconds(long.Parse(expClaim.Value));
            Assert.That(expTime, Is.LessThan(DateTimeOffset.UtcNow));
        }

        [TearDown]
        public void TearDown()
        {
            _mockContactSharingService?.Reset();
            _mockLogger?.Reset();
        }
    }
}
