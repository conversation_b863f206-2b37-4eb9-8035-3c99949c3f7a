'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Calculator, DollarSign, Percent, Calendar, TrendingUp } from 'lucide-react'

interface CAMortgageCalculatorClientProps {
  userType: 'buyer' | 'seller'
}

interface MortgageCalculation {
  monthlyPayment: number
  totalInterest: number
  totalPayment: number
  principalAndInterest: number
  cmhcInsurance: number
  monthlyInsurance: number
}

export function CAMortgageCalculatorClient({ userType }: CAMortgageCalculatorClientProps) {
  const [homePrice, setHomePrice] = useState<number>(500000)
  const [downPayment, setDownPayment] = useState<number>(100000)
  const [interestRate, setInterestRate] = useState<number>(5.5)
  const [amortization, setAmortization] = useState<number>(25)
  const [calculation, setCalculation] = useState<MortgageCalculation | null>(null)

  // Calculate CMHC insurance (required if down payment < 20%)
  const calculateCMHCInsurance = (price: number, down: number): number => {
    const downPaymentPercent = (down / price) * 100
    
    if (downPaymentPercent >= 20) return 0
    
    const loanAmount = price - down
    let insuranceRate = 0
    
    if (downPaymentPercent >= 15) insuranceRate = 0.028
    else if (downPaymentPercent >= 10) insuranceRate = 0.031
    else insuranceRate = 0.04
    
    return loanAmount * insuranceRate
  }

  // Calculate mortgage payment
  const calculateMortgage = (): MortgageCalculation => {
    const principal = homePrice - downPayment
    const cmhcInsurance = calculateCMHCInsurance(homePrice, downPayment)
    const totalLoan = principal + cmhcInsurance
    
    const monthlyRate = interestRate / 100 / 12
    const numberOfPayments = amortization * 12
    
    const monthlyPayment = totalLoan * 
      (monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments)) / 
      (Math.pow(1 + monthlyRate, numberOfPayments) - 1)
    
    const totalPayment = monthlyPayment * numberOfPayments
    const totalInterest = totalPayment - totalLoan
    
    return {
      monthlyPayment,
      totalInterest,
      totalPayment,
      principalAndInterest: monthlyPayment,
      cmhcInsurance,
      monthlyInsurance: cmhcInsurance > 0 ? cmhcInsurance / numberOfPayments : 0
    }
  }

  useEffect(() => {
    setCalculation(calculateMortgage())
  }, [homePrice, downPayment, interestRate, amortization])

  const downPaymentPercent = (downPayment / homePrice) * 100
  const maxDownPayment = homePrice * 0.95 // Maximum 95% financing

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4 flex items-center gap-2">
          <Calculator className="w-8 h-8" />
          Canadian Mortgage Calculator
        </h1>
        <p className="text-muted-foreground text-lg">
          Calculate your mortgage payments with Canadian rates and CMHC insurance requirements.
        </p>
      </div>

      <div className="grid gap-8 lg:grid-cols-2">
        {/* Input Section */}
        <Card>
          <CardHeader>
            <CardTitle>Mortgage Details</CardTitle>
            <CardDescription>
              Enter your mortgage information to calculate payments
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Home Price */}
            <div className="space-y-2">
              <Label htmlFor="homePrice">Home Price</Label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="homePrice"
                  type="number"
                  value={homePrice}
                  onChange={(e) => setHomePrice(Number(e.target.value))}
                  className="pl-10"
                  placeholder="500,000"
                />
              </div>
            </div>

            {/* Down Payment */}
            <div className="space-y-4">
              <Label>Down Payment: ${downPayment.toLocaleString()} ({downPaymentPercent.toFixed(1)}%)</Label>
              <Slider
                value={[downPayment]}
                onValueChange={(value) => setDownPayment(value[0])}
                max={maxDownPayment}
                min={homePrice * 0.05} // Minimum 5% down payment
                step={5000}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>5% (${(homePrice * 0.05).toLocaleString()})</span>
                <span>95% (${maxDownPayment.toLocaleString()})</span>
              </div>
            </div>

            {/* Interest Rate */}
            <div className="space-y-4">
              <Label>Interest Rate: {interestRate}%</Label>
              <Slider
                value={[interestRate]}
                onValueChange={(value) => setInterestRate(value[0])}
                max={10}
                min={1}
                step={0.1}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>1%</span>
                <span>10%</span>
              </div>
            </div>

            {/* Amortization */}
            <div className="space-y-4">
              <Label>Amortization: {amortization} years</Label>
              <Slider
                value={[amortization]}
                onValueChange={(value) => setAmortization(value[0])}
                max={30}
                min={5}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>5 years</span>
                <span>30 years</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Results Section */}
        <div className="space-y-6">
          {calculation && (
            <>
              {/* Monthly Payment */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="w-5 h-5" />
                    Monthly Payment
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-primary">
                    ${calculation.monthlyPayment.toLocaleString('en-CA', { 
                      minimumFractionDigits: 2, 
                      maximumFractionDigits: 2 
                    })}
                  </div>
                  <p className="text-muted-foreground">Principal & Interest</p>
                </CardContent>
              </Card>

              {/* CMHC Insurance */}
              {calculation.cmhcInsurance > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="w-5 h-5" />
                      CMHC Insurance
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Total CMHC Premium:</span>
                        <span className="font-semibold">
                          ${calculation.cmhcInsurance.toLocaleString('en-CA', { 
                            minimumFractionDigits: 2, 
                            maximumFractionDigits: 2 
                          })}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Required for down payments less than 20%
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    Loan Summary
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Loan Amount</p>
                      <p className="font-semibold">
                        ${(homePrice - downPayment).toLocaleString('en-CA')}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Total Interest</p>
                      <p className="font-semibold">
                        ${calculation.totalInterest.toLocaleString('en-CA', { 
                          minimumFractionDigits: 2, 
                          maximumFractionDigits: 2 
                        })}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Total Payments</p>
                      <p className="font-semibold">
                        ${calculation.totalPayment.toLocaleString('en-CA', { 
                          minimumFractionDigits: 2, 
                          maximumFractionDigits: 2 
                        })}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Down Payment</p>
                      <p className="font-semibold">
                        ${downPayment.toLocaleString('en-CA')} ({downPaymentPercent.toFixed(1)}%)
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Canadian Mortgage Info */}
              <Card>
                <CardHeader>
                  <CardTitle>Canadian Mortgage Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2 text-sm">
                  <p>• Minimum down payment: 5% for homes under $500,000</p>
                  <p>• CMHC insurance required for down payments under 20%</p>
                  <p>• Maximum amortization: 25 years for insured mortgages</p>
                  <p>• Stress test rate applies for qualification</p>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
