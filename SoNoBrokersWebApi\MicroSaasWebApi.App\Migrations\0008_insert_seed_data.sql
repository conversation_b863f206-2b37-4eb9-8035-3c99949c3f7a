-- Migration: 0008_insert_seed_data.sql
-- Description: Insert sample data for development and testing
-- Date: 2024-12-25
-- Author: Migration System
-- Dependencies: 0007_create_functions.sql

-- =====================================================
-- SAMPLE USERS
-- =====================================================

-- Insert sample users (buyers and sellers)
INSERT INTO public."User" (
  id, "clerkId", email, "fullName", "firstName", "lastName", 
  "phoneNumber", role, status, "emailVerified", "createdAt", "updatedAt"
) VALUES 
-- Sellers
('11111111-1111-1111-1111-111111111111', 'clerk_seller_1', '<EMAIL>', '<PERSON>', 'John', 'Seller', '******-555-0001', 'USER', 'ACTIVE', TRUE, NOW() - INTERVAL '30 days', NOW() - INTERVAL '30 days'),
('22222222-2222-2222-2222-222222222222', 'clerk_seller_2', '<EMAIL>', 'Jane Smith', 'Jane', 'Smith', '******-555-0002', 'USER', 'ACTIVE', TRUE, NOW() - INTERVAL '25 days', NOW() - INTERVAL '25 days'),
('33333333-3333-3333-3333-333333333333', 'clerk_seller_3', '<EMAIL>', 'Mike Johnson', 'Mike', 'Johnson', '******-555-0003', 'USER', 'ACTIVE', TRUE, NOW() - INTERVAL '20 days', NOW() - INTERVAL '20 days'),

-- Buyers
('44444444-4444-4444-4444-444444444444', 'clerk_buyer_1', '<EMAIL>', 'Sarah Buyer', 'Sarah', 'Buyer', '******-555-0101', 'USER', 'ACTIVE', TRUE, NOW() - INTERVAL '15 days', NOW() - INTERVAL '15 days'),
('55555555-5555-5555-5555-555555555555', 'clerk_buyer_2', '<EMAIL>', 'David Wilson', 'David', 'Wilson', '******-555-0102', 'USER', 'ACTIVE', TRUE, NOW() - INTERVAL '10 days', NOW() - INTERVAL '10 days'),
('66666666-6666-6666-6666-666666666666', 'clerk_buyer_3', '<EMAIL>', 'Emily Rodriguez', 'Emily', 'Rodriguez', '******-555-0103', 'USER', 'ACTIVE', TRUE, NOW() - INTERVAL '5 days', NOW() - INTERVAL '5 days'),

-- Admin
('77777777-7777-7777-7777-777777777777', 'clerk_admin_1', '<EMAIL>', 'Admin User', 'Admin', 'User', '******-555-0999', 'ADMIN', 'ACTIVE', TRUE, NOW() - INTERVAL '60 days', NOW() - INTERVAL '60 days')

ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- SAMPLE PROPERTIES
-- =====================================================

-- Insert sample properties
INSERT INTO public."Property" (
  id, "sellerId", title, description, price, "propertyType", "listingType", 
  status, bedrooms, bathrooms, "squareFootage", "lotSize", "yearBuilt",
  address, "imageUrls", features, amenities, "parkingSpaces", "garageSpaces",
  "propertyTaxes", "listingDate", "viewCount", "favoriteCount", 
  "isActive", "isFeatured", "createdAt", "updatedAt"
) VALUES 
-- Property 1 - Downtown Condo
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 
 '11111111-1111-1111-1111-111111111111',
 'Luxury Downtown Condo with City Views',
 'Beautiful 2-bedroom, 2-bathroom condo in the heart of downtown Toronto. Floor-to-ceiling windows with stunning city views. Modern kitchen with stainless steel appliances.',
 650000.00, 'Condominium', 'FOR_SALE', 'ACTIVE',
 2, 2.0, 1200, NULL, 2018,
 '{"street": "123 King Street West", "unit": "2505", "city": "Toronto", "province": "ON", "postalCode": "M5H 1J8", "country": "Canada"}',
 ARRAY['https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800', 'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800'],
 ARRAY['Floor-to-ceiling windows', 'Modern kitchen', 'In-suite laundry', 'Balcony'],
 ARRAY['Gym', 'Pool', '24/7 Concierge', 'Rooftop Terrace'],
 1, 1, 4500.00, NOW() - INTERVAL '25 days', 45, 8, TRUE, TRUE, NOW() - INTERVAL '25 days', NOW() - INTERVAL '25 days'),

-- Property 2 - Suburban House
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
 '22222222-2222-2222-2222-222222222222',
 'Spacious Family Home in Mississauga',
 'Perfect family home with 4 bedrooms and 3 bathrooms. Large backyard, updated kitchen, and finished basement. Close to schools and parks.',
 850000.00, 'Detached House', 'FOR_SALE', 'ACTIVE',
 4, 3.0, 2400, 0.25, 1995,
 '{"street": "456 Maple Avenue", "city": "Mississauga", "province": "ON", "postalCode": "L5B 2K3", "country": "Canada"}',
 ARRAY['https://images.unsplash.com/photo-1568605114967-8130f3a36994?w=800', 'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800'],
 ARRAY['Updated kitchen', 'Finished basement', 'Large backyard', 'Hardwood floors'],
 ARRAY['Close to schools', 'Near parks', 'Public transit'],
 2, 2, 6200.00, NOW() - INTERVAL '20 days', 32, 5, TRUE, FALSE, NOW() - INTERVAL '20 days', NOW() - INTERVAL '20 days'),

-- Property 3 - Townhouse
('cccccccc-cccc-cccc-cccc-cccccccccccc',
 '33333333-3333-3333-3333-333333333333',
 'Modern Townhouse in Vaughan',
 'Brand new 3-bedroom, 2.5-bathroom townhouse. Open concept living, modern finishes, and private garage.',
 720000.00, 'Townhouse', 'FOR_SALE', 'ACTIVE',
 3, 2.5, 1800, NULL, 2023,
 '{"street": "789 Oak Street", "city": "Vaughan", "province": "ON", "postalCode": "L4H 3M7", "country": "Canada"}',
 ARRAY['https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800'],
 ARRAY['Open concept', 'Modern finishes', 'Private garage', 'Energy efficient'],
 ARRAY['New construction', 'Close to highway', 'Shopping nearby'],
 1, 1, 5800.00, NOW() - INTERVAL '15 days', 28, 3, TRUE, FALSE, NOW() - INTERVAL '15 days', NOW() - INTERVAL '15 days')

ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- SAMPLE PROPERTY IMAGES
-- =====================================================

INSERT INTO public."PropertyImage" (
  id, "propertyId", "imageUrl", "altText", "displayOrder", "isMain", "createdAt"
) VALUES 
-- Images for Property 1
('img1-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800', 'Living room with city view', 1, TRUE, NOW()),
('img2-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800', 'Modern kitchen', 2, FALSE, NOW()),

-- Images for Property 2
('img1-2222-2222-2222-222222222222', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'https://images.unsplash.com/photo-1568605114967-8130f3a36994?w=800', 'Front exterior view', 1, TRUE, NOW()),
('img2-2222-2222-2222-222222222222', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800', 'Backyard and patio', 2, FALSE, NOW()),

-- Images for Property 3
('img1-3333-3333-3333-333333333333', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800', 'Modern townhouse exterior', 1, TRUE, NOW())

ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- SAMPLE CONTACT SHARES
-- =====================================================

INSERT INTO public."ContactShare" (
  id, "propertyId", "buyerId", "sellerId", "buyerName", "buyerEmail", 
  "buyerPhone", message, "shareType", status, "createdAt"
) VALUES 
('cs1-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '44444444-4444-4444-4444-444444444444', '11111111-1111-1111-1111-111111111111', 'Sarah Buyer', '<EMAIL>', '******-555-0101', 'Very interested in this property. Would like to schedule a viewing.', 'ContactRequest', 'Pending', NOW() - INTERVAL '2 days'),
('cs2-2222-2222-2222-222222222222', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '55555555-5555-5555-5555-555555555555', '22222222-2222-2222-2222-222222222222', 'David Wilson', '<EMAIL>', '******-555-0102', 'Perfect for our family. Can we arrange a visit this weekend?', 'ScheduleVisit', 'Approved', NOW() - INTERVAL '1 day')

ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- SAMPLE SELLER AVAILABILITY
-- =====================================================

INSERT INTO public."SellerAvailability" (
  id, "propertyId", "sellerId", "dayOfWeek", "startTime", "endTime", 
  "isAvailable", notes, "createdAt", "updatedAt"
) VALUES 
-- Seller 1 availability (Monday to Friday, 9 AM to 6 PM)
('sa1-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 1, '09:00:00', '18:00:00', TRUE, 'Available weekdays', NOW(), NOW()),
('sa2-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 2, '09:00:00', '18:00:00', TRUE, 'Available weekdays', NOW(), NOW()),
('sa3-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 3, '09:00:00', '18:00:00', TRUE, 'Available weekdays', NOW(), NOW()),
('sa4-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 4, '09:00:00', '18:00:00', TRUE, 'Available weekdays', NOW(), NOW()),
('sa5-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 5, '09:00:00', '18:00:00', TRUE, 'Available weekdays', NOW(), NOW()),

-- Seller 2 availability (Weekends only)
('sa6-2222-2222-2222-222222222222', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '22222222-2222-2222-2222-222222222222', 0, '10:00:00', '16:00:00', TRUE, 'Weekend availability', NOW(), NOW()),
('sa7-2222-2222-2222-222222222222', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '22222222-2222-2222-2222-222222222222', 6, '10:00:00', '16:00:00', TRUE, 'Weekend availability', NOW(), NOW())

ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ Migration 0008_insert_seed_data.sql completed successfully';
    RAISE NOTICE 'Inserted sample data:';
    RAISE NOTICE '- 7 sample users (3 sellers, 3 buyers, 1 admin)';
    RAISE NOTICE '- 3 sample properties (condo, house, townhouse)';
    RAISE NOTICE '- 5 property images';
    RAISE NOTICE '- 2 contact share requests';
    RAISE NOTICE '- 7 seller availability schedules';
    RAISE NOTICE '';
    RAISE NOTICE '🎉 Database setup complete! Ready for development and testing.';
    RAISE NOTICE '';
END $$;
