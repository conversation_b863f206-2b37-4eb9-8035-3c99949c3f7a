using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;

namespace MicroSaasWebApi.Services.SoNoBrokers
{
    public class PropertyImageService : IPropertyImageService
    {
        private readonly MicroSaasWebApi.App.Context.IDapperDbContext _dbContext;
        private readonly ILogger<PropertyImageService> _logger;

        public PropertyImageService(MicroSaasWebApi.App.Context.IDapperDbContext dbContext, ILogger<PropertyImageService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<IEnumerable<PropertyImage>> GetPropertyImagesAsync(string propertyId)
        {
            try
            {
                var sql = @"
                    SELECT id, ""propertyId"", url, alt, ""order"", ""isPrimary"", ""createdAt""
                    FROM public.""PropertyImage""
                    WHERE ""propertyId"" = @propertyId
                    ORDER BY ""isPrimary"" DESC, ""order"" ASC, ""createdAt"" ASC";

                return await _dbContext.QueryAsync<PropertyImage>(sql, new { propertyId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving images for property {PropertyId}", propertyId);
                throw;
            }
        }

        public async Task<PropertyImage?> GetPropertyImageByIdAsync(string id)
        {
            try
            {
                var sql = @"
                    SELECT pi.id, pi.""propertyId"", pi.url, pi.alt, pi.""order"", pi.""isPrimary"", pi.""createdAt""
                    FROM public.""PropertyImage"" pi
                    WHERE pi.id = @id";

                return await _dbContext.QueryFirstOrDefaultAsync<PropertyImage>(sql, new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving property image {ImageId}", id);
                throw;
            }
        }

        public async Task<PropertyImage> CreatePropertyImageAsync(PropertyImage propertyImage)
        {
            try
            {
                // Verify property exists
                var propertyExists = await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM snb.properties WHERE id = @propertyId)",
                    new { propertyId = propertyImage.PropertyId });

                if (!propertyExists)
                {
                    throw new InvalidOperationException("Property not found");
                }

                propertyImage.Id = Guid.NewGuid().ToString();
                propertyImage.CreatedAt = DateTime.UtcNow;

                var sql = @"
                    INSERT INTO snb.property_images (id, property_id, url, storage_path, is_primary, created_at, display_order)
                    VALUES (@Id, @PropertyId, @Url, @StoragePath, @IsPrimary, @CreatedAt, @DisplayOrder)";

                await _dbContext.ExecuteAsync(sql, propertyImage);

                return propertyImage;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating property image");
                throw;
            }
        }

        public async Task<PropertyImage> UpdatePropertyImageAsync(PropertyImage propertyImage)
        {
            try
            {
                var exists = await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM snb.property_images WHERE id = @id)",
                    new { id = propertyImage.Id });

                if (!exists)
                {
                    throw new InvalidOperationException("Property image not found");
                }

                var sql = @"
                    UPDATE snb.property_images SET
                        url = @Url,
                        storage_path = @StoragePath,
                        is_primary = @IsPrimary
                    WHERE id = @Id";

                await _dbContext.ExecuteAsync(sql, propertyImage);

                // Return updated image
                return await GetPropertyImageByIdAsync(propertyImage.Id) ?? propertyImage;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating property image {ImageId}", propertyImage.Id);
                throw;
            }
        }

        public async Task<bool> DeletePropertyImageAsync(string id)
        {
            try
            {
                var exists = await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM snb.property_images WHERE id = @id)",
                    new { id });

                if (!exists)
                {
                    return false;
                }

                // TODO: Delete actual file from Azure Blob Storage

                await _dbContext.ExecuteAsync("DELETE FROM snb.property_images WHERE id = @id", new { id });
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting property image {ImageId}", id);
                throw;
            }
        }

        public async Task<bool> SetPrimaryImageAsync(string propertyId, string imageId)
        {
            try
            {
                // Verify property exists
                var propertyExists = await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM snb.properties WHERE id = @propertyId)",
                    new { propertyId });

                if (!propertyExists)
                {
                    return false;
                }

                // Verify image exists and belongs to property
                var imageExists = await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM snb.property_images WHERE id = @imageId AND property_id = @propertyId)",
                    new { imageId, propertyId });

                if (!imageExists)
                {
                    return false;
                }

                // Update primary flags in a transaction
                await _dbContext.ExecuteInTransactionAsync(async (context) =>
                {
                    // Remove primary flag from all images for this property
                    await context.ExecuteAsync(
                        "UPDATE snb.property_images SET is_primary = false WHERE property_id = @propertyId",
                        new { propertyId });

                    // Set the specified image as primary
                    await context.ExecuteAsync(
                        "UPDATE snb.property_images SET is_primary = true WHERE id = @imageId",
                        new { imageId });
                });

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting primary image for property {PropertyId}", propertyId);
                throw;
            }
        }

        public async Task<PropertyImage?> GetPrimaryImageAsync(string propertyId)
        {
            try
            {
                var sql = @"
                    SELECT * FROM snb.property_images
                    WHERE property_id = @propertyId AND is_primary = true";

                return await _dbContext.QueryFirstOrDefaultAsync<PropertyImage>(sql, new { propertyId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving primary image for property {PropertyId}", propertyId);
                throw;
            }
        }

        public async Task<IEnumerable<PropertyImage>> UploadImagesAsync(string propertyId, IFormFileCollection files)
        {
            try
            {
                // Verify property exists
                var propertyExists = await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM snb.properties WHERE id = @propertyId)",
                    new { propertyId });

                if (!propertyExists)
                {
                    throw new InvalidOperationException("Property not found");
                }

                if (files == null || files.Count == 0)
                {
                    throw new ArgumentException("No files provided");
                }

                var uploadedImages = new List<PropertyImage>();

                foreach (var file in files)
                {
                    if (file.Length > 0)
                    {
                        // Generate unique filename
                        var fileName = $"{Guid.NewGuid()}_{file.FileName}";
                        var storagePath = $"properties/{propertyId}/{fileName}";

                        // TODO: Implement actual file upload to Azure Blob Storage
                        // For now, we'll just store the path
                        var imageUrl = $"/api/storage/properties/{propertyId}/{fileName}";

                        var propertyImage = new PropertyImage
                        {
                            Id = Guid.NewGuid().ToString(),
                            PropertyId = propertyId,
                            Url = imageUrl,
                            StoragePath = storagePath,
                            IsPrimary = uploadedImages.Count == 0, // First image is primary
                            Order = uploadedImages.Count,
                            CreatedAt = DateTime.UtcNow
                        };

                        uploadedImages.Add(propertyImage);
                    }
                }

                // Insert all images in a transaction
                await _dbContext.ExecuteInTransactionAsync(async (context) =>
                {
                    foreach (var image in uploadedImages)
                    {
                        var sql = @"
                            INSERT INTO public.""PropertyImage"" (id, ""propertyId"", url, ""isPrimary"", ""order"", ""createdAt"")
                            VALUES (@Id, @PropertyId, @Url, @IsPrimary, @Order, @CreatedAt)";

                        await context.ExecuteAsync(sql, image);
                    }
                });

                return uploadedImages;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading images for property {PropertyId}", propertyId);
                throw;
            }
        }

        public async Task<bool> DeletePropertyImagesAsync(string propertyId)
        {
            try
            {
                var images = await _dbContext.QueryAsync<PropertyImage>(
                    "SELECT * FROM snb.property_images WHERE property_id = @propertyId",
                    new { propertyId });

                if (images.Any())
                {
                    // TODO: Delete actual files from Azure Blob Storage

                    await _dbContext.ExecuteAsync(
                        "DELETE FROM snb.property_images WHERE property_id = @propertyId",
                        new { propertyId });
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting images for property {PropertyId}", propertyId);
                throw;
            }
        }
    }
}
