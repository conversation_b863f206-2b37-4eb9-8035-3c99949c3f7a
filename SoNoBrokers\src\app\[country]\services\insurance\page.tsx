// Removed auth import - no authentication required for browsing insurance services
import { InsuranceService } from './InsuranceService'
import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function InsurancePage({ params, searchParams }: PageProps) {
  // No authentication required for browsing insurance services
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/services/insurance')
  }

  // Default to buyer for insurance services
  const userType = resolvedSearchParams.userType || 'buyer'

  return (
    <InsuranceService
      userType={userType}
      isSignedIn={isSignedIn}
      country={country.toUpperCase()}
    />
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'buyer'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Home Insurance Services for ${userType === 'buyer' ? 'Home Buyers' : 'Property Owners'} in ${country} | SoNoBrokers`,
    description: `Find trusted home insurance brokers and agents in ${country}. Compare quotes, get coverage recommendations, and protect your property investment with competitive rates.`,
    keywords: `home insurance, property insurance, insurance broker, ${userType}, ${country}, coverage quotes`,
  }
}
