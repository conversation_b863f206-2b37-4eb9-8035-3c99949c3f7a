﻿# =============================================================================
# Production Docker Configuration for SoNoBrokers Web API
# Overrides base settings for production environment
# =============================================================================

# =============================================================================
# Application Configuration (Production)
# =============================================================================
ASPNETCORE_ENVIRONMENT=Production
ASPNETCORE_URLS=http://+:8080

# =============================================================================
# Database Configuration (Production)
# =============================================================================
DATABASE_URL=***************************************************************************/postgres

# =============================================================================
# Authentication - Clerk Configuration (Production)
# =============================================================================
Authentication__Clerk__PublishableKey=pk_live_Y2xlcmsuc29ub2Jyb2tlcnMuY29tJA
Authentication__Clerk__SecretKey=**************************************************
Authentication__Clerk__WebhookSecret=whsec_production_webhook_secret
Authentication__Clerk__JwtIssuer=https://clerk.sonobrokers.com
CLERK_SECRET_KEY=**************************************************

# =============================================================================
# JWT Configuration (Production)
# =============================================================================
Authentication__Jwt__Issuer=https://api.sonobrokers.com
Authentication__Jwt__SigningKey=production_jwt_signing_key_minimum_32_characters_long_secure

# =============================================================================
# External Services (Production)
# =============================================================================
ExternalServices__Stripe__PublishableKey=pk_live_51Qfy9dP82YH9JfOlFmwTk0RHvE7l6sow7ImJBI4c1GzcbhFydoVpb3DMevP8B3C4gj95qCoEise8vEhgFgWxhLEN000RfInKNH
ExternalServices__Stripe__SecretKey=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
ExternalServices__Email__Resend__ApiKey=re_BM6pyT8x_ChaS1fbRCbdprxPy1fwimWCs

# =============================================================================
# Supabase Configuration (Production)
# =============================================================================
Supabase__Url=https://yfznlsisxsnymkvydzha.supabase.co
Supabase__AnonKey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.qUBshv6Wi-90ZAEnUM2RXuhR77QFHnmEpI6O-y7l3BE
Supabase__StorageBucket=property-images

# =============================================================================
# Mapbox Configuration (Production)
# =============================================================================
Mapbox__ApiKey=pk.eyJ1IjoiamF2aWFucGljYXJkbzMzIiwiYSI6ImNtYjY4ZGRyazBiaWYybHEyMWpnNGN4cDQifQ.m63aGFvbfzrQhT3sWlSbDQ

# =============================================================================
# CORS Configuration (Production)
# =============================================================================
CORS__AllowedOrigins=https://www.sonobrokers.com,https://sonobrokers.vercel.app

# =============================================================================
# Logging Configuration (Production)
# =============================================================================
Logging__LogLevel__Default=Warning
Logging__LogLevel__Microsoft.AspNetCore=Error

# =============================================================================
# SoNoBrokers Configuration (Production)
# =============================================================================
SoNoBrokers__AppUrl=https://www.sonobrokers.com
SoNoBrokers__SignInUrl=/sign-in
SoNoBrokers__SignUpUrl=/sign-up
SoNoBrokers__MaxImageUploadSize=10485760
SoNoBrokers__AllowedImageTypes__0=image/jpeg
SoNoBrokers__AllowedImageTypes__1=image/png
SoNoBrokers__AllowedImageTypes__2=image/webp
SoNoBrokers__DefaultPageSize=20
SoNoBrokers__MaxPageSize=100

# =============================================================================
# Frontend Integration (Production)
# =============================================================================
Frontend__NextJs__AppUrl=https://www.sonobrokers.com
Frontend__NextJs__SignInUrl=/sign-in
Frontend__NextJs__SignUpUrl=/sign-up
