'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { 
  MessageCircle, 
  Search, 
  Filter, 
  MoreVertical,
  Home,
  Clock,
  User
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import type { ConversationListItem } from '@/lib/api/messaging-api'

interface ConversationListProps {
  conversations: ConversationListItem[]
  selectedConversationId?: string
  onConversationSelect?: (conversationId: string) => void
  showSearch?: boolean
  showFilters?: boolean
  className?: string
}

export function ConversationList({
  conversations,
  selectedConversationId,
  onConversationSelect,
  showSearch = true,
  showFilters = true,
  className = ''
}: ConversationListProps) {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')
  const [filterActive, setFilterActive] = useState<boolean | null>(null)
  const [filteredConversations, setFilteredConversations] = useState(conversations)

  useEffect(() => {
    let filtered = conversations

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(conv => 
        conv.propertyTitle?.toLowerCase().includes(query) ||
        conv.otherParticipantName?.toLowerCase().includes(query) ||
        conv.subject?.toLowerCase().includes(query) ||
        conv.lastMessageContent?.toLowerCase().includes(query)
      )
    }

    // Apply active filter
    if (filterActive !== null) {
      filtered = filtered.filter(conv => conv.isActive === filterActive)
    }

    setFilteredConversations(filtered)
  }, [conversations, searchQuery, filterActive])

  const handleConversationClick = (conversationId: string) => {
    if (onConversationSelect) {
      onConversationSelect(conversationId)
    } else {
      router.push(`/messages/${conversationId}`)
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatLastMessageTime = (dateString?: string) => {
    if (!dateString) return ''
    
    try {
      const date = new Date(dateString)
      return formatDistanceToNow(date, { addSuffix: true })
    } catch {
      return ''
    }
  }

  if (conversations.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <MessageCircle className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No conversations yet
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-center mb-4">
            Start browsing properties to connect with sellers
          </p>
          <Button onClick={() => router.push('/properties')}>
            <Home className="h-4 w-4 mr-2" />
            Browse Properties
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Messages ({conversations.length})
          </span>
          {showFilters && (
            <div className="flex items-center gap-2">
              <Button
                variant={filterActive === null ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterActive(null)}
              >
                All
              </Button>
              <Button
                variant={filterActive === true ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterActive(true)}
              >
                Active
              </Button>
              <Button
                variant={filterActive === false ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterActive(false)}
              >
                Archived
              </Button>
            </div>
          )}
        </CardTitle>
        
        {showSearch && (
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        )}
      </CardHeader>
      
      <CardContent className="p-0">
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {filteredConversations.map((conversation) => (
            <div
              key={conversation.id}
              className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors ${
                selectedConversationId === conversation.id 
                  ? 'bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-500' 
                  : ''
              }`}
              onClick={() => handleConversationClick(conversation.id)}
            >
              <div className="flex items-start gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarFallback className="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400">
                    {conversation.otherParticipantName ? getInitials(conversation.otherParticipantName) : <User className="h-5 w-5" />}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {conversation.otherParticipantName || 'Unknown User'}
                    </h4>
                    <div className="flex items-center gap-2">
                      {conversation.unreadCount > 0 && (
                        <Badge variant="default" className="text-xs">
                          {conversation.unreadCount}
                        </Badge>
                      )}
                      <span className="text-xs text-gray-500 flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatLastMessageTime(conversation.lastMessageAt)}
                      </span>
                    </div>
                  </div>
                  
                  {conversation.propertyTitle && (
                    <div className="flex items-center gap-2 mb-1">
                      <Home className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-600 dark:text-gray-400 truncate">
                        {conversation.propertyTitle}
                      </span>
                      {conversation.propertyPrice && (
                        <span className="text-xs font-medium text-green-600 dark:text-green-400">
                          ${conversation.propertyPrice.toLocaleString()}
                        </span>
                      )}
                    </div>
                  )}
                  
                  <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                    {conversation.subject || 'Property Inquiry'}
                  </p>
                  
                  {conversation.lastMessageContent && (
                    <p className="text-xs text-gray-500 dark:text-gray-500 truncate mt-1">
                      {conversation.lastMessageContent}
                    </p>
                  )}
                  
                  <div className="flex items-center justify-between mt-2">
                    <div className="flex items-center gap-2">
                      {!conversation.isActive && (
                        <Badge variant="secondary" className="text-xs">
                          Archived
                        </Badge>
                      )}
                      {conversation.propertyAddress && (
                        <span className="text-xs text-gray-500 truncate">
                          {conversation.propertyAddress}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                
                <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
        
        {filteredConversations.length === 0 && searchQuery && (
          <div className="p-8 text-center">
            <Search className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600 dark:text-gray-400">
              No conversations found matching "{searchQuery}"
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * Compact conversation list for sidebar
 */
export function CompactConversationList({
  conversations,
  selectedConversationId,
  onConversationSelect,
  className = ''
}: Omit<ConversationListProps, 'showSearch' | 'showFilters'>) {
  return (
    <div className={`space-y-2 ${className}`}>
      {conversations.map((conversation) => (
        <div
          key={conversation.id}
          className={`p-3 rounded-lg cursor-pointer transition-colors ${
            selectedConversationId === conversation.id 
              ? 'bg-blue-100 dark:bg-blue-900/20' 
              : 'hover:bg-gray-100 dark:hover:bg-gray-800'
          }`}
          onClick={() => onConversationSelect?.(conversation.id)}
        >
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-xs">
                {conversation.otherParticipantName ? 
                  conversation.otherParticipantName.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2) :
                  <User className="h-4 w-4" />
                }
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">
                {conversation.otherParticipantName || 'Unknown User'}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {conversation.propertyTitle || conversation.subject}
              </p>
            </div>
            {conversation.unreadCount > 0 && (
              <Badge variant="default" className="text-xs h-5 w-5 rounded-full p-0 flex items-center justify-center">
                {conversation.unreadCount}
              </Badge>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}
