using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    [Table("AIReport", Schema = "snb")]
    public class AIReport
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string PropertyId { get; set; } = string.Empty;

        [Required]
        public string ReportType { get; set; } = string.Empty;

        [Required]
        public string Title { get; set; } = string.Empty;

        [Required]
        public string Description { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "jsonb")]
        public string Data { get; set; } = string.Empty;

        public int? Score { get; set; }

        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        // Navigation properties
        [ForeignKey("PropertyId")]
        public virtual Property Property { get; set; } = null!;

        // Helper methods for JSON fields
        public T? GetDataAs<T>() where T : class
        {
            if (string.IsNullOrEmpty(Data)) return null;
            try
            {
                return JsonSerializer.Deserialize<T>(Data);
            }
            catch
            {
                return null;
            }
        }

        public void SetData<T>(T data) where T : class
        {
            Data = data != null ? JsonSerializer.Serialize(data) : "{}";
        }
    }
}
