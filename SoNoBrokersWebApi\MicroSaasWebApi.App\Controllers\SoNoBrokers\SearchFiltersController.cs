using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.Core;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using System.Security.Claims;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/sonobrokers/search-filters")]
    [Tags("Search & Filters")]
    [Authorize]
    public class SearchFiltersController : ControllerBase
    {
        private readonly ISearchFilterService _searchFilterService;
        private readonly ILogger<SearchFiltersController> _logger;

        public SearchFiltersController(ISearchFilterService searchFilterService, ILogger<SearchFiltersController> logger)
        {
            _searchFilterService = searchFilterService;
            _logger = logger;
        }

        /// <summary>
        /// Get search filters for the current user
        /// </summary>
        /// <returns>List of search filters</returns>
        [HttpGet]



        public async Task<ActionResult<ApiResponse<IEnumerable<SearchFilter>>>> GetSearchFilters()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(ApiResponse<IEnumerable<SearchFilter>>.ErrorResult("User not authenticated"));
                }

                var searchFilters = await _searchFilterService.GetUserSearchFiltersAsync(userId);

                return Ok(ApiResponse<IEnumerable<SearchFilter>>.SuccessResult(
                    searchFilters,
                    "Search filters retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving search filters for user");
                return StatusCode(500, ApiResponse<IEnumerable<SearchFilter>>.ErrorResult("Failed to retrieve search filters"));
            }
        }

        /// <summary>
        /// Create a new search filter
        /// </summary>
        /// <param name="request">Search filter details</param>
        /// <returns>Created search filter</returns>
        [HttpPost]




        public async Task<ActionResult<ApiResponse<SearchFilter>>> CreateSearchFilter([FromBody] CreateSearchFilterRequest request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(ApiResponse<SearchFilter>.ErrorResult("User not authenticated"));
                }

                if (string.IsNullOrWhiteSpace(request.Name))
                {
                    return BadRequest(ApiResponse<SearchFilter>.ErrorResult("Filter name is required"));
                }

                var searchFilter = new SearchFilter
                {
                    UserId = userId,
                    Name = request.Name,
                    Filters = System.Text.Json.JsonSerializer.Serialize(request.Filters),
                    IsDefault = request.IsDefault,
                    IsActive = true
                };

                var createdFilter = await _searchFilterService.CreateSearchFilterAsync(searchFilter);

                // Handle default filter logic if needed
                if (request.IsDefault)
                {
                    await _searchFilterService.SetDefaultSearchFilterAsync(userId, createdFilter.Id);
                }

                return CreatedAtAction(nameof(GetSearchFilter), new { id = createdFilter.Id },
                    ApiResponse<SearchFilter>.SuccessResult(createdFilter, "Search filter created successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating search filter");
                return StatusCode(500, ApiResponse<SearchFilter>.ErrorResult("Failed to create search filter"));
            }
        }

        /// <summary>
        /// Get search filter by ID
        /// </summary>
        /// <param name="id">Search filter ID</param>
        /// <returns>Search filter details</returns>
        [HttpGet("{id}")]




        public async Task<ActionResult<ApiResponse<SearchFilter>>> GetSearchFilter(string id)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(ApiResponse<SearchFilter>.ErrorResult("User not authenticated"));
                }

                var searchFilter = await _searchFilterService.GetSearchFilterByIdAsync(id);

                if (searchFilter == null || searchFilter.UserId != userId)
                {
                    return NotFound(ApiResponse<SearchFilter>.ErrorResult("Search filter not found"));
                }

                return Ok(ApiResponse<SearchFilter>.SuccessResult(searchFilter, "Search filter retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving search filter with ID: {Id}", id);
                return StatusCode(500, ApiResponse<SearchFilter>.ErrorResult("Failed to retrieve search filter"));
            }
        }

        /// <summary>
        /// Update search filter
        /// </summary>
        /// <param name="id">Search filter ID</param>
        /// <param name="request">Updated search filter details</param>
        /// <returns>Updated search filter</returns>
        [HttpPut("{id}")]





        public async Task<ActionResult<ApiResponse<SearchFilter>>> UpdateSearchFilter(string id, [FromBody] UpdateSearchFilterRequest request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(ApiResponse<SearchFilter>.ErrorResult("User not authenticated"));
                }

                // Get existing search filter
                var existingFilter = await _searchFilterService.GetSearchFilterByIdAsync(id);

                if (existingFilter == null || existingFilter.UserId != userId)
                {
                    return NotFound(ApiResponse<SearchFilter>.ErrorResult("Search filter not found"));
                }

                // Update the filter properties
                if (!string.IsNullOrWhiteSpace(request.Name))
                {
                    existingFilter.Name = request.Name;
                }

                if (request.Filters != null)
                {
                    existingFilter.Filters = System.Text.Json.JsonSerializer.Serialize(request.Filters);
                }

                if (request.IsDefault.HasValue)
                {
                    existingFilter.IsDefault = request.IsDefault.Value;
                }

                var updatedSearchFilter = await _searchFilterService.UpdateSearchFilterAsync(existingFilter);

                // Handle default filter logic if needed
                if (request.IsDefault.HasValue && request.IsDefault.Value)
                {
                    await _searchFilterService.SetDefaultSearchFilterAsync(userId, id);
                }

                return Ok(ApiResponse<SearchFilter>.SuccessResult(updatedSearchFilter, "Search filter updated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating search filter with ID: {Id}", id);
                return StatusCode(500, ApiResponse<SearchFilter>.ErrorResult("Failed to update search filter"));
            }
        }

        /// <summary>
        /// Delete search filter
        /// </summary>
        /// <param name="id">Search filter ID</param>
        /// <returns>Success message</returns>
        [HttpDelete("{id}")]




        public async Task<ActionResult<ApiResponse<object>>> DeleteSearchFilter(string id)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(ApiResponse<object>.ErrorResult("User not authenticated"));
                }

                // Verify ownership before deletion
                var existingFilter = await _searchFilterService.GetSearchFilterByIdAsync(id);
                if (existingFilter == null || existingFilter.UserId != userId)
                {
                    return NotFound(ApiResponse<object>.ErrorResult("Search filter not found"));
                }

                var deleted = await _searchFilterService.DeleteSearchFilterAsync(id);

                if (!deleted)
                {
                    return NotFound(ApiResponse<object>.ErrorResult("Search filter not found"));
                }

                return Ok(ApiResponse<object>.SuccessResult(new { }, "Search filter deleted successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting search filter with ID: {Id}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResult("Failed to delete search filter"));
            }
        }
    }

    public class CreateSearchFilterRequest
    {
        public string Name { get; set; } = string.Empty;
        public object Filters { get; set; } = new { };
        public bool IsDefault { get; set; } = false;
    }

    public class UpdateSearchFilterRequest
    {
        public string? Name { get; set; }
        public object? Filters { get; set; }
        public bool? IsDefault { get; set; }
    }
}
