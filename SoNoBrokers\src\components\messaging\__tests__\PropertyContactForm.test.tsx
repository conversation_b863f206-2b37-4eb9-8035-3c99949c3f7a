import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { useUser } from '@clerk/nextjs'
import { PropertyContactForm, PropertyContactButton } from '../PropertyContactForm'

// Mock Clerk
jest.mock('@clerk/nextjs', () => ({
  useUser: jest.fn(),
}))

// Mock Server Actions
jest.mock('@/lib/actions/messaging-actions', () => ({
  startConversationFromPropertyAction: jest.fn(),
}))

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>

describe('PropertyContactForm', () => {
  const defaultProps = {
    propertyId: 'prop-123',
    sellerId: 'seller-123',
    sellerName: 'John Seller',
    propertyTitle: 'Beautiful Family Home',
    propertyPrice: 500000,
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('shows sign in prompt when user is not signed in', () => {
    mockUseUser.mockReturnValue({
      user: null,
      isSignedIn: false,
      isLoaded: true,
    })

    render(<PropertyContactForm {...defaultProps} />)

    expect(screen.getByText('Contact Seller')).toBeInTheDocument()
    expect(screen.getByText('Sign in to contact John Seller about this property')).toBeInTheDocument()
    expect(screen.getByText('Sign In to Contact Seller')).toBeInTheDocument()
  })

  it('shows contact button when user is signed in but form not expanded', () => {
    mockUseUser.mockReturnValue({
      user: { id: 'user-123' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<PropertyContactForm {...defaultProps} />)

    expect(screen.getByText('Contact Seller')).toBeInTheDocument()
    expect(screen.getByText('Send a message to John Seller about this property')).toBeInTheDocument()
    expect(screen.getByText('Send Message')).toBeInTheDocument()
  })

  it('expands form when Send Message button is clicked', async () => {
    const user = userEvent.setup()
    mockUseUser.mockReturnValue({
      user: { id: 'user-123' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<PropertyContactForm {...defaultProps} />)

    const sendMessageButton = screen.getByText('Send Message')
    await user.click(sendMessageButton)

    expect(screen.getByLabelText('Subject')).toBeInTheDocument()
    expect(screen.getByLabelText('Message')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Inquiry about Beautiful Family Home')).toBeInTheDocument()
  })

  it('shows form fields when expanded', async () => {
    const user = userEvent.setup()
    mockUseUser.mockReturnValue({
      user: { id: 'user-123' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<PropertyContactForm {...defaultProps} />)

    // Expand the form
    await user.click(screen.getByText('Send Message'))

    // Check form fields
    expect(screen.getByLabelText('Subject')).toBeInTheDocument()
    expect(screen.getByLabelText('Message')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /send message/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument()
  })

  it('collapses form when Cancel button is clicked', async () => {
    const user = userEvent.setup()
    mockUseUser.mockReturnValue({
      user: { id: 'user-123' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<PropertyContactForm {...defaultProps} />)

    // Expand the form
    await user.click(screen.getByText('Send Message'))
    expect(screen.getByLabelText('Subject')).toBeInTheDocument()

    // Collapse the form
    await user.click(screen.getByRole('button', { name: /cancel/i }))
    expect(screen.queryByLabelText('Subject')).not.toBeInTheDocument()
  })

  it('displays seller information correctly', () => {
    mockUseUser.mockReturnValue({
      user: { id: 'user-123' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<PropertyContactForm {...defaultProps} />)

    expect(screen.getByText('John Seller')).toBeInTheDocument()
    expect(screen.getByText('Property Owner')).toBeInTheDocument()
  })

  it('has correct placeholder text in message field', async () => {
    const user = userEvent.setup()
    mockUseUser.mockReturnValue({
      user: { id: 'user-123' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<PropertyContactForm {...defaultProps} />)

    // Expand the form
    await user.click(screen.getByText('Send Message'))

    const messageField = screen.getByLabelText('Message')
    expect(messageField).toHaveAttribute(
      'placeholder',
      "Hi John Seller, I'm interested in your property listed at $500,000. Could you please provide more information?"
    )
  })

  it('shows helpful tips for users', async () => {
    const user = userEvent.setup()
    mockUseUser.mockReturnValue({
      user: { id: 'user-123' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<PropertyContactForm {...defaultProps} />)

    // Expand the form
    await user.click(screen.getByText('Send Message'))

    expect(screen.getByText('• Your message will be sent directly to the property owner')).toBeInTheDocument()
    expect(screen.getByText("• You'll be notified when they respond")).toBeInTheDocument()
    expect(screen.getByText('• All conversations are private and secure')).toBeInTheDocument()
  })
})

describe('PropertyContactButton', () => {
  const defaultProps = {
    propertyId: 'prop-123',
    sellerId: 'seller-123',
    sellerName: 'John Seller',
    propertyTitle: 'Beautiful Family Home',
  }

  beforeEach(() => {
    jest.clearAllMocks()
    // Mock window.location.href
    delete (window as any).location
    window.location = { href: '' } as any
  })

  it('renders contact button', () => {
    mockUseUser.mockReturnValue({
      user: { id: 'user-123' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<PropertyContactButton {...defaultProps} />)

    expect(screen.getByText('Contact Seller')).toBeInTheDocument()
  })

  it('redirects to sign in when user is not signed in', async () => {
    const user = userEvent.setup()
    mockUseUser.mockReturnValue({
      user: null,
      isSignedIn: false,
      isLoaded: true,
    })

    render(<PropertyContactButton {...defaultProps} />)

    await user.click(screen.getByText('Contact Seller'))

    expect(window.location.href).toBe('/sign-in')
  })

  it('redirects to property page when user is signed in', async () => {
    const user = userEvent.setup()
    mockUseUser.mockReturnValue({
      user: { id: 'user-123' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<PropertyContactButton {...defaultProps} />)

    await user.click(screen.getByText('Contact Seller'))

    expect(window.location.href).toBe('/properties/prop-123#contact')
  })
})

describe('QuickContactModal', () => {
  const defaultProps = {
    propertyId: 'prop-123',
    sellerId: 'seller-123',
    sellerName: 'John Seller',
    propertyTitle: 'Beautiful Family Home',
    propertyPrice: 500000,
    trigger: <button>Open Modal</button>,
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('opens modal when trigger is clicked', async () => {
    const user = userEvent.setup()
    mockUseUser.mockReturnValue({
      user: { id: 'user-123' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<PropertyContactForm {...defaultProps} />)

    await user.click(screen.getByText('Open Modal'))

    expect(screen.getByText('Contact Seller')).toBeInTheDocument()
  })

  it('closes modal when close button is clicked', async () => {
    const user = userEvent.setup()
    mockUseUser.mockReturnValue({
      user: { id: 'user-123' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<PropertyContactForm {...defaultProps} />)

    // Open modal
    await user.click(screen.getByText('Open Modal'))
    expect(screen.getByText('Contact Seller')).toBeInTheDocument()

    // Close modal
    await user.click(screen.getByText('×'))
    
    // Modal should be closed (contact form should not be visible)
    await waitFor(() => {
      expect(screen.queryByText('Contact Seller')).not.toBeInTheDocument()
    })
  })
})
