using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Extensions;
using MicroSaasWebApi.Models.SoNoBrokers.ContactSharing;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;

namespace MicroSaasWebApi.Controllers.Admin
{
    /// <summary>
    /// Admin controller for contact sharing management
    /// </summary>
    [ApiController]
    [Route("api/admin/contact-sharing")]
    [Authorize]
    public class AdminContactSharingController : ControllerBase
    {
        private readonly IContactSharingService _contactSharingService;
        private readonly ILogger<AdminContactSharingController> _logger;

        public AdminContactSharingController(
            IContactSharingService contactSharingService,
            ILogger<AdminContactSharingController> logger)
        {
            _contactSharingService = contactSharingService;
            _logger = logger;
        }

        /// <summary>
        /// Get all contact shares (admin only)
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ContactShareSearchResponse>> GetAllContactShares([FromQuery] ContactShareSearchParams searchParams)
        {
            try
            {
                // Check if user is admin
                if (!User.IsAdmin())
                {
                    return Forbid("Admin access required");
                }

                var result = await _contactSharingService.GetAllContactSharesAsync(searchParams);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get all contact shares for admin {UserId}", User.GetUserId());
                return StatusCode(500, "An error occurred while retrieving contact shares");
            }
        }

        /// <summary>
        /// Get contact share by ID (admin only)
        /// </summary>
        [HttpGet("{contactShareId}")]
        public async Task<ActionResult<ContactShareResponse>> GetContactShare(string contactShareId)
        {
            try
            {
                // Check if user is admin
                if (!User.IsAdmin())
                {
                    return Forbid("Admin access required");
                }

                var contactShare = await _contactSharingService.GetContactShareAsync(contactShareId);
                if (contactShare == null)
                {
                    return NotFound("Contact share not found");
                }

                return Ok(contactShare);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get contact share {ContactShareId} for admin {UserId}",
                    contactShareId, User.GetUserId());
                return StatusCode(500, "An error occurred while retrieving the contact share");
            }
        }

        /// <summary>
        /// Get contact share statistics (admin only)
        /// </summary>
        [HttpGet("stats")]
        public async Task<ActionResult<ContactShareStats>> GetContactShareStats(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                // Check if user is admin
                if (!User.IsAdmin())
                {
                    return Forbid("Admin access required");
                }

                var stats = await _contactSharingService.GetAdminContactShareStatsAsync(fromDate, toDate);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get contact share stats for admin {UserId}", User.GetUserId());
                return StatusCode(500, "An error occurred while retrieving contact share statistics");
            }
        }

        /// <summary>
        /// Send reminder emails for pending contact shares (admin only)
        /// </summary>
        [HttpPost("send-reminders")]
        public async Task<ActionResult> SendReminderEmails()
        {
            try
            {
                // Check if user is admin
                if (!User.IsAdmin())
                {
                    return Forbid("Admin access required");
                }

                var success = await _contactSharingService.SendReminderEmailsAsync();

                if (success)
                {
                    return Ok(new { message = "Reminder emails sent successfully" });
                }
                else
                {
                    return BadRequest("Failed to send reminder emails");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send reminder emails for admin {UserId}", User.GetUserId());
                return StatusCode(500, "An error occurred while sending reminder emails");
            }
        }

        /// <summary>
        /// Update contact share status (admin only)
        /// </summary>
        [HttpPut("{contactShareId}/status")]
        public async Task<ActionResult> UpdateContactShareStatus(string contactShareId, [FromBody] ContactShareSellerResponse response)
        {
            try
            {
                // Check if user is admin
                if (!User.IsAdmin())
                {
                    return Forbid("Admin access required");
                }

                response.ContactShareId = contactShareId;
                var success = await _contactSharingService.UpdateContactShareStatusAsync(contactShareId, response);

                if (success)
                {
                    return Ok(new { message = "Contact share status updated successfully" });
                }
                else
                {
                    return BadRequest("Failed to update contact share status");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update contact share status {ContactShareId} for admin {UserId}",
                    contactShareId, User.GetUserId());
                return StatusCode(500, "An error occurred while updating the contact share status");
            }
        }

        /// <summary>
        /// Delete contact share (admin only)
        /// </summary>
        [HttpDelete("{contactShareId}")]
        public async Task<ActionResult> DeleteContactShare(string contactShareId)
        {
            try
            {
                // Check if user is admin
                if (!User.IsAdmin())
                {
                    return Forbid("Admin access required");
                }

                var success = await _contactSharingService.DeleteContactShareAsync(contactShareId);

                if (success)
                {
                    return Ok(new { message = "Contact share deleted successfully" });
                }
                else
                {
                    return NotFound("Contact share not found");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete contact share {ContactShareId} for admin {UserId}",
                    contactShareId, User.GetUserId());
                return StatusCode(500, "An error occurred while deleting the contact share");
            }
        }

        /// <summary>
        /// Get contact shares by user ID (admin only)
        /// </summary>
        [HttpGet("user/{userId}")]
        public async Task<ActionResult<ContactShareSearchResponse>> GetUserContactShares(
            string userId,
            [FromQuery] ContactShareSearchParams searchParams)
        {
            try
            {
                // Check if user is admin
                if (!User.IsAdmin())
                {
                    return Forbid("Admin access required");
                }

                var result = await _contactSharingService.GetContactSharesAsync(userId, searchParams);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get contact shares for user {UserId} by admin {AdminId}",
                    userId, User.GetUserId());
                return StatusCode(500, "An error occurred while retrieving user contact shares");
            }
        }

        /// <summary>
        /// Get contact shares by property ID (admin only)
        /// </summary>
        [HttpGet("property/{propertyId}")]
        public async Task<ActionResult<ContactShareSearchResponse>> GetPropertyContactShares(
            string propertyId,
            [FromQuery] ContactShareSearchParams searchParams)
        {
            try
            {
                // Check if user is admin
                if (!User.IsAdmin())
                {
                    return Forbid("Admin access required");
                }

                var result = await _contactSharingService.GetPropertyContactSharesAsync(propertyId, searchParams);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get contact shares for property {PropertyId} by admin {AdminId}",
                    propertyId, User.GetUserId());
                return StatusCode(500, "An error occurred while retrieving property contact shares");
            }
        }
    }
}
