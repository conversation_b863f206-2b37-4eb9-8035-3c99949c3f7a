'use client'

import { useSearchParams } from 'next/navigation'
import { StandalonePage } from '@/components/shared/layout/StandalonePage'

// Country information for better messaging
const countryInfo: Record<string, { name: string; flag: string; message: string }> = {
  'FR': {
    name: 'France',
    flag: '🇫🇷',
    message: 'We are actively exploring the French real estate market and plan to launch soon.'
  },
  'DE': {
    name: 'Germany',
    flag: '🇩🇪',
    message: 'Germany is on our expansion roadmap. Stay tuned for updates!'
  },
  'UK': {
    name: 'United Kingdom',
    flag: '🇬🇧',
    message: 'The UK market is part of our European expansion plans.'
  },
  'AU': {
    name: 'Australia',
    flag: '🇦🇺',
    message: 'Australia is a key market in our Asia-Pacific expansion strategy.'
  },
  'IN': {
    name: 'India',
    flag: '🇮🇳',
    message: 'India represents a significant opportunity in our global expansion.'
  },
  'SG': {
    name: 'Singapore',
    flag: '🇸🇬',
    message: 'Singapore is part of our Southeast Asian market entry plans.'
  },
}

export function UnsupportedRegionClient() {
  const searchParams = useSearchParams()
  const countryCode = searchParams.get('country')?.toUpperCase() || 'UNKNOWN'

  const country = countryInfo[countryCode] || {
    name: countryCode === 'UNKNOWN' ? 'your region' : countryCode,
    flag: '🌍',
    message: 'We are continuously expanding to new markets worldwide.'
  }

  // Clear all region data and return to home
  const handleBackToHome = () => {
    // Clear all region-related localStorage data
    localStorage.removeItem('testCountry')
    localStorage.removeItem('userCountry')
    localStorage.removeItem('countryValid')

    console.log('🧹 Cleared all region data - returning to home')

    // Navigate to home page with full page reload to ensure clean state
    window.location.href = '/'
  }

  return (
    <StandalonePage
      title="Region Not Supported | SoNoBrokers"
      description="SoNoBrokers is not yet available in your region. We are expanding globally and will notify you when we launch in your area."
    >
      <div className="min-h-screen bg-background">
        <div className="min-h-screen flex items-center justify-center p-4">
          <div className="w-full max-w-4xl mx-auto">
            {/* Main Content Card */}
            <div className="bg-card border border-border rounded-lg shadow-lg">
              <div className="p-12 text-center">
                {/* Logo and Brand */}
                <div className="mb-8">
                  <h1 className="text-6xl font-bold text-primary mb-2">
                    SoNoBrokers
                  </h1>
                  <p className="text-lg text-muted-foreground">
                    For Sale By Owner Real Estate Platform
                  </p>
                </div>

                {/* Country Badge */}
                <div className="mb-8">
                  <div className="inline-flex items-center px-4 py-2 border border-border rounded-lg text-lg bg-muted">
                    📍 {country.flag} {country.name}
                  </div>
                </div>

                {/* Main Message */}
                <div className="mb-12">
                  <h2 className="text-4xl font-bold text-foreground mb-6">
                    Coming Soon to {country.name}
                  </h2>
                  <p className="text-xl text-muted-foreground mb-6 max-w-2xl mx-auto leading-relaxed">
                    {country.message}
                  </p>
                  <p className="text-lg text-muted-foreground">
                    Join our waiting list to be the first to know when we launch in your area.
                  </p>
                </div>

                {/* Current Markets */}
                <div className="mb-12">
                  <h3 className="text-2xl font-semibold text-foreground mb-6">
                    Currently Available In
                  </h3>
                  <div className="flex flex-wrap justify-center gap-4">
                    <div className="text-base px-4 py-2 bg-secondary text-secondary-foreground rounded-lg">
                      🇨🇦 Canada
                    </div>
                    <div className="text-base px-4 py-2 bg-secondary text-secondary-foreground rounded-lg">
                      🇺🇸 United States
                    </div>
                    <div className="text-base px-4 py-2 bg-secondary text-secondary-foreground rounded-lg">
                      🇦🇪 United Arab Emirates
                    </div>
                  </div>
                </div>

                {/* Features Preview */}
                <div className="grid md:grid-cols-3 gap-6 mb-12">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-2xl">👥</span>
                    </div>
                    <h4 className="font-semibold mb-2 text-foreground">Direct Sales</h4>
                    <p className="text-sm text-muted-foreground">
                      Connect directly with buyers and save on commissions
                    </p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-2xl">🌍</span>
                    </div>
                    <h4 className="font-semibold mb-2 text-foreground">Global Platform</h4>
                    <p className="text-sm text-muted-foreground">
                      Expanding worldwide to serve property owners everywhere
                    </p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-2xl">⏰</span>
                    </div>
                    <h4 className="font-semibold mb-2 text-foreground">Coming Soon</h4>
                    <p className="text-sm text-muted-foreground">
                      Be the first to access our platform in your region
                    </p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                  <a
                    href="/waiting-list"
                    className="inline-flex items-center px-6 py-3 bg-primary hover:bg-primary/90 text-primary-foreground rounded-lg font-medium min-w-[200px] justify-center transition-colors"
                  >
                    ✉️ Join Waiting List
                  </a>
                  <button
                    onClick={handleBackToHome}
                    className="inline-flex items-center px-6 py-3 border border-border hover:bg-accent hover:text-accent-foreground text-foreground rounded-lg font-medium min-w-[200px] justify-center transition-colors"
                  >
                    ← Back to Home
                  </button>
                </div>

                {/* Contact Info */}
                <div className="mt-12 pt-8 border-t border-border">
                  <p className="text-sm text-muted-foreground">
                    Questions? Contact us at{' '}
                    <a
                      href="mailto:<EMAIL>"
                      className="text-primary hover:underline"
                    >
                      <EMAIL>
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </StandalonePage>
  )
}
