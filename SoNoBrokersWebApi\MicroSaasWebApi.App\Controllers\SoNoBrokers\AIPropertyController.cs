using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/sonobrokers/ai")]
    [Tags("AI Services")]
    public class AIPropertyController : ControllerBase
    {
        private readonly IAIPropertyService _aiPropertyService;
        private readonly ILogger<AIPropertyController> _logger;

        public AIPropertyController(
            IAIPropertyService aiPropertyService,
            ILogger<AIPropertyController> logger)
        {
            _aiPropertyService = aiPropertyService;
            _logger = logger;
        }

        /// <summary>
        /// Import property data using AI
        /// </summary>
        [HttpPost("property-import")]
        public async Task<ActionResult<AIPropertyImportResponse>> ImportPropertyData([FromBody] AIPropertyImportRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Address))
                {
                    return BadRequest(new { error = "Address is required" });
                }

                var result = await _aiPropertyService.ImportPropertyDataAsync(request);

                if (result.Status == "error")
                {
                    return StatusCode(500, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in AI property import");
                return StatusCode(500, new { error = "Failed to import property data" });
            }
        }

        /// <summary>
        /// Get property valuation using AI
        /// </summary>
        [HttpPost("property-valuation")]
        public async Task<ActionResult<AIPropertyValuationResponse>> GetPropertyValuation([FromBody] AIPropertyValuationRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Address))
                {
                    return BadRequest(new { error = "Address is required" });
                }

                if (string.IsNullOrEmpty(request.Country))
                {
                    return BadRequest(new { error = "Country is required" });
                }

                var result = await _aiPropertyService.GetPropertyValuationAsync(request);

                if (result.Status == "error")
                {
                    return StatusCode(500, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in AI property valuation");
                return StatusCode(500, new { error = "Failed to get property valuation" });
            }
        }

        /// <summary>
        /// Generate property description using AI
        /// </summary>
        [HttpPost("generate-description")]
        public async Task<ActionResult<GenerateDescriptionResponse>> GenerateDescription([FromBody] GenerateDescriptionRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Prompt))
                {
                    return BadRequest(new { error = "Prompt is required" });
                }

                if (string.IsNullOrEmpty(request.UserId))
                {
                    return BadRequest(new { error = "User ID is required" });
                }

                var result = await _aiPropertyService.GeneratePropertyDescriptionAsync(request);

                if (result.Status == "error")
                {
                    return StatusCode(500, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating AI description");
                return StatusCode(500, new { error = "Failed to generate description" });
            }
        }
    }
}
