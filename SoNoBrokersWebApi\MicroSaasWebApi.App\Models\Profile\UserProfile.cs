using System.ComponentModel.DataAnnotations;

namespace MicroSaasWebApi.Models.Profile
{
    /// <summary>
    /// User Profile model for MicroSaaS template
    /// </summary>
    public class UserProfile
    {
        public Guid Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string Email { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string LastName { get; set; } = string.Empty;

        [MaxLength(15)]
        public string? PhoneNumber { get; set; }

        [MaxLength(500)]
        public string? Bio { get; set; }

        [MaxLength(200)]
        public string? ProfileImageUrl { get; set; }

        [MaxLength(100)]
        public string? Company { get; set; }

        [MaxLength(100)]
        public string? JobTitle { get; set; }

        [MaxLength(100)]
        public string? Website { get; set; }

        [MaxLength(50)]
        public string? Country { get; set; }

        [MaxLength(50)]
        public string? City { get; set; }

        [MaxLength(100)]
        public string? Location { get; set; }

        [MaxLength(10)]
        public string? TimeZone { get; set; }

        public bool IsEmailVerified { get; set; } = false;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? LastLoginAt { get; set; }

        // Subscription related fields
        public string? SubscriptionId { get; set; }

        public string? SubscriptionStatus { get; set; }

        public DateTime? SubscriptionStartDate { get; set; }

        public DateTime? SubscriptionEndDate { get; set; }

        public string? PlanType { get; set; } // Free, Basic, Premium, etc.

        // Supabase Auth ID
        [Required]
        public string SupabaseUserId { get; set; } = string.Empty;

        // Computed properties
        public string FullName => $"{FirstName} {LastName}";

        public bool IsSubscriptionActive =>
            SubscriptionStatus == "active" &&
            SubscriptionEndDate.HasValue &&
            SubscriptionEndDate.Value > DateTime.UtcNow;
    }
}
