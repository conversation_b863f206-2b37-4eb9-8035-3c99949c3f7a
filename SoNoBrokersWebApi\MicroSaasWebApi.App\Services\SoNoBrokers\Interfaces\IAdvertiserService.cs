using MicroSaasWebApi.Models.SoNoBrokers;

namespace MicroSaasWebApi.Services.SoNoBrokers.Interfaces
{
    public interface IAdvertiserService
    {
        Task<AdvertiserSearchResponse> GetAdvertisersAsync(AdvertiserSearchRequest request);
        Task<AdvertiserResponse?> GetAdvertiserByIdAsync(string id);
        Task<AdvertiserResponse?> GetAdvertiserByUserIdAsync(string userId);
        Task<AdvertiserResponse> CreateAdvertiserAsync(CreateAdvertiserRequest request, string userId);
        Task<AdvertiserResponse?> UpdateAdvertiserAsync(UpdateAdvertiserRequest request);
        Task<bool> DeleteAdvertiserAsync(string id);
        Task<bool> VerifyAdvertiserAsync(string id, bool isVerified);
        Task<bool> UpdateAdvertiserStatusAsync(string id, AdvertiserStatus status);
        Task<List<AdvertiserPlanFeatures>> GetAdvertiserPlansAsync();
    }
}
