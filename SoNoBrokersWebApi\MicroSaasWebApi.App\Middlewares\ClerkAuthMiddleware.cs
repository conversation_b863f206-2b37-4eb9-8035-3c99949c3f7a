using Microsoft.AspNetCore.Authorization;
using MicroSaasWebApi.Services.Auth.Interfaces;
using System.Security.Claims;
using System.Text.Json;

namespace MicroSaasWebApi.Middlewares
{
    /// <summary>
    /// Clerk Authentication Middleware
    /// Validates Clerk tokens for all API requests except public endpoints
    /// Supports role-based authorization (<PERSON><PERSON>, Buyer, Seller)
    /// </summary>
    public class ClerkAuthMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ClerkAuthMiddleware> _logger;

        // Public endpoints that don't require authentication
        private readonly HashSet<string> _publicEndpoints = new(StringComparer.OrdinalIgnoreCase)
        {
            "/api/clerk-auth/login",
            "/api/clerk-auth/register",
            "/api/clerk-auth/logout",
            "/api/clerk-auth/refresh-token",
            "/health",
            "/health/ready",
            "/health/live",
            "/api/health/ping",
            "/swagger",
            "/openapi",
            "/openapi.json",
            "/scalar",
            "/scalar/v1",
            "/api/sonobrokers/test/ping",
            "/api/sonobrokers/test/health",
            "/api/sonobrokers/geo",
            "/api/sonobrokers/communication/geo",
            "/api/sonobrokers/communication/enums",
            "/api/sonobrokers/auth/generate-test-token"
        };

        public ClerkAuthMiddleware(RequestDelegate next, ILogger<ClerkAuthMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IClerkAuthService clerkAuthService)
        {
            try
            {
                // Skip authentication for public endpoints
                if (IsPublicEndpoint(context.Request.Path))
                {
                    _logger.LogDebug("Skipping authentication for public endpoint: {Path}", context.Request.Path);
                    await _next(context);
                    return;
                }

                // Skip authentication for endpoints with [AllowAnonymous] attribute
                var endpoint = context.GetEndpoint();
                if (endpoint?.Metadata?.GetMetadata<IAllowAnonymous>() != null)
                {
                    _logger.LogDebug("Skipping authentication for [AllowAnonymous] endpoint: {Path}", context.Request.Path);
                    await _next(context);
                    return;
                }

                // Extract token from Authorization header
                var token = ExtractTokenFromHeader(context.Request);
                if (string.IsNullOrEmpty(token))
                {
                    _logger.LogWarning("No authorization token found for protected endpoint: {Path}", context.Request.Path);
                    await WriteUnauthorizedResponse(context, "Authorization token is required");
                    return;
                }

                // Validate Clerk token
                var principal = await clerkAuthService.ValidateClerkTokenAsync(token);
                if (principal == null)
                {
                    _logger.LogWarning("Invalid Clerk token for endpoint: {Path}", context.Request.Path);
                    await WriteUnauthorizedResponse(context, "Invalid or expired token");
                    return;
                }

                // Set the user principal for the request
                context.User = principal;

                // Check role-based authorization if required
                var authorizeAttribute = endpoint?.Metadata?.GetMetadata<AuthorizeAttribute>();
                if (authorizeAttribute != null && !string.IsNullOrEmpty(authorizeAttribute.Roles))
                {
                    var requiredRoles = authorizeAttribute.Roles.Split(',').Select(r => r.Trim()).ToArray();
                    var userRoles = principal.FindAll(ClaimTypes.Role).Select(c => c.Value).ToArray();

                    if (!requiredRoles.Any(role => userRoles.Contains(role)))
                    {
                        _logger.LogWarning("User {UserId} does not have required roles {RequiredRoles} for endpoint: {Path}",
                            principal.FindFirst(ClaimTypes.NameIdentifier)?.Value,
                            string.Join(", ", requiredRoles),
                            context.Request.Path);
                        await WriteForbiddenResponse(context, $"Access denied. Required roles: {string.Join(", ", requiredRoles)}");
                        return;
                    }
                }

                _logger.LogDebug("Authentication successful for user: {UserId} at endpoint: {Path}",
                    principal.FindFirst(ClaimTypes.NameIdentifier)?.Value,
                    context.Request.Path);

                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ClerkAuthMiddleware for path: {Path}", context.Request.Path);
                await WriteInternalServerErrorResponse(context, "Authentication error occurred");
            }
        }

        private bool IsPublicEndpoint(PathString path)
        {
            var pathValue = path.Value?.ToLowerInvariant() ?? string.Empty;

            // Check exact matches
            if (_publicEndpoints.Contains(pathValue))
                return true;

            // Check if path starts with any public endpoint (for swagger UI, etc.)
            return _publicEndpoints.Any(endpoint => pathValue.StartsWith(endpoint.ToLowerInvariant()));
        }

        private string? ExtractTokenFromHeader(HttpRequest request)
        {
            var authHeader = request.Headers.Authorization.FirstOrDefault();
            if (string.IsNullOrEmpty(authHeader))
                return null;

            // Support both "Bearer token" and "token" formats
            if (authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            {
                return authHeader.Substring("Bearer ".Length).Trim();
            }

            return authHeader.Trim();
        }

        private async Task WriteUnauthorizedResponse(HttpContext context, string message)
        {
            context.Response.StatusCode = 401;
            context.Response.ContentType = "application/json";

            var response = new
            {
                success = false,
                message = message,
                statusCode = 401,
                timestamp = DateTime.UtcNow
            };

            await context.Response.WriteAsync(JsonSerializer.Serialize(response));
        }

        private async Task WriteForbiddenResponse(HttpContext context, string message)
        {
            context.Response.StatusCode = 403;
            context.Response.ContentType = "application/json";

            var response = new
            {
                success = false,
                message = message,
                statusCode = 403,
                timestamp = DateTime.UtcNow
            };

            await context.Response.WriteAsync(JsonSerializer.Serialize(response));
        }

        private async Task WriteInternalServerErrorResponse(HttpContext context, string message)
        {
            context.Response.StatusCode = 500;
            context.Response.ContentType = "application/json";

            var response = new
            {
                success = false,
                message = message,
                statusCode = 500,
                timestamp = DateTime.UtcNow
            };

            await context.Response.WriteAsync(JsonSerializer.Serialize(response));
        }
    }

    /// <summary>
    /// Extension method to register ClerkAuthMiddleware
    /// </summary>
    public static class ClerkAuthMiddlewareExtensions
    {
        public static IApplicationBuilder UseClerkAuthentication(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<ClerkAuthMiddleware>();
        }
    }
}
