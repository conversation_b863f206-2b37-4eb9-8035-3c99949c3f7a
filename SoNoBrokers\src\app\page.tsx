import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

export default function HomePage() {
  return (
    <main>
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Welcome to SoNo Brokers
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Find your perfect home or sell your property commission-free across Canada, United States, and UAE.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/ca">
                🇨🇦 Canada
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/us">
                🇺🇸 United States
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/uae">
                🇦🇪 UAE
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Why Choose SoNo Brokers?</h2>
            <p className="text-xl text-muted-foreground">
              The modern way to buy and sell real estate
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>Commission-Free</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Save thousands by avoiding traditional realtor commissions
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Direct Connection</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Connect directly with buyers and sellers
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Professional Services</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Access to lawyers, inspectors, and other professionals
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </main>
  );
}
