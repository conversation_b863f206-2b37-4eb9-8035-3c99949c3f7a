using FluentAssertions;
using Microsoft.Extensions.Logging;
using MicroSaasWebApi.Config.Database;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers;
using MicroSaasWebApi.Tests.Common;
using Moq;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Services
{
    public class AdvertiserServiceTests : TestBase
    {
        private readonly Mock<DapperDbContext> _mockDbContext;
        private readonly Mock<ILogger<AdvertiserService>> _mockLogger;
        private readonly AdvertiserService _advertiserService;

        public AdvertiserServiceTests(ITestOutputHelper output) : base(output)
        {
            _mockDbContext = new Mock<DapperDbContext>();
            _mockLogger = new Mock<ILogger<AdvertiserService>>();
            _advertiserService = new AdvertiserService(_mockDbContext.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task GetAdvertisersAsync_WithValidRequest_ReturnsAdvertisers()
        {
            // Arrange
            var request = new AdvertiserSearchRequest
            {
                Page = 1,
                Limit = 10,
                ServiceType = ServiceType.photographer
            };

            var advertisers = TestDataBuilders.Advertisers.ValidAdvertiser.Generate(5);
            var totalCount = 15;

            _mockDbContext.Setup(x => x.QueryAsync<Advertiser>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(advertisers);

            _mockDbContext.Setup(x => x.QuerySingleAsync<int>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(totalCount);

            // Act
            var result = await _advertiserService.GetAdvertisersAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Advertisers.Should().HaveCount(5);
            result.Total.Should().Be(totalCount);
            result.Page.Should().Be(1);
            result.TotalPages.Should().Be(2);
            result.HasMore.Should().BeTrue();
        }

        [Fact]
        public async Task GetAdvertiserByIdAsync_WithValidId_ReturnsAdvertiser()
        {
            // Arrange
            var advertiserId = TestHelpers.GenerateTestId("adv");
            var advertiser = TestDataBuilders.Advertisers.ValidAdvertiser.Generate();
            advertiser.Id = advertiserId;

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<Advertiser>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(advertiser);

            // Act
            var result = await _advertiserService.GetAdvertiserByIdAsync(advertiserId);

            // Assert
            result.Should().NotBeNull();
            result!.Id.Should().Be(advertiserId);
            result.BusinessName.Should().Be(advertiser.BusinessName);
        }

        [Fact]
        public async Task GetAdvertiserByIdAsync_WithInvalidId_ReturnsNull()
        {
            // Arrange
            var advertiserId = "non-existent-id";

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<Advertiser>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync((Advertiser?)null);

            // Act
            var result = await _advertiserService.GetAdvertiserByIdAsync(advertiserId);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task CreateAdvertiserAsync_WithValidRequest_ReturnsCreatedAdvertiser()
        {
            // Arrange
            var userId = TestHelpers.GenerateTestId("user");
            var request = TestDataBuilders.Advertisers.CreateAdvertiserRequest.Generate();
            var createdAdvertiser = TestDataBuilders.Advertisers.ValidAdvertiser.Generate();

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<Advertiser>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(createdAdvertiser);

            // Act
            var result = await _advertiserService.CreateAdvertiserAsync(request, userId);

            // Assert
            result.Should().NotBeNull();
            result.BusinessName.Should().Be(createdAdvertiser.BusinessName);
            result.Email.Should().Be(createdAdvertiser.Email);
            result.ServiceType.Should().Be(createdAdvertiser.ServiceType);
        }

        [Fact]
        public async Task UpdateAdvertiserAsync_WithValidRequest_ReturnsUpdatedAdvertiser()
        {
            // Arrange
            var advertiserId = TestHelpers.GenerateTestId("adv");
            var request = new UpdateAdvertiserRequest
            {
                Id = advertiserId,
                BusinessName = "Updated Business Name",
                Email = "<EMAIL>"
            };

            var updatedAdvertiser = TestDataBuilders.Advertisers.ValidAdvertiser.Generate();
            updatedAdvertiser.Id = advertiserId;
            updatedAdvertiser.BusinessName = request.BusinessName;
            updatedAdvertiser.Email = request.Email!;

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<Advertiser>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(updatedAdvertiser);

            // Act
            var result = await _advertiserService.UpdateAdvertiserAsync(request);

            // Assert
            result.Should().NotBeNull();
            result!.Id.Should().Be(advertiserId);
            result.BusinessName.Should().Be(request.BusinessName);
            result.Email.Should().Be(request.Email);
        }

        [Fact]
        public async Task DeleteAdvertiserAsync_WithValidId_ReturnsTrue()
        {
            // Arrange
            var advertiserId = TestHelpers.GenerateTestId("adv");

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            // Act
            var result = await _advertiserService.DeleteAdvertiserAsync(advertiserId);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task DeleteAdvertiserAsync_WithInvalidId_ReturnsFalse()
        {
            // Arrange
            var advertiserId = "non-existent-id";

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(0);

            // Act
            var result = await _advertiserService.DeleteAdvertiserAsync(advertiserId);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task VerifyAdvertiserAsync_WithValidId_ReturnsTrue()
        {
            // Arrange
            var advertiserId = TestHelpers.GenerateTestId("adv");
            var isVerified = true;

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            // Act
            var result = await _advertiserService.VerifyAdvertiserAsync(advertiserId, isVerified);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task UpdateAdvertiserStatusAsync_WithValidId_ReturnsTrue()
        {
            // Arrange
            var advertiserId = TestHelpers.GenerateTestId("adv");
            var status = AdvertiserStatus.suspended;

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            // Act
            var result = await _advertiserService.UpdateAdvertiserStatusAsync(advertiserId, status);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task GetAdvertiserPlansAsync_ReturnsValidPlans()
        {
            // Act
            var result = await _advertiserService.GetAdvertiserPlansAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.Should().Contain(p => p.Id == AdvertiserPlan.basic);
            result.Should().Contain(p => p.Id == AdvertiserPlan.premium);
            
            var premiumPlan = result.First(p => p.Id == AdvertiserPlan.premium);
            premiumPlan.Popular.Should().BeTrue();
        }

        [Theory]
        [InlineData(ServiceType.photographer, "Toronto")]
        [InlineData(ServiceType.lawyer, "Vancouver")]
        [InlineData(ServiceType.inspector, null)]
        public async Task GetAdvertisersAsync_WithDifferentFilters_ReturnsFilteredResults(ServiceType? serviceType, string? location)
        {
            // Arrange
            var request = new AdvertiserSearchRequest
            {
                Page = 1,
                Limit = 10,
                ServiceType = serviceType,
                Location = location
            };

            var advertisers = TestDataBuilders.Advertisers.ValidAdvertiser.Generate(3);
            
            _mockDbContext.Setup(x => x.QueryAsync<Advertiser>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(advertisers);

            _mockDbContext.Setup(x => x.QuerySingleAsync<int>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(3);

            // Act
            var result = await _advertiserService.GetAdvertisersAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Advertisers.Should().HaveCount(3);
            
            // Verify that the correct SQL parameters were used
            _mockDbContext.Verify(x => x.QueryAsync<Advertiser>(
                It.Is<string>(sql => sql.Contains("WHERE 1=1")),
                It.IsAny<object>()), Times.Once);
        }

        [Fact]
        public async Task GetAdvertiserByUserIdAsync_WithValidUserId_ReturnsAdvertiser()
        {
            // Arrange
            var userId = TestHelpers.GenerateTestId("user");
            var advertiser = TestDataBuilders.Advertisers.ValidAdvertiser.Generate();
            advertiser.UserId = userId;

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<Advertiser>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(advertiser);

            // Act
            var result = await _advertiserService.GetAdvertiserByUserIdAsync(userId);

            // Assert
            result.Should().NotBeNull();
            result!.UserId.Should().Be(userId);
        }
    }
}
