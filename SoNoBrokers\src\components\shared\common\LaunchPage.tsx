'use client';

import { motion } from "motion/react";
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useState } from 'react';
import {
  Home,
  Users,
  Globe,
  Sparkles,
  CheckCircle,
  Mail,
  MapPin,
  DollarSign,
  Shield,
  Clock,
  Star
} from 'lucide-react';

export function LaunchPage() {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Add email to waiting list
    try {
      const response = await fetch('/api/waiting-list', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      });

      if (response.ok) {
        setIsSubmitted(true);
        setEmail('');
      }
    } catch (error) {
      console.error('Failed to join waiting list:', error);
    }
  };

  const features = [
    {
      icon: DollarSign,
      title: "Zero Commission Fees",
      description: "Save thousands by eliminating realtor commissions entirely. Property owners keep 100% of their sale proceeds with transparent, flat-rate listing fees only."
    },
    {
      icon: Users,
      title: "Direct Owner-to-Buyer Marketplace",
      description: "Property owners list directly and communicate with genuine buyers. No MLS, no realtor interference, no inflated prices - just honest, direct transactions."
    },
    {
      icon: Shield,
      title: "Vetted Professional Network",
      description: "Access our curated network of lawyers, home inspectors, photographers, appraisers, and contractors - all verified and rated by the community."
    },
    {
      icon: Globe,
      title: "Multi-Country FSBO Platform",
      description: "The first international for-sale-by-owner platform covering Canada, United States, and UAE with local legal compliance and market expertise."
    },
    {
      icon: Sparkles,
      title: "AI-Enhanced Listings",
      description: "Smart property descriptions, automated market valuations, pricing recommendations, and photo enhancement tools to make your listing stand out."
    },
    {
      icon: Clock,
      title: "Complete Property Management",
      description: "List properties, schedule viewings, manage inquiries, track offers, and handle documentation - all in one comprehensive platform available 24/7."
    }
  ];

  const countries = [
    {
      code: 'CA',
      name: 'Canada',
      flag: '🇨🇦',
      description: 'Direct owner-to-buyer marketplace across all provinces - no MLS, no realtors required',
      features: ['No MLS Listings', 'Legal Support Network', 'Bilingual Platform']
    },
    {
      code: 'US',
      name: 'United States',
      flag: '🇺🇸',
      description: 'Revolutionary FSBO platform in major metropolitan areas - bypass traditional real estate',
      features: ['Non-MLS Alternative', 'State Compliance', 'Local Professional Network']
    },
    {
      code: 'UAE',
      name: 'United Arab Emirates',
      flag: '🇦🇪',
      description: 'First direct property marketplace in the Middle East - no agent commissions',
      features: ['RERA Compliant', 'Arabic Support', 'Investment Property Focus']
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Badge variant="secondary" className="mb-6 text-sm px-4 py-2">
              🚀 Launching Soon
            </Badge>

            <h1 className="text-4xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              SoNo Brokers
            </h1>

            <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
              The future of real estate is here. Supporting
              <span className="font-semibold text-foreground"> Canada, United States, and UAE</span>.
            </p>

            <div className="flex flex-wrap justify-center gap-4 mb-12">
              {countries.map((country) => (
                <motion.div
                  key={country.code}
                  whileHover={{ scale: 1.05 }}
                  className="flex items-center space-x-2 bg-card border rounded-full px-4 py-2"
                >
                  <span className="text-2xl">{country.flag}</span>
                  <span className="font-medium">{country.name}</span>
                </motion.div>
              ))}
            </div>

            {/* Email Signup */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="max-w-md mx-auto"
            >
              {!isSubmitted ? (
                <form onSubmit={handleSubmit} className="flex gap-2">
                  <Input
                    type="email"
                    placeholder="Enter your email for early access"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="flex-1"
                  />
                  <Button type="submit" size="lg">
                    <Mail className="mr-2 h-4 w-4" />
                    Join Waitlist
                  </Button>
                </form>
              ) : (
                <div className="flex items-center justify-center space-x-2 text-green-600">
                  <CheckCircle className="h-5 w-5" />
                  <span className="font-medium">Thanks! We'll notify you when we launch.</span>
                </div>
              )}
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* What We Do Section */}
      <section className="py-20 bg-primary/5">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              What SoNo Brokers Does
            </h2>
            <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
              SoNo Brokers is a <span className="font-semibold text-foreground">for-sale-by-owner (FSBO) marketplace</span> that eliminates the traditional real estate industry middleman.
              Property owners list directly on our platform and connect with genuine buyers - <span className="font-semibold text-foreground">no MLS listings, no realtor commissions, no inflated prices</span>.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="bg-red-100 dark:bg-red-900/20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🚫</span>
              </div>
              <h3 className="text-xl font-bold mb-3">No MLS Listings</h3>
              <p className="text-muted-foreground">
                We don't use or integrate with MLS systems. Our platform is exclusively for direct owner-to-buyer transactions.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="bg-green-100 dark:bg-green-900/20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🏠</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Direct Property Sales</h3>
              <p className="text-muted-foreground">
                Property owners maintain full control of their sale, pricing, and negotiations without realtor interference.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="bg-blue-100 dark:bg-blue-900/20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💰</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Maximum Savings</h3>
              <p className="text-muted-foreground">
                Eliminate 5-6% realtor commissions and keep more money in your pocket with transparent, flat-rate fees.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              Why Choose SoNo Brokers?
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              We're revolutionizing real estate with technology, transparency, and trust.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <feature.icon className="h-12 w-12 text-primary mb-4" />
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Countries Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              Global Real Estate Platform
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Launching simultaneously across three major markets with localized expertise.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {countries.map((country, index) => (
              <motion.div
                key={country.code}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
              >
                <Card className="h-full text-center hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="text-6xl mb-4">{country.flag}</div>
                    <CardTitle className="text-2xl">{country.name}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-muted-foreground">
                      {country.description}
                    </p>
                    <div className="space-y-2">
                      {country.features.map((feature) => (
                        <div key={feature} className="flex items-center justify-center space-x-2">
                          <Star className="h-4 w-4 text-primary" />
                          <span className="text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-primary/10 to-primary/5">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              Ready to Revolutionize Real Estate?
            </h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
              Join the movement to eliminate realtor commissions and take control of your property transactions.
              Be among the first to experience <span className="font-semibold text-foreground">true for-sale-by-owner freedom</span> when we launch in 2025.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="flex items-center space-x-2 text-muted-foreground">
                <MapPin className="h-5 w-5" />
                <span>Available in Canada, US & UAE</span>
              </div>
              <div className="flex items-center space-x-2 text-muted-foreground">
                <Clock className="h-5 w-5" />
                <span>Launching 2025</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
