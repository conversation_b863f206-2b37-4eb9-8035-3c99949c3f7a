using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MicroSaasWebApi.Middlewares;
using MicroSaasWebApi.Services.Auth.Interfaces;
using MicroSaasWebApi.Tests.Common;
using Moq;
using System.Security.Claims;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Authentication
{
    public class ClerkMiddlewareTests : TestBase
    {
        private readonly Mock<IClerkAuthService> _mockClerkAuthService;
        private readonly Mock<ILogger<ClerkAuthMiddleware>> _mockLogger;
        private readonly Mock<RequestDelegate> _mockNext;
        private readonly ClerkAuthMiddleware _middleware;

        public ClerkMiddlewareTests(ITestOutputHelper output) : base(output)
        {
            _mockClerkAuthService = new Mock<IClerkAuthService>();
            _mockLogger = new Mock<ILogger<ClerkAuthMiddleware>>();
            _mockNext = new Mock<RequestDelegate>();
            
            _middleware = new ClerkAuthMiddleware(_mockNext.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task InvokeAsync_WithPublicEndpoint_CallsNext()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/api/clerk-auth/login";
            context.Request.Method = "POST";

            // Act
            await _middleware.InvokeAsync(context, _mockClerkAuthService.Object);

            // Assert
            _mockNext.Verify(x => x(context), Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_WithHealthEndpoint_CallsNext()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/health";
            context.Request.Method = "GET";

            // Act
            await _middleware.InvokeAsync(context, _mockClerkAuthService.Object);

            // Assert
            _mockNext.Verify(x => x(context), Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_WithSwaggerEndpoint_CallsNext()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/swagger";
            context.Request.Method = "GET";

            // Act
            await _middleware.InvokeAsync(context, _mockClerkAuthService.Object);

            // Assert
            _mockNext.Verify(x => x(context), Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_WithProtectedEndpointNoToken_ReturnsUnauthorized()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/api/sonobrokers/advertisers/me";
            context.Request.Method = "GET";
            context.Response.Body = new MemoryStream();

            // Act
            await _middleware.InvokeAsync(context, _mockClerkAuthService.Object);

            // Assert
            context.Response.StatusCode.Should().Be(401);
            _mockNext.Verify(x => x(context), Times.Never);
        }

        [Fact]
        public async Task InvokeAsync_WithValidToken_CallsNext()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/api/sonobrokers/advertisers/me";
            context.Request.Method = "GET";
            context.Request.Headers.Authorization = "Bearer valid-token";

            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, "user_123"),
                new(ClaimTypes.Email, "<EMAIL>")
            };
            var identity = new ClaimsIdentity(claims, "Test");
            var principal = new ClaimsPrincipal(identity);

            _mockClerkAuthService.Setup(x => x.ValidateClerkTokenAsync("valid-token"))
                .ReturnsAsync(principal);

            // Act
            await _middleware.InvokeAsync(context, _mockClerkAuthService.Object);

            // Assert
            context.User.Should().Be(principal);
            _mockNext.Verify(x => x(context), Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_WithInvalidToken_ReturnsUnauthorized()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/api/sonobrokers/advertisers/me";
            context.Request.Method = "GET";
            context.Request.Headers.Authorization = "Bearer invalid-token";
            context.Response.Body = new MemoryStream();

            _mockClerkAuthService.Setup(x => x.ValidateClerkTokenAsync("invalid-token"))
                .ReturnsAsync((ClaimsPrincipal?)null);

            // Act
            await _middleware.InvokeAsync(context, _mockClerkAuthService.Object);

            // Assert
            context.Response.StatusCode.Should().Be(401);
            _mockNext.Verify(x => x(context), Times.Never);
        }

        [Fact]
        public async Task InvokeAsync_WithExpiredToken_ReturnsUnauthorized()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/api/sonobrokers/projects";
            context.Request.Method = "GET";
            context.Request.Headers.Authorization = "Bearer expired-token";
            context.Response.Body = new MemoryStream();

            _mockClerkAuthService.Setup(x => x.ValidateClerkTokenAsync("expired-token"))
                .ReturnsAsync((ClaimsPrincipal?)null);

            // Act
            await _middleware.InvokeAsync(context, _mockClerkAuthService.Object);

            // Assert
            context.Response.StatusCode.Should().Be(401);
            _mockNext.Verify(x => x(context), Times.Never);
        }

        [Theory]
        [InlineData("Basic valid-token")]
        [InlineData("Token valid-token")]
        [InlineData("")]
        [InlineData("Bearer")]
        public async Task InvokeAsync_WithInvalidAuthScheme_ReturnsUnauthorized(string authHeader)
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/api/sonobrokers/advertisers";
            context.Request.Method = "POST";
            context.Request.Headers.Authorization = authHeader;
            context.Response.Body = new MemoryStream();

            // Act
            await _middleware.InvokeAsync(context, _mockClerkAuthService.Object);

            // Assert
            context.Response.StatusCode.Should().Be(401);
            _mockNext.Verify(x => x(context), Times.Never);
        }

        [Fact]
        public async Task InvokeAsync_WithValidTokenButServiceException_ReturnsUnauthorized()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/api/sonobrokers/advertisers/me";
            context.Request.Method = "GET";
            context.Request.Headers.Authorization = "Bearer valid-token";
            context.Response.Body = new MemoryStream();

            _mockClerkAuthService.Setup(x => x.ValidateClerkTokenAsync("valid-token"))
                .ThrowsAsync(new Exception("Service unavailable"));

            // Act
            await _middleware.InvokeAsync(context, _mockClerkAuthService.Object);

            // Assert
            context.Response.StatusCode.Should().Be(401);
            _mockNext.Verify(x => x(context), Times.Never);
        }

        [Theory]
        [InlineData("/api/clerk-auth/login")]
        [InlineData("/api/clerk-auth/register")]
        [InlineData("/api/clerk-auth/logout")]
        [InlineData("/api/clerk-auth/refresh-token")]
        [InlineData("/health")]
        [InlineData("/health/ready")]
        [InlineData("/health/live")]
        [InlineData("/swagger")]
        [InlineData("/openapi")]
        [InlineData("/openapi.json")]
        [InlineData("/scalar")]
        [InlineData("/scalar/v1")]
        public async Task InvokeAsync_WithPublicEndpoints_SkipsAuthentication(string path)
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = path;
            context.Request.Method = "GET";

            // Act
            await _middleware.InvokeAsync(context, _mockClerkAuthService.Object);

            // Assert
            _mockNext.Verify(x => x(context), Times.Once);
            _mockClerkAuthService.Verify(x => x.ValidateClerkTokenAsync(It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task InvokeAsync_WithCaseInsensitivePublicEndpoint_SkipsAuthentication()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/API/CLERK-AUTH/LOGIN";
            context.Request.Method = "POST";

            // Act
            await _middleware.InvokeAsync(context, _mockClerkAuthService.Object);

            // Assert
            _mockNext.Verify(x => x(context), Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_WithUserClaims_SetsContextUser()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/api/sonobrokers/advertisers/me";
            context.Request.Method = "GET";
            context.Request.Headers.Authorization = "Bearer valid-token";

            var expectedUserId = "user_123456";
            var expectedEmail = "<EMAIL>";
            var expectedRole = "USER";

            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, expectedUserId),
                new(ClaimTypes.Email, expectedEmail),
                new(ClaimTypes.Role, expectedRole),
                new("sub", expectedUserId)
            };
            var identity = new ClaimsIdentity(claims, "Clerk");
            var principal = new ClaimsPrincipal(identity);

            _mockClerkAuthService.Setup(x => x.ValidateClerkTokenAsync("valid-token"))
                .ReturnsAsync(principal);

            // Act
            await _middleware.InvokeAsync(context, _mockClerkAuthService.Object);

            // Assert
            context.User.Should().NotBeNull();
            context.User.Identity!.IsAuthenticated.Should().BeTrue();
            context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value.Should().Be(expectedUserId);
            context.User.FindFirst(ClaimTypes.Email)?.Value.Should().Be(expectedEmail);
            context.User.FindFirst(ClaimTypes.Role)?.Value.Should().Be(expectedRole);
            _mockNext.Verify(x => x(context), Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_LogsAuthenticationAttempts()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/api/sonobrokers/advertisers/me";
            context.Request.Method = "GET";
            context.Request.Headers.Authorization = "Bearer test-token";
            context.Response.Body = new MemoryStream();

            _mockClerkAuthService.Setup(x => x.ValidateClerkTokenAsync("test-token"))
                .ReturnsAsync((ClaimsPrincipal?)null);

            // Act
            await _middleware.InvokeAsync(context, _mockClerkAuthService.Object);

            // Assert
            // Verify that warning was logged for invalid token
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Invalid Clerk token")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }
    }
}
