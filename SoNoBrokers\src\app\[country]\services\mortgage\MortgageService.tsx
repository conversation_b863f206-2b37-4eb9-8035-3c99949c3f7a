import React from 'react'
import { ServiceLayout } from '@/components/shared/services/ServiceLayout'

interface MortgageServiceProps {
  userType: 'buyer' | 'seller'
  isSignedIn: boolean
  country: string
}

// Mock data - in real implementation, this would come from your database and Google Places API
const getMockProviders = (country: string) => {
  const baseProviders = [
    {
      id: '1',
      name: '<PERSON>',
      businessName: 'Johnson Mortgage Solutions',
      serviceType: 'Licensed Mortgage Broker',
      location: country === 'CA' ? 'Toronto, ON' : 'New York, NY',
      distance: '1.8 km',
      rating: 4.9,
      reviewCount: 298,
      price: 'Best Rates',
      specialties: ['First-Time Buyers', 'Investment Properties', 'Refinancing', 'Pre-Approval'],
      verified: true,
      isAdvertiser: true,
      isPremium: true,
      image: '/api/placeholder/80/80',
      phone: '******-0161',
      email: '<EMAIL>',
      website: 'https://johnsonmortgage.com',
      description: 'Licensed mortgage broker with access to 50+ lenders. Competitive rates and fast approval process.',
      coordinates: country === 'CA' ? { lat: 43.6532, lng: -79.3832 } : { lat: 40.7128, lng: -74.0060 }
    },
    {
      id: '2',
      name: 'Emily Davis',
      businessName: 'Davis Home Financing',
      serviceType: 'Mortgage Specialist',
      location: country === 'CA' ? 'Vancouver, BC' : 'Los Angeles, CA',
      distance: '3.2 km',
      rating: 4.8,
      reviewCount: 234,
      price: 'Low Rates',
      specialties: ['Conventional Loans', 'FHA Loans', 'VA Loans', 'Jumbo Mortgages'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/80/80',
      phone: '******-0162',
      description: 'Experienced mortgage specialist helping clients secure the best financing options for their home purchase.',
      coordinates: country === 'CA' ? { lat: 49.2827, lng: -123.1207 } : { lat: 34.0522, lng: -118.2437 }
    },
    {
      id: '3',
      name: 'Alex Thompson',
      businessName: 'Thompson Lending Group',
      serviceType: 'Mortgage Advisor',
      location: country === 'CA' ? 'Calgary, AB' : 'Chicago, IL',
      distance: '4.5 km',
      rating: 4.7,
      reviewCount: 187,
      price: 'Competitive',
      specialties: ['Self-Employed', 'Bad Credit', 'Quick Approval', 'Construction Loans'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/80/80',
      description: 'Specialized in difficult-to-place mortgages. Creative solutions for unique financial situations.',
      coordinates: country === 'CA' ? { lat: 51.0447, lng: -114.0719 } : { lat: 41.8781, lng: -87.6298 }
    }
  ]

  // Add Google API providers (non-registered)
  const googleProviders = [
    {
      id: 'g1',
      name: 'National Bank Mortgage',
      businessName: 'National Bank Mortgage',
      serviceType: 'Bank Lender',
      location: country === 'CA' ? 'Mississauga, ON' : 'Brooklyn, NY',
      distance: '8.1 km',
      rating: 4.4,
      reviewCount: 156,
      price: 'Standard Rates',
      specialties: ['Bank Mortgages', 'Online Application'],
      verified: false,
      isAdvertiser: false,
      isPremium: false,
      description: 'Traditional bank mortgage lending with standard rates and terms.',
      coordinates: country === 'CA' ? { lat: 43.5890, lng: -79.6441 } : { lat: 40.6782, lng: -73.9442 }
    },
    {
      id: 'g2',
      name: 'Quick Loan Express',
      businessName: 'Quick Loan Express',
      serviceType: 'Online Lender',
      location: country === 'CA' ? 'Markham, ON' : 'Queens, NY',
      distance: '12.7 km',
      rating: 4.1,
      reviewCount: 89,
      price: 'Fast Approval',
      specialties: ['Online Process', 'Quick Decisions'],
      verified: false,
      isAdvertiser: false,
      isPremium: false,
      description: 'Fast online mortgage applications with quick approval decisions.',
      coordinates: country === 'CA' ? { lat: 43.8561, lng: -79.3370 } : { lat: 40.7282, lng: -73.7949 }
    }
  ]

  return [...baseProviders, ...googleProviders]
}

export function MortgageService({
  userType,
  isSignedIn,
  country
}: MortgageServiceProps) {
  // Get providers data (this would be async in real implementation)
  const mockProviders = getMockProviders(country)

  // Sort by distance and premium status
  const providers = mockProviders.sort((a, b) => {
    if (a.isPremium && !b.isPremium) return -1
    if (!a.isPremium && b.isPremium) return 1
    if (a.isAdvertiser && !b.isAdvertiser) return -1
    if (!a.isAdvertiser && b.isAdvertiser) return 1
    return parseFloat(a.distance) - parseFloat(b.distance)
  })

  const serviceDescription = userType === 'buyer'
    ? 'Professional mortgage services to help you secure the best financing for your home purchase. Our licensed mortgage brokers and specialists have access to multiple lenders and can find competitive rates tailored to your financial situation.'
    : 'Mortgage and refinancing services for property owners and investors. Whether you\'re looking to refinance existing properties or secure financing for new investments, our mortgage professionals can help you optimize your financing strategy.'

  // Check environment variable for Google providers
  const showGoogleProviders = process.env.NEXT_PUBLIC_ENABLE_GOOGLE_PROVIDERS !== 'false'

  return (
    <ServiceLayout
      userType={userType}
      isSignedIn={isSignedIn}
      serviceTitle="Mortgage Services"
      serviceDescription={serviceDescription}
      country={country}
      providers={providers}
      showGoogleProviders={showGoogleProviders}
    />
  )
}
