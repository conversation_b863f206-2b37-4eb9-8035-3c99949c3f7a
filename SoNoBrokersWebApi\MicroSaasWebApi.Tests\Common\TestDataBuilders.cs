using Bogus;
using MicroSaasWebApi.Models.Auth;
using MicroSaasWebApi.Models.SoNoBrokers;

namespace MicroSaasWebApi.Tests.Common
{
    public static class TestDataBuilders
    {
        public static class Users
        {
            public static Faker<User> ValidUser => new Faker<User>()
                .RuleFor(u => u.Id, f => f.Random.Guid().ToString())
                .RuleFor(u => u.Email, f => f.Internet.Email())
                .RuleFor(u => u.FullName, f => f.Name.FullName())
                .RuleFor(u => u.FirstName, f => f.Name.FirstName())
                .RuleFor(u => u.LastName, f => f.Name.LastName())
                .RuleFor(u => u.Phone, f => f.Phone.PhoneNumber())
                .RuleFor(u => u.ClerkUserId, f => f.Random.AlphaNumeric(20))
                .RuleFor(u => u.Role, f => f.PickRandom<UserRole>())
                .RuleFor(u => u.UserType, f => f.PickRandom<SnbUserType>())
                .RuleFor(u => u.IsActive, f => true)
                .RuleFor(u => u.LoggedIn, f => false)
                .RuleFor(u => u.CreatedAt, f => f.Date.Recent(30))
                .RuleFor(u => u.UpdatedAt, f => f.Date.Recent(7))
                .RuleFor(u => u.LastLoginAt, f => f.Date.Recent(1));

            public static Faker<CreateUserRequest> CreateUserRequest => new Faker<CreateUserRequest>()
                .RuleFor(u => u.Email, f => f.Internet.Email())
                .RuleFor(u => u.FullName, f => f.Name.FullName())
                .RuleFor(u => u.FirstName, f => f.Name.FirstName())
                .RuleFor(u => u.LastName, f => f.Name.LastName())
                .RuleFor(u => u.Phone, f => f.Phone.PhoneNumber())
                .RuleFor(u => u.ClerkUserId, f => f.Random.AlphaNumeric(20))
                .RuleFor(u => u.Role, f => UserRole.USER)
                .RuleFor(u => u.UserType, f => f.PickRandom<SnbUserType>());
        }

        public static class Properties
        {
            public static Faker<Property> ValidProperty => new Faker<Property>()
                .RuleFor(p => p.Id, f => f.Random.Guid().ToString())
                .RuleFor(p => p.Title, f => f.Lorem.Sentence(3, 5))
                .RuleFor(p => p.Description, f => f.Lorem.Paragraph())
                .RuleFor(p => p.Price, f => f.Random.Decimal(200000, 2000000))
                .RuleFor(p => p.PropertyType, f => f.PickRandom("Detached House", "Townhouse", "Condominium", "Semi-Detached"))
                .RuleFor(p => p.Bedrooms, f => f.Random.Int(1, 6))
                .RuleFor(p => p.Bathrooms, f => f.Random.Decimal(1, 4))
                .RuleFor(p => p.Sqft, f => f.Random.Int(800, 4000))
                .RuleFor(p => p.SellerId, f => f.Random.Guid().ToString())
                .RuleFor(p => p.Status, f => PropertyStatus.active)
                .RuleFor(p => p.CreatedAt, f => f.Date.Recent(30))
                .RuleFor(p => p.UpdatedAt, f => f.Date.Recent(7));

            public static Faker<CreatePropertyRequest> CreatePropertyRequest => new Faker<CreatePropertyRequest>()
                .RuleFor(p => p.Title, f => f.Lorem.Sentence(3, 5))
                .RuleFor(p => p.Description, f => f.Lorem.Paragraph())
                .RuleFor(p => p.Price, f => f.Random.Decimal(200000, 2000000))
                .RuleFor(p => p.PropertyType, f => f.PickRandom("Detached House", "Townhouse", "Condominium"))
                .RuleFor(p => p.Bedrooms, f => f.Random.Int(1, 6))
                .RuleFor(p => p.Bathrooms, f => f.Random.Decimal(1, 4))
                .RuleFor(p => p.Sqft, f => f.Random.Int(800, 4000));
        }

        public static class Advertisers
        {
            public static Faker<Advertiser> ValidAdvertiser => new Faker<Advertiser>()
                .RuleFor(a => a.Id, f => f.Random.Guid().ToString())
                .RuleFor(a => a.UserId, f => f.Random.Guid().ToString())
                .RuleFor(a => a.BusinessName, f => f.Company.CompanyName())
                .RuleFor(a => a.ContactName, f => f.Name.FullName())
                .RuleFor(a => a.Email, f => f.Internet.Email())
                .RuleFor(a => a.Phone, f => f.Phone.PhoneNumber())
                .RuleFor(a => a.Website, f => f.Internet.Url())
                .RuleFor(a => a.Description, f => f.Lorem.Paragraph())
                .RuleFor(a => a.ServiceType, f => f.PickRandom<ServiceType>())
                .RuleFor(a => a.ServiceAreas, f => f.Make(3, () => f.Address.City()).ToArray())
                .RuleFor(a => a.LicenseNumber, f => f.Random.AlphaNumeric(10))
                .RuleFor(a => a.Plan, f => f.PickRandom<AdvertiserPlan>())
                .RuleFor(a => a.Status, f => AdvertiserStatus.active)
                .RuleFor(a => a.IsPremium, f => f.Random.Bool())
                .RuleFor(a => a.IsVerified, f => f.Random.Bool())
                .RuleFor(a => a.Images, f => f.Make(2, () => f.Internet.Url()).ToArray())
                .RuleFor(a => a.CreatedAt, f => f.Date.Recent(30))
                .RuleFor(a => a.UpdatedAt, f => f.Date.Recent(7));

            public static Faker<CreateAdvertiserRequest> CreateAdvertiserRequest => new Faker<CreateAdvertiserRequest>()
                .RuleFor(a => a.BusinessName, f => f.Company.CompanyName())
                .RuleFor(a => a.ContactName, f => f.Name.FullName())
                .RuleFor(a => a.Email, f => f.Internet.Email())
                .RuleFor(a => a.Phone, f => f.Phone.PhoneNumber())
                .RuleFor(a => a.Website, f => f.Internet.Url())
                .RuleFor(a => a.Description, f => f.Lorem.Paragraph())
                .RuleFor(a => a.ServiceType, f => f.PickRandom<ServiceType>())
                .RuleFor(a => a.ServiceAreas, f => f.Make(3, () => f.Address.City()).ToArray())
                .RuleFor(a => a.LicenseNumber, f => f.Random.AlphaNumeric(10))
                .RuleFor(a => a.Plan, f => f.PickRandom<AdvertiserPlan>())
                .RuleFor(a => a.Images, f => f.Make(2, () => f.Internet.Url()).ToArray());
        }

        public static class Projects
        {
            public static Faker<Project> ValidProject => new Faker<Project>()
                .RuleFor(p => p.Id, f => f.Random.Guid().ToString())
                .RuleFor(p => p.ConnectionId, f => f.Random.AlphaNumeric(20))
                .RuleFor(p => p.WebhookId, f => f.Random.AlphaNumeric(20))
                .RuleFor(p => p.ScenarioId, f => f.Random.AlphaNumeric(20))
                .RuleFor(p => p.UserClerkId, f => f.Random.AlphaNumeric(20))
                .RuleFor(p => p.WebhookLink, f => f.Internet.Url())
                .RuleFor(p => p.AssistantId, f => f.Random.AlphaNumeric(20))
                .RuleFor(p => p.Type, f => f.PickRandom("automation", "integration", "workflow"))
                .RuleFor(p => p.Status, f => f.PickRandom("active", "inactive", "pending"))
                .RuleFor(p => p.CreatedAt, f => f.Date.Recent(30))
                .RuleFor(p => p.UpdatedAt, f => f.Date.Recent(7));

            public static Faker<CreateProjectRequest> CreateProjectRequest => new Faker<CreateProjectRequest>()
                .RuleFor(p => p.ConnectionId, f => f.Random.AlphaNumeric(20))
                .RuleFor(p => p.WebhookId, f => f.Random.AlphaNumeric(20))
                .RuleFor(p => p.ScenarioId, f => f.Random.AlphaNumeric(20))
                .RuleFor(p => p.WebhookLink, f => f.Internet.Url())
                .RuleFor(p => p.AssistantId, f => f.Random.AlphaNumeric(20))
                .RuleFor(p => p.Type, f => f.PickRandom("automation", "integration", "workflow"))
                .RuleFor(p => p.Status, f => "active");
        }

        public static class Auth
        {
            public static Faker<LoginRequest> LoginRequest => new Faker<LoginRequest>()
                .RuleFor(l => l.Email, f => f.Internet.Email())
                .RuleFor(l => l.Password, f => f.Internet.Password(8, false, "", "Aa1!"));

            public static Faker<RegisterRequest> RegisterRequest => new Faker<RegisterRequest>()
                .RuleFor(r => r.Email, f => f.Internet.Email())
                .RuleFor(r => r.Password, f => f.Internet.Password(8, false, "", "Aa1!"))
                .RuleFor(r => r.FirstName, f => f.Name.FirstName())
                .RuleFor(r => r.LastName, f => f.Name.LastName());

            public static Faker<UserInfo> UserInfo => new Faker<UserInfo>()
                .RuleFor(u => u.Id, f => f.Random.Guid())
                .RuleFor(u => u.Email, f => f.Internet.Email())
                .RuleFor(u => u.FirstName, f => f.Name.FirstName())
                .RuleFor(u => u.LastName, f => f.Name.LastName())
                .RuleFor(u => u.IsEmailVerified, f => true)
                .RuleFor(u => u.ProfileImageUrl, f => f.Internet.Avatar());
        }

        public static class AI
        {
            public static Faker<AIPropertyImportRequest> PropertyImportRequest => new Faker<AIPropertyImportRequest>()
                .RuleFor(r => r.Address, f => f.Address.FullAddress());

            public static Faker<AIPropertyValuationRequest> PropertyValuationRequest => new Faker<AIPropertyValuationRequest>()
                .RuleFor(r => r.Address, f => f.Address.FullAddress())
                .RuleFor(r => r.Country, f => f.PickRandom("CA", "US", "UAE"));

            public static Faker<GenerateDescriptionRequest> GenerateDescriptionRequest => new Faker<GenerateDescriptionRequest>()
                .RuleFor(r => r.Prompt, f => f.Lorem.Sentence())
                .RuleFor(r => r.UserId, f => f.Random.Guid().ToString());
        }

        public static class Communication
        {
            public static Faker<ContactConciergeRequest> ContactConciergeRequest => new Faker<ContactConciergeRequest>()
                .RuleFor(r => r.Name, f => f.Name.FullName())
                .RuleFor(r => r.Email, f => f.Internet.Email())
                .RuleFor(r => r.Phone, f => f.Phone.PhoneNumber())
                .RuleFor(r => r.PropertyAddress, f => f.Address.FullAddress())
                .RuleFor(r => r.PropertyValue, f => f.Random.Decimal(200000, 2000000).ToString())
                .RuleFor(r => r.Timeline, f => f.PickRandom("1-3 months", "3-6 months", "6+ months"))
                .RuleFor(r => r.Requirements, f => f.Lorem.Paragraph())
                .RuleFor(r => r.Country, f => f.PickRandom("CA", "US", "UAE"));

            public static Faker<WaitingListRequest> WaitingListRequest => new Faker<WaitingListRequest>()
                .RuleFor(r => r.Email, f => f.Internet.Email());
        }
    }
}
