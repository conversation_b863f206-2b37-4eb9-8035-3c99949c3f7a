using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    public enum AccessType
    {
        qr_scan,
        online_access
    }

    [Table("OpenHouseAccess", Schema = "snb")]
    public class OpenHouseAccess
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string PropertyId { get; set; } = string.Empty;

        public string? BuyerId { get; set; }

        [Column(TypeName = "jsonb")]
        public string? BuyerInfo { get; set; }

        [Required]
        public AccessType AccessType { get; set; }

        public DateTime AccessedAt { get; set; } = DateTime.UtcNow;

        public string? IpAddress { get; set; }

        public string? UserAgent { get; set; }

        // Navigation properties
        [ForeignKey("PropertyId")]
        public virtual Property Property { get; set; } = null!;

        // Helper methods for JSON fields
        public T? GetBuyerInfoAs<T>() where T : class
        {
            if (string.IsNullOrEmpty(BuyerInfo)) return null;
            try
            {
                return JsonSerializer.Deserialize<T>(BuyerInfo);
            }
            catch
            {
                return null;
            }
        }

        public void SetBuyerInfo<T>(T buyerInfo) where T : class
        {
            BuyerInfo = buyerInfo != null ? JsonSerializer.Serialize(buyerInfo) : null;
        }
    }
}
