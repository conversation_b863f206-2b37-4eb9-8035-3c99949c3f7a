# Controllers Quick Reference Guide

## 🏗️ Architecture Overview

```
Controllers/
├── Core/
│   └── Stripe/
│       ├── StripePaymentsController.cs    # 💳 Payments & Subscriptions
│       └── StripeWebhookController.cs     # 🔄 Webhook Events
└── SoNoBrokers/
    ├── UsersController.cs                 # 👥 User Management
    ├── PropertiesController.cs            # 🏠 Property Listings
    ├── PropertyImagesController.cs        # 📸 Image Management
    ├── SearchFiltersController.cs         # 🔍 Search & Filters
    ├── BuyerListingsController.cs         # 🛒 Buyer Operations
    ├── WaitingListController.cs           # ⏳ Waiting Lists
    ├── SimplePropertiesController.cs      # 🏠 Simple Property Ops
    ├── SimpleUsersController.cs           # 👤 Simple User Ops
    ├── GenerateDescriptionController.cs   # 🤖 AI Descriptions
    └── UploadController.cs                # 📤 File Uploads
```

## 🚀 Quick Start Patterns

### Basic Controller Structure
```csharp
[Route("api/sonobrokers/[controller]")]
[ApiController]
public class YourController : ControllerBase
{
    private readonly DapperDbContext _dbContext;
    private readonly ILogger<YourController> _logger;
    
    public YourController(DapperDbContext dbContext, ILogger<YourController> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }
}
```

### Database Query Pattern
```csharp
[HttpGet("{id}")]
public async Task<ActionResult<ApiResponse<YourModel>>> Get(string id)
{
    try
    {
        const string sql = "SELECT * FROM snb.your_table WHERE id = @id";
        var result = await _dbContext.QueryFirstOrDefaultAsync<YourModel>(sql, new { id });
        
        if (result == null)
            return NotFound(ApiResponse<YourModel>.ErrorResult("Not found"));
            
        return Ok(ApiResponse<YourModel>.SuccessResult(result));
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error getting record: {Id}", id);
        return StatusCode(500, ApiResponse<YourModel>.ErrorResult("Internal error"));
    }
}
```

## 💳 Stripe Controllers

### StripePaymentsController Routes
```
POST   /api/stripe/payments/customers                    # Create customer
GET    /api/stripe/payments/customers/{id}               # Get customer
POST   /api/stripe/payments/payment-intents              # One-time payment
POST   /api/stripe/payments/subscriptions                # Create subscription
POST   /api/stripe/payments/sonobrokers/create-checkout  # SoNoBrokers checkout
POST   /api/stripe/payments/sonobrokers/create-portal    # Customer portal
GET    /api/stripe/payments/sonobrokers/subscription-status # Check status
```

### StripeWebhookController
```
POST   /api/stripe/webhooks                              # Handle all webhooks
```

**Supported Events:**
- `checkout.session.completed` → Create/update subscription
- `customer.subscription.updated` → Update subscription status
- `customer.subscription.deleted` → Cancel subscription
- `invoice.paid` → Activate subscription
- `invoice.payment_failed` → Mark as past due
- `payment_intent.succeeded` → Record successful payment
- `payment_intent.payment_failed` → Record failed payment

## 🏠 SoNoBrokers Controllers

### UsersController.cs
```
GET    /api/sonobrokers/users                           # List users
GET    /api/sonobrokers/users/{id}                      # Get user
POST   /api/sonobrokers/users                           # Create user
PUT    /api/sonobrokers/users/{id}                      # Update user
DELETE /api/sonobrokers/users/{id}                      # Delete user
GET    /api/sonobrokers/users/profile                   # Current user profile
```

### PropertiesController.cs
```
GET    /api/sonobrokers/properties                      # List properties
GET    /api/sonobrokers/properties/{id}                 # Get property
POST   /api/sonobrokers/properties                      # Create property
PUT    /api/sonobrokers/properties/{id}                 # Update property
DELETE /api/sonobrokers/properties/{id}                 # Delete property
GET    /api/sonobrokers/properties/search               # Search properties
```

### PropertyImagesController.cs
```
GET    /api/sonobrokers/property-images                 # List images
POST   /api/sonobrokers/property-images                 # Upload image
DELETE /api/sonobrokers/property-images/{id}            # Delete image
POST   /api/sonobrokers/property-images/bulk           # Bulk upload
```

### SearchFiltersController.cs
```
GET    /api/sonobrokers/search-filters                  # List filters
POST   /api/sonobrokers/search-filters                  # Create filter
PUT    /api/sonobrokers/search-filters/{id}             # Update filter
DELETE /api/sonobrokers/search-filters/{id}             # Delete filter
```

## 🗄️ Database Patterns

### Simple Query
```csharp
const string sql = "SELECT * FROM snb.table WHERE column = @value";
var result = await _dbContext.QueryAsync<Model>(sql, new { value });
```

### Complex Query with Joins
```csharp
const string sql = @"
    SELECT p.*, u.name as owner_name, COUNT(pi.id) as image_count
    FROM snb.properties p
    LEFT JOIN snb.users u ON p.owner_id = u.id
    LEFT JOIN snb.property_images pi ON p.id = pi.property_id
    WHERE p.status = @status
    GROUP BY p.id, u.name
    ORDER BY p.created_at DESC";
```

### Insert Operation
```csharp
const string sql = @"
    INSERT INTO snb.table (id, column1, column2, created_at)
    VALUES (@Id, @Column1, @Column2, @CreatedAt)";
    
await _dbContext.ExecuteAsync(sql, new {
    Id = Guid.NewGuid().ToString(),
    Column1 = value1,
    Column2 = value2,
    CreatedAt = DateTime.UtcNow
});
```

### Update Operation
```csharp
const string sql = @"
    UPDATE snb.table 
    SET column1 = @Column1, updated_at = @UpdatedAt
    WHERE id = @Id";
    
await _dbContext.ExecuteAsync(sql, new {
    Id = id,
    Column1 = newValue,
    UpdatedAt = DateTime.UtcNow
});
```

### Transaction Example
```csharp
using var transaction = await _dbContext.BeginTransactionAsync();
try
{
    await _dbContext.ExecuteAsync(sql1, params1, transaction);
    await _dbContext.ExecuteAsync(sql2, params2, transaction);
    await transaction.CommitAsync();
}
catch
{
    await transaction.RollbackAsync();
    throw;
}
```

## 📊 Database Schema Quick Reference

### Core Tables
```sql
snb.users                    # User profiles
snb.properties              # Property listings  
snb.property_images         # Property photos
snb.search_filters          # Saved searches
snb.subscriptions           # Stripe subscriptions
snb.payments                # One-time payments
snb.waiting_lists           # Property waiting lists
```

### Common Columns
```sql
id                          # Primary key (TEXT)
created_at                  # Creation timestamp
updated_at                  # Last update timestamp
user_id                     # Foreign key to users
```

## 🔐 Authentication & Authorization

### Clerk Integration
```csharp
[Authorize] // Requires authentication
public class YourController : ControllerBase
{
    private string GetCurrentUserId()
    {
        return User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    }
}
```

### Role-Based Access
```csharp
[Authorize(Roles = "Admin")]
[Authorize(Policy = "CanManageProperties")]
```

## 📝 API Response Format

### Success Response
```csharp
return Ok(ApiResponse<T>.SuccessResult(data, "Operation successful"));
```

### Error Response
```csharp
return BadRequest(ApiResponse<T>.ErrorResult("Validation failed", errors));
return NotFound(ApiResponse<T>.ErrorResult("Resource not found"));
return StatusCode(500, ApiResponse<T>.ErrorResult("Internal server error"));
```

## 🧪 Testing Patterns

### Unit Test Example
```csharp
[Test]
public async Task GetProperty_ValidId_ReturnsProperty()
{
    // Arrange
    var mockDb = new Mock<DapperDbContext>();
    var controller = new PropertiesController(mockDb.Object, Mock.Of<ILogger<PropertiesController>>());
    
    // Act
    var result = await controller.GetProperty("test-id");
    
    // Assert
    Assert.IsType<OkObjectResult>(result.Result);
}
```

## 🚀 Performance Tips

### Pagination
```csharp
const string sql = @"
    SELECT * FROM snb.table 
    ORDER BY created_at DESC 
    LIMIT @PageSize OFFSET @Offset";
    
var offset = (page - 1) * pageSize;
var results = await _dbContext.QueryAsync<T>(sql, new { PageSize = pageSize, Offset = offset });
```

### Caching (Future)
```csharp
// Ready for Redis integration
var cacheKey = $"property:{id}";
// var cached = await _cache.GetAsync<Property>(cacheKey);
```

## 🔧 Common Issues & Solutions

### Namespace Conflicts
```csharp
using StripeCheckout = Stripe.Checkout;
using StripeSubscription = Stripe.Subscription;
```

### Null Reference Safety
```csharp
var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
if (string.IsNullOrEmpty(userId))
    return Unauthorized();
```

### SQL Parameter Binding
```csharp
// ✅ Correct
new { userId = userId }

// ❌ Incorrect  
new { userId }  // May not work in all cases
```

## 📚 Additional Resources

- **Main Documentation:** `Controllers/README.md`
- **Technical Details:** `Controllers/TECHNICAL_IMPLEMENTATION.md`
- **Stripe Documentation:** https://docs.stripe.com/api
- **Dapper Documentation:** https://github.com/DapperLib/Dapper
- **Supabase Documentation:** https://supabase.com/docs

## 🎯 Migration Status

**✅ COMPLETED:** All 13 SoNoBrokers controllers migrated from Entity Framework to DapperDbContext
**✅ COMPLETED:** Stripe payment consolidation in Core/Stripe folder
**✅ COMPLETED:** Proper namespace organization following folder structure
**✅ COMPLETED:** Enhanced webhook handling for both payment types

**🚀 Ready for Production!**
