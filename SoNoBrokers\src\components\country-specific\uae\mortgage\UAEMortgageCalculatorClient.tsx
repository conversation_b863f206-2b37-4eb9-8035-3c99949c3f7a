'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Calculator, DollarSign, Percent, Calendar, TrendingUp, Home } from 'lucide-react'

interface UAEMortgageCalculatorClientProps {
  userType: 'buyer' | 'seller'
}

interface MortgageCalculation {
  monthlyPayment: number
  totalInterest: number
  totalPayment: number
  principalAndInterest: number
  lifeInsurance: number
  monthlyInsurance: number
  totalMonthlyPayment: number
}

export function UAEMortgageCalculatorClient({ userType }: UAEMortgageCalculatorClientProps) {
  const [propertyValue, setPropertyValue] = useState<number>(2000000) // AED
  const [downPayment, setDownPayment] = useState<number>(500000) // AED
  const [interestRate, setInterestRate] = useState<number>(4.5)
  const [loanTerm, setLoanTerm] = useState<number>(25)
  const [monthlyIncome, setMonthlyIncome] = useState<number>(50000) // AED
  const [calculation, setCalculation] = useState<MortgageCalculation | null>(null)

  // Calculate life insurance (typically required in UAE)
  const calculateLifeInsurance = (loanAmount: number): number => {
    // Typical life insurance is around 0.5% of loan amount annually
    return (loanAmount * 0.005) / 12
  }

  // Calculate mortgage payment
  const calculateMortgage = (): MortgageCalculation => {
    const principal = propertyValue - downPayment
    const monthlyRate = interestRate / 100 / 12
    const numberOfPayments = loanTerm * 12
    
    const monthlyPayment = principal * 
      (monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments)) / 
      (Math.pow(1 + monthlyRate, numberOfPayments) - 1)
    
    const totalPayment = monthlyPayment * numberOfPayments
    const totalInterest = totalPayment - principal
    
    const monthlyInsurance = calculateLifeInsurance(principal)
    const totalMonthlyPayment = monthlyPayment + monthlyInsurance
    
    return {
      monthlyPayment,
      totalInterest,
      totalPayment,
      principalAndInterest: monthlyPayment,
      lifeInsurance: monthlyInsurance * numberOfPayments,
      monthlyInsurance,
      totalMonthlyPayment
    }
  }

  useEffect(() => {
    setCalculation(calculateMortgage())
  }, [propertyValue, downPayment, interestRate, loanTerm])

  const downPaymentPercent = (downPayment / propertyValue) * 100
  const maxDownPayment = propertyValue * 0.75 // Maximum 75% financing for expats
  const debtToIncomeRatio = calculation ? (calculation.totalMonthlyPayment / monthlyIncome) * 100 : 0

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4 flex items-center gap-2">
          <Calculator className="w-8 h-8" />
          UAE Mortgage Calculator
        </h1>
        <p className="text-muted-foreground text-lg">
          Calculate your mortgage payments with UAE banking regulations and requirements.
        </p>
      </div>

      <div className="grid gap-8 lg:grid-cols-2">
        {/* Input Section */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Property & Loan Details</CardTitle>
              <CardDescription>
                Enter your property and financing information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Property Value */}
              <div className="space-y-2">
                <Label htmlFor="propertyValue">Property Value (AED)</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="propertyValue"
                    type="number"
                    value={propertyValue}
                    onChange={(e) => setPropertyValue(Number(e.target.value))}
                    className="pl-10"
                    placeholder="2,000,000"
                  />
                </div>
              </div>

              {/* Down Payment */}
              <div className="space-y-4">
                <Label>Down Payment: AED {downPayment.toLocaleString()} ({downPaymentPercent.toFixed(1)}%)</Label>
                <Slider
                  value={[downPayment]}
                  onValueChange={(value) => setDownPayment(value[0])}
                  max={maxDownPayment}
                  min={propertyValue * 0.25} // Minimum 25% down payment for expats
                  step={10000}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>25% (AED {(propertyValue * 0.25).toLocaleString()})</span>
                  <span>75% (AED {maxDownPayment.toLocaleString()})</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  UAE residents: 20% minimum | Expats: 25% minimum
                </p>
              </div>

              {/* Interest Rate */}
              <div className="space-y-4">
                <Label>Interest Rate: {interestRate}%</Label>
                <Slider
                  value={[interestRate]}
                  onValueChange={(value) => setInterestRate(value[0])}
                  max={8}
                  min={2}
                  step={0.1}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>2%</span>
                  <span>8%</span>
                </div>
              </div>

              {/* Loan Term */}
              <div className="space-y-2">
                <Label>Loan Term</Label>
                <Select value={loanTerm.toString()} onValueChange={(value) => setLoanTerm(Number(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15">15 years</SelectItem>
                    <SelectItem value="20">20 years</SelectItem>
                    <SelectItem value="25">25 years</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Maximum 25 years for expats, 30 years for UAE nationals
                </p>
              </div>

              {/* Monthly Income */}
              <div className="space-y-2">
                <Label htmlFor="monthlyIncome">Monthly Income (AED)</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="monthlyIncome"
                    type="number"
                    value={monthlyIncome}
                    onChange={(e) => setMonthlyIncome(Number(e.target.value))}
                    className="pl-10"
                    placeholder="50,000"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Results Section */}
        <div className="space-y-6">
          {calculation && (
            <>
              {/* Total Monthly Payment */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Home className="w-5 h-5" />
                    Total Monthly Payment
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-primary">
                    AED {calculation.totalMonthlyPayment.toLocaleString('en-AE', { 
                      minimumFractionDigits: 2, 
                      maximumFractionDigits: 2 
                    })}
                  </div>
                  <p className="text-muted-foreground">Including insurance</p>
                </CardContent>
              </Card>

              {/* Payment Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Monthly Payment Breakdown</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span>Principal & Interest:</span>
                    <span className="font-semibold">
                      AED {calculation.principalAndInterest.toLocaleString('en-AE', { 
                        minimumFractionDigits: 2, 
                        maximumFractionDigits: 2 
                      })}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Life Insurance:</span>
                    <span className="font-semibold">
                      AED {calculation.monthlyInsurance.toLocaleString('en-AE', { 
                        minimumFractionDigits: 2, 
                        maximumFractionDigits: 2 
                      })}
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Debt-to-Income Ratio */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Debt-to-Income Ratio
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Current Ratio:</span>
                      <span className={`font-semibold ${debtToIncomeRatio > 50 ? 'text-red-600' : debtToIncomeRatio > 40 ? 'text-yellow-600' : 'text-green-600'}`}>
                        {debtToIncomeRatio.toFixed(1)}%
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      UAE banks typically require debt-to-income ratio below 50%
                    </p>
                    {debtToIncomeRatio > 50 && (
                      <p className="text-sm text-red-600">
                        ⚠️ Your ratio exceeds typical bank requirements
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Loan Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    Loan Summary
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Loan Amount</p>
                      <p className="font-semibold">
                        AED {(propertyValue - downPayment).toLocaleString('en-AE')}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Total Interest</p>
                      <p className="font-semibold">
                        AED {calculation.totalInterest.toLocaleString('en-AE', { 
                          minimumFractionDigits: 2, 
                          maximumFractionDigits: 2 
                        })}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Total Payments</p>
                      <p className="font-semibold">
                        AED {calculation.totalPayment.toLocaleString('en-AE', { 
                          minimumFractionDigits: 2, 
                          maximumFractionDigits: 2 
                        })}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Down Payment</p>
                      <p className="font-semibold">
                        AED {downPayment.toLocaleString('en-AE')} ({downPaymentPercent.toFixed(1)}%)
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* UAE Mortgage Information */}
              <Card>
                <CardHeader>
                  <CardTitle>UAE Mortgage Requirements</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2 text-sm">
                  <p>• Minimum salary: AED 15,000/month for expats</p>
                  <p>• Maximum loan-to-value: 75% for expats, 80% for UAE nationals</p>
                  <p>• Life insurance mandatory for mortgage duration</p>
                  <p>• Property valuation required by bank-approved valuers</p>
                  <p>• Processing fees typically 1% of loan amount</p>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
