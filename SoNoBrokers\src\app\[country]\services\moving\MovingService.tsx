import React from 'react'
import { ServiceLayout } from '@/components/shared/services/ServiceLayout'

interface MovingServiceProps {
  userType: 'buyer' | 'seller'
  isSignedIn: boolean
  country: string
}

// Mock data - in real implementation, this would come from your database and Google Places API
const getMockProviders = (country: string) => {
  const baseProviders = [
    {
      id: '1',
      name: '<PERSON>',
      businessName: 'Thompson Moving Solutions',
      serviceType: 'Licensed Moving Company',
      location: country === 'CA' ? 'Toronto, ON' : 'New York, NY',
      distance: '2.1 km',
      rating: 4.9,
      reviewCount: 245,
      price: 'From $120/hr',
      specialties: ['Local Moving', 'Long Distance', 'Packing Services', 'Storage Solutions'],
      verified: true,
      isAdvertiser: true,
      isPremium: true,
      image: '/api/placeholder/80/80',
      phone: '******-0141',
      email: '<EMAIL>',
      website: 'https://thompsonmoving.com',
      description: 'Professional moving company with 15+ years experience. Fully licensed and insured with excellent customer service.',
      coordinates: country === 'CA' ? { lat: 43.6532, lng: -79.3832 } : { lat: 40.7128, lng: -74.0060 }
    },
    {
      id: '2',
      name: '<PERSON>',
      businessName: 'Rodriguez Relocation Services',
      serviceType: 'Moving Specialist',
      location: country === 'CA' ? 'Vancouver, BC' : 'Los Angeles, CA',
      distance: '3.8 km',
      rating: 4.8,
      reviewCount: 189,
      price: 'From $110/hr',
      specialties: ['Residential Moving', 'Office Relocation', 'Fragile Items', 'Same-Day Service'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/80/80',
      phone: '******-0142',
      description: 'Reliable moving services with careful handling of your belongings. Competitive rates and flexible scheduling.',
      coordinates: country === 'CA' ? { lat: 49.2827, lng: -123.1207 } : { lat: 34.0522, lng: -118.2437 }
    },
    {
      id: '3',
      name: 'David Kim',
      businessName: 'Kim Moving & Storage',
      serviceType: 'Moving Company',
      location: country === 'CA' ? 'Calgary, AB' : 'Chicago, IL',
      distance: '5.2 km',
      rating: 4.7,
      reviewCount: 156,
      price: 'From $100/hr',
      specialties: ['Budget Moving', 'Student Moves', 'Senior Discounts', 'Weekend Service'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/80/80',
      description: 'Affordable moving solutions without compromising on quality. Family-owned business serving the community.',
      coordinates: country === 'CA' ? { lat: 51.0447, lng: -114.0719 } : { lat: 41.8781, lng: -87.6298 }
    }
  ]

  // Add Google API providers (non-registered)
  const googleProviders = [
    {
      id: 'g1',
      name: 'Quick Move Express',
      businessName: 'Quick Move Express',
      serviceType: 'Moving Service',
      location: country === 'CA' ? 'Mississauga, ON' : 'Brooklyn, NY',
      distance: '8.5 km',
      rating: 4.4,
      reviewCount: 78,
      price: 'From $95/hr',
      specialties: ['Fast Service', 'Basic Moving'],
      verified: false,
      isAdvertiser: false,
      isPremium: false,
      description: 'Quick and efficient moving services for local relocations.',
      coordinates: country === 'CA' ? { lat: 43.5890, lng: -79.6441 } : { lat: 40.6782, lng: -73.9442 }
    },
    {
      id: 'g2',
      name: 'City Movers Pro',
      businessName: 'City Movers Pro',
      serviceType: 'Moving Company',
      location: country === 'CA' ? 'Markham, ON' : 'Queens, NY',
      distance: '11.3 km',
      rating: 4.2,
      reviewCount: 52,
      price: 'From $85/hr',
      specialties: ['Budget Friendly', 'Small Moves'],
      verified: false,
      isAdvertiser: false,
      isPremium: false,
      description: 'Affordable moving services for apartments and small homes.',
      coordinates: country === 'CA' ? { lat: 43.8561, lng: -79.3370 } : { lat: 40.7282, lng: -73.7949 }
    }
  ]

  return [...baseProviders, ...googleProviders]
}

export function MovingService({
  userType,
  isSignedIn,
  country
}: MovingServiceProps) {
  // Get providers data (this would be async in real implementation)
  const mockProviders = getMockProviders(country)

  // Sort by distance and premium status
  const providers = mockProviders.sort((a, b) => {
    if (a.isPremium && !b.isPremium) return -1
    if (!a.isPremium && b.isPremium) return 1
    if (a.isAdvertiser && !b.isAdvertiser) return -1
    if (!a.isAdvertiser && b.isAdvertiser) return 1
    return parseFloat(a.distance) - parseFloat(b.distance)
  })

  const serviceDescription = userType === 'buyer'
    ? 'Professional moving services to help you relocate to your new home. Our licensed and insured moving companies provide reliable, efficient service with competitive rates and careful handling of your belongings.'
    : 'Trusted moving services for property owners and landlords. Whether you\'re relocating or helping tenants move, our professional movers ensure a smooth transition with minimal disruption to your business.'

  // Check environment variable for Google providers
  const showGoogleProviders = process.env.NEXT_PUBLIC_ENABLE_GOOGLE_PROVIDERS !== 'false'

  return (
    <ServiceLayout
      userType={userType}
      isSignedIn={isSignedIn}
      serviceTitle="Moving Services"
      serviceDescription={serviceDescription}
      country={country}
      providers={providers}
      showGoogleProviders={showGoogleProviders}
    />
  )
}
