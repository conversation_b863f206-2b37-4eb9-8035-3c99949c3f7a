# PowerShell script to run SoNoBrokers database migrations
# This script runs the numbered migration files in order (0001_, 0002_, etc.)

param(
    [string]$ConnectionString = "Host=db.yfznlsisxsnymkvydzha.supabase.co;Database=postgres;Username=postgres;Password=Shared4w0rk!;SSL Mode=Require;",
    [switch]$DryRun = $false,
    [switch]$Force = $false,
    [string]$UpTo = $null
)

Write-Host "🚀 SoNoBrokers Database Migration Runner" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Get the script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# Get all numbered migration files
$MigrationFiles = Get-ChildItem -Path $ScriptDir -Filter "*.sql" | 
    Where-Object { $_.Name -match "^\d{4}_.*\.sql$" } | 
    Sort-Object Name

if ($MigrationFiles.Count -eq 0) {
    Write-Host "❌ No migration files found matching pattern 0001_*.sql" -ForegroundColor Red
    exit 1
}

Write-Host "📋 Found $($MigrationFiles.Count) migration files:" -ForegroundColor Cyan
foreach ($file in $MigrationFiles) {
    Write-Host "   - $($file.Name)" -ForegroundColor Gray
}

# Filter migrations if UpTo is specified
if ($UpTo) {
    $MigrationFiles = $MigrationFiles | Where-Object { $_.Name -le $UpTo }
    Write-Host "🎯 Running migrations up to: $UpTo" -ForegroundColor Yellow
}

if ($DryRun) {
    Write-Host "🔍 DRY RUN MODE - No changes will be made" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Would execute the following migrations:" -ForegroundColor Yellow
    foreach ($file in $MigrationFiles) {
        Write-Host "   ✓ $($file.Name)" -ForegroundColor Green
    }
    exit 0
}

# Confirm execution unless Force is specified
if (-not $Force) {
    Write-Host ""
    $confirmation = Read-Host "Do you want to proceed with running these migrations? (y/N)"
    if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
        Write-Host "❌ Migration cancelled by user" -ForegroundColor Red
        exit 0
    }
}

Write-Host ""
Write-Host "🔄 Starting migration execution..." -ForegroundColor Green

# Check if psql is available
try {
    $null = Get-Command psql -ErrorAction Stop
} catch {
    Write-Host "❌ psql command not found. Please install PostgreSQL client tools." -ForegroundColor Red
    exit 1
}

# Create a temporary script that runs all migrations
$TempScript = Join-Path $env:TEMP "sonobrokers_migrations.sql"

try {
    # Build the master migration script
    $MasterScript = @"
-- SoNoBrokers Migration Runner
-- Generated: $(Get-Date)
-- Migrations: $($MigrationFiles.Count) files

-- Create schema_migrations table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.schema_migrations (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL UNIQUE,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    checksum VARCHAR(64) NOT NULL
);

"@

    foreach ($file in $MigrationFiles) {
        $migrationName = [System.IO.Path]::GetFileNameWithoutExtension($file.Name)
        $migrationContent = Get-Content $file.FullName -Raw
        
        $MasterScript += @"

-- =====================================================
-- MIGRATION: $($file.Name)
-- =====================================================

DO `$`$
DECLARE
    migration_exists INTEGER;
BEGIN
    -- Check if migration already exists
    SELECT COUNT(*) INTO migration_exists 
    FROM public.schema_migrations 
    WHERE migration_name = '$migrationName';
    
    IF migration_exists = 0 THEN
        RAISE NOTICE 'Running migration: $migrationName';
        
        -- Execute migration content
$migrationContent

        -- Record migration
        INSERT INTO public.schema_migrations (migration_name, checksum) 
        VALUES ('$migrationName', md5('$migrationContent'));
        
        RAISE NOTICE '✅ Migration $migrationName completed successfully';
    ELSE
        RAISE NOTICE '⏭️  Migration $migrationName already executed, skipping';
    END IF;
END
`$`$;

"@
    }

    # Write the master script to temp file
    $MasterScript | Out-File -FilePath $TempScript -Encoding UTF8

    Write-Host "📝 Generated master migration script: $TempScript" -ForegroundColor Gray
    Write-Host ""

    # Execute the migration script
    Write-Host "🔄 Executing migrations..." -ForegroundColor Green
    
    $env:PGPASSWORD = ($ConnectionString -split "Password=")[1] -split ";")[0]
    
    # Parse connection string
    $Host = ($ConnectionString -split "Host=")[1] -split ";")[0]
    $Database = ($ConnectionString -split "Database=")[1] -split ";")[0]
    $Username = ($ConnectionString -split "Username=")[1] -split ";")[0]
    
    $psqlArgs = @(
        "-h", $Host
        "-d", $Database
        "-U", $Username
        "-f", $TempScript
        "-v", "ON_ERROR_STOP=1"
    )
    
    $result = & psql @psqlArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "🎉 All migrations completed successfully!" -ForegroundColor Green
        Write-Host "✅ Database is now up to date" -ForegroundColor Green
    } else {
        Write-Host ""
        Write-Host "❌ Migration failed with exit code: $LASTEXITCODE" -ForegroundColor Red
        Write-Host "Check the output above for error details" -ForegroundColor Red
        exit $LASTEXITCODE
    }

} catch {
    Write-Host ""
    Write-Host "❌ Error during migration execution:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    exit 1
} finally {
    # Clean up temp file
    if (Test-Path $TempScript) {
        Remove-Item $TempScript -Force
    }
    
    # Clear password from environment
    $env:PGPASSWORD = $null
}

Write-Host ""
Write-Host "📊 Migration Summary:" -ForegroundColor Cyan
Write-Host "   Total migrations: $($MigrationFiles.Count)" -ForegroundColor Gray
Write-Host "   Connection: $($Host)/$($Database)" -ForegroundColor Gray
Write-Host "   Completed: $(Get-Date)" -ForegroundColor Gray
