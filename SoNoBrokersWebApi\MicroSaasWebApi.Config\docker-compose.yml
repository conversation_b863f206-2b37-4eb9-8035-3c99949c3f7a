# =============================================================================
# MicroSaasWebApi.Config - Docker Compose Configuration
# =============================================================================
# Configuration server for SoNoBrokers Web API
# Serves configuration files, scripts, and templates
# =============================================================================

version: '3.8'

services:
  sonobrokers-config:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sonobrokers-config
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
    volumes:
      - config-data:/usr/share/nginx/html/config
      - ./AppSettings:/usr/share/nginx/html/config/appsettings:ro
      - ./Environment:/usr/share/nginx/html/config/environment:ro
      - ./Scripts:/usr/share/nginx/html/scripts:ro
      - ./EmailTemplates:/usr/share/nginx/html/templates/email:ro
      - ./Postman:/usr/share/nginx/html/config/postman:ro
    networks:
      - sonobrokers-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    labels:
      - "com.sonobrokers.service=config"
      - "com.sonobrokers.version=1.0.0"

volumes:
  config-data:
    driver: local

networks:
  sonobrokers-network:
    driver: bridge
    name: sonobrokers-network
    external: true
