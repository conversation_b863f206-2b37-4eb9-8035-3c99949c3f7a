using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using MicroSaasWebApi.Config.Database;
using MicroSaasWebApi.Services.SoNoBrokers;
using MicroSaasWebApi.Models.SoNoBrokers.Messaging;
using System.Data;
using Dapper;

namespace MicroSaasWebApi.Tests.Services
{
    [TestFixture]
    public class MessagingServiceTests
    {
        private Mock<IDapperDbContext> _mockContext;
        private Mock<ILogger<MessagingService>> _mockLogger;
        private Mock<IDbConnection> _mockConnection;
        private Mock<IDbTransaction> _mockTransaction;
        private MessagingService _messagingService;

        [SetUp]
        public void Setup()
        {
            _mockContext = new Mock<IDapperDbContext>();
            _mockLogger = new Mock<ILogger<MessagingService>>();
            _mockConnection = new Mock<IDbConnection>();
            _mockTransaction = new Mock<IDbTransaction>();

            _mockContext.Setup(x => x.CreateConnection()).Returns(_mockConnection.Object);
            _mockConnection.Setup(x => x.BeginTransaction()).Returns(_mockTransaction.Object);

            _messagingService = new MessagingService(_mockContext.Object, _mockLogger.Object);
        }

        [Test]
        public async Task CreateConversationAsync_WithValidRequest_ReturnsConversation()
        {
            // Arrange
            var buyerId = "buyer-123";
            var request = new CreateConversationRequest
            {
                PropertyId = "property-123",
                SellerId = "seller-123",
                Subject = "Property Inquiry",
                InitialMessage = "I'm interested in this property"
            };

            // Mock existing conversation check (no existing conversation)
            _mockConnection.Setup(x => x.QueryFirstOrDefaultAsync<string>(
                It.IsAny<string>(), It.IsAny<object>(), It.IsAny<IDbTransaction>(), null, null))
                .ReturnsAsync((string)null);

            // Mock conversation creation
            _mockConnection.Setup(x => x.ExecuteAsync(
                It.IsAny<string>(), It.IsAny<object>(), It.IsAny<IDbTransaction>(), null, null))
                .ReturnsAsync(1);

            // Mock getting conversation details
            var expectedConversation = new ConversationResponse
            {
                Id = "conv-123",
                PropertyId = request.PropertyId,
                BuyerId = buyerId,
                SellerId = request.SellerId,
                Subject = request.Subject,
                IsActive = true
            };

            // Act & Assert
            // Note: This test would need the actual GetConversationAsync method to be mocked
            // For now, we're testing the structure
            Assert.That(_messagingService, Is.Not.Null);
        }

        [Test]
        public async Task CanUserAccessConversationAsync_WithValidUser_ReturnsTrue()
        {
            // Arrange
            var userId = "user-123";
            var conversationId = "conv-123";

            _mockConnection.Setup(x => x.QuerySingleAsync<int>(
                It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(1);

            // Act
            var result = await _messagingService.CanUserAccessConversationAsync(userId, conversationId);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task CanUserAccessConversationAsync_WithInvalidUser_ReturnsFalse()
        {
            // Arrange
            var userId = "user-123";
            var conversationId = "conv-123";

            _mockConnection.Setup(x => x.QuerySingleAsync<int>(
                It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(0);

            // Act
            var result = await _messagingService.CanUserAccessConversationAsync(userId, conversationId);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public async Task CanUserAccessConversationAsync_WithException_ReturnsFalse()
        {
            // Arrange
            var userId = "user-123";
            var conversationId = "conv-123";

            _mockConnection.Setup(x => x.QuerySingleAsync<int>(
                It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _messagingService.CanUserAccessConversationAsync(userId, conversationId);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void CreateConversationRequest_WithValidData_PassesValidation()
        {
            // Arrange
            var request = new CreateConversationRequest
            {
                PropertyId = "property-123",
                SellerId = "seller-123",
                Subject = "Property Inquiry",
                InitialMessage = "I'm interested in this property"
            };

            // Act & Assert
            Assert.That(request.PropertyId, Is.Not.Empty);
            Assert.That(request.SellerId, Is.Not.Empty);
            Assert.That(request.InitialMessage, Is.Not.Empty);
            Assert.That(request.InitialMessage.Length, Is.LessThanOrEqualTo(1000));
        }

        [Test]
        public void CreateMessageRequest_WithValidData_PassesValidation()
        {
            // Arrange
            var request = new CreateMessageRequest
            {
                ConversationId = "conv-123",
                Content = "This is a test message",
                MessageType = "text"
            };

            // Act & Assert
            Assert.That(request.ConversationId, Is.Not.Empty);
            Assert.That(request.Content, Is.Not.Empty);
            Assert.That(request.Content.Length, Is.LessThanOrEqualTo(2000));
            Assert.That(request.MessageType, Is.EqualTo("text"));
        }

        [Test]
        public void ConversationSearchParams_WithDefaultValues_HasCorrectDefaults()
        {
            // Arrange & Act
            var searchParams = new ConversationSearchParams();

            // Assert
            Assert.That(searchParams.Page, Is.EqualTo(1));
            Assert.That(searchParams.Limit, Is.EqualTo(20));
            Assert.That(searchParams.SortBy, Is.EqualTo("lastMessageAt"));
            Assert.That(searchParams.SortOrder, Is.EqualTo("desc"));
        }

        [Test]
        public void MessageSearchParams_WithDefaultValues_HasCorrectDefaults()
        {
            // Arrange & Act
            var searchParams = new MessageSearchParams();

            // Assert
            Assert.That(searchParams.Page, Is.EqualTo(1));
            Assert.That(searchParams.Limit, Is.EqualTo(50));
            Assert.That(searchParams.SortBy, Is.EqualTo("createdAt"));
            Assert.That(searchParams.SortOrder, Is.EqualTo("asc"));
        }

        [Test]
        public void ConversationResponse_WithData_MapsCorrectly()
        {
            // Arrange & Act
            var response = new ConversationResponse
            {
                Id = "conv-123",
                PropertyId = "prop-123",
                PropertyTitle = "Test Property",
                BuyerId = "buyer-123",
                BuyerName = "John Buyer",
                SellerId = "seller-123",
                SellerName = "Jane Seller",
                Subject = "Property Inquiry",
                IsActive = true,
                UnreadCount = 5
            };

            // Assert
            Assert.That(response.Id, Is.EqualTo("conv-123"));
            Assert.That(response.PropertyTitle, Is.EqualTo("Test Property"));
            Assert.That(response.BuyerName, Is.EqualTo("John Buyer"));
            Assert.That(response.SellerName, Is.EqualTo("Jane Seller"));
            Assert.That(response.IsActive, Is.True);
            Assert.That(response.UnreadCount, Is.EqualTo(5));
        }

        [Test]
        public void MessageResponse_WithData_MapsCorrectly()
        {
            // Arrange & Act
            var message = new MessageResponse
            {
                Id = "msg-123",
                ConversationId = "conv-123",
                SenderId = "user-123",
                SenderName = "Test User",
                Content = "Test message content",
                MessageType = "text",
                IsRead = false,
                CreatedAt = DateTime.UtcNow,
                IsOwnMessage = true
            };

            // Assert
            Assert.That(message.Id, Is.EqualTo("msg-123"));
            Assert.That(message.ConversationId, Is.EqualTo("conv-123"));
            Assert.That(message.Content, Is.EqualTo("Test message content"));
            Assert.That(message.MessageType, Is.EqualTo("text"));
            Assert.That(message.IsRead, Is.False);
            Assert.That(message.IsOwnMessage, Is.True);
        }

        [TearDown]
        public void TearDown()
        {
            _mockContext?.Reset();
            _mockLogger?.Reset();
            _mockConnection?.Reset();
            _mockTransaction?.Reset();
        }
    }
}
