using Stripe;
using Stripe.Checkout;
using MicroSaasWebApi.Models.Payment;
using MicroSaasWebApi.Services.Configuration;
using MicroSaasWebApi.Services.Payment.Interfaces;

namespace MicroSaasWebApi.Services.Payment
{
    /// <summary>
    /// Stripe payment service implementation
    /// </summary>
    public class StripePaymentService : IStripePaymentService
    {
        private readonly ILogger<StripePaymentService> _logger;
        private readonly CustomerService _customerService;
        private readonly SubscriptionService _subscriptionService;
        private readonly SessionService _sessionService;
        private readonly InvoiceService _invoiceService;
        private readonly PaymentIntentService _paymentIntentService;
        private readonly PaymentMethodService _paymentMethodService;
        private readonly ProductService _productService;
        private readonly PriceService _priceService;
        private readonly ChargeService _chargeService;
        private readonly RefundService _refundService;
        private readonly SetupIntentService _setupIntentService;
        private readonly CouponService _couponService;
        private readonly BalanceService _balanceService;
        private readonly BalanceTransactionService _balanceTransactionService;

        public StripePaymentService(ILogger<StripePaymentService> logger)
        {
            _logger = logger;

            var stripeSecretKey = EnvironmentConfigurationService.GetEnvironmentVariable("STRIPE_SECRET_KEY");
            if (string.IsNullOrEmpty(stripeSecretKey))
            {
                throw new InvalidOperationException("STRIPE_SECRET_KEY environment variable is required");
            }

            StripeConfiguration.ApiKey = stripeSecretKey;

            _customerService = new CustomerService();
            _subscriptionService = new SubscriptionService();
            _sessionService = new SessionService();
            _invoiceService = new InvoiceService();
            _paymentIntentService = new PaymentIntentService();
            _paymentMethodService = new PaymentMethodService();
            _productService = new ProductService();
            _priceService = new PriceService();
            _chargeService = new ChargeService();
            _refundService = new RefundService();
            _setupIntentService = new SetupIntentService();
            _couponService = new CouponService();
            _balanceService = new BalanceService();
            _balanceTransactionService = new BalanceTransactionService();
        }

        public async Task<Customer?> CreateCustomerAsync(string email, string name, string? description = null)
        {
            try
            {
                var options = new CustomerCreateOptions
                {
                    Email = email,
                    Name = name,
                    Description = description
                };

                var customer = await _customerService.CreateAsync(options);
                return customer;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating Stripe customer for email: {Email}", email);
                return null;
            }
        }

        public async Task<Customer?> UpdateCustomerAsync(string customerId, string? email = null, string? name = null, string? description = null)
        {
            try
            {
                var options = new CustomerUpdateOptions();
                if (!string.IsNullOrEmpty(email)) options.Email = email;
                if (!string.IsNullOrEmpty(name)) options.Name = name;
                if (!string.IsNullOrEmpty(description)) options.Description = description;

                var customer = await _customerService.UpdateAsync(customerId, options);
                return customer;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating Stripe customer: {CustomerId}", customerId);
                return null;
            }
        }

        public async Task<bool> DeleteCustomerAsync(string customerId)
        {
            try
            {
                await _customerService.DeleteAsync(customerId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting Stripe customer: {CustomerId}", customerId);
                return false;
            }
        }

        public async Task<IEnumerable<Customer>> ListCustomersAsync(int limit = 10, string? startingAfter = null)
        {
            try
            {
                var options = new CustomerListOptions
                {
                    Limit = limit,
                    StartingAfter = startingAfter
                };

                var customers = await _customerService.ListAsync(options);
                return customers.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing Stripe customers");
                return new List<Customer>();
            }
        }

        public async Task<MicroSaasWebApi.Models.Payment.PaymentResponse> CreateSubscriptionAsync(MicroSaasWebApi.Models.Payment.CreateSubscriptionRequest request)
        {
            try
            {
                var options = new SubscriptionCreateOptions
                {
                    Customer = request.CustomerId,
                    Items = new List<SubscriptionItemOptions>
                    {
                        new SubscriptionItemOptions
                        {
                            Price = request.PriceId,
                        },
                    },
                    PaymentBehavior = "default_incomplete",
                    PaymentSettings = new SubscriptionPaymentSettingsOptions
                    {
                        SaveDefaultPaymentMethod = "on_subscription",
                    },
                    Expand = new List<string> { "latest_invoice.payment_intent" },
                };

                if (request.TrialPeriodDays > 0)
                {
                    options.TrialPeriodDays = request.TrialPeriodDays;
                }

                var subscription = await _subscriptionService.CreateAsync(options);

                return new MicroSaasWebApi.Models.Payment.PaymentResponse
                {
                    Success = true,
                    SubscriptionId = subscription.Id,
                    ClientSecret = subscription.LatestInvoice?.PaymentIntent?.ClientSecret,
                    Status = subscription.Status
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating subscription for customer: {CustomerId}", request.CustomerId);
                return new MicroSaasWebApi.Models.Payment.PaymentResponse
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<MicroSaasWebApi.Models.Payment.PaymentResponse> CreateOneTimePaymentAsync(MicroSaasWebApi.Models.Payment.OneTimePaymentRequest request)
        {
            try
            {
                var options = new PaymentIntentCreateOptions
                {
                    Amount = (long)(request.Amount * 100), // Convert to cents
                    Currency = request.Currency.ToLower(),
                    Customer = request.CustomerId,
                    Description = request.Description,
                    Metadata = request.Metadata ?? new Dictionary<string, string>(),
                    AutomaticPaymentMethods = new PaymentIntentAutomaticPaymentMethodsOptions
                    {
                        Enabled = true,
                    },
                };

                var paymentIntent = await _paymentIntentService.CreateAsync(options);

                return new MicroSaasWebApi.Models.Payment.PaymentResponse
                {
                    Success = true,
                    PaymentIntentId = paymentIntent.Id,
                    ClientSecret = paymentIntent.ClientSecret,
                    Status = paymentIntent.Status
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating one-time payment for customer: {CustomerId}", request.CustomerId);
                return new MicroSaasWebApi.Models.Payment.PaymentResponse
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<string> CreateCheckoutSessionAsync(MicroSaasWebApi.Models.Payment.CheckoutSessionRequest request)
        {
            try
            {
                var options = new SessionCreateOptions
                {
                    PaymentMethodTypes = new List<string> { "card" },
                    LineItems = request.LineItems.Select(item => new SessionLineItemOptions
                    {
                        Price = item.PriceId,
                        Quantity = item.Quantity,
                    }).ToList(),
                    Mode = request.Mode, // "payment", "subscription", or "setup"
                    SuccessUrl = request.SuccessUrl,
                    CancelUrl = request.CancelUrl,
                    Customer = request.CustomerId,
                    Metadata = request.Metadata ?? new Dictionary<string, string>(),
                };

                if (request.Mode == "subscription" && request.TrialPeriodDays > 0)
                {
                    options.SubscriptionData = new SessionSubscriptionDataOptions
                    {
                        TrialPeriodDays = request.TrialPeriodDays,
                    };
                }

                var session = await _sessionService.CreateAsync(options);
                return session.Url;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating checkout session for customer: {CustomerId}", request.CustomerId);
                throw;
            }
        }

        public async Task<Subscription?> GetSubscriptionAsync(string subscriptionId)
        {
            try
            {
                var subscription = await _subscriptionService.GetAsync(subscriptionId);
                return subscription;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting subscription: {SubscriptionId}", subscriptionId);
                return null;
            }
        }

        public async Task<Customer?> GetCustomerAsync(string customerId)
        {
            try
            {
                var customer = await _customerService.GetAsync(customerId);
                return customer;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer: {CustomerId}", customerId);
                return null;
            }
        }

        public async Task<IEnumerable<MicroSaasWebApi.Models.Payment.Invoice>> GetCustomerInvoicesAsync(string customerId, int limit = 10)
        {
            try
            {
                var options = new InvoiceListOptions
                {
                    Customer = customerId,
                    Limit = limit,
                };

                var stripeInvoices = await _invoiceService.ListAsync(options);

                // Convert Stripe invoices to our model
                var invoices = stripeInvoices.Data.Select(invoice => new MicroSaasWebApi.Models.Payment.Invoice
                {
                    Id = invoice.Id,
                    CustomerId = invoice.CustomerId,
                    SubscriptionId = invoice.SubscriptionId ?? string.Empty,
                    Amount = (decimal)invoice.AmountPaid / 100, // Convert from cents
                    Currency = invoice.Currency?.ToUpper() ?? "USD",
                    Status = invoice.Status ?? "unknown",
                    CreatedAt = invoice.Created,
                    PaidAt = invoice.StatusTransitions?.PaidAt,
                    InvoiceUrl = invoice.InvoicePdf,
                    Description = invoice.Description
                });

                return invoices;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoices for customer: {CustomerId}", customerId);
                return new List<MicroSaasWebApi.Models.Payment.Invoice>();
            }
        }

        public bool ValidateWebhookSignature(string payload, string signature, string endpointSecret)
        {
            try
            {
                var stripeEvent = EventUtility.ConstructEvent(payload, signature, endpointSecret);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Invalid webhook signature");
                return false;
            }
        }

        public Event? ParseWebhookEvent(string payload, string signature)
        {
            try
            {
                var endpointSecret = EnvironmentConfigurationService.GetEnvironmentVariable("STRIPE_WEBHOOK_SECRET");
                if (string.IsNullOrEmpty(endpointSecret))
                {
                    _logger.LogWarning("STRIPE_WEBHOOK_SECRET not configured, skipping signature validation");
                    return Event.FromJson(payload);
                }

                return EventUtility.ConstructEvent(payload, signature, endpointSecret);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing webhook event");
                return null;
            }
        }

        #region Payment Methods
        public async Task<PaymentMethod?> CreatePaymentMethodAsync(string customerId, string type = "card", Dictionary<string, object>? cardDetails = null)
        {
            try
            {
                var options = new PaymentMethodCreateOptions
                {
                    Type = type,
                    Customer = customerId
                };

                var paymentMethod = await _paymentMethodService.CreateAsync(options);
                return paymentMethod;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating payment method for customer: {CustomerId}", customerId);
                return null;
            }
        }

        public async Task<PaymentMethod?> GetPaymentMethodAsync(string paymentMethodId)
        {
            try
            {
                return await _paymentMethodService.GetAsync(paymentMethodId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment method: {PaymentMethodId}", paymentMethodId);
                return null;
            }
        }

        public async Task<PaymentMethod?> AttachPaymentMethodAsync(string paymentMethodId, string customerId)
        {
            try
            {
                var options = new PaymentMethodAttachOptions { Customer = customerId };
                return await _paymentMethodService.AttachAsync(paymentMethodId, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error attaching payment method: {PaymentMethodId}", paymentMethodId);
                return null;
            }
        }

        public async Task<PaymentMethod?> DetachPaymentMethodAsync(string paymentMethodId)
        {
            try
            {
                return await _paymentMethodService.DetachAsync(paymentMethodId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error detaching payment method: {PaymentMethodId}", paymentMethodId);
                return null;
            }
        }

        public async Task<IEnumerable<PaymentMethod>> ListCustomerPaymentMethodsAsync(string customerId, string type = "card")
        {
            try
            {
                var options = new PaymentMethodListOptions
                {
                    Customer = customerId,
                    Type = type
                };
                var paymentMethods = await _paymentMethodService.ListAsync(options);
                return paymentMethods.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing payment methods for customer: {CustomerId}", customerId);
                return new List<PaymentMethod>();
            }
        }
        #endregion

        #region Payment Intents
        public async Task<PaymentIntent?> CreatePaymentIntentAsync(long amount, string currency, string customerId, string? paymentMethodId = null, bool confirmImmediately = false)
        {
            try
            {
                var options = new PaymentIntentCreateOptions
                {
                    Amount = amount,
                    Currency = currency,
                    Customer = customerId,
                    PaymentMethod = paymentMethodId,
                    ConfirmationMethod = "manual",
                    Confirm = confirmImmediately
                };

                return await _paymentIntentService.CreateAsync(options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating payment intent");
                return null;
            }
        }

        public async Task<PaymentIntent?> GetPaymentIntentAsync(string paymentIntentId)
        {
            try
            {
                return await _paymentIntentService.GetAsync(paymentIntentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment intent: {PaymentIntentId}", paymentIntentId);
                return null;
            }
        }

        public async Task<PaymentIntent?> UpdatePaymentIntentAsync(string paymentIntentId, long? amount = null, string? currency = null)
        {
            try
            {
                var options = new PaymentIntentUpdateOptions();
                if (amount.HasValue) options.Amount = amount.Value;
                if (!string.IsNullOrEmpty(currency)) options.Currency = currency;

                return await _paymentIntentService.UpdateAsync(paymentIntentId, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating payment intent: {PaymentIntentId}", paymentIntentId);
                return null;
            }
        }

        public async Task<PaymentIntent?> ConfirmPaymentIntentAsync(string paymentIntentId, string? paymentMethodId = null)
        {
            try
            {
                var options = new PaymentIntentConfirmOptions();
                if (!string.IsNullOrEmpty(paymentMethodId)) options.PaymentMethod = paymentMethodId;

                return await _paymentIntentService.ConfirmAsync(paymentIntentId, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error confirming payment intent: {PaymentIntentId}", paymentIntentId);
                return null;
            }
        }

        public async Task<PaymentIntent?> CancelPaymentIntentAsync(string paymentIntentId)
        {
            try
            {
                return await _paymentIntentService.CancelAsync(paymentIntentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error canceling payment intent: {PaymentIntentId}", paymentIntentId);
                return null;
            }
        }
        #endregion

        #region Subscriptions (Interface Methods)
        public async Task<Subscription?> CreateSubscriptionAsync(string customerId, string priceId, int? trialPeriodDays = null)
        {
            try
            {
                var options = new SubscriptionCreateOptions
                {
                    Customer = customerId,
                    Items = new List<SubscriptionItemOptions>
                    {
                        new SubscriptionItemOptions { Price = priceId }
                    }
                };

                if (trialPeriodDays.HasValue)
                    options.TrialPeriodDays = trialPeriodDays.Value;

                return await _subscriptionService.CreateAsync(options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating subscription");
                return null;
            }
        }

        public async Task<Subscription?> UpdateSubscriptionAsync(string subscriptionId, string? priceId = null, int? quantity = null)
        {
            try
            {
                var options = new SubscriptionUpdateOptions();

                if (!string.IsNullOrEmpty(priceId))
                {
                    options.Items = new List<SubscriptionItemOptions>
                    {
                        new SubscriptionItemOptions { Price = priceId, Quantity = quantity }
                    };
                }

                return await _subscriptionService.UpdateAsync(subscriptionId, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating subscription: {SubscriptionId}", subscriptionId);
                return null;
            }
        }

        public async Task<Subscription?> CancelSubscriptionAsync(string subscriptionId, bool cancelImmediately = false)
        {
            try
            {
                if (cancelImmediately)
                {
                    return await _subscriptionService.CancelAsync(subscriptionId);
                }
                else
                {
                    var options = new SubscriptionUpdateOptions { CancelAtPeriodEnd = true };
                    return await _subscriptionService.UpdateAsync(subscriptionId, options);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error canceling subscription: {SubscriptionId}", subscriptionId);
                return null;
            }
        }

        public async Task<IEnumerable<Subscription>> ListCustomerSubscriptionsAsync(string customerId)
        {
            try
            {
                var options = new SubscriptionListOptions { Customer = customerId };
                var subscriptions = await _subscriptionService.ListAsync(options);
                return subscriptions.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing subscriptions for customer: {CustomerId}", customerId);
                return new List<Subscription>();
            }
        }
        #endregion

        #region Products and Prices
        public async Task<Product?> CreateProductAsync(string name, string? description = null, Dictionary<string, string>? metadata = null)
        {
            try
            {
                var options = new ProductCreateOptions
                {
                    Name = name,
                    Description = description,
                    Metadata = metadata
                };
                return await _productService.CreateAsync(options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating product");
                return null;
            }
        }

        public async Task<Product?> GetProductAsync(string productId)
        {
            try
            {
                return await _productService.GetAsync(productId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting product: {ProductId}", productId);
                return null;
            }
        }

        public async Task<Product?> UpdateProductAsync(string productId, string? name = null, string? description = null)
        {
            try
            {
                var options = new ProductUpdateOptions();
                if (!string.IsNullOrEmpty(name)) options.Name = name;
                if (!string.IsNullOrEmpty(description)) options.Description = description;

                return await _productService.UpdateAsync(productId, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating product: {ProductId}", productId);
                return null;
            }
        }

        public async Task<IEnumerable<Product>> ListProductsAsync(int limit = 10)
        {
            try
            {
                var options = new ProductListOptions { Limit = limit };
                var products = await _productService.ListAsync(options);
                return products.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing products");
                return new List<Product>();
            }
        }

        public async Task<Price?> CreatePriceAsync(string productId, long unitAmount, string currency, string interval = "month")
        {
            try
            {
                var options = new PriceCreateOptions
                {
                    Product = productId,
                    UnitAmount = unitAmount,
                    Currency = currency,
                    Recurring = new PriceRecurringOptions { Interval = interval }
                };
                return await _priceService.CreateAsync(options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating price");
                return null;
            }
        }

        public async Task<Price?> GetPriceAsync(string priceId)
        {
            try
            {
                return await _priceService.GetAsync(priceId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting price: {PriceId}", priceId);
                return null;
            }
        }

        public async Task<IEnumerable<Price>> ListPricesAsync(string? productId = null, int limit = 10)
        {
            try
            {
                var options = new PriceListOptions { Limit = limit };
                if (!string.IsNullOrEmpty(productId)) options.Product = productId;

                var prices = await _priceService.ListAsync(options);
                return prices.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing prices");
                return new List<Price>();
            }
        }
        #endregion

        #region Invoices (Interface Methods)
        public async Task<Stripe.Invoice?> CreateInvoiceAsync(string customerId, string? subscriptionId = null)
        {
            try
            {
                var options = new InvoiceCreateOptions
                {
                    Customer = customerId,
                    Subscription = subscriptionId
                };
                return await _invoiceService.CreateAsync(options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating invoice");
                return null;
            }
        }

        public async Task<Stripe.Invoice?> GetInvoiceAsync(string invoiceId)
        {
            try
            {
                return await _invoiceService.GetAsync(invoiceId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice: {InvoiceId}", invoiceId);
                return null;
            }
        }

        public async Task<Stripe.Invoice?> FinalizeInvoiceAsync(string invoiceId)
        {
            try
            {
                return await _invoiceService.FinalizeInvoiceAsync(invoiceId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finalizing invoice: {InvoiceId}", invoiceId);
                return null;
            }
        }

        public async Task<Stripe.Invoice?> PayInvoiceAsync(string invoiceId)
        {
            try
            {
                return await _invoiceService.PayAsync(invoiceId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error paying invoice: {InvoiceId}", invoiceId);
                return null;
            }
        }

        public async Task<IEnumerable<Stripe.Invoice>> ListCustomerInvoicesAsync(string customerId, int limit = 10)
        {
            try
            {
                var options = new InvoiceListOptions
                {
                    Customer = customerId,
                    Limit = limit
                };
                var invoices = await _invoiceService.ListAsync(options);
                return invoices.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing invoices for customer: {CustomerId}", customerId);
                return new List<Stripe.Invoice>();
            }
        }
        #endregion

        #region Webhooks (Interface Methods)
        public async Task<Event?> ConstructWebhookEventAsync(string payload, string signature, string endpointSecret)
        {
            try
            {
                return EventUtility.ConstructEvent(payload, signature, endpointSecret);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error constructing webhook event");
                return null;
            }
        }

        public async Task<bool> HandleWebhookEventAsync(Event stripeEvent)
        {
            try
            {
                _logger.LogInformation("Handling webhook event: {EventType}", stripeEvent.Type);
                // Add webhook event handling logic here
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling webhook event: {EventType}", stripeEvent.Type);
                return false;
            }
        }
        #endregion

        #region Charges
        public async Task<Charge?> CreateChargeAsync(long amount, string currency, string source, string? description = null)
        {
            try
            {
                var options = new ChargeCreateOptions
                {
                    Amount = amount,
                    Currency = currency,
                    Source = source,
                    Description = description
                };
                return await _chargeService.CreateAsync(options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating charge");
                return null;
            }
        }

        public async Task<Charge?> GetChargeAsync(string chargeId)
        {
            try
            {
                return await _chargeService.GetAsync(chargeId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting charge: {ChargeId}", chargeId);
                return null;
            }
        }

        public async Task<Refund?> RefundChargeAsync(string chargeId, long? amount = null, string? reason = null)
        {
            try
            {
                var options = new RefundCreateOptions
                {
                    Charge = chargeId,
                    Amount = amount,
                    Reason = reason
                };
                return await _refundService.CreateAsync(options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refunding charge: {ChargeId}", chargeId);
                return null;
            }
        }
        #endregion

        #region Setup Intents
        public async Task<SetupIntent?> CreateSetupIntentAsync(string customerId, string? paymentMethodId = null)
        {
            try
            {
                var options = new SetupIntentCreateOptions
                {
                    Customer = customerId,
                    PaymentMethod = paymentMethodId
                };
                return await _setupIntentService.CreateAsync(options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating setup intent");
                return null;
            }
        }

        public async Task<SetupIntent?> GetSetupIntentAsync(string setupIntentId)
        {
            try
            {
                return await _setupIntentService.GetAsync(setupIntentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting setup intent: {SetupIntentId}", setupIntentId);
                return null;
            }
        }

        public async Task<SetupIntent?> ConfirmSetupIntentAsync(string setupIntentId, string? paymentMethodId = null)
        {
            try
            {
                var options = new SetupIntentConfirmOptions();
                if (!string.IsNullOrEmpty(paymentMethodId)) options.PaymentMethod = paymentMethodId;

                return await _setupIntentService.ConfirmAsync(setupIntentId, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error confirming setup intent: {SetupIntentId}", setupIntentId);
                return null;
            }
        }
        #endregion

        #region Coupons and Discounts
        public async Task<Coupon?> CreateCouponAsync(string id, int? percentOff = null, long? amountOff = null, string? currency = null, int? durationInMonths = null)
        {
            try
            {
                var options = new CouponCreateOptions
                {
                    Id = id,
                    PercentOff = percentOff,
                    AmountOff = amountOff,
                    Currency = currency,
                    DurationInMonths = durationInMonths
                };
                return await _couponService.CreateAsync(options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating coupon");
                return null;
            }
        }

        public async Task<Coupon?> GetCouponAsync(string couponId)
        {
            try
            {
                return await _couponService.GetAsync(couponId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting coupon: {CouponId}", couponId);
                return null;
            }
        }

        public async Task<bool> DeleteCouponAsync(string couponId)
        {
            try
            {
                await _couponService.DeleteAsync(couponId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting coupon: {CouponId}", couponId);
                return false;
            }
        }
        #endregion

        #region Balance and Transfers
        public async Task<Balance?> GetBalanceAsync()
        {
            try
            {
                return await _balanceService.GetAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting balance");
                return null;
            }
        }

        public async Task<IEnumerable<BalanceTransaction>> ListBalanceTransactionsAsync(int limit = 10)
        {
            try
            {
                var options = new BalanceTransactionListOptions { Limit = limit };
                var transactions = await _balanceTransactionService.ListAsync(options);
                return transactions.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing balance transactions");
                return new List<BalanceTransaction>();
            }
        }
        #endregion
    }
}

