using FluentAssertions;
using Microsoft.Extensions.Logging;
using MicroSaasWebApi.Config.Database;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers;
using MicroSaasWebApi.Tests.Common;
using Moq;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Services
{
    public class UserServiceTests : TestBase
    {
        private readonly Mock<DapperDbContext> _mockDbContext;
        private readonly Mock<ILogger<UserService>> _mockLogger;
        private readonly UserService _userService;

        public UserServiceTests(ITestOutputHelper output) : base(output)
        {
            _mockDbContext = new Mock<DapperDbContext>();
            _mockLogger = new Mock<ILogger<UserService>>();
            _userService = new UserService(_mockDbContext.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task GetAllUsersAsync_ReturnsAllUsers()
        {
            // Arrange
            var users = TestDataBuilders.Users.ValidUser.Generate(5);

            _mockDbContext.Setup(x => x.QueryAsync<User>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(users);

            // Act
            var result = await _userService.GetAllUsersAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(5);
        }

        [Fact]
        public async Task GetUserByIdAsync_WithValidId_ReturnsUser()
        {
            // Arrange
            var userId = TestHelpers.GenerateTestId("user");
            var user = TestDataBuilders.Users.ValidUser.Generate();
            user.Id = userId;

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<User>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(user);

            // Act
            var result = await _userService.GetUserByIdAsync(userId);

            // Assert
            result.Should().NotBeNull();
            result!.Id.Should().Be(userId);
        }

        [Fact]
        public async Task GetUserByIdAsync_WithInvalidId_ReturnsNull()
        {
            // Arrange
            var userId = "non-existent-id";

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<User>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync((User?)null);

            // Act
            var result = await _userService.GetUserByIdAsync(userId);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task GetUserByEmailAsync_WithValidEmail_ReturnsUser()
        {
            // Arrange
            var email = "<EMAIL>";
            var user = TestDataBuilders.Users.ValidUser.Generate();
            user.Email = email;

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<User>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(user);

            // Act
            var result = await _userService.GetUserByEmailAsync(email);

            // Assert
            result.Should().NotBeNull();
            result!.Email.Should().Be(email);
        }

        [Fact]
        public async Task GetUserByClerkIdAsync_WithValidClerkId_ReturnsUser()
        {
            // Arrange
            var clerkUserId = "clerk_123456";
            var user = TestDataBuilders.Users.ValidUser.Generate();
            user.ClerkUserId = clerkUserId;

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<User>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(user);

            // Act
            var result = await _userService.GetUserByClerkIdAsync(clerkUserId);

            // Assert
            result.Should().NotBeNull();
            result!.ClerkUserId.Should().Be(clerkUserId);
        }

        [Fact]
        public async Task CreateUserAsync_WithValidRequest_ReturnsCreatedUser()
        {
            // Arrange
            var request = TestDataBuilders.Users.CreateUserRequest.Generate();
            var createdUser = TestDataBuilders.Users.ValidUser.Generate();
            createdUser.Email = request.Email;
            createdUser.FullName = request.FullName;

            _mockDbContext.Setup(x => x.QuerySingleAsync<bool>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(false); // Email doesn't exist

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<User>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(createdUser);

            // Act
            var result = await _userService.CreateUserAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Email.Should().Be(request.Email);
            result.FullName.Should().Be(request.FullName);
        }

        [Fact]
        public async Task CreateUserAsync_WithExistingEmail_ThrowsException()
        {
            // Arrange
            var request = TestDataBuilders.Users.CreateUserRequest.Generate();

            _mockDbContext.Setup(x => x.QuerySingleAsync<bool>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(true); // Email already exists

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => _userService.CreateUserAsync(request));
        }

        [Fact]
        public async Task UpdateUserAsync_WithValidRequest_ReturnsUpdatedUser()
        {
            // Arrange
            var userId = TestHelpers.GenerateTestId("user");
            var request = new UpdateUserRequest
            {
                Id = userId,
                Email = "<EMAIL>",
                FullName = "Updated Name"
            };

            var updatedUser = TestDataBuilders.Users.ValidUser.Generate();
            updatedUser.Id = userId;
            updatedUser.Email = request.Email!;
            updatedUser.FullName = request.FullName!;

            _mockDbContext.Setup(x => x.QuerySingleAsync<bool>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(true); // User exists

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<User>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(updatedUser);

            // Act
            var result = await _userService.UpdateUserAsync(request);

            // Assert
            result.Should().NotBeNull();
            result!.Id.Should().Be(userId);
            result.Email.Should().Be(request.Email);
            result.FullName.Should().Be(request.FullName);
        }

        [Fact]
        public async Task UpdateUserAsync_WithNonExistentUser_ReturnsNull()
        {
            // Arrange
            var request = new UpdateUserRequest
            {
                Id = "non-existent-id",
                Email = "<EMAIL>"
            };

            _mockDbContext.Setup(x => x.QuerySingleAsync<bool>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(false); // User doesn't exist

            // Act
            var result = await _userService.UpdateUserAsync(request);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task DeleteUserAsync_WithValidId_ReturnsTrue()
        {
            // Arrange
            var userId = TestHelpers.GenerateTestId("user");

            _mockDbContext.Setup(x => x.QuerySingleAsync<bool>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(true); // User exists

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            // Act
            var result = await _userService.DeleteUserAsync(userId);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task DeleteUserAsync_WithInvalidId_ReturnsFalse()
        {
            // Arrange
            var userId = "non-existent-id";

            _mockDbContext.Setup(x => x.QuerySingleAsync<bool>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(false); // User doesn't exist

            // Act
            var result = await _userService.DeleteUserAsync(userId);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task UpdateLoginStatusAsync_WithValidId_ReturnsTrue()
        {
            // Arrange
            var userId = TestHelpers.GenerateTestId("user");
            var loggedIn = true;

            _mockDbContext.Setup(x => x.QuerySingleAsync<bool>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(true); // User exists

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            // Act
            var result = await _userService.UpdateLoginStatusAsync(userId, loggedIn);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task UserExistsAsync_WithValidId_ReturnsTrue()
        {
            // Arrange
            var userId = TestHelpers.GenerateTestId("user");

            _mockDbContext.Setup(x => x.QuerySingleAsync<bool>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(true);

            // Act
            var result = await _userService.UserExistsAsync(userId);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task EmailExistsAsync_WithValidEmail_ReturnsTrue()
        {
            // Arrange
            var email = "<EMAIL>";

            _mockDbContext.Setup(x => x.QuerySingleAsync<bool>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(true);

            // Act
            var result = await _userService.EmailExistsAsync(email);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task GetUsersByTypeAsync_WithValidType_ReturnsUsers()
        {
            // Arrange
            var userType = SnbUserType.Buyer;
            var users = TestDataBuilders.Users.ValidUser.Generate(3);
            users.ForEach(u => u.UserType = userType);

            _mockDbContext.Setup(x => x.QueryAsync<User>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(users);

            // Act
            var result = await _userService.GetUsersByTypeAsync(userType);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(3);
            result.Should().OnlyContain(u => u.UserType == userType);
        }

        [Theory]
        [InlineData(UserRole.USER)]
        [InlineData(UserRole.ADMIN)]
        [InlineData(UserRole.PRODUCT)]
        public async Task CreateUserAsync_WithDifferentRoles_CreatesUserCorrectly(UserRole role)
        {
            // Arrange
            var request = TestDataBuilders.Users.CreateUserRequest.Generate();
            request.Role = role;

            var createdUser = TestDataBuilders.Users.ValidUser.Generate();
            createdUser.Role = role;

            _mockDbContext.Setup(x => x.QuerySingleAsync<bool>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(false);

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<User>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(createdUser);

            // Act
            var result = await _userService.CreateUserAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Role.Should().Be(role);
        }

        [Theory]
        [InlineData(SnbUserType.Buyer)]
        [InlineData(SnbUserType.Seller)]
        public async Task CreateUserAsync_WithDifferentUserTypes_CreatesUserCorrectly(SnbUserType userType)
        {
            // Arrange
            var request = TestDataBuilders.Users.CreateUserRequest.Generate();
            request.UserType = userType;

            var createdUser = TestDataBuilders.Users.ValidUser.Generate();
            createdUser.UserType = userType;

            _mockDbContext.Setup(x => x.QuerySingleAsync<bool>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(false);

            _mockDbContext.Setup(x => x.ExecuteAsync(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(1);

            _mockDbContext.Setup(x => x.QueryFirstOrDefaultAsync<User>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(createdUser);

            // Act
            var result = await _userService.CreateUserAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.UserType.Should().Be(userType);
        }
    }
}
