const Landing = () => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.4286 32.0002H3.42857C2.51926 32.0002 1.64719 31.639 1.00421 30.996C0.361224 30.353 0 29.481 0 28.5716V20.5716C0 19.6623 0.361224 18.7903 1.00421 18.1473C1.64719 17.5043 2.51926 17.1431 3.42857 17.1431H11.4286C12.3379 17.1431 13.21 17.5043 13.8529 18.1473C14.4959 18.7903 14.8571 19.6623 14.8571 20.5716V28.5716C14.8571 29.481 14.4959 30.353 13.8529 30.996C13.21 31.639 12.3379 32.0002 11.4286 32.0002ZM3.42857 19.4288C3.12547 19.4288 2.83478 19.5492 2.62045 19.7635C2.40612 19.9778 2.28571 20.2685 2.28571 20.5716V28.5716C2.28571 28.8747 2.40612 29.1654 2.62045 29.3798C2.83478 29.5941 3.12547 29.7145 3.42857 29.7145H11.4286C11.7317 29.7145 12.0224 29.5941 12.2367 29.3798C12.451 29.1654 12.5714 28.8747 12.5714 28.5716V20.5716C12.5714 20.2685 12.451 19.9778 12.2367 19.7635C12.0224 19.5492 11.7317 19.4288 11.4286 19.4288H3.42857Z"
        fill="currentColor"
      />
      <path
        d="M28.5692 31.9997H20.5692C19.6599 31.9997 18.7878 31.6385 18.1448 30.9955C17.5018 30.3525 17.1406 29.4805 17.1406 28.5711V20.5711C17.1406 19.6618 17.5018 18.7898 18.1448 18.1468C18.7878 17.5038 19.6599 17.1426 20.5692 17.1426H28.5692C29.4785 17.1426 30.3506 17.5038 30.9936 18.1468C31.6365 18.7898 31.9978 19.6618 31.9978 20.5711V28.5711C31.9978 29.4805 31.6365 30.3525 30.9936 30.9955C30.3506 31.6385 29.4785 31.9997 28.5692 31.9997ZM20.5692 19.4283C20.2661 19.4283 19.9754 19.5487 19.7611 19.763C19.5467 19.9774 19.4263 20.268 19.4263 20.5711V28.5711C19.4263 28.8743 19.5467 29.1649 19.7611 29.3793C19.9754 29.5936 20.2661 29.714 20.5692 29.714H28.5692C28.8723 29.714 29.163 29.5936 29.3773 29.3793C29.5916 29.1649 29.7121 28.8743 29.7121 28.5711V20.5711C29.7121 20.268 29.5916 19.9774 29.3773 19.763C29.163 19.5487 28.8723 19.4283 28.5692 19.4283H20.5692Z"
        fill="currentColor"
      />
      <path
        d="M11.4286 14.8571H3.42857C2.51926 14.8571 1.64719 14.4959 1.00421 13.8529C0.361224 13.21 0 12.3379 0 11.4286V3.42857C0 2.51926 0.361224 1.64719 1.00421 1.00421C1.64719 0.361224 2.51926 0 3.42857 0H11.4286C12.3379 0 13.21 0.361224 13.8529 1.00421C14.4959 1.64719 14.8571 2.51926 14.8571 3.42857V11.4286C14.8571 12.3379 14.4959 13.21 13.8529 13.8529C13.21 14.4959 12.3379 14.8571 11.4286 14.8571ZM3.42857 2.28571C3.12547 2.28571 2.83478 2.40612 2.62045 2.62045C2.40612 2.83478 2.28571 3.12547 2.28571 3.42857V11.4286C2.28571 11.7317 2.40612 12.0224 2.62045 12.2367C2.83478 12.451 3.12547 12.5714 3.42857 12.5714H11.4286C11.7317 12.5714 12.0224 12.451 12.2367 12.2367C12.451 12.0224 12.5714 11.7317 12.5714 11.4286V3.42857C12.5714 3.12547 12.451 2.83478 12.2367 2.62045C12.0224 2.40612 11.7317 2.28571 11.4286 2.28571H3.42857Z"
        fill="currentColor"
      />
      <path
        d="M28.5692 14.8571H20.5692C19.6599 14.8571 18.7878 14.4959 18.1448 13.8529C17.5018 13.21 17.1406 12.3379 17.1406 11.4286V3.42857C17.1406 2.51926 17.5018 1.64719 18.1448 1.00421C18.7878 0.361224 19.6599 0 20.5692 0H28.5692C29.4785 0 30.3506 0.361224 30.9936 1.00421C31.6365 1.64719 31.9978 2.51926 31.9978 3.42857V11.4286C31.9978 12.3379 31.6365 13.21 30.9936 13.8529C30.3506 14.4959 29.4785 14.8571 28.5692 14.8571ZM20.5692 2.28571C20.2661 2.28571 19.9754 2.40612 19.7611 2.62045C19.5467 2.83478 19.4263 3.12547 19.4263 3.42857V11.4286C19.4263 11.7317 19.5467 12.0224 19.7611 12.2367C19.9754 12.451 20.2661 12.5714 20.5692 12.5714H28.5692C28.8723 12.5714 29.163 12.451 29.3773 12.2367C29.5916 12.0224 29.7121 11.7317 29.7121 11.4286V3.42857C29.7121 3.12547 29.5916 2.83478 29.3773 2.62045C29.163 2.40612 28.8723 2.28571 28.5692 2.28571H20.5692Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default Landing;
