using MicroSaasWebApi.Models.SoNoBrokers;

namespace MicroSaasWebApi.Services.SoNoBrokers.Interfaces
{
    public interface IPropertyImageService
    {
        Task<IEnumerable<PropertyImage>> GetPropertyImagesAsync(string propertyId);
        Task<PropertyImage?> GetPropertyImageByIdAsync(string id);
        Task<PropertyImage> CreatePropertyImageAsync(PropertyImage propertyImage);
        Task<PropertyImage> UpdatePropertyImageAsync(PropertyImage propertyImage);
        Task<bool> DeletePropertyImageAsync(string id);
        Task<bool> SetPrimaryImageAsync(string propertyId, string imageId);
        Task<PropertyImage?> GetPrimaryImageAsync(string propertyId);
        Task<IEnumerable<PropertyImage>> UploadImagesAsync(string propertyId, IFormFileCollection files);
        Task<bool> DeletePropertyImagesAsync(string propertyId);
    }
}
