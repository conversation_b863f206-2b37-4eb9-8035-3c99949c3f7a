using MicroSaasWebApi.Models.SoNoBrokers;

namespace MicroSaasWebApi.Services.SoNoBrokers.Interfaces
{
    public interface IAdminService
    {
        Task<AdminDashboardResponse> GetDashboardDataAsync();
        Task<List<AdminUserResponse>> GetUsersAsync();
        Task<AdminUserResponse?> GetUserByIdAsync(string id);
        Task<bool> UpdateUserRoleAsync(UpdateUserRoleRequest request);
        Task<bool> UpdateUserStatusAsync(UpdateUserStatusRequest request);
        Task<List<RolePermissionResponse>> GetRolePermissionsAsync();
        Task<RolePermissionResponse> CreateRolePermissionAsync(CreateRolePermissionRequest request);
        Task<bool> UpdateRolePermissionAsync(UpdateRolePermissionRequest request);
        Task<bool> DeleteRolePermissionAsync(string id);
        Task<StripeSyncResponse> SyncStripeDataAsync(StripeSyncRequest request);
    }
}
