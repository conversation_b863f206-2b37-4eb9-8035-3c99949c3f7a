using MicroSaasWebApi.Models.SoNoBrokers;

namespace MicroSaasWebApi.Services.SoNoBrokers.Interfaces
{
    public interface ICommunicationService
    {
        Task<ContactConciergeResponse> SendConciergeInquiryAsync(ContactConciergeRequest request);
        Task<WaitingListResponse> AddToWaitingListAsync(WaitingListRequest request);
        Task<GeoLocationResponse> GetLocationByIpAsync(GeoLocationRequest request);
        Task<EnumValuesResponse> GetEnumValuesAsync();
    }
}
