'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import {
  Mail,
  DollarSign,
  Calendar,
  Clock,
  User,
  Home,
  TrendingUp,
  Search,
  Filter,
  Eye,
  MessageSquare,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'
// TODO: Move types to separate file to avoid server-only import
// import { ContactShare, ContactShareStats, ContactShareType, ContactShareStatus } from '@/lib/api/contact-sharing-api'

// Temporary type definitions (should be moved to @/types)
interface ContactShare {
  id: string
  propertyId: string
  buyerId: string
  sellerId: string
  buyerName: string
  buyerEmail: string
  buyerPhone?: string
  message?: string
  shareType: ContactShareType
  offerAmount?: number
  status: ContactShareStatus
  emailSent: boolean
  emailSentAt?: Date
  createdAt: Date
  updatedAt: Date
}

interface ContactShareStats {
  total: number
  pending: number
  responded: number
  accepted: number
  declined: number
}

enum ContactShareType {
  ContactRequest = 'ContactRequest',
  PropertyOffer = 'PropertyOffer',
  ScheduleVisit = 'ScheduleVisit',
  OfferWithVisit = 'OfferWithVisit'
}

enum ContactShareStatus {
  Sent = 'Sent',
  Viewed = 'Viewed',
  Responded = 'Responded',
  Accepted = 'Accepted',
  Declined = 'Declined',
  Expired = 'Expired'
}

interface ContactSharesDashboardProps {
  initialContactShares: ContactShare[]
  stats: ContactShareStats
  pagination: {
    page: number
    totalPages: number
    total: number
    hasMore: boolean
  }
}

export function ContactSharesDashboard({
  initialContactShares,
  stats,
  pagination
}: ContactSharesDashboardProps) {
  const [contactShares] = useState(initialContactShares)
  const [searchTerm, setSearchTerm] = useState('')
  const [activeTab, setActiveTab] = useState('all')

  // Filter contact shares based on active tab and search
  const filteredContactShares = contactShares.filter(share => {
    const matchesSearch = searchTerm === '' ||
      share.propertyTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      share.sellerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      share.buyerName.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesTab = activeTab === 'all' ||
      (activeTab === 'contact' && share.shareType === ContactShareType.ContactRequest) ||
      (activeTab === 'offers' && (share.shareType === ContactShareType.PropertyOffer || share.shareType === ContactShareType.OfferWithVisit)) ||
      (activeTab === 'visits' && (share.shareType === ContactShareType.ScheduleVisit || share.shareType === ContactShareType.OfferWithVisit)) ||
      (activeTab === 'pending' && [ContactShareStatus.Sent, ContactShareStatus.Delivered, ContactShareStatus.Viewed].includes(share.status))

    return matchesSearch && matchesTab
  })

  const getStatusIcon = (status: ContactShareStatus) => {
    switch (status) {
      case ContactShareStatus.Accepted:
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case ContactShareStatus.Declined:
        return <XCircle className="h-4 w-4 text-red-500" />
      case ContactShareStatus.Responded:
        return <MessageSquare className="h-4 w-4 text-blue-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
    }
  }

  const getStatusColor = (status: ContactShareStatus) => {
    switch (status) {
      case ContactShareStatus.Accepted:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case ContactShareStatus.Declined:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case ContactShareStatus.Responded:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      default:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
    }
  }

  const getShareTypeIcon = (shareType: ContactShareType) => {
    switch (shareType) {
      case ContactShareType.PropertyOffer:
      case ContactShareType.OfferWithVisit:
        return <DollarSign className="h-4 w-4" />
      case ContactShareType.ScheduleVisit:
        return <Calendar className="h-4 w-4" />
      default:
        return <Mail className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Contacts
                </p>
                <p className="text-2xl font-bold">{stats.totalContactShares}</p>
              </div>
              <Mail className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Offers Made
                </p>
                <p className="text-2xl font-bold">{stats.propertyOffers}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Visits Requested
                </p>
                <p className="text-2xl font-bold">{stats.scheduleRequests}</p>
              </div>
              <Calendar className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Pending Responses
                </p>
                <p className="text-2xl font-bold">{stats.pendingResponses}</p>
              </div>
              <Clock className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by property, seller, or buyer..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Contact Shares Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">All ({contactShares.length})</TabsTrigger>
          <TabsTrigger value="contact">Contact ({stats.contactRequests})</TabsTrigger>
          <TabsTrigger value="offers">Offers ({stats.propertyOffers})</TabsTrigger>
          <TabsTrigger value="visits">Visits ({stats.scheduleRequests})</TabsTrigger>
          <TabsTrigger value="pending">Pending ({stats.pendingResponses})</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {filteredContactShares.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <MessageSquare className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  No contact shares found
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-center">
                  {searchTerm ? 'Try adjusting your search terms.' : 'Start contacting sellers about properties you\'re interested in.'}
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredContactShares.map((share) => (
              <Card key={share.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
                        {getShareTypeIcon(share.shareType)}
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg">{share.propertyTitle}</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {share.propertyAddress}
                        </p>
                        <p className="text-lg font-bold text-primary mt-1">
                          ${share.propertyPrice.toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(share.status)}>
                        {getStatusIcon(share.status)}
                        <span className="ml-1">{share.statusDisplay}</span>
                      </Badge>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Share Type
                      </p>
                      <p className="text-sm">{share.shareTypeDisplay}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Seller
                      </p>
                      <p className="text-sm">{share.sellerName}</p>
                    </div>
                    {share.offerAmount && (
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Offer Amount
                        </p>
                        <p className="text-sm font-bold text-green-600">
                          ${share.offerAmount.toLocaleString()}
                        </p>
                      </div>
                    )}
                    {share.preferredVisitDate && (
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Preferred Visit Date
                        </p>
                        <p className="text-sm">
                          {new Date(share.preferredVisitDate).toLocaleDateString()}
                          {share.preferredVisitTime && ` at ${share.preferredVisitTime}`}
                        </p>
                      </div>
                    )}
                  </div>

                  {share.message && (
                    <div className="mb-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                        Message
                      </p>
                      <p className="text-sm bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                        {share.message}
                      </p>
                    </div>
                  )}

                  {share.sellerResponse && (
                    <div className="mb-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                        Seller Response
                      </p>
                      <p className="text-sm bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                        {share.sellerResponse}
                      </p>
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="text-xs text-gray-500">
                      <p>Sent: {new Date(share.createdAt).toLocaleDateString()}</p>
                      {share.respondedAt && (
                        <p>Responded: {new Date(share.respondedAt).toLocaleDateString()}</p>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                      <Button variant="outline" size="sm">
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Message
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>
      </Tabs>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center">
          <div className="flex gap-2">
            <Button variant="outline" disabled={pagination.page === 1}>
              Previous
            </Button>
            <span className="flex items-center px-4 text-sm">
              Page {pagination.page} of {pagination.totalPages}
            </span>
            <Button variant="outline" disabled={!pagination.hasMore}>
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
