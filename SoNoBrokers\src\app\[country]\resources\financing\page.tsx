import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function FinancingPage({ params, searchParams }: PageProps) {
  // This page doesn't require authentication - it's a public resource
  const isSignedIn = false // Not needed for this page

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us', 'uae']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/resources/financing')
  }

  const userType = resolvedSearchParams.userType || 'buyer'

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8">
            Home Financing Options
          </h1>

          <div className="bg-card rounded-lg p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-4">Explore Your Financing Options</h2>
            <p className="text-muted-foreground mb-6">
              Discover the best mortgage products, down payment assistance programs, and loan options
              available in {country.toUpperCase()}. Find the financing solution that fits your needs and budget.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <div className="bg-card rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Mortgage Types</h3>
              <div className="space-y-4">
                <div className="border-l-4 border-primary pl-4">
                  <h4 className="font-semibold">Fixed-Rate Mortgage</h4>
                  <p className="text-sm text-muted-foreground">
                    Stable monthly payments with interest rate locked for the entire term.
                  </p>
                </div>

                <div className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-semibold">Variable-Rate Mortgage</h4>
                  <p className="text-sm text-muted-foreground">
                    Interest rate fluctuates with market conditions, potentially lower initial rates.
                  </p>
                </div>

                <div className="border-l-4 border-green-500 pl-4">
                  <h4 className="font-semibold">Hybrid ARM</h4>
                  <p className="text-sm text-muted-foreground">
                    Fixed rate for initial period, then adjusts periodically.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Down Payment Options</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                  <span className="font-semibold">Conventional</span>
                  <span className="text-sm">5-20%</span>
                </div>

                <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                  <span className="font-semibold">FHA/CMHC</span>
                  <span className="text-sm">3.5-5%</span>
                </div>

                <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                  <span className="font-semibold">VA/Military</span>
                  <span className="text-sm">0%</span>
                </div>

                <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                  <span className="font-semibold">First-Time Buyer</span>
                  <span className="text-sm">0-3%</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6 mb-8">
            <h3 className="text-xl font-semibold mb-4">First-Time Buyer Programs</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Government Programs</h4>
                <ul className="space-y-2 text-sm">
                  <li>• First-Time Home Buyer Incentive</li>
                  <li>• Home Buyers' Plan (RRSP withdrawal)</li>
                  <li>• Provincial down payment assistance</li>
                  <li>• Tax credits and rebates</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Lender Programs</h4>
                <ul className="space-y-2 text-sm">
                  <li>• Reduced down payment options</li>
                  <li>• Flexible income verification</li>
                  <li>• Gifted down payment acceptance</li>
                  <li>• Rate discounts for new buyers</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-6 mb-8">
            <div className="bg-card rounded-lg p-6 text-center">
              <div className="text-2xl font-bold text-primary mb-2">5.25%</div>
              <div className="text-sm text-muted-foreground">Average Fixed Rate</div>
              <div className="text-xs text-muted-foreground mt-1">5-Year Term</div>
            </div>

            <div className="bg-card rounded-lg p-6 text-center">
              <div className="text-2xl font-bold text-primary mb-2">4.95%</div>
              <div className="text-sm text-muted-foreground">Average Variable Rate</div>
              <div className="text-xs text-muted-foreground mt-1">Current</div>
            </div>

            <div className="bg-card rounded-lg p-6 text-center">
              <div className="text-2xl font-bold text-primary mb-2">25</div>
              <div className="text-sm text-muted-foreground">Max Amortization</div>
              <div className="text-xs text-muted-foreground mt-1">Years</div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4">Pre-Approval Process</h3>
            <div className="grid md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-2">1</div>
                <h4 className="font-semibold text-sm">Gather Documents</h4>
                <p className="text-xs text-muted-foreground">Income, assets, credit</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-2">2</div>
                <h4 className="font-semibold text-sm">Submit Application</h4>
                <p className="text-xs text-muted-foreground">Online or in-person</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-2">3</div>
                <h4 className="font-semibold text-sm">Credit Check</h4>
                <p className="text-xs text-muted-foreground">Lender verification</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-2">4</div>
                <h4 className="font-semibold text-sm">Get Approved</h4>
                <p className="text-xs text-muted-foreground">Rate hold period</p>
              </div>
            </div>

            <div className="mt-6 text-center">
              <button className="bg-primary text-primary-foreground px-8 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors">
                Start Pre-Approval
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'buyer'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Home Financing Options for ${country} | SoNoBrokers`,
    description: `Explore mortgage types, down payment assistance, and loan programs in ${country}. Find the best financing solution for your home purchase.`,
    keywords: `home financing, mortgage options, down payment assistance, ${country}, home loans`,
  }
}
