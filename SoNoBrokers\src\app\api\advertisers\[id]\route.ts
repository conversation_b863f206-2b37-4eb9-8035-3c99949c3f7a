import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { AdvertiserService } from '@/services/advertiserService'
import { UpdateAdvertiserRequest } from '@/types/advertiser'

interface RouteParams {
  params: Promise<{ id: string }>
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const resolvedParams = await params
    const advertiser = await AdvertiserService.getAdvertiser(resolvedParams.id)
    
    if (!advertiser) {
      return NextResponse.json({ error: 'Advertiser not found' }, { status: 404 })
    }

    return NextResponse.json(advertiser)
  } catch (error) {
    console.error('Error fetching advertiser:', error)
    return NextResponse.json(
      { error: 'Failed to fetch advertiser' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const resolvedParams = await params
    const body: UpdateAdvertiserRequest = await request.json()
    
    // Check if user owns this advertiser or is admin
    const existingAdvertiser = await AdvertiserService.getAdvertiser(resolvedParams.id)
    if (!existingAdvertiser) {
      return NextResponse.json({ error: 'Advertiser not found' }, { status: 404 })
    }

    // TODO: Add proper authorization check
    // if (existingAdvertiser.userId !== userId && !isAdmin) {
    //   return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    // }

    const updatedAdvertiser = await AdvertiserService.updateAdvertiser(resolvedParams.id, body)
    
    return NextResponse.json(updatedAdvertiser)
  } catch (error) {
    console.error('Error updating advertiser:', error)
    return NextResponse.json(
      { error: 'Failed to update advertiser' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const resolvedParams = await params
    
    // Check if user owns this advertiser or is admin
    const existingAdvertiser = await AdvertiserService.getAdvertiser(resolvedParams.id)
    if (!existingAdvertiser) {
      return NextResponse.json({ error: 'Advertiser not found' }, { status: 404 })
    }

    // TODO: Add proper authorization check
    // if (existingAdvertiser.userId !== userId && !isAdmin) {
    //   return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    // }

    await AdvertiserService.deleteAdvertiser(resolvedParams.id)
    
    return NextResponse.json({ message: 'Advertiser deleted successfully' })
  } catch (error) {
    console.error('Error deleting advertiser:', error)
    return NextResponse.json(
      { error: 'Failed to delete advertiser' },
      { status: 500 }
    )
  }
}
