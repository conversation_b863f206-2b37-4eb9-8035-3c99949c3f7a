# PowerShell script to test the migrated API endpoints
# Run this script to verify all endpoints are working correctly

param(
    [string]$BaseUrl = "https://localhost:7001",
    [string]$AuthToken = ""
)

Write-Host "Testing SoNoBrokers API Migration" -ForegroundColor Green
Write-Host "Base URL: $BaseUrl" -ForegroundColor Yellow

# Function to make HTTP requests
function Invoke-ApiTest {
    param(
        [string]$Method,
        [string]$Endpoint,
        [string]$Body = "",
        [bool]$RequireAuth = $false,
        [string]$Description
    )
    
    Write-Host "`nTesting: $Description" -ForegroundColor Cyan
    Write-Host "Endpoint: $Method $Endpoint" -ForegroundColor Gray
    
    try {
        $headers = @{
            "Content-Type" = "application/json"
        }
        
        if ($RequireAuth -and $AuthToken) {
            $headers["Authorization"] = "Bearer $AuthToken"
        }
        
        $params = @{
            Uri = "$BaseUrl$Endpoint"
            Method = $Method
            Headers = $headers
        }
        
        if ($Body) {
            $params["Body"] = $Body
        }
        
        $response = Invoke-RestMethod @params
        Write-Host "✅ SUCCESS" -ForegroundColor Green
        
        if ($response) {
            Write-Host "Response preview:" -ForegroundColor Gray
            $response | ConvertTo-Json -Depth 2 | Write-Host
        }
        
        return $true
    }
    catch {
        Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test results tracking
$testResults = @()

Write-Host "`n=== Testing Communication APIs ===" -ForegroundColor Magenta

# Test Geo Location
$result = Invoke-ApiTest -Method "GET" -Endpoint "/api/sonobrokers/geo?ip=*******" -Description "Get geolocation by IP"
$testResults += @{ Test = "Geo Location"; Result = $result }

# Test Enum Values
$result = Invoke-ApiTest -Method "GET" -Endpoint "/api/sonobrokers/enums" -Description "Get enum values"
$testResults += @{ Test = "Enum Values"; Result = $result }

# Test Waiting List
$waitingListBody = @{
    email = "<EMAIL>"
} | ConvertTo-Json

$result = Invoke-ApiTest -Method "POST" -Endpoint "/api/sonobrokers/waiting-list" -Body $waitingListBody -Description "Add to waiting list"
$testResults += @{ Test = "Waiting List"; Result = $result }

# Test Concierge Contact
$conciergeBody = @{
    name = "Test User"
    email = "<EMAIL>"
    country = "CA"
    propertyAddress = "123 Test St"
} | ConvertTo-Json

$result = Invoke-ApiTest -Method "POST" -Endpoint "/api/sonobrokers/contact-concierge" -Body $conciergeBody -Description "Contact concierge"
$testResults += @{ Test = "Contact Concierge"; Result = $result }

Write-Host "`n=== Testing AI Property Services ===" -ForegroundColor Magenta

# Test AI Property Import
$importBody = @{
    address = "123 Main St, Toronto, ON"
} | ConvertTo-Json

$result = Invoke-ApiTest -Method "POST" -Endpoint "/api/sonobrokers/ai/property-import" -Body $importBody -Description "AI Property Import"
$testResults += @{ Test = "AI Property Import"; Result = $result }

# Test AI Property Valuation
$valuationBody = @{
    address = "123 Main St, Toronto, ON"
    country = "CA"
} | ConvertTo-Json

$result = Invoke-ApiTest -Method "POST" -Endpoint "/api/sonobrokers/ai/property-valuation" -Body $valuationBody -Description "AI Property Valuation"
$testResults += @{ Test = "AI Property Valuation"; Result = $result }

# Test AI Description Generation
$descriptionBody = @{
    prompt = "Generate a description for a 3-bedroom house"
    userId = "test-user-123"
} | ConvertTo-Json

$result = Invoke-ApiTest -Method "POST" -Endpoint "/api/sonobrokers/ai/generate-description" -Body $descriptionBody -Description "AI Description Generation"
$testResults += @{ Test = "AI Description Generation"; Result = $result }

Write-Host "`n=== Testing Advertiser APIs ===" -ForegroundColor Magenta

# Test Get Advertiser Plans
$result = Invoke-ApiTest -Method "GET" -Endpoint "/api/sonobrokers/advertisers/plans" -Description "Get advertiser plans"
$testResults += @{ Test = "Advertiser Plans"; Result = $result }

# Test Search Advertisers
$result = Invoke-ApiTest -Method "GET" -Endpoint "/api/sonobrokers/advertisers?page=1&limit=10" -Description "Search advertisers"
$testResults += @{ Test = "Search Advertisers"; Result = $result }

if ($AuthToken) {
    Write-Host "`n=== Testing Authenticated Endpoints ===" -ForegroundColor Magenta
    
    # Test Get User's Advertiser Profile
    $result = Invoke-ApiTest -Method "GET" -Endpoint "/api/sonobrokers/advertisers/me" -RequireAuth $true -Description "Get user's advertiser profile"
    $testResults += @{ Test = "User Advertiser Profile"; Result = $result }
    
    # Test Get User's Projects
    $result = Invoke-ApiTest -Method "GET" -Endpoint "/api/sonobrokers/projects" -RequireAuth $true -Description "Get user's projects"
    $testResults += @{ Test = "User Projects"; Result = $result }
    
    # Test Create Advertiser
    $advertiserBody = @{
        businessName = "Test Photography Services"
        email = "<EMAIL>"
        serviceType = "photographer"
        serviceAreas = @("Toronto", "Mississauga")
        plan = "basic"
    } | ConvertTo-Json
    
    $result = Invoke-ApiTest -Method "POST" -Endpoint "/api/sonobrokers/advertisers" -Body $advertiserBody -RequireAuth $true -Description "Create advertiser"
    $testResults += @{ Test = "Create Advertiser"; Result = $result }
}
else {
    Write-Host "`n⚠️  Skipping authenticated tests - no auth token provided" -ForegroundColor Yellow
    Write-Host "To test authenticated endpoints, provide an auth token:" -ForegroundColor Yellow
    Write-Host ".\TestMigration.ps1 -AuthToken 'your-clerk-jwt-token'" -ForegroundColor Yellow
}

Write-Host "`n=== Testing Error Handling ===" -ForegroundColor Magenta

# Test invalid request (missing required fields)
$result = Invoke-ApiTest -Method "POST" -Endpoint "/api/sonobrokers/advertisers" -Body "{}" -Description "Invalid advertiser creation (should fail)"
$testResults += @{ Test = "Error Handling"; Result = !$result } # Expecting failure

# Test non-existent resource
$result = Invoke-ApiTest -Method "GET" -Endpoint "/api/sonobrokers/advertisers/non-existent-id" -Description "Non-existent advertiser (should fail)"
$testResults += @{ Test = "Not Found Handling"; Result = !$result } # Expecting failure

Write-Host "`n=== Test Results Summary ===" -ForegroundColor Magenta

$passedTests = ($testResults | Where-Object { $_.Result -eq $true }).Count
$totalTests = $testResults.Count

Write-Host "`nTest Results:" -ForegroundColor White
foreach ($test in $testResults) {
    $status = if ($test.Result) { "✅ PASS" } else { "❌ FAIL" }
    $color = if ($test.Result) { "Green" } else { "Red" }
    Write-Host "$status $($test.Test)" -ForegroundColor $color
}

Write-Host "`nSummary: $passedTests/$totalTests tests passed" -ForegroundColor $(if ($passedTests -eq $totalTests) { "Green" } else { "Yellow" })

if ($passedTests -eq $totalTests) {
    Write-Host "`n🎉 All tests passed! API migration appears to be successful." -ForegroundColor Green
}
else {
    Write-Host "`n⚠️  Some tests failed. Please check the API implementation and database connectivity." -ForegroundColor Yellow
}

Write-Host "`nNext Steps:" -ForegroundColor Cyan
Write-Host "1. Run the application and verify it starts without errors" -ForegroundColor White
Write-Host "2. Check application logs for any warnings or errors" -ForegroundColor White
Write-Host "3. Test with real authentication tokens" -ForegroundColor White
Write-Host "4. Verify database connectivity and stored procedures" -ForegroundColor White
Write-Host "5. Run integration tests with actual data" -ForegroundColor White
