using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using MicroSaasWebApi.Models.Auth;
using MicroSaasWebApi.Models.Auth.Clerk;
using MicroSaasWebApi.Services.Auth.Interfaces;
using MicroSaasWebApi.Services.Auth.Clerk.Interface;

namespace MicroSaasWebApi.Services.Auth
{
    /// <summary>
    /// Clerk authentication service implementing SOLID principles
    /// Handles Clerk-specific authentication operations
    /// </summary>
    public class ClerkAuthService : IClerkAuthService
    {
        private readonly IClerkService _clerkService;
        private readonly ILogger<ClerkAuthService> _logger;
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;

        public ClerkAuthService(
            IClerkService clerkService,
            ILogger<ClerkAuthService> logger,
            IConfiguration configuration,
            HttpClient httpClient)
        {
            _clerkService = clerkService;
            _logger = logger;
            _configuration = configuration;
            _httpClient = httpClient;
        }

        /// <summary>
        /// Validates Clerk JWT token
        /// </summary>
        public Task<ClaimsPrincipal?> ValidateClerkTokenAsync(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.UTF8.GetBytes(_configuration["Clerk:SecretKey"] ?? throw new InvalidOperationException("Clerk secret key not configured"));

                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = _configuration["Clerk:Issuer"],
                    ValidateAudience = true,
                    ValidAudience = _configuration["Clerk:Audience"],
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                };

                var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);

                _logger.LogInformation("Clerk token validated successfully for user: {UserId}",
                    principal.FindFirst(ClaimTypes.NameIdentifier)?.Value);

                return Task.FromResult<ClaimsPrincipal?>(principal);
            }
            catch (SecurityTokenException ex)
            {
                _logger.LogWarning("Clerk token validation failed: {Error}", ex.Message);
                return Task.FromResult<ClaimsPrincipal?>(null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating Clerk token");
                return Task.FromResult<ClaimsPrincipal?>(null);
            }
        }

        /// <summary>
        /// Login user with Clerk
        /// </summary>
        public async Task<AuthResponse> LoginAsync(LoginRequest request)
        {
            return await AuthenticateAsync(request.Email, request.Password);
        }

        /// <summary>
        /// Authenticates user with Clerk
        /// </summary>
        public async Task<AuthResponse> AuthenticateAsync(string email, string password)
        {
            try
            {
                // Use Clerk service to authenticate
                var clerkUser = await _clerkService.AuthenticateUserAsync(email, password);

                if (clerkUser == null)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "Invalid credentials"
                    };
                }

                // Generate JWT token for the authenticated user
                var token = await GenerateJwtTokenAsync(clerkUser);

                return new AuthResponse
                {
                    Success = true,
                    AccessToken = token,
                    UserId = clerkUser.Id,
                    User = new UserInfo
                    {
                        Id = Guid.Parse(clerkUser.Id),
                        Email = clerkUser.EmailAddress,
                        FirstName = clerkUser.FirstName,
                        LastName = clerkUser.LastName,
                        IsEmailVerified = clerkUser.EmailVerified,
                        ProfileImageUrl = clerkUser.ProfileImageUrl
                    },
                    ExpiresAt = DateTime.UtcNow.AddHours(24),
                    Message = "Authentication successful"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during Clerk authentication for email: {Email}", email);
                return new AuthResponse
                {
                    Success = false,
                    Message = "Authentication failed"
                };
            }
        }

        /// <summary>
        /// Registers new user with Clerk
        /// </summary>
        public async Task<AuthResponse> RegisterAsync(RegisterRequest request)
        {
            try
            {
                var clerkUser = await _clerkService.CreateUserAsync(
                    request.Email,
                    request.Password,
                    request.FirstName,
                    request.LastName);

                if (clerkUser == null)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "User registration failed"
                    };
                }

                // Generate JWT token for the new user
                var token = await GenerateJwtTokenAsync(clerkUser);

                return new AuthResponse
                {
                    Success = true,
                    AccessToken = token,
                    UserId = clerkUser.Id,
                    User = new UserInfo
                    {
                        Id = Guid.Parse(clerkUser.Id),
                        Email = clerkUser.EmailAddress,
                        FirstName = clerkUser.FirstName,
                        LastName = clerkUser.LastName,
                        IsEmailVerified = clerkUser.EmailVerified,
                        ProfileImageUrl = clerkUser.ProfileImageUrl
                    },
                    ExpiresAt = DateTime.UtcNow.AddHours(24),
                    Message = "Registration successful"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during Clerk registration for email: {Email}", request.Email);
                return new AuthResponse
                {
                    Success = false,
                    Message = "Registration failed"
                };
            }
        }

        /// <summary>
        /// Gets user information from Clerk
        /// </summary>
        public async Task<ClerkUser?> GetUserAsync(string userId)
        {
            try
            {
                return await _clerkService.GetUserAsync(userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user from Clerk: {UserId}", userId);
                return null;
            }
        }

        /// <summary>
        /// Updates user in Clerk
        /// </summary>
        public async Task<bool> UpdateUserAsync(string userId, string? firstName = null, string? lastName = null)
        {
            try
            {
                return await _clerkService.UpdateUserAsync(userId, firstName, lastName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user in Clerk: {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// Deletes user from Clerk
        /// </summary>
        public async Task<bool> DeleteUserAsync(string userId)
        {
            try
            {
                return await _clerkService.DeleteUserAsync(userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user from Clerk: {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// Generates JWT token for Clerk user
        /// </summary>
        private Task<string> GenerateJwtTokenAsync(ClerkUser user)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(_configuration["Clerk:SecretKey"] ?? throw new InvalidOperationException("Clerk secret key not configured"));

            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, user.Id),
                new(ClaimTypes.Email, user.EmailAddress),
                new(ClaimTypes.GivenName, user.FirstName),
                new(ClaimTypes.Surname, user.LastName),
                new("email_verified", user.EmailVerified.ToString()),
                new("auth_provider", "clerk")
            };

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddHours(24),
                Issuer = _configuration["Clerk:Issuer"],
                Audience = _configuration["Clerk:Audience"],
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return Task.FromResult(tokenHandler.WriteToken(token));
        }

        /// <summary>
        /// Refreshes Clerk session token
        /// </summary>
        public Task<AuthResponse> RefreshTokenAsync(string refreshToken)
        {
            try
            {
                // Implement Clerk token refresh logic
                // This would typically involve calling Clerk's refresh endpoint

                _logger.LogInformation("Token refresh attempted with Clerk");

                // Placeholder implementation
                return Task.FromResult(new AuthResponse
                {
                    Success = true,
                    Message = "Token refreshed successfully",
                    AccessToken = "new_clerk_token", // Replace with actual refreshed token
                    ExpiresAt = DateTime.UtcNow.AddHours(24)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing Clerk token");
                return Task.FromResult(new AuthResponse
                {
                    Success = false,
                    Message = "Token refresh failed"
                });
            }
        }

        /// <summary>
        /// Logout user from Clerk
        /// </summary>
        public async Task<bool> LogoutAsync(string userId)
        {
            return await SignOutAsync(userId);
        }

        /// <summary>
        /// Signs out user from Clerk
        /// </summary>
        public Task<bool> SignOutAsync(string userId)
        {
            try
            {
                // Implement Clerk sign out logic
                _logger.LogInformation("User signed out from Clerk: {UserId}", userId);
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error signing out user from Clerk: {UserId}", userId);
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// Gets the authenticated user ID from HTTP context
        /// </summary>
        public async Task<string?> GetUserIdAsync(HttpContext httpContext)
        {
            try
            {
                // Check if user is authenticated
                if (httpContext.User?.Identity?.IsAuthenticated != true)
                {
                    return null;
                }

                // Get user ID from claims (Clerk uses 'sub' claim for user ID)
                var userId = httpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value
                           ?? httpContext.User.FindFirst("sub")?.Value;

                return await Task.FromResult(userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user ID from HTTP context");
                return null;
            }
        }

        /// <summary>
        /// Gets the authenticated user profile from HTTP context and database
        /// </summary>
        public async Task<UserInfo?> GetUserProfileAsync(HttpContext httpContext)
        {
            try
            {
                var userId = await GetUserIdAsync(httpContext);
                if (string.IsNullOrEmpty(userId))
                {
                    return null;
                }

                // Get user from Clerk
                var clerkUser = await GetUserAsync(userId);
                if (clerkUser == null)
                {
                    return null;
                }

                return new UserInfo
                {
                    Id = Guid.Parse(clerkUser.Id),
                    Email = clerkUser.EmailAddress,
                    FirstName = clerkUser.FirstName,
                    LastName = clerkUser.LastName,
                    IsEmailVerified = clerkUser.EmailVerified,
                    ProfileImageUrl = clerkUser.ProfileImageUrl
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user profile from HTTP context");
                return null;
            }
        }
    }
}
