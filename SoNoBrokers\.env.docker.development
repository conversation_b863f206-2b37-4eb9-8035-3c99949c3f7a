﻿# =============================================================================
# Development Docker Configuration for SoNoBrokers React Application
# Overrides base .env.docker settings for development environment
# =============================================================================

# =============================================================================
# Application Configuration (Development)
# =============================================================================
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_BASE_URL=http://localhost:7163

# =============================================================================
# Database Configuration (Development)
# =============================================================================
DATABASE_URL=***************************************************************************************************/postgres

# =============================================================================
# Authentication - Clerk Configuration (Development - Test Keys)
# =============================================================================
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_YmV0dGVyLXBvc3N1bS01OC5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_SECRET_KEY=sk_test_VpPsw9paA18Uka7jxuB6PeE3bLvzKVEESGaDuysAgL
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up

# =============================================================================
# Supabase Configuration (Development)
# =============================================================================
NEXT_PUBLIC_SUPABASE_URL=https://yfznlsisxsnymkvydzha.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.qUBshv6Wi-90ZAEnUM2RXuhR77QFHnmEpI6O-y7l3BE

# =============================================================================
# External Services (Development)
# =============================================================================
RESEND_API_KEY=re_BM6pyT8x_ChaS1fbRCbdprxPy1fwimWCs
NEXT_PUBLIC_MAPBOX_API_KEY=pk.eyJ1IjoiamF2aWFucGljYXJkbzMzIiwiYSI6ImNtYjY4ZGRyazBiaWYybHEyMWpnNGN4cDQifQ.m63aGFvbfzrQhT3sWlSbDQ
NEXT_PUBLIC_GOOGLE_PLACES_API_KEY=your_google_places_api_key_here

# =============================================================================
# Stripe Configuration (Development - Test Keys)
# =============================================================================
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51Qfy9dP82YH9JfOlFmwTk0RHvE7l6sow7ImJBI4c1GzcbhFydoVpb3DMevP8B3C4gj95qCoEise8vEhgFgWxhLEN000RfInKNH
STRIPE_SECRET_KEY=sk_test_51Qfy9dP82YH9JfOlPF9evXANtAjm63textOqXcpIIPpsCqxt9EwZRRyOSV4wk3YURh4hnqZOxnGXFGMf0rJM0yhv00S2q8dr3E

# =============================================================================
# Feature Flags (Development Settings)
# =============================================================================
NEXT_PUBLIC_ENABLE_GOOGLE_PROVIDERS=true
NEXT_PUBLIC_LAUNCH_MODE=true
NEXT_PUBLIC_ENABLE_LANGUAGE_SELECTOR=true
NEXT_PUBLIC_ENABLE_REGION_TESTER=true
NEXT_PUBLIC_ENABLE_ARCHIVE_MIGRATION=false
NEXT_PUBLIC_SHOW_CONCIERGE_SERVICES=false
NEXT_PUBLIC_DEV_MODE=true
NEXT_PUBLIC_SHOW_DEBUG_INFO=true
NEXT_PUBLIC_ENABLE_PROPERTY_FAVORITES=true
NEXT_PUBLIC_ENABLE_OPEN_HOUSE_FAVORITES=true
NEXT_PUBLIC_ENABLE_SERVICE_BOOKING=true
NEXT_PUBLIC_ENABLE_AI_PROPERTY_CREATOR=true
NEXT_PUBLIC_ENABLE_MORTGAGE_CALCULATOR=true
NEXT_PUBLIC_ENABLE_COMMISSION_CALCULATOR=true
NEXT_PUBLIC_ENABLE_ROLE_BASED_AUTH=true
NEXT_PUBLIC_ADMIN_EMAIL=<EMAIL>

# =============================================================================
# Supported Countries & Regions (Development)
# =============================================================================
NEXT_PUBLIC_SUPPORTED_COUNTRIES=CA,US,UAE
NEXT_PUBLIC_DEFAULT_COUNTRY=CA

# =============================================================================
# Development Specific Settings
# =============================================================================
NEXT_TELEMETRY_DISABLED=1
FAST_REFRESH=true
