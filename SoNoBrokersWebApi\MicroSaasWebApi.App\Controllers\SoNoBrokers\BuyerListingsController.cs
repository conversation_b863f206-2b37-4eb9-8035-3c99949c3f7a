using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.Core;
using MicroSaasWebApi.Models.SoNoBrokers;

using System.Security.Claims;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/sonobrokers/[controller]")]
    [Authorize]
    public class BuyerListingsController : ControllerBase
    {
        private readonly MicroSaasWebApi.App.Context.IDapperDbContext _dbContext;
        private readonly ILogger<BuyerListingsController> _logger;

        public BuyerListingsController(MicroSaasWebApi.App.Context.IDapperDbContext dbContext, ILogger<BuyerListingsController> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        /// <summary>
        /// Create a new buyer listing
        /// </summary>
        /// <param name="request">Buyer listing details</param>
        /// <returns>Created buyer listing</returns>
        [HttpPost]





        public async Task<ActionResult<ApiResponse<BuyerListings>>> CreateBuyerListing([FromBody] CreateBuyerListingRequest request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(ApiResponse<BuyerListings>.ErrorResult("User not authenticated"));
                }

                // Find the user in our database
                const string userSql = "SELECT * FROM snb.users WHERE id = @UserId";
                var user = await _dbContext.QuerySingleOrDefaultAsync<Models.SoNoBrokers.User>(userSql, new { UserId = userId });
                if (user == null)
                {
                    return NotFound(ApiResponse<BuyerListings>.ErrorResult("User not found"));
                }

                // Create the buyer listing
                var buyerListing = new BuyerListings
                {
                    Id = Guid.NewGuid().ToString(),
                    UserId = userId,
                    Title = request.Title,
                    Description = request.Description,
                    Price = request.Price,
                    PropertyType = request.PropertyType,
                    AdType = request.AdType,
                    Bedrooms = request.Bedrooms,
                    Bathrooms = request.Bathrooms,
                    Sqft = request.Sqft,
                    Storeys = request.Storeys,
                    YearBuilt = request.YearBuilt,
                    LotSize = request.LotSize,
                    LotSizeUnit = request.LotSizeUnit,
                    Address = request.Address != null ? System.Text.Json.JsonSerializer.Serialize(request.Address) : null,
                    ParkingTypes = request.ParkingTypes != null ? System.Text.Json.JsonSerializer.Serialize(request.ParkingTypes) : null,
                    ExtraFeatures = request.ExtraFeatures != null ? System.Text.Json.JsonSerializer.Serialize(request.ExtraFeatures) : null,
                    MlsNumber = request.MlsNumber,
                    VirtualTour = request.VirtualTour,
                    OpenHouse = request.OpenHouse != null ? System.Text.Json.JsonSerializer.Serialize(request.OpenHouse) : null,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                const string insertSql = @"
                    INSERT INTO snb.buyer_listings (
                        id, user_id, title, description, price, property_type, ad_type,
                        bedrooms, bathrooms, sqft, storeys, year_built, lot_size, lot_size_unit,
                        address, parking_types, extra_features, mls_number, virtual_tour,
                        open_house, created_at, updated_at
                    ) VALUES (
                        @Id, @UserId, @Title, @Description, @Price, @PropertyType, @AdType,
                        @Bedrooms, @Bathrooms, @Sqft, @Storeys, @YearBuilt, @LotSize, @LotSizeUnit,
                        @Address::jsonb, @ParkingTypes::jsonb, @ExtraFeatures::jsonb, @MlsNumber,
                        @VirtualTour, @OpenHouse::jsonb, @CreatedAt, @UpdatedAt
                    )";

                await _dbContext.ExecuteAsync(insertSql, buyerListing);

                return CreatedAtAction(nameof(GetBuyerListing), new { id = buyerListing.Id },
                    ApiResponse<BuyerListings>.SuccessResult(buyerListing, "Buyer listing created successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating buyer listing");
                return StatusCode(500, ApiResponse<BuyerListings>.ErrorResult("Failed to create buyer listing"));
            }
        }

        /// <summary>
        /// Get buyer listing by ID
        /// </summary>
        /// <param name="id">Buyer listing ID</param>
        /// <returns>Buyer listing details</returns>
        [HttpGet("{id}")]



        public async Task<ActionResult<ApiResponse<BuyerListings>>> GetBuyerListing(string id)
        {
            try
            {
                const string sql = @"
                    SELECT bl.*, u.full_name as user_name, u.email as user_email
                    FROM snb.buyer_listings bl
                    INNER JOIN snb.users u ON bl.user_id = u.id
                    WHERE bl.id = @Id";

                var buyerListing = await _dbContext.QuerySingleOrDefaultAsync<BuyerListings>(sql, new { Id = id });

                if (buyerListing == null)
                {
                    return NotFound(ApiResponse<BuyerListings>.ErrorResult("Buyer listing not found"));
                }

                return Ok(ApiResponse<BuyerListings>.SuccessResult(buyerListing, "Buyer listing retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving buyer listing with ID: {Id}", id);
                return StatusCode(500, ApiResponse<BuyerListings>.ErrorResult("Failed to retrieve buyer listing"));
            }
        }

        /// <summary>
        /// Get buyer listings for the current user
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>List of buyer listings</returns>
        [HttpGet("my-listings")]


        public async Task<ActionResult<ApiResponse<IEnumerable<BuyerListings>>>> GetMyBuyerListings(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(ApiResponse<IEnumerable<BuyerListings>>.ErrorResult("User not authenticated"));
                }

                const string countSql = "SELECT COUNT(*) FROM snb.buyer_listings WHERE user_id = @UserId";
                const string dataSql = @"
                    SELECT * FROM snb.buyer_listings
                    WHERE user_id = @UserId
                    ORDER BY created_at DESC
                    LIMIT @PageSize OFFSET @Offset";

                var totalCount = await _dbContext.QuerySingleAsync<int>(countSql, new { UserId = userId });
                var offset = (page - 1) * pageSize;
                var buyerListings = await _dbContext.QueryAsync<BuyerListings>(dataSql, new
                {
                    UserId = userId,
                    PageSize = pageSize,
                    Offset = offset
                });

                var response = ApiResponse<IEnumerable<BuyerListings>>.SuccessResult(
                    buyerListings,
                    "Buyer listings retrieved successfully");

                response.TotalCount = totalCount;
                response.Page = page;
                response.PageSize = pageSize;

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving buyer listings for user");
                return StatusCode(500, ApiResponse<IEnumerable<BuyerListings>>.ErrorResult("Failed to retrieve buyer listings"));
            }
        }
    }

    public class CreateBuyerListingRequest
    {
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public decimal? Price { get; set; }
        public string PropertyType { get; set; } = string.Empty;
        public string? AdType { get; set; }
        public int? Bedrooms { get; set; }
        public int? Bathrooms { get; set; }
        public int? Sqft { get; set; }
        public int? Storeys { get; set; }
        public int? YearBuilt { get; set; }
        public decimal? LotSize { get; set; }
        public string? LotSizeUnit { get; set; }
        public object? Address { get; set; }
        public string[]? ParkingTypes { get; set; }
        public string[]? ExtraFeatures { get; set; }
        public string? MlsNumber { get; set; }
        public string? VirtualTour { get; set; }
        public object? OpenHouse { get; set; }
    }
}
