import { RegionalComponentFactory } from '@/components/factories/RegionalComponentFactory'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
// Removed auth import - no authentication required for browsing services
import { redirect } from 'next/navigation'
import Link from 'next/link'
import { Country, SnbUserType } from '@/types'
import {
  Camera,
  Scale,
  Home,
  Wrench,
  Truck,
  Shield,
  Calculator,
  Search,
  Calendar,
  FileText,
  Crown
} from 'lucide-react'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: SnbUserType
  }>
}

const sellerServices = [
  {
    title: 'List Property',
    href: '/list-property',
    icon: Home,
    description: 'List your property directly to buyers and save on commissions.'
  },
  ...(process.env.NEXT_PUBLIC_SHOW_CONCIERGE_SERVICES === 'true' ? [{
    title: 'Concierge Services',
    href: '/services/concierge',
    icon: Crown,
    description: 'White-glove property sale management with expert coordination.'
  }] : []),
  {
    title: 'AI Property Creator',
    href: '/services/ai-property-creator',
    icon: FileText,
    description: 'Create professional property listings with AI-powered tools.'
  },
  {
    title: 'Photography Services',
    href: '/services/photography',
    icon: Camera,
    description: 'Professional real estate photography and virtual tours.'
  },
  {
    title: 'Staging Services',
    href: '/services/staging',
    icon: Home,
    description: 'Home staging consultation to maximize your property appeal.'
  },
  {
    title: 'Legal Services',
    href: '/services/legal',
    icon: Scale,
    description: 'Real estate lawyers and legal document preparation.'
  },
  {
    title: 'Marketing Services',
    href: '/services/marketing',
    icon: Wrench,
    description: 'Digital marketing and listing promotion services.'
  }
]

const buyerServices = [
  {
    title: 'Property Search',
    href: '/properties',
    icon: Search,
    description: 'Search and browse available properties in your area.'
  },
  {
    title: 'Open Houses',
    href: '/properties/open-houses',
    icon: Calendar,
    description: 'Find and attend open house events near you.'
  },
  {
    title: 'Home Inspection',
    href: '/services/home-inspection',
    icon: Home,
    description: 'Professional home inspection services and scheduling.'
  },
  {
    title: 'Insurance Services',
    href: '/services/insurance',
    icon: Shield,
    description: 'Home insurance quotes and coverage options.'
  },
  {
    title: 'Moving Services',
    href: '/services/moving',
    icon: Truck,
    description: 'Trusted moving companies and relocation assistance.'
  },
  {
    title: 'Legal Services',
    href: '/services/legal',
    icon: Scale,
    description: 'Real estate lawyers and legal document preparation.'
  },
  {
    title: 'Mortgage Services',
    href: '/services/mortgage',
    icon: Calculator,
    description: 'Mortgage brokers and lending services.'
  }
]

export default async function ServicesPage({ params, searchParams }: PageProps) {
  // No authentication required for browsing services
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = Object.values(Country)
  const countryParam = resolvedParams.country.toLowerCase()
  const countryEnum = countryParam.toUpperCase() as Country

  if (!validCountries.includes(countryEnum)) {
    redirect('/ca/services')
  }

  // Default to buyer if no userType specified
  const userType = resolvedSearchParams.userType || SnbUserType.Buyer

  // Use lowercase country for URLs
  const country = countryParam
  const services = userType === SnbUserType.Seller ? sellerServices : buyerServices

  return (
    <div className="min-h-screen bg-background">
      {/* Services Grid */}
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-foreground mb-4">
              {userType === SnbUserType.Seller ? 'Seller Services' : 'Buyer Services'}
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              {userType === SnbUserType.Seller
                ? 'Professional services to help you sell your property faster and for more money. Connect with verified service providers in your area.'
                : 'Essential tools and services to help you find and purchase your perfect home. Access trusted professionals throughout your buying journey.'
              }
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {services.map((service) => {
              const IconComponent = service.icon
              return (
                <Card key={service.title} className="group hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
                  <CardHeader className="text-center pb-4">
                    <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4 group-hover:bg-primary/20 transition-colors">
                      <IconComponent className="w-8 h-8 text-primary" />
                    </div>
                    <CardTitle className="text-xl">{service.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center">
                    <p className="text-muted-foreground mb-6">
                      {service.description}
                    </p>
                    <Button asChild className="w-full">
                      <Link href={`/${country}${service.href}?userType=${userType}`}>
                        Explore Service
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16">
            <Card className="bg-gradient-to-r from-primary/10 to-accent/10 border-primary/20">
              <CardContent className="py-12">
                <h2 className="text-3xl font-bold text-foreground mb-4">
                  Need Help Choosing?
                </h2>
                <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
                  Our team of experts can help you find the right services for your needs.
                  Get personalized recommendations based on your specific situation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button size="lg" asChild>
                    <Link href={`/${country}/contact?userType=${userType}`}>
                      Get Expert Advice
                    </Link>
                  </Button>
                  <Button size="lg" variant="outline" asChild>
                    <Link href={`/${country}/advertise?userType=${userType}`}>
                      List Your Services
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'buyer'
  const countryDisplay = resolvedParams.country.toUpperCase()

  return {
    title: `${userType === 'buyer' ? 'Buyer' : 'Seller'} Services in ${countryDisplay} | SoNoBrokers`,
    description: `Comprehensive ${userType} services in ${countryDisplay}. Find trusted professionals for real estate transactions, from photography to legal services. Compare providers and book online.`,
    keywords: `${userType} services, real estate services, ${countryDisplay}, property services, professional services`,
  }
}
