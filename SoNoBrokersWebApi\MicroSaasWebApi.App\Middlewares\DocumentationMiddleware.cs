using Microsoft.Extensions.FileProviders;
using System.Text;

namespace MicroSaasWebApi.Middlewares
{
    /// <summary>
    /// Middleware to serve markdown documentation files
    /// </summary>
    public class DocumentationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IFileProvider _fileProvider;
        private readonly ILogger<DocumentationMiddleware> _logger;

        public DocumentationMiddleware(RequestDelegate next, IFileProvider fileProvider, ILogger<DocumentationMiddleware> logger)
        {
            _next = next;
            _fileProvider = fileProvider;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var path = context.Request.Path.Value;

            // Check if this is a documentation request
            if (path != null && path.StartsWith("/docs", StringComparison.OrdinalIgnoreCase))
            {
                await HandleDocumentationRequest(context, path);
                return;
            }

            await _next(context);
        }

        private async Task HandleDocumentationRequest(HttpContext context, string path)
        {
            try
            {
                // Remove /docs prefix and get the file path
                var filePath = path.Substring(5).TrimStart('/');

                if (string.IsNullOrEmpty(filePath))
                {
                    filePath = "README.md"; // Default to README
                }

                // Ensure .md extension
                if (!filePath.EndsWith(".md", StringComparison.OrdinalIgnoreCase))
                {
                    filePath += ".md";
                }

                var fileInfo = _fileProvider.GetFileInfo(filePath);

                if (!fileInfo.Exists)
                {
                    context.Response.StatusCode = 404;
                    await context.Response.WriteAsync("Documentation file not found");
                    return;
                }

                // Read the markdown file
                using var stream = fileInfo.CreateReadStream();
                using var reader = new StreamReader(stream);
                var content = await reader.ReadToEndAsync();

                // Convert to HTML (basic conversion)
                var html = ConvertMarkdownToHtml(content, filePath);

                context.Response.ContentType = "text/html";
                await context.Response.WriteAsync(html);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error serving documentation file: {Path}", path);
                context.Response.StatusCode = 500;
                await context.Response.WriteAsync("Error loading documentation");
            }
        }

        private string ConvertMarkdownToHtml(string markdown, string fileName)
        {
            // Basic markdown to HTML conversion
            var html = new StringBuilder();
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html>");
            html.AppendLine("<head>");
            html.AppendLine($"<title>{fileName} - MicroSaaS Documentation</title>");
            html.AppendLine("<style>");
            html.AppendLine("body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }");
            html.AppendLine("h1, h2, h3 { color: #333; }");
            html.AppendLine("code { background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }");
            html.AppendLine("pre { background-color: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }");
            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");

            // Simple markdown processing
            var lines = markdown.Split('\n');
            var inCodeBlock = false;

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();

                if (trimmedLine.StartsWith("```"))
                {
                    if (inCodeBlock)
                    {
                        html.AppendLine("</pre>");
                        inCodeBlock = false;
                    }
                    else
                    {
                        html.AppendLine("<pre><code>");
                        inCodeBlock = true;
                    }
                    continue;
                }

                if (inCodeBlock)
                {
                    html.AppendLine(System.Web.HttpUtility.HtmlEncode(line));
                    continue;
                }

                if (trimmedLine.StartsWith("# "))
                {
                    html.AppendLine($"<h1>{trimmedLine.Substring(2)}</h1>");
                }
                else if (trimmedLine.StartsWith("## "))
                {
                    html.AppendLine($"<h2>{trimmedLine.Substring(3)}</h2>");
                }
                else if (trimmedLine.StartsWith("### "))
                {
                    html.AppendLine($"<h3>{trimmedLine.Substring(4)}</h3>");
                }
                else if (string.IsNullOrEmpty(trimmedLine))
                {
                    html.AppendLine("<br>");
                }
                else
                {
                    html.AppendLine($"<p>{System.Web.HttpUtility.HtmlEncode(line)}</p>");
                }
            }

            html.AppendLine("</body>");
            html.AppendLine("</html>");

            return html.ToString();
        }
    }
}
