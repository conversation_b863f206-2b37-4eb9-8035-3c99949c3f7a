# SoNoBrokers Web API Migration Documentation

## Overview
This document describes the migration of SoNoBrokers React frontend from direct Prisma/Supabase calls to .NET Core Web API endpoints.

## Architecture Changes

### Before Migration
```
React Components → Prisma Client → Supabase Database
```

### After Migration
```
React Server Components → Server Actions → .NET Web API → Dapper → Supabase Database
```

## New API Endpoints

### Authentication Endpoints

#### Track User Login
```http
POST /api/sonobrokers/auth/track-login
Authorization: Bearer {token}
```
**Description**: Updates user's last login time and sets logged in status to true.

**Response**:
```json
{
  "message": "Login tracked successfully"
}
```

#### Track User Logout
```http
POST /api/sonobrokers/auth/track-logout
Authorization: Bearer {token}
```
**Description**: Sets user's logged in status to false.

**Response**:
```json
{
  "message": "Logout tracked successfully"
}
```

#### Get Login Statistics
```http
GET /api/sonobrokers/auth/login-stats
Authorization: Bearer {token}
```
**Description**: Returns login statistics for the current user.

**Response**:
```json
{
  "isLoggedIn": true,
  "lastLoginAt": "2024-01-15T10:30:00Z"
}
```

### Enhanced User Endpoints

#### Map Auth User to SNB User
```http
POST /api/sonobrokers/users/map-auth-user
Authorization: Bearer {token}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "fullName": "John Doe",
  "firstName": "John",
  "lastName": "Doe",
  "clerkUserId": "clerk_user_123"
}
```
**Description**: Creates or updates a SoNoBrokers user based on authentication data.

#### Get User Permissions
```http
GET /api/sonobrokers/users/{userId}/permissions
Authorization: Bearer {token}
```
**Description**: Returns user permissions based on role.

**Response**:
```json
{
  "userId": "user_123",
  "role": "USER",
  "isActive": true,
  "permissions": [
    {
      "permission": "read_properties",
      "resource": "properties"
    }
  ]
}
```

### Enum Endpoints

#### Get All Enums
```http
GET /api/sonobrokers/enums
```
**Description**: Returns all enum values used by the application.

**Response**:
```json
{
  "countries": [
    {
      "code": "CA",
      "name": "Canada",
      "flag": "🇨🇦",
      "value": "ca"
    }
  ],
  "userTypes": [
    {
      "value": "BUYER",
      "label": "Buyer"
    }
  ],
  "serviceTypes": ["photographer", "lawyer", "inspector"],
  "propertyStatuses": ["pending", "active", "sold", "expired"],
  "advertiserPlans": ["basic", "premium", "enterprise"],
  "advertiserStatuses": ["pending", "active", "suspended", "cancelled"]
}
```

## Data Models

### Enums

#### UserRole
```csharp
public enum UserRole
{
    USER,
    ADMIN,
    SUPER_ADMIN
}
```

#### SnbUserType
```csharp
public enum SnbUserType
{
    USER,
    ADMIN,
    SELLER,
    BUYER
}
```

#### Country
```csharp
public enum Country
{
    CA,  // Canada
    US,  // United States
    AE   // United Arab Emirates
}
```

### DTOs

#### LoginStatsResponse
```csharp
public class LoginStatsResponse
{
    public bool IsLoggedIn { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public int? LoginCount { get; set; }
}
```

#### MapAuthUserRequest
```csharp
public class MapAuthUserRequest
{
    public string? Id { get; set; }
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    public string? FullName { get; set; }
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? ClerkUserId { get; set; }
    public string? AuthUserId { get; set; }
}
```

#### UserPermissionsResponse
```csharp
public class UserPermissionsResponse
{
    public string UserId { get; set; } = string.Empty;
    public UserRole Role { get; set; }
    public List<PermissionInfo> Permissions { get; set; } = new();
    public bool IsActive { get; set; }
}
```

## Database Integration

### DapperDbContext
The API uses Dapper ORM with PostgreSQL/Supabase for database operations.

```csharp
public interface IDapperDbContext
{
    Task<IEnumerable<T>> QueryAsync<T>(string sql, object? param = null);
    Task<T?> QueryFirstOrDefaultAsync<T>(string sql, object? param = null);
    Task<int> ExecuteAsync(string sql, object? param = null);
    // ... other methods
}
```

### Connection String
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=your-supabase-host;Database=postgres;Username=postgres;Password=your-password"
  }
}
```

## Authentication & Authorization

### Clerk Integration
The API integrates with Clerk for authentication:

1. **JWT Token Validation**: All protected endpoints validate Clerk JWT tokens
2. **User Mapping**: Clerk users are automatically mapped to SoNoBrokers users
3. **Role-Based Access**: Permissions are checked based on user roles

### Authorization Flow
```
1. User authenticates with Clerk
2. Clerk provides JWT token
3. React sends token to API
4. API validates token with Clerk
5. API maps Clerk user to SNB user
6. API checks permissions
7. API processes request
```

## Error Handling

### Standard Error Response
```json
{
  "error": "Error message",
  "details": "Additional error details",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### HTTP Status Codes
- `200 OK` - Success
- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `500 Internal Server Error` - Server error

## Performance Considerations

### Caching
- **Enum Data**: Cached for 5 minutes to reduce database calls
- **User Permissions**: Cached per request
- **Database Connections**: Connection pooling enabled

### Optimization
- **Dapper**: Lightweight ORM for better performance
- **Async/Await**: All database operations are asynchronous
- **Connection Management**: Proper connection disposal

## Security Features

### Data Protection
- **SQL Injection Prevention**: Parameterized queries with Dapper
- **Input Validation**: Data annotations on all DTOs
- **Authorization**: Role-based access control

### CORS Configuration
```csharp
services.AddCors(options =>
{
    options.AddPolicy("SoNoBrokersPolicy", builder =>
    {
        builder.WithOrigins("http://localhost:3000", "https://sonobrokers.com")
               .AllowAnyMethod()
               .AllowAnyHeader()
               .AllowCredentials();
    });
});
```

## Monitoring & Logging

### Health Checks
- Database connectivity
- External service availability
- Memory usage monitoring

### Logging
- Structured logging with Serilog
- Request/response logging
- Error tracking and alerting

## Deployment

### Environment Configuration
- **Development**: Local Supabase instance
- **Staging**: Staging Supabase project
- **Production**: Production Supabase project

### Docker Support
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:9.0
COPY . /app
WORKDIR /app
EXPOSE 80
ENTRYPOINT ["dotnet", "MicroSaasWebApi.App.dll"]
```

## Testing

### Unit Tests
- Service layer testing
- Controller testing
- DTO validation testing

### Integration Tests
- End-to-end API testing
- Database integration testing
- Authentication flow testing

## Migration Checklist

- [x] Create new API endpoints
- [x] Implement DTOs and models
- [x] Set up database context
- [x] Configure authentication
- [ ] Fix compilation errors
- [ ] Complete integration testing
- [ ] Update documentation
- [ ] Deploy to staging
- [ ] Performance testing
- [ ] Production deployment
