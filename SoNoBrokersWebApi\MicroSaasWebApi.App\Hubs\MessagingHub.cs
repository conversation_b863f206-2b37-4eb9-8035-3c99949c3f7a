using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using MicroSaasWebApi.Extensions;
using MicroSaasWebApi.Services.SoNoBrokers;
using MicroSaasWebApi.Models.SoNoBrokers.Messaging;
using System.Collections.Concurrent;

namespace MicroSaasWebApi.Hubs
{
    /// <summary>
    /// SignalR Hub for real-time messaging functionality
    /// </summary>
    [Authorize]
    public class MessagingHub : Hub
    {
        private readonly IMessagingService _messagingService;
        private readonly ILogger<MessagingHub> _logger;

        // Store user connections
        private static readonly ConcurrentDictionary<string, HashSet<string>> UserConnections = new();
        private static readonly ConcurrentDictionary<string, HashSet<string>> ConversationConnections = new();

        public MessagingHub(IMessagingService messagingService, ILogger<MessagingHub> logger)
        {
            _messagingService = messagingService;
            _logger = logger;
        }

        /// <summary>
        /// Called when a client connects
        /// </summary>
        public override async Task OnConnectedAsync()
        {
            try
            {
                var userId = Context.User?.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    _logger.LogWarning("User connected without valid user ID");
                    await base.OnConnectedAsync();
                    return;
                }

                // Add user to connections
                UserConnections.AddOrUpdate(userId,
                    new HashSet<string> { Context.ConnectionId },
                    (key, existing) =>
                    {
                        existing.Add(Context.ConnectionId);
                        return existing;
                    });

                _logger.LogInformation("User {UserId} connected with connection {ConnectionId}", userId, Context.ConnectionId);

                // Notify user is online
                await Clients.Others.SendAsync("UserOnline", userId);

                await base.OnConnectedAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in OnConnectedAsync for connection {ConnectionId}", Context.ConnectionId);
                throw;
            }
        }

        /// <summary>
        /// Called when a client disconnects
        /// </summary>
        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            try
            {
                var userId = Context.User?.GetUserId();
                if (!string.IsNullOrEmpty(userId))
                {
                    // Remove connection
                    if (UserConnections.TryGetValue(userId, out var connections))
                    {
                        connections.Remove(Context.ConnectionId);
                        if (connections.Count == 0)
                        {
                            UserConnections.TryRemove(userId, out _);

                            // Notify user is offline
                            await Clients.Others.SendAsync("UserOffline", userId);
                        }
                    }

                    // Remove from conversation groups
                    foreach (var kvp in ConversationConnections.ToList())
                    {
                        if (kvp.Value.Contains(Context.ConnectionId))
                        {
                            kvp.Value.Remove(Context.ConnectionId);
                            if (kvp.Value.Count == 0)
                            {
                                ConversationConnections.TryRemove(kvp.Key, out _);
                            }
                        }
                    }

                    _logger.LogInformation("User {UserId} disconnected with connection {ConnectionId}", userId, Context.ConnectionId);
                }

                await base.OnDisconnectedAsync(exception);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in OnDisconnectedAsync for connection {ConnectionId}", Context.ConnectionId);
            }
        }

        /// <summary>
        /// Join a conversation group for real-time updates
        /// </summary>
        public async Task JoinConversation(string conversationId)
        {
            try
            {
                var userId = Context.User?.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    await Clients.Caller.SendAsync("Error", "Authentication required");
                    return;
                }

                // Verify user has access to this conversation
                var hasAccess = await _messagingService.CanUserAccessConversationAsync(userId, conversationId);
                if (!hasAccess)
                {
                    await Clients.Caller.SendAsync("Error", "Access denied to conversation");
                    return;
                }

                // Add to SignalR group
                await Groups.AddToGroupAsync(Context.ConnectionId, $"conversation_{conversationId}");

                // Track connection
                ConversationConnections.AddOrUpdate(conversationId,
                    new HashSet<string> { Context.ConnectionId },
                    (key, existing) =>
                    {
                        existing.Add(Context.ConnectionId);
                        return existing;
                    });

                _logger.LogInformation("User {UserId} joined conversation {ConversationId}", userId, conversationId);

                await Clients.Caller.SendAsync("JoinedConversation", conversationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error joining conversation {ConversationId}", conversationId);
                await Clients.Caller.SendAsync("Error", "Failed to join conversation");
            }
        }

        /// <summary>
        /// Leave a conversation group
        /// </summary>
        public async Task LeaveConversation(string conversationId)
        {
            try
            {
                await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"conversation_{conversationId}");

                // Remove from tracking
                if (ConversationConnections.TryGetValue(conversationId, out var connections))
                {
                    connections.Remove(Context.ConnectionId);
                    if (connections.Count == 0)
                    {
                        ConversationConnections.TryRemove(conversationId, out _);
                    }
                }

                var userId = Context.User?.GetUserId();
                _logger.LogInformation("User {UserId} left conversation {ConversationId}", userId, conversationId);

                await Clients.Caller.SendAsync("LeftConversation", conversationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error leaving conversation {ConversationId}", conversationId);
            }
        }

        /// <summary>
        /// Send typing indicator
        /// </summary>
        public async Task SendTypingIndicator(string conversationId, bool isTyping)
        {
            try
            {
                var userId = Context.User?.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return;
                }

                // Verify access
                var hasAccess = await _messagingService.CanUserAccessConversationAsync(userId, conversationId);
                if (!hasAccess)
                {
                    return;
                }

                var typingIndicator = new TypingIndicator
                {
                    ConversationId = conversationId,
                    UserId = userId,
                    UserName = Context.User?.Identity?.Name ?? "Unknown User",
                    IsTyping = isTyping,
                    Timestamp = DateTime.UtcNow
                };

                // Send to others in the conversation (not to sender)
                await Clients.OthersInGroup($"conversation_{conversationId}")
                    .SendAsync("TypingIndicator", typingIndicator);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending typing indicator for conversation {ConversationId}", conversationId);
            }
        }

        /// <summary>
        /// Mark messages as read in real-time
        /// </summary>
        public async Task MarkMessagesAsRead(string conversationId, List<string>? messageIds = null)
        {
            try
            {
                var userId = Context.User?.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return;
                }

                var success = await _messagingService.MarkMessagesAsReadAsync(userId, conversationId, messageIds);
                if (success)
                {
                    // Notify others in the conversation
                    await Clients.OthersInGroup($"conversation_{conversationId}")
                        .SendAsync("MessagesRead", new { conversationId, userId, messageIds });

                    await Clients.Caller.SendAsync("MessagesMarkedAsRead", conversationId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking messages as read for conversation {ConversationId}", conversationId);
            }
        }

        /// <summary>
        /// Get online users
        /// </summary>
        public async Task GetOnlineUsers()
        {
            try
            {
                var onlineUsers = UserConnections.Keys.ToList();
                await Clients.Caller.SendAsync("OnlineUsers", onlineUsers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting online users");
            }
        }

        /// <summary>
        /// Send a message through SignalR (called from MessagingService)
        /// </summary>
        public static async Task SendMessageNotification(IHubContext<MessagingHub> hubContext, MessageNotification notification)
        {
            try
            {
                // Send to conversation group
                await hubContext.Clients.Group($"conversation_{notification.ConversationId}")
                    .SendAsync("NewMessage", notification);

                // Send to specific recipients if they're online
                foreach (var recipientId in notification.RecipientIds)
                {
                    if (UserConnections.TryGetValue(recipientId, out var connections))
                    {
                        foreach (var connectionId in connections)
                        {
                            await hubContext.Clients.Client(connectionId)
                                .SendAsync("MessageNotification", notification);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw to avoid breaking the main flow
                Console.WriteLine($"Error sending message notification: {ex.Message}");
            }
        }

        /// <summary>
        /// Send conversation update notification
        /// </summary>
        public static async Task SendConversationUpdate(IHubContext<MessagingHub> hubContext, string conversationId, object update)
        {
            try
            {
                await hubContext.Clients.Group($"conversation_{conversationId}")
                    .SendAsync("ConversationUpdated", update);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending conversation update: {ex.Message}");
            }
        }
    }
}
