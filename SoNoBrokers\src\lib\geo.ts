// MIGRATED: Use enum-actions instead of direct service
import { getCountriesAction } from '@/lib/actions/enum-actions';

// Cache for supported countries
let supportedCountriesCache: string[] | null = null;

// Get supported countries dynamically from database
export async function getSupportedCountries(): Promise<string[]> {
  if (supportedCountriesCache) {
    return supportedCountriesCache;
  }

  try {
    const result = await getCountriesAction();
    if (!result.success || !result.data) {
      throw new Error(result.error || 'Failed to fetch countries');
    }

    supportedCountriesCache = result.data.map(c => c.code);
    return supportedCountriesCache;
  } catch (error) {
    console.error('Failed to get supported countries:', error);
    throw new Error('Unable to load supported countries from database');
  }
}

// Dynamic type based on database enum - no hardcoded values
export type SupportedCountry = string;

export async function isLocationSupported(countryCode: string): Promise<boolean> {
  const supportedCountries = await getSupportedCountries();
  return supportedCountries.includes(countryCode.toUpperCase());
}

export async function getCountryName(countryCode: string): Promise<string> {
  try {
    const countries = await getCountriesFromDatabase();
    const country = countries.find(c => c.code.toUpperCase() === countryCode.toUpperCase());
    return country?.name || 'Your Region';
  } catch (error) {
    console.error('Failed to get country name:', error);
    throw new Error('Unable to load country information from database');
  }
}

export function getRegionSpecificPolicies(countryCode: string) {
  const policies: Record<string, {
    privacyLaws: string[];
    dataRetention: string;
    dataTransfer: string;
  }> = {
    US: {
      privacyLaws: ['CCPA', 'CPRA', 'VCDPA', 'CPA', 'CTDPA'],
      dataRetention: '24 months',
      dataTransfer: 'Within US and Canada only',
    },
    CA: {
      privacyLaws: ['PIPEDA'],
      dataRetention: '24 months',
      dataTransfer: 'Within US and Canada only',
    },
    UAE: {
      privacyLaws: ['UAE Data Protection Law'],
      dataRetention: '24 months',
      dataTransfer: 'Within UAE and approved countries only',
    },
  };
  return policies[countryCode.toUpperCase()] || null;
}

/**
 * Get dashboard redirect URL based on country code
 */
export function getDashboardUrl(countryCode: string): string {
  switch (countryCode.toUpperCase()) {
    case 'US':
      return '/us/dashboard';
    case 'CA':
      return '/ca/dashboard';
    case 'UAE':
      return '/uae/dashboard';
    default:
      return '/unsupported-region';
  }
}
