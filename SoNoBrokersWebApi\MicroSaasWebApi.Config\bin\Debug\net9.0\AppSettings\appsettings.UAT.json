{"AppInfo": {"Environment": "UAT"}, "TenantSettings": {"EnableMultiTenantFeatures": true, "TenantCacheExpiry": "00:15:00", "MaxTenantsPerUser": 5, "EnableTenantIsolation": true}, "FeatureFlags": {"EnableSwagger": true, "EnableHangfire": false, "EnableDetailedLogging": false, "EnablePayments": true, "EnableAnalytics": true, "EnableCustomBranding": true, "EnableMultiRegion": false, "EnableAdvancedSecurity": true, "EnableRateLimiting": true, "EnableResponseCompression": true, "EnableHealthChecks": true}, "RateLimiting": {"EnableGlobalRateLimit": true, "GlobalRequestsPerMinute": 5000, "EnablePerTenantRateLimit": true, "TenantRequestsPerMinute": 500, "EnablePerUserRateLimit": true, "UserRequestsPerMinute": 300}, "Security": {"Cors": {"AllowedOrigins": ["https://uat.microsaas.com", "https://uat-app.microsaas.com"], "AllowCredentials": true}, "Headers": {"EnableSecurityHeaders": true, "EnableHsts": true, "HstsMaxAge": ********}}, "Monitoring": {"HealthChecks": {"EnableUI": true, "EnableDetailedErrors": false, "CacheDuration": "00:02:00"}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "Microsoft.AspNetCore.Hosting": "Information", "Microsoft.AspNetCore.Routing": "Warning", "System.Net.Http.HttpClient": "Warning", "Hangfire": "Warning"}, "Console": {"IncludeScopes": false, "TimestampFormat": "yyyy-MM-dd HH:mm:ss "}}, "ExternalServices": {"Stripe": {"EnableTestMode": true}, "Azure": {"Storage": {"ContainerName": "microsaas-uat-files"}}, "Email": {"Smtp": {"Port": 587, "EnableSsl": true}}}}