# =============================================================================
# Configuration Deployment Script
# =============================================================================
# This script deploys environment-specific configuration files
# Usage: .\deploy-config.ps1 -Environment "Development|UAT|Production"
# =============================================================================

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("Development", "UAT", "Production")]
    [string]$Environment,
    
    [Parameter(Mandatory=$false)]
    [string]$TargetPath = "../MicroSaasWebApi.Full",
    
    [Parameter(Mandatory=$false)]
    [switch]$ValidateOnly
)

Write-Host "==============================================================================" -ForegroundColor Cyan
Write-Host "MicroSaaS Configuration Deployment" -ForegroundColor Cyan
Write-Host "Environment: $Environment" -ForegroundColor Yellow
Write-Host "==============================================================================" -ForegroundColor Cyan

# Define source and target files
$configFiles = @{
    "appsettings" = @{
        "source" = "appsettings.$Environment.json"
        "target" = "$TargetPath/appsettings.$Environment.json"
    }
    "env" = @{
        "source" = ".env.$($Environment.ToLower())"
        "target" = "$TargetPath/.env"
    }
}

# Validation function
function Test-ConfigurationFile {
    param($FilePath, $FileType)
    
    if (-not (Test-Path $FilePath)) {
        Write-Error "❌ $FileType file not found: $FilePath"
        return $false
    }
    
    if ($FileType -eq "JSON") {
        try {
            $content = Get-Content $FilePath -Raw | ConvertFrom-Json
            Write-Host "✅ Valid JSON: $FilePath" -ForegroundColor Green
            return $true
        }
        catch {
            Write-Error "❌ Invalid JSON in file: $FilePath - $($_.Exception.Message)"
            return $false
        }
    }
    elseif ($FileType -eq "ENV") {
        $content = Get-Content $FilePath
        $emptyValues = $content | Where-Object { $_ -match "^[^#].*=\s*$" }
        
        if ($emptyValues.Count -gt 0) {
            Write-Warning "⚠️  Empty values found in $FilePath:"
            $emptyValues | ForEach-Object { Write-Warning "   $_" }
        }
        
        Write-Host "✅ ENV file validated: $FilePath" -ForegroundColor Green
        return $true
    }
    
    return $true
}

# Validate configuration files
Write-Host "`n📋 Validating configuration files..." -ForegroundColor Blue

$validationPassed = $true

foreach ($config in $configFiles.GetEnumerator()) {
    $sourceFile = $config.Value.source
    $fileType = if ($config.Key -eq "appsettings") { "JSON" } else { "ENV" }
    
    if (-not (Test-ConfigurationFile $sourceFile $fileType)) {
        $validationPassed = $false
    }
}

if (-not $validationPassed) {
    Write-Error "❌ Configuration validation failed. Please fix the issues above."
    exit 1
}

if ($ValidateOnly) {
    Write-Host "`n✅ Configuration validation completed successfully!" -ForegroundColor Green
    exit 0
}

# Deploy configuration files
Write-Host "`n🚀 Deploying configuration files..." -ForegroundColor Blue

foreach ($config in $configFiles.GetEnumerator()) {
    $sourceFile = $config.Value.source
    $targetFile = $config.Value.target
    $configType = $config.Key.ToUpper()
    
    try {
        # Create target directory if it doesn't exist
        $targetDir = Split-Path $targetFile -Parent
        if (-not (Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }
        
        # Copy file
        Copy-Item $sourceFile $targetFile -Force
        Write-Host "✅ Deployed $configType: $sourceFile → $targetFile" -ForegroundColor Green
        
        # Set appropriate permissions (read-only for production)
        if ($Environment -eq "Production") {
            Set-ItemProperty $targetFile -Name IsReadOnly -Value $true
        }
    }
    catch {
        Write-Error "❌ Failed to deploy $configType file: $($_.Exception.Message)"
        exit 1
    }
}

# Environment-specific post-deployment tasks
switch ($Environment) {
    "Development" {
        Write-Host "`n🔧 Development environment setup..." -ForegroundColor Blue
        Write-Host "   - Swagger UI enabled" -ForegroundColor Gray
        Write-Host "   - Detailed logging enabled" -ForegroundColor Gray
        Write-Host "   - Rate limiting disabled" -ForegroundColor Gray
    }
    "UAT" {
        Write-Host "`n🧪 UAT environment setup..." -ForegroundColor Blue
        Write-Host "   - Swagger UI enabled for testing" -ForegroundColor Gray
        Write-Host "   - Rate limiting enabled" -ForegroundColor Gray
        Write-Host "   - Security headers enabled" -ForegroundColor Gray
    }
    "Production" {
        Write-Host "`n🏭 Production environment setup..." -ForegroundColor Blue
        Write-Host "   - Swagger UI disabled" -ForegroundColor Gray
        Write-Host "   - Minimal logging enabled" -ForegroundColor Gray
        Write-Host "   - Full security features enabled" -ForegroundColor Gray
        Write-Host "   - Configuration files set to read-only" -ForegroundColor Gray
    }
}

Write-Host "`n✅ Configuration deployment completed successfully!" -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Yellow
Write-Host "Target: $TargetPath" -ForegroundColor Yellow

# Display next steps
Write-Host "`n📋 Next Steps:" -ForegroundColor Blue
Write-Host "1. Set ASPNETCORE_ENVIRONMENT=$Environment" -ForegroundColor Gray
Write-Host "2. Restart the application" -ForegroundColor Gray
Write-Host "3. Verify configuration at /health endpoint" -ForegroundColor Gray

if ($Environment -ne "Development") {
    Write-Host "4. Ensure Azure Key Vault access is configured" -ForegroundColor Gray
}
