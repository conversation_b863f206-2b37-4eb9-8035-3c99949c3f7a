# =============================================================================
# SoNoBrokers .NET Web API - Docker Management Script
# Independent Docker setup for .NET Web API only
# =============================================================================

param(
    [Parameter(Position=0)]
    [ValidateSet("build", "up", "down", "restart", "logs", "clean", "dev", "test", "help")]
    [string]$Command = "help",
    
    [switch]$Development,
    [switch]$NoCache,
    [switch]$Verbose,
    [switch]$Help
)

# =============================================================================
# Configuration
# =============================================================================

$ErrorActionPreference = "Stop"
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# =============================================================================
# Helper Functions
# =============================================================================

function Write-Header {
    param([string]$Message)
    Write-Host "=============================================================================" -ForegroundColor Blue
    Write-Host " $Message" -ForegroundColor Blue
    Write-Host "=============================================================================" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "✓ $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠ $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "✗ $Message" -ForegroundColor Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ $Message" -ForegroundColor Cyan
}

function Show-Usage {
    @"
Usage: .\docker-run.ps1 [COMMAND] [OPTIONS]

SoNoBrokers .NET Web API - Independent Docker Management

COMMANDS:
    build       Build the .NET Web API Docker image
    up          Start the .NET Web API container
    down        Stop the .NET Web API container
    restart     Restart the .NET Web API container
    logs        Show API logs
    clean       Clean up containers and images
    dev         Start in development mode with hot reload
    test        Run API health check tests
    help        Show this help message

OPTIONS:
    -Development    Start in development mode
    -NoCache        Build without cache
    -Verbose        Verbose output
    -Help           Show this help message

EXAMPLES:
    .\docker-run.ps1 build                     # Build production image
    .\docker-run.ps1 up                        # Start production container
    .\docker-run.ps1 dev                       # Start development with hot reload
    .\docker-run.ps1 build -NoCache            # Build without cache
    .\docker-run.ps1 logs                      # View API logs
    .\docker-run.ps1 test                      # Test API health

IMPORTANT:
    This script runs ONLY the .NET Web API backend.
    The API will be available at http://localhost:8080
    Make sure your database (Supabase) is accessible.
    Update connection strings in .env file.

"@
}

function Test-Dependencies {
    Write-Info "Checking dependencies..."
    
    if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
        Write-Error "Docker is not installed or not in PATH"
        exit 1
    }
    
    if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue)) {
        Write-Error "Docker Compose is not installed or not in PATH"
        exit 1
    }
    
    Write-Success "All dependencies are available"
}

function Initialize-Environment {
    Write-Info "Setting up environment..."
    
    Set-Location $ScriptDir
    
    # Create .env file if it doesn't exist
    $envFile = Join-Path $ScriptDir ".env"
    $envDockerFile = Join-Path $ScriptDir ".env.docker"
    
    if (-not (Test-Path $envFile)) {
        if (Test-Path $envDockerFile) {
            Write-Warning ".env not found, copying from .env.docker"
            Copy-Item $envDockerFile $envFile
            Write-Success ".env file created"
            Write-Warning "Please update .env with your configuration"
        } else {
            Write-Warning "No environment template found"
        }
    } else {
        Write-Success ".env file exists"
    }
    
    # Set build arguments
    $env:BUILD_DATE = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
    
    try {
        $env:VCS_REF = (git rev-parse --short HEAD 2>$null)
    } catch {
        $env:VCS_REF = "unknown"
    }
    
    try {
        $env:VERSION = (git describe --tags --always 2>$null)
    } catch {
        $env:VERSION = "1.0.0"
    }
    
    Write-Success "Environment setup complete"
}

function Get-ComposeFiles {
    $files = @("-f", "docker-compose.yml")
    
    if ($Development) {
        $files += @("-f", "docker-compose.dev.yml")
    }
    
    return $files
}

# =============================================================================
# Command Functions
# =============================================================================

function Invoke-Build {
    Write-Header "Building SoNoBrokers .NET Web API"
    
    $buildArgs = @()
    if ($NoCache) {
        $buildArgs += "--no-cache"
    }
    
    $composeFiles = Get-ComposeFiles
    $allArgs = $composeFiles + @("build") + $buildArgs + @("api")
    
    Write-Info "Building .NET Web API..."
    if ($Verbose) {
        & docker-compose @allArgs
    } else {
        & docker-compose @allArgs 2>$null | Out-Null
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success ".NET Web API built successfully"
    } else {
        Write-Error "Failed to build .NET Web API"
        exit 1
    }
}

function Invoke-Up {
    Write-Header "Starting SoNoBrokers .NET Web API"
    
    $mode = if ($Development) { "development" } else { "production" }
    Write-Info "Starting .NET Web API in $mode mode..."
    
    $composeFiles = Get-ComposeFiles
    & docker-compose @composeFiles up -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success ".NET Web API started successfully"
        Write-Info "API URL: http://localhost:8080"
        Write-Info "Health Check: http://localhost:8080/health"
        Write-Info "Swagger UI: http://localhost:8080/swagger"
        
        # Wait a moment and test health
        Start-Sleep -Seconds 5
        Test-ApiHealth
    } else {
        Write-Error "Failed to start .NET Web API"
        exit 1
    }
}

function Invoke-Down {
    Write-Header "Stopping SoNoBrokers .NET Web API"
    
    $composeFiles = Get-ComposeFiles
    & docker-compose @composeFiles down
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success ".NET Web API stopped successfully"
    } else {
        Write-Error "Failed to stop .NET Web API"
        exit 1
    }
}

function Invoke-Restart {
    Write-Header "Restarting SoNoBrokers .NET Web API"
    
    Invoke-Down
    Invoke-Up
}

function Invoke-Logs {
    Write-Header "Showing .NET Web API Logs"
    
    $composeFiles = Get-ComposeFiles
    & docker-compose @composeFiles logs -f api
}

function Invoke-Clean {
    Write-Header "Cleaning up .NET Web API Resources"
    
    Write-Warning "This will remove containers, images, and volumes for the .NET Web API"
    $confirmation = Read-Host "Are you sure? (y/N)"
    
    if ($confirmation -match "^[Yy]$") {
        $composeFiles = Get-ComposeFiles
        & docker-compose @composeFiles down -v --rmi all --remove-orphans
        Write-Success "Cleanup completed"
    } else {
        Write-Info "Cleanup cancelled"
    }
}

function Invoke-Dev {
    Write-Header "Starting Development Environment"
    
    $script:Development = $true
    Invoke-Build
    Invoke-Up
    
    Write-Info "Development environment started with hot reload"
    Write-Info "API URL: http://localhost:8080"
    Write-Info "Swagger UI: http://localhost:8080/swagger"
}

function Test-ApiHealth {
    Write-Info "Testing API health..."
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8080/health" -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Success "API is healthy and responding"
        } else {
            Write-Warning "API returned status code: $($response.StatusCode)"
        }
    } catch {
        Write-Warning "API is not responding yet (this may be normal during startup)"
        Write-Info "You can manually check: http://localhost:8080/health"
    }
}

function Invoke-Test {
    Write-Header "Testing SoNoBrokers .NET Web API"
    
    Test-ApiHealth
    
    # Test Swagger endpoint
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8080/swagger" -UseBasicParsing -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Success "Swagger UI is accessible"
        }
    } catch {
        Write-Warning "Swagger UI is not accessible"
    }
    
    Write-Info "Manual testing URLs:"
    Write-Host "  Health Check: http://localhost:8080/health" -ForegroundColor Cyan
    Write-Host "  Swagger UI: http://localhost:8080/swagger" -ForegroundColor Cyan
    Write-Host "  API Base: http://localhost:8080/api" -ForegroundColor Cyan
}

# =============================================================================
# Main Script
# =============================================================================

function Main {
    if ($Help -or $Command -eq "help") {
        Show-Usage
        return
    }
    
    # Change to script directory
    Set-Location $ScriptDir
    
    # Run pre-checks
    Test-Dependencies
    Initialize-Environment
    
    # Execute command
    switch ($Command) {
        "build" { Invoke-Build }
        "up" { Invoke-Up }
        "down" { Invoke-Down }
        "restart" { Invoke-Restart }
        "logs" { Invoke-Logs }
        "clean" { Invoke-Clean }
        "dev" { Invoke-Dev }
        "test" { Invoke-Test }
        default {
            Write-Error "Unknown command: $Command"
            Show-Usage
            exit 1
        }
    }
}

# Run main function
Main
