'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import config from '@/config';
import { Facebook, Twitter, Linkedin, Mail, Phone, MapPin, Home, Search, Users } from 'lucide-react';
import { useAppContext } from '@/contexts/AppContext';

export function FooterClient() {
  const pathname = usePathname();
  const { userType, country } = useAppContext();

  // Convert country enum to lowercase for URLs
  const currentRegion = country.toLowerCase();

  // Don't show footer on certain pages
  if (['/login', '/signup', '/reset-password'].includes(pathname)) {
    return null;
  }

  return (
    <>
      {/* Main Footer */}
      <footer className="w-full border-t border-border bg-muted">
        <div className="w-full px-4 py-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 w-full">
            {/* Company Info */}
            <div>
              <h3 className="text-lg font-semibold mb-4">SoNo Brokers</h3>
              <p className="text-muted-foreground mb-4">
                Commission-Free Real Estate Platform. Buy and sell properties directly,
                save on commission fees, and access professional services on-demand.
              </p>
              <div className="flex space-x-4">
                <Link href={config.socialLinks.twitter} target="_blank" rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-primary transition-colors">
                  <Twitter size={20} />
                </Link>
                <Link href={config.socialLinks.facebook} target="_blank" rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-primary transition-colors">
                  <Facebook size={20} />
                </Link>
                <Link href={config.socialLinks.linkedin} target="_blank" rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-primary transition-colors">
                  <Linkedin size={20} />
                </Link>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-lg font-semibold mb-4">
                {userType === 'Buyer' ? 'For Buyers' : 'For Sellers'}
              </h3>
              <ul className="space-y-2">
                <li>
                  <Link href={`/${currentRegion}`} className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2">
                    <Home size={16} />
                    {country === 'CA' ? 'Canada Home' : country === 'US' ? 'USA Home' : 'UAE Home'}
                  </Link>
                </li>
                <li>
                  <Link href={`/${currentRegion}/properties`} className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2">
                    <Search size={16} />
                    {userType === 'Buyer' ? 'Browse Properties' : 'View Listings'}
                  </Link>
                </li>
                {userType === 'Seller' && (
                  <li>
                    <Link href={`/${currentRegion}/list-property`} className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-2">
                      <Users size={16} />
                      List Property
                    </Link>
                  </li>
                )}
                <li>
                  <Link href={`/${currentRegion}/services`} className="text-muted-foreground hover:text-primary transition-colors">
                    Professional Services
                  </Link>
                </li>
                <li>
                  <Link href={`/${currentRegion}/how-it-works`} className="text-muted-foreground hover:text-primary transition-colors">
                    How It Works
                  </Link>
                </li>
                <li>
                  <Link href="/about" className="text-muted-foreground hover:text-primary transition-colors">
                    About Us
                  </Link>
                </li>
              </ul>
            </div>

            {/* Contact Info & Legal */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Contact & Legal</h3>
              <ul className="space-y-3 mb-6">
                <li className="flex items-center gap-2 text-muted-foreground">
                  <Mail size={18} />
                  <a href={`mailto:${config.contact.email}`} className="hover:text-primary transition-colors">
                    {config.contact.email}
                  </a>
                </li>
                <li className="flex items-center gap-2 text-muted-foreground">
                  <Phone size={18} />
                  <a href={`tel:${config.contact.phone}`} className="hover:text-primary transition-colors">
                    {config.contact.phone}
                  </a>
                </li>
              </ul>

              {/* Legal Links */}
              <div className="space-y-2">
                <Link href="/terms" className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                  Terms of Service
                </Link>
                <Link href="/privacy-policy" className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                  Privacy Policy
                </Link>
                <Link href="/cookies" className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                  Cookie Policy
                </Link>
              </div>
            </div>
          </div>
        </div>
      </footer>

      {/* Country-Specific Legal Notice - Separate Section */}
      <div className="w-full bg-muted/50 border-t border-border">
        <div className="w-full px-4 py-6">
          <h4 className="text-sm font-semibold mb-4 text-foreground">SoNoBrokers Marketplace Legal Notice</h4>
          <div className="text-xs text-muted-foreground space-y-4 leading-relaxed">

            {/* Platform Description - Always shown */}
            <div className="bg-background p-4 rounded-lg border border-border">
              <h5 className="font-semibold text-foreground mb-2">Platform Description</h5>
              <p>
                SoNoBrokers operates as a digital marketplace platform that facilitates direct transactions between property owners, builders, contractors, and buyers/renters. Our platform enables For Sale By Owner (FSBO), For Rent By Owner (FRBO), new construction sales, and commercial property transactions without traditional real estate brokerage intermediaries. We provide a marketplace experience connecting parties directly while offering optional professional services on-demand.
              </p>
            </div>

            {/* Canada-Specific Legal Notice */}
            {country === 'CA' && (
              <div className="border-l-4 border-primary pl-4">
                <h5 className="font-semibold text-foreground mb-2">🇨🇦 CANADA - Legal Compliance & Trademark Notice</h5>
                <div className="space-y-3">
                  <p>
                    <strong>MLS® and REALTOR® Trademark Notice:</strong> The trademarks MLS®, Multiple Listing Service® and associated logos are owned by The Canadian Real Estate Association (CREA) and identify services provided by real estate professionals who are CREA members. The trademarks REALTOR®, REALTORS® and the REALTOR® logo are controlled by CREA and identify real estate professionals who are CREA members. SoNoBrokers operates independently and is not affiliated with CREA or MLS® systems.
                  </p>
                  <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-3">
                    <p>
                      <strong className="text-destructive">REALTOR® and Broker Listing Prohibition:</strong> SoNoBrokers is exclusively designed for direct property transactions by owners, builders, and contractors. Licensed real estate professionals (REALTORS®, brokers, or agents) are strictly prohibited from listing properties on our platform that are also listed on MLS® systems or represented under brokerage agreements. Any listings identified as being created by licensed real estate professionals in violation of this policy will result in immediate removal without notice, forfeiture of all fees paid, account suspension, and potential legal action. SoNoBrokers reserves the right to verify listing authenticity and take enforcement action to maintain platform integrity.
                    </p>
                  </div>
                  <p>
                    <strong>Technology Platform Operation:</strong> SoNoBrokers operates as a technology platform facilitating direct property transactions and does not provide real estate brokerage services requiring provincial licensing. We function as a digital marketplace connecting property owners, builders, contractors, and buyers directly without acting as intermediaries, representatives, or agents in real estate transactions. Our platform does not engage in activities that would require real estate licensing under provincial regulations, including representing parties, negotiating on behalf of clients, or providing real estate advice or opinions on property values.
                  </p>
                  <p>
                    <strong>Consumer Protection:</strong> All transactions comply with provincial Consumer Protection Acts, Personal Information Protection Acts (PIPA/PIPEDA), and applicable provincial legislation. Users are advised to seek independent legal counsel for complex transactions and to verify all property information independently.
                  </p>
                  <p>
                    <strong>Tax Compliance:</strong> Users are responsible for compliance with federal and provincial tax obligations including HST/GST, Property Transfer Tax, Land Transfer Tax, and capital gains reporting as applicable in their province.
                  </p>
                </div>
              </div>
            )}

            {/* USA-Specific Legal Notice */}
            {country === 'US' && (
              <div className="border-l-4 border-secondary pl-4">
                <h5 className="font-semibold text-foreground mb-2">🇺🇸 UNITED STATES - Legal Compliance & Disclaimer</h5>
                <div className="space-y-3">
                  <p>
                    <strong>NAR and MLS® Compliance:</strong> SoNoBrokers operates independently of the National Association of REALTORS® (NAR) and local Multiple Listing Services (MLS). We do not provide traditional real estate brokerage services and are not bound by NAR rules or MLS participation requirements. REALTOR® is a registered trademark of NAR. We facilitate direct property transactions between private parties without requiring real estate licensing.
                  </p>
                  <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-3">
                    <p>
                      <strong className="text-destructive">Licensed Real Estate Professional Prohibition:</strong> SoNoBrokers is exclusively designed for For Sale By Owner (FSBO) and direct property transactions. Licensed real estate professionals including REALTORS®, brokers, agents, or salespersons are strictly prohibited from listing properties on our platform that are concurrently listed on MLS systems, under exclusive listing agreements, or represented through licensed brokerage relationships. Violations of this policy will result in immediate listing removal, forfeiture of all fees and payments, permanent account termination, and potential legal action for breach of terms. SoNoBrokers maintains the right to conduct verification procedures and enforce compliance to preserve the integrity of our direct-transaction marketplace.
                    </p>
                  </div>
                  <p>
                    <strong>Technology Platform Operation:</strong> SoNoBrokers operates as a technology platform in all 50 states and the District of Columbia, facilitating direct property transactions between private parties without providing real estate brokerage services requiring state licensing. We function as a digital marketplace connecting property owners, builders, contractors, and buyers directly without acting as intermediaries, representatives, or agents in real estate transactions. Our platform does not engage in activities that would require real estate licensing under state regulations, including representing parties in transactions, negotiating on behalf of clients, providing real estate advice, or offering opinions on property values or market conditions.
                  </p>
                  <p>
                    <strong>Federal Compliance:</strong> All transactions comply with federal laws including the Fair Housing Act (42 U.S.C. § 3601 et seq.), Real Estate Settlement Procedures Act (RESPA), Truth in Lending Act (TILA), Equal Credit Opportunity Act (ECOA), and Americans with Disabilities Act (ADA). Anti-money laundering (AML) and Know Your Customer (KYC) procedures are implemented as required by the Bank Secrecy Act and USA PATRIOT Act.
                  </p>
                  <p>
                    <strong>State-Specific Disclosures:</strong> Users must comply with state-specific disclosure requirements including lead-based paint disclosures, natural hazard disclosures, and seller disclosure statements as required by state law. Professional legal and real estate services are recommended for complex transactions.
                  </p>
                  <p>
                    <strong>Consumer Protection:</strong> Platform complies with state consumer protection laws, privacy regulations including CCPA (California), and applicable state data protection requirements. Users are responsible for verifying all property information and conducting appropriate due diligence.
                  </p>
                </div>
              </div>
            )}

            {/* UAE-Specific Legal Notice */}
            {country === 'UAE' && (
              <div className="border-l-4 border-accent pl-4">
                <h5 className="font-semibold text-foreground mb-2">🇦🇪 UNITED ARAB EMIRATES - Legal Compliance & RERA Notice</h5>
                <div className="space-y-3">
                  <p>
                    <strong>Technology Platform Operation:</strong> SoNoBrokers operates as a technology platform facilitating direct property transactions across all Emirates without providing traditional real estate brokerage services requiring RERA broker registration (Category 1 or 2 licenses). We function as a digital marketplace connecting property owners, developers, contractors, and buyers directly without acting as intermediaries, representatives, or agents in real estate transactions. Our platform does not engage in activities that would require RERA licensing, including representing parties in transactions, negotiating on behalf of clients, providing real estate advice, or offering professional opinions on property values or market conditions under UAE real estate regulations.
                  </p>
                  <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-3">
                    <p>
                      <strong className="text-destructive">Licensed Real Estate Professional Prohibition:</strong> SoNoBrokers is exclusively designed for direct property transactions by owners, developers, and contractors. RERA-licensed real estate brokers, agents, or professionals holding Category 1 or Category 2 licenses are strictly prohibited from listing properties on our platform that are concurrently marketed through traditional brokerage channels or under exclusive agency agreements. Any listings identified as being created by licensed real estate professionals in violation of this policy will result in immediate removal without prior notice, complete forfeiture of all fees and payments, permanent account suspension, and potential legal action under UAE commercial law. SoNoBrokers reserves comprehensive rights to verify listing authenticity and enforce compliance to maintain the integrity of our direct-transaction marketplace.
                    </p>
                  </div>
                  <p>
                    <strong>Dubai Land Department (DLD) Compliance:</strong> All property transactions must comply with DLD regulations including Law No. 7 of 2006 (Real Estate Law), proper registration procedures, title transfer requirements, and payment of applicable fees including DLD fees (4% of property value). Users are responsible for ensuring compliance with Ejari registration requirements for rental properties and DEWA connection procedures.
                  </p>
                  <p>
                    <strong>Federal and Emirate Laws:</strong> Transactions comply with UAE Federal Law No. 5 of 1985 (Civil Transactions Law), UAE Federal Law No. 27 of 2006 (Real Estate Investment Law), and emirate-specific regulations. Foreign ownership restrictions, freehold/leasehold designations, and designated investment areas are clearly disclosed. Off-plan property sales comply with Law No. 8 of 2007 and escrow account requirements.
                  </p>
                  <p>
                    <strong>Islamic Finance Compliance:</strong> Platform supports both conventional and Sharia-compliant financing options. Islamic finance products comply with UAE Central Bank regulations, Islamic banking principles, and Sharia supervisory board requirements. Riba (interest) and Gharar (excessive uncertainty) prohibitions are observed in Islamic transactions.
                  </p>
                  <p>
                    <strong>Anti-Money Laundering & KYC:</strong> Strict AML/KYC procedures are implemented in compliance with UAE Central Bank Regulation No. 24/2019, Financial Intelligence Unit requirements, and Cabinet Resolution No. 10 of 2019. Enhanced due diligence is performed for high-value transactions and politically exposed persons (PEPs).
                  </p>
                  <p>
                    <strong>Data Protection:</strong> Platform complies with UAE Data Protection Law (Federal Decree-Law No. 45 of 2021) and Dubai Data Law (Law No. 26 of 2015) regarding personal data processing, storage, and cross-border transfers.
                  </p>
                </div>
              </div>
            )}

            {/* General Disclaimers - Always shown */}
            <div className="bg-background p-4 rounded-lg border border-border">
              <h5 className="font-semibold text-foreground mb-2">General Disclaimers</h5>
              <div className="space-y-2">
                <p>
                  <strong>No Brokerage Services:</strong> SoNoBrokers does not provide real estate brokerage services, does not represent buyers or sellers in transactions, does not receive commissions from property sales, and does not hold client funds. We operate solely as a technology platform facilitating direct connections between parties.
                </p>
                <p>
                  <strong>Professional Services Network:</strong> Licensed professionals (lawyers, mortgage brokers, inspectors, photographers, appraisers) in our network operate independently and maintain their own licenses, professional insurance, and regulatory compliance. SoNoBrokers does not guarantee their services or performance.
                </p>
                <p>
                  <strong>User Responsibility:</strong> Users are responsible for conducting due diligence, verifying property information, obtaining appropriate legal counsel, securing proper financing, and ensuring compliance with all applicable laws and regulations in their jurisdiction. Independent professional advice is strongly recommended.
                </p>
                <p>
                  <strong>Limitation of Liability:</strong> SoNoBrokers provides the platform "as is" without warranties and disclaims liability for transaction outcomes, property conditions, user compliance with applicable laws, or actions of third-party service providers. Users assume full responsibility for their transactions and decisions.
                </p>
              </div>
            </div>

            <p className="text-center font-medium">
              For legal questions or compliance concerns, contact: <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a>
            </p>
          </div>
        </div>
      </div>

      {/* Copyright - Separate Section */}
      <div className="w-full bg-background border-t border-border">
        <div className="w-full px-4 py-4">
          <div className="text-center">
            <p className="text-sm text-muted-foreground">
              © 2025 SoNo Brokers. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
