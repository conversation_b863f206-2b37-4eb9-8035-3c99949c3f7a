﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>aspnet-MicroSaasWebApi-ba0483c2-66a7-482d-abad-f27be7006d0c</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<DockerfileContext>.</DockerfileContext>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<NoWarn>$(NoWarn);1591</NoWarn>
		<!-- Explicit version to avoid git-based version issues -->
		<Version>1.0.0</Version>
		<AssemblyVersion>1.0.0.0</AssemblyVersion>
		<FileVersion>1.0.0.0</FileVersion>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Functions\**" />
		<Compile Remove="Models\PHW\**" />
		<Compile Remove="Models\Sharepoint\**" />
		<Compile Remove="Properties\PHW\**" />
		<Compile Remove="Services\DataAPI\**" />
		<Compile Remove="Services\PHW\**" />
		<Compile Remove="Tenants\**" />
		<Content Remove="Functions\**" />
		<Content Remove="Models\PHW\**" />
		<Content Remove="Models\Sharepoint\**" />
		<Content Remove="Properties\PHW\**" />
		<Content Remove="Services\DataAPI\**" />
		<Content Remove="Services\PHW\**" />
		<Content Remove="Tenants\**" />
		<EmbeddedResource Remove="Functions\**" />
		<EmbeddedResource Remove="Models\PHW\**" />
		<EmbeddedResource Remove="Models\Sharepoint\**" />
		<EmbeddedResource Remove="Properties\PHW\**" />
		<EmbeddedResource Remove="Services\DataAPI\**" />
		<EmbeddedResource Remove="Services\PHW\**" />
		<EmbeddedResource Remove="Tenants\**" />
		<None Remove="Functions\**" />
		<None Remove="Models\PHW\**" />
		<None Remove="Models\Sharepoint\**" />
		<None Remove="Properties\PHW\**" />
		<None Remove="Services\DataAPI\**" />
		<None Remove="Services\PHW\**" />
		<None Remove="Tenants\**" />
		<_WebToolingArtifacts Remove="Properties\PHW\**" />
	</ItemGroup>



	<ItemGroup>
	  <Content Remove="Assets\PAL\AppSettings\appsettings_PAL_UAT.json" />
	</ItemGroup>

	<ItemGroup>
		<Content Include=".config\dotnet-tools.json" />
	</ItemGroup>

	<ItemGroup>
		<!-- Health Checks for .NET 9 -->
		<PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="9.0.0" />
		<PackageReference Include="AspNetCore.HealthChecks.UI" Version="8.0.2" />
		<PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="8.0.1" />
		<PackageReference Include="AspNetCore.HealthChecks.UI.Core" Version="8.0.1" />
		<PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="8.0.1" />
		<PackageReference Include="AspNetCore.HealthChecks.Npgsql" Version="8.0.2" />
		<PackageReference Include="AspNetCore.HealthChecks.Uris" Version="8.0.1" />
		<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.1" />
		<PackageReference Include="Azure.Identity" Version="1.12.0" />
		<PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.6.0" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.24.0" />
		<PackageReference Include="Markdig" Version="0.34.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.AzureADB2C.UI" Version="6.0.26" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="9.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
		<!-- .NET 9 Native OpenAPI Support -->
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0" />
		<PackageReference Include="Scalar.AspNetCore" Version="1.2.42" />

		<PackageReference Include="Microsoft.Azure.AppConfiguration.AspNetCore" Version="7.1.0" />
		<PackageReference Include="Microsoft.Azure.KeyVault" Version="3.0.5" />
		<PackageReference Include="Microsoft.Extensions.Configuration.AzureAppConfiguration" Version="7.1.0" />
		<PackageReference Include="Microsoft.Graph" Version="5.80.0" />
		<PackageReference Include="Microsoft.Identity.Web" Version="2.16.1" />
		<PackageReference Include="Microsoft.Identity.Web.UI" Version="2.16.1" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.6" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.0" />
		<PackageReference Include="NWebsec.AspNetCore.Middleware" Version="3.0.0" />
		<PackageReference Include="RestSharp" Version="112.0.0" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5">
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		  <PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		  <PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="Dapper" Version="2.1.35" />
		<PackageReference Include="QRCoder" Version="1.4.3" />

		<!-- Clerk Authentication - Updated to compatible version -->
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.6.1" />

		<!-- SignalR is included in ASP.NET Core framework for .NET 8 - no separate package needed -->

		<!-- Stripe Payment Integration -->
		<PackageReference Include="Stripe.net" Version="43.15.0" />

		<!-- Azure Services -->
		<PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.0" />
		<PackageReference Include="DotNetEnv" Version="3.0.0" />

		<!-- Rate Limiting -->
		<PackageReference Include="AspNetCoreRateLimit" Version="4.0.2" />

		<!-- Additional Security -->
		<PackageReference Include="Microsoft.AspNetCore.DataProtection" Version="9.0.0" />

		<!-- Logging and Monitoring -->
		<PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="5.0.0" />
		<PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />

		<!-- Background Services -->
		<PackageReference Include="Hangfire.Core" Version="1.8.5" />
		<PackageReference Include="Hangfire.PostgreSql" Version="1.20.0" />
		<PackageReference Include="Hangfire.AspNetCore" Version="1.8.5" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Assets\PXW\Postman\" />
		<Folder Include="Services\Sharepoint\PHW\" />
	</ItemGroup>

	<!-- Project reference temporarily removed due to version string issue -->
	<!--
	<ItemGroup>
		<ProjectReference Include="..\MicroSaasWebApi.Config\MicroSaasWebApi.Config.csproj" />
	</ItemGroup>
	-->

	<!-- SharePointAPI project reference removed - project does not exist -->
	<ItemGroup>
		<AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
			<_Parameter1>MicroSaasWebApi.Tests.Unit</_Parameter1>
		</AssemblyAttribute>
	</ItemGroup>
	<ItemGroup>
	  <None Include="Assets\PAL\AppSettings\appsettings_PAL_UAT.json" />
	</ItemGroup>

	<ItemGroup>
		<Content Update="appsettings.phw-react.Development.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="appsettings.phw-react.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
	</ItemGroup>

</Project>
