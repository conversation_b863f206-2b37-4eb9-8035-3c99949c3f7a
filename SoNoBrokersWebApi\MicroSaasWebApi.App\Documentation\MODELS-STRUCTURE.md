# 📁 MicroSaaS Web API - Models Folder Structure

## 🎯 **Clean Core-Based Architecture**

```
Models/
├── 📁 Core/                           # Core business models (NEW STRUCTURE)
│   ├── 📄 User.cs                     # Core user model with all properties
│   ├── 📄 Product.cs                  # Product model with complete CRUD fields
│   ├── 📄 Document.cs                 # Document/file management model
│   ├── 📄 Subscription.cs             # Payment/subscription model
│   ├── 📄 UserRole.cs                 # User authorization model
│   ├── 📄 ApiResponse.cs              # Standard API response wrapper
│   └── 📁 Interfaces/                 # Core model interfaces
│       ├── 📄 IEntity.cs              # Base entity interface
│       ├── 📄 IAuditable.cs           # Audit trail interface
│       └── 📄 ISoftDeletable.cs       # Soft delete interface
│
├── 📁 Auth/                           # Authentication models
│   ├── 📄 AuthModels.cs               # Login/register models
│   ├── 📁 Clerk/                      # Clerk-specific models
│   ├── 📁 Azure/                      # Azure AD models
│   ├── 📁 Google/                     # Google OAuth models
│   ├── 📁 AWS/                        # AWS Cognito models
│   └── 📁 Interfaces/                 # Auth interfaces
│
├── 📁 Payment/                        # Payment & billing models
│   ├── 📄 PaymentModels.cs            # Stripe payment models
│   └── 📁 Interfaces/                 # Payment interfaces
│
├── 📁 Storage/                        # File storage models
│   ├── 📄 BlobStorageModels.cs        # Azure Blob storage models
│   ├── 📄 SharePointModels.cs         # SharePoint integration models
│   └── 📁 Interfaces/                 # Storage interfaces
│
├── 📁 Configuration/                  # App configuration models
│   ├── 📄 AppSettings.cs              # Application settings
│   └── 📁 Interfaces/                 # Configuration interfaces
│
├── 📁 Profile/                        # User profile models (LEGACY - TO BE MERGED)
│   ├── 📄 UserProfile.cs              # Extended user profile
│   ├── 📄 Account.cs                  # Account information
│   ├── 📄 AccountInformation.cs       # Account details
│   ├── 📄 AccountMember.cs            # Account membership
│   ├── 📄 AccountProfile.cs           # Account profile
│   ├── 📄 FileDetail.cs               # File details
│   └── 📁 Interfaces/                 # Profile interfaces
│
├── 📁 Data/                           # Data models (LEGACY - TO BE MERGED)
│   └── 📄 Product.cs                  # Legacy product model
│
├── 📁 Document/                       # Document models (LEGACY - TO BE MERGED)
│   ├── 📄 DocumentType.cs             # Legacy document type
│   └── 📁 Interfaces/                 # Document interfaces
│
├── 📁 Database/                       # Database models (LEGACY - TO BE MERGED)
│   └── 📄 MicroSaasModels.cs          # Legacy database models
│
├── 📁 Error/                          # Error handling models
│   └── 📄 ErrorResponse.cs            # Error response model
│
└── 📁 Request/                        # Request models (CLEANED UP)
    └── 📄 RequestBody.cs              # Simplified request body
```

## 🔄 **Migration Strategy**

### **✅ Completed**
- ✅ Created new `Models/Core/` structure
- ✅ Removed Tenant-related configurations from appsettings
- ✅ Removed Base folder references
- ✅ Created clean core models with all required properties

### **🔄 Next Steps**
1. **Merge Legacy Models** - Consolidate Data, Document, Database, Profile into Core
2. **Create Controllers** - Generate CRUD controllers for each Core model
3. **Create Services** - Generate service layer following same structure
4. **Update Repositories** - Create repository interfaces and implementations
5. **Clean Middlewares** - Remove tenant-specific middlewares

## 🎯 **Core Models Overview**

### **User Model**
- Complete user management with Clerk integration
- Profile information, preferences, audit trails
- Role-based authorization support

### **Product Model**
- Full e-commerce product management
- Inventory, pricing, categorization
- Digital and physical product support

### **Document Model**
- File management with SharePoint integration
- Metadata, categorization, access control
- Version control and audit trails

### **Subscription Model**
- Stripe integration for payments
- Trial periods, billing cycles
- Subscription lifecycle management

### **UserRole Model**
- Granular permission system
- Scope-based authorization
- Role assignment audit trails

### **ApiResponse Model**
- Standardized API responses
- Error handling and messaging
- Request tracking and timestamps

## 🏗️ **Architecture Benefits**

### **✅ Simplified Structure**
- Single Core namespace for all business models
- No tenant complexity in models
- Clear separation of concerns

### **✅ CRUD Ready**
- All models have complete property sets
- Ready for Entity Framework integration
- Audit trails and soft delete support

### **✅ API Ready**
- Standardized response models
- Error handling built-in
- Request/response patterns established

### **✅ Scalable Design**
- Interface-based architecture
- Dependency injection ready
- Service layer separation

## 🔧 **Next Implementation Phase**

1. **Controllers**: Create CRUD controllers for each Core model
2. **Services**: Implement business logic services
3. **Repositories**: Create data access layer
4. **Middlewares**: Add necessary middleware for Core functionality
5. **Documentation**: Update all documentation to reflect new structure
