# =============================================================================
# SoNoBrokers .NET 9 Web API Dockerfile
# Multi-stage build for optimized production image
# =============================================================================

# Stage 1: Base runtime image
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
LABEL stage=base
LABEL description="Base runtime environment"

WORKDIR /app

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Install required packages for health checks and debugging
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:8080
ENV DOTNET_RUNNING_IN_CONTAINER=true
ENV DOTNET_USE_POLLING_FILE_WATCHER=true

# Expose ports
EXPOSE 8080

# =============================================================================
# Stage 2: Build environment
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
LABEL stage=build
LABEL description="Build environment"

WORKDIR /src

# Copy project files for dependency resolution
COPY ["MicroSaasWebApi.App.csproj", "./"]

# Restore dependencies
RUN dotnet restore "MicroSaasWebApi.App.csproj"

# Copy source code
COPY . .

# Build the application
RUN dotnet build "MicroSaasWebApi.App.csproj" -c Release -o /app/build --no-restore

# =============================================================================
# Stage 3: Publish
FROM build AS publish
LABEL stage=publish
LABEL description="Publish application"

RUN dotnet publish "MicroSaasWebApi.App.csproj" \
    -c Release \
    -o /app/publish \
    --no-restore \
    /p:UseAppHost=false

# =============================================================================
# Stage 4: Final runtime image
FROM base AS final
LABEL stage=final
LABEL description="Production runtime"

WORKDIR /app

# Copy published application
COPY --from=publish /app/publish .

# Configuration files are managed via environment variables

# Set correct permissions
RUN chown -R appuser:appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Start the application
ENTRYPOINT ["dotnet", "MicroSaasWebApi.App.dll"]

# =============================================================================
# Development Stage (Optional)
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS development
LABEL stage=development
LABEL description="Development environment"

WORKDIR /src

# Install development tools
RUN dotnet tool install --global dotnet-ef
RUN dotnet tool install --global dotnet-watch
ENV PATH="$PATH:/root/.dotnet/tools"

# Copy source code
COPY . .

# Restore dependencies
RUN dotnet restore "MicroSaasWebApi.App/MicroSaasWebApi.App.csproj"

# Set development environment
ENV ASPNETCORE_ENVIRONMENT=Development
ENV ASPNETCORE_URLS=http://+:8080

EXPOSE 8080

# Start development server with hot reload
CMD ["dotnet", "watch", "run", "--project", "MicroSaasWebApi.App", "--urls", "http://0.0.0.0:8080"]

# =============================================================================
# Build Arguments and Labels
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION

LABEL org.label-schema.build-date=$BUILD_DATE \
    org.label-schema.name="SoNoBrokers Web API" \
    org.label-schema.description="SoNoBrokers .NET 9 Web API backend" \
    org.label-schema.url="https://api.sonobrokers.com" \
    org.label-schema.vcs-ref=$VCS_REF \
    org.label-schema.vcs-url="https://github.com/sonobrokers/sonobrokers-webapi" \
    org.label-schema.vendor="SoNoBrokers" \
    org.label-schema.version=$VERSION \
    org.label-schema.schema-version="1.0"
