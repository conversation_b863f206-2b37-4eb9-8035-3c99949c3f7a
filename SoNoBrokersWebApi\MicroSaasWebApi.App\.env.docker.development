﻿# =============================================================================
# Development Docker Configuration for SoNoBrokers Web API
# Overrides base settings for development environment
# =============================================================================

# =============================================================================
# Application Configuration (Development)
# =============================================================================
ASPNETCORE_ENVIRONMENT=Development
ASPNETCORE_URLS=http://+:8080

# =============================================================================
# Database Configuration (Development)
# =============================================================================
DATABASE_URL=***************************************************************************/postgres

# =============================================================================
# Authentication - Clerk Configuration (Development)
# =============================================================================
Authentication__Clerk__PublishableKey=pk_test_YmV0dGVyLXBvc3N1bS01OC5jbGVyay5hY2NvdW50cy5kZXYk
Authentication__Clerk__SecretKey=sk_test_VpPsw9paA18Uka7jxuB6PeE3bLvzKVEESGaDuysAgL
Authentication__Clerk__WebhookSecret=whsec_local_dev_webhook_secret
Authentication__Clerk__JwtIssuer=https://better-possum-58.clerk.accounts.dev
CLERK_SECRET_KEY=sk_test_VpPsw9paA18Uka7jxuB6PeE3bLvzKVEESGaDuysAgL

# =============================================================================
# JWT Configuration (Development)
# =============================================================================
Authentication__Jwt__Issuer=https://localhost:7163
Authentication__Jwt__SigningKey=local_dev_jwt_signing_key_minimum_32_characters_long

# =============================================================================
# External Services (Development)
# =============================================================================
ExternalServices__Stripe__PublishableKey=pk_test_51Qfy9dP82YH9JfOlFmwTk0RHvE7l6sow7ImJBI4c1GzcbhFydoVpb3DMevP8B3C4gj95qCoEise8vEhgFgWxhLEN000RfInKNH
ExternalServices__Stripe__SecretKey=sk_test_51Qfy9dP82YH9JfOlPF9evXANtAjm63textOqXcpIIPpsCqxt9EwZRRyOSV4wk3YURh4hnqZOxnGXFGMf0rJM0yhv00S2q8dr3E
ExternalServices__Email__Resend__ApiKey=re_BM6pyT8x_ChaS1fbRCbdprxPy1fwimWCs

# =============================================================================
# Supabase Configuration (Development)
# =============================================================================
Supabase__Url=https://yfznlsisxsnymkvydzha.supabase.co
Supabase__AnonKey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.qUBshv6Wi-90ZAEnUM2RXuhR77QFHnmEpI6O-y7l3BE
Supabase__StorageBucket=property-images

# =============================================================================
# Mapbox Configuration (Development)
# =============================================================================
Mapbox__ApiKey=pk.eyJ1IjoiamF2aWFucGljYXJkbzMzIiwiYSI6ImNtYjY4ZGRyazBiaWYybHEyMWpnNGN4cDQifQ.m63aGFvbfzrQhT3sWlSbDQ

# =============================================================================
# CORS Configuration (Development)
# =============================================================================
CORS__AllowedOrigins=http://localhost:3000,http://host.docker.internal:3000

# =============================================================================
# Logging Configuration (Development)
# =============================================================================
Logging__LogLevel__Default=Information
Logging__LogLevel__Microsoft.AspNetCore=Warning

# =============================================================================
# SoNoBrokers Configuration (Development)
# =============================================================================
SoNoBrokers__AppUrl=http://localhost:3000
SoNoBrokers__SignInUrl=/sign-in
SoNoBrokers__SignUpUrl=/sign-up
SoNoBrokers__MaxImageUploadSize=10485760
SoNoBrokers__AllowedImageTypes__0=image/jpeg
SoNoBrokers__AllowedImageTypes__1=image/png
SoNoBrokers__AllowedImageTypes__2=image/webp
SoNoBrokers__DefaultPageSize=20
SoNoBrokers__MaxPageSize=100

# =============================================================================
# Frontend Integration (Development)
# =============================================================================
Frontend__NextJs__AppUrl=http://localhost:3000
Frontend__NextJs__SignInUrl=/sign-in
Frontend__NextJs__SignUpUrl=/sign-up
