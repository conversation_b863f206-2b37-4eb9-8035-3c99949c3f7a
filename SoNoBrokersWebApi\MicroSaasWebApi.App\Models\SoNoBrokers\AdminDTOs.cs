using System.ComponentModel.DataAnnotations;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    // Admin Dashboard DTOs
    public class AdminDashboardResponse
    {
        public bool Success { get; set; }
        public AdminDashboardData? Data { get; set; }
        public string? Error { get; set; }
    }

    public class AdminDashboardData
    {
        public UserStatistics Users { get; set; } = new();
        public ActivityStatistics Activity { get; set; } = new();
        public Dictionary<string, int> Subscriptions { get; set; } = new();
    }

    public class UserStatistics
    {
        public int Total { get; set; }
        public int Active { get; set; }
        public int Recent { get; set; }
        public UserRoleStatistics ByRole { get; set; } = new();
        public UserTypeStatistics ByType { get; set; } = new();
    }

    public class UserRoleStatistics
    {
        public int Admin { get; set; }
        public int User { get; set; }
        public int Product { get; set; }
        public int Operator { get; set; }
    }

    public class UserTypeStatistics
    {
        public int Buyers { get; set; }
        public int Sellers { get; set; }
        public int Operators { get; set; }
    }

    public class ActivityStatistics
    {
        public int RecentLogins { get; set; }
    }

    // Admin User Management DTOs
    public class AdminUserResponse
    {
        public string Id { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Phone { get; set; }
        public UserRole Role { get; set; }
        public SnbUserType UserType { get; set; }
        public bool IsActive { get; set; }
        public bool LoggedIn { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class UpdateUserRoleRequest
    {
        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        public UserRole Role { get; set; }
    }

    public class UpdateUserStatusRequest
    {
        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        public bool IsActive { get; set; }
    }

    // Admin Roles DTOs
    public class RolePermissionResponse
    {
        public string Id { get; set; } = string.Empty;
        public UserRole Role { get; set; }
        public string Permission { get; set; } = string.Empty;
        public string? Resource { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class CreateRolePermissionRequest
    {
        [Required]
        public UserRole Role { get; set; }

        [Required]
        public string Permission { get; set; } = string.Empty;

        public string? Resource { get; set; }
    }

    public class UpdateRolePermissionRequest
    {
        [Required]
        public string Id { get; set; } = string.Empty;

        public UserRole? Role { get; set; }

        public string? Permission { get; set; }

        public string? Resource { get; set; }
    }

    // Admin Stripe Sync DTOs
    public class StripeSyncRequest
    {
        public bool ForceSync { get; set; } = false;
        public string? CustomerId { get; set; }
    }

    public class StripeSyncResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int SyncedCustomers { get; set; }
        public int SyncedSubscriptions { get; set; }
        public int SyncedPayments { get; set; }
        public string[] Errors { get; set; } = Array.Empty<string>();
    }
}
