using System.Linq.Expressions;

namespace MicroSaasWebApi.Services.Repository.Interface
{
    /// <summary>
    /// Generic repository interface for common CRUD operations
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    public interface IBaseRepository<T> where T : class
    {
        // Entity Framework CRUD Operations
        Task<T?> GetByIdAsync(Guid id);
        Task<T?> GetByIdAsync(int id);
        Task<IEnumerable<T>> GetAllAsync();
        Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);
        Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate);
        Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate);
        Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null);

        // Pagination
        Task<(IEnumerable<T> Items, int TotalCount)> GetPagedAsync(
            int pageNumber,
            int pageSize,
            Expression<Func<T, bool>>? filter = null,
            Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null);

        // CRUD Operations
        Task<T> AddAsync(T entity);
        Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities);
        Task<T> UpdateAsync(T entity);
        Task<IEnumerable<T>> UpdateRangeAsync(IEnumerable<T> entities);
        Task DeleteAsync(Guid id);
        Task DeleteAsync(int id);
        Task DeleteAsync(T entity);
        Task DeleteRangeAsync(IEnumerable<T> entities);

        // Transaction Support
        Task<int> SaveChangesAsync();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
    }

    /// <summary>
    /// Repository interface with Dapper support for stored procedures
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    public interface IDapperRepository<T> where T : class
    {
        // Stored Procedure Execution
        Task<IEnumerable<TResult>> ExecuteStoredProcedureAsync<TResult>(string storedProcedure, object? parameters = null);
        Task<TResult?> ExecuteStoredProcedureSingleAsync<TResult>(string storedProcedure, object? parameters = null);
        Task<int> ExecuteStoredProcedureNonQueryAsync(string storedProcedure, object? parameters = null);

        // Raw SQL Execution
        Task<IEnumerable<TResult>> QueryAsync<TResult>(string sql, object? parameters = null);
        Task<TResult?> QuerySingleOrDefaultAsync<TResult>(string sql, object? parameters = null);
        Task<int> ExecuteAsync(string sql, object? parameters = null);

        // Bulk Operations
        Task<int> BulkInsertAsync(IEnumerable<T> entities);
        Task<int> BulkUpdateAsync(IEnumerable<T> entities);
        Task<int> BulkDeleteAsync(IEnumerable<T> entities);
    }

    /// <summary>
    /// Unit of Work pattern interface
    /// </summary>
    public interface IUnitOfWork : IDisposable
    {
        // Repository Access
        IUserRepository Users { get; }
        ISubscriptionRepository Subscriptions { get; }
        IRoleRepository Roles { get; }
        IPermissionRepository Permissions { get; }
        IAuditLogRepository AuditLogs { get; }

        // Transaction Management
        Task<int> SaveChangesAsync();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
    }
}
