using Dapper;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;

namespace MicroSaasWebApi.Services.SoNoBrokers
{
    public class PropertyService : IPropertyService
    {
        private readonly MicroSaasWebApi.App.Context.IDapperDbContext _dbContext;
        private readonly ILogger<PropertyService> _logger;

        public PropertyService(MicroSaasWebApi.App.Context.IDapperDbContext dbContext, ILogger<PropertyService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<IEnumerable<Property>> GetAllPropertiesAsync()
        {
            try
            {
                var sql = @"
                    SELECT p.*, u.full_name as seller_name, u.avatar_url as seller_avatar, u.role as seller_role
                    FROM snb.properties p
                    LEFT JOIN auth.users u ON p.seller_id = u.id
                    ORDER BY p.created_at DESC";

                var properties = await _dbContext.QueryAsync<Property>(sql);

                // Load images for each property
                foreach (var property in properties)
                {
                    property.Images = await GetPropertyImagesAsync(property.Id);
                }

                return properties;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all properties");
                throw;
            }
        }

        public async Task<Property?> GetPropertyByIdAsync(string id)
        {
            try
            {
                var sql = @"
                    SELECT p.*, u.full_name as seller_name, u.avatar_url as seller_avatar, u.role as seller_role
                    FROM snb.properties p
                    LEFT JOIN auth.users u ON p.seller_id = u.id
                    WHERE p.id = @id";

                var property = await _dbContext.QueryFirstOrDefaultAsync<Property>(sql, new { id });

                if (property != null)
                {
                    // Load related data
                    property.Images = await GetPropertyImagesAsync(property.Id);
                    // Note: Add other related data loading as needed (viewings, conversations, etc.)
                }

                return property;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving property {PropertyId}", id);
                throw;
            }
        }

        public async Task<Property> CreatePropertyAsync(Property property)
        {
            try
            {
                // Verify seller exists
                var sellerExists = await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM auth.users WHERE id = @sellerId)",
                    new { sellerId = property.SellerId });

                if (!sellerExists)
                {
                    throw new InvalidOperationException("Seller not found");
                }

                property.Id = Guid.NewGuid().ToString();
                property.CreatedAt = DateTime.UtcNow;
                property.UpdatedAt = DateTime.UtcNow;

                var sql = @"
                    INSERT INTO snb.properties (
                        id, title, description, price, bedrooms, bathrooms, sqft,
                        property_type, address, coordinates, features, status,
                        seller_id, created_at, updated_at, expires_at, amenities,
                        year_built, lot_size, city, province, mls_number
                    ) VALUES (
                        @Id, @Title, @Description, @Price, @Bedrooms, @Bathrooms, @Sqft,
                        @PropertyType, @Address, @Coordinates, @Features, @Status,
                        @SellerId, @CreatedAt, @UpdatedAt, @ExpiresAt, @Amenities,
                        @YearBuilt, @LotSize, @City, @Province, @MlsNumber
                    )";

                await _dbContext.ExecuteAsync(sql, property);

                // Load the created property with related data
                var createdProperty = await GetPropertyByIdAsync(property.Id);
                return createdProperty!;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating property");
                throw;
            }
        }

        public async Task<Property> UpdatePropertyAsync(Property property)
        {
            try
            {
                // Check if property exists
                var exists = await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM snb.properties WHERE id = @id)",
                    new { id = property.Id });

                if (!exists)
                {
                    throw new InvalidOperationException("Property not found");
                }

                property.UpdatedAt = DateTime.UtcNow;

                var sql = @"
                    UPDATE snb.properties SET
                        title = @Title,
                        description = @Description,
                        price = @Price,
                        bedrooms = @Bedrooms,
                        bathrooms = @Bathrooms,
                        sqft = @Sqft,
                        property_type = @PropertyType,
                        address = @Address,
                        coordinates = @Coordinates,
                        features = @Features,
                        status = @Status,
                        updated_at = @UpdatedAt,
                        expires_at = @ExpiresAt,
                        amenities = @Amenities,
                        year_built = @YearBuilt,
                        lot_size = @LotSize,
                        city = @City,
                        province = @Province
                    WHERE id = @Id";

                await _dbContext.ExecuteAsync(sql, property);

                // Load updated property with related data
                var updatedProperty = await GetPropertyByIdAsync(property.Id);
                return updatedProperty!;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating property {PropertyId}", property.Id);
                throw;
            }
        }

        public async Task<bool> DeletePropertyAsync(string id)
        {
            try
            {
                var exists = await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM snb.properties WHERE id = @id)",
                    new { id });

                if (!exists)
                {
                    return false;
                }

                await _dbContext.ExecuteAsync("DELETE FROM snb.properties WHERE id = @id", new { id });
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting property {PropertyId}", id);
                throw;
            }
        }

        public async Task<bool> PropertyExistsAsync(string id)
        {
            try
            {
                return await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM snb.properties WHERE id = @id)",
                    new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if property exists {PropertyId}", id);
                throw;
            }
        }

        public async Task<IEnumerable<Property>> GetPropertiesBySellerAsync(string sellerId)
        {
            try
            {
                var sql = @"
                    SELECT p.*, u.full_name as seller_name, u.avatar_url as seller_avatar, u.role as seller_role
                    FROM snb.properties p
                    LEFT JOIN auth.users u ON p.seller_id = u.id
                    WHERE p.seller_id = @sellerId
                    ORDER BY p.created_at DESC";

                var properties = await _dbContext.QueryAsync<Property>(sql, new { sellerId });

                // Load images for each property
                foreach (var property in properties)
                {
                    property.Images = await GetPropertyImagesAsync(property.Id);
                }

                return properties;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving properties for seller {SellerId}", sellerId);
                throw;
            }
        }

        public async Task<IEnumerable<Property>> GetPropertiesByStatusAsync(PropertyStatus status)
        {
            try
            {
                var sql = @"
                    SELECT p.*, u.full_name as seller_name, u.avatar_url as seller_avatar, u.role as seller_role
                    FROM snb.properties p
                    LEFT JOIN auth.users u ON p.seller_id = u.id
                    WHERE p.status = @status
                    ORDER BY p.created_at DESC";

                var properties = await _dbContext.QueryAsync<Property>(sql, new { status });

                // Load images for each property
                foreach (var property in properties)
                {
                    property.Images = await GetPropertyImagesAsync(property.Id);
                }

                return properties;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving properties by status {Status}", status);
                throw;
            }
        }

        public async Task<IEnumerable<Property>> SearchPropertiesAsync(
            string? searchTerm = null,
            string? city = null,
            string? province = null,
            decimal? minPrice = null,
            decimal? maxPrice = null,
            int? bedrooms = null,
            int? bathrooms = null,
            PropertyStatus? status = null)
        {
            try
            {
                var sql = @"
                    SELECT p.*, u.full_name as seller_name, u.avatar_url as seller_avatar, u.role as seller_role
                    FROM snb.properties p
                    LEFT JOIN auth.users u ON p.seller_id = u.id
                    WHERE 1=1";

                var parameters = new DynamicParameters();

                // Apply status filter (default to active if not specified)
                if (status.HasValue)
                {
                    sql += " AND p.status = @status";
                    parameters.Add("status", status.Value);
                }
                else
                {
                    sql += " AND p.status = @status";
                    parameters.Add("status", PropertyStatus.active);
                }

                // Apply search filters
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    sql += " AND (p.title ILIKE @searchTerm OR p.description ILIKE @searchTerm OR p.property_type ILIKE @searchTerm)";
                    parameters.Add("searchTerm", $"%{searchTerm}%");
                }

                if (!string.IsNullOrEmpty(city))
                {
                    sql += " AND LOWER(p.city) = LOWER(@city)";
                    parameters.Add("city", city);
                }

                if (!string.IsNullOrEmpty(province))
                {
                    sql += " AND LOWER(p.province) = LOWER(@province)";
                    parameters.Add("province", province);
                }

                if (minPrice.HasValue)
                {
                    sql += " AND p.price >= @minPrice";
                    parameters.Add("minPrice", minPrice.Value);
                }

                if (maxPrice.HasValue)
                {
                    sql += " AND p.price <= @maxPrice";
                    parameters.Add("maxPrice", maxPrice.Value);
                }

                if (bedrooms.HasValue)
                {
                    sql += " AND p.bedrooms >= @bedrooms";
                    parameters.Add("bedrooms", bedrooms.Value);
                }

                if (bathrooms.HasValue)
                {
                    sql += " AND p.bathrooms >= @bathrooms";
                    parameters.Add("bathrooms", bathrooms.Value);
                }

                sql += " ORDER BY p.created_at DESC";

                var properties = await _dbContext.QueryAsync<Property>(sql, parameters);

                // Load images for each property
                foreach (var property in properties)
                {
                    property.Images = await GetPropertyImagesAsync(property.Id);
                }

                return properties;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching properties");
                throw;
            }
        }

        public async Task<(IEnumerable<Property> Properties, int TotalCount)> GetPropertiesPagedAsync(
            int page,
            int pageSize,
            PropertyStatus? status = null,
            string? propertyType = null,
            decimal? minPrice = null,
            decimal? maxPrice = null)
        {
            try
            {
                var whereClause = "WHERE 1=1";
                var parameters = new DynamicParameters();

                // Apply filters
                if (status.HasValue)
                {
                    whereClause += " AND p.status = @status";
                    parameters.Add("status", status.Value);
                }

                if (!string.IsNullOrEmpty(propertyType))
                {
                    whereClause += " AND LOWER(p.property_type) = LOWER(@propertyType)";
                    parameters.Add("propertyType", propertyType);
                }

                if (minPrice.HasValue)
                {
                    whereClause += " AND p.price >= @minPrice";
                    parameters.Add("minPrice", minPrice.Value);
                }

                if (maxPrice.HasValue)
                {
                    whereClause += " AND p.price <= @maxPrice";
                    parameters.Add("maxPrice", maxPrice.Value);
                }

                // Get total count
                var countSql = $@"
                    SELECT COUNT(*)
                    FROM snb.properties p
                    {whereClause}";

                var totalCount = await _dbContext.QuerySingleAsync<int>(countSql, parameters);

                // Get paginated results
                var offset = (page - 1) * pageSize;
                parameters.Add("offset", offset);
                parameters.Add("pageSize", pageSize);

                var propertiesSql = $@"
                    SELECT p.*, u.full_name as seller_name, u.avatar_url as seller_avatar, u.role as seller_role
                    FROM snb.properties p
                    LEFT JOIN auth.users u ON p.seller_id = u.id
                    {whereClause}
                    ORDER BY p.created_at DESC
                    LIMIT @pageSize OFFSET @offset";

                var properties = await _dbContext.QueryAsync<Property>(propertiesSql, parameters);

                // Load images for each property
                foreach (var property in properties)
                {
                    property.Images = await GetPropertyImagesAsync(property.Id);
                }

                return (properties, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving paged properties");
                throw;
            }
        }

        /// <summary>
        /// Helper method to load property images
        /// </summary>
        private async Task<List<PropertyImage>> GetPropertyImagesAsync(string propertyId)
        {
            try
            {
                var sql = "SELECT * FROM snb.property_images WHERE property_id = @propertyId ORDER BY display_order";
                var images = await _dbContext.QueryAsync<PropertyImage>(sql, new { propertyId });
                return images.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting images for property: {PropertyId}", propertyId);
                return new List<PropertyImage>();
            }
        }
    }
}
