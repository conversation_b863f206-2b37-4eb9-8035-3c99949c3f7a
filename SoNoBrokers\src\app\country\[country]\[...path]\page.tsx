import { redirect } from 'next/navigation'
import { Country } from '@prisma/client'
import { getSupportedCountries } from '@/lib/geo'

interface CountryPathPageProps {
  params: Promise<{
    country: string
    path: string[]
  }>
}

export default async function CountryPathPage({ params }: CountryPathPageProps) {
  const resolvedParams = await params
  const country = resolvedParams.country.toUpperCase()
  const path = resolvedParams.path.join('/')

  // Validate country against database enum
  try {
    const supportedCountries = await getSupportedCountries()
    if (!supportedCountries.includes(country)) {
      redirect(`/unsupported-region?country=${country}`)
    }
  } catch (error) {
    console.error('Failed to validate country:', error)
    redirect(`/unsupported-region?country=${country}`)
  }

  // Redirect to the path without country prefix
  // This allows the app to work normally while maintaining country context
  redirect(`/${path}`)
}
