# SoNoBrokers React to .NET API Feature Mapping

## Overview

This document maps React frontend features to their corresponding .NET Web API endpoints, showing the complete integration between the SoNoBrokers frontend and backend systems.

## Architecture Flow

```
React Component → Server Action → API Service → .NET Controller → Service Layer → Database
```

## Feature Mapping

### 🏠 Property Management

#### Property Search & Listing
| React Feature | React Component | API Service | .NET Endpoint | Controller | Service |
|---------------|-----------------|-------------|---------------|------------|---------|
| Property Search | `PropertySearchPage` | `propertiesApi.searchProperties()` | `GET /api/sonobrokers/properties` | `PropertiesController.GetProperties()` | `PropertyService.SearchPropertiesAsync()` |
| Property Details | `PropertyDetailsPage` | `propertiesApi.getProperty()` | `GET /api/sonobrokers/properties/{id}` | `PropertiesController.GetProperty()` | `PropertyService.GetPropertyByIdAsync()` |
| Featured Properties | `FeaturedPropertiesSection` | `propertiesApi.getFeaturedProperties()` | `GET /api/sonobrokers/properties/featured` | `PropertiesController.GetFeaturedProperties()` | `PropertyService.GetFeaturedPropertiesAsync()` |
| Property Filters | `PropertyFiltersComponent` | `propertiesApi.searchProperties()` | `GET /api/sonobrokers/properties` | `PropertiesController.GetProperties()` | `PropertyService.SearchPropertiesAsync()` |

#### Property Management (Seller)
| React Feature | React Component | API Service | .NET Endpoint | Controller | Service |
|---------------|-----------------|-------------|---------------|------------|---------|
| Create Property | `CreatePropertyForm` | `propertiesApi.createProperty()` | `POST /api/sonobrokers/properties` | `PropertiesController.CreateProperty()` | `PropertyService.CreatePropertyAsync()` |
| Edit Property | `EditPropertyForm` | `propertiesApi.updateProperty()` | `PUT /api/sonobrokers/properties/{id}` | `PropertiesController.UpdateProperty()` | `PropertyService.UpdatePropertyAsync()` |
| Delete Property | `PropertyActionsMenu` | `propertiesApi.deleteProperty()` | `DELETE /api/sonobrokers/properties/{id}` | `PropertiesController.DeleteProperty()` | `PropertyService.DeletePropertyAsync()` |
| My Properties | `MyPropertiesPage` | `propertiesApi.getUserProperties()` | `GET /api/sonobrokers/properties/user/{userId}` | `PropertiesController.GetUserProperties()` | `PropertyService.GetUserPropertiesAsync()` |
| Property Status | `PropertyStatusToggle` | `propertiesApi.updatePropertyStatus()` | `PUT /api/sonobrokers/properties/{id}/status` | `PropertiesController.UpdatePropertyStatus()` | `PropertyService.UpdatePropertyStatusAsync()` |

### 📸 Property Images

| React Feature | React Component | API Service | .NET Endpoint | Controller | Service |
|---------------|-----------------|-------------|---------------|------------|---------|
| Image Gallery | `PropertyImageGallery` | `propertyImagesApi.getPropertyImages()` | `GET /api/sonobrokers/property-images/{propertyId}` | `PropertyImagesController.GetPropertyImages()` | `PropertyImageService.GetPropertyImagesAsync()` |
| Image Upload | `ImageUploadComponent` | `propertyImagesApi.uploadImage()` | `POST /api/sonobrokers/property-images` | `PropertyImagesController.UploadImage()` | `PropertyImageService.UploadImageAsync()` |
| Set Primary Image | `ImageActionsMenu` | `propertyImagesApi.setPrimaryImage()` | `PUT /api/sonobrokers/property-images/{propertyId}/primary/{imageId}` | `PropertyImagesController.SetPrimaryImage()` | `PropertyImageService.SetPrimaryImageAsync()` |
| Delete Image | `ImageActionsMenu` | `propertyImagesApi.deleteImage()` | `DELETE /api/sonobrokers/property-images/{id}` | `PropertyImagesController.DeleteImage()` | `PropertyImageService.DeleteImageAsync()` |
| Image Caption | `ImageCaptionEditor` | `propertyImagesApi.updateImage()` | `PUT /api/sonobrokers/property-images/{id}` | `PropertyImagesController.UpdateImage()` | `PropertyImageService.UpdateImageAsync()` |

### 👤 User Management

#### User Profile
| React Feature | React Component | API Service | .NET Endpoint | Controller | Service |
|---------------|-----------------|-------------|---------------|------------|---------|
| User Profile | `UserProfilePage` | `usersApi.getProfile()` | `GET /api/sonobrokers/users/profile` | `UsersController.GetProfile()` | `UserService.GetUserProfileAsync()` |
| Edit Profile | `EditProfileForm` | `usersApi.updateProfile()` | `PUT /api/sonobrokers/users/profile` | `UsersController.UpdateProfile()` | `UserService.UpdateUserProfileAsync()` |
| User Details | `UserDetailsPage` | `usersApi.getUser()` | `GET /api/sonobrokers/users/{id}` | `UsersController.GetUser()` | `UserService.GetUserByIdAsync()` |
| User Settings | `UserSettingsPage` | `usersApi.updateSettings()` | `PUT /api/sonobrokers/users/settings` | `UsersController.UpdateSettings()` | `UserService.UpdateUserSettingsAsync()` |

#### Saved Properties
| React Feature | React Component | API Service | .NET Endpoint | Controller | Service |
|---------------|-----------------|-------------|---------------|------------|---------|
| Saved Properties | `SavedPropertiesPage` | `usersApi.getSavedProperties()` | `GET /api/sonobrokers/users/saved-properties` | `UsersController.GetSavedProperties()` | `UserService.GetSavedPropertiesAsync()` |
| Save Property | `SavePropertyButton` | `usersApi.saveProperty()` | `POST /api/sonobrokers/users/saved-properties/{propertyId}` | `UsersController.SaveProperty()` | `UserService.SavePropertyAsync()` |
| Remove Saved | `RemoveSavedButton` | `usersApi.removeSavedProperty()` | `DELETE /api/sonobrokers/users/saved-properties/{propertyId}` | `UsersController.RemoveSavedProperty()` | `UserService.RemoveSavedPropertyAsync()` |

### 🔐 Authentication

| React Feature | React Component | API Service | .NET Endpoint | Controller | Service |
|---------------|-----------------|-------------|---------------|------------|---------|
| User Login | `SignInPage` | `authApi.login()` | `POST /api/sonobrokers/auth/login` | `AuthController.Login()` | `AuthService.LoginAsync()` |
| User Registration | `SignUpPage` | `authApi.register()` | `POST /api/sonobrokers/auth/register` | `AuthController.Register()` | `AuthService.RegisterAsync()` |
| Logout | `UserMenu` | `authApi.logout()` | `POST /api/sonobrokers/auth/logout` | `AuthController.Logout()` | `AuthService.LogoutAsync()` |
| Token Refresh | `AuthProvider` | `authApi.refreshToken()` | `POST /api/sonobrokers/auth/refresh` | `AuthController.RefreshToken()` | `AuthService.RefreshTokenAsync()` |

### 📅 Property Scheduling

#### Visit Scheduling
| React Feature | React Component | API Service | .NET Endpoint | Controller | Service |
|---------------|-----------------|-------------|---------------|------------|---------|
| Schedule Visit | `ScheduleVisitForm` | `schedulingApi.scheduleVisit()` | `POST /api/sonobrokers/scheduling/visits` | `SchedulingController.ScheduleVisit()` | `PropertySchedulingService.ScheduleVisitAsync()` |
| My Visits | `MyVisitsPage` | `schedulingApi.getUserVisits()` | `GET /api/sonobrokers/scheduling/visits` | `SchedulingController.GetUserVisits()` | `PropertySchedulingService.GetUserVisitsAsync()` |
| Visit Details | `VisitDetailsPage` | `schedulingApi.getVisit()` | `GET /api/sonobrokers/scheduling/visits/{id}` | `SchedulingController.GetVisit()` | `PropertySchedulingService.GetVisitByIdAsync()` |
| Update Visit | `UpdateVisitForm` | `schedulingApi.updateVisit()` | `PUT /api/sonobrokers/scheduling/visits/{id}` | `SchedulingController.UpdateVisit()` | `PropertySchedulingService.UpdateVisitAsync()` |
| Cancel Visit | `CancelVisitButton` | `schedulingApi.cancelVisit()` | `DELETE /api/sonobrokers/scheduling/visits/{id}` | `SchedulingController.CancelVisit()` | `PropertySchedulingService.CancelVisitAsync()` |

#### Seller Availability
| React Feature | React Component | API Service | .NET Endpoint | Controller | Service |
|---------------|-----------------|-------------|---------------|------------|---------|
| Set Availability | `AvailabilityForm` | `schedulingApi.setAvailability()` | `POST /api/sonobrokers/scheduling/availability` | `SchedulingController.SetAvailability()` | `PropertySchedulingService.SetSellerAvailabilityAsync()` |
| View Availability | `AvailabilityCalendar` | `schedulingApi.getAvailability()` | `GET /api/sonobrokers/scheduling/availability` | `SchedulingController.GetAvailability()` | `PropertySchedulingService.GetSellerAvailabilityAsync()` |

#### QR Code System
| React Feature | React Component | API Service | .NET Endpoint | Controller | Service |
|---------------|-----------------|-------------|---------------|------------|---------|
| Generate QR Code | `QRCodeGenerator` | `qrCodeApi.generateQRCode()` | `POST /api/sonobrokers/qr-codes` | `QRCodeController.GenerateQRCode()` | `QrCodeService.GenerateQrCodeAsync()` |
| Scan QR Code | `QRCodeScanner` | `qrCodeApi.validateQRCode()` | `POST /api/sonobrokers/qr-codes/validate` | `QRCodeController.ValidateQRCode()` | `QrCodeService.ValidateQrCodeAsync()` |
| QR Code Details | `QRCodeDetailsPage` | `qrCodeApi.getQRCode()` | `GET /api/sonobrokers/qr-codes/{id}` | `QRCodeController.GetQRCode()` | `QrCodeService.GetQrCodeAsync()` |

### 💬 Communication

#### Messaging System
| React Feature | React Component | API Service | .NET Endpoint | Controller | Service |
|---------------|-----------------|-------------|---------------|------------|---------|
| Send Message | `MessageForm` | `communicationApi.sendMessage()` | `POST /api/sonobrokers/communication/messages` | `CommunicationController.SendMessage()` | `CommunicationService.SendMessageAsync()` |
| Conversations | `ConversationsPage` | `communicationApi.getConversations()` | `GET /api/sonobrokers/communication/conversations` | `CommunicationController.GetConversations()` | `CommunicationService.GetConversationsAsync()` |
| Message Thread | `MessageThread` | `communicationApi.getMessages()` | `GET /api/sonobrokers/communication/conversations/{id}/messages` | `CommunicationController.GetMessages()` | `CommunicationService.GetMessagesAsync()` |
| Mark as Read | `MessageItem` | `communicationApi.markAsRead()` | `PUT /api/sonobrokers/communication/messages/{id}/read` | `CommunicationController.MarkAsRead()` | `CommunicationService.MarkMessageAsReadAsync()` |

#### Contact Sharing
| React Feature | React Component | API Service | .NET Endpoint | Controller | Service |
|---------------|-----------------|-------------|---------------|------------|---------|
| Share Contact | `ShareContactForm` | `contactSharingApi.shareContact()` | `POST /api/sonobrokers/contact-sharing` | `ContactSharingController.ShareContact()` | `ContactSharingService.ShareContactAsync()` |
| Contact Requests | `ContactRequestsPage` | `contactSharingApi.getContactRequests()` | `GET /api/sonobrokers/contact-sharing/requests` | `ContactSharingController.GetContactRequests()` | `ContactSharingService.GetContactRequestsAsync()` |
| Approve Contact | `ApproveContactButton` | `contactSharingApi.approveContact()` | `PUT /api/sonobrokers/contact-sharing/{id}/approve` | `ContactSharingController.ApproveContact()` | `ContactSharingService.ApproveContactRequestAsync()` |

### 📊 Dashboard & Analytics

#### User Dashboard
| React Feature | React Component | API Service | .NET Endpoint | Controller | Service |
|---------------|-----------------|-------------|---------------|------------|---------|
| Dashboard Overview | `DashboardPage` | `dashboardApi.getDashboardData()` | `GET /api/sonobrokers/dashboard` | `DashboardController.GetDashboardData()` | `DashboardService.GetUserDashboardDataAsync()` |
| Property Stats | `PropertyStatsWidget` | `analyticsApi.getPropertyStats()` | `GET /api/sonobrokers/analytics/properties` | `AnalyticsController.GetPropertyStats()` | `AnalyticsService.GetPropertyStatsAsync()` |
| Visit Stats | `VisitStatsWidget` | `analyticsApi.getVisitStats()` | `GET /api/sonobrokers/analytics/visits` | `AnalyticsController.GetVisitStats()` | `AnalyticsService.GetVisitStatsAsync()` |

#### Admin Dashboard
| React Feature | React Component | API Service | .NET Endpoint | Controller | Service |
|---------------|-----------------|-------------|---------------|------------|---------|
| Admin Overview | `AdminDashboardPage` | `adminApi.getAdminStats()` | `GET /api/sonobrokers/admin/stats` | `AdminController.GetAdminStats()` | `AdminService.GetAdminStatsAsync()` |
| User Management | `UserManagementPage` | `adminApi.getUsers()` | `GET /api/sonobrokers/admin/users` | `AdminController.GetUsers()` | `AdminService.GetUsersAsync()` |
| Property Moderation | `PropertyModerationPage` | `adminApi.getPendingProperties()` | `GET /api/sonobrokers/admin/properties/pending` | `AdminController.GetPendingProperties()` | `AdminService.GetPendingPropertiesAsync()` |

### 🔍 Search & Filtering

| React Feature | React Component | API Service | .NET Endpoint | Controller | Service |
|---------------|-----------------|-------------|---------------|------------|---------|
| Advanced Search | `AdvancedSearchForm` | `searchApi.advancedSearch()` | `POST /api/sonobrokers/search/advanced` | `SearchController.AdvancedSearch()` | `SearchService.AdvancedSearchAsync()` |
| Saved Searches | `SavedSearchesPage` | `searchApi.getSavedSearches()` | `GET /api/sonobrokers/search/saved` | `SearchController.GetSavedSearches()` | `SearchService.GetSavedSearchesAsync()` |
| Search Alerts | `SearchAlertsPage` | `searchApi.getSearchAlerts()` | `GET /api/sonobrokers/search/alerts` | `SearchController.GetSearchAlerts()` | `SearchService.GetSearchAlertsAsync()` |

### 🌍 Multi-Country Support

| React Feature | React Component | API Service | .NET Endpoint | Controller | Service |
|---------------|-----------------|-------------|---------------|------------|---------|
| Country Selection | `CountrySelector` | `locationApi.getCountries()` | `GET /api/sonobrokers/locations/countries` | `LocationController.GetCountries()` | `LocationService.GetCountriesAsync()` |
| State/Province List | `StateSelector` | `locationApi.getStates()` | `GET /api/sonobrokers/locations/states/{country}` | `LocationController.GetStates()` | `LocationService.GetStatesAsync()` |
| City List | `CitySelector` | `locationApi.getCities()` | `GET /api/sonobrokers/locations/cities/{state}` | `LocationController.GetCities()` | `LocationService.GetCitiesAsync()` |

## Server Actions Implementation

### Property Actions
```typescript
// src/lib/actions/property-actions.ts
'use server';

export async function getPropertiesAction(params: PropertySearchParams) {
  // Calls: GET /api/sonobrokers/properties
  const result = await propertiesApi.searchProperties(params);
  return { success: true, data: result };
}

export async function createPropertyAction(data: CreatePropertyRequest) {
  // Calls: POST /api/sonobrokers/properties
  const result = await propertiesApi.createProperty(data);
  revalidatePath('/dashboard/properties');
  return { success: true, data: result };
}
```

### User Actions
```typescript
// src/lib/actions/user-actions.ts
'use server';

export async function getUserProfileAction() {
  // Calls: GET /api/sonobrokers/users/profile
  const result = await usersApi.getProfile();
  return { success: true, data: result };
}

export async function savePropertyAction(propertyId: string) {
  // Calls: POST /api/sonobrokers/users/saved-properties/{propertyId}
  await usersApi.saveProperty(propertyId);
  revalidatePath('/dashboard/saved-properties');
  return { success: true };
}
```

## API Service Layer

### Properties API Service
```typescript
// src/lib/api/properties-api.ts
export const propertiesApi = {
  searchProperties: (params) => apiClient.get('/api/sonobrokers/properties', { params }),
  getProperty: (id) => apiClient.get(`/api/sonobrokers/properties/${id}`),
  createProperty: (data) => apiClient.post('/api/sonobrokers/properties', data),
  updateProperty: (id, data) => apiClient.put(`/api/sonobrokers/properties/${id}`, data),
  deleteProperty: (id) => apiClient.delete(`/api/sonobrokers/properties/${id}`)
};
```

### Users API Service
```typescript
// src/lib/api/users-api.ts
export const usersApi = {
  getProfile: () => apiClient.get('/api/sonobrokers/users/profile'),
  updateProfile: (data) => apiClient.put('/api/sonobrokers/users/profile', data),
  getSavedProperties: () => apiClient.get('/api/sonobrokers/users/saved-properties'),
  saveProperty: (id) => apiClient.post(`/api/sonobrokers/users/saved-properties/${id}`),
  removeSavedProperty: (id) => apiClient.delete(`/api/sonobrokers/users/saved-properties/${id}`)
};
```

## Component Integration Examples

### Property List Component
```typescript
// Uses: GET /api/sonobrokers/properties
export async function PropertyList({ searchParams }: PropertyListProps) {
  const result = await getPropertiesAction(searchParams);
  
  if (!result.success) {
    return <div>Error loading properties</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {result.data.map((property) => (
        <PropertyCard key={property.id} property={property} />
      ))}
    </div>
  );
}
```

### Property Form Component
```typescript
// Uses: POST /api/sonobrokers/properties
'use client';

export function PropertyForm() {
  const handleSubmit = async (formData: FormData) => {
    const result = await createPropertyAction(data);
    
    if (result.success) {
      router.push(`/properties/${result.data.id}`);
    }
  };

  return <form action={handleSubmit}>...</form>;
}
```

## Migration Status

### ✅ Completed Features
- Property search and listing
- Property CRUD operations
- User authentication and profiles
- Property image management
- Basic scheduling functionality
- API client configuration
- Server actions implementation

### 🔄 In Progress Features
- Advanced scheduling features
- Communication system
- Contact sharing
- QR code system
- Analytics and reporting

### 📋 Pending Features
- Real-time messaging
- Advanced search filters
- Payment integration
- Notification system
- Mobile app API support

## Testing Integration

### API Integration Tests
```typescript
// Test React → API integration
describe('Property Integration', () => {
  it('should create property via API', async () => {
    const result = await createPropertyAction(mockPropertyData);
    expect(result.success).toBe(true);
    expect(result.data.id).toBeDefined();
  });
});
```

### End-to-End Tests
```typescript
// Test full user flow
test('User can create and view property', async ({ page }) => {
  await page.goto('/dashboard/properties/new');
  await page.fill('[name="title"]', 'Test Property');
  await page.click('[type="submit"]');
  await expect(page).toHaveURL(/\/properties\/\w+/);
});
```

## Performance Considerations

### Caching Strategy
- Server-side caching for property listings
- Client-side caching with SWR/React Query
- Image optimization and lazy loading
- API response caching

### Optimization Techniques
- Pagination for large datasets
- Image compression and resizing
- Database query optimization
- CDN for static assets

## Security Implementation

### Authentication Flow
1. User authenticates with Clerk
2. Clerk issues JWT token
3. React includes token in API requests
4. .NET API validates token
5. API returns authorized data

### Authorization Levels
- **Public**: Property listings, search
- **Authenticated**: User profile, saved properties
- **Owner**: Property management, scheduling
- **Admin**: User management, moderation

For more details, see the [API Integration Guide](./API-Integration.md) and [SoNoBrokers API Documentation](../../SoNoBrokersWebApi/MicroSaasWebApi.App/Documentation/SoNoBrokers-API.md).
