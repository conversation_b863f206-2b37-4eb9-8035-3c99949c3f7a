# SoNoBrokers .NET Web API Documentation

## Overview

The SoNoBrokers .NET Web API is a comprehensive backend service built with ASP.NET Core 9 that provides secure, scalable endpoints for the SoNoBrokers property marketplace platform.

## Quick Start

### API Base URL
- **Development**: `https://localhost:7163`
- **API Documentation**: `https://localhost:7163/scalar/v1`
- **Health Check**: `https://localhost:7163/health`
- **Health Dashboard**: `https://localhost:7163/healthchecks-ui`

### Test API Connection
```bash
# Test basic connectivity (no auth required)
curl https://localhost:7163/health

# Test authentication endpoint
curl https://localhost:7163/api/sonobrokers/auth/test \
  -H "Authorization: Bearer YOUR_CLERK_JWT_TOKEN"

# Expected Response:
{
  "success": true,
  "message": "Authentication successful",
  "data": {
    "userId": "user_xxx",
    "timestamp": "2024-12-25T14:20:17.3729704Z",
    "environment": "Development"
  }
}
```

## Authentication

### JWT Token Required
All endpoints (except public ones) require a valid Clerk JWT token:
```http
Authorization: Bearer <jwt_token>
```

### Public Endpoints (No Auth Required)
- `/api/sonobrokers/test/ping` - API connectivity test
- `/api/sonobrokers/test/health` - Database health check
- `/api/sonobrokers/auth/login` - User login
- `/api/sonobrokers/auth/register` - User registration
- `/scalar/v1` - API documentation
- `/api/health` - System health checks

## Core API Endpoints

### 🏠 Properties API

#### Search Properties
```http
GET /api/sonobrokers/properties
Query Parameters:
- page: int (default: 1)
- limit: int (default: 20, max: 100)
- country: string (CA, US, UAE)
- propertyType: string (House, Condo, Townhouse, etc.)
- minPrice: decimal
- maxPrice: decimal
- bedrooms: int
- bathrooms: int
- city: string
- state: string
- zipCode: string
- sortBy: string (price, date, size)
- sortOrder: string (asc, desc)
```

#### Get Property Details
```http
GET /api/sonobrokers/properties/{id}
```

#### Create Property
```http
POST /api/sonobrokers/properties
Content-Type: application/json
Authorization: Bearer <token>

{
  "title": "Beautiful Family Home",
  "description": "Spacious 4-bedroom house in quiet neighborhood",
  "price": 750000,
  "propertyType": "House",
  "bedrooms": 4,
  "bathrooms": 3,
  "squareFootage": 2500,
  "lotSize": 0.25,
  "address": {
    "street": "123 Main Street",
    "city": "Toronto",
    "state": "ON",
    "zipCode": "M1A 1A1",
    "country": "CA"
  },
  "features": ["Garage", "Garden", "Fireplace"],
  "isActive": true
}
```

#### Update Property
```http
PUT /api/sonobrokers/properties/{id}
Authorization: Bearer <token>
```

#### Delete Property
```http
DELETE /api/sonobrokers/properties/{id}
Authorization: Bearer <token>
```

### 👤 Users API

#### Get Current User Profile
```http
GET /api/sonobrokers/users/profile
Authorization: Bearer <token>
```

#### Update User Profile
```http
PUT /api/sonobrokers/users/profile
Authorization: Bearer <token>

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "******-123-4567",
  "bio": "Experienced property seller",
  "preferences": {
    "notifications": true,
    "marketing": false
  }
}
```

#### Get User by ID
```http
GET /api/sonobrokers/users/{id}
Authorization: Bearer <token>
```

### 📸 Property Images API

#### Get Property Images
```http
GET /api/sonobrokers/property-images/{propertyId}
```

#### Upload Property Image
```http
POST /api/sonobrokers/property-images
Content-Type: multipart/form-data
Authorization: Bearer <token>

Form Data:
- file: [image file]
- propertyId: "property-uuid"
- caption: "Living room view" (optional)
- isPrimary: "true" (optional)
```

#### Update Image Details
```http
PUT /api/sonobrokers/property-images/{id}
Authorization: Bearer <token>

{
  "caption": "Updated caption",
  "isPrimary": false
}
```

#### Delete Image
```http
DELETE /api/sonobrokers/property-images/{id}
Authorization: Bearer <token>
```

### 📅 Scheduling API

#### Schedule Property Visit
```http
POST /api/sonobrokers/scheduling/visits
Authorization: Bearer <token>

{
  "propertyId": "property-uuid",
  "requestedDate": "2025-07-01T14:00:00Z",
  "timeSlot": "2:00 PM - 3:00 PM",
  "message": "Interested in viewing this property",
  "contactInfo": {
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "phone": "******-987-6543"
  }
}
```

#### Get User's Visits
```http
GET /api/sonobrokers/scheduling/visits
Authorization: Bearer <token>
Query Parameters:
- status: string (pending, confirmed, completed, cancelled)
- fromDate: datetime
- toDate: datetime
```

#### Update Visit Status
```http
PUT /api/sonobrokers/scheduling/visits/{id}
Authorization: Bearer <token>

{
  "status": "confirmed",
  "notes": "Visit confirmed for 2 PM"
}
```

### 💬 Communication API

#### Send Message
```http
POST /api/sonobrokers/communication/messages
Authorization: Bearer <token>

{
  "recipientId": "user-uuid",
  "propertyId": "property-uuid",
  "subject": "Question about property",
  "content": "I'm interested in scheduling a viewing",
  "messageType": "inquiry"
}
```

#### Get Conversations
```http
GET /api/sonobrokers/communication/conversations
Authorization: Bearer <token>
Query Parameters:
- page: int
- limit: int
```

#### Get Messages in Conversation
```http
GET /api/sonobrokers/communication/conversations/{conversationId}/messages
Authorization: Bearer <token>
```

## Response Format

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data object
  },
  "errors": []
}
```

### Error Response
```json
{
  "success": false,
  "message": "Operation failed",
  "data": null,
  "errors": [
    "Specific error message 1",
    "Specific error message 2"
  ]
}
```

## HTTP Status Codes

| Code | Description | When Used |
|------|-------------|-----------|
| 200 | OK | Successful GET, PUT requests |
| 201 | Created | Successful POST requests |
| 204 | No Content | Successful DELETE requests |
| 400 | Bad Request | Invalid request data |
| 401 | Unauthorized | Missing/invalid authentication |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Resource not found |
| 409 | Conflict | Resource conflict (duplicate) |
| 422 | Unprocessable Entity | Validation errors |
| 500 | Internal Server Error | Server error |

## React Integration Examples

### API Client Setup
```typescript
// src/lib/api-client.ts
import axios from 'axios';
import { auth } from '@clerk/nextjs';

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5005',
  timeout: 30000,
});

apiClient.interceptors.request.use(async (config) => {
  const { getToken } = auth();
  const token = await getToken();
  
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  
  return config;
});
```

### Properties Service
```typescript
// src/lib/api/properties-api.ts
export const propertiesApi = {
  searchProperties: async (params: PropertySearchParams) => {
    const response = await apiClient.get('/api/sonobrokers/properties', { params });
    return response.data.data;
  },

  createProperty: async (data: CreatePropertyRequest) => {
    const response = await apiClient.post('/api/sonobrokers/properties', data);
    return response.data.data;
  }
};
```

### Server Action Example
```typescript
// src/lib/actions/property-actions.ts
'use server';

export async function createPropertyAction(data: CreatePropertyRequest) {
  try {
    const property = await propertiesApi.createProperty(data);
    revalidatePath('/dashboard/properties');
    return { success: true, data: property };
  } catch (error) {
    return { success: false, error: 'Failed to create property' };
  }
}
```

## Database Integration

### Technology Stack
- **Database**: PostgreSQL (Supabase)
- **ORM**: Dapper with custom DapperDbContext
- **Migrations**: Custom migration system
- **Connection**: Pooled connections with automatic lifecycle management

### Stored Procedures Used
- `sp_search_properties` - Advanced property search with filters
- `sp_create_property` - Property creation with validation
- `sp_update_property_status` - Property status management
- `sp_get_user_dashboard_data` - Dashboard data aggregation
- `sp_schedule_property_visit` - Visit scheduling with conflict detection

## Health Monitoring

### Health Check Endpoints
```http
GET /api/health              # Overall system health
GET /api/health/ready         # Readiness probe
GET /api/health/live          # Liveness probe
GET /api/sonobrokers/test/health  # Database connectivity
```

### Health Check Response
```json
{
  "status": "Healthy",
  "totalDuration": "00:00:00.0123456",
  "entries": {
    "database": {
      "status": "Healthy",
      "duration": "00:00:00.0098765"
    },
    "memory": {
      "status": "Healthy",
      "data": {
        "allocated": "45.2 MB",
        "gen0": 12,
        "gen1": 8,
        "gen2": 3
      }
    }
  }
}
```

## Configuration

### Required Environment Variables
```env
# Database Connection
DATABASE_URL=postgresql://user:password@host:port/database

# Clerk Authentication
Clerk__SecretKey=sk_test_...
Clerk__PublishableKey=pk_test_...

# SoNoBrokers Settings
SoNoBrokers__AppUrl=http://localhost:3000
SoNoBrokers__MaxImageUploadSize=10485760
SoNoBrokers__DefaultPageSize=20
SoNoBrokers__MaxPageSize=100
```

### Feature Flags
```json
{
  "FeatureFlags": {
    "EnableSwagger": true,
    "EnableDetailedLogging": true,
    "EnablePayments": false,
    "EnableAnalytics": true,
    "EnableRateLimiting": true,
    "EnableHealthChecks": true
  }
}
```

## Development & Testing

### Running the API
```bash
cd SoNoBrokersWebApi/MicroSaasWebApi.App
dotnet restore
dotnet run
```

### Testing Endpoints
```bash
# Test connectivity
curl http://localhost:5005/api/sonobrokers/test/ping

# Test with authentication (replace with actual token)
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:5005/api/sonobrokers/properties
```

### Interactive API Documentation
Visit `http://localhost:5005/scalar/v1` for:
- Complete endpoint documentation
- Interactive testing interface
- Request/response examples
- Authentication setup
- Code generation

## Error Handling & Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Check JWT token validity
   - Ensure token is included in Authorization header
   - Verify Clerk configuration

2. **404 Not Found**
   - Verify endpoint URL
   - Check if resource exists
   - Confirm route parameters

3. **500 Internal Server Error**
   - Check server logs
   - Verify database connectivity
   - Check configuration settings

### Debugging Tips
- Use `/api/sonobrokers/test/ping` to verify API is running
- Use `/api/sonobrokers/test/health` to check database connectivity
- Check logs for detailed error information
- Use Scalar documentation for endpoint testing

## Support & Resources

- **API Documentation**: `http://localhost:5005/scalar/v1`
- **Health Status**: `http://localhost:5005/api/health`
- **Test Endpoint**: `http://localhost:5005/api/sonobrokers/test/ping`
- **Project Documentation**: See `/docs/SoNoBrokers/` folder
- **Support Email**: <EMAIL>
