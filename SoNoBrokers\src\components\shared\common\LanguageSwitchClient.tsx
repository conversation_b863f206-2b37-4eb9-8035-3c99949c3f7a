'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Globe } from 'lucide-react'
import { useAppContext } from '@/contexts/AppContext'

interface Language {
  code: string
  name: string
  flag: string
}

interface LanguageSwitchClientProps {
  currentLanguage: string
  onLanguageChange: (language: string) => void
}

export function LanguageSwitchClient({ 
  currentLanguage, 
  onLanguageChange 
}: LanguageSwitchClientProps) {
  const { country } = useAppContext()

  // Define language options based on country
  const getLanguageOptions = (countryCode: string): Language[] => {
    switch (countryCode) {
      case 'CA':
        return [
          { code: 'en', name: 'English', flag: '🇬🇧' },
          { code: 'fr', name: 'Français', flag: '🇫🇷' }
        ]
      case 'US':
        return [
          { code: 'en', name: 'English', flag: '🇺🇸' },
          { code: 'es', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸' }
        ]
      case 'UAE':
        return [
          { code: 'en', name: 'English', flag: '🇬🇧' },
          { code: 'ar', name: 'العربية', flag: '🇦🇪' }
        ]
      default:
        return [
          { code: 'en', name: 'English', flag: '🇬🇧' }
        ]
    }
  }

  const languageOptions = getLanguageOptions(country)
  const currentLang = languageOptions.find(lang => lang.code === currentLanguage) || languageOptions[0]

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="h-8 px-2 gap-1">
          <Globe className="h-4 w-4" />
          <span className="text-sm">{currentLang.flag}</span>
          <span className="hidden sm:inline text-sm">{currentLang.name}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {languageOptions.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => onLanguageChange(language.code)}
            className={`flex items-center gap-2 cursor-pointer ${
              currentLanguage === language.code ? 'bg-accent' : ''
            }`}
          >
            <span className="text-lg">{language.flag}</span>
            <span className="flex-1">{language.name}</span>
            {currentLanguage === language.code && (
              <span className="text-xs text-muted-foreground">✓</span>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
