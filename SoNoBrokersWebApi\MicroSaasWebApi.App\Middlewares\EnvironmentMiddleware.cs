using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace MicroSaasWebApi.Middlewares
{
    public class EnvironmentMiddleware
    {
        private readonly RequestDelegate _next;

        public EnvironmentMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var environment = context.Request.Headers["X-Environment"];
            if (!string.IsNullOrEmpty(environment))
            {
                context.Items["Environment"] = environment;
            }

            await _next(context);
        }
    }
}
