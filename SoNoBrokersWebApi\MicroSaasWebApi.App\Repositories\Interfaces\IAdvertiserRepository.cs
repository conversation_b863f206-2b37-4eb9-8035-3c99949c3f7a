using MicroSaasWebApi.Models.SoNoBrokers;

namespace MicroSaasWebApi.App.Repositories.Interfaces
{
    /// <summary>
    /// Interface for advertiser repository operations
    /// </summary>
    public interface IAdvertiserRepository
    {
        // Basic CRUD operations
        Task<IEnumerable<Advertiser>> GetAllAsync();
        Task<Advertiser?> GetByIdAsync(string id);
        Task<Advertiser?> GetByUserIdAsync(string userId);
        Task<string> CreateAsync(Advertiser advertiser);
        Task<bool> UpdateAsync(Advertiser advertiser);
        Task<bool> DeleteAsync(string id);

        // Advertiser-specific operations
        Task<IEnumerable<Advertiser>> GetByServiceTypeAsync(ServiceType serviceType);
        Task<IEnumerable<Advertiser>> GetByServiceAreaAsync(string serviceArea);
        Task<IEnumerable<Advertiser>> GetVerifiedAdvertisersAsync();
        Task<IEnumerable<Advertiser>> GetPremiumAdvertisersAsync();
        Task<bool> UpdateVerificationStatusAsync(string id, bool isVerified);
        Task<bool> UpdatePremiumStatusAsync(string id, bool isPremium);

        // Search and filtering
        Task<(IEnumerable<Advertiser> Advertisers, int TotalCount)> SearchAsync(
            string? searchTerm = null,
            ServiceType? serviceType = null,
            string? serviceArea = null,
            AdvertiserStatus? status = null,
            bool? isVerified = null,
            bool? isPremium = null,
            int page = 1,
            int pageSize = 20);

        // Statistics
        Task<int> GetCountByStatusAsync(AdvertiserStatus status);
        Task<int> GetVerifiedCountAsync();
        Task<int> GetPremiumCountAsync();
    }
}
