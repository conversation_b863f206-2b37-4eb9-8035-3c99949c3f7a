import { auth } from '@clerk/nextjs/server'
import { apiClient } from '@/lib/api-client'

/**
 * Properties API Service
 * Handles property listings, search, and management
 */

// Types
export interface Property {
  id: string
  title: string
  description: string
  price: number
  propertyType: string
  bedrooms: number
  bathrooms: number
  sqft?: number
  address?: string
  city?: string
  province?: string
  country?: string
  postalCode?: string
  latitude?: number
  longitude?: number
  sellerId: string
  status: 'active' | 'inactive' | 'sold' | 'pending'
  images?: string[]
  features?: string[]
  createdAt: string
  updatedAt: string
}

export interface CreatePropertyRequest {
  title: string
  description: string
  price: number
  propertyType: string
  bedrooms: number
  bathrooms: number
  sqft?: number
  address?: string
  city?: string
  province?: string
  country?: string
  postalCode?: string
  latitude?: number
  longitude?: number
  features?: string[]
}

export interface UpdatePropertyRequest extends Partial<CreatePropertyRequest> {
  status?: 'active' | 'inactive' | 'sold' | 'pending'
}

export interface PropertySearchParams {
  page?: number
  limit?: number
  minPrice?: number
  maxPrice?: number
  bedrooms?: number
  bathrooms?: number
  propertyType?: string
  city?: string
  province?: string
  country?: string
  status?: string
  sellerId?: string
  search?: string
}

export interface PropertySearchResponse {
  properties: Property[]
  total: number
  page: number
  totalPages: number
  hasMore: boolean
}

export interface PropertyImage {
  id: string
  propertyId: string
  url: string
  filename: string
  size: number
  mimeType: string
  isPrimary: boolean
  createdAt: string
}

/**
 * Server Action: Get all properties with search and filters
 */
export async function getProperties(params: PropertySearchParams = {}): Promise<PropertySearchResponse> {
  try {
    const searchParams = new URLSearchParams()

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value))
      }
    })

    const response = await apiClient.get<ApiResponse<Property[]>>(`/api/sonobrokers/properties?${searchParams}`)

    // Handle .NET API response format
    return {
      properties: response.data || [],
      total: response.data?.length || 0,
      page: params.page || 1,
      limit: params.limit || 20,
      hasMore: false // Will be updated when pagination is implemented in .NET API
    }
  } catch (error) {
    console.error('Failed to get properties:', error)
    throw error
  }
}

/**
 * Server Action: Get property by ID
 */
export async function getPropertyById(id: string): Promise<Property | null> {
  try {
    const response = await apiClient.get<ApiResponse<Property>>(`/api/sonobrokers/properties/${id}`)
    return response.data || null
  } catch (error) {
    console.error('Failed to get property:', error)
    return null
  }
}

/**
 * Server Action: Create new property
 */
export async function createProperty(data: CreatePropertyRequest): Promise<Property> {
  try {
    const { getToken } = auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const response = await apiClient.post<ApiResponse<Property>>('/api/sonobrokers/properties', data)
    return response.data!
  } catch (error) {
    console.error('Failed to create property:', error)
    throw error
  }
}

/**
 * Server Action: Update property
 */
export async function updateProperty(id: string, data: UpdatePropertyRequest): Promise<Property> {
  try {
    const { getToken } = auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const response = await apiClient.put<ApiResponse<Property>>(`/api/sonobrokers/properties/${id}`, data)
    return response.data!
  } catch (error) {
    console.error('Failed to update property:', error)
    throw error
  }
}

/**
 * Server Action: Delete property
 */
export async function deleteProperty(id: string): Promise<void> {
  try {
    const { getToken } = auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    await apiClient.delete(`/api/sonobrokers/properties/${id}`)
  } catch (error) {
    console.error('Failed to delete property:', error)
    throw error
  }
}

/**
 * Server Action: Get properties by seller
 */
export async function getPropertiesBySeller(sellerId?: string): Promise<Property[]> {
  try {
    const { getToken } = auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const params = sellerId ? { sellerId } : {}
    const response = await getProperties(params)
    return response.properties
  } catch (error) {
    console.error('Failed to get seller properties:', error)
    throw error
  }
}

/**
 * Server Action: Search properties
 */
export async function searchProperties(query: string, filters: PropertySearchParams = {}): Promise<PropertySearchResponse> {
  try {
    const searchParams = {
      ...filters,
      search: query
    }

    return await getProperties(searchParams)
  } catch (error) {
    console.error('Failed to search properties:', error)
    throw error
  }
}

/**
 * Server Action: Get property images
 */
export async function getPropertyImages(propertyId: string): Promise<PropertyImage[]> {
  try {
    const images = await apiClient.get<PropertyImage[]>(`/api/sonobrokers/properties/${propertyId}/images`)
    return images
  } catch (error) {
    console.error('Failed to get property images:', error)
    return []
  }
}

/**
 * Server Action: Upload property images
 */
export async function uploadPropertyImages(propertyId: string, files: File[]): Promise<PropertyImage[]> {
  try {
    const { getToken } = auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const formData = new FormData()
    files.forEach((file, index) => {
      formData.append(`images`, file)
    })
    formData.append('propertyId', propertyId)

    const response = await apiClient.upload<ApiResponse<PropertyImage[]>>(`/api/sonobrokers/property-images/upload/${propertyId}`, files[0], { propertyId })
    return response.data || []
  } catch (error) {
    console.error('Failed to upload property images:', error)
    throw error
  }
}

/**
 * Server Action: Delete property image
 */
export async function deletePropertyImage(propertyId: string, imageId: string): Promise<void> {
  try {
    const { getToken } = auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    await apiClient.delete(`/api/sonobrokers/property-images/${imageId}`)
  } catch (error) {
    console.error('Failed to delete property image:', error)
    throw error
  }
}

/**
 * Server Action: Get featured properties
 */
export async function getFeaturedProperties(limit: number = 6): Promise<Property[]> {
  try {
    const response = await getProperties({ 
      limit, 
      status: 'active',
      page: 1 
    })
    return response.properties
  } catch (error) {
    console.error('Failed to get featured properties:', error)
    return []
  }
}
