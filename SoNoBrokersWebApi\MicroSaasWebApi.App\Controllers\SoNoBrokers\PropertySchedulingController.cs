using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Extensions;
using MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    /// <summary>
    /// Controller for property visit scheduling and management
    /// </summary>
    [ApiController]
    [Route("api/sonobrokers/property-scheduling")]
    [Tags("Property Scheduling")]
    [Authorize]
    public class PropertySchedulingController : ControllerBase
    {
        private readonly IPropertySchedulingService _schedulingService;
        private readonly ILogger<PropertySchedulingController> _logger;

        public PropertySchedulingController(
            IPropertySchedulingService schedulingService,
            ILogger<PropertySchedulingController> logger)
        {
            _schedulingService = schedulingService;
            _logger = logger;
        }

        #region Seller Availability

        /// <summary>
        /// Create seller availability for a property
        /// </summary>
        [HttpPost("availability")]
        public async Task<ActionResult<SellerAvailabilityResponse>> CreateSellerAvailability([FromBody] CreateSellerAvailabilityRequest request)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                var availability = await _schedulingService.CreateSellerAvailabilityAsync(userId, request);
                return Ok(availability);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create seller availability for user {UserId}", User.GetUserId());
                return StatusCode(500, "An error occurred while creating seller availability");
            }
        }

        /// <summary>
        /// Get seller availability
        /// </summary>
        [HttpGet("availability")]
        public async Task<ActionResult<List<SellerAvailabilityResponse>>> GetSellerAvailability([FromQuery] string? propertyId = null)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                var availability = await _schedulingService.GetSellerAvailabilityAsync(userId, propertyId);
                return Ok(availability);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get seller availability for user {UserId}", User.GetUserId());
                return StatusCode(500, "An error occurred while retrieving seller availability");
            }
        }

        /// <summary>
        /// Update seller availability
        /// </summary>
        [HttpPut("availability/{availabilityId}")]
        public async Task<ActionResult<SellerAvailabilityResponse>> UpdateSellerAvailability(
            string availabilityId,
            [FromBody] UpdateSellerAvailabilityRequest request)
        {
            try
            {
                var availability = await _schedulingService.UpdateSellerAvailabilityAsync(availabilityId, request);
                if (availability == null)
                {
                    return NotFound("Seller availability not found");
                }

                return Ok(availability);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update seller availability {AvailabilityId}", availabilityId);
                return StatusCode(500, "An error occurred while updating seller availability");
            }
        }

        /// <summary>
        /// Delete seller availability
        /// </summary>
        [HttpDelete("availability/{availabilityId}")]
        public async Task<ActionResult> DeleteSellerAvailability(string availabilityId)
        {
            try
            {
                var success = await _schedulingService.DeleteSellerAvailabilityAsync(availabilityId);
                if (!success)
                {
                    return NotFound("Seller availability not found");
                }

                return Ok(new { message = "Seller availability deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete seller availability {AvailabilityId}", availabilityId);
                return StatusCode(500, "An error occurred while deleting seller availability");
            }
        }

        /// <summary>
        /// Get property availability for buyers
        /// </summary>
        [HttpGet("property/{propertyId}/availability")]
        [AllowAnonymous]
        public async Task<ActionResult<List<SellerAvailabilityResponse>>> GetPropertyAvailability(string propertyId)
        {
            try
            {
                var availability = await _schedulingService.GetPropertyAvailabilityAsync(propertyId);
                return Ok(availability);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get property availability for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property availability");
            }
        }

        #endregion

        #region Visit Scheduling

        /// <summary>
        /// Create a visit schedule request
        /// </summary>
        [HttpPost("visits")]
        public async Task<ActionResult<PropertyVisitScheduleResponse>> CreateVisitSchedule([FromBody] CreateVisitScheduleRequest request)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                var visit = await _schedulingService.CreateVisitScheduleAsync(userId, request);
                return Ok(visit);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create visit schedule for user {UserId}", User.GetUserId());
                return StatusCode(500, "An error occurred while creating visit schedule");
            }
        }

        /// <summary>
        /// Get visit schedule by ID
        /// </summary>
        [HttpGet("visits/{visitId}")]
        public async Task<ActionResult<PropertyVisitScheduleResponse>> GetVisitSchedule(string visitId)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                // Check access
                var hasAccess = await _schedulingService.CanUserAccessVisitScheduleAsync(userId, visitId);
                if (!hasAccess)
                {
                    return Forbid("Access denied to this visit schedule");
                }

                var visit = await _schedulingService.GetVisitScheduleAsync(visitId);
                if (visit == null)
                {
                    return NotFound("Visit schedule not found");
                }

                return Ok(visit);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get visit schedule {VisitId} for user {UserId}",
                    visitId, User.GetUserId());
                return StatusCode(500, "An error occurred while retrieving visit schedule");
            }
        }

        /// <summary>
        /// Get visit schedules for current user
        /// </summary>
        [HttpGet("visits")]
        public async Task<ActionResult<VisitScheduleSearchResponse>> GetVisitSchedules([FromQuery] VisitScheduleSearchParams searchParams)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                var result = await _schedulingService.GetVisitSchedulesAsync(userId, searchParams);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get visit schedules for user {UserId}", User.GetUserId());
                return StatusCode(500, "An error occurred while retrieving visit schedules");
            }
        }

        /// <summary>
        /// Respond to visit request (seller)
        /// </summary>
        [HttpPut("visits/{visitId}/respond")]
        public async Task<ActionResult<PropertyVisitScheduleResponse>> RespondToVisitRequest(
            string visitId,
            [FromBody] RespondToVisitRequest request)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                // Check access
                var hasAccess = await _schedulingService.CanUserAccessVisitScheduleAsync(userId, visitId);
                if (!hasAccess)
                {
                    return Forbid("Access denied to this visit schedule");
                }

                var visit = await _schedulingService.RespondToVisitRequestAsync(visitId, request);
                if (visit == null)
                {
                    return NotFound("Visit schedule not found");
                }

                return Ok(visit);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to respond to visit request {VisitId} for user {UserId}",
                    visitId, User.GetUserId());
                return StatusCode(500, "An error occurred while responding to visit request");
            }
        }

        /// <summary>
        /// Cancel visit schedule
        /// </summary>
        [HttpPut("visits/{visitId}/cancel")]
        public async Task<ActionResult> CancelVisitSchedule(string visitId, [FromBody] CancelVisitRequest request)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                // Check access
                var hasAccess = await _schedulingService.CanUserAccessVisitScheduleAsync(userId, visitId);
                if (!hasAccess)
                {
                    return Forbid("Access denied to this visit schedule");
                }

                var success = await _schedulingService.CancelVisitScheduleAsync(visitId, request.Reason ?? "Cancelled by user");
                if (!success)
                {
                    return NotFound("Visit schedule not found");
                }

                return Ok(new { message = "Visit schedule cancelled successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to cancel visit schedule {VisitId} for user {UserId}",
                    visitId, User.GetUserId());
                return StatusCode(500, "An error occurred while cancelling visit schedule");
            }
        }

        #endregion

        #region QR Code Management

        /// <summary>
        /// Generate QR code for property
        /// </summary>
        [HttpPost("property/{propertyId}/qr-code")]
        public async Task<ActionResult<PropertyQrCodeResponse>> GeneratePropertyQrCode(string propertyId)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                // Check if user can manage this property
                var canManage = await _schedulingService.CanUserManagePropertySchedulingAsync(userId, propertyId);
                if (!canManage)
                {
                    return Forbid("Access denied to manage this property");
                }

                var qrCode = await _schedulingService.GeneratePropertyQrCodeAsync(propertyId, userId);
                return Ok(qrCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate QR code for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while generating QR code");
            }
        }

        /// <summary>
        /// Get property QR code
        /// </summary>
        [HttpGet("property/{propertyId}/qr-code")]
        public async Task<ActionResult<PropertyQrCodeResponse>> GetPropertyQrCode(string propertyId)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                // Check if user can manage this property
                var canManage = await _schedulingService.CanUserManagePropertySchedulingAsync(userId, propertyId);
                if (!canManage)
                {
                    return Forbid("Access denied to manage this property");
                }

                var qrCode = await _schedulingService.GetPropertyQrCodeAsync(propertyId);
                if (qrCode == null)
                {
                    return NotFound("QR code not found for this property");
                }

                return Ok(qrCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get QR code for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving QR code");
            }
        }

        #endregion

        #region Visit Verification

        /// <summary>
        /// Verify visit using QR code
        /// </summary>
        [HttpPost("visits/{visitId}/verify")]
        public async Task<ActionResult<VisitVerificationResponse>> VerifyVisit(string visitId, [FromBody] VerifyVisitRequest request)
        {
            try
            {
                var verification = await _schedulingService.VerifyVisitAsync(visitId, request);
                return Ok(verification);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to verify visit {VisitId}", visitId);
                return StatusCode(500, "An error occurred while verifying visit");
            }
        }

        /// <summary>
        /// Get visit verifications
        /// </summary>
        [HttpGet("visits/{visitId}/verifications")]
        public async Task<ActionResult<List<VisitVerificationResponse>>> GetVisitVerifications(string visitId)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                // Check access
                var hasAccess = await _schedulingService.CanUserAccessVisitScheduleAsync(userId, visitId);
                if (!hasAccess)
                {
                    return Forbid("Access denied to this visit schedule");
                }

                var verifications = await _schedulingService.GetVisitVerificationsAsync(visitId);
                return Ok(verifications);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get visit verifications for visit {VisitId}", visitId);
                return StatusCode(500, "An error occurred while retrieving visit verifications");
            }
        }

        #endregion

        #region Statistics

        /// <summary>
        /// Get visit scheduling statistics
        /// </summary>
        [HttpGet("stats")]
        public async Task<ActionResult<VisitSchedulingStats>> GetVisitSchedulingStats()
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                var stats = await _schedulingService.GetVisitSchedulingStatsAsync(userId);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get visit scheduling stats for user {UserId}", User.GetUserId());
                return StatusCode(500, "An error occurred while retrieving visit scheduling statistics");
            }
        }

        /// <summary>
        /// Get upcoming visits
        /// </summary>
        [HttpGet("upcoming")]
        public async Task<ActionResult<List<PropertyVisitScheduleResponse>>> GetUpcomingVisits([FromQuery] int days = 7)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                var visits = await _schedulingService.GetUpcomingVisitsAsync(userId, days);
                return Ok(visits);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get upcoming visits for user {UserId}", User.GetUserId());
                return StatusCode(500, "An error occurred while retrieving upcoming visits");
            }
        }

        #endregion
    }

    /// <summary>
    /// Request model for cancelling visits
    /// </summary>
    public class CancelVisitRequest
    {
        public string? Reason { get; set; }
    }
}
