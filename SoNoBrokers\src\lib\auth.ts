import { UserRole, SnbUserType } from '@/types'
// MIGRATED: Use API calls instead of direct service
import { getCurrentUserProfile } from '@/lib/api/auth-api'
import { auth, currentUser } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import React from 'react'

export interface AuthUser {
  id: string
  email: string
  fullName?: string | null
  firstName?: string | null
  lastName?: string | null
  role: UserRole
  userType: SnbUserType
  isActive: boolean
  permissions: Array<{
    permission: string
    resource?: string | null
  }>
}

/**
 * Get the current authenticated user with role and permissions
 */
export async function getAuthUser(): Promise<AuthUser | null> {
  try {
    // Use currentUser() for server components - provides full user data
    const clerkUser = await currentUser()
    if (!clerkUser) return null

    const email = clerkUser.primaryEmailAddress?.emailAddress
    if (!email) return null

    // Get current user profile from API (this handles user creation/sync automatically)
    const userProfile = await getCurrentUserProfile()
    if (!userProfile) return null

    return {
      id: userProfile.id,
      email: userProfile.email,
      fullName: userProfile.fullName,
      firstName: userProfile.firstName,
      lastName: userProfile.lastName,
      role: userProfile.role,
      userType: userProfile.userType,
      isActive: userProfile.isActive,
      permissions: userProfile.permissions || []
    }
  } catch (error) {
    console.error('Error getting auth user:', error)
    return null
  }
}

/**
 * Check if user has specific permission
 */
export function hasPermission(
  user: AuthUser | null,
  permission: string,
  resource?: string
): boolean {
  if (!user || !user.isActive) return false

  return user.permissions.some(
    p => p.permission === permission && (resource ? p.resource === resource : true)
  )
}

/**
 * Check if user has any of the specified roles
 */
export function hasRole(user: AuthUser | null, roles: UserRole[]): boolean {
  if (!user || !user.isActive) return false
  return roles.includes(user.role)
}

/**
 * Check if user is admin
 */
export function isAdmin(user: AuthUser | null): boolean {
  return hasRole(user, [UserRole.ADMIN])
}

/**
 * Require authentication - redirect to sign-in if not authenticated
 */
export async function requireAuth(): Promise<AuthUser> {
  try {
    const user = await getAuthUser()
    if (!user) {
      redirect('/sign-in')
    }
    return user
  } catch (error) {
    console.error('Error in requireAuth:', error)
    redirect('/sign-in')
  }
}

/**
 * Get authenticated user for API routes using auth() function
 * This approach works better with Next.js 15 and Clerk
 */
export async function getAuthUserAPI(): Promise<AuthUser | null> {
  try {
    // Use auth() for API routes - this is the recommended approach
    const { userId } = await auth()

    if (!userId) {
      console.log('No authenticated user found')
      return null
    }

    // Get SNB user by Clerk ID
    const snbUser = await UserService.getUserByClerkId(userId)
    if (!snbUser) {
      console.warn('User authenticated with Clerk but not found in SNB database:', userId)
      return null
    }

    // Get user with permissions
    const userWithPermissions = await UserService.getUserById(snbUser.id)
    if (!userWithPermissions) {
      console.warn('SNB user found but getUserById failed:', snbUser.id)
      return null
    }

    return {
      id: userWithPermissions.id,
      email: userWithPermissions.email,
      fullName: userWithPermissions.fullName,
      firstName: userWithPermissions.firstName,
      lastName: userWithPermissions.lastName,
      role: userWithPermissions.role,
      userType: userWithPermissions.userType,
      isActive: userWithPermissions.isActive,
      permissions: userWithPermissions.permissions || []
    }
  } catch (error) {
    console.error('Error getting auth user for API:', error)
    return null
  }
}

/**
 * Require authentication for API routes - returns null if not authenticated
 */
export async function requireAuthAPI(): Promise<AuthUser | null> {
  try {
    const user = await getAuthUserAPI()
    return user
  } catch (error) {
    console.error('Error in requireAuthAPI:', error)
    return null
  }
}

/**
 * Require specific role - redirect to unauthorized if user doesn't have role
 */
export async function requireRole(roles: UserRole[]): Promise<AuthUser> {
  const user = await requireAuth()
  if (!hasRole(user, roles)) {
    redirect('/unauthorized')
  }
  return user
}

/**
 * Require admin role
 */
export async function requireAdmin(): Promise<AuthUser> {
  return requireRole([UserRole.ADMIN])
}

/**
 * Require admin role for API routes - returns null if not admin
 */
export async function requireAdminAPI(): Promise<AuthUser | null> {
  try {
    const user = await getAuthUserAPI()
    if (!user || !hasRole(user, [UserRole.ADMIN])) {
      return null
    }
    return user
  } catch (error) {
    console.error('Error in requireAdminAPI:', error)
    return null
  }
}

/**
 * Require specific permission
 */
export async function requirePermission(
  permission: string,
  resource?: string
): Promise<AuthUser> {
  const user = await requireAuth()
  if (!hasPermission(user, permission, resource)) {
    redirect('/unauthorized')
  }
  return user
}

/**
 * Role-based route protection HOC
 */
export function withAuth<T extends Record<string, any>>(
  Component: React.ComponentType<T & { user: AuthUser }>,
  options?: {
    roles?: UserRole[]
    permissions?: Array<{ permission: string; resource?: string }>
    redirectTo?: string
  }
) {
  return async function AuthenticatedComponent(props: T) {
    const user = await getAuthUser()

    if (!user) {
      redirect(options?.redirectTo || '/sign-in')
    }

    // Check roles
    if (options?.roles && !hasRole(user, options.roles)) {
      redirect('/unauthorized')
    }

    // Check permissions
    if (options?.permissions) {
      const hasRequiredPermissions = options.permissions.every(
        ({ permission, resource }) => hasPermission(user, permission, resource)
      )
      if (!hasRequiredPermissions) {
        redirect('/unauthorized')
      }
    }

    return React.createElement(Component, { ...props, user } as T & { user: AuthUser })
  }
}

/**
 * Client-side hook for role checking
 */
export function useAuth() {
  // This would be implemented as a React hook for client-side components
  // For now, we'll create the server-side utilities
  return {
    hasPermission,
    hasRole,
    isAdmin
  }
}

// Permission constants
export const PERMISSIONS = {
  // User management
  READ_USERS: 'read',
  WRITE_USERS: 'write',
  DELETE_USERS: 'delete',
  
  // Reports and Analytics
  READ_REPORTS: 'read',
  WRITE_REPORTS: 'write',
  READ_ANALYTICS: 'read',
  
  // Communications
  SEND_BULK_EMAILS: 'send',
  SEND_NOTIFICATIONS: 'send',
  
  // Role management
  MANAGE_ROLES: 'manage',
  
  // Properties
  READ_PROPERTIES: 'read',
  WRITE_PROPERTIES: 'write',
  
  // Profile
  READ_PROFILE: 'read',
  WRITE_PROFILE: 'write'
} as const

export const RESOURCES = {
  USERS: 'users',
  REPORTS: 'reports',
  ANALYTICS: 'analytics',
  BULK_EMAILS: 'bulk_emails',
  NOTIFICATIONS: 'notifications',
  ROLES: 'roles',
  PROPERTIES: 'properties',
  PROFILE: 'profile'
} as const
