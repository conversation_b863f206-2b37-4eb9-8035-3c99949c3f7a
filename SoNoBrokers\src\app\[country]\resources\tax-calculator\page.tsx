import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function TaxCalculatorPage({ params, searchParams }: PageProps) {
  // Resources pages don't require authentication
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us', 'uae']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/resources/tax-calculator')
  }

  const userType = resolvedSearchParams.userType || 'seller'

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8">
            Real Estate Tax Calculator
          </h1>

          <div className="bg-card rounded-lg p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-4">Calculate Your Tax Obligations</h2>
            <p className="text-muted-foreground mb-6">
              Calculate capital gains tax, property transfer tax, and other tax implications
              for your real estate transaction in {country.toUpperCase()}.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <div className="bg-card rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Capital Gains Calculator</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Purchase Price</label>
                  <input
                    type="number"
                    placeholder="$0"
                    className="w-full p-3 border border-border rounded-lg"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Sale Price</label>
                  <input
                    type="number"
                    placeholder="$0"
                    className="w-full p-3 border border-border rounded-lg"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Improvements Cost</label>
                  <input
                    type="number"
                    placeholder="$0"
                    className="w-full p-3 border border-border rounded-lg"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Years Owned</label>
                  <input
                    type="number"
                    placeholder="0"
                    className="w-full p-3 border border-border rounded-lg"
                  />
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Property Transfer Tax</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Property Value</label>
                  <input
                    type="number"
                    placeholder="$0"
                    className="w-full p-3 border border-border rounded-lg"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Province/State</label>
                  <select className="w-full p-3 border border-border rounded-lg">
                    {country === 'ca' ? (
                      <>
                        <option>Ontario</option>
                        <option>British Columbia</option>
                        <option>Alberta</option>
                        <option>Quebec</option>
                      </>
                    ) : (
                      <>
                        <option>California</option>
                        <option>New York</option>
                        <option>Texas</option>
                        <option>Florida</option>
                      </>
                    )}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">First-Time Buyer?</label>
                  <select className="w-full p-3 border border-border rounded-lg">
                    <option>No</option>
                    <option>Yes</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4">Tax Summary</h3>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-primary">$0</div>
                <div className="text-sm text-muted-foreground">Capital Gains Tax</div>
              </div>

              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-primary">$0</div>
                <div className="text-sm text-muted-foreground">Transfer Tax</div>
              </div>

              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-primary">$0</div>
                <div className="text-sm text-muted-foreground">Total Tax</div>
              </div>
            </div>

            <button className="w-full mt-6 bg-primary text-primary-foreground py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors">
              Calculate Taxes
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'seller'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Real Estate Tax Calculator for ${country} | SoNoBrokers`,
    description: `Calculate capital gains tax, property transfer tax, and other real estate taxes in ${country}. Free tax calculator with detailed breakdowns.`,
    keywords: `tax calculator, capital gains, property tax, ${country}, real estate taxes`,
  }
}
