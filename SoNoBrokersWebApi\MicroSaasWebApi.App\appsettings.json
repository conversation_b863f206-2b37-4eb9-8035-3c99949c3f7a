{"AppInfo": {"Name": "MicroSaaS Web API", "Version": "2.0.0", "Environment": "Development", "Description": "Multi-tenant SaaS API with modern .NET 9 architecture"}, "ApiEndpoints": {"DataApi": {"BaseUrl": "", "Timeout": "00:02:00", "RetryCount": 3, "EnableCircuitBreaker": true}, "DocumentApi": {"BaseUrl": "", "Timeout": "00:05:00", "RetryCount": 3, "EnableCircuitBreaker": true}, "PermissionApi": {"BaseUrl": "", "Timeout": "00:01:00", "RetryCount": 3, "EnableCircuitBreaker": true}}, "FeatureFlags": {"EnableSwagger": true, "EnableHangfire": true, "EnableDetailedLogging": true, "EnablePayments": false, "EnableAnalytics": true, "EnableCustomBranding": false, "EnableMultiRegion": false, "EnableAdvancedSecurity": false, "EnableRateLimiting": true, "EnableResponseCompression": true, "EnableHealthChecks": true}, "Authentication": {"Clerk": {"PublishableKey": "", "SecretKey": "", "WebhookSecret": "", "JwtIssuer": "", "JwtAudience": "microsaas-api", "EnableWebhooks": true, "SessionTokenName": "__session", "AllowedOrigins": ["http://localhost:3000", "https://app.microsaas.com"]}, "Jwt": {"Issuer": "", "Audience": "microsaas-clients", "SigningKey": "", "ExpiryMinutes": 60, "RefreshTokenExpiryDays": 7, "EnableRefreshTokens": true}}, "RateLimiting": {"EnableGlobalRateLimit": true, "GlobalRequestsPerMinute": 1000, "EnablePerTenantRateLimit": true, "TenantRequestsPerMinute": 100, "EnablePerUserRateLimit": true, "UserRequestsPerMinute": 60}, "IpRateLimiting": {"EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "HttpStatusCode": 429, "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 100}, {"Endpoint": "*/auth/*", "Period": "5m", "Limit": 5}]}, "IpRateLimitPolicies": {"IpRules": [{"Ip": "127.0.0.1", "Rules": [{"Endpoint": "*", "Period": "1m", "Limit": 1000}]}]}, "Security": {"Cors": {"EnableCors": true, "AllowedOrigins": ["https://localhost:3000", "https://www.sonobrokers.com"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"], "AllowedHeaders": ["*"], "AllowCredentials": true}, "Headers": {"EnableSecurityHeaders": true, "EnableHsts": true, "HstsMaxAge": 31536000, "EnableContentTypeOptions": true, "EnableFrameOptions": true, "EnableXssProtection": true}}, "Monitoring": {"HealthChecks": {"EnableUI": true, "EnableDetailedErrors": true, "CacheDuration": "00:01:00"}}, "HealthChecks": {"Memory": {"MemoryStatus": "Monitoring MicroSaaS API memory usage", "Threshold": **********, "WarningThreshold": *********, "ForceGarbageCollection": false}}, "ApplicationUrl": "https://localhost:7163", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "Microsoft.AspNetCore.Hosting": "Information", "Microsoft.AspNetCore.Routing": "Warning", "System.Net.Http.HttpClient": "Warning", "Hangfire": "Information"}, "Console": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss "}}, "ExternalServices": {"Stripe": {"PublishableKey": "", "SecretKey": "", "WebhookSecret": "", "EnableTestMode": false, "PriceIds": {"PropertyListings": {"USA": {"Residential": "price_usa_residential_listing", "Commercial": "price_usa_commercial_listing", "Land": "price_usa_land_listing", "Rental": "price_usa_rental_listing"}, "Canada": {"Residential": "price_ca_residential_listing", "Commercial": "price_ca_commercial_listing", "Land": "price_ca_land_listing", "Rental": "price_ca_rental_listing"}, "UAE": {"Residential": "price_uae_residential_listing", "Commercial": "price_uae_commercial_listing", "Land": "price_uae_land_listing", "Rental": "price_uae_rental_listing"}}, "AdvertiserSubscriptions": {"USA": {"Basic": "price_usa_advertiser_basic_monthly", "Premium": "price_usa_advertiser_premium_monthly", "Enterprise": "price_usa_advertiser_enterprise_monthly"}, "Canada": {"Basic": "price_ca_advertiser_basic_monthly", "Premium": "price_ca_advertiser_premium_monthly", "Enterprise": "price_ca_advertiser_enterprise_monthly"}, "UAE": {"Basic": "price_uae_advertiser_basic_monthly", "Premium": "price_uae_advertiser_premium_monthly", "Enterprise": "price_uae_advertiser_enterprise_monthly"}}, "ConciergeServices": {"USA": {"Consultation": "price_usa_concierge_consultation", "PropertyManagement": "price_usa_concierge_property_mgmt", "LegalAssistance": "price_usa_concierge_legal", "DocumentPreparation": "price_usa_concierge_documents", "MarketAnalysis": "price_usa_concierge_market_analysis"}, "Canada": {"Consultation": "price_ca_concierge_consultation", "PropertyManagement": "price_ca_concierge_property_mgmt", "LegalAssistance": "price_ca_concierge_legal", "DocumentPreparation": "price_ca_concierge_documents", "MarketAnalysis": "price_ca_concierge_market_analysis"}, "UAE": {"Consultation": "price_uae_concierge_consultation", "PropertyManagement": "price_uae_concierge_property_mgmt", "LegalAssistance": "price_uae_concierge_legal", "DocumentPreparation": "price_uae_concierge_documents", "MarketAnalysis": "price_uae_concierge_market_analysis"}}}}, "Azure": {"Storage": {"ConnectionString": "", "ContainerName": "microsaas-files"}, "ServiceBus": {"ConnectionString": ""}, "KeyVault": {"Uri": "", "ClientId": "", "ClientSecret": "", "TenantId": ""}}, "Email": {"SendGrid": {"ApiKey": ""}, "Smtp": {"Host": "", "Port": 587, "Username": "", "Password": "", "EnableSsl": true}, "Resend": {"ApiKey": ""}}, "Make": {"OrganizationId": "", "TeamId": "", "ApiKey": "", "ApiUrl": "https://us1.make.com/api/v2"}, "N8N": {"ApiKey": "", "ApiUrl": "", "WebhookUrl": ""}, "OpenAI": {"ApiKey": "", "Model": "gpt-4", "MaxTokens": 2000}, "WordPress": {"DatabaseUser": "", "DatabasePassword": "", "DatabaseName": "", "RestEndpoint": "", "MySqlDatabase": "", "MySqlUser": "", "MySqlPassword": ""}}, "Frontend": {"NextJs": {"AppUrl": "https://localhost:3000", "SignInUrl": "/sign-in", "SignUpUrl": "/sign-up"}}, "Supabase": {"Url": "", "AnonKey": "", "ServiceRoleKey": "", "StorageBucket": "property-images"}, "Mapbox": {"ApiKey": ""}, "SoNoBrokers": {"AppUrl": "https://localhost:3000", "SignInUrl": "/sign-in", "SignUpUrl": "/sign-up", "MaxImageUploadSize": 10485760, "AllowedImageTypes": ["image/jpeg", "image/png", "image/webp"], "DefaultPageSize": 20, "MaxPageSize": 100}, "AllowedHosts": "*"}