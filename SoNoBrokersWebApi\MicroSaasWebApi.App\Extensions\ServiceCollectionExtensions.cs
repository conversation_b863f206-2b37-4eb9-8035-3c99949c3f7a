using Azure.Storage.Blobs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Services.Auth;
using MicroSaasWebApi.Services.Auth.Interfaces;
using MicroSaasWebApi.Services.Storage;
using MicroSaasWebApi.Services.Storage.Interfaces;
using MicroSaasWebApi.Services.Payment;
using MicroSaasWebApi.Services.Payment.Interfaces;
using MicroSaasWebApi.Services.Core;
using MicroSaasWebApi.Services.Core.Interfaces;
using MicroSaasWebApi.Services.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;

namespace MicroSaasWebApi.Extensions
{
    /// <summary>
    /// Service collection extensions for dependency injection
    /// Follows Dependency Inversion Principle
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Registers authentication services
        /// </summary>
        public static IServiceCollection AddAuthenticationServices(this IServiceCollection services)
        {
            // Clerk Authentication
            services.AddScoped<IClerkAuthService, ClerkAuthService>();

            // Note: Azure AD Authentication removed - SoNoBrokers uses Clerk authentication only
            // Note: Role-based services removed for SoNoBrokers - using Clerk authentication only

            return services;
        }

        /// <summary>
        /// Registers storage services
        /// </summary>
        public static IServiceCollection AddStorageServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Azure Blob Storage
            services.AddSingleton(x =>
            {
                var connectionString = configuration.GetConnectionString("AzureStorage");
                return new BlobServiceClient(connectionString);
            });
            services.AddScoped<IAzureBlobStorageService, AzureBlobStorageService>();

            // Note: SharePoint services removed for SoNoBrokers

            return services;
        }

        /// <summary>
        /// Registers payment services
        /// </summary>
        public static IServiceCollection AddPaymentServices(this IServiceCollection services)
        {
            // Stripe Payment Service
            services.AddScoped<IStripePaymentService, StripePaymentService>();

            return services;
        }

        /// <summary>
        /// Registers SoNoBrokers services (using proper repository pattern)
        /// </summary>
        public static IServiceCollection AddSoNoBrokersServices(this IServiceCollection services)
        {
            // Database Context
            services.AddScoped<MicroSaasWebApi.App.Context.IDapperDbContext, MicroSaasWebApi.App.Context.DapperDbContext>();

            // Repositories
            services.AddScoped<MicroSaasWebApi.App.Repositories.Interfaces.IPropertyRepository, MicroSaasWebApi.App.Repositories.PropertyRepository>();
            services.AddScoped<MicroSaasWebApi.App.Repositories.Interfaces.IUserRepository, MicroSaasWebApi.App.Repositories.UserRepository>();
            services.AddScoped<MicroSaasWebApi.App.Repositories.Interfaces.IAdvertiserRepository, MicroSaasWebApi.App.Repositories.AdvertiserRepository>();

            // Core SoNoBrokers services
            services.AddScoped<IPropertyService, PropertyService>();
            services.AddScoped<IPropertySearchService, PropertySearchService>();
            services.AddScoped<IPropertyImageService, PropertyImageService>();
            services.AddScoped<MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IUserService, MicroSaasWebApi.Services.SoNoBrokers.UserService>();
            services.AddScoped<IWaitingListService, WaitingListService>();
            services.AddScoped<ISubscriptionService, SubscriptionService>();

            // New migrated services
            services.AddScoped<IAdvertiserService, AdvertiserService>();
            services.AddScoped<IAIPropertyService, AIPropertyService>();
            services.AddScoped<ICommunicationService, CommunicationService>();
            services.AddScoped<IAdminService, AdminService>();
            services.AddScoped<IProjectService, ProjectService>();
            services.AddScoped<ISearchFilterService, SearchFilterService>();

            // HttpClient for external API calls
            services.AddHttpClient<CommunicationService>();

            return services;
        }

        /// <summary>
        /// Registers basic authorization policies for SoNoBrokers
        /// </summary>
        public static IServiceCollection AddAuthorizationPolicies(this IServiceCollection services)
        {
            services.AddAuthorization(options =>
            {
                // Basic policies for SoNoBrokers - using Clerk authentication
                options.AddPolicy("RequireAuthentication", policy =>
                    policy.RequireAuthenticatedUser());

                // Note: Complex role/permission policies removed for SoNoBrokers
                // Using Clerk's built-in role management instead
            });

            return services;
        }

        /// <summary>
        /// Registers all custom services for SoNoBrokers
        /// </summary>
        public static IServiceCollection AddCustomServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddAuthenticationServices();
            services.AddStorageServices(configuration);
            services.AddPaymentServices();
            services.AddSoNoBrokersServices();
            services.AddAuthorizationPolicies();

            return services;
        }
    }

    // Authorization handlers removed - using only Clerk authentication for SoNoBrokers
}
