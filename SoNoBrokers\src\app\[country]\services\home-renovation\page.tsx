import { redirect } from 'next/navigation'
import { HomeRenovationService } from './HomeRenovationService'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function HomeRenovationPage({ params, searchParams }: PageProps) {
  // Services pages don't require authentication
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us', 'uae']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/services/home-renovation')
  }

  const userType = resolvedSearchParams.userType || 'seller'

  return (
    <HomeRenovationService
      userType={userType}
      isSignedIn={isSignedIn}
      country={country.toUpperCase()}
    />
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'seller'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Home Renovation & General Contracting Services in ${country} | SoNoBrokers`,
    description: `Find trusted home renovation contractors in ${country}. Professional painting, roofing, flooring, and general contracting services for your property.`,
    keywords: `home renovation, general contractors, painting services, roofing, flooring, ${country}, home improvement`,
  }
}
