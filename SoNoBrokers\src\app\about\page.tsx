import { getSEOTags, renderSchemaTags } from '@/lib/seo'
import config from '@/config'
import Image from 'next/image'

export const metadata = getSEOTags({
  title: `About Us | ${config.appName}`,
  description: `Learn about ${config.appName}, the commission-free real estate platform revolutionizing property transactions in the US and Canada.`,
  canonicalUrlRelative: '/about',
})

export default function AboutPage() {
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-6">About SoNo Brokers</h1>
        
        <div className="prose dark:prose-invert max-w-none">
          <p className="text-xl mb-8">
            SoNo Brokers is revolutionizing real estate by eliminating unnecessary commission fees
            and connecting property buyers and sellers directly.
          </p>
          
          <div className="relative w-full h-64 md:h-96 rounded-lg overflow-hidden mb-8">
            <Image 
              src="/images/about-hero.jpg" 
              alt="SoNo Brokers Team" 
              fill 
              className="object-cover"
              priority
            />
          </div>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Our Mission</h2>
          <p>
            Our mission is to make real estate transactions more affordable, transparent, and 
            efficient by leveraging technology to eliminate unnecessary intermediaries while 
            still providing access to professional services when needed.
          </p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Our Story</h2>
          <p>
            Founded in 2023, SoNo Brokers was born from the frustration of seeing homeowners 
            lose thousands of dollars in commission fees during property transactions. Our 
            founders, experienced in both real estate and technology, saw an opportunity to 
            create a platform that would disrupt the traditional brokerage model.
          </p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Our Values</h2>
          <ul>
            <li><strong>Transparency:</strong> We believe in clear, upfront pricing with no hidden fees.</li>
            <li><strong>Empowerment:</strong> We give property owners control over their transactions.</li>
            <li><strong>Innovation:</strong> We continuously improve our platform to better serve our users.</li>
            <li><strong>Accessibility:</strong> We make professional real estate services available to everyone.</li>
          </ul>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Our Team</h2>
          <p>
            Our diverse team brings together expertise in real estate, technology, finance, and 
            customer service. We're united by our passion for transforming the real estate industry 
            and making property transactions better for everyone.
          </p>
        </div>
      </div>
      
      {/* Add Schema.org structured data */}
      {renderSchemaTags()}
    </div>
  )
}