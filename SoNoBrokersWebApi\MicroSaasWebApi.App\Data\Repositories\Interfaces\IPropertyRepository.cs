using MicroSaasWebApi.Models.SoNoBrokers;

namespace MicroSaasWebApi.Data.Repositories.Interfaces
{
    /// <summary>
    /// Interface for property repository operations
    /// </summary>
    public interface IPropertyRepository
    {
        // Basic CRUD operations
        Task<IEnumerable<Property>> GetAllAsync();
        Task<Property?> GetByIdAsync(string id);
        Task<string> CreateAsync(Property property);
        Task<bool> UpdateAsync(Property property);
        Task<bool> DeleteAsync(string id);

        // Search and filtering
        Task<(IEnumerable<Property> Properties, int TotalCount)> SearchAsync(
            string? searchTerm = null,
            string? city = null,
            string? province = null,
            decimal? minPrice = null,
            decimal? maxPrice = null,
            int? bedrooms = null,
            int? bathrooms = null,
            PropertyStatus? status = null,
            int page = 1,
            int pageSize = 20);

        // Property-specific operations
        Task<IEnumerable<Property>> GetBySellerIdAsync(string sellerId);
        Task<IEnumerable<Property>> GetActivePropertiesAsync();
        Task<bool> UpdateStatusAsync(string id, PropertyStatus status);
        Task<int> GetCountByStatusAsync(PropertyStatus status);
    }
}
