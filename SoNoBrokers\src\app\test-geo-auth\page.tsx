'use client'

/**
 * Test Page for Geo Detection and Authentication Integration
 * This page demonstrates the complete flow:
 * 1. IP detection and country routing
 * 2. User authentication with .NET Web API
 * 3. Login/logout tracking
 * 4. AppContext integration
 */

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAppContext } from '@/contexts/AppContext'
import { useAuth } from '@/hooks/useAuth'
import { useCountryDetection } from '@/components/geo/CountryDetector'
import { 
  Country, 
  detectCountryFromIP, 
  getCurrentCountryFromPath,
  clearCountryCache 
} from '@/lib/geo'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { SignInButton, SignOutButton } from '@clerk/nextjs'

export default function TestGeoAuthPage() {
  const router = useRouter()
  const pathname = usePathname()
  
  // Context and hooks
  const { country, userType, isLoading: contextLoading } = useAppContext()
  const { 
    user, 
    isAuthenticated, 
    isLoading: authLoading, 
    clerkUser,
    refreshUser,
    logout 
  } = useAuth()
  const { detectedCountry, isDetecting, error: geoError } = useCountryDetection()

  // Local state for testing
  const [ipCountry, setIpCountry] = useState<Country | null>(null)
  const [testResults, setTestResults] = useState<string[]>([])

  // Add test result
  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`])
  }

  // Test IP detection
  const testIpDetection = async () => {
    try {
      addTestResult('Testing IP detection...')
      const detected = await detectCountryFromIP()
      setIpCountry(detected)
      addTestResult(`IP detection result: ${detected}`)
    } catch (error) {
      addTestResult(`IP detection error: ${error}`)
    }
  }

  // Clear country cache
  const handleClearCache = () => {
    clearCountryCache()
    addTestResult('Country cache cleared')
  }

  // Test user refresh
  const handleRefreshUser = async () => {
    try {
      addTestResult('Refreshing user data...')
      await refreshUser()
      addTestResult('User data refreshed successfully')
    } catch (error) {
      addTestResult(`User refresh error: ${error}`)
    }
  }

  // Test logout
  const handleLogout = async () => {
    try {
      addTestResult('Logging out...')
      await logout()
      addTestResult('Logout completed')
    } catch (error) {
      addTestResult(`Logout error: ${error}`)
    }
  }

  // Get current country from path
  const currentPathCountry = getCurrentCountryFromPath()

  useEffect(() => {
    addTestResult('Page loaded')
    addTestResult(`Current pathname: ${pathname}`)
    if (currentPathCountry) {
      addTestResult(`Country from path: ${currentPathCountry}`)
    }
  }, [pathname, currentPathCountry])

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Geo Detection & Auth Test</h1>
        <p className="text-muted-foreground">
          Test page for IP detection, country routing, and .NET Web API authentication
        </p>
      </div>

      {/* Country Detection Status */}
      <Card>
        <CardHeader>
          <CardTitle>Country Detection Status</CardTitle>
          <CardDescription>
            IP-based country detection and routing information
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm font-medium">Current Path</p>
              <Badge variant="outline">{pathname}</Badge>
            </div>
            <div>
              <p className="text-sm font-medium">Path Country</p>
              <Badge variant={currentPathCountry ? "default" : "secondary"}>
                {currentPathCountry || 'None'}
              </Badge>
            </div>
            <div>
              <p className="text-sm font-medium">Context Country</p>
              <Badge variant="default">{country}</Badge>
            </div>
            <div>
              <p className="text-sm font-medium">Detected Country</p>
              <Badge variant={detectedCountry ? "default" : "secondary"}>
                {isDetecting ? 'Detecting...' : (detectedCountry || 'None')}
              </Badge>
            </div>
          </div>

          {geoError && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
              <p className="text-sm text-destructive">Geo Error: {geoError}</p>
            </div>
          )}

          <div className="flex gap-2">
            <Button onClick={testIpDetection} variant="outline" size="sm">
              Test IP Detection
            </Button>
            <Button onClick={handleClearCache} variant="outline" size="sm">
              Clear Cache
            </Button>
          </div>

          {ipCountry && (
            <div className="p-3 bg-primary/10 border border-primary/20 rounded-md">
              <p className="text-sm">Manual IP Detection Result: <strong>{ipCountry}</strong></p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Authentication Status */}
      <Card>
        <CardHeader>
          <CardTitle>Authentication Status</CardTitle>
          <CardDescription>
            .NET Web API authentication and user management
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm font-medium">Auth Status</p>
              <Badge variant={isAuthenticated ? "default" : "secondary"}>
                {authLoading ? 'Loading...' : (isAuthenticated ? 'Authenticated' : 'Not Authenticated')}
              </Badge>
            </div>
            <div>
              <p className="text-sm font-medium">User Type</p>
              <Badge variant="outline">{userType}</Badge>
            </div>
            <div>
              <p className="text-sm font-medium">Clerk User</p>
              <Badge variant={clerkUser ? "default" : "secondary"}>
                {clerkUser ? 'Loaded' : 'None'}
              </Badge>
            </div>
            <div>
              <p className="text-sm font-medium">SNB User</p>
              <Badge variant={user ? "default" : "secondary"}>
                {user ? 'Loaded' : 'None'}
              </Badge>
            </div>
          </div>

          {user && (
            <div className="p-3 bg-primary/10 border border-primary/20 rounded-md space-y-2">
              <p className="text-sm"><strong>User ID:</strong> {user.id}</p>
              <p className="text-sm"><strong>Email:</strong> {user.email}</p>
              <p className="text-sm"><strong>Full Name:</strong> {user.fullName}</p>
              <p className="text-sm"><strong>Role:</strong> {user.role}</p>
              <p className="text-sm"><strong>User Type:</strong> {user.userType}</p>
              <p className="text-sm"><strong>Active:</strong> {user.isActive ? 'Yes' : 'No'}</p>
              <p className="text-sm"><strong>Logged In:</strong> {user.loggedIn ? 'Yes' : 'No'}</p>
            </div>
          )}

          <div className="flex gap-2">
            {isAuthenticated ? (
              <>
                <Button onClick={handleRefreshUser} variant="outline" size="sm">
                  Refresh User
                </Button>
                <Button onClick={handleLogout} variant="outline" size="sm">
                  Logout
                </Button>
                <SignOutButton>
                  <Button variant="destructive" size="sm">
                    Sign Out (Clerk)
                  </Button>
                </SignOutButton>
              </>
            ) : (
              <SignInButton mode="modal">
                <Button variant="default" size="sm">
                  Sign In
                </Button>
              </SignInButton>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>Test Results</CardTitle>
          <CardDescription>
            Real-time log of test operations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-1 max-h-60 overflow-y-auto">
            {testResults.map((result, index) => (
              <p key={index} className="text-sm font-mono bg-muted p-2 rounded">
                {result}
              </p>
            ))}
          </div>
          {testResults.length === 0 && (
            <p className="text-sm text-muted-foreground">No test results yet</p>
          )}
        </CardContent>
      </Card>

      {/* Navigation Test */}
      <Card>
        <CardHeader>
          <CardTitle>Navigation Test</CardTitle>
          <CardDescription>
            Test country-based routing
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Button 
              onClick={() => router.push('/ca')} 
              variant="outline" 
              size="sm"
            >
              Go to /ca
            </Button>
            <Button 
              onClick={() => router.push('/usa')} 
              variant="outline" 
              size="sm"
            >
              Go to /usa
            </Button>
            <Button 
              onClick={() => router.push('/uae')} 
              variant="outline" 
              size="sm"
            >
              Go to /uae
            </Button>
            <Button 
              onClick={() => router.push('/')} 
              variant="outline" 
              size="sm"
            >
              Go to / (root)
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
