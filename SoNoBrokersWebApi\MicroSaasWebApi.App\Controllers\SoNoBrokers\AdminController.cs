using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/admin")]
    [Tags("Admin")]
    public class AdminController : ControllerBase
    {
        private readonly IAdminService _adminService;
        private readonly ILogger<AdminController> _logger;

        public AdminController(
            IAdminService adminService,
            ILogger<AdminController> logger)
        {
            _adminService = adminService;
            _logger = logger;
        }

        /// <summary>
        /// Get admin dashboard statistics
        /// </summary>
        [HttpGet("dashboard")]
        public async Task<ActionResult<AdminDashboardResponse>> GetDashboard()
        {
            try
            {
                // TODO: Add admin authorization check
                var result = await _adminService.GetDashboardDataAsync();

                if (!result.Success)
                {
                    return StatusCode(500, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting admin dashboard");
                return StatusCode(500, new { success = false, error = "Failed to fetch dashboard data" });
            }
        }

        // NOTE: User management endpoints moved to UsersController (/api/sonobrokers/users)
        // This keeps admin endpoints focused on admin-specific functionality

        /// <summary>
        /// Get role permissions
        /// </summary>
        [HttpGet("roles")]
        public async Task<ActionResult<List<RolePermissionResponse>>> GetRolePermissions()
        {
            try
            {
                // TODO: Add admin authorization check
                var permissions = await _adminService.GetRolePermissionsAsync();
                return Ok(permissions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting role permissions");
                return StatusCode(500, new { error = "Failed to get role permissions" });
            }
        }

        /// <summary>
        /// Create role permission
        /// </summary>
        [HttpPost("roles")]
        public async Task<ActionResult<RolePermissionResponse>> CreateRolePermission([FromBody] CreateRolePermissionRequest request)
        {
            try
            {
                // TODO: Add admin authorization check
                var permission = await _adminService.CreateRolePermissionAsync(request);
                return CreatedAtAction(nameof(GetRolePermissions), permission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating role permission");
                return StatusCode(500, new { error = "Failed to create role permission" });
            }
        }

        /// <summary>
        /// Update role permission
        /// </summary>
        [HttpPut("roles/{id}")]
        public async Task<ActionResult> UpdateRolePermission(string id, [FromBody] UpdateRolePermissionRequest request)
        {
            try
            {
                // TODO: Add admin authorization check
                request.Id = id;
                var updated = await _adminService.UpdateRolePermissionAsync(request);
                if (!updated)
                {
                    return NotFound(new { error = "Role permission not found" });
                }

                return Ok(new { message = "Role permission updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating role permission: {Id}", id);
                return StatusCode(500, new { error = "Failed to update role permission" });
            }
        }

        /// <summary>
        /// Delete role permission
        /// </summary>
        [HttpDelete("roles/{id}")]
        public async Task<ActionResult> DeleteRolePermission(string id)
        {
            try
            {
                // TODO: Add admin authorization check
                var deleted = await _adminService.DeleteRolePermissionAsync(id);
                if (!deleted)
                {
                    return NotFound(new { error = "Role permission not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting role permission: {Id}", id);
                return StatusCode(500, new { error = "Failed to delete role permission" });
            }
        }

        /// <summary>
        /// Sync Stripe data
        /// </summary>
        [HttpPost("stripe/sync")]
        public async Task<ActionResult<StripeSyncResponse>> SyncStripeData([FromBody] StripeSyncRequest request)
        {
            try
            {
                // TODO: Add admin authorization check
                var result = await _adminService.SyncStripeDataAsync(request);

                if (!result.Success)
                {
                    return StatusCode(500, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing Stripe data");
                return StatusCode(500, new { error = "Failed to sync Stripe data" });
            }
        }
    }
}
