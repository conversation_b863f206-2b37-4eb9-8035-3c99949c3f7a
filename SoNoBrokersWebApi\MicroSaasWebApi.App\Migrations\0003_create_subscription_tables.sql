-- Migration: 0003_create_subscription_tables.sql
-- Description: Create subscription and service provider related tables
-- Date: 2024-12-25
-- Author: Migration System
-- Dependencies: 0002_create_core_tables.sql

-- =====================================================
-- SUBSCRIPTION AND PAYMENT TABLES
-- =====================================================

-- Subscription SNB table
CREATE TABLE IF NOT EXISTS public."SubscriptionSnb" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "userId" UUID NOT NULL,
  type "SubscriptionType" NOT NULL,
  status "SubscriptionStatus" DEFAULT 'PENDING',
  "startDate" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "endDate" TIMESTAMP WITH TIME ZONE,
  "autoRenew" BOOLEAN DEFAULT TRUE,
  "paymentMethod" TEXT,
  "lastPaymentDate" TIMESTAMP WITH TIME ZONE,
  "nextPaymentDate" TIMESTAMP WITH TIME ZONE,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT "SubscriptionSnb_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- Advertiser table
CREATE TABLE IF NOT EXISTS public."Advertiser" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "userId" UUID NOT NULL,
  "businessName" TEXT NOT NULL,
  "businessType" TEXT,
  "businessDescription" TEXT,
  "contactEmail" TEXT,
  "contactPhone" TEXT,
  "businessAddress" JSONB,
  "websiteUrl" TEXT,
  "logoUrl" TEXT,
  "serviceAreas" TEXT[],
  "specialties" TEXT[],
  "licenseNumber" TEXT,
  "insuranceInfo" JSONB,
  "businessHours" JSONB,
  "socialMediaLinks" JSONB,
  "isVerified" BOOLEAN DEFAULT FALSE,
  "verificationDate" TIMESTAMP WITH TIME ZONE,
  "rating" DECIMAL(3,2) DEFAULT 0.00,
  "reviewCount" INTEGER DEFAULT 0,
  "isActive" BOOLEAN DEFAULT TRUE,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT "Advertiser_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- Advertiser Subscription table
CREATE TABLE IF NOT EXISTS public."AdvertiserSubscription" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "advertiserId" UUID NOT NULL,
  "subscriptionType" "SubscriptionType" NOT NULL,
  status "SubscriptionStatus" DEFAULT 'PENDING',
  "startDate" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "endDate" TIMESTAMP WITH TIME ZONE,
  "monthlyFee" DECIMAL(10,2),
  "featuresIncluded" JSONB,
  "maxListings" INTEGER,
  "priorityPlacement" BOOLEAN DEFAULT FALSE,
  "analyticsAccess" BOOLEAN DEFAULT FALSE,
  "customBranding" BOOLEAN DEFAULT FALSE,
  "leadManagement" BOOLEAN DEFAULT FALSE,
  "autoRenew" BOOLEAN DEFAULT TRUE,
  "lastPaymentDate" TIMESTAMP WITH TIME ZONE,
  "nextPaymentDate" TIMESTAMP WITH TIME ZONE,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT "AdvertiserSubscription_advertiserId_fkey" FOREIGN KEY ("advertiserId") REFERENCES public."Advertiser"(id) ON DELETE CASCADE
);

-- Service Provider table
CREATE TABLE IF NOT EXISTS public."ServiceProvider" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "userId" UUID NOT NULL,
  "serviceType" "ServiceType" NOT NULL,
  "businessName" TEXT NOT NULL,
  "businessDescription" TEXT,
  "contactEmail" TEXT,
  "contactPhone" TEXT,
  "businessAddress" JSONB,
  "websiteUrl" TEXT,
  "logoUrl" TEXT,
  "serviceAreas" TEXT[],
  "specialties" TEXT[],
  "licenseNumber" TEXT,
  "insuranceInfo" JSONB,
  "businessHours" JSONB,
  "socialMediaLinks" JSONB,
  "portfolioImages" TEXT[],
  "certifications" TEXT[],
  "yearsExperience" INTEGER,
  "hourlyRate" DECIMAL(10,2),
  "fixedRates" JSONB,
  "isVerified" BOOLEAN DEFAULT FALSE,
  "verificationDate" TIMESTAMP WITH TIME ZONE,
  "rating" DECIMAL(3,2) DEFAULT 0.00,
  "reviewCount" INTEGER DEFAULT 0,
  "totalBookings" INTEGER DEFAULT 0,
  "completedBookings" INTEGER DEFAULT 0,
  "isActive" BOOLEAN DEFAULT TRUE,
  "availabilitySchedule" JSONB,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  CONSTRAINT "ServiceProvider_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- Service Booking table
CREATE TABLE IF NOT EXISTS public."ServiceBooking" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "serviceProviderId" UUID NOT NULL,
  "customerId" UUID NOT NULL,
  "propertyId" UUID,
  "serviceType" "ServiceType" NOT NULL,
  "bookingDate" TIMESTAMP WITH TIME ZONE NOT NULL,
  "duration" INTEGER DEFAULT 60,
  "totalCost" DECIMAL(10,2),
  "description" TEXT,
  "customerNotes" TEXT,
  "providerNotes" TEXT,
  status TEXT DEFAULT 'pending',
  "paymentStatus" TEXT DEFAULT 'pending',
  "completedAt" TIMESTAMP WITH TIME ZONE,
  "cancelledAt" TIMESTAMP WITH TIME ZONE,
  "cancellationReason" TEXT,
  "customerRating" INTEGER,
  "customerReview" TEXT,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  CONSTRAINT "ServiceBooking_serviceProviderId_fkey" FOREIGN KEY ("serviceProviderId") REFERENCES public."ServiceProvider"(id) ON DELETE CASCADE,
  CONSTRAINT "ServiceBooking_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES public."User"(id) ON DELETE CASCADE,
  CONSTRAINT "ServiceBooking_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE SET NULL
);

-- =====================================================
-- STRIPE PAYMENT INTEGRATION TABLES
-- =====================================================

-- Stripe Customers table
CREATE TABLE IF NOT EXISTS public."stripe_customers" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "user_id" UUID NOT NULL,
  "stripe_customer_id" TEXT UNIQUE NOT NULL,
  email TEXT,
  name TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  CONSTRAINT "stripe_customers_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- Stripe Products table
CREATE TABLE IF NOT EXISTS public."stripe_products" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "stripe_product_id" TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  active BOOLEAN DEFAULT TRUE,
  metadata JSONB,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ Migration 0003_create_subscription_tables.sql completed successfully';
    RAISE NOTICE 'Created tables:';
    RAISE NOTICE '- SubscriptionSnb (user subscriptions)';
    RAISE NOTICE '- Advertiser (business advertisers)';
    RAISE NOTICE '- AdvertiserSubscription (advertiser plans)';
    RAISE NOTICE '- ServiceProvider (service providers)';
    RAISE NOTICE '- ServiceBooking (service bookings)';
    RAISE NOTICE '- stripe_customers (Stripe integration)';
    RAISE NOTICE '- stripe_products (Stripe products)';
    RAISE NOTICE '';
END $$;
