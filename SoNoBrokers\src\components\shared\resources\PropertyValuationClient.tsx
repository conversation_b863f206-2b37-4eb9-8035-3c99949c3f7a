'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion';
import {
  Home,
  MapPin,
  DollarSign,
  TrendingUp,
  Calendar,
  Ruler,
  Bed,
  Bath,
  Car,
  Sparkles,
  FileText,
  BarChart3,
  Loader2
} from 'lucide-react';
import { AddressAutofill } from '../properties/AddressAutofill';

interface PropertyValuationClientProps {
  country: string;
  userType: 'buyer' | 'seller';
}

interface PropertyDetails {
  address: string;
  city: string;
  province: string;
  postalCode: string;
  propertyType: string;
  yearBuilt: number;
  bedrooms: number;
  bathrooms: number;
  squareFootage: number;
  lotSize: number;
  parkingSpaces: number;
  features: string[];
  condition: string;
  neighborhood: {
    name: string;
    walkScore: number;
    schools: string[];
    amenities: string[];
  };
  marketData: {
    averagePrice: number;
    pricePerSqFt: number;
    daysOnMarket: number;
    priceChange: number;
  };
  valuation: {
    estimatedValue: number;
    lowEstimate: number;
    highEstimate: number;
    confidence: number;
  };
}

export function PropertyValuationClient({ country, userType }: PropertyValuationClientProps) {
  const router = useRouter();
  const [address, setAddress] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [propertyDetails, setPropertyDetails] = useState<PropertyDetails | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleAddressSelect = (addressComponents: any) => {
    const fullAddress = `${addressComponents.address}, ${addressComponents.city}, ${addressComponents.province}`;
    setAddress(fullAddress);
  };

  const getInstantValuation = async () => {
    if (!address.trim()) {
      setError('Please enter a property address');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Call AI valuation service
      const response = await fetch('/api/ai-property-valuation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          address,
          country
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get property valuation');
      }

      const data = await response.json();
      setPropertyDetails(data);
    } catch (err) {
      setError('Unable to get property valuation. Please try again.');
      console.error('Valuation error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: country === 'CA' ? 'CAD' : country === 'US' ? 'USD' : 'AED',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(1)}%`;
  };

  return (
    <div className="flex flex-col h-screen bg-background">
      {/* Header Section */}
      <div className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="px-4 py-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-foreground mb-2 flex items-center justify-center">
              <Sparkles className="w-8 h-8 mr-3 text-primary" />
              AI Property Valuation Tool
            </h1>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Get instant, AI-powered property valuations using real-time market data and comparable sales analysis.
              Supports properties in Canada, USA, and UAE.
            </p>
          </div>
        </div>
      </div>

      {/* Search Bar */}
      <div className="border-b border-border bg-card">
        <div className="px-4 py-4">
          <div className="max-w-2xl mx-auto space-y-4">
            <div className="space-y-2">
              <Label htmlFor="address" className="text-sm font-medium">Property Address</Label>
              <AddressAutofill
                value={address}
                onChange={setAddress}
                onAddressSelect={handleAddressSelect}
                placeholder="Enter property address (e.g., 123 Main St, Toronto, ON)"
                className="min-w-[500px] w-full"
              />
              <p className="text-xs text-muted-foreground">
                Supports addresses in Canada, United States, and United Arab Emirates
              </p>
            </div>

            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
                {error}
              </div>
            )}

            <Button
              onClick={getInstantValuation}
              className="w-full"
              size="lg"
              disabled={isLoading || !address.trim()}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Analyzing Property...
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4 mr-2" />
                  Get Instant AI Valuation
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto">
          {!propertyDetails ? (
            /* How It Works Section */
            <div className="p-6">
              <div className="max-w-4xl mx-auto">
                <div className="bg-card rounded-lg p-6">
                  <h2 className="text-2xl font-semibold mb-6 text-center">How Our AI Valuation Works</h2>
                  <div className="grid md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-3">1</div>
                      <h3 className="font-semibold mb-2">Enter Address</h3>
                      <p className="text-sm text-muted-foreground">Simply enter the property address using our smart autofill</p>
                    </div>
                    <div className="text-center">
                      <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-3">2</div>
                      <h3 className="font-semibold mb-2">AI Analysis</h3>
                      <p className="text-sm text-muted-foreground">Our AI analyzes comparable sales, market trends, and property features</p>
                    </div>
                    <div className="text-center">
                      <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-3">3</div>
                      <h3 className="font-semibold mb-2">Instant Report</h3>
                      <p className="text-sm text-muted-foreground">Get detailed valuation with property insights and market data</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Valuation Results */
            <div className="p-6">
              <div className="max-w-6xl mx-auto space-y-6">
                {/* Header with Property Info */}
                <div className="border-b border-border pb-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h2 className="text-2xl font-semibold">Property Valuation Results</h2>
                      <p className="text-muted-foreground">{propertyDetails.address}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-3xl font-bold text-primary">
                        {formatCurrency(propertyDetails.valuation.estimatedValue)}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {propertyDetails.valuation.confidence}% Confidence
                      </p>
                    </div>
                  </div>
                </div>

                {/* Quick Stats Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="font-semibold">{formatCurrency(propertyDetails.marketData.pricePerSqFt)}</div>
                      <div className="text-xs text-muted-foreground">Price per Sq Ft</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="font-semibold">{propertyDetails.marketData.daysOnMarket} days</div>
                      <div className="text-xs text-muted-foreground">Avg Days on Market</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className={`font-semibold ${propertyDetails.marketData.priceChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {formatPercentage(propertyDetails.marketData.priceChange)}
                      </div>
                      <div className="text-xs text-muted-foreground">Price Change YoY</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="font-semibold">{formatCurrency(propertyDetails.marketData.averagePrice)}</div>
                      <div className="text-xs text-muted-foreground">Area Average</div>
                    </CardContent>
                  </Card>
                </div>

                {/* Value Range */}
                <Card>
                  <CardContent className="p-4">
                    <h3 className="font-semibold mb-3">Valuation Range</h3>
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <div className="font-semibold text-green-600">
                          {formatCurrency(propertyDetails.valuation.lowEstimate)}
                        </div>
                        <div className="text-xs text-muted-foreground">Low Estimate</div>
                      </div>
                      <div>
                        <div className="font-semibold text-primary text-lg">
                          {formatCurrency(propertyDetails.valuation.estimatedValue)}
                        </div>
                        <div className="text-xs text-muted-foreground">Best Estimate</div>
                      </div>
                      <div>
                        <div className="font-semibold text-red-600">
                          {formatCurrency(propertyDetails.valuation.highEstimate)}
                        </div>
                        <div className="text-xs text-muted-foreground">High Estimate</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Detailed Property Information Accordion */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <FileText className="w-5 h-5 mr-2" />
                      Detailed Property Analysis
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Accordion type="single" collapsible className="w-full">
                      <AccordionItem value="property-details">
                        <AccordionTrigger className="text-left">
                          <div className="flex items-center">
                            <Home className="w-4 h-4 mr-2" />
                            Property Details & Features
                          </div>
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-4">
                            <div className="grid md:grid-cols-2 gap-4">
                              <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                  <span className="flex items-center text-sm">
                                    <MapPin className="w-4 h-4 mr-2" />
                                    Address
                                  </span>
                                  <span className="font-medium">{propertyDetails.address}</span>
                                </div>
                                <div className="flex items-center justify-between">
                                  <span className="flex items-center text-sm">
                                    <Home className="w-4 h-4 mr-2" />
                                    Property Type
                                  </span>
                                  <span className="font-medium">{propertyDetails.propertyType}</span>
                                </div>
                                <div className="flex items-center justify-between">
                                  <span className="flex items-center text-sm">
                                    <Calendar className="w-4 h-4 mr-2" />
                                    Year Built
                                  </span>
                                  <span className="font-medium">{propertyDetails.yearBuilt}</span>
                                </div>
                                <div className="flex items-center justify-between">
                                  <span className="flex items-center text-sm">
                                    <Ruler className="w-4 h-4 mr-2" />
                                    Square Footage
                                  </span>
                                  <span className="font-medium">{propertyDetails.squareFootage.toLocaleString()} sq ft</span>
                                </div>
                              </div>
                              <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                  <span className="flex items-center text-sm">
                                    <Bed className="w-4 h-4 mr-2" />
                                    Bedrooms
                                  </span>
                                  <span className="font-medium">{propertyDetails.bedrooms}</span>
                                </div>
                                <div className="flex items-center justify-between">
                                  <span className="flex items-center text-sm">
                                    <Bath className="w-4 h-4 mr-2" />
                                    Bathrooms
                                  </span>
                                  <span className="font-medium">{propertyDetails.bathrooms}</span>
                                </div>
                                <div className="flex items-center justify-between">
                                  <span className="flex items-center text-sm">
                                    <Car className="w-4 h-4 mr-2" />
                                    Parking Spaces
                                  </span>
                                  <span className="font-medium">{propertyDetails.parkingSpaces}</span>
                                </div>
                                <div className="flex items-center justify-between">
                                  <span className="flex items-center text-sm">
                                    <Ruler className="w-4 h-4 mr-2" />
                                    Lot Size
                                  </span>
                                  <span className="font-medium">{propertyDetails.lotSize.toLocaleString()} sq ft</span>
                                </div>
                              </div>
                            </div>

                            <div>
                              <h4 className="font-semibold mb-2">Property Features</h4>
                              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                                {propertyDetails.features.map((feature, index) => (
                                  <div key={index} className="text-sm bg-muted rounded px-2 py-1">
                                    {feature}
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>

                      <AccordionItem value="neighborhood">
                        <AccordionTrigger className="text-left">
                          <div className="flex items-center">
                            <MapPin className="w-4 h-4 mr-2" />
                            Neighborhood Analysis
                          </div>
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-4">
                            <div className="grid md:grid-cols-2 gap-4">
                              <div>
                                <h4 className="font-semibold mb-2">Neighborhood: {propertyDetails.neighborhood.name}</h4>
                                <div className="space-y-2">
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm">Walk Score</span>
                                    <span className="font-medium">{propertyDetails.neighborhood.walkScore}/100</span>
                                  </div>
                                </div>
                              </div>
                              <div>
                                <h4 className="font-semibold mb-2">Nearby Schools</h4>
                                <ul className="space-y-1">
                                  {propertyDetails.neighborhood.schools.map((school, index) => (
                                    <li key={index} className="text-sm text-muted-foreground">• {school}</li>
                                  ))}
                                </ul>
                              </div>
                            </div>

                            <div>
                              <h4 className="font-semibold mb-2">Local Amenities</h4>
                              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                                {propertyDetails.neighborhood.amenities.map((amenity, index) => (
                                  <div key={index} className="text-sm bg-muted rounded px-2 py-1">
                                    {amenity}
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>

                      <AccordionItem value="market-analysis">
                        <AccordionTrigger className="text-left">
                          <div className="flex items-center">
                            <BarChart3 className="w-4 h-4 mr-2" />
                            Market Analysis & Trends
                          </div>
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-4">
                            <div className="grid md:grid-cols-2 gap-6">
                              <div>
                                <h4 className="font-semibold mb-3">Market Insights</h4>
                                <div className="space-y-3">
                                  <div className="flex items-center space-x-3">
                                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span className="text-sm">Strong seller's market in this area</span>
                                  </div>
                                  <div className="flex items-center space-x-3">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                    <span className="text-sm">Properties selling above asking price</span>
                                  </div>
                                  <div className="flex items-center space-x-3">
                                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                    <span className="text-sm">Low inventory driving competition</span>
                                  </div>
                                </div>
                              </div>

                              <div>
                                <h4 className="font-semibold mb-3">Valuation Factors</h4>
                                <div className="space-y-2 text-sm">
                                  <div>• Recent comparable sales analysis</div>
                                  <div>• Property condition and features</div>
                                  <div>• Neighborhood desirability score</div>
                                  <div>• Current market trends and demand</div>
                                  <div>• Location-specific price adjustments</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  </CardContent>
                </Card>

                {/* Next Steps */}
                <Card>
                  <CardHeader>
                    <CardTitle>Next Steps</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <p className="text-muted-foreground">
                        This AI valuation provides a comprehensive estimate based on current market data.
                        For the most accurate valuation, consider these additional steps:
                      </p>
                      <div className="grid md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <h4 className="font-semibold">For Sellers</h4>
                          <ul className="text-sm space-y-1">
                            <li>• Get a professional appraisal</li>
                            <li>• Consider home staging improvements</li>
                            <li>• Review comparable recent sales</li>
                            <li>• Consult with local market experts</li>
                          </ul>
                        </div>
                        <div className="space-y-2">
                          <h4 className="font-semibold">For Buyers</h4>
                          <ul className="text-sm space-y-1">
                            <li>• Schedule a professional inspection</li>
                            <li>• Research neighborhood trends</li>
                            <li>• Compare with similar properties</li>
                            <li>• Consider future market projections</li>
                          </ul>
                        </div>
                      </div>
                      <div className="flex gap-3 pt-4">
                        <Button
                          className="flex-1"
                          onClick={() => router.push(`/${country.toLowerCase()}/services/professional-appraisal`)}
                        >
                          Get Professional Appraisal
                        </Button>
                        <Button
                          variant="outline"
                          className="flex-1"
                          onClick={() => router.push(`/${country.toLowerCase()}/properties`)}
                        >
                          View Similar Properties
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
