import { HomeInspectionService } from './HomeInspectionService'
// Removed auth import - no authentication required for browsing home inspection services
import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function HomeInspectionPage({ params, searchParams }: PageProps) {
  // No authentication required for browsing home inspection services
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/services/home-inspection')
  }

  // Default to buyer if no userType specified
  const userType = resolvedSearchParams.userType || 'buyer'

  return (
    <HomeInspectionService
      userType={userType}
      isSignedIn={isSignedIn}
      country={country.toUpperCase()}
    />
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'buyer'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Home Inspection Services for ${userType === 'buyer' ? 'Buyers' : 'Sellers'} in ${country} | SoNoBrokers`,
    description: `Find certified home inspection services in ${country}. Professional inspectors for ${userType === 'buyer' ? 'property purchases' : 'pre-listing inspections'}. Compare prices and book online.`,
    keywords: `home inspection, property inspection, ${userType}, ${country}, real estate services`,
  }
}
