using System.ComponentModel.DataAnnotations;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    /// <summary>
    /// Login request DTO
    /// </summary>
    public class LoginRequest
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        public string Password { get; set; } = string.Empty;

        public bool RememberMe { get; set; } = false;
    }

    /// <summary>
    /// Register request DTO
    /// </summary>
    public class RegisterRequest
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        [MinLength(8)]
        public string Password { get; set; } = string.Empty;

        [Required]
        [Compare("Password")]
        public string ConfirmPassword { get; set; } = string.Empty;

        [Required]
        public string FullName { get; set; } = string.Empty;

        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Phone { get; set; }
        public UserRole Role { get; set; } = UserRole.USER;
        public SnbUserType UserType { get; set; } = SnbUserType.BUYER;
    }

    /// <summary>
    /// Authentication response DTO
    /// </summary>
    public class AuthResponse
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public string? Token { get; set; }
        public string? RefreshToken { get; set; }
        public UserInfo? User { get; set; }
        public string? Error { get; set; }
        public DateTime? ExpiresAt { get; set; }
    }

    /// <summary>
    /// User information DTO
    /// </summary>
    public class UserInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public UserRole Role { get; set; }
        public SnbUserType UserType { get; set; }
        public bool IsActive { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public bool LoggedIn { get; set; }
        public List<PermissionInfo> Permissions { get; set; } = new();
    }

    /// <summary>
    /// Permission information DTO
    /// </summary>
    public class PermissionInfo
    {
        public string Permission { get; set; } = string.Empty;
        public string Resource { get; set; } = string.Empty;
    }

    /// <summary>
    /// Token validation request DTO
    /// </summary>
    public class TokenValidationRequest
    {
        [Required]
        public string Token { get; set; } = string.Empty;
    }

    /// <summary>
    /// Token validation response DTO
    /// </summary>
    public class TokenValidationResponse
    {
        public bool Valid { get; set; }
        public string? UserId { get; set; }
        public string? Error { get; set; }
        public DateTime? ExpiresAt { get; set; }
    }

    /// <summary>
    /// Password reset request DTO
    /// </summary>
    public class PasswordResetRequest
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;
    }

    /// <summary>
    /// Password reset confirmation DTO
    /// </summary>
    public class PasswordResetConfirmRequest
    {
        [Required]
        public string Token { get; set; } = string.Empty;

        [Required]
        [MinLength(8)]
        public string NewPassword { get; set; } = string.Empty;

        [Required]
        [Compare("NewPassword")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }
}
