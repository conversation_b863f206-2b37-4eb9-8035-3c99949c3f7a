# 💻 SoNoBrokers Local Development Setup Guide (Windows 11)

This guide provides step-by-step instructions to run the React frontend and .NET Web API applications locally on Windows 11 without Docker.

## 📋 Prerequisites

- **Windows 11** operating system
- **Node.js 20+** for React development
- **.NET 9 SDK** for Web API development
- **PowerShell** (built-in with Windows 11)
- **Git** for version control
- **Visual Studio Code** (recommended IDE)
- **Windows Terminal** (recommended)

## 🚀 Part 1: System Setup and Installation

### 1.1 Install Node.js 20+

#### Download and Install Node.js
```powershell
# Check if Node.js is already installed
node --version
npm --version

# If not installed, download from: https://nodejs.org/
# Choose LTS version (20.x or higher)
# Verify installation after install
node --version
npm --version
```

#### Configure npm (Optional)
```powershell
# Set npm registry (if needed)
npm config set registry https://registry.npmjs.org/

# Check npm configuration
npm config list

# Update npm to latest version
npm install -g npm@latest
```

### 1.2 Install .NET 9 SDK

#### Download and Install .NET 9 SDK
```powershell
# Check if .NET is already installed
dotnet --version
dotnet --list-sdks

# If not installed, download from: https://dotnet.microsoft.com/download/dotnet/9.0
# Choose SDK (not just Runtime)
# Verify installation after install
dotnet --version
dotnet --info
```

#### Configure .NET (Optional)
```powershell
# Check .NET configuration
dotnet --info

# List available project templates
dotnet new list

# Check global tools
dotnet tool list -g
```

### 1.3 Install Development Tools

#### Install Visual Studio Code
```powershell
# Download from: https://code.visualstudio.com/
# Install recommended extensions:
# - C# Dev Kit
# - JavaScript and TypeScript
# - ES7+ React/Redux/React-Native snippets
# - Prettier - Code formatter
# - GitLens
```

#### Install Windows Terminal (if not already installed)
```powershell
# Install from Microsoft Store or GitHub
# https://github.com/microsoft/terminal
```

## 🛠️ Part 2: Project Setup and Configuration

### 2.1 Clone and Setup Project

#### Navigate to Projects Directory
```powershell
# Create projects directory if it doesn't exist
New-Item -ItemType Directory -Force -Path "C:\Projects"
cd C:\Projects

# If project is not cloned yet
# git clone <repository-url> SoNoBrokersRoot
cd SoNoBrokersRoot
```

#### Verify Project Structure
```powershell
# Check project structure
Get-ChildItem -Name

# Should see:
# - SoNoBrokers (React app)
# - SoNoBrokersWebApi (Web API)
# - Other files and folders
```

### 2.2 Setup React Application

#### Navigate to React Project
```powershell
cd C:\Projects\SoNoBrokersRoot\SoNoBrokers
```

#### Install Dependencies
```powershell
# Install all npm dependencies
npm install

# If you encounter permission issues, try:
# npm install --no-optional
# npm audit fix (if there are vulnerabilities)
```

#### Verify React Configuration
```powershell
# Check package.json
Get-Content package.json | Select-String "name|version|scripts" -A 2

# Check if .env file exists
Test-Path .env

# View environment variables (first 10 lines)
Get-Content .env | Select-Object -First 10
```

### 2.3 Setup .NET Web API

#### Navigate to Web API Project
```powershell
cd C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App
```

#### Restore Dependencies
```powershell
# Restore NuGet packages
dotnet restore

# If you encounter issues, try:
# dotnet clean
# dotnet restore --force
```

#### Verify .NET Configuration
```powershell
# Check project file
Get-Content MicroSaasWebApi.App.csproj | Select-String "TargetFramework|PackageReference" -A 1

# Check if .env file exists
Test-Path .env

# View environment variables (first 10 lines)
Get-Content .env | Select-Object -First 10
```

## 🌐 Part 3: Running Applications Locally

### 3.1 Start .NET Web API (Backend First)

#### Navigate to API Project
```powershell
cd C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App
```

#### Start API Development Server
```powershell
# Start the API server
dotnet run --urls "http://localhost:7163"

# Alternative: Use specific environment
# dotnet run --environment Development --urls "http://localhost:7163"
```

#### Verify API is Running
```powershell
# In a new PowerShell window, test the API
# Test health endpoint
Invoke-RestMethod -Uri "http://localhost:7163/health" -Method Get

# Test properties endpoint
Invoke-RestMethod -Uri "http://localhost:7163/api/sonobrokers/properties" -Method Get

# Or use curl if available
# curl http://localhost:7163/health
# curl http://localhost:7163/api/sonobrokers/properties
```

#### API Access Points
- **API Base URL**: http://localhost:7163
- **Health Check**: http://localhost:7163/health
- **API Documentation**: http://localhost:7163/scalar/v1
- **Properties API**: http://localhost:7163/api/sonobrokers/properties

### 3.2 Start React Application (Frontend Second)

#### Open New PowerShell Window
```powershell
# Open new PowerShell window (keep API running in first window)
# Navigate to React project
cd C:\Projects\SoNoBrokersRoot\SoNoBrokers
```

#### Start React Development Server
```powershell
# Start the React development server
npm run dev

# Alternative commands:
# npm start (if configured)
# npm run develop
```

#### Verify React is Running
```powershell
# React should automatically open browser to http://localhost:3000
# If not, manually open: http://localhost:3000

# Check if React can connect to API
# Open browser developer tools (F12)
# Check console for any API connection errors
```

#### React Access Points
- **Frontend URL**: http://localhost:3000
- **Development Tools**: Available in browser (F12)
- **Hot Reload**: Enabled by default

## 🔧 Part 4: Development Workflow

### 4.1 Daily Development Routine

#### Start Development Environment
```powershell
# Terminal 1: Start API
cd C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App
dotnet run --urls "http://localhost:7163"

# Terminal 2: Start React (after API is running)
cd C:\Projects\SoNoBrokersRoot\SoNoBrokers
npm run dev
```

#### Development Best Practices
1. **Always start API first** (React depends on it)
2. **Keep both terminals open** during development
3. **Monitor logs** in both terminals for errors
4. **Test API endpoints** before testing React features
5. **Use browser dev tools** for React debugging

### 4.2 Code Changes and Hot Reload

#### React Hot Reload
- **Automatic**: Changes to React files trigger automatic reload
- **Manual Refresh**: Press `Ctrl+R` in browser if needed
- **Clear Cache**: `Ctrl+Shift+R` for hard refresh

#### .NET API Hot Reload
```powershell
# .NET 9 supports hot reload
# Changes to C# files should automatically reload
# If not working, restart with:
# Ctrl+C to stop, then dotnet run --urls "http://localhost:7163"
```

### 4.3 Testing Integration

#### Test API Endpoints
```powershell
# Test health check
Invoke-RestMethod -Uri "http://localhost:7163/health"

# Test properties (public endpoint)
Invoke-RestMethod -Uri "http://localhost:7163/api/sonobrokers/properties"

# Test with authentication (if needed)
# $headers = @{ Authorization = "Bearer YOUR_TOKEN" }
# Invoke-RestMethod -Uri "http://localhost:7163/api/sonobrokers/users/profile" -Headers $headers
```

#### Test React Frontend
1. **Open browser**: http://localhost:3000
2. **Check console**: F12 → Console tab
3. **Test navigation**: Click through different pages
4. **Test API calls**: Monitor Network tab in dev tools
5. **Test authentication**: Sign in/out functionality

## 🛑 Part 5: Stopping Applications

### 5.1 Stop Development Servers

#### Stop React Development Server
```powershell
# In the React terminal window
# Press Ctrl+C
# Confirm with 'Y' if prompted
```

#### Stop .NET API Server
```powershell
# In the API terminal window
# Press Ctrl+C
# Server should stop gracefully
```

### 5.2 Clean Up Processes

#### Check for Running Processes
```powershell
# Check if any Node.js processes are still running
Get-Process | Where-Object {$_.ProcessName -like "*node*"}

# Check if any .NET processes are still running
Get-Process | Where-Object {$_.ProcessName -like "*dotnet*"}
```

#### Force Kill Processes (if needed)
```powershell
# Kill Node.js processes (if stuck)
Get-Process | Where-Object {$_.ProcessName -like "*node*"} | Stop-Process -Force

# Kill .NET processes (if stuck)
Get-Process | Where-Object {$_.ProcessName -like "*dotnet*"} | Stop-Process -Force
```

## 🐛 Part 6: Troubleshooting Windows 11 Issues

### 6.1 Node.js and npm Issues

#### Node.js Installation Problems
```powershell
# Check Node.js installation
node --version
npm --version

# If command not found, check PATH
$env:PATH -split ';' | Where-Object {$_ -like "*node*"}

# Reinstall Node.js if needed
# Download from: https://nodejs.org/
# Choose "Add to PATH" during installation
```

#### npm Permission Issues
```powershell
# Run PowerShell as Administrator if needed
# Or configure npm to use different directory
npm config set prefix "C:\Users\<USER>\AppData\Roaming\npm"

# Clear npm cache if issues persist
npm cache clean --force
```

#### npm Install Failures
```powershell
# Delete node_modules and package-lock.json
Remove-Item -Recurse -Force node_modules
Remove-Item package-lock.json

# Reinstall dependencies
npm install

# If still failing, try:
npm install --legacy-peer-deps
npm install --force
```

### 6.2 .NET SDK Issues

#### .NET Installation Problems
```powershell
# Check .NET installation
dotnet --version
dotnet --info

# If command not found, check PATH
$env:PATH -split ';' | Where-Object {$_ -like "*dotnet*"}

# Reinstall .NET SDK if needed
# Download from: https://dotnet.microsoft.com/download
```

#### .NET Restore Issues
```powershell
# Clear NuGet cache
dotnet nuget locals all --clear

# Force restore
dotnet restore --force --no-cache

# If still failing, delete bin and obj folders
Remove-Item -Recurse -Force bin, obj
dotnet restore
```

#### .NET Build Issues
```powershell
# Clean and rebuild
dotnet clean
dotnet build

# Check for specific errors
dotnet build --verbosity detailed
```

### 6.3 Port and Network Issues

#### Port Already in Use
```powershell
# Check what's using port 3000 (React)
netstat -ano | findstr :3000

# Check what's using port 7163 (API)
netstat -ano | findstr :7163

# Kill process using the port (replace PID with actual process ID)
taskkill /PID <PID_NUMBER> /F
```

#### Windows Firewall Issues
```powershell
# Check if Windows Firewall is blocking
# Windows Security → Firewall & network protection
# Allow Node.js and .NET through firewall

# Temporarily disable firewall for testing (not recommended for production)
# netsh advfirewall set allprofiles state off
# netsh advfirewall set allprofiles state on
```

#### Network Connectivity Issues
```powershell
# Test localhost connectivity
Test-NetConnection -ComputerName localhost -Port 3000
Test-NetConnection -ComputerName localhost -Port 7163

# Check if services are listening
netstat -an | findstr :3000
netstat -an | findstr :7163
```

### 6.4 Environment Variable Issues

#### Missing Environment Variables
```powershell
# Check if .env files exist
Test-Path "C:\Projects\SoNoBrokersRoot\SoNoBrokers\.env"
Test-Path "C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App\.env"

# View environment variables
Get-Content "C:\Projects\SoNoBrokersRoot\SoNoBrokers\.env" | Select-Object -First 10
Get-Content "C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App\.env" | Select-Object -First 10
```

#### Database Connection Issues
```powershell
# Test database connectivity (if API fails to start)
# Check DATABASE_URL in .env file
# Verify Supabase connection string is correct
# Check if database is accessible from your network
```

### 6.5 Performance Issues

#### Slow npm install
```powershell
# Use faster npm registry
npm config set registry https://registry.npmjs.org/

# Use npm ci for faster installs (if package-lock.json exists)
npm ci

# Consider using yarn instead
# npm install -g yarn
# yarn install
```

#### Slow .NET restore/build
```powershell
# Use local NuGet cache
dotnet restore --packages "C:\temp\nuget-packages"

# Build in parallel
dotnet build --configuration Release --parallel
```

#### Memory Issues
```powershell
# Check available memory
Get-ComputerInfo | Select-Object TotalPhysicalMemory, AvailablePhysicalMemory

# Close unnecessary applications
# Increase virtual memory if needed
```

## 📊 Part 7: Quick Reference

### 7.1 Essential Commands

#### Daily Development Commands
```powershell
# Start API
cd C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App
dotnet run --urls "http://localhost:7163"

# Start React (in new terminal)
cd C:\Projects\SoNoBrokersRoot\SoNoBrokers
npm run dev

# Test API health
Invoke-RestMethod -Uri "http://localhost:7163/health"
```

#### Troubleshooting Commands
```powershell
# Check running processes
Get-Process | Where-Object {$_.ProcessName -like "*node*"}
Get-Process | Where-Object {$_.ProcessName -like "*dotnet*"}

# Check port usage
netstat -ano | findstr :3000
netstat -ano | findstr :7163

# Kill stuck processes
taskkill /PID <PID_NUMBER> /F
```

### 7.2 Application URLs

| Component | Local URL | Purpose |
|-----------|-----------|---------|
| **React Frontend** | http://localhost:3000 | Main application |
| **.NET API** | http://localhost:7163 | Backend API |
| **API Health** | http://localhost:7163/health | Health check |
| **API Docs** | http://localhost:7163/scalar/v1 | API documentation |
| **Properties API** | http://localhost:7163/api/sonobrokers/properties | Properties endpoint |

### 7.3 File Locations

| Component | Path | Purpose |
|-----------|------|---------|
| **React App** | `C:\Projects\SoNoBrokersRoot\SoNoBrokers` | Frontend source code |
| **Web API** | `C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App` | Backend source code |
| **React .env** | `C:\Projects\SoNoBrokersRoot\SoNoBrokers\.env` | Frontend environment variables |
| **API .env** | `C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi\MicroSaasWebApi.App\.env` | Backend environment variables |

## 🎯 Success Indicators

### Local Development
✅ **Node.js**: Version 20+ installed and accessible
✅ **.NET SDK**: Version 9+ installed and accessible
✅ **React Frontend**: Loads at http://localhost:3000
✅ **.NET API**: Health check returns 200 at http://localhost:7163/health
✅ **Integration**: Frontend can call API endpoints
✅ **Database**: API can connect to Supabase database
✅ **Hot Reload**: Changes reflect automatically in both applications

### Development Environment
✅ **PowerShell**: Can execute commands without errors
✅ **Ports**: 3000 and 7163 are available and not blocked
✅ **Environment Variables**: .env files exist and are properly configured
✅ **Dependencies**: All npm and NuGet packages installed successfully
✅ **Network**: No firewall or connectivity issues

---

**Ready for Local Development on Windows 11! 💻🚀**
