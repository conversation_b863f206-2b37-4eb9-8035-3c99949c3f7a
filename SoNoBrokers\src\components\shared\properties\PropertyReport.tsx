'use client';

import { PropertyReport as PropertyReportType } from '@/lib/ai-services';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    Star,
    MapPin,
    ShoppingBag,
    Utensils,
    Bus,
    Heart,
    Dumbbell,
    Camera,
    Landmark,
    Shield,
    Truck,
    Home,
    Footprints
} from 'lucide-react';

interface PropertyReportProps {
    report: PropertyReportType;
    className?: string;
}

const getReportIcon = (type: string) => {
    const icons = {
        schools: Star,
        shopping: ShoppingBag,
        grocery: ShoppingBag,
        dining: Utensils,
        transit: Bus,
        healthcare: Heart,
        fitness: Dumbbell,
        entertainment: Camera,
        landmarks: Landmark,
        services: Shield,
        movein: Truck,
        neighborhood: Home,
        walkability: Footprints
    };

    const IconComponent = icons[type as keyof typeof icons] || Home;
    return <IconComponent className="h-5 w-5" />;
};

const renderSchoolsData = (data: any) => (
    <div className="space-y-4">
        <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Average Rating</span>
            <Badge variant="secondary">{data.averageRating}/10</Badge>
        </div>
        <div className="space-y-3">
            {data.schools.map((school: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                        <p className="font-medium">{school.name}</p>
                        <p className="text-sm text-muted-foreground">{school.type}</p>
                    </div>
                    <div className="text-right">
                        <p className="font-medium">{school.rating}/10</p>
                        <p className="text-sm text-muted-foreground">{school.distance}</p>
                    </div>
                </div>
            ))}
        </div>
    </div>
);

const renderShoppingData = (data: any) => (
    <div className="space-y-4">
        <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Total Stores</span>
            <Badge variant="secondary">{data.totalStores}</Badge>
        </div>
        <div className="space-y-3">
            {data.locations.map((location: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                        <p className="font-medium">{location.name}</p>
                        <p className="text-sm text-muted-foreground">{location.type}</p>
                    </div>
                    <div className="text-right">
                        <p className="font-medium">{location.stores} stores</p>
                        <p className="text-sm text-muted-foreground">{location.distance}</p>
                    </div>
                </div>
            ))}
        </div>
    </div>
);

const renderGroceryData = (data: any) => (
    <div className="space-y-4">
        <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Average Distance</span>
            <Badge variant="secondary">{data.averageDistance}</Badge>
        </div>
        <div className="space-y-3">
            {data.stores.map((store: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                        <p className="font-medium">{store.name}</p>
                        <p className="text-sm text-muted-foreground">{store.type}</p>
                    </div>
                    <div className="text-right">
                        <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span className="font-medium">{store.rating}</span>
                        </div>
                        <p className="text-sm text-muted-foreground">{store.distance}</p>
                    </div>
                </div>
            ))}
        </div>
    </div>
);

const renderDiningData = (data: any) => (
    <div className="space-y-4">
        <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Average Rating</span>
            <Badge variant="secondary">{data.averageRating}/5</Badge>
        </div>
        <div className="flex flex-wrap gap-2 mb-4">
            {data.cuisineTypes.map((cuisine: string) => (
                <Badge key={cuisine} variant="outline">{cuisine}</Badge>
            ))}
        </div>
        <div className="space-y-3">
            {data.restaurants.map((restaurant: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                        <p className="font-medium">{restaurant.name}</p>
                        <p className="text-sm text-muted-foreground">{restaurant.type} • {restaurant.cuisine}</p>
                    </div>
                    <div className="text-right">
                        <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span className="font-medium">{restaurant.rating}</span>
                        </div>
                        <p className="text-sm text-muted-foreground">{restaurant.distance}</p>
                    </div>
                </div>
            ))}
        </div>
    </div>
);

const renderTransitData = (data: any) => (
    <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 border rounded-lg">
                <p className="text-2xl font-bold text-primary">{data.walkabilityScore}</p>
                <p className="text-sm text-muted-foreground">Walk Score</p>
            </div>
            <div className="text-center p-3 border rounded-lg">
                <p className="text-lg font-medium">{data.commuteToDowntown}</p>
                <p className="text-sm text-muted-foreground">to Downtown</p>
            </div>
        </div>
        <div className="space-y-3">
            {data.options.map((option: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                        <p className="font-medium">{option.name}</p>
                        <p className="text-sm text-muted-foreground">{option.type}</p>
                    </div>
                    <div className="text-right">
                        <p className="text-sm text-muted-foreground">{option.distance}</p>
                        <div className="flex flex-wrap gap-1 justify-end">
                            {(option.routes || option.lines || option.services || []).slice(0, 2).map((service: string) => (
                                <Badge key={service} variant="outline" className="text-xs">{service}</Badge>
                            ))}
                        </div>
                    </div>
                </div>
            ))}
        </div>
    </div>
);

const renderHealthcareData = (data: any) => (
    <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 border rounded-lg">
                <p className="text-lg font-medium">{data.emergencyServices ? 'Yes' : 'No'}</p>
                <p className="text-sm text-muted-foreground">Emergency Services</p>
            </div>
            <div className="text-center p-3 border rounded-lg">
                <p className="text-lg font-medium">{data.specialistAccess}</p>
                <p className="text-sm text-muted-foreground">Specialist Access</p>
            </div>
        </div>
        <div className="space-y-3">
            {data.facilities.map((facility: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                        <p className="font-medium">{facility.name}</p>
                        <p className="text-sm text-muted-foreground">{facility.type}</p>
                    </div>
                    <div className="text-right">
                        <p className="text-sm text-muted-foreground">{facility.distance}</p>
                        <div className="flex flex-wrap gap-1 justify-end">
                            {(facility.specialties || facility.services || []).slice(0, 2).map((service: string) => (
                                <Badge key={service} variant="outline" className="text-xs">{service}</Badge>
                            ))}
                        </div>
                    </div>
                </div>
            ))}
        </div>
    </div>
);

const renderWalkabilityData = (data: any) => (
    <div className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
            <div className="text-center p-3 border rounded-lg">
                <p className="text-2xl font-bold text-primary">{data.walkScore}</p>
                <p className="text-sm text-muted-foreground">Walk Score</p>
            </div>
            <div className="text-center p-3 border rounded-lg">
                <p className="text-2xl font-bold text-primary">{data.bikeScore}</p>
                <p className="text-sm text-muted-foreground">Bike Score</p>
            </div>
            <div className="text-center p-3 border rounded-lg">
                <p className="text-2xl font-bold text-primary">{data.transitScore}</p>
                <p className="text-sm text-muted-foreground">Transit Score</p>
            </div>
        </div>
        <div className="p-3 border rounded-lg">
            <p className="text-sm">{data.dailyErrands}</p>
        </div>
        <div>
            <p className="font-medium mb-2">Popular Walking Routes:</p>
            <div className="flex flex-wrap gap-2">
                {data.walkingRoutes.map((route: string) => (
                    <Badge key={route} variant="outline">{route}</Badge>
                ))}
            </div>
        </div>
    </div>
);

const renderGenericData = (data: any) => (
    <div className="space-y-3">
        {Array.isArray(data.facilities || data.venues || data.landmarks || data.services) &&
            (data.facilities || data.venues || data.landmarks || data.services).map((item: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                        <p className="font-medium">{item.name}</p>
                        <p className="text-sm text-muted-foreground">{item.type}</p>
                    </div>
                    <div className="text-right">
                        {item.rating && (
                            <div className="flex items-center gap-1">
                                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                <span className="font-medium">{item.rating}</span>
                            </div>
                        )}
                        <p className="text-sm text-muted-foreground">{item.distance}</p>
                    </div>
                </div>
            ))
        }
    </div>
);

const renderReportData = (report: PropertyReportType) => {
    const { type, data } = report;

    switch (type) {
        case 'schools':
            return renderSchoolsData(data);
        case 'shopping':
            return renderShoppingData(data);
        case 'grocery':
            return renderGroceryData(data);
        case 'dining':
            return renderDiningData(data);
        case 'transit':
            return renderTransitData(data);
        case 'healthcare':
            return renderHealthcareData(data);
        case 'walkability':
            return renderWalkabilityData(data);
        default:
            return renderGenericData(data);
    }
};

export function PropertyReport({ report, className = '' }: PropertyReportProps) {
    return (
        <Card className={`hover:shadow-lg transition-shadow ${className}`}>
            <CardHeader>
                <CardTitle className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-primary/10 text-primary">
                        {getReportIcon(report.type)}
                    </div>
                    <div className="flex-1">
                        <div className="flex items-center justify-between">
                            <h3 className="text-lg font-semibold">{report.title}</h3>
                            {report.score && (
                                <Badge variant="secondary" className="ml-2">
                                    {report.score}/100
                                </Badge>
                            )}
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">{report.description}</p>
                    </div>
                </CardTitle>
            </CardHeader>
            <CardContent>
                {renderReportData(report)}
                <div className="mt-4 pt-4 border-t">
                    <p className="text-xs text-muted-foreground">
                        Last updated: {new Date(report.lastUpdated).toLocaleDateString()}
                    </p>
                </div>
            </CardContent>
        </Card>
    );
}
