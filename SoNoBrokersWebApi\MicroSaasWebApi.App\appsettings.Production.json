{"AppInfo": {"Environment": "Production"}, "TenantSettings": {"EnableMultiTenantFeatures": true, "TenantCacheExpiry": "01:00:00", "DefaultTenantId": "default"}, "ApiEndpoints": {"DataApi": {"BaseUrl": "https://api.microsaas.com/data-api/v1"}, "DocumentApi": {"BaseUrl": "https://api.microsaas.com/document-api/v1"}, "PermissionApi": {"BaseUrl": "https://api.microsaas.com/permission-api/v1"}}, "FeatureFlags": {"EnableSwagger": false, "EnableHangfire": false, "EnableDetailedLogging": false, "EnablePayments": true, "EnableAnalytics": true, "EnableCustomBranding": true, "EnableMultiRegion": true, "EnableAdvancedSecurity": true}, "Authentication": {"Clerk": {"PublishableKey": "@Microsoft.KeyVault(VaultName=microsaas-kv;SecretName=clerk-publishable-key)", "JwtIssuer": "https://clerk.microsaas.com", "AllowedOrigins": ["https://app.microsaas.com", "https://admin.microsaas.com"]}}, "RateLimiting": {"GlobalRequestsPerMinute": 500, "TenantRequestsPerMinute": 50, "UserRequestsPerMinute": 30}, "Security": {"Cors": {"AllowedOrigins": ["https://app.microsaas.com", "https://admin.microsaas.com"]}, "Headers": {"EnableHsts": true, "HstsMaxAge": 31536000}}, "Monitoring": {"HealthChecks": {"EnableDetailedErrors": false}}, "Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Error", "Microsoft.EntityFrameworkCore": "Error", "Microsoft.AspNetCore.Hosting": "Warning", "System.Net.Http.HttpClient": "Error"}}, "ConnectionStrings": {"DefaultConnection": "@Microsoft.KeyVault(VaultName=microsaas-kv;SecretName=default-connection-string)"}, "KeyVault": {"Url": "https://microsaas-kv.vault.azure.net/", "EnableKeyVault": true}}