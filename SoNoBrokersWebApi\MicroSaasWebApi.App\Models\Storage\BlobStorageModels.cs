using System.ComponentModel.DataAnnotations;

namespace MicroSaasWebApi.Models.Storage
{
    /// <summary>
    /// Azure Blob Storage file model
    /// </summary>
    public class BlobStorageFile
    {
        public string Name { get; set; } = string.Empty;
        public string ContainerName { get; set; } = string.Empty;
        public long Size { get; set; }
        public string? ContentType { get; set; }
        public DateTimeOffset? CreatedOn { get; set; }
        public DateTimeOffset? LastModified { get; set; }
        public string? ETag { get; set; }
        public string? Url { get; set; }
        public IDictionary<string, string>? Metadata { get; set; }
    }

    /// <summary>
    /// Azure Blob Storage file with content
    /// </summary>
    public class BlobStorageFileContent
    {
        public BlobStorageFile File { get; set; } = new();
        public Stream Content { get; set; } = Stream.Null;
    }

    /// <summary>
    /// Blob storage upload request
    /// </summary>
    public class BlobStorageUploadRequest
    {
        [Required]
        public string ContainerName { get; set; } = string.Empty;

        [Required]
        public string FileName { get; set; } = string.Empty;

        [Required]
        public IFormFile File { get; set; } = null!;

        public string? ContentType { get; set; }
        public Dictionary<string, string>? Metadata { get; set; }
    }

    /// <summary>
    /// Blob storage list request
    /// </summary>
    public class BlobStorageListRequest
    {
        [Required]
        public string ContainerName { get; set; } = string.Empty;

        public string? Prefix { get; set; }
        public int MaxResults { get; set; } = 100;
    }

    /// <summary>
    /// Blob storage update request
    /// </summary>
    public class BlobStorageUpdateRequest
    {
        [Required]
        public string ContainerName { get; set; } = string.Empty;

        [Required]
        public string FileName { get; set; } = string.Empty;

        public IFormFile? NewContent { get; set; }
        public string? NewContentType { get; set; }
        public Dictionary<string, string>? NewMetadata { get; set; }
    }

    /// <summary>
    /// Blob storage SAS URL request
    /// </summary>
    public class BlobStorageSasRequest
    {
        [Required]
        public string ContainerName { get; set; } = string.Empty;

        [Required]
        public string FileName { get; set; } = string.Empty;

        public int ExpiryHours { get; set; } = 1;
        public string Permissions { get; set; } = "r"; // r=read, w=write, d=delete, l=list
    }

    /// <summary>
    /// Blob storage copy request
    /// </summary>
    public class BlobStorageCopyRequest
    {
        [Required]
        public string SourceContainerName { get; set; } = string.Empty;

        [Required]
        public string SourceFileName { get; set; } = string.Empty;

        [Required]
        public string DestinationContainerName { get; set; } = string.Empty;

        [Required]
        public string DestinationFileName { get; set; } = string.Empty;
    }
}
