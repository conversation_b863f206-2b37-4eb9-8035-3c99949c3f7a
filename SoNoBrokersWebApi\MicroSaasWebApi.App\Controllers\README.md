# Controllers Architecture Documentation

## Overview
This document outlines the controller architecture for the MicroSaasWebApi application, specifically focusing on the SoNoBrokers implementation and Core Stripe payment functionality.

## Architecture Structure

```
Controllers/
├── Core/
│   ├── Clerk/
│   │   └── ClerkAuthController.cs         # Clerk authentication
│   └── Stripe/
│       ├── StripePaymentsController.cs    # One-time + Subscription payments
│       └── StripeWebhookController.cs     # Webhook handling for both payment types
└── SoNoBrokers/
    ├── BuyerListingsController.cs         # ✅ DapperDbContext - Buyer listing management
    ├── GenerateDescriptionController.cs   # ✅ DapperDbContext - AI description generation
    ├── PropertiesController.cs            # ✅ DapperDbContext - Property CRUD operations
    ├── PropertyImagesController.cs        # ✅ DapperDbContext - Property image management
    ├── SearchFiltersController.cs         # ✅ DapperDbContext - Search filter management
    ├── SimplePropertiesController.cs      # ✅ DapperDbContext - Simplified property operations
    ├── SimpleUsersController.cs           # ✅ DapperDbContext - Simplified user operations
    ├── UploadController.cs                # ✅ DapperDbContext - File upload handling
    ├── UsersController.cs                 # ✅ DapperDbContext - User management
    └── WaitingListController.cs           # ✅ DapperDbContext - Waiting list management
```

## Core Controllers

### Core/Stripe/StripePaymentsController.cs
**Route:** `/api/stripe/payments`
**Purpose:** Comprehensive Stripe payment handling for both one-time payments and subscriptions

#### Key Features:
- **Customer Management:** Create, update, delete, and retrieve Stripe customers
- **Payment Intents:** Handle one-time payments with confirmation
- **Subscriptions:** Create and cancel subscription-based payments
- **SoNoBrokers Integration:** Specialized endpoints for SoNoBrokers checkout and portal
- **Authentication:** Requires authorization for all payment operations

#### Key Endpoints:
- `POST /customers` - Create new Stripe customer
- `GET /customers/{customerId}` - Retrieve customer details
- `POST /payment-intents` - Create one-time payment intent
- `POST /subscriptions` - Create subscription
- `POST /sonobrokers/create-checkout` - SoNoBrokers checkout session
- `POST /sonobrokers/create-portal` - Customer portal access
- `GET /sonobrokers/subscription-status` - Check subscription status

#### Database Integration:
- Uses **DapperDbContext** for Supabase database operations
- Queries `snb.subscriptions` table for subscription management
- Supports stored procedures for complex operations

### Core/Stripe/StripeWebhookController.cs
**Route:** `/api/stripe/webhooks`
**Purpose:** Handle Stripe webhook events for payment and subscription lifecycle management

#### Supported Webhook Events:
- `checkout.session.completed` - Process successful checkout sessions
- `customer.subscription.updated` - Handle subscription changes
- `customer.subscription.deleted` - Process subscription cancellations
- `invoice.paid` - Update subscription status on successful payment
- `invoice.payment_failed` - Handle failed payments
- `payment_intent.succeeded` - Process successful one-time payments
- `payment_intent.payment_failed` - Handle failed one-time payments

#### Database Operations:
- **Subscription Management:** Creates/updates records in `snb.subscriptions`
- **Payment Tracking:** Records one-time payments in `snb.payments`
- **Status Synchronization:** Keeps local database in sync with Stripe
- **Transaction Safety:** Uses proper error handling and logging

## SoNoBrokers Controllers

All SoNoBrokers controllers have been successfully migrated from Entity Framework to **DapperDbContext** with Supabase integration.

### Migration Status: ✅ COMPLETE
- **Database Layer:** All controllers use DapperDbContext
- **Schema:** Connected to Supabase `snb` schema
- **Performance:** Raw SQL queries for optimal performance
- **Transactions:** Support for complex database operations
- **Stored Procedures:** Ready for advanced database operations

### SoNoBrokers/UsersController.cs
**Route:** `/api/sonobrokers/users`
**Purpose:** Complete user management for SoNoBrokers platform

#### Key Features:
- User CRUD operations with Clerk integration
- Profile management and updates
- User search and filtering
- Role-based access control

#### Database Tables:
- `snb.users` - Primary user data
- Integration with Clerk auth schema

### SoNoBrokers/PropertiesController.cs
**Route:** `/api/sonobrokers/properties`
**Purpose:** Property listing management

#### Key Features:
- Property CRUD operations
- Advanced search and filtering
- Property status management
- Owner/agent assignment

#### Database Tables:
- `snb.properties` - Property listings
- `snb.property_images` - Associated images
- `snb.search_filters` - Search configurations

### SoNoBrokers/PropertyImagesController.cs
**Route:** `/api/sonobrokers/property-images`
**Purpose:** Property image management with Azure Blob Storage integration

#### Key Features:
- Image upload and storage
- Image metadata management
- Bulk image operations
- Image optimization and resizing

#### Database Tables:
- `snb.property_images` - Image metadata and URLs

### SoNoBrokers/SearchFiltersController.cs
**Route:** `/api/sonobrokers/search-filters`
**Purpose:** Advanced search filter management

#### Key Features:
- Custom search filter creation
- Filter persistence and sharing
- Complex query building
- Performance optimization

#### Database Tables:
- `snb.search_filters` - Saved search configurations

### SoNoBrokers/BuyerListingsController.cs
**Route:** `/api/sonobrokers/buyer-listings`
**Purpose:** Buyer-specific property listings and preferences

#### Key Features:
- Buyer preference management
- Personalized property recommendations
- Saved searches and alerts
- Matching algorithms

### SoNoBrokers/WaitingListController.cs
**Route:** `/api/sonobrokers/waiting-list`
**Purpose:** Property waiting list management

#### Key Features:
- Waiting list registration
- Priority management
- Notification system
- Availability tracking

## Database Architecture

### DapperDbContext Integration
All SoNoBrokers controllers use the **DapperDbContext** for database operations:

```csharp
// Example usage in controllers
private readonly DapperDbContext _dbContext;

// Raw SQL queries for performance
const string sql = @"
    SELECT * FROM snb.users 
    WHERE id = @userId";
var user = await _dbContext.QueryFirstOrDefaultAsync<User>(sql, new { userId });

// Stored procedure support
await _dbContext.ExecuteStoredProcedureAsync("snb.update_user_profile", parameters);
```

### Supabase Schema Integration
- **Primary Schema:** `snb` (SoNoBrokers)
- **Auth Schema:** Clerk-managed authentication tables
- **Performance:** Optimized queries with proper indexing
- **Scalability:** Ready for horizontal scaling

## Security & Authentication

### Authorization Patterns
- **Clerk Integration:** All controllers support Clerk authentication
- **Role-Based Access:** Different permission levels for users/agents/admins
- **API Security:** Proper input validation and sanitization
- **Rate Limiting:** Built-in protection against abuse

### Data Protection
- **Input Validation:** Comprehensive model validation
- **SQL Injection Prevention:** Parameterized queries only
- **CORS Configuration:** Proper cross-origin resource sharing
- **Audit Logging:** Complete operation tracking

## Performance Considerations

### Database Optimization
- **Raw SQL:** Direct queries for maximum performance
- **Connection Pooling:** Efficient database connection management
- **Caching Strategy:** Ready for Redis integration
- **Pagination:** Efficient large dataset handling

### API Performance
- **Async Operations:** All database calls are asynchronous
- **Response Compression:** Optimized payload sizes
- **Error Handling:** Graceful degradation and recovery
- **Monitoring:** Comprehensive logging and metrics

## Development Guidelines

### Adding New Controllers
1. Follow the established namespace pattern: `MicroSaasWebApi.Controllers.SoNoBrokers`
2. Use DapperDbContext for all database operations
3. Implement proper error handling and logging
4. Add comprehensive API documentation with Swagger
5. Include unit tests for all endpoints

### Database Operations
1. Use parameterized queries to prevent SQL injection
2. Implement proper transaction handling for complex operations
3. Add appropriate indexes for query performance
4. Use stored procedures for complex business logic

### API Design
1. Follow RESTful conventions
2. Use appropriate HTTP status codes
3. Implement consistent error response format
4. Add proper input validation
5. Include comprehensive API documentation

## Future Enhancements

### Planned Improvements
- **GraphQL Integration:** For complex data fetching scenarios
- **Real-time Updates:** SignalR integration for live notifications
- **Advanced Caching:** Redis implementation for performance
- **Microservices:** Potential service decomposition for scalability

### Monitoring & Observability
- **Application Insights:** Comprehensive telemetry
- **Health Checks:** Endpoint monitoring and alerting
- **Performance Metrics:** Database and API performance tracking
- **Error Tracking:** Centralized error logging and analysis
