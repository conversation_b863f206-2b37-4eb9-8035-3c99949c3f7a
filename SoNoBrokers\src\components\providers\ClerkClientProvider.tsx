'use client';

import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import { ReactNode, useEffect, useState } from 'react';

interface ClerkClientProviderProps {
  children: ReactNode;
}

export function ClerkClientProvider({ children }: ClerkClientProviderProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render Clerk<PERSON><PERSON>ider during SSR
  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <ClerkProvider
      appearance={{
        baseTheme: undefined,
        elements: {
          formButtonPrimary: 'bg-primary text-primary-foreground hover:bg-primary/90',
          footerActionLink: 'text-primary hover:text-primary/90',
        },
      }}
      signInUrl="/sign-in"
      signUpUrl="/sign-up"
      afterSignInUrl="/dashboard"
      afterSignUpUrl="/dashboard"
    >
      {children}
    </ClerkProvider>
  );
}
