using MicroSaasWebApi.Services.Configuration;

namespace MicroSaasWebApi.Models
{
    /// <summary>
    /// Simplified application settings that loads from environment variables
    /// </summary>
    public class AppSettings
    {
        // Application Properties
        public string AppName => EnvironmentConfigurationService.GetEnvironmentVariable("APP_NAME", "MicroSaasWebApi");
        public string Version => EnvironmentConfigurationService.GetEnvironmentVariable("APP_VERSION", "1.0.0");
        public string Environment => EnvironmentConfigurationService.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "Development");
        public string AllowedHosts => EnvironmentConfigurationService.GetEnvironmentVariable("ALLOWED_HOSTS", "*");

        // Database Configuration
        public string DatabaseUrl => EnvironmentConfigurationService.GetEnvironmentVariable("DATABASE_URL", "");
        public string PostgresUser => EnvironmentConfigurationService.GetEnvironmentVariable("POSTGRES_USER", "postgres");
        public string PostgresPassword => EnvironmentConfigurationService.GetEnvironmentVariable("POSTGRES_PASSWORD", "");
        public string PostgresDbName => EnvironmentConfigurationService.GetEnvironmentVariable("POSTGRES_DBNAME", "postgres");
        public string DatabaseHost => EnvironmentConfigurationService.GetEnvironmentVariable("DATABASE_HOST", "localhost");
        public string DatabasePort => EnvironmentConfigurationService.GetEnvironmentVariable("DATABASE_PORT", "5432");

        // Clerk Authentication
        public string ClerkPublishableKey => EnvironmentConfigurationService.GetEnvironmentVariable("NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY", "");
        public string ClerkSecretKey => EnvironmentConfigurationService.GetEnvironmentVariable("CLERK_SECRET_KEY", "");
        public string ClerkSignInUrl => EnvironmentConfigurationService.GetEnvironmentVariable("NEXT_PUBLIC_CLERK_SIGN_IN_URL", "/sign-in");
        public string ClerkSignUpUrl => EnvironmentConfigurationService.GetEnvironmentVariable("NEXT_PUBLIC_CLERK_SIGN_UP_URL", "/sign-up");

        // Stripe Payment Configuration
        public string StripePublishableKey => EnvironmentConfigurationService.GetEnvironmentVariable("NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY", "");
        public string StripeSecretKey => EnvironmentConfigurationService.GetEnvironmentVariable("STRIPE_SECRET_KEY", "");
        public string StripeWebhookSecret => EnvironmentConfigurationService.GetEnvironmentVariable("STRIPE_WEBHOOK_SECRET", "");

        // Application URLs
        public string AppUrl => EnvironmentConfigurationService.GetEnvironmentVariable("NEXT_PUBLIC_APP_URL", "http://localhost:3000");
        public string ApiUrl => EnvironmentConfigurationService.GetEnvironmentVariable("API_URL", "http://localhost:5000");

        // External Services
        public string ResendApiKey => EnvironmentConfigurationService.GetEnvironmentVariable("RESEND_API_KEY", "");

        // Make.com Integration
        public string MakeOrganizationId => EnvironmentConfigurationService.GetEnvironmentVariable("MAKE_ORGANIZATION_ID", "");
        public string MakeTeamId => EnvironmentConfigurationService.GetEnvironmentVariable("MAKE_TEAM_ID", "");
        public string MakeApiKey => EnvironmentConfigurationService.GetEnvironmentVariable("MAKE_API_KEY", "");
        public string MakeApiUrl => EnvironmentConfigurationService.GetEnvironmentVariable("MAKE_API_URL", "");

        // N8N Integration
        public string N8NApiKey => EnvironmentConfigurationService.GetEnvironmentVariable("N8N_API_KEY", "");
        public string N8NApiUrl => EnvironmentConfigurationService.GetEnvironmentVariable("N8N_API_URL", "");
        public string N8NWebhookUrl => EnvironmentConfigurationService.GetEnvironmentVariable("N8N_WEBHOOK_URL", "");

        // WordPress Integration
        public string WordPressDbUser => EnvironmentConfigurationService.GetEnvironmentVariable("WORDPRESS_DB_USER", "");
        public string WordPressDbPassword => EnvironmentConfigurationService.GetEnvironmentVariable("WORDPRESS_DB_PASSWORD", "");
        public string WordPressDbName => EnvironmentConfigurationService.GetEnvironmentVariable("WORDPRESS_DB_NAME", "");
        public string WordPressRestEndpoint => EnvironmentConfigurationService.GetEnvironmentVariable("WP_REST_ENDPOINT", "");

        // MySQL Configuration (for WordPress)
        public string MySqlDatabase => EnvironmentConfigurationService.GetEnvironmentVariable("MYSQL_DATABASE", "");
        public string MySqlUser => EnvironmentConfigurationService.GetEnvironmentVariable("MYSQL_USER", "");
        public string MySqlPassword => EnvironmentConfigurationService.GetEnvironmentVariable("MYSQL_PASSWORD", "");
        public string MySqlRandomRootPassword => EnvironmentConfigurationService.GetEnvironmentVariable("MYSQL_RANDOM_ROOT_PASSWORD", "");

        // Azure Configuration (Optional)
        public string AzureTenantId => EnvironmentConfigurationService.GetEnvironmentVariable("AZURE_TENANT_ID", "");
        public string AzureClientId => EnvironmentConfigurationService.GetEnvironmentVariable("AZURE_CLIENT_ID", "");
        public string AzureClientSecret => EnvironmentConfigurationService.GetEnvironmentVariable("AZURE_CLIENT_SECRET", "");
        public string KeyVaultUri => EnvironmentConfigurationService.GetEnvironmentVariable("KEYVAULT_URI", "");
        public string AppConfigurationEndpoint => EnvironmentConfigurationService.GetEnvironmentVariable("APPCONFIGURATION_ENDPOINT", "");
        public string AppConfigurationConnectionString => EnvironmentConfigurationService.GetEnvironmentVariable("APPCONFIGURATION_CONNECTIONSTRING", "");
        public string AppConfigurationEnvironmentLabel => EnvironmentConfigurationService.GetEnvironmentVariable("APPCONFIGURATION_ENVIRONMENT_LABEL", "");

        // Logging Configuration
        public LoggingSettings Logging { get; set; } = new LoggingSettings();

        // Validation method to check required settings
        public List<string> ValidateRequiredSettings()
        {
            var missingSettings = new List<string>();

            // Check required database settings
            if (string.IsNullOrEmpty(DatabaseUrl))
                missingSettings.Add("DATABASE_URL");

            // Check required Clerk settings
            if (string.IsNullOrEmpty(ClerkSecretKey))
                missingSettings.Add("CLERK_SECRET_KEY");

            if (string.IsNullOrEmpty(ClerkPublishableKey))
                missingSettings.Add("NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY");

            // Check required Stripe settings (if payment features are enabled)
            if (string.IsNullOrEmpty(StripeSecretKey))
                missingSettings.Add("STRIPE_SECRET_KEY");

            return missingSettings;
        }

        // Helper method to get connection string
        public string GetConnectionString()
        {
            if (!string.IsNullOrEmpty(DatabaseUrl))
                return DatabaseUrl;

            return $"Host={DatabaseHost};Port={DatabasePort};Database={PostgresDbName};Username={PostgresUser};Password={PostgresPassword}";
        }
    }

    /// <summary>
    /// Logging configuration settings
    /// </summary>
    public class LoggingSettings
    {
        public string DefaultLevel => EnvironmentConfigurationService.GetEnvironmentVariable("LOG_LEVEL", "Information");
        public string MicrosoftLevel => EnvironmentConfigurationService.GetEnvironmentVariable("LOG_LEVEL_MICROSOFT", "Warning");
        public string SystemLevel => EnvironmentConfigurationService.GetEnvironmentVariable("LOG_LEVEL_SYSTEM", "Information");
        public string AspNetCoreLevel => EnvironmentConfigurationService.GetEnvironmentVariable("LOG_LEVEL_ASPNETCORE", "Warning");
        public string EntityFrameworkLevel => EnvironmentConfigurationService.GetEnvironmentVariable("LOG_LEVEL_EF", "Warning");

        public bool EnableFileLogging => bool.TryParse(EnvironmentConfigurationService.GetEnvironmentVariable("ENABLE_FILE_LOGGING", "true"), out var result) && result;
        public bool EnableConsoleLogging => bool.TryParse(EnvironmentConfigurationService.GetEnvironmentVariable("ENABLE_CONSOLE_LOGGING", "true"), out var result2) && result2;
        public string LogFilePath => EnvironmentConfigurationService.GetEnvironmentVariable("LOG_FILE_PATH", "logs/microsaas-.txt");
        public int MaxLogFileSizeMB => int.TryParse(EnvironmentConfigurationService.GetEnvironmentVariable("MAX_LOG_FILE_SIZE_MB", "10"), out var size) ? size : 10;
        public int RetainedFileCountLimit => int.TryParse(EnvironmentConfigurationService.GetEnvironmentVariable("RETAINED_FILE_COUNT_LIMIT", "31"), out var count) ? count : 31;
    }

    /// <summary>
    /// Rate limiting configuration
    /// </summary>
    public class RateLimitSettings
    {
        public bool EnableRateLimiting => bool.TryParse(EnvironmentConfigurationService.GetEnvironmentVariable("ENABLE_RATE_LIMITING", "true"), out var result) && result;
        public int GeneralRules_Period => int.TryParse(EnvironmentConfigurationService.GetEnvironmentVariable("RATE_LIMIT_PERIOD_SECONDS", "60"), out var period) ? period : 60;
        public int GeneralRules_Limit => int.TryParse(EnvironmentConfigurationService.GetEnvironmentVariable("RATE_LIMIT_REQUESTS", "100"), out var limit) ? limit : 100;
        public int AuthRules_Period => int.TryParse(EnvironmentConfigurationService.GetEnvironmentVariable("AUTH_RATE_LIMIT_PERIOD_SECONDS", "300"), out var authPeriod) ? authPeriod : 300;
        public int AuthRules_Limit => int.TryParse(EnvironmentConfigurationService.GetEnvironmentVariable("AUTH_RATE_LIMIT_REQUESTS", "5"), out var authLimit) ? authLimit : 5;
    }

    /// <summary>
    /// CORS configuration
    /// </summary>
    public class CorsSettings
    {
        public string AllowedOrigins => EnvironmentConfigurationService.GetEnvironmentVariable("CORS_ALLOWED_ORIGINS", "http://localhost:3000,https://localhost:3001");
        public string AllowedMethods => EnvironmentConfigurationService.GetEnvironmentVariable("CORS_ALLOWED_METHODS", "GET,POST,PUT,DELETE,OPTIONS");
        public string AllowedHeaders => EnvironmentConfigurationService.GetEnvironmentVariable("CORS_ALLOWED_HEADERS", "Content-Type,Authorization,X-Requested-With");
        public bool AllowCredentials => bool.TryParse(EnvironmentConfigurationService.GetEnvironmentVariable("CORS_ALLOW_CREDENTIALS", "true"), out var result) && result;
    }
}
