using Dapper;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;

namespace MicroSaasWebApi.Services.SoNoBrokers
{
    public class AdminService : IAdminService
    {
        private readonly MicroSaasWebApi.App.Context.IDapperDbContext _dbContext;
        private readonly ILogger<AdminService> _logger;

        public AdminService(MicroSaasWebApi.App.Context.IDapperDbContext dbContext, ILogger<AdminService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<AdminDashboardResponse> GetDashboardDataAsync()
        {
            try
            {
                _logger.LogInformation("Fetching admin dashboard data");

                // Get user statistics
                var userStatsQuery = @"
                    SELECT 
                        COUNT(*) as total_users,
                        COUNT(CASE WHEN ""isActive"" = true THEN 1 END) as active_users,
                        COUNT(CASE WHEN role = 'ADMIN' THEN 1 END) as admin_users,
                        COUNT(CASE WHEN role = 'USER' THEN 1 END) as regular_users,
                        COUNT(CASE WHEN role = 'PRODUCT' THEN 1 END) as product_users,
                        COUNT(CASE WHEN role = 'OPERATOR' THEN 1 END) as operator_users,
                        COUNT(CASE WHEN ""userType"" = 'Buyer' THEN 1 END) as buyers,
                        COUNT(CASE WHEN ""userType"" = 'Seller' THEN 1 END) as sellers
                    FROM public.""User""";

                var userStats = await _dbContext.QueryFirstOrDefaultAsync<dynamic>(userStatsQuery);

                // Get recent users (last 30 days)
                var recentUsersQuery = @"
                    SELECT COUNT(*) as recent_users
                    FROM public.""User""
                    WHERE ""createdAt"" >= NOW() - INTERVAL '30 days'";

                var recentUsers = await _dbContext.QueryFirstOrDefaultAsync<dynamic>(recentUsersQuery);

                // Get login statistics (last 30 days)
                var loginStatsQuery = @"
                    SELECT COUNT(*) as recent_logins
                    FROM public.""User""
                    WHERE ""lastLoginAt"" >= NOW() - INTERVAL '30 days'";

                var loginStats = await _dbContext.QueryFirstOrDefaultAsync<dynamic>(loginStatsQuery);

                // Get subscription statistics
                var subscriptionStatsQuery = @"
                    SELECT status, COUNT(*) as count
                    FROM public.""SubscriptionSnb""
                    GROUP BY status";

                var subscriptionStats = await _dbContext.QueryAsync<dynamic>(subscriptionStatsQuery);

                var subscriptionDict = subscriptionStats.ToDictionary(
                    s => (string)s.status,
                    s => (int)s.count
                );

                var dashboardData = new AdminDashboardData
                {
                    Users = new UserStatistics
                    {
                        Total = (int)userStats.total_users,
                        Active = (int)userStats.active_users,
                        Recent = (int)recentUsers.recent_users,
                        ByRole = new UserRoleStatistics
                        {
                            Admin = (int)userStats.admin_users,
                            User = (int)userStats.regular_users,
                            Product = (int)userStats.product_users,
                            Operator = (int)userStats.operator_users
                        },
                        ByType = new UserTypeStatistics
                        {
                            Buyers = (int)userStats.buyers,
                            Sellers = (int)userStats.sellers,
                            Operators = 0 // Not in current schema
                        }
                    },
                    Activity = new ActivityStatistics
                    {
                        RecentLogins = (int)loginStats.recent_logins
                    },
                    Subscriptions = subscriptionDict
                };

                return new AdminDashboardResponse
                {
                    Success = true,
                    Data = dashboardData
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching admin dashboard data");
                return new AdminDashboardResponse
                {
                    Success = false,
                    Error = "Failed to fetch dashboard data"
                };
            }
        }

        public async Task<List<AdminUserResponse>> GetUsersAsync()
        {
            try
            {
                const string sql = @"
                    SELECT 
                        id, email, ""fullName"", ""firstName"", ""lastName"", phone,
                        role, ""userType"", ""isActive"", ""loggedIn"", ""lastLoginAt"",
                        ""createdAt"", ""updatedAt""
                    FROM public.""User""
                    ORDER BY ""createdAt"" DESC";

                var users = await _dbContext.QueryAsync<dynamic>(sql);

                return users.Select(u => new AdminUserResponse
                {
                    Id = (string)u.id,
                    Email = (string)u.email,
                    FullName = (string)u.fullName,
                    FirstName = u.firstName,
                    LastName = u.lastName,
                    Phone = u.phone,
                    Role = Enum.Parse<UserRole>((string)u.role),
                    UserType = Enum.Parse<SnbUserType>((string)u.userType),
                    IsActive = (bool)u.isActive,
                    LoggedIn = (bool)u.loggedIn,
                    LastLoginAt = u.lastLoginAt,
                    CreatedAt = (DateTime)u.createdAt,
                    UpdatedAt = (DateTime)u.updatedAt
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users");
                throw;
            }
        }

        public async Task<AdminUserResponse?> GetUserByIdAsync(string id)
        {
            try
            {
                const string sql = @"
                    SELECT 
                        id, email, ""fullName"", ""firstName"", ""lastName"", phone,
                        role, ""userType"", ""isActive"", ""loggedIn"", ""lastLoginAt"",
                        ""createdAt"", ""updatedAt""
                    FROM public.""User""
                    WHERE id = @id";

                var user = await _dbContext.QueryFirstOrDefaultAsync<dynamic>(sql, new { id });

                if (user == null) return null;

                return new AdminUserResponse
                {
                    Id = (string)user.id,
                    Email = (string)user.email,
                    FullName = (string)user.fullName,
                    FirstName = user.firstName,
                    LastName = user.lastName,
                    Phone = user.phone,
                    Role = Enum.Parse<UserRole>((string)user.role),
                    UserType = Enum.Parse<SnbUserType>((string)user.userType),
                    IsActive = (bool)user.isActive,
                    LoggedIn = (bool)user.loggedIn,
                    LastLoginAt = user.lastLoginAt,
                    CreatedAt = (DateTime)user.createdAt,
                    UpdatedAt = (DateTime)user.updatedAt
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by ID: {Id}", id);
                throw;
            }
        }

        public async Task<bool> UpdateUserRoleAsync(UpdateUserRoleRequest request)
        {
            try
            {
                const string sql = @"
                    UPDATE public.""User""
                    SET role = @role, ""updatedAt"" = @updatedAt
                    WHERE id = @userId";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, new
                {
                    userId = request.UserId,
                    role = request.Role.ToString(),
                    updatedAt = DateTime.UtcNow
                });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user role: {UserId}", request.UserId);
                throw;
            }
        }

        public async Task<bool> UpdateUserStatusAsync(UpdateUserStatusRequest request)
        {
            try
            {
                const string sql = @"
                    UPDATE public.""User""
                    SET ""isActive"" = @isActive, ""updatedAt"" = @updatedAt
                    WHERE id = @userId";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, new
                {
                    userId = request.UserId,
                    isActive = request.IsActive,
                    updatedAt = DateTime.UtcNow
                });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user status: {UserId}", request.UserId);
                throw;
            }
        }

        public async Task<List<RolePermissionResponse>> GetRolePermissionsAsync()
        {
            try
            {
                const string sql = @"
                    SELECT id, role, permission, resource, ""createdAt""
                    FROM public.""RolePermission""
                    ORDER BY role, permission";

                var permissions = await _dbContext.QueryAsync<dynamic>(sql);

                return permissions.Select(p => new RolePermissionResponse
                {
                    Id = (string)p.id,
                    Role = Enum.Parse<UserRole>((string)p.role),
                    Permission = (string)p.permission,
                    Resource = p.resource,
                    CreatedAt = (DateTime)p.createdAt
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting role permissions");
                throw;
            }
        }

        public async Task<RolePermissionResponse> CreateRolePermissionAsync(CreateRolePermissionRequest request)
        {
            try
            {
                var id = Guid.NewGuid().ToString();
                var now = DateTime.UtcNow;

                const string sql = @"
                    INSERT INTO public.""RolePermission"" (id, role, permission, resource, ""createdAt"")
                    VALUES (@id, @role, @permission, @resource, @createdAt)";

                await _dbContext.ExecuteAsync(sql, new
                {
                    id,
                    role = request.Role.ToString(),
                    permission = request.Permission,
                    resource = request.Resource,
                    createdAt = now
                });

                return new RolePermissionResponse
                {
                    Id = id,
                    Role = request.Role,
                    Permission = request.Permission,
                    Resource = request.Resource,
                    CreatedAt = now
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating role permission");
                throw;
            }
        }

        public async Task<bool> UpdateRolePermissionAsync(UpdateRolePermissionRequest request)
        {
            try
            {
                var updates = new List<string>();
                var parameters = new DynamicParameters();
                parameters.Add("id", request.Id);

                if (request.Role.HasValue)
                {
                    updates.Add("role = @role");
                    parameters.Add("role", request.Role.Value.ToString());
                }

                if (!string.IsNullOrEmpty(request.Permission))
                {
                    updates.Add("permission = @permission");
                    parameters.Add("permission", request.Permission);
                }

                if (request.Resource != null)
                {
                    updates.Add("resource = @resource");
                    parameters.Add("resource", request.Resource);
                }

                if (!updates.Any()) return false;

                var sql = $@"
                    UPDATE public.""RolePermission""
                    SET {string.Join(", ", updates)}
                    WHERE id = @id";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating role permission: {Id}", request.Id);
                throw;
            }
        }

        public async Task<bool> DeleteRolePermissionAsync(string id)
        {
            try
            {
                const string sql = @"DELETE FROM public.""RolePermission"" WHERE id = @id";
                var rowsAffected = await _dbContext.ExecuteAsync(sql, new { id });
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting role permission: {Id}", id);
                throw;
            }
        }

        public async Task<StripeSyncResponse> SyncStripeDataAsync(StripeSyncRequest request)
        {
            try
            {
                _logger.LogInformation("Starting Stripe data sync. Force: {Force}, Customer: {Customer}",
                    request.ForceSync, request.CustomerId);

                // TODO: Implement actual Stripe sync logic
                // This would involve:
                // 1. Fetching data from Stripe API
                // 2. Comparing with local database
                // 3. Updating/creating records as needed
                // 4. Handling errors and conflicts

                // For now, return a mock response
                await Task.Delay(1000);

                return new StripeSyncResponse
                {
                    Success = true,
                    Message = "Stripe data sync completed successfully",
                    SyncedCustomers = 10,
                    SyncedSubscriptions = 5,
                    SyncedPayments = 15,
                    Errors = Array.Empty<string>()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing Stripe data");
                return new StripeSyncResponse
                {
                    Success = false,
                    Message = "Failed to sync Stripe data",
                    Errors = new[] { ex.Message }
                };
            }
        }
    }
}
