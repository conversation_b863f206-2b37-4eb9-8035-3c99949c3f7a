<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MicroSaasWebApi.App</name>
    </assembly>
    <members>
        <member name="T:MicroSaasWebApi.App.Context.DapperDbContext">
            <summary>
            Dapper database context for SoNoBrokers operations
            Consolidated implementation using only DATABASE_URL configuration
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.App.Context.DapperDbContext.Connection">
            <summary>
            Get database connection
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.ConvertSupabaseUriToConnectionString(System.String)">
            <summary>
            Convert Supabase URI to connection string format
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.GetConnection">
            <summary>
            Get the current database connection (implements IDapperDbContext)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.CreateConnection">
            <summary>
            Create a new database connection (for services that need their own connection)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.QueryAsync``1(System.String,System.Object)">
            <summary>
            Execute a query and return results
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.QuerySingleOrDefaultAsync``1(System.String,System.Object)">
            <summary>
            Execute a query and return a single result
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.QueryFirstOrDefaultAsync``1(System.String,System.Object)">
            <summary>
            Execute a query and return first result or default
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.QueryFirstAsync``1(System.String,System.Object)">
            <summary>
            Execute a query and return first result
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.QuerySingleAsync``1(System.String,System.Object)">
            <summary>
            Execute a scalar query and return a single value
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.ExecuteAsync(System.String,System.Object)">
            <summary>
            Execute a command (INSERT, UPDATE, DELETE) and return affected rows
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.ExecuteScalarAsync``1(System.String,System.Object)">
            <summary>
            Execute a scalar command and return a single value
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.BeginTransactionAsync">
            <summary>
            Begin a database transaction
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.CommitTransactionAsync">
            <summary>
            Commit the current transaction
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.RollbackTransactionAsync">
            <summary>
            Rollback the current transaction
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.ExecuteStoredProcedureAsync``1(System.String,System.Object)">
            <summary>
            Execute a stored procedure and return results
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.ExecuteStoredProcedureSingleAsync``1(System.String,System.Object)">
            <summary>
            Execute a stored procedure and return a single result
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.ExecuteStoredProcedureAsync(System.String,System.Object)">
            <summary>
            Execute a stored procedure without return value
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.ExecuteFunctionAsync``1(System.String,System.Object)">
            <summary>
            Execute a Supabase function (PostgreSQL function) and return results
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.ExecuteFunctionSingleAsync``1(System.String,System.Object)">
            <summary>
            Execute a Supabase function (PostgreSQL function) and return a single result
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.BulkInsertAsync``1(System.Collections.Generic.IEnumerable{``0},System.String)">
            <summary>
            Bulk insert entities
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.BulkUpdateAsync``1(System.Collections.Generic.IEnumerable{``0},System.String,System.String)">
            <summary>
            Bulk update entities
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.BulkDeleteAsync``1(System.Collections.Generic.IEnumerable{``0},System.String,System.String)">
            <summary>
            Bulk delete entities
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.CanConnectAsync">
            <summary>
            Test database connectivity
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.ExecuteInTransactionAsync(System.Func{MicroSaasWebApi.App.Context.IDapperDbContext,System.Threading.Tasks.Task})">
            <summary>
            Execute multiple queries in a transaction without return value
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.ExecuteInTransactionAsync``1(System.Func{MicroSaasWebApi.App.Context.IDapperDbContext,System.Threading.Tasks.Task{``0}})">
            <summary>
            Execute multiple queries in a transaction with return value
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.App.Context.DapperDbContext.Dispose">
            <summary>
            Dispose resources
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.App.Context.IDapperDbContext">
            <summary>
            Dapper database context interface for SoNoBrokers operations
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.App.Repositories.AdvertiserRepository">
            <summary>
            Advertiser repository implementation using Dapper
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.App.Repositories.Interfaces.IAdvertiserRepository">
            <summary>
            Interface for advertiser repository operations
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.App.Repositories.Interfaces.IPropertyRepository">
            <summary>
            Interface for property repository operations
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.App.Repositories.Interfaces.IUserRepository">
            <summary>
            Interface for user repository operations
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.App.Repositories.PropertyRepository">
            <summary>
            Property repository implementation using Dapper
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.App.Repositories.UserRepository">
            <summary>
            User repository implementation using Dapper
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Controllers.Admin.AdminContactSharingController">
            <summary>
            Admin controller for contact sharing management
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Admin.AdminContactSharingController.GetAllContactShares(MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareSearchParams)">
            <summary>
            Get all contact shares (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Admin.AdminContactSharingController.GetContactShare(System.String)">
            <summary>
            Get contact share by ID (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Admin.AdminContactSharingController.GetContactShareStats(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Get contact share statistics (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Admin.AdminContactSharingController.SendReminderEmails">
            <summary>
            Send reminder emails for pending contact shares (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Admin.AdminContactSharingController.UpdateContactShareStatus(System.String,MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareSellerResponse)">
            <summary>
            Update contact share status (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Admin.AdminContactSharingController.DeleteContactShare(System.String)">
            <summary>
            Delete contact share (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Admin.AdminContactSharingController.GetUserContactShares(System.String,MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareSearchParams)">
            <summary>
            Get contact shares by user ID (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Admin.AdminContactSharingController.GetPropertyContactShares(System.String,MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareSearchParams)">
            <summary>
            Get contact shares by property ID (admin only)
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Controllers.Admin.AdminMessagingController">
            <summary>
            Admin controller for managing and monitoring all conversations and messages
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Admin.AdminMessagingController.GetAllConversations(MicroSaasWebApi.Models.SoNoBrokers.Messaging.ConversationSearchParams)">
            <summary>
            Get all conversations in the system (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Admin.AdminMessagingController.GetConversation(System.String)">
            <summary>
            Get a specific conversation with all details (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Admin.AdminMessagingController.GetConversationMessages(System.String,MicroSaasWebApi.Models.SoNoBrokers.Messaging.MessageSearchParams)">
            <summary>
            Get all messages in a conversation (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Admin.AdminMessagingController.GetMessagingStats">
            <summary>
            Get messaging statistics across the platform (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Admin.AdminMessagingController.GetMessagingActivity(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Get messaging activity over time (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Admin.AdminMessagingController.UpdateConversation(System.String,MicroSaasWebApi.Controllers.Admin.AdminUpdateConversationRequest)">
            <summary>
            Update conversation status (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Admin.AdminMessagingController.DeleteConversation(System.String)">
            <summary>
            Delete conversation (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Admin.AdminMessagingController.GetPropertyConversations(System.String,MicroSaasWebApi.Models.SoNoBrokers.Messaging.ConversationSearchParams)">
            <summary>
            Get conversations by property (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Admin.AdminMessagingController.GetUserConversations(System.String,MicroSaasWebApi.Models.SoNoBrokers.Messaging.ConversationSearchParams)">
            <summary>
            Get conversations by user (admin only)
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Controllers.Admin.AdminUpdateConversationRequest">
            <summary>
            Admin-specific DTOs
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Controllers.Core.ClerkAuthController">
            <summary>
            Clerk Authentication controller with role-based authorization
            Handles Clerk authentication and role management (Admin, Buyer, Seller)
            Public endpoints: Login, Register, Logout (no token required)
            All other controllers require valid Clerk token
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.ClerkAuthController.Login(MicroSaasWebApi.Models.Auth.Clerk.LoginRequest)">
            <summary>
            Authenticate user with Clerk - PUBLIC ENDPOINT
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.ClerkAuthController.Register(MicroSaasWebApi.Models.Auth.Clerk.RegisterRequest)">
            <summary>
            Register new user with Clerk - PUBLIC ENDPOINT
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.ClerkAuthController.RefreshToken(MicroSaasWebApi.Models.Auth.Clerk.RefreshTokenRequest)">
            <summary>
            Refresh Clerk authentication token - PUBLIC ENDPOINT
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.ClerkAuthController.Logout">
            <summary>
            Sign out user from Clerk - PUBLIC ENDPOINT
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.ClerkAuthController.ValidateToken(MicroSaasWebApi.Models.Auth.Clerk.TokenValidationRequest)">
            <summary>
            Validate Clerk token - REQUIRES VALID TOKEN
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.ClerkAuthController.GetUserRoles">
            <summary>
            Get user roles from Clerk metadata - REQUIRES VALID TOKEN
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.ClerkAuthController.GetUserPermissions">
            <summary>
            Get user permissions from Clerk metadata - REQUIRES VALID TOKEN
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Controllers.Core.ConfigurationController">
            <summary>
            Configuration controller for managing application settings
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.ConfigurationController.GetApplicationInfo">
            <summary>
            Get application information (public endpoint)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.ConfigurationController.GetFeatureFlags">
            <summary>
            Get feature flags (requires authentication)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.ConfigurationController.GetEnvironmentConfig">
            <summary>
            Get environment configuration (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.ConfigurationController.ValidateConfiguration">
            <summary>
            Validate configuration (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.ConfigurationController.GetConfigurationHealth">
            <summary>
            Get health status with configuration details (admin only)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.ConfigurationController.RefreshConfiguration">
            <summary>
            Refresh configuration (admin only)
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Controllers.Core.Stripe.StripePaymentsController">
            <summary>
            Stripe payments controller implementing SOLID principles
            Handles Stripe payment operations including one-time payments and subscriptions
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.Stripe.StripePaymentsController.CreateCustomer(MicroSaasWebApi.Controllers.Core.Stripe.CreateCustomerRequest)">
            <summary>
            Create a new Stripe customer
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.Stripe.StripePaymentsController.GetCustomer(System.String)">
            <summary>
            Get customer by ID
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.Stripe.StripePaymentsController.UpdateCustomer(System.String,MicroSaasWebApi.Controllers.Core.Stripe.UpdateCustomerRequest)">
            <summary>
            Update customer
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.Stripe.StripePaymentsController.DeleteCustomer(System.String)">
            <summary>
            Delete customer
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.Stripe.StripePaymentsController.CreatePaymentIntent(MicroSaasWebApi.Controllers.Core.Stripe.CreatePaymentIntentRequest)">
            <summary>
            Create payment intent
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.Stripe.StripePaymentsController.ConfirmPaymentIntent(System.String,MicroSaasWebApi.Controllers.Core.Stripe.ConfirmPaymentIntentRequest)">
            <summary>
            Confirm payment intent
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.Stripe.StripePaymentsController.CreateSubscription(MicroSaasWebApi.Controllers.Core.Stripe.CreateSubscriptionRequest)">
            <summary>
            Create subscription
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.Stripe.StripePaymentsController.CancelSubscription(System.String,MicroSaasWebApi.Controllers.Core.Stripe.CancelSubscriptionRequest)">
            <summary>
            Cancel subscription
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.Stripe.StripePaymentsController.HandleWebhook">
            <summary>
            Handle Stripe webhooks
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.Stripe.StripePaymentsController.CreateSoNoBrokersCheckoutSession(MicroSaasWebApi.Controllers.Core.Stripe.SoNoBrokersCreateCheckoutRequest)">
            <summary>
            Create Stripe checkout session for SoNoBrokers subscription
            </summary>
            <param name="request">Checkout session details</param>
            <returns>Checkout session URL</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.Stripe.StripePaymentsController.CreateSoNoBrokersCustomerPortal">
            <summary>
            Create Stripe customer portal session for SoNoBrokers
            </summary>
            <returns>Customer portal URL</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.Stripe.StripePaymentsController.GetSoNoBrokersSubscriptionStatus">
            <summary>
            Get subscription status for current SoNoBrokers user
            </summary>
            <returns>Subscription status</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.Core.Stripe.StripeWebhookController.HandleStripeWebhook">
            <summary>
            Handle Stripe webhooks for SoNoBrokers
            </summary>
            <returns>Webhook processing result</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdminController.GetDashboard">
            <summary>
            Get admin dashboard statistics
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdminController.GetUsers">
            <summary>
            Get all users
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdminController.GetUser(System.String)">
            <summary>
            Get user by ID
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdminController.UpdateUserRole(System.String,MicroSaasWebApi.Models.SoNoBrokers.UserRole)">
            <summary>
            Update user role
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdminController.UpdateUserStatus(System.String,System.Boolean)">
            <summary>
            Update user status
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdminController.GetRolePermissions">
            <summary>
            Get role permissions
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdminController.CreateRolePermission(MicroSaasWebApi.Models.SoNoBrokers.CreateRolePermissionRequest)">
            <summary>
            Create role permission
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdminController.UpdateRolePermission(System.String,MicroSaasWebApi.Models.SoNoBrokers.UpdateRolePermissionRequest)">
            <summary>
            Update role permission
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdminController.DeleteRolePermission(System.String)">
            <summary>
            Delete role permission
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdminController.SyncStripeData(MicroSaasWebApi.Models.SoNoBrokers.StripeSyncRequest)">
            <summary>
            Sync Stripe data
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdvertisersController.GetAdvertisers(MicroSaasWebApi.Models.SoNoBrokers.AdvertiserSearchRequest)">
            <summary>
            Get advertisers with filtering and pagination
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdvertisersController.GetAdvertiser(System.String)">
            <summary>
            Get advertiser by ID
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdvertisersController.CreateAdvertiser(MicroSaasWebApi.Models.SoNoBrokers.CreateAdvertiserRequest)">
            <summary>
            Create a new advertiser
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdvertisersController.UpdateAdvertiser(System.String,MicroSaasWebApi.Models.SoNoBrokers.UpdateAdvertiserRequest)">
            <summary>
            Update an existing advertiser
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdvertisersController.DeleteAdvertiser(System.String)">
            <summary>
            Delete an advertiser
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdvertisersController.GetMyAdvertiser">
            <summary>
            Get current user's advertiser profile
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdvertisersController.GetAdvertiserPlans">
            <summary>
            Get available advertiser plans
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdvertisersController.VerifyAdvertiser(System.String,System.Boolean)">
            <summary>
            Admin: Verify an advertiser
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AdvertisersController.UpdateAdvertiserStatus(System.String,MicroSaasWebApi.Models.SoNoBrokers.AdvertiserStatus)">
            <summary>
            Admin: Update advertiser status
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AIPropertyController.ImportPropertyData(MicroSaasWebApi.Models.SoNoBrokers.AIPropertyImportRequest)">
            <summary>
            Import property data using AI
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AIPropertyController.GetPropertyValuation(MicroSaasWebApi.Models.SoNoBrokers.AIPropertyValuationRequest)">
            <summary>
            Get property valuation using AI
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AIPropertyController.GenerateDescription(MicroSaasWebApi.Models.SoNoBrokers.GenerateDescriptionRequest)">
            <summary>
            Generate property description using AI
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AuthController.Login(MicroSaasWebApi.Models.SoNoBrokers.LoginRequest)">
            <summary>
            Login user with email and password
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AuthController.Register(MicroSaasWebApi.Models.SoNoBrokers.RegisterRequest)">
            <summary>
            Register new user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AuthController.GetProfile">
            <summary>
            Get current user profile
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AuthController.UpdateProfile(MicroSaasWebApi.Controllers.SoNoBrokers.UpdateProfileRequest)">
            <summary>
            Update user profile
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AuthController.RefreshToken(MicroSaasWebApi.Controllers.SoNoBrokers.RefreshTokenRequest)">
            <summary>
            Refresh authentication token
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AuthController.Logout">
            <summary>
            Logout user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AuthController.ValidateAuth">
            <summary>
            Validate current authentication status
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AuthController.TrackLogin">
            <summary>
            Track user login - updates lastLoginAt and loggedIn status
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AuthController.TrackLogout">
            <summary>
            Track user logout - updates loggedIn status to false
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AuthController.GetLoginStats">
            <summary>
            Get login statistics for current user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AuthController.SyncUserWithDatabase(System.String,MicroSaasWebApi.Models.Auth.Clerk.UserInfo)">
            <summary>
            Sync user data with local database
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.AuthController.GenerateTestToken(MicroSaasWebApi.Controllers.SoNoBrokers.TestTokenRequest)">
            <summary>
            Generate test bearer token for API testing (Development only)
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Controllers.SoNoBrokers.TestTokenRequest">
            <summary>
            Request model for generating test tokens
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Controllers.SoNoBrokers.UpdateProfileRequest">
            <summary>
            Request model for updating user profile
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Controllers.SoNoBrokers.RefreshTokenRequest">
            <summary>
            Request model for refreshing token
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.BuyerListingsController.CreateBuyerListing(MicroSaasWebApi.Controllers.SoNoBrokers.CreateBuyerListingRequest)">
            <summary>
            Create a new buyer listing
            </summary>
            <param name="request">Buyer listing details</param>
            <returns>Created buyer listing</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.BuyerListingsController.GetBuyerListing(System.String)">
            <summary>
            Get buyer listing by ID
            </summary>
            <param name="id">Buyer listing ID</param>
            <returns>Buyer listing details</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.BuyerListingsController.GetMyBuyerListings(System.Int32,System.Int32)">
            <summary>
            Get buyer listings for the current user
            </summary>
            <param name="page">Page number</param>
            <param name="pageSize">Page size</param>
            <returns>List of buyer listings</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.CommunicationController.ContactConcierge(MicroSaasWebApi.Models.SoNoBrokers.ContactConciergeRequest)">
            <summary>
            Send concierge service inquiry
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.CommunicationController.AddToWaitingList(MicroSaasWebApi.Models.SoNoBrokers.WaitingListRequest)">
            <summary>
            Add email to waiting list
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.CommunicationController.GetGeoLocation(System.String)">
            <summary>
            Get geolocation information by IP
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.CommunicationController.GetEnumValues">
            <summary>
            Get all enum values from database
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Controllers.SoNoBrokers.ContactSharingController">
            <summary>
            Controller for contact sharing functionality
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.ContactSharingController.CreateContactShare(MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.CreateContactShareRequest)">
            <summary>
            Create a new contact share request
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.ContactSharingController.GetContactShare(System.String)">
            <summary>
            Get contact share by ID
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.ContactSharingController.GetContactShares(MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareSearchParams)">
            <summary>
            Get contact shares for current user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.ContactSharingController.GetPropertyContactShares(System.String,MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareSearchParams)">
            <summary>
            Get contact shares for a specific property
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.ContactSharingController.RespondToContactShare(System.String,MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareSellerResponse)">
            <summary>
            Update contact share status (seller response)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.ContactSharingController.GetContactShareStats">
            <summary>
            Get contact share statistics for current user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.ContactSharingController.GetPropertyContactShareStats(System.String)">
            <summary>
            Get contact share statistics for a property
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.ContactSharingController.DeleteContactShare(System.String)">
            <summary>
            Delete a contact share
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.GenerateDescriptionController.GenerateDescription(MicroSaasWebApi.Controllers.SoNoBrokers.PropertyDescriptionRequest)">
            <summary>
            Generate AI-powered property description
            </summary>
            <param name="request">Property details for description generation</param>
            <returns>Generated property description</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.GeoController.GeocodeAddress(System.String)">
            <summary>
            Get geolocation data for an address
            </summary>
            <param name="address">Address to geocode</param>
            <returns>Geolocation data</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.GeoController.ReverseGeocode(System.Double,System.Double)">
            <summary>
            Reverse geocode coordinates to get address
            </summary>
            <param name="latitude">Latitude</param>
            <param name="longitude">Longitude</param>
            <returns>Address information</returns>
        </member>
        <member name="T:MicroSaasWebApi.Controllers.SoNoBrokers.MessagingController">
            <summary>
            Controller for managing conversations and messages between buyers and sellers
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.MessagingController.CreateConversation(MicroSaasWebApi.Models.SoNoBrokers.Messaging.CreateConversationRequest)">
            <summary>
            Create a new conversation about a property
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.MessagingController.GetConversations(MicroSaasWebApi.Models.SoNoBrokers.Messaging.ConversationSearchParams)">
            <summary>
            Get all conversations for the current user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.MessagingController.GetConversation(System.String)">
            <summary>
            Get a specific conversation with messages
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.MessagingController.SendMessage(MicroSaasWebApi.Models.SoNoBrokers.Messaging.CreateMessageRequest)">
            <summary>
            Send a message in a conversation
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.MessagingController.GetMessages(System.String,MicroSaasWebApi.Models.SoNoBrokers.Messaging.MessageSearchParams)">
            <summary>
            Get messages in a conversation
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.MessagingController.MarkMessagesAsRead(System.String,MicroSaasWebApi.Models.SoNoBrokers.Messaging.MarkMessagesReadRequest)">
            <summary>
            Mark messages as read
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.MessagingController.GetMessageStats">
            <summary>
            Get message statistics for the current user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.MessagingController.UpdateConversation(System.String,MicroSaasWebApi.Models.SoNoBrokers.Messaging.UpdateConversationRequest)">
            <summary>
            Update a conversation
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.MessagingController.DeleteConversation(System.String)">
            <summary>
            Delete (deactivate) a conversation
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.ProjectsController.GetProjects(MicroSaasWebApi.Models.SoNoBrokers.ProjectSearchRequest)">
            <summary>
            Get projects for the authenticated user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.ProjectsController.GetProject(System.String)">
            <summary>
            Get project by ID
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.ProjectsController.CreateProject(MicroSaasWebApi.Models.SoNoBrokers.CreateProjectRequest)">
            <summary>
            Create a new project
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.ProjectsController.UpdateProject(System.String,MicroSaasWebApi.Models.SoNoBrokers.UpdateProjectRequest)">
            <summary>
            Update an existing project
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.ProjectsController.DeleteProject(System.String)">
            <summary>
            Delete a project
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertiesController.GetProperties(System.Nullable{MicroSaasWebApi.Models.SoNoBrokers.PropertyStatus},System.String,System.Nullable{System.Decimal},System.Nullable{System.Decimal},System.Int32,System.Int32)">
            <summary>
            Get all properties
            </summary>
            <param name="status">Filter by property status</param>
            <param name="propertyType">Filter by property type</param>
            <param name="minPrice">Minimum price filter</param>
            <param name="maxPrice">Maximum price filter</param>
            <param name="page">Page number</param>
            <param name="pageSize">Page size</param>
            <returns>List of properties</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertiesController.GetProperty(System.String)">
            <summary>
            Get property by ID
            </summary>
            <param name="id">Property ID</param>
            <returns>Property details</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertiesController.CreateProperty(MicroSaasWebApi.Models.SoNoBrokers.Property)">
            <summary>
            Create a new property
            </summary>
            <param name="property">Property data</param>
            <returns>Created property</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertiesController.UpdateProperty(System.String,MicroSaasWebApi.Models.SoNoBrokers.Property)">
            <summary>
            Update property
            </summary>
            <param name="id">Property ID</param>
            <param name="property">Updated property data</param>
            <returns>Updated property</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertiesController.DeleteProperty(System.String)">
            <summary>
            Delete property
            </summary>
            <param name="id">Property ID</param>
            <returns>Success message</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertiesController.SearchProperties(System.String,System.String,System.String,System.Nullable{System.Decimal},System.Nullable{System.Decimal},System.Nullable{System.Int32},System.Nullable{System.Int32},System.Int32,System.Int32)">
            <summary>
            Search properties
            </summary>
            <param name="searchTerm">Search term</param>
            <param name="city">City filter</param>
            <param name="province">Province filter</param>
            <param name="minPrice">Minimum price</param>
            <param name="maxPrice">Maximum price</param>
            <param name="bedrooms">Number of bedrooms</param>
            <param name="bathrooms">Number of bathrooms</param>
            <param name="page">Page number</param>
            <param name="pageSize">Page size</param>
            <returns>Search results</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertyImagesController.GetPropertyImages(System.String)">
            <summary>
            Get images for a property
            </summary>
            <param name="propertyId">Property ID</param>
            <returns>List of property images</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertyImagesController.GetPropertyImage(System.String)">
            <summary>
            Get image by ID
            </summary>
            <param name="id">Image ID</param>
            <returns>Property image details</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertyImagesController.UploadPropertyImages(System.String,Microsoft.AspNetCore.Http.IFormFileCollection)">
            <summary>
            Upload property images
            </summary>
            <param name="propertyId">Property ID</param>
            <param name="files">Image files</param>
            <returns>Uploaded images</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertyImagesController.CreatePropertyImage(MicroSaasWebApi.Models.SoNoBrokers.PropertyImage)">
            <summary>
            Create property image record
            </summary>
            <param name="propertyImage">Property image data</param>
            <returns>Created property image</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertyImagesController.UpdatePropertyImage(System.String,MicroSaasWebApi.Models.SoNoBrokers.PropertyImage)">
            <summary>
            Update property image
            </summary>
            <param name="id">Image ID</param>
            <param name="propertyImage">Updated image data</param>
            <returns>Updated property image</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertyImagesController.DeletePropertyImage(System.String)">
            <summary>
            Delete property image
            </summary>
            <param name="id">Image ID</param>
            <returns>Success message</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertyImagesController.SetPrimaryImage(System.String,System.String)">
            <summary>
            Set primary image for property
            </summary>
            <param name="propertyId">Property ID</param>
            <param name="imageId">Image ID to set as primary</param>
            <returns>Success message</returns>
        </member>
        <member name="T:MicroSaasWebApi.Controllers.SoNoBrokers.PropertySchedulingController">
            <summary>
            Controller for property visit scheduling and management
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertySchedulingController.CreateSellerAvailability(MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling.CreateSellerAvailabilityRequest)">
            <summary>
            Create seller availability for a property
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertySchedulingController.GetSellerAvailability(System.String)">
            <summary>
            Get seller availability
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertySchedulingController.UpdateSellerAvailability(System.String,MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling.UpdateSellerAvailabilityRequest)">
            <summary>
            Update seller availability
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertySchedulingController.DeleteSellerAvailability(System.String)">
            <summary>
            Delete seller availability
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertySchedulingController.GetPropertyAvailability(System.String)">
            <summary>
            Get property availability for buyers
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertySchedulingController.CreateVisitSchedule(MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling.CreateVisitScheduleRequest)">
            <summary>
            Create a visit schedule request
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertySchedulingController.GetVisitSchedule(System.String)">
            <summary>
            Get visit schedule by ID
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertySchedulingController.GetVisitSchedules(MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling.VisitScheduleSearchParams)">
            <summary>
            Get visit schedules for current user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertySchedulingController.RespondToVisitRequest(System.String,MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling.RespondToVisitRequest)">
            <summary>
            Respond to visit request (seller)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertySchedulingController.CancelVisitSchedule(System.String,MicroSaasWebApi.Controllers.SoNoBrokers.CancelVisitRequest)">
            <summary>
            Cancel visit schedule
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertySchedulingController.GeneratePropertyQrCode(System.String)">
            <summary>
            Generate QR code for property
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertySchedulingController.GetPropertyQrCode(System.String)">
            <summary>
            Get property QR code
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertySchedulingController.VerifyVisit(System.String,MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling.VerifyVisitRequest)">
            <summary>
            Verify visit using QR code
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertySchedulingController.GetVisitVerifications(System.String)">
            <summary>
            Get visit verifications
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertySchedulingController.GetVisitSchedulingStats">
            <summary>
            Get visit scheduling statistics
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.PropertySchedulingController.GetUpcomingVisits(System.Int32)">
            <summary>
            Get upcoming visits
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Controllers.SoNoBrokers.CancelVisitRequest">
            <summary>
            Request model for cancelling visits
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.SearchFiltersController.GetSearchFilters">
            <summary>
            Get search filters for the current user
            </summary>
            <returns>List of search filters</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.SearchFiltersController.CreateSearchFilter(MicroSaasWebApi.Controllers.SoNoBrokers.CreateSearchFilterRequest)">
            <summary>
            Create a new search filter
            </summary>
            <param name="request">Search filter details</param>
            <returns>Created search filter</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.SearchFiltersController.GetSearchFilter(System.String)">
            <summary>
            Get search filter by ID
            </summary>
            <param name="id">Search filter ID</param>
            <returns>Search filter details</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.SearchFiltersController.UpdateSearchFilter(System.String,MicroSaasWebApi.Controllers.SoNoBrokers.UpdateSearchFilterRequest)">
            <summary>
            Update search filter
            </summary>
            <param name="id">Search filter ID</param>
            <param name="request">Updated search filter details</param>
            <returns>Updated search filter</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.SearchFiltersController.DeleteSearchFilter(System.String)">
            <summary>
            Delete search filter
            </summary>
            <param name="id">Search filter ID</param>
            <returns>Success message</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.SimplePropertiesController.GetProperties">
            <summary>
            Get all properties
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.SimplePropertiesController.GetProperty(System.String)">
            <summary>
            Get property by ID
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.SimplePropertiesController.CreateProperty(MicroSaasWebApi.Models.SoNoBrokers.Property)">
            <summary>
            Create a new property
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.SimplePropertiesController.SearchProperties(System.String,System.String,System.Nullable{System.Decimal},System.Nullable{System.Decimal},System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            Search properties
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.UploadController.GetSasToken(MicroSaasWebApi.Controllers.SoNoBrokers.SasTokenRequest)">
            <summary>
            Get SAS token for Azure Blob storage upload
            </summary>
            <param name="request">Upload request details</param>
            <returns>SAS token and upload URL</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.UploadController.GetUploadUrl(MicroSaasWebApi.Controllers.SoNoBrokers.UploadUrlRequest)">
            <summary>
            Get upload URL for Supabase storage (alternative to Azure)
            </summary>
            <param name="request">Upload request details</param>
            <returns>Upload URL and metadata</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.UsersController.GetUsers">
            <summary>
            Get all users
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.UsersController.GetUser(System.String)">
            <summary>
            Get user by ID
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.UsersController.CreateUser(MicroSaasWebApi.Models.SoNoBrokers.User)">
            <summary>
            Create a new user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.UsersController.UpdateUser(System.String,MicroSaasWebApi.Models.SoNoBrokers.User)">
            <summary>
            Update user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.UsersController.DeleteUser(System.String)">
            <summary>
            Delete user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.UsersController.GetUserByEmail(System.String)">
            <summary>
            Get user by email
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.UsersController.UpdateLoginStatus(System.String,System.Boolean)">
            <summary>
            Update user login status
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.WaitingListController.AddToWaitingList(MicroSaasWebApi.Models.SoNoBrokers.WaitingListRequest)">
            <summary>
            Add email to waiting list
            </summary>
            <param name="request">Email to add to waiting list</param>
            <returns>Success message</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.WaitingListController.GetWaitingListStats">
            <summary>
            Get waiting list statistics (admin only)
            </summary>
            <returns>Waiting list statistics</returns>
        </member>
        <member name="M:MicroSaasWebApi.Controllers.SoNoBrokers.WaitingListController.CheckEmail(System.String)">
            <summary>
            Check if email exists in waiting list
            </summary>
            <param name="email">Email to check</param>
            <returns>Whether email exists in waiting list</returns>
        </member>
        <member name="T:MicroSaasWebApi.Data.Context.DapperDbContext">
            <summary>
            Dapper database context for SoNoBrokers operations
            Consolidated implementation using only DATABASE_URL configuration
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Data.Context.DapperDbContext.Connection">
            <summary>
            Get database connection
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.ConvertSupabaseUriToConnectionString(System.String)">
            <summary>
            Convert Supabase URI to connection string format
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.GetConnection">
            <summary>
            Get the current database connection (implements IDapperDbContext)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.CreateConnection">
            <summary>
            Create a new database connection (for services that need their own connection)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.QueryAsync``1(System.String,System.Object)">
            <summary>
            Execute a query and return results
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.QuerySingleOrDefaultAsync``1(System.String,System.Object)">
            <summary>
            Execute a query and return a single result
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.QueryFirstOrDefaultAsync``1(System.String,System.Object)">
            <summary>
            Execute a query and return first result or default
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.QueryFirstAsync``1(System.String,System.Object)">
            <summary>
            Execute a query and return first result
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.QuerySingleAsync``1(System.String,System.Object)">
            <summary>
            Execute a scalar query and return a single value
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.ExecuteAsync(System.String,System.Object)">
            <summary>
            Execute a command (INSERT, UPDATE, DELETE) and return affected rows
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.ExecuteScalarAsync``1(System.String,System.Object)">
            <summary>
            Execute a scalar command and return a single value
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.BeginTransactionAsync">
            <summary>
            Begin a database transaction
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.CommitTransactionAsync">
            <summary>
            Commit the current transaction
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.RollbackTransactionAsync">
            <summary>
            Rollback the current transaction
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.ExecuteStoredProcedureAsync``1(System.String,System.Object)">
            <summary>
            Execute a stored procedure and return results
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.ExecuteStoredProcedureSingleAsync``1(System.String,System.Object)">
            <summary>
            Execute a stored procedure and return a single result
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.ExecuteStoredProcedureAsync(System.String,System.Object)">
            <summary>
            Execute a stored procedure without return value
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.ExecuteFunctionAsync``1(System.String,System.Object)">
            <summary>
            Execute a Supabase function (PostgreSQL function) and return results
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.BulkInsertAsync``1(System.Collections.Generic.IEnumerable{``0},System.String)">
            <summary>
            Bulk insert entities
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.BulkUpdateAsync``1(System.Collections.Generic.IEnumerable{``0},System.String,System.String)">
            <summary>
            Bulk update entities
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.BulkDeleteAsync``1(System.Collections.Generic.IEnumerable{``0},System.String,System.String)">
            <summary>
            Bulk delete entities
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.CanConnectAsync">
            <summary>
            Test database connectivity
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.ExecuteInTransactionAsync(System.Func{MicroSaasWebApi.Data.Context.IDapperDbContext,System.Threading.Tasks.Task})">
            <summary>
            Execute multiple queries in a transaction without return value
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.ExecuteInTransactionAsync``1(System.Func{MicroSaasWebApi.Data.Context.IDapperDbContext,System.Threading.Tasks.Task{``0}})">
            <summary>
            Execute multiple queries in a transaction with return value
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Data.Context.DapperDbContext.Dispose">
            <summary>
            Dispose resources
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Data.Context.IDapperDbContext">
            <summary>
            Dapper database context interface for SoNoBrokers operations
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Data.Repositories.Interfaces.IAdvertiserRepository">
            <summary>
            Interface for advertiser repository operations
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Data.Repositories.Interfaces.IPropertyRepository">
            <summary>
            Interface for property repository operations
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Data.Repositories.Interfaces.IUserRepository">
            <summary>
            Interface for user repository operations
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Data.Repositories.SoNoBrokers.AdvertiserRepository">
            <summary>
            Advertiser repository implementation using Dapper
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Data.Repositories.SoNoBrokers.PropertyRepository">
            <summary>
            Property repository implementation using Dapper
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Data.Repositories.SoNoBrokers.UserRepository">
            <summary>
            User repository implementation using Dapper
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Extensions.ClaimsPrincipalExtensions">
            <summary>
            Extension methods for ClaimsPrincipal to provide common authentication utilities
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Extensions.ClaimsPrincipalExtensions.GetUserId(System.Security.Claims.ClaimsPrincipal)">
            <summary>
            Gets the user ID from the claims principal
            </summary>
            <param name="principal">The claims principal</param>
            <returns>The user ID or null if not found</returns>
        </member>
        <member name="M:MicroSaasWebApi.Extensions.ClaimsPrincipalExtensions.GetUserEmail(System.Security.Claims.ClaimsPrincipal)">
            <summary>
            Gets the user email from the claims principal
            </summary>
            <param name="principal">The claims principal</param>
            <returns>The user email or null if not found</returns>
        </member>
        <member name="M:MicroSaasWebApi.Extensions.ClaimsPrincipalExtensions.GetUserName(System.Security.Claims.ClaimsPrincipal)">
            <summary>
            Gets the user name from the claims principal
            </summary>
            <param name="principal">The claims principal</param>
            <returns>The user name or null if not found</returns>
        </member>
        <member name="M:MicroSaasWebApi.Extensions.ClaimsPrincipalExtensions.IsAdmin(System.Security.Claims.ClaimsPrincipal)">
            <summary>
            Checks if the user has admin role
            </summary>
            <param name="principal">The claims principal</param>
            <returns>True if user is admin, false otherwise</returns>
        </member>
        <member name="M:MicroSaasWebApi.Extensions.ClaimsPrincipalExtensions.HasRole(System.Security.Claims.ClaimsPrincipal,System.String)">
            <summary>
            Checks if the user has a specific role
            </summary>
            <param name="principal">The claims principal</param>
            <param name="role">The role to check</param>
            <returns>True if user has the role, false otherwise</returns>
        </member>
        <member name="M:MicroSaasWebApi.Extensions.ClaimsPrincipalExtensions.HasRole(System.Security.Claims.ClaimsPrincipal,MicroSaasWebApi.Models.SoNoBrokers.UserRole)">
            <summary>
            Checks if the user has a specific role
            </summary>
            <param name="principal">The claims principal</param>
            <param name="role">The role to check</param>
            <returns>True if user has the role, false otherwise</returns>
        </member>
        <member name="M:MicroSaasWebApi.Extensions.ClaimsPrincipalExtensions.GetUserRole(System.Security.Claims.ClaimsPrincipal)">
            <summary>
            Gets the user's role from claims
            </summary>
            <param name="principal">The claims principal</param>
            <returns>The user role or null if not found</returns>
        </member>
        <member name="M:MicroSaasWebApi.Extensions.ClaimsPrincipalExtensions.IsAuthenticated(System.Security.Claims.ClaimsPrincipal)">
            <summary>
            Checks if the user is authenticated
            </summary>
            <param name="principal">The claims principal</param>
            <returns>True if authenticated, false otherwise</returns>
        </member>
        <member name="M:MicroSaasWebApi.Extensions.ClaimsPrincipalExtensions.GetClaimValue(System.Security.Claims.ClaimsPrincipal,System.String)">
            <summary>
            Gets a custom claim value
            </summary>
            <param name="principal">The claims principal</param>
            <param name="claimType">The claim type</param>
            <returns>The claim value or null if not found</returns>
        </member>
        <member name="T:MicroSaasWebApi.Extensions.ServiceCollectionExtensions">
            <summary>
            Service collection extensions for dependency injection
            Follows Dependency Inversion Principle
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Extensions.ServiceCollectionExtensions.AddAuthenticationServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Registers authentication services
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Extensions.ServiceCollectionExtensions.AddStorageServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Registers storage services
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Extensions.ServiceCollectionExtensions.AddPaymentServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Registers payment services
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Extensions.ServiceCollectionExtensions.AddSoNoBrokersServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Registers SoNoBrokers services (using proper repository pattern)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Extensions.ServiceCollectionExtensions.AddAuthorizationPolicies(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Registers basic authorization policies for SoNoBrokers
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Extensions.ServiceCollectionExtensions.AddCustomServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Registers all custom services for SoNoBrokers
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Filters.HangfireAuthorizationFilter">
            <summary>
            Authorization filter for Hangfire dashboard
            Only allows access in development and staging environments
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Health.HealthCheckConfiguration">
            <summary>
            Health Check Configuration for .NET 9 Web API
            Following best practices from: https://medium.com/@jeslurrahman/implementing-health-checks-in-net-8-c3ba10af83c3
            Adapted for PostgreSQL/Supabase instead of SQL Server
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Health.HealthCheckConfiguration.ConfigureHealthChecks(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Configure comprehensive health checks for the MicroSaaS Web API
            </summary>
            <param name="services">Service collection</param>
            <param name="configuration">Application configuration</param>
        </member>
        <member name="M:MicroSaasWebApi.Health.HealthCheckConfiguration.UseHealthCheckMiddleware(Microsoft.AspNetCore.Builder.WebApplication)">
            <summary>
            Configure health check middleware and endpoints
            </summary>
            <param name="app">Web application</param>
        </member>
        <member name="M:MicroSaasWebApi.Health.HealthCheckConfiguration.GetHealthCheckUrl(Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Get the appropriate health check URL based on environment
            </summary>
            <param name="configuration">Configuration</param>
            <returns>Health check URL</returns>
        </member>
        <member name="T:MicroSaasWebApi.Health.MemoryHealthCheck">
            <summary>
            Health check for monitoring memory usage and garbage collection
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Health.MemoryCheckOptions">
            <summary>
            Configuration options for memory health check
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Health.MemoryCheckOptions.MemoryStatus">
            <summary>
            Memory status description
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Health.MemoryCheckOptions.Threshold">
            <summary>
            Failure threshold in bytes (default: 1GB)
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Health.MemoryCheckOptions.ForceGarbageCollection">
            <summary>
            Whether to force garbage collection before checking memory
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Health.MemoryCheckOptions.WarningThreshold">
            <summary>
            Warning threshold in bytes (default: 512MB)
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Health.RemoteEndpointHealthCheck">
            <summary>
            Health check for monitoring remote endpoints and external services
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Hubs.MessagingHub">
            <summary>
            SignalR Hub for real-time messaging functionality
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MessagingHub.OnConnectedAsync">
            <summary>
            Called when a client connects
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MessagingHub.OnDisconnectedAsync(System.Exception)">
            <summary>
            Called when a client disconnects
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MessagingHub.JoinConversation(System.String)">
            <summary>
            Join a conversation group for real-time updates
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MessagingHub.LeaveConversation(System.String)">
            <summary>
            Leave a conversation group
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MessagingHub.SendTypingIndicator(System.String,System.Boolean)">
            <summary>
            Send typing indicator
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MessagingHub.MarkMessagesAsRead(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Mark messages as read in real-time
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MessagingHub.GetOnlineUsers">
            <summary>
            Get online users
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MessagingHub.SendMessageNotification(Microsoft.AspNetCore.SignalR.IHubContext{MicroSaasWebApi.Hubs.MessagingHub},MicroSaasWebApi.Models.SoNoBrokers.Messaging.MessageNotification)">
            <summary>
            Send a message through SignalR (called from MessagingService)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MessagingHub.SendConversationUpdate(Microsoft.AspNetCore.SignalR.IHubContext{MicroSaasWebApi.Hubs.MessagingHub},System.String,System.Object)">
            <summary>
            Send conversation update notification
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Hubs.MicroSaasHub">
            <summary>
            SignalR hub for real-time communication in MicroSaaS application
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MicroSaasHub.OnConnectedAsync">
            <summary>
            Handle client connection
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MicroSaasHub.OnDisconnectedAsync(System.Exception)">
            <summary>
            Handle client disconnection
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MicroSaasHub.JoinGroup(System.String)">
            <summary>
            Join a specific group (e.g., organization, project)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MicroSaasHub.LeaveGroup(System.String)">
            <summary>
            Leave a specific group
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MicroSaasHub.SendMessageToGroup(System.String,System.String)">
            <summary>
            Send a message to a specific group
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MicroSaasHub.SendPrivateMessage(System.String,System.String)">
            <summary>
            Send a private message to a specific user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MicroSaasHub.BroadcastSystemNotification(System.String,System.String)">
            <summary>
            Broadcast system notification to all connected users
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MicroSaasHub.SendPaymentUpdate(System.String,System.Object)">
            <summary>
            Send payment status update to user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MicroSaasHub.SendSubscriptionUpdate(System.String,System.Object)">
            <summary>
            Send subscription status update to user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Hubs.MicroSaasHub.SendAnalyticsUpdate(System.Object)">
            <summary>
            Send real-time analytics update to admin users
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Middlewares.ClerkAuthMiddleware">
            <summary>
            Clerk Authentication Middleware
            Validates Clerk tokens for all API requests except public endpoints
            Supports role-based authorization (Admin, Buyer, Seller)
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Middlewares.ClerkAuthMiddlewareExtensions">
            <summary>
            Extension method to register ClerkAuthMiddleware
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Middlewares.DocumentationMiddleware">
            <summary>
            Middleware to serve markdown documentation files
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Middleware.CleanMiddlewareOrdering">
            <summary>
            Clean middleware pipeline configuration for Core functionality
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Middleware.CleanMiddlewareOrdering.ConfigureCleanMiddlewarePipeline(Microsoft.AspNetCore.Builder.WebApplication)">
            <summary>
            Configure the clean middleware pipeline without tenant complexity
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Middleware.MiddlewareOrdering">
            <summary>
            Middleware ordering configuration for .NET 9
            Implements the Chain of Responsibility pattern for middleware execution
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Middleware.MiddlewareOrdering.ConfigureMiddlewarePipeline(Microsoft.AspNetCore.Builder.WebApplication)">
            <summary>
            Configure middleware pipeline in the correct order
            Order is critical for security, performance, and functionality
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Middleware.MiddlewareOrder">
            <summary>
            Middleware execution order enum for reference
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Auth.Clerk.LoginRequest">
            <summary>
            Login request model
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Auth.Clerk.RegisterRequest">
            <summary>
            Registration request model
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Auth.Clerk.AuthResponse">
            <summary>
            Authentication response model
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Auth.Clerk.UserInfo">
            <summary>
            User information model
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Auth.Clerk.PasswordResetRequest">
            <summary>
            Password reset request model
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Auth.Clerk.PasswordUpdateRequest">
            <summary>
            Password update request model
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Auth.Clerk.EmailVerificationRequest">
            <summary>
            Email verification request model
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Auth.Clerk.RefreshTokenRequest">
            <summary>
            Request DTO for token refresh
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Auth.Clerk.TokenValidationRequest">
            <summary>
            Request DTO for token validation
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Auth.Clerk.AssignRoleRequest">
            <summary>
            Request DTO for assigning role to user
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Auth.Clerk.RemoveRoleRequest">
            <summary>
            Request DTO for removing role from user
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.AppSettings">
            <summary>
            Simplified application settings that loads from environment variables
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.LoggingSettings">
            <summary>
            Logging configuration settings
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.RateLimitSettings">
            <summary>
            Rate limiting configuration
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.CorsSettings">
            <summary>
            CORS configuration
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Core.ApiResponse`1">
            <summary>
            Standard API response wrapper
            </summary>
            <typeparam name="T">Type of data being returned</typeparam>
        </member>
        <member name="T:MicroSaasWebApi.Models.Core.ApiResponse">
            <summary>
            Standard API response without data
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Core.Document">
            <summary>
            Document model for file management
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Core.Product">
            <summary>
            Product model for the MicroSaaS application
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Core.Subscription">
            <summary>
            Subscription model for payment management
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Core.User">
            <summary>
            Core User model for the MicroSaaS application
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Core.UserRole">
            <summary>
            User role model for authorization
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Database.User">
            <summary>
            User entity for MicroSaaS application
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Database.Subscription">
            <summary>
            Subscription entity for payment management
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Database.Role">
            <summary>
            Role entity for authorization
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Database.UserRole">
            <summary>
            User-Role mapping entity
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Database.Permission">
            <summary>
            Permission entity for fine-grained access control
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Database.RolePermission">
            <summary>
            Role-Permission mapping entity
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Database.AuditLog">
            <summary>
            Audit log entity for tracking changes
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Payment.SubscriptionPlan">
            <summary>
            Subscription plan model
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Payment.CreateSubscriptionRequest">
            <summary>
            Subscription request model
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Payment.OneTimePaymentRequest">
            <summary>
            One-time payment request model
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Payment.PaymentResponse">
            <summary>
            Payment response model
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Payment.CheckoutSessionRequest">
            <summary>
            Checkout session request model
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Payment.CheckoutLineItem">
            <summary>
            Checkout line item model
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Payment.SubscriptionStatus">
            <summary>
            Subscription status model
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Payment.ClerkWebhookEvent">
            <summary>
            Webhook event model for Clerk
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Payment.Invoice">
            <summary>
            Invoice model
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Profile.Account">
            <summary>
            Summary description for Account
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Profile.AccountInformation">
            <summary>
            Summary description for AccountInfo
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Profile.AccountProfile">
            <summary>
            Summary description for Account Profile
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Profile.UserProfile">
            <summary>
            User Profile model for MicroSaaS template
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.LoginRequest">
            <summary>
            Login request DTO
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.RegisterRequest">
            <summary>
            Register request DTO
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.AuthResponse">
            <summary>
            Authentication response DTO
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.UserInfo">
            <summary>
            User information DTO
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.PermissionInfo">
            <summary>
            Permission information DTO
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.TokenValidationRequest">
            <summary>
            Token validation request DTO
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.TokenValidationResponse">
            <summary>
            Token validation response DTO
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.PasswordResetRequest">
            <summary>
            Password reset request DTO
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.PasswordResetConfirmRequest">
            <summary>
            Password reset confirmation DTO
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShare">
            <summary>
            Contact sharing entity for buyer-seller direct communication
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareType">
            <summary>
            Type of contact sharing request
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareStatus">
            <summary>
            Status of contact sharing request
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.CreateContactShareRequest">
            <summary>
            DTO for creating contact share request
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareResponse">
            <summary>
            DTO for contact share response
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareSellerResponse">
            <summary>
            DTO for seller response to contact share
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareSearchParams">
            <summary>
            DTO for contact share search parameters
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareSearchResponse">
            <summary>
            DTO for contact share search response
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareStats">
            <summary>
            DTO for contact share statistics
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactSharePropertyStats">
            <summary>
            DTO for property-specific contact share statistics
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.UserRole">
            <summary>
            User role enum matching React frontend
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.SnbUserType">
            <summary>
            SoNoBrokers user type enum matching React frontend
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Country">
            <summary>
            Country enum
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.PropertyStatus">
            <summary>
            Property status enum
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.AdvertiserPlan">
            <summary>
            Advertiser plan enum
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.AdvertiserStatus">
            <summary>
            Advertiser status enum
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.ServiceType">
            <summary>
            Service type enum
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Conversation">
            <summary>
            Represents a conversation between users about a property
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Conversation.PropertyId">
            <summary>
            Property that the conversation is about
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Conversation.BuyerId">
            <summary>
            Buyer participating in the conversation
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Conversation.SellerId">
            <summary>
            Seller participating in the conversation
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Conversation.AgentId">
            <summary>
            Agent participating in the conversation (optional)
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Conversation.Subject">
            <summary>
            Subject/title of the conversation
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Conversation.LastMessageAt">
            <summary>
            Timestamp of the last message in this conversation
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Conversation.IsActive">
            <summary>
            Whether the conversation is active
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Conversation.CreatedAt">
            <summary>
            When the conversation was created
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Conversation.UpdatedAt">
            <summary>
            When the conversation was last updated
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.CreateConversationRequest">
            <summary>
            DTO for creating a new conversation
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.ConversationResponse">
            <summary>
            DTO for conversation response
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.ConversationListItem">
            <summary>
            DTO for conversation list item
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.ConversationSearchParams">
            <summary>
            DTO for conversation search parameters
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.ConversationSearchResponse">
            <summary>
            DTO for conversation search response
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.UpdateConversationRequest">
            <summary>
            DTO for updating conversation
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.ConversationParticipant">
            <summary>
            DTO for conversation participants
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Message">
            <summary>
            Represents a message in a conversation
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Message.ConversationId">
            <summary>
            ID of the conversation this message belongs to
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Message.SenderId">
            <summary>
            ID of the user who sent this message
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Message.Content">
            <summary>
            Content of the message
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Message.MessageType">
            <summary>
            Type of message (text, system, etc.)
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Message.Attachments">
            <summary>
            Attachments (for future use)
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Message.IsRead">
            <summary>
            Whether the message has been read
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Message.ReadAt">
            <summary>
            When the message was read
            </summary>
        </member>
        <member name="P:MicroSaasWebApi.Models.SoNoBrokers.Messaging.Message.CreatedAt">
            <summary>
            When the message was created
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.CreateMessageRequest">
            <summary>
            DTO for creating a new message
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.MessageResponse">
            <summary>
            DTO for message response
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.MessageSearchParams">
            <summary>
            DTO for message search parameters
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.MessageSearchResponse">
            <summary>
            DTO for message search response
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.MarkMessagesReadRequest">
            <summary>
            DTO for marking messages as read
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.BulkMessageRequest">
            <summary>
            DTO for bulk message operations
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.MessageStats">
            <summary>
            DTO for message statistics
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.ConversationStats">
            <summary>
            DTO for conversation statistics
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.MessageNotification">
            <summary>
            DTO for real-time message notification
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.Messaging.TypingIndicator">
            <summary>
            DTO for typing indicator
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling.SellerAvailability">
            <summary>
            Seller availability schedule for property visits
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling.PropertyVisitSchedule">
            <summary>
            Property visit schedule request and confirmation
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling.VisitVerification">
            <summary>
            Visit verification log for security tracking
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling.PropertyQrCode">
            <summary>
            Property QR code for visit verification
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling.VisitStatus">
            <summary>
            Enums for visit scheduling
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling.CreateSellerAvailabilityRequest">
            <summary>
            Request models for API endpoints
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling.SellerAvailabilityResponse">
            <summary>
            Response models for API endpoints
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling.VisitScheduleSearchParams">
            <summary>
            Search and filter parameters
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Storage.BlobStorageFile">
            <summary>
            Azure Blob Storage file model
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Storage.BlobStorageFileContent">
            <summary>
            Azure Blob Storage file with content
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Storage.BlobStorageUploadRequest">
            <summary>
            Blob storage upload request
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Storage.BlobStorageListRequest">
            <summary>
            Blob storage list request
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Storage.BlobStorageUpdateRequest">
            <summary>
            Blob storage update request
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Storage.BlobStorageSasRequest">
            <summary>
            Blob storage SAS URL request
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Models.Storage.BlobStorageCopyRequest">
            <summary>
            Blob storage copy request
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Auth.ClerkAuthService">
            <summary>
            Clerk authentication service implementing SOLID principles
            Handles Clerk-specific authentication operations
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.ClerkAuthService.ValidateClerkTokenAsync(System.String)">
            <summary>
            Validates Clerk JWT token
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.ClerkAuthService.LoginAsync(MicroSaasWebApi.Models.Auth.Clerk.LoginRequest)">
            <summary>
            Login user with Clerk
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.ClerkAuthService.AuthenticateAsync(System.String,System.String)">
            <summary>
            Authenticates user with Clerk
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.ClerkAuthService.RegisterAsync(MicroSaasWebApi.Models.Auth.Clerk.RegisterRequest)">
            <summary>
            Registers new user with Clerk
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.ClerkAuthService.GetUserAsync(System.String)">
            <summary>
            Gets user information from Clerk
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.ClerkAuthService.UpdateUserAsync(System.String,System.String,System.String)">
            <summary>
            Updates user in Clerk
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.ClerkAuthService.DeleteUserAsync(System.String)">
            <summary>
            Deletes user from Clerk
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.ClerkAuthService.GenerateJwtTokenAsync(MicroSaasWebApi.Services.Auth.Clerk.Interface.ClerkUser)">
            <summary>
            Generates JWT token for Clerk user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.ClerkAuthService.RefreshTokenAsync(System.String)">
            <summary>
            Refreshes Clerk session token
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.ClerkAuthService.LogoutAsync(System.String)">
            <summary>
            Logout user from Clerk
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.ClerkAuthService.SignOutAsync(System.String)">
            <summary>
            Signs out user from Clerk
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.ClerkAuthService.GetUserIdAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Gets the authenticated user ID from HTTP context
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.ClerkAuthService.GetUserProfileAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Gets the authenticated user profile from HTTP context and database
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Auth.Interfaces.IClerkAuthService">
            <summary>
            Interface for Clerk authentication service
            Follows Interface Segregation Principle
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.Interfaces.IClerkAuthService.ValidateClerkTokenAsync(System.String)">
            <summary>
            Validates Clerk JWT token and returns claims principal
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.Interfaces.IClerkAuthService.AuthenticateAsync(System.String,System.String)">
            <summary>
            Authenticates user with email and password via Clerk
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.Interfaces.IClerkAuthService.LoginAsync(MicroSaasWebApi.Models.Auth.Clerk.LoginRequest)">
            <summary>
            Login user with Clerk
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.Interfaces.IClerkAuthService.RegisterAsync(MicroSaasWebApi.Models.Auth.Clerk.RegisterRequest)">
            <summary>
            Registers new user via Clerk
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.Interfaces.IClerkAuthService.LogoutAsync(System.String)">
            <summary>
            Logout user from Clerk
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.Interfaces.IClerkAuthService.GetUserAsync(System.String)">
            <summary>
            Gets user information from Clerk
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.Interfaces.IClerkAuthService.UpdateUserAsync(System.String,System.String,System.String)">
            <summary>
            Updates user information in Clerk
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.Interfaces.IClerkAuthService.DeleteUserAsync(System.String)">
            <summary>
            Deletes user from Clerk
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.Interfaces.IClerkAuthService.RefreshTokenAsync(System.String)">
            <summary>
            Refreshes authentication token
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.Interfaces.IClerkAuthService.SignOutAsync(System.String)">
            <summary>
            Signs out user from Clerk
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.Interfaces.IClerkAuthService.GetUserIdAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Gets the authenticated user ID from HTTP context
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Auth.Interfaces.IClerkAuthService.GetUserProfileAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Gets the authenticated user profile from HTTP context and database
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Configuration.IAppConfigurationService">
            <summary>
            Service for managing application configuration
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Configuration.AppConfigurationService">
            <summary>
            Implementation of application configuration service
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Configuration.AppConfigurationService.ValidateSettings">
            <summary>
            Validate all required settings and return any missing ones
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Configuration.AppConfigurationService.GetFeatureFlags">
            <summary>
            Get feature flags configuration
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Configuration.AppConfigurationService.GetEnvironmentConfig">
            <summary>
            Get environment-specific configuration
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Configuration.EnvironmentConfig">
            <summary>
            Environment-specific configuration
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Configuration.EnvironmentConfigurationService">
            <summary>
            Service for loading environment variables from .env file
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Configuration.EnvironmentConfigurationService.LoadEnvironmentVariables">
            <summary>
            Load environment variables from .env file
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Configuration.EnvironmentConfigurationService.GetEnvironmentVariable(System.String,System.String)">
            <summary>
            Get environment variable with fallback
            </summary>
            <param name="key">Environment variable key</param>
            <param name="defaultValue">Default value if not found</param>
            <returns>Environment variable value or default</returns>
        </member>
        <member name="M:MicroSaasWebApi.Services.Configuration.EnvironmentConfigurationService.ValidateRequiredVariables(System.String[])">
            <summary>
            Validate required environment variables
            </summary>
            <param name="requiredVariables">List of required environment variable keys</param>
            <returns>List of missing variables</returns>
        </member>
        <member name="T:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService">
            <summary>
            Document service interface for document management operations
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.GetDocumentsAsync(System.Int32,System.Int32,System.String,System.String,System.String,System.Nullable{System.Boolean})">
            <summary>
            Get documents with pagination and filtering
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.GetDocumentByIdAsync(System.Guid)">
            <summary>
            Get document by ID
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.GetDocumentsByCategoryAsync(System.String,System.Int32,System.Int32)">
            <summary>
            Get documents by category
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.GetDocumentsByUserAsync(System.Guid,System.Int32,System.Int32)">
            <summary>
            Get documents by user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.UploadDocumentAsync(MicroSaasWebApi.Models.Core.Document,Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Upload new document
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.UpdateDocumentAsync(MicroSaasWebApi.Models.Core.Document)">
            <summary>
            Update document metadata
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.DeleteDocumentAsync(System.Guid)">
            <summary>
            Delete document
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.GetDocumentStreamAsync(System.Guid)">
            <summary>
            Get document stream for download
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.GetDocumentUrlAsync(System.Guid,System.Nullable{System.TimeSpan})">
            <summary>
            Get document URL for direct access
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.DocumentExistsAsync(System.Guid)">
            <summary>
            Check if document exists
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.GetDocumentCountAsync(System.Nullable{System.Boolean})">
            <summary>
            Get document count
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.GetDocumentsByContentTypeAsync(System.String,System.Int32,System.Int32)">
            <summary>
            Get documents by content type
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.SearchDocumentsAsync(System.String,System.Int32,System.Int32)">
            <summary>
            Search documents by name or description
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.GetPublicDocumentsAsync(System.Int32,System.Int32)">
            <summary>
            Get public documents
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.GetCategoriesAsync">
            <summary>
            Get all document categories
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.GetDocumentsBySizeRangeAsync(System.Int64,System.Int64,System.Int32,System.Int32)">
            <summary>
            Get documents by size range
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.GetDocumentsByDateRangeAsync(System.DateTime,System.DateTime,System.Int32,System.Int32)">
            <summary>
            Get documents by date range
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.SetDocumentPublicStatusAsync(System.Guid,System.Boolean)">
            <summary>
            Update document access status
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.GetUserStorageUsedAsync(System.Guid)">
            <summary>
            Get total storage used by user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IDocumentService.ValidateFileUploadAsync(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Validate file upload
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Core.Interfaces.IProductService">
            <summary>
            Product service interface for product management operations
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IProductService.GetProductsAsync(System.Int32,System.Int32,System.String,System.String,System.Nullable{System.Boolean},System.Nullable{System.Decimal},System.Nullable{System.Decimal})">
            <summary>
            Get products with pagination and filtering
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IProductService.GetProductByIdAsync(System.Guid)">
            <summary>
            Get product by ID
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IProductService.GetProductBySkuAsync(System.String)">
            <summary>
            Get product by SKU
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IProductService.GetProductsByCategoryAsync(System.String,System.Int32,System.Int32)">
            <summary>
            Get products by category
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IProductService.CreateProductAsync(MicroSaasWebApi.Models.Core.Product)">
            <summary>
            Create new product
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IProductService.UpdateProductAsync(MicroSaasWebApi.Models.Core.Product)">
            <summary>
            Update existing product
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IProductService.DeleteProductAsync(System.Guid)">
            <summary>
            Delete product (soft delete)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IProductService.UpdateStockAsync(System.Guid,System.Int32,System.String)">
            <summary>
            Update product stock
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IProductService.ProductExistsBySkuAsync(System.String)">
            <summary>
            Check if product exists by SKU
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IProductService.GetProductCountAsync(System.Nullable{System.Boolean})">
            <summary>
            Get product count
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IProductService.GetLowStockProductsAsync(System.Int32)">
            <summary>
            Get products with low stock
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IProductService.GetFeaturedProductsAsync(System.Int32)">
            <summary>
            Get featured products
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IProductService.SearchProductsAsync(System.String,System.Int32,System.Int32)">
            <summary>
            Search products by name or description
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IProductService.GetProductsByPriceRangeAsync(System.Decimal,System.Decimal,System.Int32,System.Int32)">
            <summary>
            Get products by price range
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IProductService.GetCategoriesAsync">
            <summary>
            Get all product categories
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IProductService.SetProductActiveStatusAsync(System.Guid,System.Boolean)">
            <summary>
            Activate/deactivate product
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Core.Interfaces.IUserService">
            <summary>
            User service interface for user management operations
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IUserService.GetUsersAsync(System.Int32,System.Int32,System.String,System.Nullable{System.Boolean})">
            <summary>
            Get users with pagination and filtering
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IUserService.GetUserByIdAsync(System.Guid)">
            <summary>
            Get user by ID
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IUserService.GetUserByClerkIdAsync(System.String)">
            <summary>
            Get user by Clerk ID
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IUserService.GetUserByEmailAsync(System.String)">
            <summary>
            Get user by email
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IUserService.CreateUserAsync(MicroSaasWebApi.Models.Core.User)">
            <summary>
            Create new user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IUserService.UpdateUserAsync(MicroSaasWebApi.Models.Core.User)">
            <summary>
            Update existing user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IUserService.DeleteUserAsync(System.Guid)">
            <summary>
            Delete user (soft delete)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IUserService.UpdateLastLoginAsync(System.Guid)">
            <summary>
            Update user's last login timestamp
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IUserService.VerifyEmailAsync(System.Guid)">
            <summary>
            Verify user's email
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IUserService.SetUserActiveStatusAsync(System.Guid,System.Boolean)">
            <summary>
            Activate/deactivate user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IUserService.GetUserCountAsync(System.Nullable{System.Boolean})">
            <summary>
            Get user count
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IUserService.UserExistsByEmailAsync(System.String)">
            <summary>
            Check if user exists by email
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IUserService.UserExistsByClerkIdAsync(System.String)">
            <summary>
            Check if user exists by Clerk ID
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IUserService.GetUsersByRoleAsync(System.String,System.Int32,System.Int32)">
            <summary>
            Get users by role
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Core.Interfaces.IUserService.SearchUsersAsync(System.String,System.Int32,System.Int32)">
            <summary>
            Search users by name or email
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Payment.Interfaces.IStripePaymentService">
            <summary>
            Interface for Stripe payment service
            Follows Interface Segregation Principle
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Payment.StripePaymentService">
            <summary>
            Stripe payment service implementation
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Repository.Interface.IBaseRepository`1">
            <summary>
            Generic repository interface for common CRUD operations
            </summary>
            <typeparam name="T">Entity type</typeparam>
        </member>
        <member name="T:MicroSaasWebApi.Services.Repository.Interface.IDapperRepository`1">
            <summary>
            Repository interface with Dapper support for stored procedures
            </summary>
            <typeparam name="T">Entity type</typeparam>
        </member>
        <member name="T:MicroSaasWebApi.Services.Repository.Interface.IUnitOfWork">
            <summary>
            Unit of Work pattern interface
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Repository.Interface.IUserRepository">
            <summary>
            User repository interface with specific operations
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Repository.Interface.ISubscriptionRepository">
            <summary>
            Subscription repository interface with specific operations
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Repository.Interface.IRoleRepository">
            <summary>
            Role repository interface with specific operations
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Repository.Interface.IPermissionRepository">
            <summary>
            Permission repository interface with specific operations
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Repository.Interface.IAuditLogRepository">
            <summary>
            Audit log repository interface with specific operations
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.SoNoBrokers.ContactSharingEmailService">
            <summary>
            Email service for contact sharing functionality using Resend API
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.ContactSharingEmailService.SendEmailAsync(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Send email using Resend API
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.SoNoBrokers.ContactSharingService">
            <summary>
            Service for managing contact sharing between buyers and sellers
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.ContactSharingService.UpdateEmailSentStatusAsync(System.String,System.Boolean)">
            <summary>
            Helper method to update email sent status
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.ContactSharingService.BuildSearchConditions(System.Collections.Generic.List{System.String},Dapper.DynamicParameters,MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareSearchParams)">
            <summary>
            Helper method to build search conditions
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.ContactSharingService.MapToContactShareResponse(System.Object)">
            <summary>
            Helper method to map database result to ContactShareResponse
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.ContactSharingService.GetShareTypeDisplay(MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareType)">
            <summary>
            Get display text for share type
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.ContactSharingService.GetStatusDisplay(MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareStatus)">
            <summary>
            Get display text for status
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingEmailService">
            <summary>
            Interface for contact sharing email service
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingEmailService.SendContactShareEmailAsync(MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareResponse)">
            <summary>
            Send contact sharing email to seller
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingEmailService.SendPropertyOfferEmailAsync(MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareResponse)">
            <summary>
            Send property offer email to seller
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingEmailService.SendVisitSchedulingEmailAsync(MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareResponse)">
            <summary>
            Send visit scheduling email to seller
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingEmailService.SendOfferWithVisitEmailAsync(MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareResponse)">
            <summary>
            Send combined offer and visit email to seller
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingEmailService.SendBuyerConfirmationEmailAsync(MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareResponse)">
            <summary>
            Send confirmation email to buyer
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingEmailService.SendSellerResponseEmailAsync(MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareResponse,System.String)">
            <summary>
            Send seller response email to buyer
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingEmailService.SendSellerReminderEmailAsync(MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareResponse)">
            <summary>
            Send reminder email to seller for pending requests
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingEmailService.GenerateEmailTrackingId">
            <summary>
            Generate email tracking ID
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingEmailService.TrackEmailOpenAsync(System.String)">
            <summary>
            Track email open
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingEmailService.TrackEmailClickAsync(System.String,System.String)">
            <summary>
            Track email click
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingService">
            <summary>
            Interface for contact sharing service
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingService.CreateContactShareAsync(System.String,MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.CreateContactShareRequest)">
            <summary>
            Create a new contact share request
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingService.GetContactShareAsync(System.String)">
            <summary>
            Get contact share by ID
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingService.GetContactSharesAsync(System.String,MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareSearchParams)">
            <summary>
            Get contact shares for a user (buyer or seller)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingService.GetPropertyContactSharesAsync(System.String,MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareSearchParams)">
            <summary>
            Get contact shares for a specific property
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingService.UpdateContactShareStatusAsync(System.String,MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareSellerResponse)">
            <summary>
            Update contact share status (seller response)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingService.GetContactShareStatsAsync(System.String)">
            <summary>
            Get contact share statistics for a user
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingService.GetPropertyContactShareStatsAsync(System.String)">
            <summary>
            Get contact share statistics for a property
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingService.CanUserAccessContactShareAsync(System.String,System.String)">
            <summary>
            Check if user can access contact share
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingService.MarkContactShareAsViewedAsync(System.String)">
            <summary>
            Mark contact share as viewed
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingService.DeleteContactShareAsync(System.String)">
            <summary>
            Delete contact share
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingService.GetPendingContactSharesAsync(System.String,System.Int32)">
            <summary>
            Get pending contact shares for seller (for reminders)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingService.SendReminderEmailsAsync">
            <summary>
            Send reminder emails for pending contact shares
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingService.GetAdminContactShareStatsAsync(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Get contact share analytics for admin
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IContactSharingService.GetAllContactSharesAsync(MicroSaasWebApi.Models.SoNoBrokers.ContactSharing.ContactShareSearchParams)">
            <summary>
            Get all contact shares for admin
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IPropertySchedulingService">
            <summary>
            Service interface for property visit scheduling and management
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.VisitSchedulingStats">
            <summary>
            Statistics model for visit scheduling
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.ICalendarInviteService">
            <summary>
            Calendar invite service interface
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IQrCodeService">
            <summary>
            QR code service interface
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.SoNoBrokers.IMessagingService">
            <summary>
            Service for managing conversations and messages
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.MessagingService.SendMessageNotificationAsync(MicroSaasWebApi.Models.SoNoBrokers.Messaging.MessageResponse,System.String)">
            <summary>
            Send real-time message notification via SignalR
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.SoNoBrokers.PropertySchedulingService">
            <summary>
            Service for managing property visit scheduling and verification
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.PropertySearchService.SearchPropertiesUsingStoredProcedureAsync(MicroSaasWebApi.Services.SoNoBrokers.Interfaces.PropertySearchRequest)">
            <summary>
            Example method showing how to use Supabase stored procedures/functions
            This method demonstrates different ways to call database functions
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.PropertySearchService.GetTotalPropertiesCountAsync">
            <summary>
            Example method showing how to call a simple Supabase function without parameters
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.PropertySearchService.UpdatePropertyStatusAsync(System.String,System.String,System.String)">
            <summary>
            Example method showing how to use stored procedures for complex operations
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.PropertyService.GetPropertyImagesAsync(System.String)">
            <summary>
            Helper method to load property images
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.SoNoBrokers.QrCodeService">
            <summary>
            Service for generating and validating QR codes for property visits
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.QrCodeService.GenerateQrCodeAsync(System.String)">
            <summary>
            Generate QR code as base64 string
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.QrCodeService.GenerateQrCodeImageAsync(System.String)">
            <summary>
            Generate QR code as byte array
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.QrCodeService.ValidateQrCodeAsync(System.String,System.String)">
            <summary>
            Validate QR code data against expected data
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.QrCodeService.EncryptQrCodeData(System.String,System.String,System.DateTime)">
            <summary>
            Encrypt QR code data with property and seller information
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.QrCodeService.DecryptQrCodeData(System.String)">
            <summary>
            Decrypt QR code data
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.QrCodeService.GenerateSignature(System.String,System.String,System.DateTime)">
            <summary>
            Generate signature for QR code validation
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.QrCodeService.EncryptString(System.String)">
            <summary>
            Encrypt string using AES
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.QrCodeService.DecryptString(System.String)">
            <summary>
            Decrypt string using AES
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.SoNoBrokers.QrCodeService.GetKeyBytes">
            <summary>
            Get encryption key as byte array
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.SoNoBrokers.CalendarInviteService">
            <summary>
            Calendar invite service for sending visit invitations
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Storage.AzureBlobStorageService">
            <summary>
            Azure Blob Storage service implementing SOLID principles
            Handles CRUD operations for Azure Blob Storage (files, images, videos)
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Storage.AzureBlobStorageService.UploadFileAsync(System.String,System.String,System.IO.Stream,System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Uploads file to Azure Blob Storage
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Storage.AzureBlobStorageService.DownloadFileAsync(System.String,System.String)">
            <summary>
            Downloads file from Azure Blob Storage
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Storage.AzureBlobStorageService.UpdateFileAsync(System.String,System.String,System.IO.Stream,System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Updates file in Azure Blob Storage
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Storage.AzureBlobStorageService.DeleteFileAsync(System.String,System.String)">
            <summary>
            Deletes file from Azure Blob Storage
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Storage.AzureBlobStorageService.ListFilesAsync(System.String,System.String,System.Int32)">
            <summary>
            Lists files in Azure Blob Storage container
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Storage.AzureBlobStorageService.GetFileMetadataAsync(System.String,System.String)">
            <summary>
            Gets file metadata from Azure Blob Storage
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Storage.AzureBlobStorageService.GenerateSasUrlAsync(System.String,System.String,System.TimeSpan,Azure.Storage.Sas.BlobSasPermissions)">
            <summary>
            Generates SAS URL for file access
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Storage.AzureBlobStorageService.CopyFileAsync(System.String,System.String,System.String,System.String)">
            <summary>
            Copies file within Azure Blob Storage
            </summary>
        </member>
        <member name="T:MicroSaasWebApi.Services.Storage.Interfaces.IAzureBlobStorageService">
            <summary>
            Interface for Azure Blob Storage service
            Follows Interface Segregation Principle
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Storage.Interfaces.IAzureBlobStorageService.UploadFileAsync(System.String,System.String,System.IO.Stream,System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Uploads file to Azure Blob Storage
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Storage.Interfaces.IAzureBlobStorageService.DownloadFileAsync(System.String,System.String)">
            <summary>
            Downloads file from Azure Blob Storage
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Storage.Interfaces.IAzureBlobStorageService.UpdateFileAsync(System.String,System.String,System.IO.Stream,System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Updates file in Azure Blob Storage
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Storage.Interfaces.IAzureBlobStorageService.DeleteFileAsync(System.String,System.String)">
            <summary>
            Deletes file from Azure Blob Storage
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Storage.Interfaces.IAzureBlobStorageService.ListFilesAsync(System.String,System.String,System.Int32)">
            <summary>
            Lists files in Azure Blob Storage container
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Storage.Interfaces.IAzureBlobStorageService.GetFileMetadataAsync(System.String,System.String)">
            <summary>
            Gets file metadata from Azure Blob Storage
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Storage.Interfaces.IAzureBlobStorageService.GenerateSasUrlAsync(System.String,System.String,System.TimeSpan,Azure.Storage.Sas.BlobSasPermissions)">
            <summary>
            Generates SAS URL for file access
            </summary>
        </member>
        <member name="M:MicroSaasWebApi.Services.Storage.Interfaces.IAzureBlobStorageService.CopyFileAsync(System.String,System.String,System.String,System.String)">
            <summary>
            Copies file within Azure Blob Storage
            </summary>
        </member>
    </members>
</doc>
