'use client';

import * as React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useClerk } from '@clerk/nextjs'
import { useRouter, usePathname } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useAppContext } from '@/contexts/AppContext'
import { LayoutGroup, motion } from "motion/react"
import { TextRotate } from "@/components/shared/common/TextRotateClient"
import { SignInButton } from '@clerk/nextjs'

import { Button } from '@/components/ui/button'
import { ThemeToggle } from '@/components/shared/common/ThemeToggleClient'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from '@/components/ui/sheet'
import {
  ChevronDown, LogOut, Settings, Home, Globe, Users, ShoppingCart, Sparkles, Menu, X, User
} from 'lucide-react'
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu'
import { cn } from '@/lib/utils'
import { Country, UserRole } from '@prisma/client'

// Language options based on country
const getLanguageOptions = (country: Country) => {
  const languageMap = {
    CA: [
      { code: 'en', name: 'English', flag: '🇨🇦' },
      { code: 'fr', name: 'Français', flag: '🇫🇷' }
    ],
    US: [
      { code: 'en', name: 'English', flag: '🇺🇸' },
      { code: 'es', name: 'Español', flag: '🇪🇸' }
    ],
    UAE: [
      { code: 'en', name: 'English', flag: '🇦🇪' },
      { code: 'ar', name: 'العربية', flag: '🇸🇦' }
    ]
  }
  return languageMap[country] || languageMap.CA
}

// Helper function to build country-specific hrefs
const buildHref = (href: string, country: string): string => {
  return `/${country}${href}`
}

// Role-based settings helper
const getRoleSettingsPath = (role: UserRole, country: string): string => {
  const roleMap: Record<UserRole, string> = {
    'ADMIN': 'admin',
    'PRODUCT': 'product',
    'OPERATOR': 'operator',
    'SERVICE_PROVIDER': 'service-provider',
    'USER': 'user'
  }
  return `/${country}/${roleMap[role] || 'user'}/settings`
}

// Navigation menu data - User type specific
const buyerServices = [
  {
    title: "Property Search",
    href: "/properties",
    description: "Search and browse available properties in your area"
  },
  {
    title: "Open Houses",
    href: "/properties/open-houses",
    description: "Find and attend open house events near you"
  },
  {
    title: "Home Inspection",
    href: "/services/home-inspection",
    description: "Professional home inspection services and scheduling"
  },
  {
    title: "Insurance Services",
    href: "/services/insurance",
    description: "Home insurance quotes and coverage options"
  },
  {
    title: "Moving Services",
    href: "/services/moving",
    description: "Trusted moving companies and relocation assistance"
  },
  {
    title: "Legal Services",
    href: "/services/legal",
    description: "Real estate lawyers and legal document preparation"
  },
  {
    title: "Mortgage Services",
    href: "/services/mortgage",
    description: "Mortgage brokers and lending services"
  }
];

const sellerServices = [
  {
    title: "List Property",
    href: "/list-property",
    description: "List your property directly to buyers and save on commissions"
  },
  ...(process.env.NEXT_PUBLIC_SHOW_CONCIERGE_SERVICES === 'true' ? [{
    title: "Concierge Services",
    href: "/services/concierge",
    description: "White-glove property sale management with expert coordination"
  }] : []),
  {
    title: "AI Property Creator",
    href: "/services/ai-property-creator",
    description: "Create professional property listings with AI-powered tools"
  },
  {
    title: "Photography Services",
    href: "/services/photography",
    description: "Professional real estate photography and virtual tours"
  },
  {
    title: "Staging Services",
    href: "/services/staging",
    description: "Home staging consultation to maximize your property appeal"
  },
  {
    title: "Legal Services",
    href: "/services/legal",
    description: "Real estate lawyers and legal document preparation"
  },
  {
    title: "Marketing Services",
    href: "/services/marketing",
    description: "Digital marketing and listing promotion services"
  },
  {
    title: "Property Appraisal",
    href: "/services/professional-appraisal",
    description: "Professional property appraisal services for accurate valuations"
  }
];

const buyerResources = [
  {
    title: "Buyer's Guide",
    href: "/resources/buyers-guide",
    description: "Complete guide to buying your first home"
  },
  {
    title: "Buying Checklist",
    href: "/resources/buying-checklist",
    description: "Step-by-step checklist for home buyers"
  },
  {
    title: "First-Time Buyer",
    href: "/resources/first-time-buyer",
    description: "Essential information for first-time home buyers"
  },
  {
    title: "Financing Options",
    href: "/resources/financing",
    description: "Understand your mortgage and financing options"
  },
  {
    title: "Closing Costs",
    href: "/resources/closing-costs",
    description: "Calculate and understand closing costs"
  },
  {
    title: "Neighborhood Info",
    href: "/resources/neighborhoods",
    description: "Detailed information about local neighborhoods"
  }
];

const sellerResources = [
  {
    title: "Seller's Guide",
    href: "/resources/seller-guide",
    description: "Complete guide to selling your property"
  },
  {
    title: "Property Valuation",
    href: "/resources/property-valuation",
    description: "Get an accurate valuation of your property"
  },
  {
    title: "Pricing Calculator",
    href: "/resources/pricing-calculator",
    description: "Calculate the optimal price for your property"
  },
  {
    title: "Commission Calculator",
    href: "/resources/commission-calculator",
    description: "Calculate potential savings with SoNoBrokers"
  },
  {
    title: "Tax Calculator",
    href: "/resources/tax-calculator",
    description: "Understand tax implications of selling"
  },
  {
    title: "Market Analysis",
    href: "/resources/market-analysis",
    description: "Current market trends and analysis"
  }
];

// ListItem component for navigation menus
const ListItem = React.forwardRef<
  React.ElementRef<"a">,
  React.ComponentPropsWithoutRef<"a"> & { title: string; href: string; children: React.ReactNode }
>(({ className, title, children, href, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <Link
          ref={ref}
          href={href}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
            {children}
          </p>
        </Link>
      </NavigationMenuLink>
    </li>
  )
})
ListItem.displayName = "ListItem"

// ServiceListItem component for navigation menus with smart authentication
const ServiceListItem = React.forwardRef<
  React.ElementRef<"div">,
  { title: string; href: string; children: React.ReactNode; className?: string; isSignedIn: boolean; country: string }
>(({ className, title, children, href, isSignedIn, country, ...props }, ref) => {
  const router = useRouter()

  const handleClick = () => {
    // Build country-specific href
    let finalHref = href
    if (!href.includes('/ca/') && !href.includes('/us/') && !href.includes('/uae/')) {
      if (href.startsWith('/')) {
        finalHref = `/${country.toLowerCase()}${href}`
      } else {
        finalHref = `/${country.toLowerCase()}/${href}`
      }
    }

    router.push(finalHref)
  }

  // Regular navigation item
  return (
    <li>
      <NavigationMenuLink asChild>
        <div
          ref={ref}
          onClick={handleClick}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
            {children}
          </p>
        </div>
      </NavigationMenuLink>
    </li>
  )
})
ServiceListItem.displayName = "ServiceListItem"

interface HeaderClientProps {
  userType?: 'Buyer' | 'Seller';
}

export function HeaderClient({ userType: propUserType = 'Buyer' }: HeaderClientProps) {
  // Use AppContext for state management
  const {
    userType: contextUserType,
    setUserType: contextSetUserType,
    country: contextCountry,
    setCountry: contextSetCountry,
    isSignedIn: contextIsSignedIn,
    snbUser,
    userRole,
    clearAuthenticationData
  } = useAppContext();

  // Use Clerk hooks only for sign out functionality
  const { signOut } = useClerk();
  const router = useRouter();
  const pathname = usePathname();

  // Use AppContext values exclusively
  const userType = contextUserType || propUserType;
  const setUserType = contextSetUserType || (() => { });
  const country = contextCountry || 'CA';
  const setCountry = contextSetCountry || (() => { });
  const isSignedIn = contextIsSignedIn;

  // State for mobile menu and language
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('en');

  // Get language options for current country
  const languageOptions = getLanguageOptions(country);

  // Check if we're in development mode
  const isDev = process.env.NODE_ENV === 'development';

  // Feature flags
  const isLanguageSelectorEnabled = process.env.NEXT_PUBLIC_ENABLE_LANGUAGE_SELECTOR === 'true';

  // Region check effect - redirect to appropriate country on load
  useEffect(() => {
    const checkRegion = async () => {
      try {
        // Only check region if we're on the root path to avoid infinite redirects
        if (pathname !== '/' && pathname !== '') return;

        // Check if we need to redirect based on region
        const response = await fetch('/api/geo');
        const data = await response.json();

        if (data.country && data.country !== country) {
          const newCountry = data.country as Country;
          setCountry(newCountry);
          // Redirect to country-specific route
          router.push(`/${newCountry.toLowerCase()}`);
        }
      } catch (error) {
        console.error('Region check failed:', error);
      }
    };

    // Only run region check on initial load for root path
    if (pathname === '/' || pathname === '') {
      checkRegion();
    }
  }, [pathname, country, setCountry, router]);

  // Event handlers
  const handleSignOut = async () => {
    try {
      // Clear authentication data from AppContext
      clearAuthenticationData();

      // Sign out from Clerk
      await signOut();

      // Redirect to country-specific landing page
      router.push(`/${country.toLowerCase()}`);
    } catch (error) {
      console.error('Error during sign out:', error);
      // Still redirect even if there's an error
      router.push(`/${country.toLowerCase()}`);
    }
  };

  const handleLanguageChange = (langCode: string) => {
    setCurrentLanguage(langCode);
    // Here you would implement actual language switching logic
  };

  return (
    <motion.header
      className="sticky top-0 z-50 w-full border-b border-border bg-background"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="w-full px-4">
        <div className="flex h-12 items-center">
          {/* Left Side - Logo (Always Left Aligned) */}
          <div className="flex items-center">
            <Link href={buildHref('/', country.toLowerCase())} className="flex items-center gap-0.5 h-8">
              <Image
                src="/logo.svg"
                alt="SoNoBrokers"
                width={32}
                height={32}
                className="h-8 w-8"
              />
              <div className="flex items-center h-8">
                <LayoutGroup>
                  <motion.div className="flex items-center whitespace-pre text-xl font-bold h-8" layout>
                    <motion.span
                      className="text-primary h-8 flex items-center"
                      layout
                      transition={{ type: "spring", damping: 30, stiffness: 400 }}
                    >
                      SoNo{" "}
                    </motion.span>
                    <div className="px-2 py-1 rounded-md flex items-center justify-center h-8 bg-primary text-primary-foreground">
                      <TextRotate
                        words={[
                          "Brokers!",
                          "Brokers",
                        ]}
                        className="overflow-hidden flex items-center justify-center font-bold h-8"
                        duration={2000}
                      />
                    </div>
                  </motion.div>
                </LayoutGroup>
              </div>
            </Link>
          </div>

          {/* Right Side - Everything Else (Always Right Aligned) */}
          <div className="flex items-center ml-auto">
            {/* Desktop Navigation */}
            <NavigationMenu className="hidden lg:flex">
              <NavigationMenuList>
                {/* Dashboard - Only show when signed in */}
                {isSignedIn && (
                  <NavigationMenuItem>
                    <NavigationMenuLink asChild>
                      <Link href={buildHref('/dashboard', country.toLowerCase())} className={navigationMenuTriggerStyle()}>
                        Dashboard
                      </Link>
                    </NavigationMenuLink>
                  </NavigationMenuItem>
                )}

                {/* Services Dropdown */}
                <NavigationMenuItem>
                  <NavigationMenuTrigger>Services</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                      {(userType === 'Buyer' ? buyerServices : sellerServices).map((service) => {
                        const href = buildHref(service.href, country.toLowerCase());

                        // Use ServiceListItem which handles authentication logic internally
                        return (
                          <ServiceListItem
                            key={service.title}
                            title={service.title}
                            href={href}
                            isSignedIn={isSignedIn}
                            country={country}
                          >
                            {service.description}
                          </ServiceListItem>
                        );
                      })}
                    </ul>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                {/* Resources Dropdown */}
                <NavigationMenuItem>
                  <NavigationMenuTrigger>Resources</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                      {(userType === 'Buyer' ? buyerResources : sellerResources).map((resource) => (
                        <ListItem
                          key={resource.title}
                          title={resource.title}
                          href={buildHref(resource.href, country.toLowerCase())}
                        >
                          {resource.description}
                        </ListItem>
                      ))}
                    </ul>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                {/* Advertise Link */}
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link href={buildHref('/advertise', country.toLowerCase())} className={navigationMenuTriggerStyle()}>
                      Advertise
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>

            <ThemeToggle />

            {/* User Type Selector */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  {userType === 'Buyer' ? <ShoppingCart className="h-4 w-4" /> : <Users className="h-4 w-4" />}
                  <span>{userType}</span>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>View As</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setUserType('Buyer')}>
                  <ShoppingCart className="h-4 w-4" />
                  <span>Buyer</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setUserType('Seller')}>
                  <Users className="h-4 w-4" />
                  <span>Seller</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Language Selector - Feature Flag Disabled */}
            {isLanguageSelectorEnabled && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 px-2 text-xs">
                    <Globe className="mr-1 h-3 w-3" />
                    {languageOptions.find(lang => lang.code === currentLanguage)?.flag}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-40">
                  <DropdownMenuLabel>Language</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {languageOptions.map((lang) => (
                    <DropdownMenuItem
                      key={lang.code}
                      onClick={() => handleLanguageChange(lang.code)}
                      className="flex items-center space-x-2"
                    >
                      <span>{lang.flag}</span>
                      <span className="text-xs">{lang.name}</span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {/* Dev Region Tester */}
            {isDev && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 px-2 text-xs">
                    <Settings className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Dev Tools</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/region-tester">Region Tester</Link>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {/* User Menu */}
            {isSignedIn ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 rounded-full p-0">
                    {snbUser?.firstName ? (
                      <div className="h-6 w-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs font-medium">
                        {snbUser.firstName.charAt(0).toUpperCase()}
                      </div>
                    ) : (
                      <User className="h-4 w-4" />
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuLabel>My Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />

                  <DropdownMenuItem asChild>
                    <Link href={buildHref('/dashboard', country.toLowerCase())}>
                      <Home className="mr-2 h-4 w-4" />
                      Dashboard
                    </Link>
                  </DropdownMenuItem>

                  {/* Role-based settings */}
                  {userRole && (
                    <DropdownMenuItem asChild>
                      <Link href={getRoleSettingsPath(userRole, country.toLowerCase())}>
                        <Settings className="mr-2 h-4 w-4" />
                        {userRole === 'ADMIN' ? 'Admin Settings' : 'Settings'}
                      </Link>
                    </DropdownMenuItem>
                  )}

                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleSignOut}>
                    <LogOut className="mr-2 h-4 w-4" />
                    Sign out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <SignInButton mode="modal">
                <Button size="sm" className="h-8 px-3 text-xs">Sign In</Button>
              </SignInButton>
            )}

            {/* Mobile Menu */}
            <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
              <SheetTrigger asChild className="lg:hidden">
                <Button variant="ghost" size="sm">
                  <Menu className="h-4 w-4" />
                </Button>
              </SheetTrigger>

              <SheetContent side="right">
                <div className="flex flex-col h-full">
                  {/* Mobile Header */}
                  <div className="flex items-center justify-between p-4 border-b">
                    <div className="flex items-center">
                      <Image
                        src="/logo.svg"
                        alt="SoNoBrokers"
                        width={24}
                        height={24}
                        className="h-6 w-6"
                      />
                      <span className="font-bold">SoNo</span>
                    </div>
                    <Button variant="ghost" size="sm" onClick={() => setMobileMenuOpen(false)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Mobile Navigation */}
                  <nav className="flex-1 p-4 space-y-4">
                    {/* Dashboard */}
                    {isSignedIn && (
                      <Link
                        href={buildHref('/dashboard', country.toLowerCase())}
                        onClick={() => setMobileMenuOpen(false)}
                        className="flex items-center gap-3 px-3 py-2 rounded-md hover:bg-accent hover:text-accent-foreground transition-colors"
                      >
                        <Home className="h-4 w-4" />
                        <span>Dashboard</span>
                      </Link>
                    )}

                    {/* Services */}
                    <div className="space-y-2">
                      <h3 className="font-semibold text-sm text-muted-foreground px-3 py-2">Services</h3>
                      <div className="space-y-1">
                        {(userType === 'Buyer' ? buyerServices : sellerServices).map((service) => {
                          const href = buildHref(service.href, country.toLowerCase());

                          return (
                            <div
                              key={service.title}
                              onClick={() => {
                                setMobileMenuOpen(false)
                                router.push(href)
                              }}
                              className="flex items-center gap-3 px-3 py-2 rounded-md hover:bg-accent hover:text-accent-foreground transition-colors cursor-pointer"
                            >
                              <span>{service.title}</span>
                            </div>
                          );
                        })}
                      </div>
                    </div>

                    {/* Resources */}
                    <div className="space-y-2">
                      <h3 className="font-semibold text-sm text-muted-foreground px-3 py-2">Resources</h3>
                      <div className="space-y-1">
                        {(userType === 'Buyer' ? buyerResources : sellerResources).map((resource) => (
                          <Link
                            key={resource.title}
                            href={buildHref(resource.href, country.toLowerCase())}
                            onClick={() => setMobileMenuOpen(false)}
                            className="flex items-center gap-3 px-3 py-2 rounded-md hover:bg-accent hover:text-accent-foreground transition-colors"
                          >
                            <span>{resource.title}</span>
                          </Link>
                        ))}
                      </div>
                    </div>

                    {/* Advertise */}
                    <Link
                      href={buildHref('/advertise', country.toLowerCase())}
                      onClick={() => setMobileMenuOpen(false)}
                      className="flex items-center gap-3 px-3 py-2 rounded-md hover:bg-accent hover:text-accent-foreground transition-colors"
                    >
                      <Sparkles className="h-4 w-4" />
                      <span>Advertise</span>
                    </Link>
                  </nav>

                  {/* Mobile Footer */}
                  <div className="p-4 border-t">
                    {/* User Type Toggle */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm">
                          {userType === 'Buyer' ? <ShoppingCart className="h-4 w-4" /> : <Users className="h-4 w-4" />}
                          <span>{userType}</span>
                          <ChevronDown className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setUserType('Buyer')}>
                          <ShoppingCart className="h-4 w-4" />
                          <span>Buyer</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setUserType('Seller')}>
                          <Users className="h-4 w-4" />
                          <span>Seller</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>

                    {/* Language Selector - Feature Flag Disabled */}
                    {isLanguageSelectorEnabled && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Globe className="h-4 w-4" />
                            <span>{country}</span>
                            <ChevronDown className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {languageOptions.map((lang) => (
                            <DropdownMenuItem
                              key={lang.code}
                              onClick={() => handleLanguageChange(lang.code)}
                            >
                              <span>{lang.flag}</span>
                              <span>{lang.name}</span>
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}

                    {/* Auth */}
                    {isSignedIn ? (
                      <div>
                        {snbUser?.email === '<EMAIL>' && (
                          <Link href={`/${country.toLowerCase()}/admin/settings`}>
                            <Button variant="ghost" size="sm">
                              <Settings className="h-4 w-4" />
                              Admin Settings
                            </Button>
                          </Link>
                        )}

                        {userRole && (
                          <Link href={getRoleSettingsPath(userRole, country.toLowerCase())}>
                            <Button variant="ghost" size="sm">
                              <Settings className="h-4 w-4" />
                              Settings
                            </Button>
                          </Link>
                        )}

                        <Button variant="ghost" size="sm" onClick={handleSignOut}>
                          <LogOut className="h-4 w-4" />
                          Sign out
                        </Button>
                      </div>
                    ) : (
                      <SignInButton mode="modal">
                        <Button size="sm">
                          <Sparkles className="h-4 w-4" />
                          Get Started
                        </Button>
                      </SignInButton>
                    )}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </motion.header>
  );
}