using Dapper;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.SoNoBrokers.Messaging;
using MicroSaasWebApi.Hubs;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace MicroSaasWebApi.Services.SoNoBrokers
{
    /// <summary>
    /// Service for managing conversations and messages
    /// </summary>
    public interface IMessagingService
    {
        Task<ConversationResponse> CreateConversationAsync(string buyerId, CreateConversationRequest request);
        Task<ConversationSearchResponse> GetConversationsAsync(string userId, ConversationSearchParams searchParams);
        Task<ConversationResponse?> GetConversationAsync(string conversationId, string userId);
        Task<MessageResponse> SendMessageAsync(string senderId, CreateMessageRequest request);
        Task<MessageSearchResponse> GetMessagesAsync(string conversationId, string userId, MessageSearchParams searchParams);
        Task<bool> MarkMessagesAsReadAsync(string userId, string conversationId, List<string>? messageIds = null);
        Task<MessageStats> GetMessageStatsAsync(string userId);
        Task<bool> UpdateConversationAsync(string conversationId, string userId, UpdateConversationRequest request);
        Task<bool> DeleteConversationAsync(string conversationId, string userId);
        Task<ConversationSearchResponse> GetAdminConversationsAsync(ConversationSearchParams searchParams);
        Task<bool> CanUserAccessConversationAsync(string userId, string conversationId);
    }

    public class MessagingService : IMessagingService
    {
        private readonly MicroSaasWebApi.App.Context.IDapperDbContext _context;
        private readonly ILogger<MessagingService> _logger;
        private readonly IHubContext<MessagingHub> _hubContext;

        public MessagingService(
            IDapperDbContext context,
            ILogger<MessagingService> logger,
            IHubContext<MessagingHub> hubContext)
        {
            _context = context;
            _logger = logger;
            _hubContext = hubContext;
        }

        public async Task<ConversationResponse> CreateConversationAsync(string buyerId, CreateConversationRequest request)
        {
            try
            {
                using var connection = _context.CreateConnection();
                using var transaction = connection.BeginTransaction();

                // Check if conversation already exists between buyer and seller for this property
                var existingConversation = await connection.QueryFirstOrDefaultAsync<string>(
                    @"SELECT ""id"" FROM ""Conversation"" 
                      WHERE ""propertyId"" = @PropertyId 
                      AND ""buyerId"" = @BuyerId 
                      AND ""sellerId"" = @SellerId 
                      AND ""isActive"" = true",
                    new { PropertyId = request.PropertyId, BuyerId = buyerId, SellerId = request.SellerId },
                    transaction);

                string conversationId;

                if (existingConversation != null)
                {
                    conversationId = existingConversation;
                    _logger.LogInformation("Using existing conversation {ConversationId}", conversationId);
                }
                else
                {
                    // Create new conversation
                    conversationId = Guid.NewGuid().ToString();
                    await connection.ExecuteAsync(
                        @"INSERT INTO ""Conversation"" (""id"", ""propertyId"", ""buyerId"", ""sellerId"", ""subject"", ""isActive"", ""createdAt"", ""updatedAt"")
                          VALUES (@Id, @PropertyId, @BuyerId, @SellerId, @Subject, true, @CreatedAt, @UpdatedAt)",
                        new
                        {
                            Id = conversationId,
                            PropertyId = request.PropertyId,
                            BuyerId = buyerId,
                            SellerId = request.SellerId,
                            Subject = request.Subject ?? "Property Inquiry",
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        },
                        transaction);

                    _logger.LogInformation("Created new conversation {ConversationId}", conversationId);
                }

                // Send initial message
                var messageId = Guid.NewGuid().ToString();
                await connection.ExecuteAsync(
                    @"INSERT INTO ""Message"" (""id"", ""conversationId"", ""senderId"", ""content"", ""messageType"", ""isRead"", ""createdAt"")
                      VALUES (@Id, @ConversationId, @SenderId, @Content, 'text', false, @CreatedAt)",
                    new
                    {
                        Id = messageId,
                        ConversationId = conversationId,
                        SenderId = buyerId,
                        Content = request.InitialMessage,
                        CreatedAt = DateTime.UtcNow
                    },
                    transaction);

                // Update conversation last message time
                await connection.ExecuteAsync(
                    @"UPDATE ""Conversation"" SET ""lastMessageAt"" = @LastMessageAt, ""updatedAt"" = @UpdatedAt 
                      WHERE ""id"" = @ConversationId",
                    new { ConversationId = conversationId, LastMessageAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                    transaction);

                transaction.Commit();

                // Return the conversation details
                return await GetConversationAsync(conversationId, buyerId)
                    ?? throw new InvalidOperationException("Failed to retrieve created conversation");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating conversation for buyer {BuyerId} and property {PropertyId}",
                    buyerId, request.PropertyId);
                throw;
            }
        }

        public async Task<ConversationSearchResponse> GetConversationsAsync(string userId, ConversationSearchParams searchParams)
        {
            try
            {
                using var connection = _context.CreateConnection();

                var whereConditions = new List<string>
                {
                    @"(c.""buyerId"" = @UserId OR c.""sellerId"" = @UserId OR c.""agentId"" = @UserId)"
                };

                var parameters = new DynamicParameters();
                parameters.Add("UserId", userId);
                parameters.Add("Offset", (searchParams.Page - 1) * searchParams.Limit);
                parameters.Add("Limit", searchParams.Limit);

                if (searchParams.PropertyId != null)
                {
                    whereConditions.Add(@"c.""propertyId"" = @PropertyId");
                    parameters.Add("PropertyId", searchParams.PropertyId);
                }

                if (searchParams.IsActive.HasValue)
                {
                    whereConditions.Add(@"c.""isActive"" = @IsActive");
                    parameters.Add("IsActive", searchParams.IsActive.Value);
                }

                if (searchParams.FromDate.HasValue)
                {
                    whereConditions.Add(@"c.""createdAt"" >= @FromDate");
                    parameters.Add("FromDate", searchParams.FromDate.Value);
                }

                if (searchParams.ToDate.HasValue)
                {
                    whereConditions.Add(@"c.""createdAt"" <= @ToDate");
                    parameters.Add("ToDate", searchParams.ToDate.Value);
                }

                if (!string.IsNullOrEmpty(searchParams.Search))
                {
                    whereConditions.Add(@"(c.""subject"" ILIKE @Search OR p.""title"" ILIKE @Search)");
                    parameters.Add("Search", $"%{searchParams.Search}%");
                }

                var whereClause = string.Join(" AND ", whereConditions);
                var orderBy = GetOrderByClause(searchParams.SortBy, searchParams.SortOrder);

                var query = $@"
                    SELECT 
                        c.""id"",
                        c.""propertyId"",
                        p.""title"" as PropertyTitle,
                        p.""address""->>'formatted' as PropertyAddress,
                        p.""price"" as PropertyPrice,
                        c.""subject"",
                        c.""lastMessageAt"",
                        c.""isActive"",
                        c.""createdAt"",
                        CASE 
                            WHEN c.""buyerId"" = @UserId THEN c.""sellerId""
                            WHEN c.""sellerId"" = @UserId THEN c.""buyerId""
                            ELSE c.""agentId""
                        END as OtherParticipantId,
                        CASE 
                            WHEN c.""buyerId"" = @UserId THEN seller.""fullName""
                            WHEN c.""sellerId"" = @UserId THEN buyer.""fullName""
                            ELSE agent.""fullName""
                        END as OtherParticipantName,
                        CASE 
                            WHEN c.""buyerId"" = @UserId THEN seller.""email""
                            WHEN c.""sellerId"" = @UserId THEN buyer.""email""
                            ELSE agent.""email""
                        END as OtherParticipantEmail,
                        lm.""content"" as LastMessageContent,
                        lm.""senderId"" as LastMessageSenderId,
                        COALESCE(unread.unread_count, 0) as UnreadCount
                    FROM ""Conversation"" c
                    LEFT JOIN ""Property"" p ON c.""propertyId"" = p.""id""
                    LEFT JOIN ""User"" buyer ON c.""buyerId"" = buyer.""id""
                    LEFT JOIN ""User"" seller ON c.""sellerId"" = seller.""id""
                    LEFT JOIN ""User"" agent ON c.""agentId"" = agent.""id""
                    LEFT JOIN ""Message"" lm ON lm.""id"" = (
                        SELECT ""id"" FROM ""Message"" 
                        WHERE ""conversationId"" = c.""id"" 
                        ORDER BY ""createdAt"" DESC 
                        LIMIT 1
                    )
                    LEFT JOIN (
                        SELECT ""conversationId"", COUNT(*) as unread_count
                        FROM ""Message""
                        WHERE ""senderId"" != @UserId AND ""isRead"" = false
                        GROUP BY ""conversationId""
                    ) unread ON unread.""conversationId"" = c.""id""
                    WHERE {whereClause}
                    ORDER BY {orderBy}
                    OFFSET @Offset LIMIT @Limit";

                var conversations = await connection.QueryAsync<ConversationListItem>(query, parameters);

                // Get total count
                var countQuery = $@"
                    SELECT COUNT(*)
                    FROM ""Conversation"" c
                    LEFT JOIN ""Property"" p ON c.""propertyId"" = p.""id""
                    WHERE {whereClause}";

                var total = await connection.QuerySingleAsync<int>(countQuery, parameters);

                return new ConversationSearchResponse
                {
                    Conversations = conversations.ToList(),
                    Total = total,
                    Page = searchParams.Page,
                    TotalPages = (int)Math.Ceiling((double)total / searchParams.Limit),
                    HasMore = (searchParams.Page * searchParams.Limit) < total
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting conversations for user {UserId}", userId);
                throw;
            }
        }

        public async Task<ConversationResponse?> GetConversationAsync(string conversationId, string userId)
        {
            try
            {
                using var connection = _context.CreateConnection();

                // Check if user has access to this conversation
                if (!await CanUserAccessConversationAsync(userId, conversationId))
                {
                    return null;
                }

                var query = @"
                    SELECT 
                        c.""id"",
                        c.""propertyId"",
                        p.""title"" as PropertyTitle,
                        p.""address""->>'formatted' as PropertyAddress,
                        p.""price"" as PropertyPrice,
                        c.""buyerId"",
                        buyer.""fullName"" as BuyerName,
                        buyer.""email"" as BuyerEmail,
                        c.""sellerId"",
                        seller.""fullName"" as SellerName,
                        seller.""email"" as SellerEmail,
                        c.""agentId"",
                        agent.""fullName"" as AgentName,
                        c.""subject"",
                        c.""lastMessageAt"",
                        c.""isActive"",
                        c.""createdAt""
                    FROM ""Conversation"" c
                    LEFT JOIN ""Property"" p ON c.""propertyId"" = p.""id""
                    LEFT JOIN ""User"" buyer ON c.""buyerId"" = buyer.""id""
                    LEFT JOIN ""User"" seller ON c.""sellerId"" = seller.""id""
                    LEFT JOIN ""User"" agent ON c.""agentId"" = agent.""id""
                    WHERE c.""id"" = @ConversationId";

                var conversation = await connection.QueryFirstOrDefaultAsync<ConversationResponse>(query,
                    new { ConversationId = conversationId });

                if (conversation == null)
                {
                    return null;
                }

                // Get unread count for this user
                var unreadCount = await connection.QuerySingleAsync<int>(
                    @"SELECT COUNT(*) FROM ""Message"" 
                      WHERE ""conversationId"" = @ConversationId 
                      AND ""senderId"" != @UserId 
                      AND ""isRead"" = false",
                    new { ConversationId = conversationId, UserId = userId });

                conversation.UnreadCount = unreadCount;

                return conversation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting conversation {ConversationId} for user {UserId}",
                    conversationId, userId);
                throw;
            }
        }

        private string GetOrderByClause(string? sortBy, string? sortOrder)
        {
            var validSortFields = new Dictionary<string, string>
            {
                { "lastMessageAt", @"c.""lastMessageAt""" },
                { "createdAt", @"c.""createdAt""" },
                { "subject", @"c.""subject""" },
                { "propertyTitle", @"p.""title""" }
            };

            var field = validSortFields.GetValueOrDefault(sortBy ?? "lastMessageAt", @"c.""lastMessageAt""");
            var order = sortOrder?.ToLower() == "asc" ? "ASC" : "DESC";

            return $"{field} {order} NULLS LAST";
        }

        public async Task<MessageResponse> SendMessageAsync(string senderId, CreateMessageRequest request)
        {
            try
            {
                using var connection = _context.CreateConnection();
                using var transaction = connection.BeginTransaction();

                // Verify user has access to the conversation
                if (!await CanUserAccessConversationAsync(senderId, request.ConversationId))
                {
                    throw new UnauthorizedAccessException("User does not have access to this conversation");
                }

                // Create the message
                var messageId = Guid.NewGuid().ToString();
                await connection.ExecuteAsync(
                    @"INSERT INTO ""Message"" (""id"", ""conversationId"", ""senderId"", ""content"", ""messageType"", ""isRead"", ""createdAt"")
                      VALUES (@Id, @ConversationId, @SenderId, @Content, @MessageType, false, @CreatedAt)",
                    new
                    {
                        Id = messageId,
                        ConversationId = request.ConversationId,
                        SenderId = senderId,
                        Content = request.Content,
                        MessageType = request.MessageType,
                        CreatedAt = DateTime.UtcNow
                    },
                    transaction);

                // Update conversation last message time
                await connection.ExecuteAsync(
                    @"UPDATE ""Conversation"" SET ""lastMessageAt"" = @LastMessageAt, ""updatedAt"" = @UpdatedAt
                      WHERE ""id"" = @ConversationId",
                    new { ConversationId = request.ConversationId, LastMessageAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                    transaction);

                transaction.Commit();

                // Return the message details
                var query = @"
                    SELECT
                        m.""id"",
                        m.""conversationId"",
                        m.""senderId"",
                        u.""fullName"" as SenderName,
                        u.""email"" as SenderEmail,
                        m.""content"",
                        m.""messageType"",
                        m.""isRead"",
                        m.""readAt"",
                        m.""createdAt""
                    FROM ""Message"" m
                    JOIN ""User"" u ON m.""senderId"" = u.""id""
                    WHERE m.""id"" = @MessageId";

                var message = await connection.QueryFirstAsync<MessageResponse>(query, new { MessageId = messageId });
                message.IsOwnMessage = true;

                // Send real-time notification
                await SendMessageNotificationAsync(message, request.ConversationId);

                return message;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message from user {SenderId} to conversation {ConversationId}",
                    senderId, request.ConversationId);
                throw;
            }
        }

        public async Task<MessageSearchResponse> GetMessagesAsync(string conversationId, string userId, MessageSearchParams searchParams)
        {
            try
            {
                using var connection = _context.CreateConnection();

                // Verify user has access to the conversation
                if (!await CanUserAccessConversationAsync(userId, conversationId))
                {
                    throw new UnauthorizedAccessException("User does not have access to this conversation");
                }

                var whereConditions = new List<string> { @"m.""conversationId"" = @ConversationId" };
                var parameters = new DynamicParameters();
                parameters.Add("ConversationId", conversationId);
                parameters.Add("UserId", userId);
                parameters.Add("Offset", (searchParams.Page - 1) * searchParams.Limit);
                parameters.Add("Limit", searchParams.Limit);

                if (!string.IsNullOrEmpty(searchParams.Search))
                {
                    whereConditions.Add(@"m.""content"" ILIKE @Search");
                    parameters.Add("Search", $"%{searchParams.Search}%");
                }

                if (searchParams.FromDate.HasValue)
                {
                    whereConditions.Add(@"m.""createdAt"" >= @FromDate");
                    parameters.Add("FromDate", searchParams.FromDate.Value);
                }

                if (searchParams.ToDate.HasValue)
                {
                    whereConditions.Add(@"m.""createdAt"" <= @ToDate");
                    parameters.Add("ToDate", searchParams.ToDate.Value);
                }

                if (searchParams.IsRead.HasValue)
                {
                    whereConditions.Add(@"m.""isRead"" = @IsRead");
                    parameters.Add("IsRead", searchParams.IsRead.Value);
                }

                var whereClause = string.Join(" AND ", whereConditions);
                var orderBy = GetMessageOrderByClause(searchParams.SortBy, searchParams.SortOrder);

                var query = $@"
                    SELECT
                        m.""id"",
                        m.""conversationId"",
                        m.""senderId"",
                        u.""fullName"" as SenderName,
                        u.""email"" as SenderEmail,
                        m.""content"",
                        m.""messageType"",
                        m.""isRead"",
                        m.""readAt"",
                        m.""createdAt"",
                        CASE WHEN m.""senderId"" = @UserId THEN true ELSE false END as IsOwnMessage
                    FROM ""Message"" m
                    JOIN ""User"" u ON m.""senderId"" = u.""id""
                    WHERE {whereClause}
                    ORDER BY {orderBy}
                    OFFSET @Offset LIMIT @Limit";

                var messages = await connection.QueryAsync<MessageResponse>(query, parameters);

                // Get total count
                var countQuery = $@"
                    SELECT COUNT(*)
                    FROM ""Message"" m
                    WHERE {whereClause}";

                var total = await connection.QuerySingleAsync<int>(countQuery, parameters);

                return new MessageSearchResponse
                {
                    Messages = messages.ToList(),
                    Total = total,
                    Page = searchParams.Page,
                    TotalPages = (int)Math.Ceiling((double)total / searchParams.Limit),
                    HasMore = (searchParams.Page * searchParams.Limit) < total
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting messages for conversation {ConversationId} and user {UserId}",
                    conversationId, userId);
                throw;
            }
        }

        public async Task<bool> MarkMessagesAsReadAsync(string userId, string conversationId, List<string>? messageIds = null)
        {
            try
            {
                using var connection = _context.CreateConnection();

                // Verify user has access to the conversation
                if (!await CanUserAccessConversationAsync(userId, conversationId))
                {
                    return false;
                }

                string query;
                object parameters;

                if (messageIds != null && messageIds.Any())
                {
                    // Mark specific messages as read
                    query = @"UPDATE ""Message""
                             SET ""isRead"" = true, ""readAt"" = @ReadAt
                             WHERE ""conversationId"" = @ConversationId
                             AND ""id"" = ANY(@MessageIds)
                             AND ""senderId"" != @UserId
                             AND ""isRead"" = false";
                    parameters = new { ConversationId = conversationId, MessageIds = messageIds.ToArray(), UserId = userId, ReadAt = DateTime.UtcNow };
                }
                else
                {
                    // Mark all unread messages in conversation as read
                    query = @"UPDATE ""Message""
                             SET ""isRead"" = true, ""readAt"" = @ReadAt
                             WHERE ""conversationId"" = @ConversationId
                             AND ""senderId"" != @UserId
                             AND ""isRead"" = false";
                    parameters = new { ConversationId = conversationId, UserId = userId, ReadAt = DateTime.UtcNow };
                }

                var rowsAffected = await connection.ExecuteAsync(query, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking messages as read for user {UserId} in conversation {ConversationId}",
                    userId, conversationId);
                return false;
            }
        }

        public async Task<MessageStats> GetMessageStatsAsync(string userId)
        {
            try
            {
                using var connection = _context.CreateConnection();

                var query = @"
                    SELECT
                        COUNT(DISTINCT c.""id"") as TotalConversations,
                        COUNT(DISTINCT CASE WHEN c.""isActive"" = true THEN c.""id"" END) as ActiveConversations,
                        COUNT(m.""id"") as TotalMessages,
                        COUNT(CASE WHEN m.""senderId"" != @UserId AND m.""isRead"" = false THEN m.""id"" END) as UnreadMessages,
                        MAX(m.""createdAt"") as LastMessageAt
                    FROM ""Conversation"" c
                    LEFT JOIN ""Message"" m ON c.""id"" = m.""conversationId""
                    WHERE (c.""buyerId"" = @UserId OR c.""sellerId"" = @UserId OR c.""agentId"" = @UserId)";

                var stats = await connection.QueryFirstAsync<MessageStats>(query, new { UserId = userId });

                // Get conversation-specific stats
                var conversationStatsQuery = @"
                    SELECT
                        c.""id"" as ConversationId,
                        p.""title"" as PropertyTitle,
                        COUNT(m.""id"") as MessageCount,
                        COUNT(CASE WHEN m.""senderId"" != @UserId AND m.""isRead"" = false THEN m.""id"" END) as UnreadCount,
                        MAX(m.""createdAt"") as LastMessageAt,
                        (SELECT u.""fullName"" FROM ""User"" u JOIN ""Message"" lm ON u.""id"" = lm.""senderId""
                         WHERE lm.""conversationId"" = c.""id"" ORDER BY lm.""createdAt"" DESC LIMIT 1) as LastMessageSender
                    FROM ""Conversation"" c
                    LEFT JOIN ""Property"" p ON c.""propertyId"" = p.""id""
                    LEFT JOIN ""Message"" m ON c.""id"" = m.""conversationId""
                    WHERE (c.""buyerId"" = @UserId OR c.""sellerId"" = @UserId OR c.""agentId"" = @UserId)
                    AND c.""isActive"" = true
                    GROUP BY c.""id"", p.""title""
                    ORDER BY MAX(m.""createdAt"") DESC NULLS LAST";

                var conversationStats = await connection.QueryAsync<ConversationStats>(conversationStatsQuery, new { UserId = userId });
                stats.ConversationStats = conversationStats.ToList();

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting message stats for user {UserId}", userId);
                throw;
            }
        }

        public async Task<bool> UpdateConversationAsync(string conversationId, string userId, UpdateConversationRequest request)
        {
            try
            {
                using var connection = _context.CreateConnection();

                // Verify user has access to the conversation
                if (!await CanUserAccessConversationAsync(userId, conversationId))
                {
                    return false;
                }

                var updateFields = new List<string>();
                var parameters = new DynamicParameters();
                parameters.Add("ConversationId", conversationId);
                parameters.Add("UpdatedAt", DateTime.UtcNow);

                if (!string.IsNullOrEmpty(request.Subject))
                {
                    updateFields.Add(@"""subject"" = @Subject");
                    parameters.Add("Subject", request.Subject);
                }

                if (request.IsActive.HasValue)
                {
                    updateFields.Add(@"""isActive"" = @IsActive");
                    parameters.Add("IsActive", request.IsActive.Value);
                }

                if (!updateFields.Any())
                {
                    return false;
                }

                updateFields.Add(@"""updatedAt"" = @UpdatedAt");

                var query = $@"UPDATE ""Conversation"" SET {string.Join(", ", updateFields)} WHERE ""id"" = @ConversationId";
                var rowsAffected = await connection.ExecuteAsync(query, parameters);

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating conversation {ConversationId} for user {UserId}",
                    conversationId, userId);
                return false;
            }
        }

        public async Task<bool> DeleteConversationAsync(string conversationId, string userId)
        {
            try
            {
                using var connection = _context.CreateConnection();

                // Verify user has access to the conversation
                if (!await CanUserAccessConversationAsync(userId, conversationId))
                {
                    return false;
                }

                // Soft delete by setting isActive to false
                var rowsAffected = await connection.ExecuteAsync(
                    @"UPDATE ""Conversation"" SET ""isActive"" = false, ""updatedAt"" = @UpdatedAt WHERE ""id"" = @ConversationId",
                    new { ConversationId = conversationId, UpdatedAt = DateTime.UtcNow });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting conversation {ConversationId} for user {UserId}",
                    conversationId, userId);
                return false;
            }
        }

        public async Task<ConversationSearchResponse> GetAdminConversationsAsync(ConversationSearchParams searchParams)
        {
            try
            {
                using var connection = _context.CreateConnection();

                var whereConditions = new List<string>();
                var parameters = new DynamicParameters();
                parameters.Add("Offset", (searchParams.Page - 1) * searchParams.Limit);
                parameters.Add("Limit", searchParams.Limit);

                if (searchParams.PropertyId != null)
                {
                    whereConditions.Add(@"c.""propertyId"" = @PropertyId");
                    parameters.Add("PropertyId", searchParams.PropertyId);
                }

                if (searchParams.UserId != null)
                {
                    whereConditions.Add(@"(c.""buyerId"" = @UserId OR c.""sellerId"" = @UserId OR c.""agentId"" = @UserId)");
                    parameters.Add("UserId", searchParams.UserId);
                }

                if (searchParams.IsActive.HasValue)
                {
                    whereConditions.Add(@"c.""isActive"" = @IsActive");
                    parameters.Add("IsActive", searchParams.IsActive.Value);
                }

                if (!string.IsNullOrEmpty(searchParams.Search))
                {
                    whereConditions.Add(@"(c.""subject"" ILIKE @Search OR p.""title"" ILIKE @Search OR buyer.""fullName"" ILIKE @Search OR seller.""fullName"" ILIKE @Search)");
                    parameters.Add("Search", $"%{searchParams.Search}%");
                }

                var whereClause = whereConditions.Any() ? "WHERE " + string.Join(" AND ", whereConditions) : "";
                var orderBy = GetOrderByClause(searchParams.SortBy, searchParams.SortOrder);

                var query = $@"
                    SELECT
                        c.""id"",
                        c.""propertyId"",
                        p.""title"" as PropertyTitle,
                        p.""address""->>'formatted' as PropertyAddress,
                        p.""price"" as PropertyPrice,
                        c.""buyerId"" as OtherParticipantId,
                        buyer.""fullName"" as OtherParticipantName,
                        buyer.""email"" as OtherParticipantEmail,
                        c.""subject"",
                        c.""lastMessageAt"",
                        c.""isActive"",
                        c.""createdAt"",
                        lm.""content"" as LastMessageContent,
                        lm.""senderId"" as LastMessageSenderId,
                        COALESCE(unread.unread_count, 0) as UnreadCount
                    FROM ""Conversation"" c
                    LEFT JOIN ""Property"" p ON c.""propertyId"" = p.""id""
                    LEFT JOIN ""User"" buyer ON c.""buyerId"" = buyer.""id""
                    LEFT JOIN ""User"" seller ON c.""sellerId"" = seller.""id""
                    LEFT JOIN ""Message"" lm ON lm.""id"" = (
                        SELECT ""id"" FROM ""Message""
                        WHERE ""conversationId"" = c.""id""
                        ORDER BY ""createdAt"" DESC
                        LIMIT 1
                    )
                    LEFT JOIN (
                        SELECT ""conversationId"", COUNT(*) as unread_count
                        FROM ""Message""
                        WHERE ""isRead"" = false
                        GROUP BY ""conversationId""
                    ) unread ON unread.""conversationId"" = c.""id""
                    {whereClause}
                    ORDER BY {orderBy}
                    OFFSET @Offset LIMIT @Limit";

                var conversations = await connection.QueryAsync<ConversationListItem>(query, parameters);

                // Get total count
                var countQuery = $@"
                    SELECT COUNT(*)
                    FROM ""Conversation"" c
                    LEFT JOIN ""Property"" p ON c.""propertyId"" = p.""id""
                    LEFT JOIN ""User"" buyer ON c.""buyerId"" = buyer.""id""
                    LEFT JOIN ""User"" seller ON c.""sellerId"" = seller.""id""
                    {whereClause}";

                var total = await connection.QuerySingleAsync<int>(countQuery, parameters);

                return new ConversationSearchResponse
                {
                    Conversations = conversations.ToList(),
                    Total = total,
                    Page = searchParams.Page,
                    TotalPages = (int)Math.Ceiling((double)total / searchParams.Limit),
                    HasMore = (searchParams.Page * searchParams.Limit) < total
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting admin conversations");
                throw;
            }
        }

        private string GetMessageOrderByClause(string? sortBy, string? sortOrder)
        {
            var validSortFields = new Dictionary<string, string>
            {
                { "createdAt", @"m.""createdAt""" },
                { "content", @"m.""content""" },
                { "sender", @"u.""fullName""" }
            };

            var field = validSortFields.GetValueOrDefault(sortBy ?? "createdAt", @"m.""createdAt""");
            var order = sortOrder?.ToLower() == "desc" ? "DESC" : "ASC";

            return $"{field} {order}";
        }

        public async Task<bool> CanUserAccessConversationAsync(string userId, string conversationId)
        {
            try
            {
                using var connection = _context.CreateConnection();

                var count = await connection.QuerySingleAsync<int>(
                    @"SELECT COUNT(*) FROM ""Conversation""
                      WHERE ""id"" = @ConversationId
                      AND (""buyerId"" = @UserId OR ""sellerId"" = @UserId OR ""agentId"" = @UserId)",
                    new { ConversationId = conversationId, UserId = userId });

                return count > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking conversation access for user {UserId} and conversation {ConversationId}",
                    userId, conversationId);
                return false;
            }
        }

        /// <summary>
        /// Send real-time message notification via SignalR
        /// </summary>
        private async Task SendMessageNotificationAsync(MessageResponse message, string conversationId)
        {
            try
            {
                using var connection = _context.CreateConnection();

                // Get conversation participants
                var participants = await connection.QueryAsync<string>(
                    @"SELECT DISTINCT unnest(ARRAY[""buyerId"", ""sellerId"", ""agentId""]) as participant_id
                      FROM ""Conversation""
                      WHERE ""id"" = @ConversationId
                      AND unnest(ARRAY[""buyerId"", ""sellerId"", ""agentId""]) IS NOT NULL",
                    new { ConversationId = conversationId });

                // Get property info for notification
                var propertyInfo = await connection.QueryFirstOrDefaultAsync<(string?, string?)>(
                    @"SELECT p.""id"", p.""title""
                      FROM ""Conversation"" c
                      LEFT JOIN ""Property"" p ON c.""propertyId"" = p.""id""
                      WHERE c.""id"" = @ConversationId",
                    new { ConversationId = conversationId });

                var notification = new MessageNotification
                {
                    MessageId = message.Id,
                    ConversationId = conversationId,
                    SenderId = message.SenderId,
                    SenderName = message.SenderName,
                    Content = message.Content,
                    PropertyId = propertyInfo.Item1,
                    PropertyTitle = propertyInfo.Item2,
                    CreatedAt = message.CreatedAt,
                    RecipientIds = participants.Where(p => p != message.SenderId).ToList()
                };

                // Send via SignalR
                await MessagingHub.SendMessageNotification(_hubContext, notification);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message notification for message {MessageId}", message.Id);
                // Don't throw - this is a non-critical feature
            }
        }
    }
}
