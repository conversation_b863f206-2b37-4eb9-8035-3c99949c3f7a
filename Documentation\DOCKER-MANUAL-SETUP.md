# 🐳 SoNoBrokers Development & Docker Deployment Guide

This guide covers manual development setup and Docker deployment to Vercel (React) and Azure Portal (Web API).

## 📋 Prerequisites

- **Node.js 20+** for React development
- **.NET 9 SDK** for Web API development
- **Docker Desktop** installed and running
- **Vercel CLI** for deployment
- **Azure CLI** for deployment
- **PowerShell** (Windows) or **Terminal** (macOS/Linux)
- **Git** for version control

## 🛠️ Part 1: Manual Development Setup

### 1.1 Start React Application Manually

#### Navigate and Install
```powershell
cd C:\Projects\SoNoBrokersRoot\SoNoBrokers
npm install
```

#### Start Development Server
```powershell
npm run dev
```

#### Verify React Application
- **URL**: http://localhost:3000
- **Environment**: Uses existing `.env` file
- **API Target**: `NEXT_PUBLIC_API_BASE_URL=http://localhost:7163`

### 1.2 Start .NET Web API Manually

#### Navigate and Restore
```powershell
cd C:\Projects\SoNoBrokersRoot\\MicroSaasWebApi.App
dotnet restoreSoNoBrokersWebApi
```

#### Start Development Server
```powershell
dotnet run --urls "http://localhost:7163"
```

#### Verify .NET API Application
- **API URL**: http://localhost:7163
- **Health Check**: http://localhost:7163/health
- **API Docs**: http://localhost:7163/scalar/v1
- **Environment**: Uses existing `.env` file

### 1.3 Development Workflow
1. **Start API first**: `dotnet run --urls "http://localhost:7163"`
2. **Start React second**: `npm run dev`
3. **Develop and test** with both running
4. **Test integration** between React and API

## 🐳 Part 2: Manual Docker Setup on Windows 11

### 2.1 Prerequisites for Windows 11 Docker

#### Install Docker Desktop
1. **Download Docker Desktop**: https://www.docker.com/products/docker-desktop/
2. **Install with WSL 2 backend** (recommended for Windows 11)
3. **Start Docker Desktop** and ensure it's running
4. **Verify installation**:
```powershell
docker --version
docker-compose --version
```

#### Enable WSL 2 (if not already enabled)
```powershell
# Run as Administrator
wsl --install
wsl --set-default-version 2
```

### 2.2 Setup Environment Files

### 1.1 Create React Environment File

Navigate to the React application folder and create a Docker environment file:

```powershell
cd SoNoBrokers
```

Create `.env.docker` file:
```bash
# React Application Docker Environment
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_BASE_URL=http://localhost:7163

# Clerk Authentication (from your existing .env file)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_YmV0dGVyLXBvc3N1bS01OC5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_SECRET_KEY=sk_test_VpPsw9paA18Uka7jxuB6PeE3bLvzKVEESGaDuysAgL

# Supabase (from your existing .env file)
NEXT_PUBLIC_SUPABASE_URL=https://yfznlsisxsnymkvydzha.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.qUBshv6Wi-90ZAEnUM2RXuhR77QFHnmEpI6O-y7l3BE

# Database (from your existing .env file)
DATABASE_URL=***************************************************************************************************/postgres

# External Services (from your existing .env file)
RESEND_API_KEY=re_BM6pyT8x_ChaS1fbRCbdprxPy1fwimWCs
NEXT_PUBLIC_MAPBOX_API_KEY=pk.eyJ1IjoiamF2aWFucGljYXJkbzMzIiwiYSI6ImNtYjY4ZGRyazBiaWYybHEyMWpnNGN4cDQifQ.m63aGFvbfzrQhT3sWlSbDQ

# Feature Flags (from your existing .env file)
NEXT_PUBLIC_ENABLE_GOOGLE_PROVIDERS=true
NEXT_PUBLIC_LAUNCH_MODE=true
NEXT_PUBLIC_ENABLE_LANGUAGE_SELECTOR=false
NEXT_PUBLIC_ENABLE_REGION_TESTER=false
NEXT_PUBLIC_SUPPORTED_COUNTRIES=CA,US,UAE
NEXT_PUBLIC_DEFAULT_COUNTRY=CA
```

### 1.2 Create Web API Environment File

Navigate to the Web API folder:

```powershell
cd ../SoNoBrokersWebApi/MicroSaasWebApi.App
```

Create `.env.docker` file:
```bash
# .NET Web API Docker Environment
ASPNETCORE_ENVIRONMENT=Development
ASPNETCORE_URLS=http://+:8080

# Database (from your existing .env file)
DATABASE_URL=***************************************************************************/postgres

# Clerk Authentication (from your existing .env file)
Authentication__Clerk__PublishableKey=pk_test_YmV0dGVyLXBvc3N1bS01OC5jbGVyay5hY2NvdW50cy5kZXYk
Authentication__Clerk__SecretKey=sk_test_VpPsw9paA18Uka7jxuB6PeE3bLvzKVEESGaDuysAgL
Authentication__Clerk__WebhookSecret=whsec_local_dev_webhook_secret
Authentication__Clerk__JwtIssuer=https://better-possum-58.clerk.accounts.dev
CLERK_SECRET_KEY=sk_test_VpPsw9paA18Uka7jxuB6PeE3bLvzKVEESGaDuysAgL

# JWT Configuration
Authentication__Jwt__Issuer=https://localhost:7001
Authentication__Jwt__SigningKey=local_dev_jwt_signing_key_minimum_32_characters_long

# External Services (from your existing .env file)
ExternalServices__Stripe__PublishableKey=pk_live_51Qfy9dP82YH9JfOlFmwTk0RHvE7l6sow7ImJBI4c1GzcbhFydoVpb3DMevP8B3C4gj95qCoEise8vEhgFgWxhLEN000RfInKNH
ExternalServices__Stripe__SecretKey=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
ExternalServices__Email__Resend__ApiKey=re_BM6pyT8x_ChaS1fbRCbdprxPy1fwimWCs

# Supabase (from your existing .env file)
Supabase__Url=https://yfznlsisxsnymkvydzha.supabase.co
Supabase__AnonKey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.qUBshv6Wi-90ZAEnUM2RXuhR77QFHnmEpI6O-y7l3BE
Supabase__StorageBucket=property-images

# Mapbox
Mapbox__ApiKey=pk.eyJ1IjoiamF2aWFucGljYXJkbzMzIiwiYSI6ImNtYjY4ZGRyazBiaWYybHEyMWpnNGN4cDQifQ.m63aGFvbfzrQhT3sWlSbDQ

# CORS Configuration
CORS__AllowedOrigins=http://localhost:3000,http://host.docker.internal:3000

# Logging
Logging__LogLevel__Default=Information
Logging__LogLevel__Microsoft.AspNetCore=Warning
```

## 🌐 Step 2: Manual Docker Setup on Windows 11

### 2.1 Verify Docker is Running
```powershell
# Check Docker Desktop is running
docker info

# Check available images
docker images

# Check running containers
docker ps
```

### 2.2 Build React Docker Image Manually

#### Navigate to React Folder
```powershell
cd C:\Projects\SoNoBrokersRoot\SoNoBrokers
```

#### Create Dockerfile (if not exists)
```dockerfile
# Dockerfile for React Application
FROM node:20-alpine AS base
WORKDIR /app

# Install dependencies
FROM base AS deps
RUN apk add --no-cache libc6-compat
COPY package.json package-lock.json* ./
RUN npm ci --only=production --ignore-scripts

# Build application
FROM base AS builder
COPY package.json package-lock.json* ./
RUN npm ci
COPY . .
RUN npm run build

# Production image
FROM base AS runner
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs
EXPOSE 3000
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

#### Build React Docker Image
```powershell
# Build the image (this may take 5-10 minutes)
docker build -t sonobrokers-frontend:latest .

# Verify image was created
docker images | findstr sonobrokers-frontend
```

### 2.3 Run React Container Manually

#### Option 1: Run with Environment File
```powershell
# Run container in detached mode
docker run -d `
  --name sonobrokers-frontend `
  --env-file .env.docker `
  -p 3000:3000 `
  sonobrokers-frontend:latest

# Check if container is running
docker ps
```

#### Option 2: Run with Individual Environment Variables
```powershell
# Run with specific environment variables
docker run -d `
  --name sonobrokers-frontend `
  -p 3000:3000 `
  -e NODE_ENV=development `
  -e NEXT_PUBLIC_APP_URL=http://localhost:3000 `
  -e NEXT_PUBLIC_API_BASE_URL=http://localhost:7163 `
  -e NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_YmV0dGVyLXBvc3N1bS01OC5jbGVyay5hY2NvdW50cy5kZXYk `
  sonobrokers-frontend:latest
```

#### Option 3: Run Interactively (for debugging)
```powershell
# Run container interactively to see logs
docker run -it `
  --name sonobrokers-frontend-debug `
  --env-file .env.docker `
  -p 3000:3000 `
  sonobrokers-frontend:latest
```

### 2.4 Verify React Application
```powershell
# Check container status
docker ps

# View container logs
docker logs sonobrokers-frontend

# Follow logs in real-time
docker logs -f sonobrokers-frontend

# Test the application
# Open browser to: http://localhost:3000
```

## 🔧 Step 3: Manual .NET Web API Docker Setup

### 3.1 Navigate to Web API Folder
```powershell
cd C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi
```

### 3.2 Build .NET API Docker Image Manually

#### Verify Dockerfile exists
```powershell
# Check if Dockerfile exists
Get-Content Dockerfile | Select-Object -First 10
```

#### Build .NET API Docker Image
```powershell
# Build the image (this may take 10-15 minutes)
docker build -t sonobrokers-api:latest .

# Verify image was created
docker images | findstr sonobrokers-api

# Check image size
docker images sonobrokers-api:latest
```

### 3.3 Run .NET API Container Manually

#### Option 1: Run with Environment File
```powershell
# Run container in detached mode
docker run -d `
  --name sonobrokers-api `
  --env-file MicroSaasWebApi.App/.env.docker `
  -p 7163:8080 `
  sonobrokers-api:latest

# Check if container is running
docker ps
```

#### Option 2: Run with Individual Environment Variables
```powershell
# Run with specific environment variables
docker run -d `
  --name sonobrokers-api `
  -p 7163:8080 `
  -e ASPNETCORE_ENVIRONMENT=Development `
  -e ASPNETCORE_URLS=http://+:8080 `
  -e "DATABASE_URL=***************************************************************************/postgres" `
  -e "Authentication__Clerk__PublishableKey=pk_test_YmV0dGVyLXBvc3N1bS01OC5jbGVyay5hY2NvdW50cy5kZXYk" `
  -e "Authentication__Clerk__SecretKey=sk_test_VpPsw9paA18Uka7jxuB6PeE3bLvzKVEESGaDuysAgL" `
  -e "CORS__AllowedOrigins=http://localhost:3000" `
  sonobrokers-api:latest
```

#### Option 3: Run Interactively (for debugging)
```powershell
# Run container interactively to see logs
docker run -it `
  --name sonobrokers-api-debug `
  --env-file MicroSaasWebApi.App/.env.docker `
  -p 7163:8080 `
  sonobrokers-api:latest
```

### 3.4 Verify .NET API Application
```powershell
# Check container status
docker ps

# View container logs
docker logs sonobrokers-api

# Follow logs in real-time
docker logs -f sonobrokers-api

# Test the API endpoints
curl http://localhost:7163/health
curl http://localhost:7163/api/sonobrokers/properties

# Open API documentation in browser
# http://localhost:7163/scalar/v1
```

## 🔍 Step 4: Docker Container Management on Windows 11

### 4.1 Container Lifecycle Management

#### Start Both Containers Together
```powershell
# Start API first (backend)
docker run -d `
  --name sonobrokers-api `
  --env-file SoNoBrokersWebApi/MicroSaasWebApi.App/.env.docker `
  -p 7163:8080 `
  sonobrokers-api:latest

# Wait a moment for API to start
Start-Sleep -Seconds 10

# Start React frontend
docker run -d `
  --name sonobrokers-frontend `
  --env-file SoNoBrokers/.env.docker `
  -p 3000:3000 `
  sonobrokers-frontend:latest

# Check both containers are running
docker ps
```

#### Stop Containers
```powershell
# Stop individual containers
docker stop sonobrokers-frontend
docker stop sonobrokers-api

# Stop all containers
docker stop $(docker ps -q)
```

#### Remove Containers
```powershell
# Remove individual containers
docker rm sonobrokers-frontend
docker rm sonobrokers-api

# Remove all stopped containers
docker container prune -f
```

#### Restart Containers
```powershell
# Restart individual containers
docker restart sonobrokers-frontend
docker restart sonobrokers-api

# Restart all containers
docker restart $(docker ps -q)
```

### 4.2 Docker Monitoring and Debugging

#### View Container Logs
```powershell
# View logs for specific container
docker logs sonobrokers-frontend
docker logs sonobrokers-api

# Follow logs in real-time
docker logs -f sonobrokers-frontend
docker logs -f sonobrokers-api

# View last 50 lines of logs
docker logs --tail 50 sonobrokers-frontend
```

#### Container Resource Usage
```powershell
# View container resource usage
docker stats

# View specific container stats
docker stats sonobrokers-frontend sonobrokers-api
```

#### Execute Commands in Running Containers
```powershell
# Access React container shell
docker exec -it sonobrokers-frontend sh

# Access API container shell
docker exec -it sonobrokers-api bash

# Run specific commands in containers
docker exec sonobrokers-api curl http://localhost:8080/health
docker exec sonobrokers-frontend npm --version
```

#### Inspect Container Configuration
```powershell
# Inspect container details
docker inspect sonobrokers-frontend
docker inspect sonobrokers-api

# View container environment variables
docker exec sonobrokers-frontend env
docker exec sonobrokers-api env

# View container network settings
docker inspect sonobrokers-frontend | findstr IPAddress
docker inspect sonobrokers-api | findstr IPAddress
```

### 4.3 Docker Image Management

#### List Images
```powershell
# List all images
docker images

# List specific images
docker images | findstr sonobrokers
```

#### Remove Images
```powershell
# Remove specific images
docker rmi sonobrokers-frontend:latest
docker rmi sonobrokers-api:latest

# Remove unused images
docker image prune -f

# Remove all images (careful!)
docker rmi $(docker images -q)
```

#### Rebuild Images
```powershell
# Rebuild React image
cd C:\Projects\SoNoBrokersRoot\SoNoBrokers
docker build --no-cache -t sonobrokers-frontend:latest .

# Rebuild API image
cd C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi
docker build --no-cache -t sonobrokers-api:latest .
```

## 🔍 Step 5: Test Integration

### 4.1 Test API Endpoints
```powershell
# Test health endpoint
curl http://localhost:7163/health

# Test properties endpoint (public)
curl http://localhost:7163/api/sonobrokers/properties
```

### 4.2 Test React Frontend
1. Open browser: http://localhost:3000
2. Verify the frontend loads
3. Check browser console for any API connection errors
4. Test property browsing functionality

## 🛑 Step 6: Stop and Clean Up

### 6.1 Graceful Shutdown

### 5.1 Stop Containers
```powershell
# Stop both containers
docker stop sonobrokers-frontend sonobrokers-api

# Remove containers
docker rm sonobrokers-frontend sonobrokers-api
```

### 5.2 Remove Images (Optional)
```powershell
# Remove images to free space
docker rmi sonobrokers-frontend:latest sonobrokers-api:latest
```

## 🐛 Windows 11 Docker Troubleshooting

### Windows 11 Specific Issues

**1. Docker Desktop Not Starting**
```powershell
# Check if WSL 2 is enabled
wsl --list --verbose

# Enable WSL 2 if needed
wsl --install
wsl --set-default-version 2

# Restart Docker Desktop
# Right-click Docker Desktop → Quit Docker Desktop
# Start Docker Desktop again
```

**2. WSL 2 Integration Issues**
```powershell
# Check WSL 2 distributions
wsl --list --online

# Install Ubuntu (recommended)
wsl --install -d Ubuntu

# Set Ubuntu as default
wsl --set-default Ubuntu

# In Docker Desktop → Settings → Resources → WSL Integration
# Enable integration with Ubuntu
```

**3. Hyper-V Conflicts**
```powershell
# Check if Hyper-V is enabled (run as Administrator)
Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V

# If you need to disable Hyper-V for other virtualization software
# Disable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All
```

**4. Windows Firewall Issues**
```powershell
# Allow Docker through Windows Firewall
# Windows Security → Firewall & network protection → Allow an app through firewall
# Add Docker Desktop

# Or temporarily disable firewall for testing
# netsh advfirewall set allprofiles state off
# netsh advfirewall set allprofiles state on
```

**5. Memory and Resource Issues**
```powershell
# Check available memory
Get-ComputerInfo | Select-Object TotalPhysicalMemory, AvailablePhysicalMemory

# In Docker Desktop → Settings → Resources
# Adjust Memory limit (recommended: 4GB minimum)
# Adjust CPU limit (recommended: 2 CPUs minimum)
```

**6. Port Conflicts on Windows**
```powershell
# Check what's using ports 3000 and 7163
netstat -ano | findstr :3000
netstat -ano | findstr :7163

# Kill process using the port (replace PID with actual process ID)
taskkill /PID <PID_NUMBER> /F

# Check if Windows reserves the port
netsh interface ipv4 show excludedportrange protocol=tcp
```

**7. File Path Issues (Windows vs Linux)**
```powershell
# Use forward slashes in Docker commands
# Instead of: C:\Projects\SoNoBrokersRoot
# Use: /c/Projects/SoNoBrokersRoot (in Git Bash)
# Or use PowerShell with proper escaping

# Check current directory format
pwd
Get-Location
```

**8. Docker Build Context Issues**
```powershell
# Clear Docker build cache
docker builder prune -f

# Build with no cache
docker build --no-cache -t sonobrokers-frontend:latest .

# Check .dockerignore file exists and is properly configured
Get-Content .dockerignore
```

## 🐛 General Troubleshooting

### Common Issues

**1. Port Already in Use**
```powershell
# Check what's using the port
netstat -ano | findstr :3000
netstat -ano | findstr :7163

# Kill process if needed
taskkill /PID <PID_NUMBER> /F
```

**2. Container Build Fails**
```powershell
# Clean Docker cache
docker system prune -a

# Rebuild without cache
docker build --no-cache -t sonobrokers-frontend:latest .
```

**3. Environment Variables Not Loading**
```powershell
# Check if .env.docker file exists
ls .env.docker

# Verify container environment
docker exec sonobrokers-frontend env
docker exec sonobrokers-api env
```

**4. Database Connection Issues**
```powershell
# Test database connectivity from container
docker exec sonobrokers-api curl -f http://localhost:8080/health
```

### Logs and Debugging
```powershell
# View real-time logs
docker logs -f sonobrokers-frontend
docker logs -f sonobrokers-api

# Enter container for debugging
docker exec -it sonobrokers-frontend sh
docker exec -it sonobrokers-api bash
```

## 🚀 Part 3: Docker Deployment to Production

### 3.1 Deploy React to Vercel using Docker

#### Install Vercel CLI
```powershell
npm install -g vercel
```

#### Login to Vercel
```powershell
vercel login
```

#### Deploy React with Docker Support
```powershell
cd C:\Projects\SoNoBrokersRoot\SoNoBrokers

# Deploy to Vercel with Docker
vercel --prod --docker
```

#### Configure Vercel Environment Variables
Add these in Vercel Dashboard → Project → Settings → Environment Variables:
```bash
# Production API URL (Azure Web App)
NEXT_PUBLIC_API_BASE_URL=https://sonobrokers-api.azurewebsites.net

# All other environment variables from your .env file
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_YmV0dGVyLXBvc3N1bS01OC5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_SECRET_KEY=sk_test_VpPsw9paA18Uka7jxuB6PeE3bLvzKVEESGaDuysAgL
# ... (copy all other variables from your .env file)
```

### 3.2 Deploy .NET Web API to Azure Portal using Docker

#### Install Azure CLI
```powershell
# Download from: https://aka.ms/installazurecliwindows
az --version
```

#### Login to Azure
```powershell
az login
```

#### Create Azure Container Registry
```powershell
# Create resource group
az group create --name sonobrokers-rg --location "East US"

# Create container registry
az acr create --resource-group sonobrokers-rg --name sonobrokersregistry --sku Basic --admin-enabled true
```

#### Build and Push Docker Image
```powershell
cd C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi

# Get ACR login server
$acrLoginServer = az acr show --name sonobrokersregistry --resource-group sonobrokers-rg --query loginServer --output tsv

# Login to ACR
az acr login --name sonobrokersregistry

# Build Docker image
docker build -t $acrLoginServer/sonobrokers-api:latest .

# Push to ACR
docker push $acrLoginServer/sonobrokers-api:latest
```

#### Deploy to Azure Web App
```powershell
# Create App Service Plan
az appservice plan create --name sonobrokers-plan --resource-group sonobrokers-rg --sku B1 --is-linux

# Create Web App with container
az webapp create --resource-group sonobrokers-rg --plan sonobrokers-plan --name sonobrokers-api --deployment-container-image-name $acrLoginServer/sonobrokers-api:latest

# Configure container registry credentials
$acrPassword = az acr credential show --name sonobrokersregistry --query passwords[0].value --output tsv
az webapp config container set --name sonobrokers-api --resource-group sonobrokers-rg --docker-custom-image-name $acrLoginServer/sonobrokers-api:latest --docker-registry-server-url https://$acrLoginServer --docker-registry-server-user sonobrokersregistry --docker-registry-server-password $acrPassword
```

#### Configure Azure Web App Environment Variables
```powershell
# Set environment variables
az webapp config appsettings set --resource-group sonobrokers-rg --name sonobrokers-api --settings `
  ASPNETCORE_ENVIRONMENT=Production `
  ASPNETCORE_URLS=http://+:8080 `
  "DATABASE_URL=***************************************************************************/postgres" `
  "Authentication__Clerk__PublishableKey=pk_test_YmV0dGVyLXBvc3N1bS01OC5jbGVyay5hY2NvdW50cy5kZXYk" `
  "Authentication__Clerk__SecretKey=sk_test_VpPsw9paA18Uka7jxuB6PeE3bLvzKVEESGaDuysAgL" `
  "CORS__AllowedOrigins=https://sonobrokers.vercel.app,https://www.sonobrokers.com"
```

## 🔄 Part 4: Update and Redeploy Process

### 4.1 Update React Application
```powershell
cd C:\Projects\SoNoBrokersRoot\SoNoBrokers
# Make your changes
npm run dev  # Test locally

# Deploy updates
vercel --prod --docker
```

### 4.2 Update .NET Web API
```powershell
cd C:\Projects\SoNoBrokersRoot\SoNoBrokersWebApi
# Make your changes
cd MicroSaasWebApi.App && dotnet run --urls "http://localhost:7163"  # Test locally

# Build and deploy updates
$acrLoginServer = az acr show --name sonobrokersregistry --resource-group sonobrokers-rg --query loginServer --output tsv
docker build -t $acrLoginServer/sonobrokers-api:latest .
docker push $acrLoginServer/sonobrokers-api:latest

# Restart Web App to pull new image
az webapp restart --resource-group sonobrokers-rg --name sonobrokers-api
```

## 📊 Quick Reference

### Development URLs
| Component | Local URL | Local Container | Production URL |
|-----------|-----------|-----------------|----------------|
| **React Frontend** | http://localhost:3000 | sonobrokers-frontend | https://sonobrokers.vercel.app |
| **.NET API** | http://localhost:7163 | sonobrokers-api | https://sonobrokers-api.azurewebsites.net |

### Key Commands
```powershell
# Manual Development
cd SoNoBrokers && npm run dev
cd SoNoBrokersWebApi\MicroSaasWebApi.App && dotnet run --urls "http://localhost:7163"

# Local Docker Testing
docker run -d --name sonobrokers-frontend --env-file .env.docker -p 3000:3000 sonobrokers-frontend:latest
docker run -d --name sonobrokers-api --env-file MicroSaasWebApi.App/.env.docker -p 7163:8080 sonobrokers-api:latest

# Production Deployment
vercel --prod --docker  # React to Vercel
az webapp restart --resource-group sonobrokers-rg --name sonobrokers-api  # API to Azure
```

## 🎯 Success Indicators

### Local Development
✅ **React Frontend**: Loads at http://localhost:3000
✅ **.NET API**: Health check returns 200 at http://localhost:7163/health
✅ **Integration**: Frontend can call API endpoints
✅ **Database**: API can connect to Supabase database

### Local Docker Testing
✅ **React Container**: Runs successfully with `docker run`
✅ **API Container**: Runs successfully with `docker run`
✅ **Environment Variables**: Loaded correctly in containers
✅ **Network Communication**: Containers can communicate

### Production Deployment
✅ **React on Vercel**: Deployed successfully with Docker support
✅ **API on Azure**: Deployed to Azure Web App with Container Registry
✅ **Environment Variables**: Configured in Vercel and Azure
✅ **CORS Configuration**: Cross-origin requests work
✅ **End-to-End**: Full application works in production

---

**Ready for Development and Production Deployment! 🐳🚀**
🌐 React Application Environment Files:
.env.docker - Base Docker configuration
.env.docker.development - Development-specific overrides
.env.docker.production - Production-specific overrides
docker-compose.development.yml - Development Docker Compose
docker-compose.production.yml - Production Docker Compose
🔧 Web API Environment Files:
.env.docker.development - Development API configuration
.env.docker.production - Production API configuration
📚 Documentation:
 DOCKER-ENVIRONMENT-GUIDE.md - Comprehensive usage guide
🎯 Key Features Implemented:
Environment Hierarchy:
Base configuration in .env.docker
Environment-specific overrides in .env.docker.development and .env.docker.production
Local overrides support with .env.docker.local (gitignored)

# React
docker run --env-file .env.docker.development -p 3000:3000 sonobrokers-frontend:dev

# API
docker run --env-file MicroSaasWebApi.App/.env.docker.development -p 7163:8080 sonobrokers-api:dev

# Docker Compose
docker-compose -f docker-compose.development.yml up --build


PROD
# React
docker run --env-file .env.docker.production -p 3000:3000 sonobrokers-frontend:prod

# API
docker run --env-file MicroSaasWebApi.App/.env.docker.production -p 7163:8080 sonobrokers-api:prod

# Docker Compose
docker-compose -f docker-compose.production.yml up --build



# Run API only
cd MicroSaasWebApi.App && docker-compose up -d

# Run Config server only  
cd MicroSaasWebApi.Config && docker-compose up -d

# Run Tests only
cd MicroSaasWebApi.Tests && docker-compose up

# From root directory
docker-compose -f docker-compose.master.yml up -d