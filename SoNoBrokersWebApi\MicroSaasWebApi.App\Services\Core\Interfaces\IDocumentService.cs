using MicroSaasWebApi.Models.Core;

namespace MicroSaasWebApi.Services.Core.Interfaces
{
    /// <summary>
    /// Document service interface for document management operations
    /// </summary>
    public interface IDocumentService
    {
        /// <summary>
        /// Get documents with pagination and filtering
        /// </summary>
        Task<IEnumerable<Document>> GetDocumentsAsync(
            int page,
            int pageSize,
            string? search = null,
            string? category = null,
            string? contentType = null,
            bool? isPublic = null);

        /// <summary>
        /// Get document by ID
        /// </summary>
        Task<Document?> GetDocumentByIdAsync(Guid id);

        /// <summary>
        /// Get documents by category
        /// </summary>
        Task<IEnumerable<Document>> GetDocumentsByCategoryAsync(string category, int page = 1, int pageSize = 10);

        /// <summary>
        /// Get documents by user
        /// </summary>
        Task<IEnumerable<Document>> GetDocumentsByUserAsync(Guid userId, int page = 1, int pageSize = 10);

        /// <summary>
        /// Upload new document
        /// </summary>
        Task<Document> UploadDocumentAsync(Document document, IFormFile file);

        /// <summary>
        /// Update document metadata
        /// </summary>
        Task<Document> UpdateDocumentAsync(Document document);

        /// <summary>
        /// Delete document
        /// </summary>
        Task<bool> DeleteDocumentAsync(Guid id);

        /// <summary>
        /// Get document stream for download
        /// </summary>
        Task<Stream?> GetDocumentStreamAsync(Guid id);

        /// <summary>
        /// Get document URL for direct access
        /// </summary>
        Task<string?> GetDocumentUrlAsync(Guid id, TimeSpan? expiry = null);

        /// <summary>
        /// Check if document exists
        /// </summary>
        Task<bool> DocumentExistsAsync(Guid id);

        /// <summary>
        /// Get document count
        /// </summary>
        Task<int> GetDocumentCountAsync(bool? isPublic = null);

        /// <summary>
        /// Get documents by content type
        /// </summary>
        Task<IEnumerable<Document>> GetDocumentsByContentTypeAsync(string contentType, int page = 1, int pageSize = 10);

        /// <summary>
        /// Search documents by name or description
        /// </summary>
        Task<IEnumerable<Document>> SearchDocumentsAsync(string searchTerm, int page = 1, int pageSize = 10);

        /// <summary>
        /// Get public documents
        /// </summary>
        Task<IEnumerable<Document>> GetPublicDocumentsAsync(int page = 1, int pageSize = 10);

        /// <summary>
        /// Get all document categories
        /// </summary>
        Task<IEnumerable<string>> GetCategoriesAsync();

        /// <summary>
        /// Get documents by size range
        /// </summary>
        Task<IEnumerable<Document>> GetDocumentsBySizeRangeAsync(long minSize, long maxSize, int page = 1, int pageSize = 10);

        /// <summary>
        /// Get documents by date range
        /// </summary>
        Task<IEnumerable<Document>> GetDocumentsByDateRangeAsync(DateTime startDate, DateTime endDate, int page = 1, int pageSize = 10);

        /// <summary>
        /// Update document access status
        /// </summary>
        Task<bool> SetDocumentPublicStatusAsync(Guid id, bool isPublic);

        /// <summary>
        /// Get total storage used by user
        /// </summary>
        Task<long> GetUserStorageUsedAsync(Guid userId);

        /// <summary>
        /// Validate file upload
        /// </summary>
        Task<(bool IsValid, string? ErrorMessage)> ValidateFileUploadAsync(IFormFile file);
    }
}
