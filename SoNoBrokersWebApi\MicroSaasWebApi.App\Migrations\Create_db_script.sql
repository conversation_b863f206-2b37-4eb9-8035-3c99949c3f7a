-- =====================================================
-- SoNoBrokers Database Creation Script
-- Complete database setup from scratch
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- =====================================================
-- ENUMS - Create all enums first
-- =====================================================

-- User related enums
CREATE TYPE "UserRole" AS ENUM (
  'ADMIN',
  'USER',
  'PRODUCT',
  'OPERATOR',
  'SERVICE_PROVIDER'
);

CREATE TYPE "UserStatus" AS ENUM (
  'ACTIVE',
  'INACTIVE',
  'SUSPENDED',
  'PENDING'
);

CREATE TYPE "UserType" AS ENUM (
  'Buyer',
  'Seller'
);

-- Property related enums
CREATE TYPE "PropertyType" AS ENUM (
  'Detached House',
  'Semi-Detached House',
  'Townhouse',
  'Condominium',
  'Apartment',
  'Duplex',
  'Triplex',
  'Fourplex',
  'Mobile Home',
  'Vacant Land',
  'Commercial',
  'Industrial',
  'Investment',
  'Other'
);

CREATE TYPE "PropertyStatus" AS ENUM (
  'ACTIVE',
  'SOLD',
  'PENDING',
  'WITHDRAWN',
  'EXPIRED',
  'DRAFT'
);

CREATE TYPE "ListingType" AS ENUM (
  'FOR_SALE',
  'FOR_RENT',
  'SOLD',
  'RENTED'
);

-- Service related enums
CREATE TYPE "ServiceType" AS ENUM (
  'lawyer',
  'photographer',
  'inspector',
  'appraiser',
  'home_inspector',
  'mortgage_broker',
  'insurance_agent',
  'contractor',
  'cleaner',
  'stager',
  'marketing_agency'
);

-- Booking and offer related enums
CREATE TYPE "BookingStatus" AS ENUM (
  'pending',
  'confirmed',
  'completed',
  'cancelled'
);

CREATE TYPE "OfferStatus" AS ENUM (
  'pending',
  'reviewed',
  'accepted',
  'rejected'
);

-- Access and subscription related enums
CREATE TYPE "AccessType" AS ENUM (
  'qr_scan',
  'online_access'
);

CREATE TYPE "SubscriptionStatus" AS ENUM (
  'ACTIVE',
  'INACTIVE',
  'CANCELLED',
  'EXPIRED',
  'PENDING'
);

CREATE TYPE "SubscriptionType" AS ENUM (
  'BASIC',
  'PREMIUM',
  'ENTERPRISE'
);

-- Advertiser related enums
CREATE TYPE "AdvertiserPlan" AS ENUM (
  'basic',
  'premium',
  'enterprise'
);

CREATE TYPE "AdvertiserStatus" AS ENUM (
  'pending',
  'active',
  'suspended',
  'cancelled'
);

-- Country enum
CREATE TYPE "Country" AS ENUM (
  'CA',
  'US',
  'UAE'
);

-- Contact Sharing related enums
CREATE TYPE "ContactShareType" AS ENUM (
  'ContactRequest',
  'PropertyOffer',
  'ScheduleVisit',
  'OfferWithVisit'
);

CREATE TYPE "ContactShareStatus" AS ENUM (
  'Sent',
  'Viewed',
  'Responded',
  'Accepted',
  'Declined',
  'Expired'
);

-- Property Scheduling related enums
CREATE TYPE "VisitStatus" AS ENUM (
  'Pending',
  'Confirmed',
  'Rescheduled',
  'Cancelled',
  'Completed',
  'NoShow',
  'Expired'
);

CREATE TYPE "VisitType" AS ENUM (
  'InPerson',
  'Virtual',
  'SelfGuided'
);

CREATE TYPE "VerificationMethod" AS ENUM (
  'QrCode',
  'SellerPresent',
  'KeyBox',
  'PropertyManager',
  'Other'
);

-- =====================================================
-- CORE TABLES - Create tables in dependency order
-- =====================================================

-- Users table (base table, no dependencies) - Complete schema for React application
CREATE TABLE IF NOT EXISTS public."User" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  email text NOT NULL UNIQUE,
  "fullName" text NOT NULL,
  "firstName" text,
  "lastName" text,
  "phoneNumber" text,
  "profileImageUrl" text,
  address jsonb,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  "lastLoginAt" timestamp with time zone,
  "loggedIn" boolean DEFAULT false,
  "clerkId" text UNIQUE,
  "clerkUserId" text UNIQUE,
  "authUserId" text UNIQUE,
  role "UserRole" DEFAULT 'USER',
  "userType" "UserType" DEFAULT 'Buyer',
  status "UserStatus" DEFAULT 'ACTIVE',
  "emailVerified" boolean DEFAULT false,
  "phoneVerified" boolean DEFAULT false,
  "isActive" boolean DEFAULT true,
  "createdByAdmin" uuid,
  CONSTRAINT "User_pkey" PRIMARY KEY (id),
  CONSTRAINT "User_createdByAdmin_fkey" FOREIGN KEY ("createdByAdmin") REFERENCES public."User"(id) ON DELETE NO ACTION ON UPDATE NO ACTION
);
-- Property table (depends on User)
CREATE TABLE public."Property" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text,
  price decimal(12,2) NOT NULL,
  address jsonb NOT NULL,
  bedrooms integer,
  bathrooms decimal(3,1),
  "squareFeet" integer,
  "lotSize" decimal(10,2),
  "yearBuilt" integer,
  "propertyType" text NOT NULL,
  status "PropertyStatus" DEFAULT 'pending',
  "listingDate" timestamp with time zone DEFAULT now(),
  "expiryDate" timestamp with time zone,
  "sellerId" uuid NOT NULL,
  "agentId" uuid,
  features jsonb DEFAULT '[]',
  amenities jsonb DEFAULT '[]',
  "virtualTourUrl" text,
  "videoUrl" text,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "Property_pkey" PRIMARY KEY (id),
  CONSTRAINT "Property_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES public."User"(id) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "Property_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES public."User"(id) ON UPDATE NO ACTION
);

-- Service Provider table (depends on User)
CREATE TABLE public."ServiceProvider" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "userId" uuid NOT NULL UNIQUE,
  "serviceType" "ServiceType" NOT NULL,
  "businessName" text,
  "licenseNumber" text,
  "serviceArea" text[],
  "hourlyRate" decimal(8,2),
  "fixedRates" jsonb DEFAULT '{}',
  availability jsonb DEFAULT '{}',
  rating decimal(3,2) DEFAULT 0,
  "totalReviews" integer DEFAULT 0,
  "isVerified" boolean DEFAULT false,
  "insuranceInfo" jsonb,
  website text,
  description text,
  images text[],
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "ServiceProvider_pkey" PRIMARY KEY (id),
  CONSTRAINT "ServiceProvider_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON DELETE CASCADE ON UPDATE NO ACTION
);

-- Advertiser table (depends on User and ServiceProvider)
CREATE TABLE public."Advertiser" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "userId" uuid NOT NULL UNIQUE,
  "serviceProviderId" uuid,
  "businessName" text NOT NULL,
  "contactName" text,
  email text NOT NULL,
  phone text,
  website text,
  description text,
  "serviceType" "ServiceType" NOT NULL,
  "serviceAreas" text[],
  "licenseNumber" text,
  plan "AdvertiserPlan" DEFAULT 'basic',
  status "AdvertiserStatus" DEFAULT 'pending',
  "featuredUntil" timestamp with time zone,
  "isPremium" boolean DEFAULT false,
  "isVerified" boolean DEFAULT false,
  images text[],
  metadata jsonb DEFAULT '{}',
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "Advertiser_pkey" PRIMARY KEY (id),
  CONSTRAINT "Advertiser_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON DELETE CASCADE,
  CONSTRAINT "Advertiser_serviceProviderId_fkey" FOREIGN KEY ("serviceProviderId") REFERENCES public."ServiceProvider"(id) ON DELETE SET NULL
);
-- Advertiser Subscription table (depends on Advertiser)
CREATE TABLE public."AdvertiserSubscription" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "advertiserId" uuid NOT NULL,
  "stripeSubscriptionId" text UNIQUE,
  "stripePriceId" text,
  plan "AdvertiserPlan" NOT NULL,
  status "SubscriptionStatus" DEFAULT 'active',
  "currentPeriodStart" timestamp with time zone,
  "currentPeriodEnd" timestamp with time zone,
  "cancelAtPeriodEnd" boolean DEFAULT false,
  "canceledAt" timestamp with time zone,
  "trialStart" timestamp with time zone,
  "trialEnd" timestamp with time zone,
  metadata jsonb DEFAULT '{}',
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "AdvertiserSubscription_pkey" PRIMARY KEY (id),
  CONSTRAINT "AdvertiserSubscription_advertiserId_fkey" FOREIGN KEY ("advertiserId") REFERENCES public."Advertiser"(id) ON DELETE CASCADE
);

-- Buyer Listings table (depends on User and Property)
CREATE TABLE public."BuyerListings" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "buyerId" uuid NOT NULL,
  "propertyId" uuid NOT NULL,
  "savedAt" timestamp with time zone DEFAULT now(),
  notes text,
  "isFavorite" boolean DEFAULT false,
  CONSTRAINT "BuyerListings_pkey" PRIMARY KEY (id),
  CONSTRAINT "BuyerListings_buyerId_fkey" FOREIGN KEY ("buyerId") REFERENCES public."User"(id) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "BuyerListings_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "BuyerListings_buyerId_propertyId_key" UNIQUE ("buyerId", "propertyId")
);

-- Buyer Offer table (depends on User and Property)
CREATE TABLE public."BuyerOffer" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "propertyId" uuid NOT NULL,
  "buyerId" uuid NOT NULL,
  "offerAmount" decimal(12,2) NOT NULL,
  "downPayment" decimal(12,2),
  "financingType" text,
  "closingDate" date,
  contingencies jsonb DEFAULT '[]',
  status "OfferStatus" DEFAULT 'pending',
  "counterOfferAmount" decimal(12,2),
  "sellerNotes" text,
  "buyerNotes" text,
  "expiryDate" timestamp with time zone,
  "submittedAt" timestamp with time zone DEFAULT now(),
  "respondedAt" timestamp with time zone,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "BuyerOffer_pkey" PRIMARY KEY (id),
  CONSTRAINT "BuyerOffer_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "BuyerOffer_buyerId_fkey" FOREIGN KEY ("buyerId") REFERENCES public."User"(id) ON DELETE CASCADE ON UPDATE NO ACTION
);

-- Buyer Profile table (depends on User)
CREATE TABLE public."BuyerProfile" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "userId" uuid NOT NULL UNIQUE,
  "preApprovalAmount" decimal(12,2),
  "preApprovalDate" date,
  "lenderName" text,
  "agentId" uuid,
  preferences jsonb DEFAULT '{}',
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "BuyerProfile_pkey" PRIMARY KEY (id),
  CONSTRAINT "BuyerProfile_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "BuyerProfile_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES public."User"(id) ON UPDATE NO ACTION
);
-- Seller Profile table (depends on User)
CREATE TABLE public."SellerProfile" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "userId" uuid NOT NULL UNIQUE,
  "agentId" uuid,
  "commissionRate" decimal(5,4),
  "marketingBudget" decimal(10,2),
  preferences jsonb DEFAULT '{}',
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "SellerProfile_pkey" PRIMARY KEY (id),
  CONSTRAINT "SellerProfile_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "SellerProfile_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES public."User"(id) ON UPDATE NO ACTION
);

-- Conversation table (depends on User and Property)
CREATE TABLE public."Conversation" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "propertyId" uuid,
  "buyerId" uuid,
  "sellerId" uuid,
  "agentId" uuid,
  subject text,
  "lastMessageAt" timestamp with time zone DEFAULT now(),
  "isActive" boolean DEFAULT true,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "Conversation_pkey" PRIMARY KEY (id),
  CONSTRAINT "Conversation_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON UPDATE NO ACTION,
  CONSTRAINT "Conversation_buyerId_fkey" FOREIGN KEY ("buyerId") REFERENCES public."User"(id) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "Conversation_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES public."User"(id) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "Conversation_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES public."User"(id) ON UPDATE NO ACTION
);

-- Message table (depends on Conversation and User)
CREATE TABLE public."Message" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "conversationId" uuid NOT NULL,
  "senderId" uuid NOT NULL,
  content text NOT NULL,
  "messageType" text DEFAULT 'text',
  attachments jsonb DEFAULT '[]',
  "isRead" boolean DEFAULT false,
  "readAt" timestamp with time zone,
  "createdAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "Message_pkey" PRIMARY KEY (id),
  CONSTRAINT "Message_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES public."Conversation"(id) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "Message_senderId_fkey" FOREIGN KEY ("senderId") REFERENCES public."User"(id) ON DELETE CASCADE ON UPDATE NO ACTION
);

-- Notifications table (depends on User)
CREATE TABLE public."Notifications" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "userId" uuid NOT NULL,
  title text NOT NULL,
  message text NOT NULL,
  "notificationType" text NOT NULL,
  "entityType" text,
  "entityId" uuid,
  "isRead" boolean DEFAULT false,
  "readAt" timestamp with time zone,
  "createdAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "Notifications_pkey" PRIMARY KEY (id),
  CONSTRAINT "Notifications_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON DELETE CASCADE ON UPDATE NO ACTION
);

-- Open House Access table (depends on Property and User)
CREATE TABLE public."OpenHouseAccess" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "propertyId" uuid NOT NULL,
  "userId" uuid,
  "accessType" "AccessType" NOT NULL,
  "qrCode" text,
  "accessToken" text,
  "startTime" timestamp with time zone NOT NULL,
  "endTime" timestamp with time zone NOT NULL,
  "accessedAt" timestamp with time zone,
  "visitorInfo" jsonb,
  "isActive" boolean DEFAULT true,
  "createdAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "OpenHouseAccess_pkey" PRIMARY KEY (id),
  CONSTRAINT "OpenHouseAccess_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "OpenHouseAccess_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON UPDATE NO ACTION
);

-- Audiences table (standalone)
CREATE TABLE public."Audiences" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "resendId" text NOT NULL UNIQUE,
  name text NOT NULL,
  description text,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "Audiences_pkey" PRIMARY KEY (id)
);
-- Property Image table (depends on Property)
CREATE TABLE public."PropertyImage" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "propertyId" uuid NOT NULL,
  url text NOT NULL,
  caption text,
  "isPrimary" boolean DEFAULT false,
  "displayOrder" integer DEFAULT 0,
  "createdAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "PropertyImage_pkey" PRIMARY KEY (id),
  CONSTRAINT "PropertyImage_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE ON UPDATE NO ACTION
);

-- Property Analytics table (depends on Property)
CREATE TABLE public."PropertyAnalytics" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "propertyId" uuid NOT NULL UNIQUE,
  "viewCount" integer DEFAULT 0,
  "favoriteCount" integer DEFAULT 0,
  "inquiryCount" integer DEFAULT 0,
  "viewingCount" integer DEFAULT 0,
  "offerCount" integer DEFAULT 0,
  "lastViewedAt" timestamp with time zone,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "PropertyAnalytics_pkey" PRIMARY KEY (id),
  CONSTRAINT "PropertyAnalytics_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE ON UPDATE NO ACTION
);

-- Property Viewing table (depends on Property and User)
CREATE TABLE public."PropertyViewing" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "propertyId" uuid NOT NULL,
  "buyerId" uuid NOT NULL,
  "agentId" uuid,
  "scheduledDate" timestamp with time zone NOT NULL,
  "actualDate" timestamp with time zone,
  status "BookingStatus" DEFAULT 'pending',
  notes text,
  feedback text,
  rating integer,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "PropertyViewing_pkey" PRIMARY KEY (id),
  CONSTRAINT "PropertyViewing_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "PropertyViewing_buyerId_fkey" FOREIGN KEY ("buyerId") REFERENCES public."User"(id) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "PropertyViewing_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES public."User"(id) ON UPDATE NO ACTION
);

-- Search Filter table (depends on User)
CREATE TABLE public."SearchFilter" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "userId" uuid NOT NULL,
  name text NOT NULL,
  "minPrice" decimal(12,2),
  "maxPrice" decimal(12,2),
  "minBedrooms" integer,
  "maxBedrooms" integer,
  "minBathrooms" decimal(3,1),
  "maxBathrooms" decimal(3,1),
  "propertyTypes" jsonb DEFAULT '[]',
  location jsonb,
  "maxDistance" integer,
  features jsonb DEFAULT '[]',
  "isActive" boolean DEFAULT true,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "SearchFilter_pkey" PRIMARY KEY (id),
  CONSTRAINT "SearchFilter_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON DELETE CASCADE ON UPDATE NO ACTION
);
-- Service Booking table (depends on ServiceProvider, User, and Property)
CREATE TABLE public."ServiceBooking" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "serviceProviderId" uuid NOT NULL,
  "clientId" uuid NOT NULL,
  "propertyId" uuid,
  "serviceType" "ServiceType" NOT NULL,
  "scheduledDate" timestamp with time zone NOT NULL,
  "estimatedDuration" integer,
  "estimatedCost" decimal(10,2),
  "actualCost" decimal(10,2),
  status "BookingStatus" DEFAULT 'pending',
  notes text,
  "specialRequirements" text,
  "completedAt" timestamp with time zone,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "ServiceBooking_pkey" PRIMARY KEY (id),
  CONSTRAINT "ServiceBooking_serviceProviderId_fkey" FOREIGN KEY ("serviceProviderId") REFERENCES public."ServiceProvider"(id) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "ServiceBooking_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES public."User"(id) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "ServiceBooking_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON UPDATE NO ACTION
);

-- Subscription table (depends on User)
CREATE TABLE public."Subscription" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "userId" uuid NOT NULL,
  "stripeSubscriptionId" text NOT NULL,
  status "SubscriptionStatus" DEFAULT 'active',
  "planType" text NOT NULL,
  "startsAt" timestamp with time zone NOT NULL,
  "endsAt" timestamp with time zone NOT NULL,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "Subscription_pkey" PRIMARY KEY (id),
  CONSTRAINT "Subscription_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "Subscription_userId_stripeSubscriptionId_key" UNIQUE ("userId", "stripeSubscriptionId")
);

-- AI Report table (depends on Property and User)
CREATE TABLE public."AIReport" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "propertyId" uuid NOT NULL,
  "userId" uuid NOT NULL,
  "reportType" text NOT NULL,
  "analysisData" jsonb NOT NULL,
  recommendations jsonb DEFAULT '[]',
  "marketComparisons" jsonb DEFAULT '[]',
  "priceEstimate" decimal(12,2),
  "confidenceScore" decimal(5,4),
  "generatedAt" timestamp with time zone DEFAULT now(),
  "isPublic" boolean DEFAULT false,
  "createdAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "AIReport_pkey" PRIMARY KEY (id),
  CONSTRAINT "AIReport_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT "AIReport_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON DELETE CASCADE ON UPDATE NO ACTION
);

-- User Activity table (depends on User)
CREATE TABLE public."UserActivity" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "userId" uuid NOT NULL,
  "activityType" text NOT NULL,
  "entityType" text,
  "entityId" uuid,
  metadata jsonb DEFAULT '{}',
  "createdAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "UserActivity_pkey" PRIMARY KEY (id),
  CONSTRAINT "UserActivity_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON DELETE CASCADE ON UPDATE NO ACTION
);
-- Role Permissions table (standalone)
CREATE TABLE public."role_permissions" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  role "UserRole" NOT NULL,
  permission text NOT NULL,
  resource text,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "role_permissions_pkey" PRIMARY KEY (id),
  CONSTRAINT "role_permissions_role_permission_key" UNIQUE (role, permission)
);

-- Project table (standalone)
CREATE TABLE public.project (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_clerk_id text NOT NULL,
  scenario_id text,
  webhook_link text,
  connection_id text,
  webhook_id text,
  assistant_id text,
  type text,
  status text DEFAULT 'inactive',
  name text,
  description text,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT project_pkey PRIMARY KEY (id)
);

-- Stripe tables
CREATE TABLE public.stripe_customers (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  stripe_customer_id text NOT NULL UNIQUE,
  email text NOT NULL,
  name text,
  phone text,
  address jsonb,
  metadata jsonb DEFAULT '{}',
  default_payment_method text,
  "created_at" timestamp with time zone DEFAULT now(),
  "updated_at" timestamp with time zone DEFAULT now(),
  CONSTRAINT stripe_customers_pkey PRIMARY KEY (id),
  CONSTRAINT stripe_customers_user_id_fkey FOREIGN KEY (user_id) REFERENCES public."User"(id) ON DELETE CASCADE
);

CREATE TABLE public.stripe_products (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  stripe_product_id text NOT NULL UNIQUE,
  name text NOT NULL,
  description text,
  active boolean DEFAULT true,
  metadata jsonb DEFAULT '{}',
  "created_at" timestamp with time zone DEFAULT now(),
  "updated_at" timestamp with time zone DEFAULT now(),
  CONSTRAINT stripe_products_pkey PRIMARY KEY (id)
);

CREATE TABLE public.stripe_prices (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  stripe_price_id text NOT NULL UNIQUE,
  stripe_product_id text NOT NULL,
  unit_amount integer NOT NULL,
  currency text DEFAULT 'usd',
  recurring_interval text,
  recurring_interval_count integer DEFAULT 1,
  type text NOT NULL,
  active boolean DEFAULT true,
  metadata jsonb DEFAULT '{}',
  "created_at" timestamp with time zone DEFAULT now(),
  "updated_at" timestamp with time zone DEFAULT now(),
  CONSTRAINT stripe_prices_pkey PRIMARY KEY (id),
  CONSTRAINT stripe_prices_stripe_product_id_fkey FOREIGN KEY (stripe_product_id) REFERENCES public.stripe_products(stripe_product_id)
);
CREATE TABLE public.stripe_payments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  stripe_payment_intent_id text NOT NULL UNIQUE,
  stripe_customer_id text NOT NULL,
  user_id uuid,
  amount integer NOT NULL,
  currency text DEFAULT 'usd',
  status text NOT NULL,
  payment_method text,
  description text,
  metadata jsonb DEFAULT '{}',
  receipt_url text,
  "created_at" timestamp with time zone DEFAULT now(),
  "updated_at" timestamp with time zone DEFAULT now(),
  CONSTRAINT stripe_payments_pkey PRIMARY KEY (id),
  CONSTRAINT stripe_payments_user_id_fkey FOREIGN KEY (user_id) REFERENCES public."User"(id) ON DELETE CASCADE,
  CONSTRAINT stripe_payments_stripe_customer_id_fkey FOREIGN KEY (stripe_customer_id) REFERENCES public.stripe_customers(stripe_customer_id)
);

CREATE TABLE public.stripe_invoices (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  stripe_invoice_id text NOT NULL UNIQUE,
  stripe_customer_id text NOT NULL,
  stripe_subscription_id text,
  user_id uuid,
  amount_due integer NOT NULL,
  amount_paid integer DEFAULT 0,
  amount_remaining integer DEFAULT 0,
  currency text DEFAULT 'usd',
  status text NOT NULL,
  due_date timestamp with time zone,
  paid_at timestamp with time zone,
  hosted_invoice_url text,
  invoice_pdf text,
  metadata jsonb DEFAULT '{}',
  "created_at" timestamp with time zone DEFAULT now(),
  "updated_at" timestamp with time zone DEFAULT now(),
  CONSTRAINT stripe_invoices_pkey PRIMARY KEY (id),
  CONSTRAINT stripe_invoices_user_id_fkey FOREIGN KEY (user_id) REFERENCES public."User"(id) ON DELETE CASCADE,
  CONSTRAINT stripe_invoices_stripe_customer_id_fkey FOREIGN KEY (stripe_customer_id) REFERENCES public.stripe_customers(stripe_customer_id)
);

CREATE TABLE public.stripe_webhook_events (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  stripe_event_id text NOT NULL UNIQUE,
  event_type text NOT NULL,
  object_id text,
  object_type text,
  livemode boolean DEFAULT false,
  api_version text,
  data jsonb NOT NULL,
  processed_at timestamp with time zone,
  processing_status text DEFAULT 'pending',
  error_message text,
  "created_at" timestamp with time zone DEFAULT now(),
  CONSTRAINT stripe_webhook_events_pkey PRIMARY KEY (id)
);

-- Prisma migrations table
CREATE TABLE public._prisma_migrations (
  id character varying NOT NULL,
  checksum character varying NOT NULL,
  finished_at timestamp with time zone,
  migration_name character varying NOT NULL,
  logs text,
  rolled_back_at timestamp with time zone,
  started_at timestamp with time zone NOT NULL DEFAULT now(),
  applied_steps_count integer NOT NULL DEFAULT 0,
  CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id)
);

-- PostGIS spatial reference systems table (if using PostGIS)
CREATE TABLE IF NOT EXISTS public.spatial_ref_sys (
  srid integer NOT NULL CHECK (srid > 0 AND srid <= 998999),
  auth_name character varying,
  auth_srid integer,
  srtext character varying,
  proj4text character varying,
  CONSTRAINT spatial_ref_sys_pkey PRIMARY KEY (srid)
);
-- =====================================================
-- INDEXES - Create all indexes for performance
-- =====================================================

-- User table indexes
CREATE INDEX idx_user_active ON public."User"("isActive");
CREATE INDEX idx_user_auth_id ON public."User"("authUserId");
CREATE INDEX idx_user_clerk_id ON public."User"("clerkUserId");
CREATE INDEX idx_user_email ON public."User"(email);
CREATE INDEX idx_user_role ON public."User"(role);
CREATE INDEX idx_user_type ON public."User"("userType");

-- Property table indexes
CREATE INDEX idx_property_agent ON public."Property"("agentId");
CREATE INDEX idx_property_listing_date ON public."Property"("listingDate");
CREATE INDEX idx_property_price ON public."Property"(price);
CREATE INDEX idx_property_seller ON public."Property"("sellerId");
CREATE INDEX idx_property_status ON public."Property"(status);
CREATE INDEX idx_property_type ON public."Property"("propertyType");

-- BuyerListings table indexes
CREATE INDEX idx_buyer_listings_buyer ON public."BuyerListings"("buyerId");
CREATE INDEX idx_buyer_listings_favorite ON public."BuyerListings"("isFavorite");
CREATE INDEX idx_buyer_listings_property ON public."BuyerListings"("propertyId");

-- Message table indexes
CREATE INDEX idx_message_conversation ON public."Message"("conversationId");
CREATE INDEX idx_message_created ON public."Message"("createdAt");
CREATE INDEX idx_message_read ON public."Message"("isRead");
CREATE INDEX idx_message_sender ON public."Message"("senderId");

-- Stripe table indexes
CREATE INDEX idx_stripe_customers_user_id ON public.stripe_customers(user_id);
CREATE INDEX idx_stripe_customers_stripe_id ON public.stripe_customers(stripe_customer_id);
CREATE INDEX idx_stripe_payments_user_id ON public.stripe_payments(user_id);
CREATE INDEX idx_stripe_payments_customer_id ON public.stripe_payments(stripe_customer_id);
CREATE INDEX idx_stripe_invoices_user_id ON public.stripe_invoices(user_id);
CREATE INDEX idx_stripe_invoices_subscription_id ON public.stripe_invoices(stripe_subscription_id);
CREATE INDEX idx_stripe_webhook_events_type ON public.stripe_webhook_events(event_type);
CREATE INDEX idx_stripe_webhook_events_object_id ON public.stripe_webhook_events(object_id);

-- Additional performance indexes
CREATE INDEX idx_property_created_at ON public."Property"("createdAt");
CREATE INDEX idx_property_updated_at ON public."Property"("updatedAt");
CREATE INDEX idx_conversation_last_message ON public."Conversation"("lastMessageAt");
CREATE INDEX idx_conversation_active ON public."Conversation"("isActive");
CREATE INDEX idx_notifications_user_read ON public."Notifications"("userId", "isRead");
CREATE INDEX idx_user_activity_user_created ON public."UserActivity"("userId", "createdAt");
CREATE INDEX idx_buyer_offer_property_status ON public."BuyerOffer"("propertyId", status);
CREATE INDEX idx_service_booking_provider_date ON public."ServiceBooking"("serviceProviderId", "scheduledDate");
CREATE INDEX idx_property_viewing_buyer_date ON public."PropertyViewing"("buyerId", "scheduledDate");

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to update conversation last message timestamp
CREATE OR REPLACE FUNCTION update_conversation_last_message()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE public."Conversation"
    SET "lastMessageAt" = NEW."createdAt"
    WHERE id = NEW."conversationId";
    RETURN NEW;
END;
$$ language 'plpgsql';
-- Function to update property analytics on view
CREATE OR REPLACE FUNCTION increment_property_view_count()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public."PropertyAnalytics" ("propertyId", "viewCount", "lastViewedAt", "createdAt", "updatedAt")
    VALUES (NEW."propertyId", 1, NEW."createdAt", CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT ("propertyId")
    DO UPDATE SET
        "viewCount" = public."PropertyAnalytics"."viewCount" + 1,
        "lastViewedAt" = NEW."createdAt",
        "updatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to update property analytics on favorite
CREATE OR REPLACE FUNCTION update_property_favorite_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' AND NEW."isFavorite" = true THEN
        INSERT INTO public."PropertyAnalytics" ("propertyId", "favoriteCount", "createdAt", "updatedAt")
        VALUES (NEW."propertyId", 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ON CONFLICT ("propertyId")
        DO UPDATE SET
            "favoriteCount" = public."PropertyAnalytics"."favoriteCount" + 1,
            "updatedAt" = CURRENT_TIMESTAMP;
    ELSIF TG_OP = 'UPDATE' THEN
        IF OLD."isFavorite" = false AND NEW."isFavorite" = true THEN
            UPDATE public."PropertyAnalytics"
            SET "favoriteCount" = "favoriteCount" + 1, "updatedAt" = CURRENT_TIMESTAMP
            WHERE "propertyId" = NEW."propertyId";
        ELSIF OLD."isFavorite" = true AND NEW."isFavorite" = false THEN
            UPDATE public."PropertyAnalytics"
            SET "favoriteCount" = GREATEST("favoriteCount" - 1, 0), "updatedAt" = CURRENT_TIMESTAMP
            WHERE "propertyId" = NEW."propertyId";
        END IF;
    ELSIF TG_OP = 'DELETE' AND OLD."isFavorite" = true THEN
        UPDATE public."PropertyAnalytics"
        SET "favoriteCount" = GREATEST("favoriteCount" - 1, 0), "updatedAt" = CURRENT_TIMESTAMP
        WHERE "propertyId" = OLD."propertyId";
    END IF;

    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ language 'plpgsql';

-- Function to update property analytics on offer
CREATE OR REPLACE FUNCTION increment_property_offer_count()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public."PropertyAnalytics" ("propertyId", "offerCount", "createdAt", "updatedAt")
    VALUES (NEW."propertyId", 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT ("propertyId")
    DO UPDATE SET
        "offerCount" = public."PropertyAnalytics"."offerCount" + 1,
        "updatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to create property analytics record when property is created
CREATE OR REPLACE FUNCTION create_property_analytics()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public."PropertyAnalytics" ("propertyId", "createdAt", "updatedAt")
    VALUES (NEW.id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
    RETURN NEW;
END;
$$ language 'plpgsql';
-- =====================================================
-- TRIGGERS - Create all triggers
-- =====================================================

-- Updated_at triggers for all tables with updatedAt column
CREATE TRIGGER update_user_updated_at BEFORE UPDATE ON public."User"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_property_updated_at BEFORE UPDATE ON public."Property"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_buyer_listings_updated_at BEFORE UPDATE ON public."BuyerListings"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_buyer_offer_updated_at BEFORE UPDATE ON public."BuyerOffer"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_buyer_profile_updated_at BEFORE UPDATE ON public."BuyerProfile"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_seller_profile_updated_at BEFORE UPDATE ON public."SellerProfile"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_conversation_updated_at BEFORE UPDATE ON public."Conversation"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_provider_updated_at BEFORE UPDATE ON public."ServiceProvider"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_booking_updated_at BEFORE UPDATE ON public."ServiceBooking"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_property_viewing_updated_at BEFORE UPDATE ON public."PropertyViewing"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_search_filter_updated_at BEFORE UPDATE ON public."SearchFilter"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscription_updated_at BEFORE UPDATE ON public."Subscription"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_property_analytics_updated_at BEFORE UPDATE ON public."PropertyAnalytics"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_advertiser_updated_at BEFORE UPDATE ON public."Advertiser"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_advertiser_subscription_updated_at BEFORE UPDATE ON public."AdvertiserSubscription"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_audiences_updated_at BEFORE UPDATE ON public."Audiences"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_role_permissions_updated_at BEFORE UPDATE ON public."role_permissions"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Conversation last message update trigger
CREATE TRIGGER update_conversation_last_message_trigger
    AFTER INSERT ON public."Message"
    FOR EACH ROW EXECUTE FUNCTION update_conversation_last_message();

-- Property analytics triggers
CREATE TRIGGER create_property_analytics_trigger
    AFTER INSERT ON public."Property"
    FOR EACH ROW EXECUTE FUNCTION create_property_analytics();

CREATE TRIGGER update_property_favorite_count_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public."BuyerListings"
    FOR EACH ROW EXECUTE FUNCTION update_property_favorite_count();

CREATE TRIGGER increment_property_offer_count_trigger
    AFTER INSERT ON public."BuyerOffer"
    FOR EACH ROW EXECUTE FUNCTION increment_property_offer_count();
-- =====================================================
-- STORED PROCEDURES AND FUNCTIONS
-- =====================================================

-- Procedure to search properties with filters
CREATE OR REPLACE FUNCTION search_properties(
    p_min_price DECIMAL DEFAULT NULL,
    p_max_price DECIMAL DEFAULT NULL,
    p_min_bedrooms INTEGER DEFAULT NULL,
    p_max_bedrooms INTEGER DEFAULT NULL,
    p_min_bathrooms DECIMAL DEFAULT NULL,
    p_max_bathrooms DECIMAL DEFAULT NULL,
    p_property_types TEXT[] DEFAULT NULL,
    p_status "PropertyStatus" DEFAULT 'active',
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    title TEXT,
    description TEXT,
    price DECIMAL,
    address JSONB,
    bedrooms INTEGER,
    bathrooms DECIMAL,
    "squareFeet" INTEGER,
    "propertyType" TEXT,
    status "PropertyStatus",
    "listingDate" TIMESTAMPTZ,
    "sellerId" UUID,
    "agentId" UUID,
    "createdAt" TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id, p.title, p.description, p.price, p.address,
        p.bedrooms, p.bathrooms, p."squareFeet", p."propertyType",
        p.status, p."listingDate", p."sellerId", p."agentId", p."createdAt"
    FROM public."Property" p
    WHERE
        (p_min_price IS NULL OR p.price >= p_min_price) AND
        (p_max_price IS NULL OR p.price <= p_max_price) AND
        (p_min_bedrooms IS NULL OR p.bedrooms >= p_min_bedrooms) AND
        (p_max_bedrooms IS NULL OR p.bedrooms <= p_max_bedrooms) AND
        (p_min_bathrooms IS NULL OR p.bathrooms >= p_min_bathrooms) AND
        (p_max_bathrooms IS NULL OR p.bathrooms <= p_max_bathrooms) AND
        (p_property_types IS NULL OR p."propertyType" = ANY(p_property_types)) AND
        p.status = p_status
    ORDER BY p."listingDate" DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Procedure to get user dashboard statistics
CREATE OR REPLACE FUNCTION get_user_dashboard_stats(p_user_id UUID)
RETURNS TABLE (
    total_properties INTEGER,
    active_properties INTEGER,
    total_views INTEGER,
    total_favorites INTEGER,
    total_offers INTEGER,
    total_messages INTEGER,
    unread_messages INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        (SELECT COUNT(*)::INTEGER FROM public."Property" WHERE "sellerId" = p_user_id),
        (SELECT COUNT(*)::INTEGER FROM public."Property" WHERE "sellerId" = p_user_id AND status = 'active'),
        (SELECT COALESCE(SUM("viewCount"), 0)::INTEGER FROM public."PropertyAnalytics" pa
         JOIN public."Property" p ON pa."propertyId" = p.id WHERE p."sellerId" = p_user_id),
        (SELECT COALESCE(SUM("favoriteCount"), 0)::INTEGER FROM public."PropertyAnalytics" pa
         JOIN public."Property" p ON pa."propertyId" = p.id WHERE p."sellerId" = p_user_id),
        (SELECT COUNT(*)::INTEGER FROM public."BuyerOffer" bo
         JOIN public."Property" p ON bo."propertyId" = p.id WHERE p."sellerId" = p_user_id),
        (SELECT COUNT(*)::INTEGER FROM public."Message" m
         JOIN public."Conversation" c ON m."conversationId" = c.id
         WHERE c."sellerId" = p_user_id OR c."buyerId" = p_user_id OR c."agentId" = p_user_id),
        (SELECT COUNT(*)::INTEGER FROM public."Message" m
         JOIN public."Conversation" c ON m."conversationId" = c.id
         WHERE (c."sellerId" = p_user_id OR c."buyerId" = p_user_id OR c."agentId" = p_user_id)
         AND m."isRead" = false AND m."senderId" != p_user_id);
END;
$$ LANGUAGE plpgsql;
-- Procedure to get property recommendations
CREATE OR REPLACE FUNCTION get_property_recommendations(
    p_user_id UUID,
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    title TEXT,
    price DECIMAL,
    bedrooms INTEGER,
    bathrooms DECIMAL,
    "propertyType" TEXT,
    "listingDate" TIMESTAMPTZ,
    similarity_score DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    WITH user_preferences AS (
        SELECT
            COALESCE(AVG(p.price), 500000) as avg_price,
            COALESCE(MODE() WITHIN GROUP (ORDER BY p.bedrooms), 3) as preferred_bedrooms,
            COALESCE(MODE() WITHIN GROUP (ORDER BY p."propertyType"), 'house') as preferred_type
        FROM public."BuyerListings" bl
        JOIN public."Property" p ON bl."propertyId" = p.id
        WHERE bl."buyerId" = p_user_id AND bl."isFavorite" = true
    )
    SELECT
        p.id, p.title, p.price, p.bedrooms, p.bathrooms,
        p."propertyType", p."listingDate",
        (
            CASE WHEN ABS(p.price - up.avg_price) < up.avg_price * 0.2 THEN 0.4 ELSE 0.0 END +
            CASE WHEN p.bedrooms = up.preferred_bedrooms THEN 0.3 ELSE 0.0 END +
            CASE WHEN p."propertyType" = up.preferred_type THEN 0.3 ELSE 0.0 END
        )::DECIMAL as similarity_score
    FROM public."Property" p
    CROSS JOIN user_preferences up
    WHERE p.status = 'active'
    AND NOT EXISTS (
        SELECT 1 FROM public."BuyerListings" bl
        WHERE bl."propertyId" = p.id AND bl."buyerId" = p_user_id
    )
    ORDER BY similarity_score DESC, p."listingDate" DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Procedure to mark messages as read
CREATE OR REPLACE FUNCTION mark_messages_as_read(
    p_conversation_id UUID,
    p_user_id UUID
)
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    UPDATE public."Message"
    SET "isRead" = true, "readAt" = CURRENT_TIMESTAMP
    WHERE "conversationId" = p_conversation_id
    AND "senderId" != p_user_id
    AND "isRead" = false;

    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- Procedure to get property analytics summary
CREATE OR REPLACE FUNCTION get_property_analytics_summary(p_property_id UUID)
RETURNS TABLE (
    property_id UUID,
    view_count INTEGER,
    favorite_count INTEGER,
    inquiry_count INTEGER,
    viewing_count INTEGER,
    offer_count INTEGER,
    last_viewed_at TIMESTAMPTZ,
    total_messages INTEGER,
    active_conversations INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        pa."propertyId",
        COALESCE(pa."viewCount", 0),
        COALESCE(pa."favoriteCount", 0),
        COALESCE(pa."inquiryCount", 0),
        COALESCE(pa."viewingCount", 0),
        COALESCE(pa."offerCount", 0),
        pa."lastViewedAt",
        (SELECT COUNT(*)::INTEGER FROM public."Message" m
         JOIN public."Conversation" c ON m."conversationId" = c.id
         WHERE c."propertyId" = p_property_id),
        (SELECT COUNT(*)::INTEGER FROM public."Conversation"
         WHERE "propertyId" = p_property_id AND "isActive" = true)
    FROM public."PropertyAnalytics" pa
    WHERE pa."propertyId" = p_property_id;
END;
$$ LANGUAGE plpgsql;
-- Procedure to cleanup expired open house access
CREATE OR REPLACE FUNCTION cleanup_expired_open_house_access()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    UPDATE public."OpenHouseAccess"
    SET "isActive" = false
    WHERE "endTime" < CURRENT_TIMESTAMP AND "isActive" = true;

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Procedure to get user activity summary
CREATE OR REPLACE FUNCTION get_user_activity_summary(
    p_user_id UUID,
    p_days INTEGER DEFAULT 30
)
RETURNS TABLE (
    activity_type TEXT,
    activity_count BIGINT,
    last_activity TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        ua."activityType",
        COUNT(*),
        MAX(ua."createdAt")
    FROM public."UserActivity" ua
    WHERE ua."userId" = p_user_id
    AND ua."createdAt" >= CURRENT_TIMESTAMP - INTERVAL '1 day' * p_days
    GROUP BY ua."activityType"
    ORDER BY COUNT(*) DESC;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- ADDITIONAL TABLES FOR REACT APPLICATION
-- =====================================================

-- BuyerProfile table
CREATE TABLE IF NOT EXISTS public."BuyerProfile" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "userId" uuid NOT NULL,
  "preApprovalAmount" numeric,
  "preApprovalDate" date,
  "lenderName" text,
  "agentId" uuid,
  preferences jsonb DEFAULT '{}',
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "BuyerProfile_pkey" PRIMARY KEY (id),
  CONSTRAINT "BuyerProfile_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- SellerProfile table
CREATE TABLE IF NOT EXISTS public."SellerProfile" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "userId" uuid NOT NULL,
  "businessName" text,
  "licenseNumber" text,
  "yearsExperience" integer,
  "specializations" text[],
  "serviceAreas" text[],
  preferences jsonb DEFAULT '{}',
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "SellerProfile_pkey" PRIMARY KEY (id),
  CONSTRAINT "SellerProfile_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- BuyerListings table
CREATE TABLE IF NOT EXISTS public."BuyerListings" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "buyerId" uuid NOT NULL,
  title text NOT NULL,
  description text,
  budget numeric,
  "preferredAreas" text[],
  requirements jsonb DEFAULT '{}',
  "isActive" boolean DEFAULT true,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "BuyerListings_pkey" PRIMARY KEY (id),
  CONSTRAINT "BuyerListings_buyerId_fkey" FOREIGN KEY ("buyerId") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- PropertyAnalytics table
CREATE TABLE IF NOT EXISTS public."PropertyAnalytics" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "propertyId" uuid NOT NULL,
  "viewCount" integer DEFAULT 0,
  "favoriteCount" integer DEFAULT 0,
  "contactCount" integer DEFAULT 0,
  "offerCount" integer DEFAULT 0,
  "lastViewedAt" timestamp with time zone,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "PropertyAnalytics_pkey" PRIMARY KEY (id),
  CONSTRAINT "PropertyAnalytics_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE
);

-- PropertyQrCode table
CREATE TABLE IF NOT EXISTS public."PropertyQrCode" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "propertyId" uuid NOT NULL,
  "sellerId" uuid NOT NULL,
  "qrCodeData" text NOT NULL,
  "qrCodeImageUrl" text,
  "isActive" boolean DEFAULT true,
  "expiresAt" timestamp with time zone,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "PropertyQrCode_pkey" PRIMARY KEY (id),
  CONSTRAINT "PropertyQrCode_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE,
  CONSTRAINT "PropertyQrCode_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- PropertyViewing table
CREATE TABLE IF NOT EXISTS public."PropertyViewing" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "propertyId" uuid NOT NULL,
  "buyerId" uuid NOT NULL,
  "sellerId" uuid NOT NULL,
  "viewedAt" timestamp with time zone DEFAULT now(),
  "duration" integer,
  "deviceInfo" text,
  "ipAddress" inet,
  "createdAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "PropertyViewing_pkey" PRIMARY KEY (id),
  CONSTRAINT "PropertyViewing_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE,
  CONSTRAINT "PropertyViewing_buyerId_fkey" FOREIGN KEY ("buyerId") REFERENCES public."User"(id) ON DELETE CASCADE,
  CONSTRAINT "PropertyViewing_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- PropertyVisitSchedule table
CREATE TABLE IF NOT EXISTS public."PropertyVisitSchedule" (
  id text NOT NULL DEFAULT (gen_random_uuid())::text,
  "contactShareId" text NOT NULL,
  "propertyId" uuid NOT NULL,
  "buyerId" uuid NOT NULL,
  "sellerId" uuid NOT NULL,
  "scheduledDate" date NOT NULL,
  "scheduledTime" time NOT NULL,
  "visitType" "VisitType" DEFAULT 'InPerson',
  status "VisitStatus" DEFAULT 'Pending',
  "buyerNotes" text,
  "sellerNotes" text,
  "confirmationCode" text,
  "reminderSent" boolean DEFAULT false,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "PropertyVisitSchedule_pkey" PRIMARY KEY (id),
  CONSTRAINT "PropertyVisitSchedule_contactShareId_fkey" FOREIGN KEY ("contactShareId") REFERENCES public."ContactShare"(id) ON DELETE CASCADE,
  CONSTRAINT "PropertyVisitSchedule_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE,
  CONSTRAINT "PropertyVisitSchedule_buyerId_fkey" FOREIGN KEY ("buyerId") REFERENCES public."User"(id) ON DELETE CASCADE,
  CONSTRAINT "PropertyVisitSchedule_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- UserActivity table
CREATE TABLE IF NOT EXISTS public."UserActivity" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "userId" uuid NOT NULL,
  action text NOT NULL,
  "entityType" text,
  "entityId" uuid,
  metadata jsonb,
  "ipAddress" inet,
  "userAgent" text,
  "createdAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "UserActivity_pkey" PRIMARY KEY (id),
  CONSTRAINT "UserActivity_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- Notifications table
CREATE TABLE IF NOT EXISTS public."Notifications" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "userId" uuid NOT NULL,
  title text NOT NULL,
  message text NOT NULL,
  "notificationType" text NOT NULL,
  "entityType" text,
  "entityId" uuid,
  "isRead" boolean DEFAULT false,
  "readAt" timestamp with time zone,
  "createdAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "Notifications_pkey" PRIMARY KEY (id),
  CONSTRAINT "Notifications_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- SearchFilter table
CREATE TABLE IF NOT EXISTS public."SearchFilter" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "userId" uuid NOT NULL,
  name text NOT NULL,
  filters jsonb NOT NULL,
  "isActive" boolean DEFAULT true,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "SearchFilter_pkey" PRIMARY KEY (id),
  CONSTRAINT "SearchFilter_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- SellerAvailability table
CREATE TABLE IF NOT EXISTS public."SellerAvailability" (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  "sellerId" uuid NOT NULL,
  "propertyId" uuid NOT NULL,
  "dayOfWeek" integer NOT NULL,
  "startTime" time NOT NULL,
  "endTime" time NOT NULL,
  "isAvailable" boolean DEFAULT true,
  "createdAt" timestamp with time zone DEFAULT now(),
  "updatedAt" timestamp with time zone DEFAULT now(),
  CONSTRAINT "SellerAvailability_pkey" PRIMARY KEY (id),
  CONSTRAINT "SellerAvailability_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES public."User"(id) ON DELETE CASCADE,
  CONSTRAINT "SellerAvailability_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE
);

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Insert default role permissions
INSERT INTO public."role_permissions" (role, permission, resource) VALUES
('ADMIN', 'CREATE', 'users'),
('ADMIN', 'READ', 'users'),
('ADMIN', 'UPDATE', 'users'),
('ADMIN', 'DELETE', 'users'),
('ADMIN', 'CREATE', 'properties'),
('ADMIN', 'READ', 'properties'),
('ADMIN', 'UPDATE', 'properties'),
('ADMIN', 'DELETE', 'properties'),
('ADMIN', 'READ', 'analytics'),
('USER', 'CREATE', 'properties'),
('USER', 'READ', 'properties'),
('USER', 'UPDATE', 'own_properties'),
('USER', 'DELETE', 'own_properties'),
('USER', 'CREATE', 'messages'),
('USER', 'READ', 'own_messages'),
('OPERATOR', 'READ', 'properties'),
('OPERATOR', 'UPDATE', 'property_status'),
('service_provider', 'CREATE', 'services'),
('service_provider', 'READ', 'services'),
('service_provider', 'UPDATE', 'own_services')
ON CONFLICT (role, permission) DO NOTHING;

-- =====================================================
-- SCRIPT COMPLETION
-- =====================================================

-- Create a function to verify database setup
CREATE OR REPLACE FUNCTION verify_database_setup()
RETURNS TABLE (
    table_name TEXT,
    row_count BIGINT,
    has_indexes BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        t.table_name::TEXT,
        (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = t.table_name AND table_schema = 'public'),
        (SELECT COUNT(*) > 0 FROM pg_indexes WHERE tablename = t.table_name AND schemaname = 'public')
    FROM information_schema.tables t
    WHERE t.table_schema = 'public'
    AND t.table_type = 'BASE TABLE'
    AND t.table_name NOT LIKE 'pg_%'
    AND t.table_name NOT LIKE 'sql_%'
    ORDER BY t.table_name;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- END OF SCRIPT
-- =====================================================

-- To verify the setup, run: SELECT * FROM verify_database_setup();
-- To check enum types, run: SELECT typname FROM pg_type WHERE typtype = 'e';
-- To check functions, run: SELECT proname FROM pg_proc WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');

COMMENT ON DATABASE postgres IS 'SoNoBrokers Real Estate Platform Database - Complete setup with all tables, indexes, functions, triggers, and stored procedures';
