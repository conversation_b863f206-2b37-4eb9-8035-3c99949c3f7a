using MicroSaasWebApi.Models;

namespace MicroSaasWebApi.Services.Configuration
{
    /// <summary>
    /// Service for managing application configuration
    /// </summary>
    public interface IAppConfigurationService
    {
        AppSettings GetAppSettings();
        string GetConnectionString();
        bool IsFeatureEnabled(string featureName);
        T GetSetting<T>(string key, T defaultValue);
        void RefreshSettings();
    }

    /// <summary>
    /// Implementation of application configuration service
    /// </summary>
    public class AppConfigurationService : IAppConfigurationService
    {
        private readonly AppSettings _appSettings;
        private readonly ILogger<AppConfigurationService> _logger;

        public AppConfigurationService(AppSettings appSettings, ILogger<AppConfigurationService> logger)
        {
            _appSettings = appSettings;
            _logger = logger;
        }

        public AppSettings GetAppSettings()
        {
            return _appSettings;
        }

        public string GetConnectionString()
        {
            return _appSettings.GetConnectionString();
        }

        public bool IsFeatureEnabled(string featureName)
        {
            return featureName.ToLower() switch
            {
                "ratelimiting" => !string.IsNullOrEmpty(EnvironmentConfigurationService.GetEnvironmentVariable("ENABLE_RATE_LIMITING")) &&
                                 bool.TryParse(EnvironmentConfigurationService.GetEnvironmentVariable("ENABLE_RATE_LIMITING"), out var rateLimitEnabled) && rateLimitEnabled,

                "filelogging" => _appSettings.Logging.EnableFileLogging,

                "consolelogging" => _appSettings.Logging.EnableConsoleLogging,

                "azureintegration" => !string.IsNullOrEmpty(_appSettings.KeyVaultUri) || !string.IsNullOrEmpty(_appSettings.AppConfigurationEndpoint),

                "stripeintegration" => !string.IsNullOrEmpty(_appSettings.StripeSecretKey),

                "clerkauth" => !string.IsNullOrEmpty(_appSettings.ClerkSecretKey),

                "makeintegration" => !string.IsNullOrEmpty(_appSettings.MakeApiKey),

                "n8nintegration" => !string.IsNullOrEmpty(_appSettings.N8NApiKey),

                "wordpressintegration" => !string.IsNullOrEmpty(_appSettings.WordPressRestEndpoint),

                "emailintegration" => !string.IsNullOrEmpty(_appSettings.ResendApiKey),

                _ => false
            };
        }

        public T GetSetting<T>(string key, T defaultValue)
        {
            try
            {
                var value = EnvironmentConfigurationService.GetEnvironmentVariable(key);
                if (string.IsNullOrEmpty(value))
                {
                    return defaultValue;
                }

                // Handle different types
                if (typeof(T) == typeof(bool))
                {
                    return (T)(object)bool.Parse(value);
                }
                else if (typeof(T) == typeof(int))
                {
                    return (T)(object)int.Parse(value);
                }
                else if (typeof(T) == typeof(decimal))
                {
                    return (T)(object)decimal.Parse(value);
                }
                else if (typeof(T) == typeof(double))
                {
                    return (T)(object)double.Parse(value);
                }
                else if (typeof(T) == typeof(DateTime))
                {
                    return (T)(object)DateTime.Parse(value);
                }
                else
                {
                    return (T)(object)value;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error parsing setting {Key}, returning default value", key);
                return defaultValue;
            }
        }

        public void RefreshSettings()
        {
            // In a more complex scenario, this could reload settings from external sources
            // For now, environment variables are static during application lifetime
            _logger.LogInformation("Settings refresh requested - environment variables are static during application lifetime");
        }

        /// <summary>
        /// Validate all required settings and return any missing ones
        /// </summary>
        public List<string> ValidateSettings()
        {
            return _appSettings.ValidateRequiredSettings();
        }

        /// <summary>
        /// Get feature flags configuration
        /// </summary>
        public Dictionary<string, bool> GetFeatureFlags()
        {
            return new Dictionary<string, bool>
            {
                { "RateLimiting", IsFeatureEnabled("ratelimiting") },
                { "FileLogging", IsFeatureEnabled("filelogging") },
                { "ConsoleLogging", IsFeatureEnabled("consolelogging") },
                { "AzureIntegration", IsFeatureEnabled("azureintegration") },
                { "StripeIntegration", IsFeatureEnabled("stripeintegration") },
                { "ClerkAuth", IsFeatureEnabled("clerkauth") },
                { "MakeIntegration", IsFeatureEnabled("makeintegration") },
                { "N8NIntegration", IsFeatureEnabled("n8nintegration") },
                { "WordPressIntegration", IsFeatureEnabled("wordpressintegration") },
                { "EmailIntegration", IsFeatureEnabled("emailintegration") }
            };
        }

        /// <summary>
        /// Get environment-specific configuration
        /// </summary>
        public EnvironmentConfig GetEnvironmentConfig()
        {
            return new EnvironmentConfig
            {
                Environment = _appSettings.Environment,
                IsDevelopment = _appSettings.Environment.Equals("Development", StringComparison.OrdinalIgnoreCase),
                IsProduction = _appSettings.Environment.Equals("Production", StringComparison.OrdinalIgnoreCase),
                IsStaging = _appSettings.Environment.Equals("Staging", StringComparison.OrdinalIgnoreCase),
                AppUrl = _appSettings.AppUrl,
                ApiUrl = _appSettings.ApiUrl,
                AllowedHosts = _appSettings.AllowedHosts
            };
        }
    }

    /// <summary>
    /// Environment-specific configuration
    /// </summary>
    public class EnvironmentConfig
    {
        public string Environment { get; set; } = string.Empty;
        public bool IsDevelopment { get; set; }
        public bool IsProduction { get; set; }
        public bool IsStaging { get; set; }
        public string AppUrl { get; set; } = string.Empty;
        public string ApiUrl { get; set; } = string.Empty;
        public string AllowedHosts { get; set; } = string.Empty;
    }
}
