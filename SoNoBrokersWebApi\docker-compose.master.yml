# =============================================================================
# SoNoBrokers Web API - Master Docker Compose
# =============================================================================
# Orchestrates all services: App, Config, and Tests
# Use this for complete deployment or development environment
# =============================================================================

version: '3.8'

services:
  # Main Web API Application
  sonobrokers-api:
    build:
      context: ./MicroSaasWebApi.App
      dockerfile: Dockerfile
    container_name: sonobrokers-api
    restart: unless-stopped
    ports:
      - "7163:8080"   # HTTP
      - "7164:8443"   # HTTPS
    environment:
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT:-Production}
      - ASPNETCORE_URLS=http://+:8080;https://+:8443
      - DATABASE_URL=${DATABASE_URL}
      - CLERK_SECRET_KEY=${CLERK_SECRET_KEY}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - RESEND_API_KEY=${RESEND_API_KEY}
      - CORS_ALLOWED_ORIGINS=https://localhost:3000,https://www.api.sonobrokers.com,https://sonobrokers.com,https://localhost:7163
    volumes:
      - api-logs:/app/logs
    networks:
      - sonobrokers-network
    depends_on:
      - sonobrokers-config
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health/ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    labels:
      - "com.sonobrokers.service=api"
      - "com.sonobrokers.environment=${ASPNETCORE_ENVIRONMENT:-production}"

  # Configuration Server
  sonobrokers-config:
    build:
      context: ./MicroSaasWebApi.Config
      dockerfile: Dockerfile
    container_name: sonobrokers-config
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - config-data:/usr/share/nginx/html/config
    networks:
      - sonobrokers-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    labels:
      - "com.sonobrokers.service=config"

  # Test Runner (optional - only for development/CI)
  sonobrokers-tests:
    build:
      context: ./MicroSaasWebApi.Tests
      dockerfile: Dockerfile
    container_name: sonobrokers-tests
    profiles:
      - testing
      - development
    environment:
      - ASPNETCORE_ENVIRONMENT=Test
      - DATABASE_URL=${TEST_DATABASE_URL:-${DATABASE_URL}}
      - CLERK_SECRET_KEY=${TEST_CLERK_SECRET_KEY:-${CLERK_SECRET_KEY}}
      - TEST_API_BASE_URL=http://sonobrokers-api:8080
    volumes:
      - test-results:/app/test-results
    networks:
      - sonobrokers-network
    depends_on:
      - sonobrokers-api
    labels:
      - "com.sonobrokers.service=tests"

volumes:
  api-logs:
    driver: local
  config-data:
    driver: local
  test-results:
    driver: local

networks:
  sonobrokers-network:
    driver: bridge
    name: sonobrokers-network
