import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

export default function UAEHeroSection() {
  return (
    <section className="relative bg-gradient-to-br from-emerald-600 via-emerald-700 to-emerald-800 text-white py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-6">
            Discover Luxury Properties in the 
            <span className="text-gold-400"> UAE</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-emerald-100">
            From Dubai's skyline to Abu Dhabi's elegance - find your perfect property in the Emirates
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button size="lg" className="bg-gold-500 hover:bg-gold-600 text-emerald-900 font-semibold">
              Explore Properties
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-emerald-900">
              List Your Property
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-gold-400 mb-2">7</div>
                <div className="text-emerald-100">Emirates Covered</div>
              </CardContent>
            </Card>
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-gold-400 mb-2">0%</div>
                <div className="text-emerald-100">Commission Fees</div>
              </CardContent>
            </Card>
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-gold-400 mb-2">24/7</div>
                <div className="text-emerald-100">Arabic & English Support</div>
              </CardContent>
            </Card>
          </div>

          <div className="mt-12 text-center">
            <p className="text-emerald-200 text-lg">
              🏙️ Dubai • 🏛️ Abu Dhabi • 🏖️ Sharjah • 🌊 Ajman • 🏔️ Ras Al Khaimah • 🌴 Fujairah • 🏜️ Umm Al Quwain
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
