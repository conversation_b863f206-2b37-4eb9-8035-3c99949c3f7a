namespace MicroSaasWebApi.Models.SoNoBrokers
{
    /// <summary>
    /// User role enum matching React frontend
    /// </summary>
    public enum UserRole
    {
        USER,
        ADMIN,
        SUPER_ADMIN
    }

    /// <summary>
    /// SoNoBrokers user type enum matching React frontend
    /// </summary>
    public enum SnbUserType
    {
        USER,
        ADMIN,
        SELLER,
        BUYER
    }

    /// <summary>
    /// Country enum
    /// </summary>
    public enum Country
    {
        CA,
        US,
        AE
    }

    /// <summary>
    /// Property status enum
    /// </summary>
    public enum PropertyStatus
    {
        pending,
        active,
        sold,
        expired
    }

    /// <summary>
    /// Advertiser plan enum
    /// </summary>
    public enum AdvertiserPlan
    {
        basic,
        premium,
        enterprise
    }

    /// <summary>
    /// Advertiser status enum
    /// </summary>
    public enum AdvertiserStatus
    {
        pending,
        active,
        suspended,
        cancelled
    }

    /// <summary>
    /// Service type enum
    /// </summary>
    public enum ServiceType
    {
        lawyer,
        photographer,
        inspector,
        appraiser,
        home_inspector,
        mortgage_broker,
        insurance_agent,
        contractor,
        cleaner,
        stager,
        marketing_agency
    }
}
