namespace MicroSaasWebApi.Models.SoNoBrokers
{
    /// <summary>
    /// User role enum matching database values
    /// </summary>
    public enum UserRole
    {
        ADMIN,
        USER,
        PRODUCT,
        OPERATOR,
        SERVICE_PROVIDER
    }

    /// <summary>
    /// User status enum matching database values
    /// </summary>
    public enum UserStatus
    {
        ACTIVE,
        INACTIVE,
        SUSPENDED,
        PENDING
    }

    /// <summary>
    /// SoNoBrokers user type enum matching database values
    /// </summary>
    public enum SnbUserType
    {
        Buyer,
        Seller
    }

    /// <summary>
    /// Country enum matching database values
    /// </summary>
    public enum Country
    {
        CA,
        US,
        UAE
    }

    /// <summary>
    /// Property status enum matching database values
    /// </summary>
    public enum PropertyStatus
    {
        ACTIVE,
        SOLD,
        PENDING,
        WITHDRAWN,
        EXPIRED,
        DRAFT
    }

    /// <summary>
    /// Advertiser plan enum
    /// </summary>
    public enum AdvertiserPlan
    {
        basic,
        premium,
        enterprise
    }

    /// <summary>
    /// Advertiser status enum
    /// </summary>
    public enum AdvertiserStatus
    {
        pending,
        active,
        suspended,
        cancelled
    }

    /// <summary>
    /// Service type enum
    /// </summary>
    public enum ServiceType
    {
        lawyer,
        photographer,
        inspector,
        appraiser,
        home_inspector,
        mortgage_broker,
        insurance_agent,
        contractor,
        cleaner,
        stager,
        marketing_agency
    }
}
