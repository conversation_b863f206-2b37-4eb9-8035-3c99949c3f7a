import { Outfit } from 'next/font/google'
import { ReactNode, Suspense } from 'react'
import type { Metadata, Viewport } from 'next'
// import { headers } from 'next/headers' // Removed unused import to fix Next.js 15 compatibility
import { NavigationEvents } from '@/components/shared/common/NavigationEvents'
import { RegionCheck } from '@/components/shared/common/RegionCheck'
import { LaunchPage } from '@/components/shared/common/LaunchPage'
import { Analytics } from '@vercel/analytics/next'
import { SpeedInsights } from '@vercel/speed-insights/next'
import { ThemeProvider } from '@/components/providers/theme-provider'
import { Toaster } from '@/components/ui/toaster'
import { Header } from '@/components/shared/layout/Header'
import { Footer } from '@/components/shared/layout/Footer'
import { AppClientProvider } from '@/components/providers/AppClientProvider'
import { ClerkClientProvider } from '@/components/providers/ClerkClientProvider'
import { I18nProvider } from '@/components/providers/I18nProvider'
import { ErrorBoundary } from '@/components/shared/common/ErrorBoundary'

import cn from 'classnames'
import '@/assets/styles/globals.css'

const outfit = Outfit({
	subsets: ['latin'],
	variable: '--font-sans',
	display: 'swap',
})

export const viewport: Viewport = {
	themeColor: 'oklch(0.795 0.184 86.047)',
	width: 'device-width',
	initialScale: 1,
}

export const metadata: Metadata = {
	title: "SoNo Brokers",
	description: "Find your perfect home with SoNo Brokers",
	icons: {
		icon: "/favicon.ico",
	},
}

export default async function RootLayout({ children }: { children: ReactNode }) {
	// Check if launch mode is enabled
	const isLaunchMode = process.env.NEXT_PUBLIC_LAUNCH_MODE === 'true';

	return (
		<html lang='en' suppressHydrationWarning>
			<head>
				<script
					dangerouslySetInnerHTML={{
						__html: `
						try {
							const theme = localStorage.getItem('theme') || 'light';
							if (theme === 'dark') {
								document.documentElement.classList.add('dark');
							} else {
								document.documentElement.classList.remove('dark');
							}
						} catch (e) {
							// Default to light mode if localStorage is not available
							document.documentElement.classList.remove('dark');
						}
					`,
					}}
				/>
			</head>
			<body className={cn('min-h-screen bg-background font-sans antialiased', outfit.variable)} suppressHydrationWarning>
				<ErrorBoundary>
					<I18nProvider>
						<ClerkClientProvider>
							<ThemeProvider
								attribute="class"
								defaultTheme="light"
								enableSystem
								disableTransitionOnChange
							>
								<AppClientProvider>
									{isLaunchMode ? (
										// Launch mode: Show launch page without header/footer
										<div className="relative min-h-screen">
											<LaunchPage />
											<Toaster />
											<Suspense fallback={null}>
												<NavigationEvents />
											</Suspense>
										</div>
									) : (
										// Normal mode: Show app with header/footer and region checking
										<RegionCheck>
											<div className="relative min-h-screen flex flex-col px-4">
												<Header />
												<main className="flex-1">
													{children}
												</main>
												<Footer />
												<Toaster />
												<Suspense fallback={null}>
													<NavigationEvents />
												</Suspense>
											</div>
										</RegionCheck>
									)}
								</AppClientProvider>
							</ThemeProvider>
						</ClerkClientProvider>
					</I18nProvider>
				</ErrorBoundary>
				<Analytics />
				<SpeedInsights />
			</body>
		</html>
	)
}
