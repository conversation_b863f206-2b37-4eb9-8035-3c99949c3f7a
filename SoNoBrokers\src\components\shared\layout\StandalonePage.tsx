import { Outfit } from 'next/font/google'
import '@/assets/styles/globals.css'

const outfit = Outfit({
  subsets: ['latin'],
  variable: '--font-sans',
  display: 'swap',
})

interface StandalonePageProps {
  children: React.ReactNode
  title: string
  description: string
  noIndex?: boolean
}

/**
 * Standalone page wrapper for pages that don't use the main layout
 * Uses the same theme system from globals.css
 * Used by: unsupported-region, waiting-list, launch pages
 */
export function StandalonePage({
  children,
  title,
  description,
  noIndex = true
}: StandalonePageProps) {
  return (
    <html lang="en" className={outfit.variable} suppressHydrationWarning>
      <head>
        <title>{title}</title>
        <meta name="description" content={description} />
        {noIndex && <meta name="robots" content="noindex, nofollow" />}
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              try {
                const theme = localStorage.getItem('theme') || 'light';
                if (theme === 'dark') {
                  document.documentElement.classList.add('dark');
                } else {
                  document.documentElement.classList.remove('dark');
                }
              } catch (e) {
                document.documentElement.classList.remove('dark');
              }
            `,
          }}
        />
      </head>
      <body className="min-h-screen bg-background font-sans antialiased" suppressHydrationWarning>
        {children}
      </body>
    </html>
  )
}
