# SoNoBrokers API Reference

## Overview

Complete API reference for all SoNoBrokers Web API endpoints, organized by feature area and functionality.

## Base URLs

| Environment | URL | Description |
|-------------|-----|-------------|
| Development | `https://localhost:7163` | Local development |
| Staging | `https://staging-api.sonobrokers.com` | Staging environment |
| Production | `https://api.sonobrokers.com` | Production environment |

## 📖 API Documentation

- **Interactive Documentation**: Available at `/scalar/v1` (e.g., `https://localhost:7163/scalar/v1`)
- **OpenAPI Specification**: Available at `/openapi/v1.json`
- **Health Checks**: Available at `/health` and `/healthchecks-ui`

## Authentication

All protected endpoints require Clerk JWT authentication:

```http
Authorization: Bearer <clerk_jwt_token>
```

### Getting Authentication Token

**Frontend (React):**
```typescript
import { useAuth } from '@clerk/nextjs'

const { getToken } = useAuth()
const token = await getToken()
```

**Server Actions:**
```typescript
import { auth } from '@clerk/nextjs/server'

const { getToken } = auth()
const token = await getToken()
```

## 📋 API Endpoints Overview

### 🏠 Properties API

| Endpoint | Method | Description | Auth |
|----------|--------|-------------|------|
| `GET /api/sonobrokers/properties` | GET | Get all properties with filters | ❌ |
| `GET /api/sonobrokers/properties/{id}` | GET | Get property by ID | ❌ |
| `POST /api/sonobrokers/properties` | POST | Create new property | ✅ |
| `PUT /api/sonobrokers/properties/{id}` | PUT | Update property | ✅ |
| `DELETE /api/sonobrokers/properties/{id}` | DELETE | Delete property | ✅ |
| `GET /api/sonobrokers/properties/search` | GET | Advanced property search | ❌ |
| `GET /api/sonobrokers/properties/user/{userId}` | GET | Get user's properties | ✅ |

### 📸 Property Images API

| Endpoint | Method | Description | Auth |
|----------|--------|-------------|------|
| `GET /api/sonobrokers/properties/{id}/images` | GET | Get property images | ❌ |
| `POST /api/sonobrokers/properties/{id}/images` | POST | Upload property image | ✅ |
| `PUT /api/sonobrokers/property-images/{id}` | PUT | Update image details | ✅ |
| `DELETE /api/sonobrokers/property-images/{id}` | DELETE | Delete property image | ✅ |

### 👥 Users API

| Endpoint | Method | Description | Auth |
|----------|--------|-------------|------|
| `GET /api/sonobrokers/users` | GET | Get all users | ✅ |
| `GET /api/sonobrokers/users/{id}` | GET | Get user by ID | ✅ |
| `POST /api/sonobrokers/users` | POST | Create new user | ✅ |
| `PUT /api/sonobrokers/users/{id}` | PUT | Update user | ✅ |
| `DELETE /api/sonobrokers/users/{id}` | DELETE | Delete user | ✅ |
| `GET /api/sonobrokers/users/profile` | GET | Get current user profile | ✅ |
| `PUT /api/sonobrokers/users/profile` | PUT | Update current user profile | ✅ |

### 🤝 Contact Sharing API

| Endpoint | Method | Description | Auth |
|----------|--------|-------------|------|
| `POST /api/sonobrokers/contact-sharing` | POST | Create contact share | ✅ |
| `GET /api/sonobrokers/contact-sharing` | GET | Get user contact shares | ✅ |
| `GET /api/sonobrokers/contact-sharing/{id}` | GET | Get contact share by ID | ✅ |
| `PUT /api/sonobrokers/contact-sharing/{id}/respond` | PUT | Seller responds to contact | ✅ |
| `DELETE /api/sonobrokers/contact-sharing/{id}` | DELETE | Delete contact share | ✅ |
| `GET /api/sonobrokers/contact-sharing/stats` | GET | Get contact share statistics | ✅ |

### 📅 Property Scheduling API

| Endpoint | Method | Description | Auth |
|----------|--------|-------------|------|
| `POST /api/sonobrokers/property-scheduling/availability` | POST | Create seller availability | ✅ |
| `GET /api/sonobrokers/property-scheduling/availability` | GET | Get seller availability | ✅ |
| `PUT /api/sonobrokers/property-scheduling/availability/{id}` | PUT | Update availability | ✅ |
| `DELETE /api/sonobrokers/property-scheduling/availability/{id}` | DELETE | Delete availability | ✅ |
| `GET /api/sonobrokers/property-scheduling/property/{id}/availability` | GET | Get property availability | ❌ |
| `POST /api/sonobrokers/property-scheduling/visits` | POST | Create visit schedule | ✅ |
| `GET /api/sonobrokers/property-scheduling/visits/{id}` | GET | Get visit details | ✅ |
| `GET /api/sonobrokers/property-scheduling/visits` | GET | Get user visits | ✅ |
| `PUT /api/sonobrokers/property-scheduling/visits/{id}/respond` | PUT | Respond to visit request | ✅ |
| `PUT /api/sonobrokers/property-scheduling/visits/{id}/cancel` | PUT | Cancel visit | ✅ |
| `POST /api/sonobrokers/property-scheduling/property/{id}/qr-code` | POST | Generate QR code | ✅ |
| `GET /api/sonobrokers/property-scheduling/property/{id}/qr-code` | GET | Get QR code | ✅ |
| `POST /api/sonobrokers/property-scheduling/visits/{id}/verify` | POST | Verify visit | ✅ |

### 🔍 Search & Filters API

| Endpoint | Method | Description | Auth |
|----------|--------|-------------|------|
| `GET /api/sonobrokers/search/properties` | GET | Advanced property search | ❌ |
| `GET /api/sonobrokers/search/filters` | GET | Get available search filters | ❌ |
| `POST /api/sonobrokers/search/filters` | POST | Save search filter | ✅ |
| `GET /api/sonobrokers/search/filters/user` | GET | Get user's saved filters | ✅ |
| `DELETE /api/sonobrokers/search/filters/{id}` | DELETE | Delete saved filter | ✅ |

### 💬 Messaging API

| Endpoint | Method | Description | Auth |
|----------|--------|-------------|------|
| `GET /api/sonobrokers/messaging/conversations` | GET | Get user conversations | ✅ |
| `GET /api/sonobrokers/messaging/conversations/{id}` | GET | Get conversation details | ✅ |
| `POST /api/sonobrokers/messaging/conversations` | POST | Create new conversation | ✅ |
| `POST /api/sonobrokers/messaging/conversations/{id}/messages` | POST | Send message | ✅ |
| `PUT /api/sonobrokers/messaging/messages/{id}/read` | PUT | Mark message as read | ✅ |

### 🏢 Advertisers API

| Endpoint | Method | Description | Auth |
|----------|--------|-------------|------|
| `GET /api/sonobrokers/advertisers` | GET | Get all advertisers | ❌ |
| `GET /api/sonobrokers/advertisers/{id}` | GET | Get advertiser by ID | ❌ |
| `POST /api/sonobrokers/advertisers` | POST | Create advertiser profile | ✅ |
| `PUT /api/sonobrokers/advertisers/{id}` | PUT | Update advertiser profile | ✅ |
| `GET /api/sonobrokers/advertisers/my-profile` | GET | Get current advertiser profile | ✅ |
| `PUT /api/sonobrokers/advertisers/{id}/upgrade-premium` | PUT | Upgrade to premium | ✅ |

### 🔧 Admin API

| Endpoint | Method | Description | Auth |
|----------|--------|-------------|------|
| `GET /api/admin/dashboard` | GET | Get admin dashboard | ✅ (Admin) |
| `GET /api/admin/users` | GET | Get all users | ✅ (Admin) |
| `GET /api/admin/properties` | GET | Get all properties | ✅ (Admin) |
| `GET /api/admin/statistics` | GET | Get platform statistics | ✅ (Admin) |
| `PUT /api/admin/users/{id}/status` | PUT | Update user status | ✅ (Admin) |
| `PUT /api/admin/properties/{id}/status` | PUT | Update property status | ✅ (Admin) |

### 🤖 AI Services API

| Endpoint | Method | Description | Auth |
|----------|--------|-------------|------|
| `POST /api/sonobrokers/ai/property/description` | POST | Generate property description | ✅ |
| `POST /api/sonobrokers/ai/property/analyze` | POST | Analyze property features | ✅ |
| `POST /api/sonobrokers/ai/property/price-estimate` | POST | Get AI price estimate | ✅ |

### 🔐 Authentication API

| Endpoint | Method | Description | Auth |
|----------|--------|-------------|------|
| `GET /api/sonobrokers/auth/test` | GET | Test authentication | ✅ |
| `POST /api/sonobrokers/auth/webhook` | POST | Clerk webhook handler | ❌ |
| `GET /api/sonobrokers/auth/user-info` | GET | Get current user info | ✅ |
| `/admin/properties` | GET | Get all properties | ✅ (Admin) |
| `/admin/contact-sharing` | GET | Get all contact shares | ✅ (Admin) |
| `/admin/stats` | GET | Get system statistics | ✅ (Admin) |

### Core API

| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/health` | GET | Health check | ❌ |
| `/status` | GET | API status | ❌ |
| `/openapi/v1.json` | GET | OpenAPI specification | ❌ |

## React Component to API Mapping

### Contact Sharing Components

| React Component | Primary API Endpoint | Secondary Endpoints |
|-----------------|---------------------|-------------------|
| `ContactShareButton` | `POST /contact-sharing` | `GET /users/profile` |
| `ContactShareModal` | `POST /contact-sharing` | - |
| `ContactSharesDashboard` | `GET /contact-sharing` | `GET /contact-sharing/stats` |
| `ContactShareDetails` | `GET /contact-sharing/{id}` | `PUT /contact-sharing/{id}/respond` |
| `PropertyQuickActions` | `POST /contact-sharing` | - |

### Property Scheduling Components

| React Component | Primary API Endpoint | Secondary Endpoints |
|-----------------|---------------------|-------------------|
| `SellerAvailabilityManager` | `GET /property-scheduling/availability` | `POST`, `PUT`, `DELETE /availability` |
| `PropertyQrCodeManager` | `GET /property-scheduling/property/{id}/qr-code` | `POST /qr-code` |
| `VisitVerificationScanner` | `POST /property-scheduling/visits/{id}/verify` | - |
| `CalendarScheduling` | `POST /property-scheduling/visits` | `GET /property/{id}/availability` |

### Property Components

| React Component | Primary API Endpoint | Secondary Endpoints |
|-----------------|---------------------|-------------------|
| `PropertyCard` | - | `POST /contact-sharing` |
| `PropertyList` | `GET /properties` | `GET /properties/search` |
| `PropertyDetails` | `GET /properties/{id}` | `GET /contact-sharing/property/{id}/stats` |
| `PropertyForm` | `POST /properties` | `PUT /properties/{id}` |

### Dashboard Components

| React Component | Primary API Endpoint | Secondary Endpoints |
|-----------------|---------------------|-------------------|
| `DashboardOverview` | `GET /dashboard/overview` | Multiple stats endpoints |
| `PropertiesDashboard` | `GET /properties/user` | `GET /properties/{id}/analytics` |
| `ContactSharesDashboard` | `GET /contact-sharing` | `GET /contact-sharing/stats` |
| `SchedulingDashboard` | `GET /property-scheduling/visits` | `GET /property-scheduling/stats` |

## Server Actions to API Mapping

### Contact Sharing Actions

| Server Action | API Endpoint | HTTP Method |
|---------------|--------------|-------------|
| `shareContactAction` | `/contact-sharing` | POST |
| `submitOfferAction` | `/contact-sharing` | POST |
| `scheduleVisitAction` | `/contact-sharing` | POST |
| `submitOfferWithVisitAction` | `/contact-sharing` | POST |
| `sellerRespondAction` | `/contact-sharing/{id}/respond` | PUT |
| `deleteContactShareAction` | `/contact-sharing/{id}` | DELETE |

### Property Scheduling Actions

| Server Action | API Endpoint | HTTP Method |
|---------------|--------------|-------------|
| `createAvailabilityAction` | `/property-scheduling/availability` | POST |
| `updateAvailabilityAction` | `/property-scheduling/availability/{id}` | PUT |
| `deleteAvailabilityAction` | `/property-scheduling/availability/{id}` | DELETE |
| `createVisitScheduleAction` | `/property-scheduling/visits` | POST |
| `respondToVisitAction` | `/property-scheduling/visits/{id}/respond` | PUT |
| `cancelVisitAction` | `/property-scheduling/visits/{id}/cancel` | PUT |
| `generateQrCodeAction` | `/property-scheduling/property/{id}/qr-code` | POST |
| `verifyVisitAction` | `/property-scheduling/visits/{id}/verify` | POST |

### Property Actions

| Server Action | API Endpoint | HTTP Method |
|---------------|--------------|-------------|
| `createPropertyAction` | `/properties` | POST |
| `updatePropertyAction` | `/properties/{id}` | PUT |
| `deletePropertyAction` | `/properties/{id}` | DELETE |
| `searchPropertiesAction` | `/properties/search` | GET |

## API Client Configuration

### Base Configuration

```typescript
// src/lib/api/base-api.ts
export const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'https://localhost:5001/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
}
```

### Authentication Headers

```typescript
// src/lib/api/auth-headers.ts
export const getAuthHeaders = async () => {
  const token = await getClerkToken()
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
}
```

### API Client Functions

```typescript
// src/lib/api/contact-sharing-api.ts
export const contactSharingApi = {
  create: (data) => POST('/sonobrokers/contact-sharing', data),
  getAll: (params) => GET('/sonobrokers/contact-sharing', { params }),
  getById: (id) => GET(`/sonobrokers/contact-sharing/${id}`),
  respond: (id, data) => PUT(`/sonobrokers/contact-sharing/${id}/respond`, data),
  delete: (id) => DELETE(`/sonobrokers/contact-sharing/${id}`),
  getStats: () => GET('/sonobrokers/contact-sharing/stats')
}
```

## Error Handling

### Standard Error Response Format

```json
{
  "error": "Error message",
  "details": "Detailed error information or validation errors",
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/api/endpoint/path",
  "traceId": "trace-id-for-debugging"
}
```

### HTTP Status Codes

| Code | Description | Usage |
|------|-------------|-------|
| 200 | OK | Successful GET, PUT requests |
| 201 | Created | Successful POST requests |
| 204 | No Content | Successful DELETE requests |
| 400 | Bad Request | Validation errors, invalid data |
| 401 | Unauthorized | Authentication required |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Resource not found |
| 409 | Conflict | Resource conflict (duplicate, etc.) |
| 422 | Unprocessable Entity | Business logic validation errors |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server errors |

## Rate Limiting

### Rate Limit Headers

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 995
X-RateLimit-Reset: 1642248600
X-RateLimit-RetryAfter: 3600
```

### Rate Limits by Endpoint Category

| Category | Limit | Window |
|----------|-------|--------|
| General API | 1000 requests | 1 hour |
| Contact Sharing | 10 creates | 1 day |
| Property Offers | 5 per property | 1 day |
| Visit Scheduling | 10 requests | 1 day |
| QR Code Generation | 5 per property | 1 day |
| Email Sending | 100 emails | 1 day |

## Pagination

### Request Parameters

```http
GET /api/endpoint?page=1&limit=20&sortBy=createdAt&sortOrder=desc
```

### Response Format

```json
{
  "data": [...],
  "total": 150,
  "page": 1,
  "limit": 20,
  "totalPages": 8,
  "hasMore": true,
  "hasPrevious": false
}
```

## Filtering and Searching

### Common Query Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `search` | string | Text search | `?search=beautiful home` |
| `sortBy` | string | Sort field | `?sortBy=createdAt` |
| `sortOrder` | string | Sort direction | `?sortOrder=desc` |
| `page` | integer | Page number | `?page=2` |
| `limit` | integer | Items per page | `?limit=50` |
| `fromDate` | date | Date range start | `?fromDate=2024-01-01` |
| `toDate` | date | Date range end | `?toDate=2024-12-31` |

### Property-Specific Filters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `location` | string | Location search | `?location=Toronto` |
| `priceMin` | number | Minimum price | `?priceMin=400000` |
| `priceMax` | number | Maximum price | `?priceMax=600000` |
| `bedrooms` | integer | Number of bedrooms | `?bedrooms=3` |
| `bathrooms` | number | Number of bathrooms | `?bathrooms=2.5` |
| `propertyType` | string | Property type | `?propertyType=Detached House` |

## Webhooks

### Webhook Events

| Event | Description | Payload |
|-------|-------------|---------|
| `contact_share.created` | Contact share created | Contact share data |
| `contact_share.responded` | Seller responded | Response data |
| `visit.requested` | Visit requested | Visit data |
| `visit.confirmed` | Visit confirmed | Visit data |
| `visit.verified` | Visit verified | Verification data |
| `property.created` | Property created | Property data |

### Webhook Configuration

```json
{
  "url": "https://your-app.com/webhooks/sonobrokers",
  "events": ["contact_share.created", "visit.confirmed"],
  "secret": "webhook-secret-key"
}
```

## Testing

### Test Environment

- **Base URL**: `https://localhost:5001/api`
- **Test Database**: Isolated test database
- **Mock Services**: Email and external services mocked

### Test Data

Use the following test IDs for development:

```json
{
  "testUserId": "test-user-123",
  "testPropertyId": "test-property-123",
  "testSellerId": "test-seller-123",
  "testBuyerId": "test-buyer-123"
}
```

## Documentation Links

- [Contact Sharing API](./CONTACT-SHARING-API.md)
- [Property Scheduling API](./PROPERTY-SCHEDULING-API.md)
- [Authentication Guide](./authentication.md)
- [Deployment Guide](./deployment.md)
- [React Integration Guide](../../SoNoBrokers/docs/react-api-integration.md)
