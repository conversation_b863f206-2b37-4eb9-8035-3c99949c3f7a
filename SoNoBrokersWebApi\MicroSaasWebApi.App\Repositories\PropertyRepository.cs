using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.App.Repositories.Interfaces;
using System.Text;

namespace MicroSaasWebApi.App.Repositories
{
    /// <summary>
    /// Property repository implementation using Dapper
    /// </summary>
    public class PropertyRepository : IPropertyRepository
    {
        private readonly IDapperDbContext _dbContext;
        private readonly ILogger<PropertyRepository> _logger;

        public PropertyRepository(IDapperDbContext dbContext, ILogger<PropertyRepository> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<IEnumerable<Property>> GetAllAsync()
        {
            try
            {
                const string sql = @"
                    SELECT p.*, u.""fullName"" as seller_name, u.email as seller_email
                    FROM public.""Property"" p
                    LEFT JOIN public.""User"" u ON p.""sellerId"" = u.id
                    ORDER BY p.""createdAt"" DESC";

                return await _dbContext.QueryAsync<Property>(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all properties");
                throw;
            }
        }

        public async Task<Property?> GetByIdAsync(string id)
        {
            try
            {
                const string sql = @"
                    SELECT p.*, u.""fullName"" as seller_name, u.email as seller_email
                    FROM public.""Property"" p
                    LEFT JOIN public.""User"" u ON p.""sellerId"" = u.id
                    WHERE p.id = @Id";

                return await _dbContext.QueryFirstOrDefaultAsync<Property>(sql, new { Id = id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving property by ID: {Id}", id);
                throw;
            }
        }

        public async Task<string> CreateAsync(Property property)
        {
            try
            {
                property.Id = Guid.NewGuid().ToString();
                property.CreatedAt = DateTime.UtcNow;
                property.UpdatedAt = DateTime.UtcNow;

                const string sql = @"
                    INSERT INTO public.""Property"" (
                        id, ""sellerId"", title, description, price, ""propertyType"", ""listingType"",
                        status, bedrooms, bathrooms, ""squareFootage"", ""lotSize"", ""yearBuilt"",
                        address, ""imageUrls"", features, amenities, ""parkingSpaces"", ""garageSpaces"",
                        ""propertyTaxes"", ""listingDate"", ""viewCount"", ""favoriteCount"",
                        ""isActive"", ""isFeatured"", ""createdAt"", ""updatedAt""
                    ) VALUES (
                        @Id, @SellerId, @Title, @Description, @Price, @PropertyType, @ListingType,
                        @Status, @Bedrooms, @Bathrooms, @SquareFootage, @LotSize, @YearBuilt,
                        @Address, @ImageUrls, @Features, @Amenities, @ParkingSpaces, @GarageSpaces,
                        @PropertyTaxes, @ListingDate, @ViewCount, @FavoriteCount,
                        @IsActive, @IsFeatured, @CreatedAt, @UpdatedAt
                    )";

                await _dbContext.ExecuteAsync(sql, property);
                return property.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating property");
                throw;
            }
        }

        public async Task<bool> UpdateAsync(Property property)
        {
            try
            {
                property.UpdatedAt = DateTime.UtcNow;

                const string sql = @"
                    UPDATE public.""Property"" SET
                        title = @Title,
                        description = @Description,
                        price = @Price,
                        ""propertyType"" = @PropertyType,
                        ""listingType"" = @ListingType,
                        status = @Status,
                        bedrooms = @Bedrooms,
                        bathrooms = @Bathrooms,
                        ""squareFootage"" = @SquareFootage,
                        ""lotSize"" = @LotSize,
                        ""yearBuilt"" = @YearBuilt,
                        address = @Address,
                        ""imageUrls"" = @ImageUrls,
                        features = @Features,
                        amenities = @Amenities,
                        ""parkingSpaces"" = @ParkingSpaces,
                        ""garageSpaces"" = @GarageSpaces,
                        ""propertyTaxes"" = @PropertyTaxes,
                        ""listingDate"" = @ListingDate,
                        ""viewCount"" = @ViewCount,
                        ""favoriteCount"" = @FavoriteCount,
                        ""isActive"" = @IsActive,
                        ""isFeatured"" = @IsFeatured,
                        ""updatedAt"" = @UpdatedAt
                    WHERE id = @Id";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, property);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating property: {Id}", property.Id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(string id)
        {
            try
            {
                const string sql = @"DELETE FROM public.""Property"" WHERE id = @Id";
                var rowsAffected = await _dbContext.ExecuteAsync(sql, new { Id = id });
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting property: {Id}", id);
                throw;
            }
        }

        public async Task<IEnumerable<Property>> GetBySellerIdAsync(string sellerId)
        {
            try
            {
                const string sql = @"
                    SELECT p.*, u.""fullName"" as seller_name, u.email as seller_email
                    FROM public.""Property"" p
                    LEFT JOIN public.""User"" u ON p.""sellerId"" = u.id
                    WHERE p.""sellerId"" = @SellerId
                    ORDER BY p.""createdAt"" DESC";

                return await _dbContext.QueryAsync<Property>(sql, new { SellerId = sellerId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving properties by seller ID: {SellerId}", sellerId);
                throw;
            }
        }

        public async Task<IEnumerable<Property>> GetActivePropertiesAsync()
        {
            try
            {
                const string sql = @"
                    SELECT p.*, u.""fullName"" as seller_name, u.email as seller_email
                    FROM public.""Property"" p
                    LEFT JOIN public.""User"" u ON p.""sellerId"" = u.id
                    WHERE p.""isActive"" = true AND p.status = 'Active'
                    ORDER BY p.""createdAt"" DESC";

                return await _dbContext.QueryAsync<Property>(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active properties");
                throw;
            }
        }

        public async Task<bool> UpdateStatusAsync(string id, PropertyStatus status)
        {
            try
            {
                const string sql = @"
                    UPDATE public.""Property"" SET
                        status = @Status,
                        ""updatedAt"" = @UpdatedAt
                    WHERE id = @Id";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, new
                {
                    Id = id,
                    Status = status,
                    UpdatedAt = DateTime.UtcNow
                });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating property status: {Id}", id);
                throw;
            }
        }

        public async Task<int> GetCountByStatusAsync(PropertyStatus status)
        {
            try
            {
                const string sql = @"
                    SELECT COUNT(*) FROM public.""Property""
                    WHERE status = @Status";

                return await _dbContext.QuerySingleAsync<int>(sql, new { Status = status });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property count by status: {Status}", status);
                throw;
            }
        }

        public async Task<(IEnumerable<Property> Properties, int TotalCount)> SearchAsync(
            string? searchTerm = null,
            string? city = null,
            string? province = null,
            decimal? minPrice = null,
            decimal? maxPrice = null,
            int? bedrooms = null,
            int? bathrooms = null,
            PropertyStatus? status = null,
            int page = 1,
            int pageSize = 20)
        {
            try
            {
                var whereConditions = new List<string>();
                var parameters = new Dictionary<string, object>();

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    whereConditions.Add(@"(p.title ILIKE @SearchTerm OR p.description ILIKE @SearchTerm OR p.address ILIKE @SearchTerm)");
                    parameters.Add("SearchTerm", $"%{searchTerm}%");
                }

                if (!string.IsNullOrEmpty(city))
                {
                    whereConditions.Add(@"p.address ILIKE @City");
                    parameters.Add("City", $"%{city}%");
                }

                if (!string.IsNullOrEmpty(province))
                {
                    whereConditions.Add(@"p.address ILIKE @Province");
                    parameters.Add("Province", $"%{province}%");
                }

                if (minPrice.HasValue)
                {
                    whereConditions.Add("p.price >= @MinPrice");
                    parameters.Add("MinPrice", minPrice.Value);
                }

                if (maxPrice.HasValue)
                {
                    whereConditions.Add("p.price <= @MaxPrice");
                    parameters.Add("MaxPrice", maxPrice.Value);
                }

                if (bedrooms.HasValue)
                {
                    whereConditions.Add("p.bedrooms = @Bedrooms");
                    parameters.Add("Bedrooms", bedrooms.Value);
                }

                if (bathrooms.HasValue)
                {
                    whereConditions.Add("p.bathrooms = @Bathrooms");
                    parameters.Add("Bathrooms", bathrooms.Value);
                }

                if (status.HasValue)
                {
                    whereConditions.Add("p.status = @Status");
                    parameters.Add("Status", status.Value);
                }

                var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";
                var offset = (page - 1) * pageSize;

                parameters.Add("Offset", offset);
                parameters.Add("PageSize", pageSize);

                // Get total count
                var countSql = $@"
                    SELECT COUNT(*) FROM public.""Property"" p
                    {whereClause}";

                var totalCount = await _dbContext.QuerySingleAsync<int>(countSql, parameters);

                // Get paginated results
                var dataSql = $@"
                    SELECT p.*, u.""fullName"" as seller_name, u.email as seller_email
                    FROM public.""Property"" p
                    LEFT JOIN public.""User"" u ON p.""sellerId"" = u.id
                    {whereClause}
                    ORDER BY p.""createdAt"" DESC
                    LIMIT @PageSize OFFSET @Offset";

                var properties = await _dbContext.QueryAsync<Property>(dataSql, parameters);

                return (properties, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching properties");
                throw;
            }
        }
    }
}
