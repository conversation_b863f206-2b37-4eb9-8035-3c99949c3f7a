version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sonobrokers-frontend-prod
    env_file:
      - .env.docker.production
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    networks:
      - sonobrokers-prod-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  sonobrokers-prod-network:
    driver: bridge
