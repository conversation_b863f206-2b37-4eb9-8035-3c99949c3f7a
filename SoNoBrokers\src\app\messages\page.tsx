import { Suspense } from 'react'
import { Metada<PERSON> } from 'next'
import { redirect } from 'next/navigation'
import { getCurrentUserProfile } from '@/lib/api/auth-api'
import { getConversations, getMessageStats } from '@/lib/api/messaging-api'
import { ConversationList } from '@/components/messaging/ConversationList'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { MessageCircle, Home, Plus } from 'lucide-react'

/**
 * Messages Page - Server Component
 * Shows all conversations for the current user
 */

export const metadata: Metadata = {
  title: 'Messages | SoNoBrokers',
  description: 'View and manage your property conversations',
}

export default async function MessagesPage() {
  // Check authentication
  const userProfile = await getCurrentUserProfile()
  if (!userProfile) {
    redirect('/sign-in')
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Messages
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-300">
          Manage your conversations with buyers and sellers
        </p>
      </div>

      {/* Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Conversations List */}
        <div className="lg:col-span-1">
          <Suspense fallback={<ConversationsLoading />}>
            <ConversationsSection />
          </Suspense>
        </div>

        {/* Welcome/Instructions */}
        <div className="lg:col-span-2">
          <Suspense fallback={<StatsLoading />}>
            <WelcomeSection userType={userProfile.userType} />
          </Suspense>
        </div>
      </div>
    </div>
  )
}

/**
 * Conversations Section Server Component
 */
async function ConversationsSection() {
  try {
    const conversationsResponse = await getConversations({
      page: 1,
      limit: 50,
      sortBy: 'lastMessageAt',
      sortOrder: 'desc'
    })

    return (
      <ConversationList
        conversations={conversationsResponse.conversations}
        showSearch={true}
        showFilters={true}
      />
    )
  } catch (error) {
    console.error('Failed to load conversations:', error)
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <MessageCircle className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Unable to load conversations
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-center mb-4">
            There was an error loading your conversations. Please try again.
          </p>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </CardContent>
      </Card>
    )
  }
}

/**
 * Welcome Section Server Component
 */
async function WelcomeSection({ userType }: { userType?: string }) {
  try {
    const stats = await getMessageStats()

    return (
      <div className="space-y-6">
        {/* Stats Card */}
        <Card>
          <CardHeader>
            <CardTitle>Your Messaging Activity</CardTitle>
            <CardDescription>
              Overview of your conversations and messages
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {stats.totalConversations}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Conversations
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {stats.activeConversations}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Active
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {stats.totalMessages}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Messages
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                  {stats.unreadMessages}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Unread
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Welcome Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              {userType === 'Seller' ? 'Manage Your Property Inquiries' : 'Connect with Property Owners'}
            </CardTitle>
            <CardDescription>
              {userType === 'Seller' 
                ? 'Respond to buyer inquiries and manage your property conversations'
                : 'Start conversations with sellers about properties you\'re interested in'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {userType === 'Seller' ? (
              <div className="space-y-4">
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                    Tips for Sellers
                  </h4>
                  <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                    <li>• Respond to inquiries promptly to maintain buyer interest</li>
                    <li>• Provide detailed answers about your property features</li>
                    <li>• Be transparent about property condition and pricing</li>
                    <li>• Schedule viewings efficiently through the messaging system</li>
                  </ul>
                </div>
                
                <div className="flex gap-2">
                  <Button onClick={() => window.location.href = '/dashboard/properties'}>
                    <Home className="h-4 w-4 mr-2" />
                    Manage Properties
                  </Button>
                  <Button variant="outline" onClick={() => window.location.href = '/dashboard/properties/create'}>
                    <Plus className="h-4 w-4 mr-2" />
                    List New Property
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                  <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">
                    Tips for Buyers
                  </h4>
                  <ul className="text-sm text-green-800 dark:text-green-200 space-y-1">
                    <li>• Ask specific questions about properties you're interested in</li>
                    <li>• Request additional photos or virtual tours</li>
                    <li>• Inquire about neighborhood amenities and schools</li>
                    <li>• Be clear about your timeline and financing status</li>
                  </ul>
                </div>
                
                <div className="flex gap-2">
                  <Button onClick={() => window.location.href = '/properties'}>
                    <Home className="h-4 w-4 mr-2" />
                    Browse Properties
                  </Button>
                  <Button variant="outline" onClick={() => window.location.href = '/properties?search=true'}>
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Start Searching
                  </Button>
                </div>
              </div>
            )}

            {stats.totalConversations === 0 && (
              <div className="text-center py-8">
                <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  No conversations yet
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {userType === 'Seller' 
                    ? 'Once buyers start inquiring about your properties, conversations will appear here.'
                    : 'Start browsing properties and contact sellers to begin conversations.'
                  }
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    )
  } catch (error) {
    console.error('Failed to load message stats:', error)
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <MessageCircle className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Welcome to Messages
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-center">
            Connect with buyers and sellers through secure messaging
          </p>
        </CardContent>
      </Card>
    )
  }
}

/**
 * Loading Components
 */
function ConversationsLoading() {
  return (
    <Card>
      <CardHeader>
        <div className="animate-pulse space-y-2">
          <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-32"></div>
          <div className="h-10 bg-gray-300 dark:bg-gray-700 rounded"></div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="animate-pulse flex items-center space-x-3">
            <div className="h-10 w-10 bg-gray-300 dark:bg-gray-700 rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-3/4"></div>
              <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

function StatsLoading() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="animate-pulse space-y-2">
            <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-48"></div>
            <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-64"></div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="text-center animate-pulse">
                <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-12 mx-auto mb-2"></div>
                <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-16 mx-auto"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <div className="animate-pulse space-y-2">
            <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-56"></div>
            <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-80"></div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-24 bg-gray-300 dark:bg-gray-700 rounded"></div>
            <div className="flex gap-2">
              <div className="h-10 bg-gray-300 dark:bg-gray-700 rounded w-32"></div>
              <div className="h-10 bg-gray-300 dark:bg-gray-700 rounded w-32"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
