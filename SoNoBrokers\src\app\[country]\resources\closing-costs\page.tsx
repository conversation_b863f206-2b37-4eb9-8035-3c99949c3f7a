import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function ClosingCostsPage({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/resources/closing-costs')
  }

  const userType = resolvedSearchParams.userType || 'seller'

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8">
            Closing Cost Estimator
          </h1>

          <div className="bg-card rounded-lg p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-4">Estimate Your Closing Costs</h2>
            <p className="text-muted-foreground mb-6">
              Get a detailed breakdown of all closing costs for your property transaction in {country.toUpperCase()}.
              Our calculator includes all fees, taxes, and expenses you can expect.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <div className="bg-card rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Property Details</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Property Price</label>
                  <input
                    type="number"
                    placeholder="$0"
                    className="w-full p-3 border border-border rounded-lg"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Down Payment</label>
                  <input
                    type="number"
                    placeholder="$0"
                    className="w-full p-3 border border-border rounded-lg"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Loan Amount</label>
                  <input
                    type="number"
                    placeholder="$0"
                    className="w-full p-3 border border-border rounded-lg"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Property Type</label>
                  <select className="w-full p-3 border border-border rounded-lg">
                    <option>Single Family Home</option>
                    <option>Condominium</option>
                    <option>Townhouse</option>
                    <option>Multi-Family</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Location & Preferences</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Province/State</label>
                  <select className="w-full p-3 border border-border rounded-lg">
                    {country === 'ca' ? (
                      <>
                        <option>Ontario</option>
                        <option>British Columbia</option>
                        <option>Alberta</option>
                        <option>Quebec</option>
                      </>
                    ) : (
                      <>
                        <option>California</option>
                        <option>New York</option>
                        <option>Texas</option>
                        <option>Florida</option>
                      </>
                    )}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Transaction Type</label>
                  <select className="w-full p-3 border border-border rounded-lg">
                    <option>Purchase</option>
                    <option>Sale</option>
                    <option>Refinance</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">First-Time Buyer?</label>
                  <select className="w-full p-3 border border-border rounded-lg">
                    <option>No</option>
                    <option>Yes</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4">Estimated Closing Costs</h3>
            <div className="space-y-3">
              <div className="flex justify-between py-2 border-b border-border">
                <span>Legal Fees</span>
                <span className="font-semibold">$0</span>
              </div>
              <div className="flex justify-between py-2 border-b border-border">
                <span>Home Inspection</span>
                <span className="font-semibold">$0</span>
              </div>
              <div className="flex justify-between py-2 border-b border-border">
                <span>Property Transfer Tax</span>
                <span className="font-semibold">$0</span>
              </div>
              <div className="flex justify-between py-2 border-b border-border">
                <span>Title Insurance</span>
                <span className="font-semibold">$0</span>
              </div>
              <div className="flex justify-between py-2 border-b border-border">
                <span>Appraisal Fee</span>
                <span className="font-semibold">$0</span>
              </div>
              <div className="flex justify-between py-2 border-b border-border">
                <span>Survey Fee</span>
                <span className="font-semibold">$0</span>
              </div>
              <div className="flex justify-between py-3 text-lg font-bold bg-muted rounded-lg px-4">
                <span>Total Closing Costs</span>
                <span className="text-primary">$0</span>
              </div>
            </div>

            <button className="w-full mt-6 bg-primary text-primary-foreground py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors">
              Calculate Closing Costs
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'seller'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Closing Cost Estimator for ${country} | SoNoBrokers`,
    description: `Calculate all closing costs for your property transaction in ${country}. Free estimator with detailed breakdown of fees, taxes, and expenses.`,
    keywords: `closing costs, property fees, real estate costs, ${country}, closing calculator`,
  }
}
