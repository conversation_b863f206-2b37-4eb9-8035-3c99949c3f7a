using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MicroSaasWebApi.Services.Repository.Interface;
using MicroSaasWebApi.Services.Configuration;


namespace MicroSaasWebApi.Controllers.Core
{
    /// <summary>
    /// Configuration controller for managing application settings
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize] // Requires valid Clerk token (except for [AllowAnonymous] endpoints)
    public class ConfigurationController : ControllerBase
    {
        private readonly ILogger<ConfigurationController> _logger;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAppConfigurationService _configService;

        public ConfigurationController(
            ILogger<ConfigurationController> logger,
            IUnitOfWork unitOfWork,
            IAppConfigurationService configService)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            _configService = configService;
        }

        /// <summary>
        /// Get application information (public endpoint)
        /// </summary>
        [HttpGet("info")]
        [AllowAnonymous]
        
        public IActionResult GetApplicationInfo()
        {
            try
            {
                var appSettings = _configService.GetAppSettings();

                return Ok(new
                {
                    AppName = appSettings.AppName,
                    Version = appSettings.Version,
                    Status = "Running",
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving application info");
                return StatusCode(500, "Failed to retrieve application info");
            }
        }

        /// <summary>
        /// Get feature flags (requires authentication)
        /// </summary>
        [HttpGet("features")]
        [Authorize(Policy = "AdminOnly")]
        
        public IActionResult GetFeatureFlags()
        {
            try
            {
                // TODO: Implement feature flags retrieval
                var featureFlags = new Dictionary<string, bool>
                {
                    { "EnableAdvancedAuth", true },
                    { "EnableBlobStorage", true },
                    { "EnableSharePoint", false },
                    { "EnableStripePayments", true }
                };

                return Ok(featureFlags);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving feature flags");
                return StatusCode(500, "Failed to retrieve feature flags");
            }
        }

        /// <summary>
        /// Get environment configuration (admin only)
        /// </summary>
        [HttpGet("environment")]
        [Authorize(Policy = "AdminOnly")]
        
        public IActionResult GetEnvironmentConfig()
        {
            try
            {
                var environmentConfig = new
                {
                    Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development",
                    DatabaseProvider = "SqlServer",
                    CacheProvider = "Redis",
                    LogLevel = "Information"
                };

                return Ok(environmentConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving environment config");
                return StatusCode(500, "Failed to retrieve environment config");
            }
        }

        /// <summary>
        /// Validate configuration (admin only)
        /// </summary>
        [HttpGet("validate")]
        [Authorize(Policy = "AdminOnly")]
        
        public IActionResult ValidateConfiguration()
        {
            try
            {
                var missingSettings = new List<string>();

                // Check required configuration
                if (string.IsNullOrEmpty(Environment.GetEnvironmentVariable("ConnectionStrings__DefaultConnection")))
                    missingSettings.Add("DefaultConnection");

                if (string.IsNullOrEmpty(Environment.GetEnvironmentVariable("Clerk__SecretKey")))
                    missingSettings.Add("Clerk__SecretKey");

                var validationResult = new
                {
                    IsValid = !missingSettings.Any(),
                    MissingSettings = missingSettings,
                    ValidationTimestamp = DateTime.UtcNow
                };

                if (missingSettings.Any())
                {
                    _logger.LogWarning("Configuration validation failed. Missing settings: {MissingSettings}",
                        string.Join(", ", missingSettings));
                }

                return Ok(validationResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating configuration");
                return StatusCode(500, "Failed to validate configuration");
            }
        }

        /// <summary>
        /// Get health status with configuration details (admin only)
        /// </summary>
        [HttpGet("health")]
        [Authorize(Policy = "AdminOnly")]
        
        public async Task<IActionResult> GetConfigurationHealth()
        {
            try
            {
                // Test database connection
                bool databaseHealthy = false;
                try
                {
                    var userCount = await _unitOfWork.Users.CountAsync();
                    databaseHealthy = true;
                }
                catch (Exception dbEx)
                {
                    _logger.LogWarning(dbEx, "Database health check failed");
                }

                var healthStatus = new
                {
                    OverallHealth = databaseHealthy ? "Healthy" : "Unhealthy",
                    Database = new
                    {
                        Status = databaseHealthy ? "Healthy" : "Unhealthy",
                        ConnectionString = !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("ConnectionStrings__DefaultConnection")) ? "Configured" : "Missing"
                    },
                    Configuration = new
                    {
                        Status = "Healthy",
                        Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development"
                    },
                    CheckTimestamp = DateTime.UtcNow
                };

                return Ok(healthStatus);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking configuration health");
                return StatusCode(500, "Failed to check configuration health");
            }
        }

        /// <summary>
        /// Refresh configuration (admin only)
        /// </summary>
        [HttpPost("refresh")]
        [Authorize(Policy = "AdminOnly")]
        
        public IActionResult RefreshConfiguration()
        {
            try
            {
                // TODO: Implement configuration refresh logic
                _logger.LogInformation("Configuration refresh requested by user");

                return Ok("Configuration refreshed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing configuration");
                return StatusCode(500, "Failed to refresh configuration");
            }
        }
    }
}
