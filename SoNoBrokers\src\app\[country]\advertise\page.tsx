import { AdvertisePage } from '@/components/shared/advertise/AdvertisePage'
import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function AdvertisePageRoute({ params, searchParams }: PageProps) {
  // Note: Authentication is handled by middleware for protected routes
  // This page is accessible to all users, but requires sign-in for form submission
  const isSignedIn = true // Will be handled by the client component

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us', 'uae']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/advertise')
  }

  // Default to seller if no userType specified
  const userType = resolvedSearchParams.userType || 'seller'

  return (
    <AdvertisePage
      userType={userType}
      isSignedIn={isSignedIn}
      country={country.toUpperCase()}
    />
  )
}

export async function generateMetadata({ params }: PageProps) {
  const resolvedParams = await params
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Advertise Your Services on SoNoBrokers ${country} | Grow Your Business`,
    description: `Advertise your real estate services on SoNoBrokers ${country}. Reach thousands of buyers and sellers looking for professional services. Location-based advertising with proven results.`,
    keywords: `advertise services, real estate marketing, business advertising, ${country}, service providers, lead generation`,
  }
}
