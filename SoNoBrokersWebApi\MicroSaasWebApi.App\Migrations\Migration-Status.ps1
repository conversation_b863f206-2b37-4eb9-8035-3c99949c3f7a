# PowerShell script to check SoNoBrokers database migration status
# This script shows which migrations have been applied and which are pending

param(
    [string]$ConnectionString = "Host=db.yfznlsisxsnymkvydzha.supabase.co;Database=postgres;Username=postgres;Password=Shared4w0rk!;SSL Mode=Require;"
)

Write-Host "📊 SoNoBrokers Migration Status Checker" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Get the script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# Get all numbered migration files
$MigrationFiles = Get-ChildItem -Path $ScriptDir -Filter "*.sql" | 
    Where-Object { $_.Name -match "^\d{4}_.*\.sql$" } | 
    Sort-Object Name

if ($MigrationFiles.Count -eq 0) {
    Write-Host "❌ No migration files found matching pattern 0001_*.sql" -ForegroundColor Red
    exit 1
}

Write-Host "📋 Found $($MigrationFiles.Count) migration files" -ForegroundColor Cyan
Write-Host ""

# Check if psql is available
try {
    $null = Get-Command psql -ErrorAction Stop
} catch {
    Write-Host "❌ psql command not found. Please install PostgreSQL client tools." -ForegroundColor Red
    exit 1
}

try {
    # Parse connection string
    $Host = ($ConnectionString -split "Host=")[1] -split ";")[0]
    $Database = ($ConnectionString -split "Database=")[1] -split ";")[0]
    $Username = ($ConnectionString -split "Username=")[1] -split ";")[0]
    $Password = ($ConnectionString -split "Password=")[1] -split ";")[0]
    
    $env:PGPASSWORD = $Password
    
    Write-Host "🔍 Checking database connection..." -ForegroundColor Gray
    
    # Test connection
    $testQuery = "SELECT 1;"
    $testResult = & psql -h $Host -d $Database -U $Username -t -c $testQuery 2>&1
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to connect to database" -ForegroundColor Red
        Write-Host $testResult -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ Database connection successful" -ForegroundColor Green
    Write-Host ""
    
    # Check if schema_migrations table exists
    $tableExistsQuery = @"
SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'schema_migrations'
);
"@
    
    $tableExists = & psql -h $Host -d $Database -U $Username -t -c $tableExistsQuery
    $tableExists = $tableExists.Trim()
    
    if ($tableExists -eq "f") {
        Write-Host "⚠️  schema_migrations table does not exist" -ForegroundColor Yellow
        Write-Host "   This means no migrations have been run yet" -ForegroundColor Yellow
        Write-Host ""
        
        Write-Host "📋 Migration Status:" -ForegroundColor Cyan
        foreach ($file in $MigrationFiles) {
            $migrationName = [System.IO.Path]::GetFileNameWithoutExtension($file.Name)
            Write-Host "   ⏳ $migrationName - PENDING" -ForegroundColor Yellow
        }
        
        Write-Host ""
        Write-Host "💡 To run migrations, use: .\Run-Migrations.ps1" -ForegroundColor Cyan
        exit 0
    }
    
    # Get executed migrations
    $executedQuery = @"
SELECT migration_name, executed_at 
FROM public.schema_migrations 
ORDER BY migration_name;
"@
    
    $executedMigrations = & psql -h $Host -d $Database -U $Username -t -c $executedQuery | 
        Where-Object { $_.Trim() -ne "" } |
        ForEach-Object { 
            $parts = $_.Split("|")
            @{
                Name = $parts[0].Trim()
                ExecutedAt = $parts[1].Trim()
            }
        }
    
    Write-Host "📋 Migration Status:" -ForegroundColor Cyan
    Write-Host ""
    
    $pendingCount = 0
    $executedCount = 0
    
    foreach ($file in $MigrationFiles) {
        $migrationName = [System.IO.Path]::GetFileNameWithoutExtension($file.Name)
        $executed = $executedMigrations | Where-Object { $_.Name -eq $migrationName }
        
        if ($executed) {
            Write-Host "   ✅ $migrationName - EXECUTED ($($executed.ExecutedAt))" -ForegroundColor Green
            $executedCount++
        } else {
            Write-Host "   ⏳ $migrationName - PENDING" -ForegroundColor Yellow
            $pendingCount++
        }
    }
    
    Write-Host ""
    Write-Host "📊 Summary:" -ForegroundColor Cyan
    Write-Host "   Total migrations: $($MigrationFiles.Count)" -ForegroundColor Gray
    Write-Host "   Executed: $executedCount" -ForegroundColor Green
    Write-Host "   Pending: $pendingCount" -ForegroundColor Yellow
    Write-Host "   Database: $Host/$Database" -ForegroundColor Gray
    
    if ($pendingCount -gt 0) {
        Write-Host ""
        Write-Host "💡 To run pending migrations, use: .\Run-Migrations.ps1" -ForegroundColor Cyan
    } else {
        Write-Host ""
        Write-Host "🎉 Database is up to date!" -ForegroundColor Green
    }
    
    # Show any extra migrations in database that don't have files
    $allExecuted = $executedMigrations | ForEach-Object { $_.Name }
    $allFiles = $MigrationFiles | ForEach-Object { [System.IO.Path]::GetFileNameWithoutExtension($_.Name) }
    $extraMigrations = $allExecuted | Where-Object { $_ -notin $allFiles }
    
    if ($extraMigrations.Count -gt 0) {
        Write-Host ""
        Write-Host "⚠️  Warning: Found executed migrations without corresponding files:" -ForegroundColor Yellow
        foreach ($extra in $extraMigrations) {
            Write-Host "   - $extra" -ForegroundColor Yellow
        }
    }

} catch {
    Write-Host ""
    Write-Host "❌ Error checking migration status:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    exit 1
} finally {
    # Clear password from environment
    $env:PGPASSWORD = $null
}

Write-Host ""
Write-Host "✅ Migration status check completed" -ForegroundColor Green
