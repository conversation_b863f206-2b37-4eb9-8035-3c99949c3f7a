-- Migration: 0004_create_contact_scheduling_tables.sql
-- Description: Create contact sharing and property scheduling tables
-- Date: 2024-12-25
-- Author: Migration System
-- Dependencies: 0003_create_subscription_tables.sql

-- =====================================================
-- CONTACT SHARING TABLES
-- =====================================================

-- Contact Share table - Main table for buyer-seller contact sharing
CREATE TABLE IF NOT EXISTS public."ContactShare" (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
  "propertyId" UUID NOT NULL,
  "buyerId" UUID NOT NULL,
  "sellerId" UUID NOT NULL,
  "buyerName" TEXT NOT NULL,
  "buyerEmail" TEXT NOT NULL,
  "buyerPhone" TEXT,
  message TEXT,
  "shareType" "ContactShareType" NOT NULL DEFAULT 'ContactRequest',
  "offerAmount" DECIMAL(12,2),
  "schedulingPreference" TEXT,
  "preferredVisitDate" DATE,
  "preferredVisitTime" TIME,
  status "ContactShareStatus" NOT NULL DEFAULT 'Pending',
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "respondedAt" TIMESTAMP WITH TIME ZONE,
  "sellerResponse" TEXT,
  "emailSent" BOOLEAN DEFAULT FALSE,
  "emailSentAt" TIMESTAMP WITH TIME ZONE,
  "emailId" TEXT,
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Foreign key constraints
  CONSTRAINT "ContactShare_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE,
  CONSTRAINT "ContactShare_buyerId_fkey" FOREIGN KEY ("buyerId") REFERENCES public."User"(id) ON DELETE CASCADE,
  CONSTRAINT "ContactShare_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- =====================================================
-- PROPERTY SCHEDULING TABLES
-- =====================================================

-- Seller Availability table - Seller's weekly availability schedule
CREATE TABLE IF NOT EXISTS public."SellerAvailability" (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
  "propertyId" UUID NOT NULL,
  "sellerId" UUID NOT NULL,
  "dayOfWeek" INTEGER NOT NULL CHECK ("dayOfWeek" >= 0 AND "dayOfWeek" <= 6), -- 0=Sunday, 6=Saturday
  "startTime" TIME NOT NULL,
  "endTime" TIME NOT NULL,
  "isAvailable" BOOLEAN DEFAULT TRUE,
  notes TEXT,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Foreign key constraints
  CONSTRAINT "SellerAvailability_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE,
  CONSTRAINT "SellerAvailability_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES public."User"(id) ON DELETE CASCADE,

  -- Unique constraint to prevent overlapping availability for same day
  CONSTRAINT "SellerAvailability_unique_day_time" UNIQUE ("propertyId", "sellerId", "dayOfWeek", "startTime", "endTime")
);

-- Property Visit Schedule table - Visit requests and confirmations
CREATE TABLE IF NOT EXISTS public."PropertyVisitSchedule" (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
  "propertyId" UUID NOT NULL,
  "buyerId" UUID NOT NULL,
  "sellerId" UUID NOT NULL,
  "contactShareId" TEXT NOT NULL,
  "requestedDate" DATE NOT NULL,
  "requestedTime" TIME NOT NULL,
  "confirmedDate" DATE,
  "confirmedTime" TIME,
  status "VisitStatus" NOT NULL DEFAULT 'Pending',
  "visitType" "VisitType" NOT NULL DEFAULT 'InPerson',
  "buyerNotes" TEXT,
  "sellerNotes" TEXT,
  "sellerResponse" TEXT,
  "qrCode" TEXT,
  "qrCodeGenerated" BOOLEAN DEFAULT FALSE,
  "qrCodeGeneratedAt" TIMESTAMP WITH TIME ZONE,
  "visitVerified" BOOLEAN DEFAULT FALSE,
  "visitVerifiedAt" TIMESTAMP WITH TIME ZONE,
  "verificationMethod" TEXT,
  "calendarInviteSent" BOOLEAN DEFAULT FALSE,
  "calendarInviteSentAt" TIMESTAMP WITH TIME ZONE,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "respondedAt" TIMESTAMP WITH TIME ZONE,

  -- Foreign key constraints
  CONSTRAINT "PropertyVisitSchedule_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE,
  CONSTRAINT "PropertyVisitSchedule_buyerId_fkey" FOREIGN KEY ("buyerId") REFERENCES public."User"(id) ON DELETE CASCADE,
  CONSTRAINT "PropertyVisitSchedule_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES public."User"(id) ON DELETE CASCADE,
  CONSTRAINT "PropertyVisitSchedule_contactShareId_fkey" FOREIGN KEY ("contactShareId") REFERENCES public."ContactShare"(id) ON DELETE CASCADE
);

-- Property QR Code table - QR codes for property visit verification
CREATE TABLE IF NOT EXISTS public."PropertyQrCode" (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
  "propertyId" UUID NOT NULL,
  "sellerId" UUID NOT NULL,
  "qrCodeData" TEXT NOT NULL,
  "qrCodeImage" TEXT NOT NULL,
  "isActive" BOOLEAN DEFAULT TRUE,
  "expiresAt" TIMESTAMP WITH TIME ZONE,
  "scanCount" INTEGER DEFAULT 0,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "lastScannedAt" TIMESTAMP WITH TIME ZONE,

  -- Foreign key constraints
  CONSTRAINT "PropertyQrCode_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE,
  CONSTRAINT "PropertyQrCode_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES public."User"(id) ON DELETE CASCADE,

  -- Unique constraint - one active QR code per property
  CONSTRAINT "PropertyQrCode_unique_active_property" UNIQUE ("propertyId", "isActive") DEFERRABLE INITIALLY DEFERRED
);

-- Visit Verification table - Log of visit verifications for security
CREATE TABLE IF NOT EXISTS public."VisitVerification" (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
  "visitScheduleId" TEXT NOT NULL,
  "propertyId" UUID NOT NULL,
  "buyerId" UUID NOT NULL,
  "sellerId" UUID,
  method "VerificationMethod" NOT NULL DEFAULT 'QrCode',
  "qrCodeScanned" TEXT,
  "deviceInfo" TEXT,
  "ipAddress" TEXT,
  location TEXT,
  "isValid" BOOLEAN DEFAULT TRUE,
  notes TEXT,
  "verifiedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Foreign key constraints
  CONSTRAINT "VisitVerification_visitScheduleId_fkey" FOREIGN KEY ("visitScheduleId") REFERENCES public."PropertyVisitSchedule"(id) ON DELETE CASCADE,
  CONSTRAINT "VisitVerification_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE,
  CONSTRAINT "VisitVerification_buyerId_fkey" FOREIGN KEY ("buyerId") REFERENCES public."User"(id) ON DELETE CASCADE,
  CONSTRAINT "VisitVerification_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES public."User"(id) ON DELETE SET NULL
);

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ Migration 0004_create_contact_scheduling_tables.sql completed successfully';
    RAISE NOTICE 'Created tables:';
    RAISE NOTICE '- ContactShare (buyer-seller contact sharing)';
    RAISE NOTICE '- SellerAvailability (seller availability schedule)';
    RAISE NOTICE '- PropertyVisitSchedule (visit requests and confirmations)';
    RAISE NOTICE '- PropertyQrCode (QR codes for visit verification)';
    RAISE NOTICE '- VisitVerification (visit verification logs)';
    RAISE NOTICE '';
END $$;
