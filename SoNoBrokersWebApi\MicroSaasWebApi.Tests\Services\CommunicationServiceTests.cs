using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MicroSaasWebApi.Data;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers;
using MicroSaasWebApi.Tests.Common;
using Moq;
using Moq.Protected;
using System.Net;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Services
{
    public class CommunicationServiceTests : TestBase
    {
        private readonly Mock<DapperDbContext> _mockDbContext;
        private readonly Mock<ILogger<CommunicationService>> _mockLogger;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private readonly HttpClient _httpClient;
        private readonly CommunicationService _communicationService;

        public CommunicationServiceTests(ITestOutputHelper output) : base(output)
        {
            _mockDbContext = new Mock<DapperDbContext>();
            _mockLogger = new Mock<ILogger<CommunicationService>>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

            _communicationService = new CommunicationService(
                _mockDbContext.Object,
                _mockLogger.Object,
                _mockConfiguration.Object,
                _httpClient);
        }

        [Fact]
        public async Task SendConciergeInquiryAsync_WithValidRequest_ReturnsSuccessResponse()
        {
            // Arrange
            var request = TestDataBuilders.Communication.ContactConciergeRequest.Generate();

            // Act
            var result = await _communicationService.SendConciergeInquiryAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Message.Should().Be("Inquiry sent successfully");
        }

        [Fact]
        public async Task AddToWaitingListAsync_WithValidEmail_ReturnsSuccessResponse()
        {
            // Arrange
            var request = TestDataBuilders.Communication.WaitingListRequest.Generate();

            // Act
            var result = await _communicationService.AddToWaitingListAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Message.Should().Be("Successfully added to waiting list");
            result.ContactId.Should().NotBeNullOrEmpty();
            result.ContactId.ShouldBeValidGuid();
        }

        [Fact]
        public async Task GetLocationByIpAsync_WithValidIp_ReturnsLocationData()
        {
            // Arrange
            var request = new GeoLocationRequest { Ip = "*******" };

            var mockResponse = new
            {
                country_code = "US",
                country_name = "United States",
                city = "Mountain View",
                region = "California",
                timezone = "America/Los_Angeles"
            };

            var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(JsonSerializer.Serialize(mockResponse))
            };

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(httpResponse);

            // Act
            var result = await _communicationService.GetLocationByIpAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Country.Should().Be("US");
            result.CountryName.Should().Be("United States");
            result.City.Should().Be("Mountain View");
            result.Region.Should().Be("California");
            result.Timezone.Should().Be("America/Los_Angeles");
        }

        [Fact]
        public async Task GetLocationByIpAsync_WithNoIp_UsesDefaultAndReturnsData()
        {
            // Arrange
            var request = new GeoLocationRequest { Ip = null };

            _mockConfiguration.Setup(x => x["TEST_GEO_IP"]).Returns("*********");

            var mockResponse = new
            {
                country_code = "CA",
                country_name = "Canada",
                city = "Toronto",
                region = "Ontario",
                timezone = "America/Toronto"
            };

            var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(JsonSerializer.Serialize(mockResponse))
            };

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(httpResponse);

            // Act
            var result = await _communicationService.GetLocationByIpAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Country.Should().Be("CA");
            result.CountryName.Should().Be("Canada");
        }

        [Fact]
        public async Task GetLocationByIpAsync_WithHttpError_ReturnsDefaultResponse()
        {
            // Arrange
            var request = new GeoLocationRequest { Ip = "invalid-ip" };

            var httpResponse = new HttpResponseMessage(HttpStatusCode.BadRequest);

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(httpResponse);

            // Act
            var result = await _communicationService.GetLocationByIpAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Country.Should().Be("US");
            result.CountryName.Should().Be("United States");
            result.City.Should().Be("Unknown");
            result.Region.Should().Be("Unknown");
            result.Timezone.Should().Be("UTC");
        }

        [Fact]
        public async Task GetEnumValuesAsync_ReturnsAllEnumValues()
        {
            // Arrange
            var countries = new[] { "CA", "US", "UAE" };
            var serviceTypes = new[] { "photographer", "lawyer", "inspector" };
            var userTypes = new[] { "Buyer", "Seller" };

            _mockDbContext.SetupSequence(x => x.QueryAsync<string>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(countries)
                .ReturnsAsync(serviceTypes)
                .ReturnsAsync(userTypes);

            // Act
            var result = await _communicationService.GetEnumValuesAsync();

            // Assert
            result.Should().NotBeNull();
            result.Enums.Should().NotBeEmpty();
            result.Enums.Should().ContainKey("countries");
            result.Enums.Should().ContainKey("serviceTypes");
            result.Enums.Should().ContainKey("userTypes");
            result.Enums.Should().ContainKey("propertyStatuses");
            result.Enums.Should().ContainKey("advertiserPlans");
            result.Enums.Should().ContainKey("advertiserStatuses");

            // Verify countries
            var countryInfos = result.Enums["countries"] as List<CountryInfo>;
            countryInfos.Should().NotBeNull();
            countryInfos.Should().HaveCount(3);
            countryInfos.Should().Contain(c => c.Code == "CA" && c.Name == "Canada" && c.Flag == "🇨🇦");
            countryInfos.Should().Contain(c => c.Code == "US" && c.Name == "United States" && c.Flag == "🇺🇸");
            countryInfos.Should().Contain(c => c.Code == "UAE" && c.Name == "United Arab Emirates" && c.Flag == "🇦🇪");

            // Verify service types
            var serviceTypeInfos = result.Enums["serviceTypes"] as List<ServiceTypeInfo>;
            serviceTypeInfos.Should().NotBeNull();
            serviceTypeInfos.Should().HaveCount(3);
            serviceTypeInfos.Should().Contain(s => s.Value == "photographer" && s.Label == "Property Photographer");
            serviceTypeInfos.Should().Contain(s => s.Value == "lawyer" && s.Label == "Real Estate Lawyer");
        }

        [Theory]
        [InlineData("<EMAIL>", "Test User", "CA")]
        [InlineData("<EMAIL>", "Another User", "US")]
        public async Task SendConciergeInquiryAsync_WithDifferentRequests_LogsCorrectly(string email, string name, string country)
        {
            // Arrange
            var request = new ContactConciergeRequest
            {
                Email = email,
                Name = name,
                Country = country,
                PropertyAddress = "123 Test St"
            };

            // Act
            var result = await _communicationService.SendConciergeInquiryAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();

            // Verify logging was called
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Processing concierge inquiry from: {email}")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task AddToWaitingListAsync_WithValidEmail_LogsCorrectly()
        {
            // Arrange
            var request = new WaitingListRequest { Email = "<EMAIL>" };

            // Act
            var result = await _communicationService.AddToWaitingListAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();

            // Verify logging was called
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Adding email to waiting list: <EMAIL>")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetEnumValuesAsync_WithDatabaseError_ThrowsException()
        {
            // Arrange
            _mockDbContext.Setup(x => x.QueryAsync<string>(It.IsAny<string>(), It.IsAny<object>()))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _communicationService.GetEnumValuesAsync());
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _httpClient?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
