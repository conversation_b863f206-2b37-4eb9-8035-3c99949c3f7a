# =============================================================================
# SoNoBrokers React Application - Independent Docker Setup
# This runs ONLY the React frontend application
# =============================================================================

# =============================================================================
# Networks
# =============================================================================
networks:
  sonobrokers-frontend:
    driver: bridge

# =============================================================================
# Volumes
# =============================================================================
volumes:
  frontend-node-modules:
    driver: local
  frontend-next-cache:
    driver: local

# =============================================================================
# Services
# =============================================================================
services:
  # ===========================================================================
  # React Frontend Application
  # ===========================================================================
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
      args:
        BUILD_DATE: ${BUILD_DATE:-}
        VCS_REF: ${VCS_REF:-}
        VERSION: ${VERSION:-1.0.0}
    container_name: sonobrokers-frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-http://host.docker.internal:7163}
      - NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL:-https://localhost:3000}
      - NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=${NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
      - CLERK_SECRET_KEY=${CLERK_SECRET_KEY}
      - DATABASE_URL=${DATABASE_URL}
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - RESEND_API_KEY=${RESEND_API_KEY}
      - NEXT_PUBLIC_GOOGLE_PLACES_API_KEY=${NEXT_PUBLIC_GOOGLE_PLACES_API_KEY}
      - NEXT_PUBLIC_MAPBOX_API_KEY=${NEXT_PUBLIC_MAPBOX_API_KEY}
      # Feature Flags
      - NEXT_PUBLIC_ENABLE_GOOGLE_PROVIDERS=${NEXT_PUBLIC_ENABLE_GOOGLE_PROVIDERS:-true}
      - NEXT_PUBLIC_LAUNCH_MODE=${NEXT_PUBLIC_LAUNCH_MODE:-false}
      - NEXT_PUBLIC_ENABLE_LANGUAGE_SELECTOR=${NEXT_PUBLIC_ENABLE_LANGUAGE_SELECTOR:-false}
      - NEXT_PUBLIC_ENABLE_REGION_TESTER=${NEXT_PUBLIC_ENABLE_REGION_TESTER:-true}
      - NEXT_PUBLIC_DEV_MODE=${NEXT_PUBLIC_DEV_MODE:-false}
      - NEXT_PUBLIC_SUPPORTED_COUNTRIES=${NEXT_PUBLIC_SUPPORTED_COUNTRIES:-CA,US,UAE}
      - NEXT_PUBLIC_DEFAULT_COUNTRY=${NEXT_PUBLIC_DEFAULT_COUNTRY:-CA}
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    networks:
      - sonobrokers-frontend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    labels:
      - "com.sonobrokers.service=frontend"
      - "com.sonobrokers.version=${VERSION:-1.0.0}"

# =============================================================================
# Development Override Available
# Use: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
# =============================================================================