'use client'

import { useState } from 'react'
import { useUser } from '@clerk/nextjs'
import { Button } from '@/components/ui/button'
import { Share2, Mail, Phone, Calendar, DollarSign } from 'lucide-react'
import { ContactShareModal } from './ContactShareModal'

interface ContactShareButtonProps {
  propertyId: string
  sellerId: string
  sellerName: string
  propertyTitle: string
  propertyPrice: number
  propertyAddress: string
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  className?: string
  showText?: boolean
}

export function ContactShareButton({
  propertyId,
  sellerId,
  sellerName,
  propertyTitle,
  propertyPrice,
  propertyAddress,
  variant = 'outline',
  size = 'sm',
  className = '',
  showText = true
}: ContactShareButtonProps) {
  const { user, isSignedIn } = useUser()
  const [isModalOpen, setIsModalOpen] = useState(false)

  const handleClick = () => {
    if (!isSignedIn) {
      // Redirect to sign in
      window.location.href = '/sign-in'
      return
    }

    // Open contact share modal
    setIsModalOpen(true)
  }

  return (
    <>
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={handleClick}
      >
        <Share2 className="h-4 w-4 mr-2" />
        {showText && 'Share Contact'}
      </Button>

      {isModalOpen && (
        <ContactShareModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          propertyId={propertyId}
          sellerId={sellerId}
          sellerName={sellerName}
          propertyTitle={propertyTitle}
          propertyPrice={propertyPrice}
          propertyAddress={propertyAddress}
          defaultBuyerName={user?.fullName || ''}
          defaultBuyerEmail={user?.primaryEmailAddress?.emailAddress || ''}
        />
      )}
    </>
  )
}

/**
 * Quick action buttons for property cards
 */
export function PropertyQuickActions({
  propertyId,
  sellerId,
  sellerName,
  propertyTitle,
  propertyPrice,
  propertyAddress,
  className = ''
}: Omit<ContactShareButtonProps, 'variant' | 'size' | 'showText'>) {
  const { user, isSignedIn } = useUser()
  const [activeModal, setActiveModal] = useState<'contact' | 'offer' | 'visit' | null>(null)

  const handleAction = (action: 'contact' | 'offer' | 'visit') => {
    if (!isSignedIn) {
      window.location.href = '/sign-in'
      return
    }
    setActiveModal(action)
  }

  return (
    <div className={`flex gap-2 ${className}`}>
      {/* Contact Seller */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => handleAction('contact')}
        title="Share your contact information with seller"
      >
        <Mail className="h-4 w-4" />
      </Button>

      {/* Make Offer */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => handleAction('offer')}
        title="Submit an offer for this property"
      >
        <DollarSign className="h-4 w-4" />
      </Button>

      {/* Schedule Visit */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => handleAction('visit')}
        title="Request to schedule a property visit"
      >
        <Calendar className="h-4 w-4" />
      </Button>

      {/* Contact Share Modal */}
      {activeModal === 'contact' && (
        <ContactShareModal
          isOpen={true}
          onClose={() => setActiveModal(null)}
          propertyId={propertyId}
          sellerId={sellerId}
          sellerName={sellerName}
          propertyTitle={propertyTitle}
          propertyPrice={propertyPrice}
          propertyAddress={propertyAddress}
          defaultBuyerName={user?.fullName || ''}
          defaultBuyerEmail={user?.primaryEmailAddress?.emailAddress || ''}
          defaultShareType="contact"
        />
      )}

      {/* Offer Modal */}
      {activeModal === 'offer' && (
        <ContactShareModal
          isOpen={true}
          onClose={() => setActiveModal(null)}
          propertyId={propertyId}
          sellerId={sellerId}
          sellerName={sellerName}
          propertyTitle={propertyTitle}
          propertyPrice={propertyPrice}
          propertyAddress={propertyAddress}
          defaultBuyerName={user?.fullName || ''}
          defaultBuyerEmail={user?.primaryEmailAddress?.emailAddress || ''}
          defaultShareType="offer"
        />
      )}

      {/* Visit Modal */}
      {activeModal === 'visit' && (
        <ContactShareModal
          isOpen={true}
          onClose={() => setActiveModal(null)}
          propertyId={propertyId}
          sellerId={sellerId}
          sellerName={sellerName}
          propertyTitle={propertyTitle}
          propertyPrice={propertyPrice}
          propertyAddress={propertyAddress}
          defaultBuyerName={user?.fullName || ''}
          defaultBuyerEmail={user?.primaryEmailAddress?.emailAddress || ''}
          defaultShareType="visit"
        />
      )}
    </div>
  )
}

/**
 * Compact contact share button for property cards
 */
export function CompactContactShareButton({
  propertyId,
  sellerId,
  sellerName,
  propertyTitle,
  propertyPrice,
  propertyAddress,
  className = ''
}: Omit<ContactShareButtonProps, 'variant' | 'size' | 'showText'>) {
  return (
    <ContactShareButton
      propertyId={propertyId}
      sellerId={sellerId}
      sellerName={sellerName}
      propertyTitle={propertyTitle}
      propertyPrice={propertyPrice}
      propertyAddress={propertyAddress}
      variant="ghost"
      size="sm"
      className={className}
      showText={false}
    />
  )
}

/**
 * Full contact share button with text
 */
export function FullContactShareButton({
  propertyId,
  sellerId,
  sellerName,
  propertyTitle,
  propertyPrice,
  propertyAddress,
  className = ''
}: Omit<ContactShareButtonProps, 'variant' | 'size' | 'showText'>) {
  return (
    <ContactShareButton
      propertyId={propertyId}
      sellerId={sellerId}
      sellerName={sellerName}
      propertyTitle={propertyTitle}
      propertyPrice={propertyPrice}
      propertyAddress={propertyAddress}
      variant="default"
      size="default"
      className={className}
      showText={true}
    />
  )
}
