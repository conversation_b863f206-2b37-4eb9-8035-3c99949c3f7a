import { UserRole, SnbUserType } from '@/types'
import { currentUser } from '@clerk/nextjs/server'
// TODO: Migrate to .NET Web API - temporarily disabled
// import prisma from '@/lib/prisma'

export interface CreateUserData {
  email: string
  fullName?: string
  firstName?: string
  lastName?: string
  phone?: string
  role?: UserRole
  userType?: SnbUserType
  clerkUserId?: string
  authUserId?: string
}

export interface UpdateUserData {
  fullName?: string
  firstName?: string
  lastName?: string
  phone?: string
  role?: UserRole
  userType?: SnbUserType
  isActive?: boolean
}

export class UserService {
  /**
   * Map auth.users to snb.User on login
   * Creates or updates user record based on authentication data
   */
  static async mapAuthUserToSnbUser(authData: {
    id?: string
    email: string
    fullName?: string
    firstName?: string
    lastName?: string
    clerkUserId?: string
    authUserId?: string
  }) {
    try {
      // Use raw query to work with public.User table
      const existingUserQuery = `
        SELECT * FROM public."User"
        WHERE email = $1 OR "clerkUserId" = $2 OR "authUserId" = $3
        LIMIT 1
      `

      const existingUsers = await prisma.$queryRawUnsafe(
        existingUserQuery,
        authData.email,
        authData.clerkUserId || null,
        authData.authUserId || null
      ) as any[]

      if (existingUsers.length > 0) {
        const existingUser = existingUsers[0]

        // Update existing user with latest auth data
        const updateQuery = `
          UPDATE public."User"
          SET
            email = $1,
            "fullName" = COALESCE($2, "fullName"),
            "firstName" = COALESCE($3, "firstName"),
            "lastName" = COALESCE($4, "lastName"),
            "clerkUserId" = COALESCE($5, "clerkUserId"),
            "authUserId" = COALESCE($6, "authUserId"),
            "lastLoginAt" = $7,
            "loggedIn" = true,
            "updatedAt" = $8
          WHERE id = $9::uuid
          RETURNING *
        `

        const updatedUsers = await prisma.$queryRawUnsafe(
          updateQuery,
          authData.email,
          authData.fullName,
          authData.firstName,
          authData.lastName,
          authData.clerkUserId,
          authData.authUserId,
          new Date(),
          new Date(),
          existingUser.id
        ) as any[]

        return updatedUsers[0]
      } else {
        // Create new user
        const insertQuery = `
          INSERT INTO public."User" (
            id, email, "fullName", "firstName", "lastName",
            "clerkUserId", "authUserId", role, "userType",
            "isActive", "lastLoginAt", "loggedIn", "createdAt", "updatedAt"
          ) VALUES (
            gen_random_uuid(), $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
          ) RETURNING *
        `

        const newUsers = await prisma.$queryRawUnsafe(
          insertQuery,
          authData.email,
          authData.fullName || authData.email.split('@')[0],
          authData.firstName,
          authData.lastName,
          authData.clerkUserId,
          authData.authUserId,
          'USER', // Default role
          'Buyer', // Default user type
          true, // isActive
          new Date(), // lastLoginAt
          true, // loggedIn
          new Date(), // createdAt
          new Date() // updatedAt
        ) as any[]

        return newUsers[0]
      }
    } catch (error) {
      console.error('Error mapping auth user to SNB user:', error)
      console.error('Auth data:', authData)
      throw error
    }
  }

  /**
   * Get user by ID with role and permissions
   */
  static async getUserById(id: string) {
    try {
      const userQuery = `
        SELECT u.*, cb.id as "createdBy_id", cb.email as "createdBy_email", cb."fullName" as "createdBy_fullName"
        FROM public."User" u
        LEFT JOIN public."User" cb ON u."createdByAdmin" = cb.id
        WHERE u.id = $1::uuid
      `

      const users = await prisma.$queryRawUnsafe(userQuery, id) as any[]
      if (users.length === 0) return null

      const user = users[0]

      // Get user permissions based on role (with error handling)
      let permissions: Array<{permission: string, resource: string}> = []
      try {
        // Use raw query to avoid Prisma schema issues with updatedAt field
        const rolePermissionsQuery = `
          SELECT role, permission, resource
          FROM public.role_permissions
          WHERE role = $1::public."UserRole"
        `
        const rolePermissions = await prisma.$queryRawUnsafe(rolePermissionsQuery, user.role) as any[]
        permissions = rolePermissions.map(p => ({
          permission: p.permission,
          resource: p.resource
        }))
      } catch (permissionError) {
        console.warn('Error fetching permissions for user role:', user.role, permissionError)
        // Continue without permissions rather than failing completely
      }

      return {
        ...user,
        createdBy: user.createdBy_id ? {
          id: user.createdBy_id,
          email: user.createdBy_email,
          fullName: user.createdBy_fullName
        } : null,
        permissions
      }
    } catch (error) {
      console.error('Error getting user by ID:', error)
      throw error
    }
  }

  /**
   * Get user by email
   */
  static async getUserByEmail(email: string) {
    try {
      const userQuery = `
        SELECT u.*, cb.id as "createdBy_id", cb.email as "createdBy_email", cb."fullName" as "createdBy_fullName"
        FROM public."User" u
        LEFT JOIN public."User" cb ON u."createdByAdmin" = cb.id
        WHERE u.email = $1
      `

      const users = await prisma.$queryRawUnsafe(userQuery, email) as any[]
      if (users.length === 0) return null

      const user = users[0]
      return {
        ...user,
        createdBy: user.createdBy_id ? {
          id: user.createdBy_id,
          email: user.createdBy_email,
          fullName: user.createdBy_fullName
        } : null
      }
    } catch (error) {
      console.error('Error getting user by email:', error)
      throw error
    }
  }

  /**
   * Get user by Clerk ID
   */
  static async getUserByClerkId(clerkUserId: string) {
    try {
      const userQuery = `
        SELECT u.*, cb.id as "createdBy_id", cb.email as "createdBy_email", cb."fullName" as "createdBy_fullName"
        FROM public."User" u
        LEFT JOIN public."User" cb ON u."createdByAdmin" = cb.id
        WHERE u."clerkUserId" = $1
      `

      const users = await prisma.$queryRawUnsafe(userQuery, clerkUserId) as any[]
      if (users.length === 0) return null

      const user = users[0]
      return {
        ...user,
        createdBy: user.createdBy_id ? {
          id: user.createdBy_id,
          email: user.createdBy_email,
          fullName: user.createdBy_fullName
        } : null
      }
    } catch (error) {
      console.error('Error getting user by Clerk ID:', error)
      throw error
    }
  }

  /**
   * Create a new user (Admin only)
   */
  static async createUser(userData: CreateUserData, createdByAdminId?: string) {
    try {
      const insertQuery = `
        INSERT INTO public."User" (
          id, email, "fullName", "firstName", "lastName", phone, address,
          "clerkUserId", "authUserId", role, "userType", "isActive",
          "createdByAdmin", "createdAt", "updatedAt"
        ) VALUES (
          gen_random_uuid(), $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
        ) RETURNING *
      `

      const newUsers = await prisma.$queryRawUnsafe(
        insertQuery,
        userData.email,
        userData.fullName || userData.email.split('@')[0],
        userData.firstName,
        userData.lastName,
        userData.phone,
        null, // address
        userData.clerkUserId,
        userData.authUserId,
        userData.role || 'USER',
        userData.userType || 'Buyer',
        true, // isActive
        createdByAdminId,
        new Date(), // createdAt
        new Date() // updatedAt
      ) as any[]

      return newUsers[0]
    } catch (error) {
      console.error('Error creating user:', error)
      throw error
    }
  }

  /**
   * Update user data
   */
  static async updateUser(id: string, userData: UpdateUserData) {
    try {
      const updateQuery = `
        UPDATE public."User"
        SET
          "fullName" = COALESCE($1, "fullName"),
          "firstName" = COALESCE($2, "firstName"),
          "lastName" = COALESCE($3, "lastName"),
          phone = COALESCE($4, phone),
          role = COALESCE($5::public."UserRole", role),
          "userType" = COALESCE($6::public."UserType", "userType"),
          "isActive" = COALESCE($7, "isActive"),
          "updatedAt" = $8
        WHERE id = $9::uuid
        RETURNING *
      `

      const updatedUsers = await prisma.$queryRawUnsafe(
        updateQuery,
        userData.fullName,
        userData.firstName,
        userData.lastName,
        userData.phone,
        userData.role,
        userData.userType,
        userData.isActive,
        new Date(),
        id
      ) as any[]

      return updatedUsers[0]
    } catch (error) {
      console.error('Error updating user:', error)
      throw error
    }
  }

  /**
   * Update user type (Buyer/Seller/etc)
   */
  static async updateUserType(id: string, userType: SnbUserType) {
    try {
      const updateQuery = `
        UPDATE public."User"
        SET "userType" = $1::public."UserType", "updatedAt" = $2
        WHERE id = $3::uuid
        RETURNING *
      `

      const updatedUsers = await prisma.$queryRawUnsafe(
        updateQuery,
        userType,
        new Date(),
        id
      ) as any[]

      return updatedUsers[0]
    } catch (error) {
      console.error('Error updating user type:', error)
      throw error
    }
  }

  /**
   * Get all users (Admin only)
   */
  static async getAllUsers(page = 1, limit = 10, role?: UserRole) {
    try {
      const skip = (page - 1) * limit
      const roleFilter = role ? `WHERE u.role = '${role}'` : ''

      const usersQuery = `
        SELECT u.*, cb.id as "createdBy_id", cb.email as "createdBy_email", cb."fullName" as "createdBy_fullName"
        FROM public."User" u
        LEFT JOIN public."User" cb ON u."createdByAdmin" = cb.id
        ${roleFilter}
        ORDER BY u."createdAt" DESC
        LIMIT $1 OFFSET $2
      `

      const countQuery = `
        SELECT COUNT(*) as total
        FROM public."User" u
        ${roleFilter}
      `

      const [users, countResult] = await Promise.all([
        prisma.$queryRawUnsafe(usersQuery, limit, skip) as Promise<any[]>,
        prisma.$queryRawUnsafe(countQuery) as Promise<any[]>
      ])

      const total = parseInt(countResult[0].total)

      return {
        users: users.map(user => ({
          ...user,
          createdBy: user.createdBy_id ? {
            id: user.createdBy_id,
            email: user.createdBy_email,
            fullName: user.createdBy_fullName
          } : null
        })),
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    } catch (error) {
      console.error('Error getting all users:', error)
      throw error
    }
  }

  /**
   * Check if user has permission
   */
  static async hasPermission(userId: string, permission: string, resource?: string) {
    try {
      const userQuery = `
        SELECT role, "isActive" FROM public."User" WHERE id = $1::uuid
      `
      const users = await prisma.$queryRawUnsafe(userQuery, userId) as any[]

      if (users.length === 0 || !users[0].isActive) return false

      // Use raw query to avoid Prisma schema issues
      const permissionQuery = `
        SELECT 1 FROM public.role_permissions
        WHERE role = $1::public."UserRole" AND permission = $2 AND (resource = $3 OR resource IS NULL)
        LIMIT 1
      `
      const rolePermissions = await prisma.$queryRawUnsafe(
        permissionQuery,
        users[0].role,
        permission,
        resource || null
      ) as any[]

      const rolePermission = rolePermissions.length > 0

      return !!rolePermission
    } catch (error) {
      console.error('Error checking permission:', error)
      return false
    }
  }

  /**
   * Get current authenticated user from Clerk and map to SNB user
   */
  static async getCurrentUser() {
    try {
      const clerkUser = await currentUser()
      if (!clerkUser) return null

      const email = clerkUser.primaryEmailAddress?.emailAddress
      if (!email) return null

      // Map Clerk user to SNB user
      const snbUser = await this.mapAuthUserToSnbUser({
        email,
        fullName: `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim(),
        firstName: clerkUser.firstName || undefined,
        lastName: clerkUser.lastName || undefined,
        clerkUserId: clerkUser.id
      })

      return snbUser
    } catch (error) {
      console.error('Error getting current user:', error)
      return null
    }
  }
}

export default UserService

