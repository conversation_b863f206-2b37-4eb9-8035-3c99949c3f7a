using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using System.Text.Json;

namespace MicroSaasWebApi.Services.SoNoBrokers
{
    public class CommunicationService : ICommunicationService
    {
        private readonly MicroSaasWebApi.App.Context.IDapperDbContext _dbContext;
        private readonly ILogger<CommunicationService> _logger;
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;

        public CommunicationService(
            MicroSaasWebApi.App.Context.IDapperDbContext dbContext,
            ILogger<CommunicationService> logger,
            IConfiguration configuration,
            HttpClient httpClient)
        {
            _dbContext = dbContext;
            _logger = logger;
            _configuration = configuration;
            _httpClient = httpClient;
        }

        public Task<ContactConciergeResponse> SendConciergeInquiryAsync(ContactConciergeRequest request)
        {
            try
            {
                _logger.LogInformation("Processing concierge inquiry from: {Email}", request.Email);

                // Create email content
                var emailContent = $@"
New Concierge Service Inquiry

Name: {request.Name}
Email: {request.Email}
Phone: {request.Phone ?? "Not provided"}
Property Address: {request.PropertyAddress ?? "Not provided"}
Property Value: {request.PropertyValue ?? "Not provided"}
Timeline: {request.Timeline ?? "Not provided"}
Requirements: {request.Requirements ?? "Not provided"}
Package Interest: {request.PackageInterest ?? "Not provided"}
Country: {request.Country}
Selected Package: {request.SelectedPackage ?? "Not provided"}

---
This inquiry was submitted through the SoNoBrokers Concierge Services page.
Please respond within 24 hours.
                ".Trim();

                // TODO: Integrate with email service (Resend, SendGrid, etc.)
                // For now, log the inquiry
                _logger.LogInformation("Concierge inquiry: {Content}", emailContent);

                // In a real implementation, you would:
                // 1. Send <NAME_EMAIL>
                // 2. Send confirmation email to user
                // 3. Store inquiry in database if needed

                return Task.FromResult(new ContactConciergeResponse
                {
                    Message = "Inquiry sent successfully",
                    Success = true
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing concierge inquiry");
                return Task.FromResult(new ContactConciergeResponse
                {
                    Message = "Failed to send inquiry",
                    Success = false
                });
            }
        }

        public async Task<WaitingListResponse> AddToWaitingListAsync(WaitingListRequest request)
        {
            try
            {
                _logger.LogInformation("Adding email to waiting list: {Email}", request.Email);

                // TODO: Integrate with email service (Resend) to add to audience
                // For now, simulate the process
                await Task.Delay(500);

                var contactId = Guid.NewGuid().ToString();

                return new WaitingListResponse
                {
                    Message = "Successfully added to waiting list",
                    Success = true,
                    ContactId = contactId
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding to waiting list");
                return new WaitingListResponse
                {
                    Message = "Error adding to waiting list",
                    Success = false
                };
            }
        }

        public async Task<GeoLocationResponse> GetLocationByIpAsync(GeoLocationRequest request)
        {
            try
            {
                var ip = request.Ip;

                // If not provided, try test IP from configuration
                if (string.IsNullOrEmpty(ip))
                {
                    ip = _configuration["TEST_GEO_IP"];
                }

                // Default to localhost if still not provided
                if (string.IsNullOrEmpty(ip))
                {
                    ip = "127.0.0.1";
                }

                _logger.LogInformation("Getting location for IP: {IP}", ip);

                // Use a geolocation service
                var response = await _httpClient.GetAsync($"https://ipapi.co/{ip}/json/");

                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception("Failed to fetch location data");
                }

                var jsonContent = await response.Content.ReadAsStringAsync();
                var locationData = JsonSerializer.Deserialize<JsonElement>(jsonContent);

                return new GeoLocationResponse
                {
                    Country = locationData.GetProperty("country_code").GetString() ?? "US",
                    CountryName = locationData.GetProperty("country_name").GetString() ?? "United States",
                    City = locationData.GetProperty("city").GetString() ?? "Unknown",
                    Region = locationData.GetProperty("region").GetString() ?? "Unknown",
                    Timezone = locationData.GetProperty("timezone").GetString() ?? "UTC"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching location data");

                // Return default response if the geolocation service fails
                return new GeoLocationResponse
                {
                    Country = "US",
                    CountryName = "United States",
                    City = "Unknown",
                    Region = "Unknown",
                    Timezone = "UTC"
                };
            }
        }

        public async Task<EnumValuesResponse> GetEnumValuesAsync()
        {
            try
            {
                _logger.LogInformation("Fetching enum values from database");

                // Get countries from database
                var countriesQuery = @"
                    SELECT enumlabel as value
                    FROM pg_enum
                    WHERE enumtypid = (
                        SELECT oid
                        FROM pg_type
                        WHERE typname = 'Country'
                    )
                    ORDER BY enumlabel";

                var countries = await _dbContext.QueryAsync<string>(countriesQuery);

                // Get service types from database
                var serviceTypesQuery = @"
                    SELECT enumlabel as value
                    FROM pg_enum
                    WHERE enumtypid = (
                        SELECT oid
                        FROM pg_type
                        WHERE typname = 'ServiceType'
                    )
                    ORDER BY enumlabel";

                var serviceTypes = await _dbContext.QueryAsync<string>(serviceTypesQuery);

                // Get user types from database
                var userTypesQuery = @"
                    SELECT enumlabel as value
                    FROM pg_enum
                    WHERE enumtypid = (
                        SELECT oid
                        FROM pg_type
                        WHERE typname = 'UserType'
                    )
                    ORDER BY enumlabel";

                var userTypes = await _dbContext.QueryAsync<string>(userTypesQuery);

                // Map countries to country info
                var countryInfos = countries.Select(c => new CountryInfo
                {
                    Code = c,
                    Name = c switch
                    {
                        "CA" => "Canada",
                        "US" => "United States",
                        "UAE" => "United Arab Emirates",
                        _ => c
                    },
                    Flag = c switch
                    {
                        "CA" => "🇨🇦",
                        "US" => "🇺🇸",
                        "UAE" => "🇦🇪",
                        _ => "🏳️"
                    }
                }).ToList();

                // Map service types to service type info
                var serviceTypeInfos = serviceTypes.Select(st => new ServiceTypeInfo
                {
                    Value = st,
                    Label = st switch
                    {
                        "lawyer" => "Real Estate Lawyer",
                        "photographer" => "Property Photographer",
                        "inspector" => "Home Inspector",
                        "appraiser" => "Property Appraiser",
                        "home_inspector" => "Home Inspector",
                        "mortgage_broker" => "Mortgage Broker",
                        "insurance_agent" => "Insurance Agent",
                        "contractor" => "General Contractor",
                        "cleaner" => "Cleaning Service",
                        "stager" => "Home Stager",
                        "marketing_agency" => "Marketing Agency",
                        _ => st
                    }
                }).ToList();

                var enumValues = new Dictionary<string, object>
                {
                    { "countries", countryInfos },
                    { "serviceTypes", serviceTypeInfos },
                    { "userTypes", new[] {
                        new { value = "Buyer", label = "Buyer" },
                        new { value = "Seller", label = "Seller" }
                    }},
                    { "propertyStatuses", new[] { "ACTIVE", "SOLD", "PENDING", "WITHDRAWN", "EXPIRED", "DRAFT" } },
                    { "advertiserPlans", new[] { "basic", "premium", "enterprise" } },
                    { "advertiserStatuses", new[] { "pending", "active", "suspended", "cancelled" } }
                };

                return new EnumValuesResponse
                {
                    Enums = enumValues
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching enum values");
                throw;
            }
        }
    }
}
