﻿# =============================================================================
# Base Docker Configuration for SoNoBrokers React Application
# This file contains base settings that can be overridden by environment-specific files
# =============================================================================

# =============================================================================
# Application Configuration
# =============================================================================
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://localhost:3000
NEXT_PUBLIC_API_BASE_URL=https://host.docker.internal:7163

# =============================================================================
# Database Configuration (Base)
# =============================================================================
DATABASE_URL=***************************************************************************************************/postgres

# =============================================================================
# Authentication - Clerk Configuration (Base)
# =============================================================================
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_YmV0dGVyLXBvc3N1bS01OC5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_SECRET_KEY=sk_test_VpPsw9paA18Uka7jxuB6PeE3bLvzKVEESGaDuysAgL
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up

# =============================================================================
# Supabase Configuration
# =============================================================================
NEXT_PUBLIC_SUPABASE_URL=https://yfznlsisxsnymkvydzha.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.qUBshv6Wi-90ZAEnUM2RXuhR77QFHnmEpI6O-y7l3BE

# =============================================================================
# External Services (Base)
# =============================================================================
RESEND_API_KEY=re_BM6pyT8x_ChaS1fbRCbdprxPy1fwimWCs
NEXT_PUBLIC_MAPBOX_API_KEY=pk.eyJ1IjoiamF2aWFucGljYXJkbzMzIiwiYSI6ImNtYjY4ZGRyazBiaWYybHEyMWpnNGN4cDQifQ.m63aGFvbfzrQhT3sWlSbDQ

# =============================================================================
# Feature Flags (Base Settings)
# =============================================================================
NEXT_PUBLIC_ENABLE_GOOGLE_PROVIDERS=true
NEXT_PUBLIC_LAUNCH_MODE=false
NEXT_PUBLIC_DEV_MODE=false
NEXT_PUBLIC_SUPPORTED_COUNTRIES=CA,US,UAE
NEXT_PUBLIC_DEFAULT_COUNTRY=CA
