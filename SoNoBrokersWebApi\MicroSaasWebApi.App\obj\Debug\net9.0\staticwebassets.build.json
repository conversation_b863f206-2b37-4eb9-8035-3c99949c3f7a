{"Version": 1, "Hash": "w45/YhzfRIgIwcJM2la1JNEr6Mv93ALRpy7pnJC8YQM=", "Source": "MicroSaasWebApi.App", "BasePath": "_content/MicroSaasWebApi.App", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "MicroSaasWebApi.App\\wwwroot", "Source": "MicroSaasWebApi.App", "ContentRoot": "C:\\Projects\\SoNoBrokersRoot\\SoNoBrokersWebApi\\MicroSaasWebApi.App\\wwwroot\\", "BasePath": "_content/MicroSaasWebApi.App", "Pattern": "**"}], "Assets": [], "Endpoints": []}