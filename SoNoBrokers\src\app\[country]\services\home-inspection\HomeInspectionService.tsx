import React from 'react'
import { ServiceLayout } from '@/components/shared/services/ServiceLayout'

interface HomeInspectionServiceProps {
  userType: 'buyer' | 'seller'
  isSignedIn: boolean
  country: string
}

// Mock data - in real implementation, this would come from your database and Google Places API
const getMockProviders = (country: string) => {
  const baseProviders = [
    {
      id: '1',
      name: '<PERSON>',
      businessName: 'Wilson Home Inspections',
      serviceType: 'Certified Home Inspector',
      location: country === 'CA' ? 'Toronto, ON' : 'New York, NY',
      distance: '2.3 km',
      rating: 4.9,
      reviewCount: 156,
      price: 'From $450',
      specialties: ['Full Inspections', 'Thermal Imaging', 'Detailed Reports', 'Same-Day Service'],
      verified: true,
      isAdvertiser: true,
      isPremium: true,
      image: '/api/placeholder/80/80',
      phone: '******-0123',
      email: '<EMAIL>',
      website: 'https://wilsoninspections.com',
      description: 'Certified home inspector with 15+ years experience. Comprehensive reports delivered within 24 hours.',
      coordinates: country === 'CA' ? { lat: 43.6532, lng: -79.3832 } : { lat: 40.7128, lng: -74.0060 }
    },
    {
      id: '2',
      name: '<PERSON>',
      business<PERSON>ame: 'Chen Property Inspections',
      serviceType: 'Licensed Home Inspector',
      location: country === 'CA' ? 'Vancouver, BC' : 'Los Angeles, CA',
      distance: '3.7 km',
      rating: 4.8,
      reviewCount: 203,
      price: 'From $395',
      specialties: ['Structural Analysis', 'Electrical Systems', 'Plumbing', 'HVAC'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/80/80',
      phone: '******-0124',
      description: 'Professional home inspector specializing in older homes and heritage properties.',
      coordinates: country === 'CA' ? { lat: 49.2827, lng: -123.1207 } : { lat: 34.0522, lng: -118.2437 }
    },
    {
      id: '3',
      name: 'Mike Rodriguez',
      businessName: 'Rodriguez Inspection Services',
      serviceType: 'Home Inspector',
      location: country === 'CA' ? 'Calgary, AB' : 'Chicago, IL',
      distance: '5.1 km',
      rating: 4.7,
      reviewCount: 89,
      price: 'From $425',
      specialties: ['New Construction', 'Condo Inspections', 'Commercial Properties'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/80/80',
      description: 'Experienced inspector with background in construction and engineering.',
      coordinates: country === 'CA' ? { lat: 51.0447, lng: -114.0719 } : { lat: 41.8781, lng: -87.6298 }
    }
  ]

  // Add Google API providers (non-registered)
  const googleProviders = [
    {
      id: 'g1',
      name: 'ABC Home Inspections',
      businessName: 'ABC Home Inspections',
      serviceType: 'Home Inspector',
      location: country === 'CA' ? 'Mississauga, ON' : 'Brooklyn, NY',
      distance: '8.2 km',
      rating: 4.5,
      reviewCount: 67,
      price: 'From $380',
      specialties: ['General Inspections', 'Pest Control'],
      verified: false,
      isAdvertiser: false,
      isPremium: false,
      description: 'Local home inspection service found via Google listings.',
      coordinates: country === 'CA' ? { lat: 43.5890, lng: -79.6441 } : { lat: 40.6782, lng: -73.9442 }
    },
    {
      id: 'g2',
      name: 'Quick Inspect Pro',
      businessName: 'Quick Inspect Pro',
      serviceType: 'Home Inspector',
      location: country === 'CA' ? 'Markham, ON' : 'Queens, NY',
      distance: '12.5 km',
      rating: 4.3,
      reviewCount: 34,
      price: 'From $350',
      specialties: ['Fast Service', 'Weekend Availability'],
      verified: false,
      isAdvertiser: false,
      isPremium: false,
      description: 'Quick turnaround home inspection services available 7 days a week.',
      coordinates: country === 'CA' ? { lat: 43.8561, lng: -79.3370 } : { lat: 40.7282, lng: -73.7949 }
    }
  ]

  return [...baseProviders, ...googleProviders]
}

export function HomeInspectionService({
  userType,
  isSignedIn,
  country
}: HomeInspectionServiceProps) {
  // Get providers data (this would be async in real implementation)
  const mockProviders = getMockProviders(country)

  // Sort by distance and premium status
  const providers = mockProviders.sort((a, b) => {
    if (a.isPremium && !b.isPremium) return -1
    if (!a.isPremium && b.isPremium) return 1
    if (a.isAdvertiser && !b.isAdvertiser) return -1
    if (!a.isAdvertiser && b.isAdvertiser) return 1
    return parseFloat(a.distance) - parseFloat(b.distance)
  })

  const serviceDescription = userType === 'buyer'
    ? 'Professional home inspection services to help you make an informed purchase decision. Our certified inspectors provide comprehensive reports on property condition, potential issues, and maintenance recommendations.'
    : 'Pre-listing home inspections help identify and address potential issues before putting your property on the market. This proactive approach can prevent deal delays and help you price your home accurately.'

  // Check environment variable for Google providers
  const showGoogleProviders = process.env.NEXT_PUBLIC_ENABLE_GOOGLE_PROVIDERS !== 'false'

  return (
    <ServiceLayout
      userType={userType}
      isSignedIn={isSignedIn}
      serviceTitle="Home Inspection Services"
      serviceDescription={serviceDescription}
      country={country}
      providers={providers}
      showGoogleProviders={showGoogleProviders}
    />
  )
}
