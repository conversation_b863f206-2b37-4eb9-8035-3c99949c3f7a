{"navigation": {"home": "Home", "dashboard": "Dashboard", "properties": "Properties", "services": "Services", "about": "About", "contact": "Contact", "signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "getStarted": "Get Started", "buyerServices": "Buyer Services", "sellerServices": "Seller Services", "resources": "Resources", "settings": "Settings", "adminSettings": "<PERSON><PERSON>s", "advertise": "Advertise", "buyer": "Buyer", "seller": "<PERSON><PERSON>", "devTools": "<PERSON>", "regionTester": "Region Tester", "myAccount": "My Account", "sonobrokers": "SoNoBrokers", "sono": "SoNo"}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "clear": "Clear", "apply": "Apply", "submit": "Submit", "close": "Close", "open": "Open", "view": "View", "download": "Download", "upload": "Upload", "share": "Share", "copy": "Copy", "copied": "Copied!", "select": "Select", "selectAll": "Select All", "none": "None", "all": "All", "yes": "Yes", "no": "No", "optional": "Optional", "required": "Required"}, "countries": {"canada": "Canada", "usa": "United States", "uae": "United Arab Emirates"}, "languages": {"english": "English", "spanish": "Spanish", "french": "French", "arabic": "Arabic"}, "userTypes": {"buyer": "Buyer", "seller": "<PERSON><PERSON>"}, "properties": {"listProperty": "List Property", "searchProperties": "Search Properties", "propertyDetails": "Property Details", "price": "Price", "bedrooms": "Bedrooms", "bathrooms": "Bathrooms", "squareFeet": "Square Feet", "lotSize": "Lot Size", "yearBuilt": "Year Built", "propertyType": "Property Type", "status": "Status", "description": "Description", "features": "Features", "location": "Location", "address": "Address", "city": "City", "state": "State", "zipCode": "ZIP Code", "country": "Country"}, "services": {"photography": "Photography Services", "inspection": "Home Inspection", "mortgage": "Mortgage Services", "legal": "Legal Services", "insurance": "Insurance", "renovation": "Home Renovation", "cleaning": "Cleaning Services", "moving": "Moving Services", "staging": "Home Staging", "valuation": "Property Valuation"}, "forms": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "message": "Message", "subject": "Subject", "name": "Name", "company": "Company", "website": "Website", "address": "Address", "city": "City", "state": "State", "zipCode": "ZIP Code", "country": "Country", "tryagain": "Try Again", "oopssomethingwent": "Oops! Something went wrong", "somethingwentwrong": "Something went wrong", "gohome": "Go Home", "editproperty": "Edit Property", "listproperty": "List Property", "propertyinformation": "Property Information", "propertyimages": "Property Images", "contactsupport": "Contact Support", "pagenotfound": "Page Not Found", "userprofile": "User Profile", "phonenumber": "Phone Number", "emailaddress": "Email Address", "firstname": "First Name", "lastname": "Last Name", "savechanges": "Save Changes", "sendmessage": "Send Message"}, "errors": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidPhone": "Please enter a valid phone number", "minLength": "Must be at least {{min}} characters", "maxLength": "Must be no more than {{max}} characters", "networkError": "Network error. Please try again.", "serverError": "Server error. Please try again later.", "notFound": "Page not found", "unauthorized": "You are not authorized to access this page", "forbidden": "Access forbidden"}, "success": {"saved": "Successfully saved", "updated": "Successfully updated", "deleted": "Successfully deleted", "sent": "Successfully sent", "uploaded": "Successfully uploaded", "downloaded": "Successfully downloaded"}, "footer": {"allRightsReserved": "All rights reserved", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "cookiePolicy": "<PERSON><PERSON>", "support": "Support", "documentation": "Documentation", "api": "API", "status": "Status", "sonoBrokers": "SoNo Brokers", "listProperty": "List Property", "professionalServices": "Professional Services", "howItWorks": "How It Works", "contactLegal": "Contact & Legal", "marketplaceLegalNotice": "SoNoBrokers Marketplace Legal Notice", "platformDescription": "Platform Description", "generalDisclaimers": "General Disclaimers"}, "auth": {"welcomeBack": "Welcome back", "createAccount": "Create an account", "forgotPassword": "Forgot password?", "resetPassword": "Reset password", "changePassword": "Change password", "currentPassword": "Current password", "newPassword": "New password", "confirmPassword": "Confirm password", "rememberMe": "Remember me", "signInWith": "Sign in with {{provider}}", "signUpWith": "Sign up with {{provider}}", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "signInHere": "Sign in here", "signUpHere": "Sign up here"}, "dashboard": {"welcome": "Welcome to your dashboard", "overview": "Overview", "recentActivity": "Recent Activity", "quickActions": "Quick Actions", "statistics": "Statistics", "notifications": "Notifications", "profile": "Profile", "account": "Account", "billing": "Billing", "subscription": "Subscription"}, "pricing": {"free": "Free", "basic": "Basic", "premium": "Premium", "enterprise": "Enterprise", "monthly": "Monthly", "yearly": "Yearly", "perMonth": "per month", "perYear": "per year", "mostPopular": "Most Popular", "choosePlan": "Choose <PERSON>", "currentPlan": "Current Plan", "upgrade": "Upgrade", "downgrade": "Downgrade"}, "hero": {"title": "Discover Your", "dreamHome": "Dream Home", "inCanada": "in the United States", "subtitle": "Skip the commission fees and connect directly with property owners across all 50 states. Save thousands while finding your perfect American home."}, "buttons": {"browseProperties": "Browse Properties", "listProperty": "List Your Property"}, "homepage": {"badge": "America's Premier Commission-Free Platform", "title": "Find Your Perfect", "titleHighlight": "American Dream", "titleEnd": "Home", "subtitle": "Skip the commission fees and connect directly with property owners across all 50 states. Save thousands while finding your perfect American home.", "whyChoose": "Why Choose SoNoBrokers USA?", "whyChooseSubtitle": "America's most innovative commission-free real estate platform, revolutionizing property transactions.", "features": {"zeroCommission": {"title": "Zero Commission Fees", "description": "Save thousands on traditional realtor commissions. Keep more money for your American dream home."}, "nationwide": {"title": "Nationwide Coverage", "description": "From New York to California, Alaska to Florida - we cover all 50 states with local expertise."}, "mlsIntegration": {"title": "MLS Integration", "description": "Direct access to Multiple Listing Service data with real-time updates and verified information."}, "directCommunication": {"title": "Direct Communication", "description": "Connect directly with property owners and buyers. No middleman, transparent negotiations."}, "marketIntelligence": {"title": "Market Intelligence", "description": "Advanced analytics, price trends, and neighborhood insights powered by big data."}, "licensedProfessionals": {"title": "Licensed Professionals", "description": "Access to state-licensed real estate professionals, lawyers, and service providers."}}, "coverage": {"title": "Coast to Coast Coverage", "subtitle": "Serving all 50 states with local expertise and nationwide reach.", "regions": {"northeast": "Northeast", "southeast": "Southeast", "westCoast": "West Coast", "southwest": "Southwest", "alaskaHawaii": "Alaska & Hawaii"}}, "professionals": {"title": "Licensed Professionals", "subtitle": "Connect with verified professionals across all 50 states for seamless transactions.", "realEstateAttorneys": {"title": "Real Estate Attorneys", "description": "State-licensed attorneys specializing in property law and closings.", "features": ["Contract Review", "Title Insurance", "Closing Services"]}, "mortgageBrokers": {"title": "Mortgage Brokers", "description": "Licensed mortgage professionals with access to hundreds of lenders.", "features": ["Rate Shopping", "Pre-approval", "Loan Processing"]}, "photography": {"title": "Photography & Media", "description": "Professional real estate photography and marketing services.", "features": ["HDR Photography", "Virtual Tours", "Drone Videos"]}}, "howItWorks": {"title": "How It Works", "subtitle": "Simple, transparent process designed for the American real estate market.", "steps": {"signUp": {"step": "01", "title": "Sign Up Free", "description": "Create your account and verify identity. Choose your state and property preferences."}, "searchList": {"step": "02", "title": "Search or List", "description": "Browse MLS-integrated listings or list your property with professional photos."}, "connectClose": {"step": "03", "title": "Connect & Close", "description": "Connect directly with buyers/sellers. Use our network of professionals for closing."}}}, "cta": {"title": "Ready to Find Your American Dream Home?", "subtitle": "Join thousands of Americans who have saved millions in commission fees nationwide.", "getStarted": "Get Started Today", "learnMore": "Learn More"}, "stats": {"activeProperties": "75k+", "activePropertiesLabel": "Active Properties", "happyClients": "40k+", "happyClientsLabel": "Happy Clients", "statesCovered": "50", "statesCoveredLabel": "States Covered", "commissionSaved": "$4.2B+", "commissionSavedLabel": "Commission Saved"}}}