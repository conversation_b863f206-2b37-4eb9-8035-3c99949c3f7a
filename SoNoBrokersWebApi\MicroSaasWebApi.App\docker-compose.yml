# =============================================================================
# SoNoBrokers .NET Web API - Independent Docker Setup
# This runs ONLY the .NET Web API backend
# =============================================================================

version: '3.8'

# =============================================================================
# Networks
# =============================================================================
networks:
  sonobrokers-api:
    driver: bridge

# =============================================================================
# Volumes
# =============================================================================
volumes:
  api-logs:
    driver: local

# =============================================================================
# Services
# =============================================================================
services:
  # ===========================================================================
  # .NET Web API Application
  # ===========================================================================
  api:
    build:
      context: .
      dockerfile: Dockerfile
      target: final
      args:
        BUILD_DATE: ${BUILD_DATE:-}
        VCS_REF: ${VCS_REF:-}
        VERSION: ${VERSION:-1.0.0}
    container_name: sonobrokers-api
    restart: unless-stopped
    environment:
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT:-Production}
      - ASPNETCORE_URLS=https://+:7163
      - DATABASE_URL=${DATABASE_URL:-***************************************************************************/postgres}
      - ConnectionStrings__DefaultConnection=${DATABASE_URL:-***************************************************************************/postgres}
      - ConnectionStrings__SupabaseConnection=${DATABASE_URL:-***************************************************************************/postgres}
      - CLERK_SECRET_KEY=${CLERK_SECRET_KEY:-sk_test_VpPsw9paA18Uka7jxuB6PeE3bLvzKVEESGaDuysAgL}
      - NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=${NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:-pk_test_YmV0dGVyLXBvc3N1bS01OC5jbGVyay5hY2NvdW50cy5kZXYk}
      - Authentication__Clerk__SecretKey=${CLERK_SECRET_KEY}
      - Authentication__Clerk__PublishableKey=${CLERK_PUBLISHABLE_KEY}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY:-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}
      - RESEND_API_KEY=${RESEND_API_KEY:-re_BM6pyT8x_ChaS1fbRCbdprxPy1fwimWCs}
      - Logging__LogLevel__Default=${LOG_LEVEL:-Information}
      - Logging__LogLevel__Microsoft.AspNetCore=Warning
      - CORS__AllowedOrigins=${CORS_ALLOWED_ORIGINS:-https://localhost:3000,http://localhost:3000}
    ports:
      - "${API_HTTPS_PORT:-7163}:8443"
    volumes:
      - api-logs:/app/logs
    networks:
      - sonobrokers-api
    healthcheck:
      test: ["CMD", "curl", "-f", "-k", "https://localhost:8443/api/sonobrokers/test/ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    labels:
      - "com.sonobrokers.service=api"
      - "com.sonobrokers.version=${VERSION:-1.0.0}"

# =============================================================================
# Development Override Available
# Use: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
# =============================================================================
