using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.Core;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using Dapper;
using SoNoBrokersProperty = MicroSaasWebApi.Models.SoNoBrokers.Property;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/sonobrokers/simple-properties")]
    [Tags("Simple Properties")]
    public class SimplePropertiesController : ControllerBase
    {
        private readonly IPropertyService _propertyService;
        private readonly ILogger<SimplePropertiesController> _logger;

        public SimplePropertiesController(IPropertyService propertyService, ILogger<SimplePropertiesController> logger)
        {
            _propertyService = propertyService;
            _logger = logger;
        }

        /// <summary>
        /// Get all properties
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ApiResponse<IEnumerable<SoNoBrokersProperty>>>> GetProperties()
        {
            try
            {
                var properties = await _propertyService.GetAllPropertiesAsync();
                return Ok(ApiResponse<IEnumerable<SoNoBrokersProperty>>.SuccessResult(properties, "Properties retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving properties");
                return StatusCode(500, ApiResponse<IEnumerable<SoNoBrokersProperty>>.ErrorResult("Failed to retrieve properties"));
            }
        }

        /// <summary>
        /// Get property by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<ApiResponse<SoNoBrokersProperty>>> GetProperty(string id)
        {
            try
            {
                var property = await _propertyService.GetPropertyByIdAsync(id);

                if (property == null)
                {
                    return NotFound(ApiResponse<SoNoBrokersProperty>.ErrorResult("Property not found"));
                }

                return Ok(ApiResponse<SoNoBrokersProperty>.SuccessResult(property, "Property retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving property {PropertyId}", id);
                return StatusCode(500, ApiResponse<SoNoBrokersProperty>.ErrorResult("Failed to retrieve property"));
            }
        }

        /// <summary>
        /// Create a new property
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<ApiResponse<SoNoBrokersProperty>>> CreateProperty(SoNoBrokersProperty property)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse<SoNoBrokersProperty>.ErrorResult("Invalid property data"));
                }

                var createdProperty = await _propertyService.CreatePropertyAsync(property);

                return CreatedAtAction(nameof(GetProperty), new { id = createdProperty.Id },
                    ApiResponse<SoNoBrokersProperty>.SuccessResult(createdProperty, "Property created successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating property");
                return StatusCode(500, ApiResponse<SoNoBrokersProperty>.ErrorResult("Failed to create property"));
            }
        }

        /// <summary>
        /// Search properties
        /// </summary>
        [HttpGet("search")]
        public async Task<ActionResult<ApiResponse<IEnumerable<SoNoBrokersProperty>>>> SearchProperties(
            [FromQuery] string? searchTerm = null,
            [FromQuery] string? city = null,
            [FromQuery] decimal? minPrice = null,
            [FromQuery] decimal? maxPrice = null,
            [FromQuery] int? bedrooms = null,
            [FromQuery] int? bathrooms = null)
        {
            try
            {
                var properties = await _propertyService.SearchPropertiesAsync(
                    searchTerm: searchTerm,
                    city: city,
                    minPrice: minPrice,
                    maxPrice: maxPrice,
                    bedrooms: bedrooms,
                    bathrooms: bathrooms);

                return Ok(ApiResponse<IEnumerable<SoNoBrokersProperty>>.SuccessResult(properties, "Search completed successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching properties");
                return StatusCode(500, ApiResponse<IEnumerable<SoNoBrokersProperty>>.ErrorResult("Failed to search properties"));
            }
        }
    }
}
