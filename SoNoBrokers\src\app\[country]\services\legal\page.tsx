import { LegalService } from './LegalService'
// Removed auth import - no authentication required for browsing legal services
import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function LegalPage({ params, searchParams }: PageProps) {
  // No authentication required for browsing legal services
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/services/legal')
  }

  // Default to buyer if no userType specified
  const userType = resolvedSearchParams.userType || 'buyer'

  return (
    <LegalService
      userType={userType}
      isSignedIn={isSignedIn}
      country={country.toUpperCase()}
    />
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'buyer'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Real Estate Legal Services for ${userType === 'buyer' ? 'Buyers' : 'Sellers'} in ${country} | SoNoBrokers`,
    description: `Expert real estate lawyers in ${country}. Professional legal services for property transactions, contract review, and closing procedures. Find qualified attorneys near you.`,
    keywords: `real estate lawyer, property attorney, legal services, ${userType}, ${country}, closing lawyer`,
  }
}
