import { NextResponse } from 'next/server'
import { UserService } from '@/services/userService'
import { auth } from '@clerk/nextjs/server'

// GET /api/test-auth - Test authentication flow
export async function GET() {
  try {
    // Use auth() instead of currentUser() for API routes
    const { userId } = await auth()

    if (!userId) {
      return NextResponse.json({
        success: false,
        message: 'Not authenticated with Clerk',
        clerkUserId: null
      }, { status: 401 })
    }

    // Test getting SNB user by Clerk ID
    const snbUser = await UserService.getUserByClerkId(userId)

    return NextResponse.json({
      success: true,
      message: 'Authentication successful',
      data: {
        clerkUserId: userId,
        snbUser: snbUser ? {
          id: snbUser.id,
          email: snbUser.email,
          fullName: snbUser.fullName,
          role: snbUser.role,
          userType: snbUser.userType
        } : null
      }
    })
  } catch (error) {
    console.error('Error testing authentication:', error)
    return NextResponse.json(
      { success: false, error: 'Authentication test failed', details: error.message },
      { status: 500 }
    )
  }
}
