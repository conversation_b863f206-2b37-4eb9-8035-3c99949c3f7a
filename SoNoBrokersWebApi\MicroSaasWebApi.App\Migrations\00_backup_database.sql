-- =====================================================
-- SoNoBrokers Database Backup Script
-- This script creates a complete backup of the current database
-- Run this BEFORE running the new comprehensive database script
-- =====================================================

-- =====================================================
-- BACKUP INSTRUCTIONS
-- =====================================================

/*
To create a backup of your current database:

1. Using pg_dump (Recommended):
   pg_dump "***************************************************************************************************/postgres" > backup_$(date +%Y%m%d_%H%M%S).sql

2. Using Supabase Dashboard:
   - Go to Settings → Database
   - Click "Database backups"
   - Create a manual backup

3. Using this SQL script (creates a data export):
   Run the queries below to export your current data
*/

-- =====================================================
-- DATA EXPORT QUERIES
-- =====================================================

-- Export current table structure
SELECT 
  table_name,
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
ORDER BY table_name, ordinal_position;

-- Export current enum types
SELECT 
  t.typname as enum_name,
  e.enumlabel as enum_value
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid  
WHERE t.typname IN (
  'UserRole', 'UserStatus', 'PropertyType', 'PropertyStatus', 'ListingType',
  'SubscriptionStatus', 'SubscriptionType', 'ServiceType',
  'ContactShareType', 'ContactShareStatus', 'VisitStatus', 'VisitType', 'VerificationMethod'
)
ORDER BY t.typname, e.enumsortorder;

-- Export current indexes
SELECT 
  schemaname,
  tablename,
  indexname,
  indexdef
FROM pg_indexes 
WHERE schemaname = 'public'
ORDER BY tablename, indexname;

-- Export current foreign key constraints
SELECT
  tc.table_name, 
  kcu.column_name, 
  ccu.table_name AS foreign_table_name,
  ccu.column_name AS foreign_column_name,
  tc.constraint_name
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
  ON tc.constraint_name = kcu.constraint_name
  AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
  ON ccu.constraint_name = tc.constraint_name
  AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_schema = 'public'
ORDER BY tc.table_name, kcu.column_name;

-- =====================================================
-- CURRENT DATABASE STATISTICS
-- =====================================================

-- Count records in each table
DO $$
DECLARE
  table_record RECORD;
  table_count INTEGER;
BEGIN
  RAISE NOTICE '=== CURRENT DATABASE STATISTICS ===';
  
  FOR table_record IN 
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_type = 'BASE TABLE'
    ORDER BY table_name
  LOOP
    EXECUTE format('SELECT COUNT(*) FROM public."%I"', table_record.table_name) INTO table_count;
    RAISE NOTICE 'Table "%": % records', table_record.table_name, table_count;
  END LOOP;
END $$;

-- =====================================================
-- BACKUP VERIFICATION
-- =====================================================

-- List all tables that will be affected
SELECT 
  'Tables to be recreated:' as info,
  string_agg(table_name, ', ') as tables
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE';

-- List all enums that will be recreated
SELECT 
  'Enums to be recreated:' as info,
  string_agg(DISTINCT typname, ', ') as enums
FROM pg_type 
WHERE typname IN (
  'UserRole', 'UserStatus', 'PropertyType', 'PropertyStatus', 'ListingType',
  'SubscriptionStatus', 'SubscriptionType', 'ServiceType',
  'ContactShareType', 'ContactShareStatus', 'VisitStatus', 'VisitType', 'VerificationMethod'
);

-- =====================================================
-- BACKUP COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '📋 DATABASE BACKUP INFORMATION GENERATED';
  RAISE NOTICE '=====================================';
  RAISE NOTICE '';
  RAISE NOTICE '⚠️  IMPORTANT: Before running 01_create_database.sql:';
  RAISE NOTICE '1. Save the output of this script';
  RAISE NOTICE '2. Create a pg_dump backup if possible';
  RAISE NOTICE '3. Verify you have access to restore if needed';
  RAISE NOTICE '';
  RAISE NOTICE '🔄 The new script will:';
  RAISE NOTICE '- DROP and recreate all tables';
  RAISE NOTICE '- DROP and recreate all enums';
  RAISE NOTICE '- Create new indexes and constraints';
  RAISE NOTICE '- Add new tables for Contact Sharing and Property Scheduling';
  RAISE NOTICE '';
  RAISE NOTICE '✅ After backup, you can safely run:';
  RAISE NOTICE '1. 01_create_database.sql (complete schema)';
  RAISE NOTICE '2. 03_stored_procedures_functions.sql (business logic)';
  RAISE NOTICE '3. 04_seed_data.sql (sample data)';
  RAISE NOTICE '4. 05_verify_database_sync.sql (verification)';
  RAISE NOTICE '';
END $$;
