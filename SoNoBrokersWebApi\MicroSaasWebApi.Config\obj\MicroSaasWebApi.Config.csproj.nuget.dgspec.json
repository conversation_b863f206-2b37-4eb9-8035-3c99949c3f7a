{"format": 1, "restore": {"C:\\Projects\\SoNoBrokersRoot\\SoNoBrokersWebApi\\MicroSaasWebApi.Config\\MicroSaasWebApi.Config.csproj": {}}, "projects": {"C:\\Projects\\SoNoBrokersRoot\\SoNoBrokersWebApi\\MicroSaasWebApi.Config\\MicroSaasWebApi.Config.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\SoNoBrokersRoot\\SoNoBrokersWebApi\\MicroSaasWebApi.Config\\MicroSaasWebApi.Config.csproj", "projectName": "MicroSaasWebApi.Config", "projectPath": "C:\\Projects\\SoNoBrokersRoot\\SoNoBrokersWebApi\\MicroSaasWebApi.Config\\MicroSaasWebApi.Config.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\SoNoBrokersRoot\\SoNoBrokersWebApi\\MicroSaasWebApi.Config\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"], "warnNotAsError": ["NU1605", "NU1902"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[9.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}