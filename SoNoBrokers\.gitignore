# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files (Next.js standard)
# Commit: .env, .env.development, .env.production
# Ignore: .env.local, .env.development.local, .env.production.local
.env.local
.env.development.local
.env.production.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# public
public/robots.txt
public/sitemap.xml
public/sitemap-0.xml