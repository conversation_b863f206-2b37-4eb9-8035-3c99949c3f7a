using MicroSaasWebApi.Models.SoNoBrokers.ContactSharing;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Net.Http;
using System.Text;
using System.Text.Json;

namespace MicroSaasWebApi.Services.SoNoBrokers
{
    /// <summary>
    /// Email service for contact sharing functionality using Resend API
    /// </summary>
    public class ContactSharingEmailService : IContactSharingEmailService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<ContactSharingEmailService> _logger;
        private readonly string _resendApiKey;
        private readonly string _fromEmail;
        private readonly string _baseUrl;

        public ContactSharingEmailService(
            HttpClient httpClient,
            IConfiguration configuration,
            ILogger<ContactSharingEmailService> logger)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _logger = logger;
            _resendApiKey = _configuration["Resend:ApiKey"] ?? throw new ArgumentNullException("Resend:ApiKey");
            _fromEmail = _configuration["Resend:FromEmail"] ?? "<EMAIL>";
            _baseUrl = _configuration["App:BaseUrl"] ?? "https://www.sonobrokers.com";

            _httpClient.BaseAddress = new Uri("https://api.resend.com/");
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_resendApiKey}");
        }

        public async Task<bool> SendContactShareEmailAsync(ContactShareResponse contactShare)
        {
            try
            {
                var subject = $"New Contact Request for {contactShare.PropertyTitle}";
                var htmlContent = GenerateContactShareEmailHtml(contactShare);
                var textContent = GenerateContactShareEmailText(contactShare);

                return await SendEmailAsync(
                    contactShare.SellerEmail,
                    contactShare.SellerName,
                    subject,
                    htmlContent,
                    textContent,
                    contactShare.Id
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send contact share email for {ContactShareId}", contactShare.Id);
                return false;
            }
        }

        public async Task<bool> SendPropertyOfferEmailAsync(ContactShareResponse contactShare)
        {
            try
            {
                var subject = $"New Property Offer: ${contactShare.OfferAmount:N0} for {contactShare.PropertyTitle}";
                var htmlContent = GeneratePropertyOfferEmailHtml(contactShare);
                var textContent = GeneratePropertyOfferEmailText(contactShare);

                return await SendEmailAsync(
                    contactShare.SellerEmail,
                    contactShare.SellerName,
                    subject,
                    htmlContent,
                    textContent,
                    contactShare.Id
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send property offer email for {ContactShareId}", contactShare.Id);
                return false;
            }
        }

        public async Task<bool> SendVisitSchedulingEmailAsync(ContactShareResponse contactShare)
        {
            try
            {
                var subject = $"Property Visit Request for {contactShare.PropertyTitle}";
                var htmlContent = GenerateVisitSchedulingEmailHtml(contactShare);
                var textContent = GenerateVisitSchedulingEmailText(contactShare);

                return await SendEmailAsync(
                    contactShare.SellerEmail,
                    contactShare.SellerName,
                    subject,
                    htmlContent,
                    textContent,
                    contactShare.Id
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send visit scheduling email for {ContactShareId}", contactShare.Id);
                return false;
            }
        }

        public async Task<bool> SendOfferWithVisitEmailAsync(ContactShareResponse contactShare)
        {
            try
            {
                var subject = $"Property Offer & Visit Request: ${contactShare.OfferAmount:N0} for {contactShare.PropertyTitle}";
                var htmlContent = GenerateOfferWithVisitEmailHtml(contactShare);
                var textContent = GenerateOfferWithVisitEmailText(contactShare);

                return await SendEmailAsync(
                    contactShare.SellerEmail,
                    contactShare.SellerName,
                    subject,
                    htmlContent,
                    textContent,
                    contactShare.Id
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send offer with visit email for {ContactShareId}", contactShare.Id);
                return false;
            }
        }

        public async Task<bool> SendBuyerConfirmationEmailAsync(ContactShareResponse contactShare)
        {
            try
            {
                var subject = GetBuyerConfirmationSubject(contactShare);
                var htmlContent = GenerateBuyerConfirmationEmailHtml(contactShare);
                var textContent = GenerateBuyerConfirmationEmailText(contactShare);

                return await SendEmailAsync(
                    contactShare.BuyerEmail,
                    contactShare.BuyerName,
                    subject,
                    htmlContent,
                    textContent,
                    contactShare.Id
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send buyer confirmation email for {ContactShareId}", contactShare.Id);
                return false;
            }
        }

        public async Task<bool> SendSellerResponseEmailAsync(ContactShareResponse contactShare, string sellerResponse)
        {
            try
            {
                var subject = $"Response from {contactShare.SellerName} - {contactShare.PropertyTitle}";
                var htmlContent = GenerateSellerResponseEmailHtml(contactShare, sellerResponse);
                var textContent = GenerateSellerResponseEmailText(contactShare, sellerResponse);

                return await SendEmailAsync(
                    contactShare.BuyerEmail,
                    contactShare.BuyerName,
                    subject,
                    htmlContent,
                    textContent,
                    contactShare.Id
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send seller response email for {ContactShareId}", contactShare.Id);
                return false;
            }
        }

        public async Task<bool> SendSellerReminderEmailAsync(ContactShareResponse contactShare)
        {
            try
            {
                var subject = $"Reminder: Pending {contactShare.ShareTypeDisplay} for {contactShare.PropertyTitle}";
                var htmlContent = GenerateSellerReminderEmailHtml(contactShare);
                var textContent = GenerateSellerReminderEmailText(contactShare);

                return await SendEmailAsync(
                    contactShare.SellerEmail,
                    contactShare.SellerName,
                    subject,
                    htmlContent,
                    textContent,
                    contactShare.Id
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send seller reminder email for {ContactShareId}", contactShare.Id);
                return false;
            }
        }

        public string GenerateEmailTrackingId()
        {
            return Guid.NewGuid().ToString("N");
        }

        public Task TrackEmailOpenAsync(string trackingId)
        {
            try
            {
                _logger.LogInformation("Email opened: {TrackingId}", trackingId);
                // TODO: Store email tracking data in database
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to track email open for {TrackingId}", trackingId);
                return Task.CompletedTask;
            }
        }

        public Task TrackEmailClickAsync(string trackingId, string linkType)
        {
            try
            {
                _logger.LogInformation("Email link clicked: {TrackingId}, Link: {LinkType}", trackingId, linkType);
                // TODO: Store email click tracking data in database
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to track email click for {TrackingId}", trackingId);
                return Task.CompletedTask;
            }
        }

        /// <summary>
        /// Send email using Resend API
        /// </summary>
        private async Task<bool> SendEmailAsync(
            string toEmail,
            string toName,
            string subject,
            string htmlContent,
            string textContent,
            string contactShareId)
        {
            try
            {
                var trackingId = GenerateEmailTrackingId();

                var emailRequest = new
                {
                    from = $"SoNoBrokers <{_fromEmail}>",
                    to = new[] { $"{toName} <{toEmail}>" },
                    subject = subject,
                    html = htmlContent,
                    text = textContent,
                    tags = new[]
                    {
                        new { name = "category", value = "contact-sharing" },
                        new { name = "contact_share_id", value = contactShareId },
                        new { name = "tracking_id", value = trackingId }
                    }
                };

                var json = JsonSerializer.Serialize(emailRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("emails", content);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Email sent successfully to {Email} for contact share {ContactShareId}",
                        toEmail, contactShareId);
                    return true;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to send email to {Email}. Status: {StatusCode}, Error: {Error}",
                        toEmail, response.StatusCode, errorContent);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception sending email to {Email} for contact share {ContactShareId}",
                    toEmail, contactShareId);
                return false;
            }
        }

        // Email template generation methods will be added in the next part
        private string GenerateContactShareEmailHtml(ContactShareResponse contactShare) => "";
        private string GenerateContactShareEmailText(ContactShareResponse contactShare) => "";
        private string GeneratePropertyOfferEmailHtml(ContactShareResponse contactShare) => "";
        private string GeneratePropertyOfferEmailText(ContactShareResponse contactShare) => "";
        private string GenerateVisitSchedulingEmailHtml(ContactShareResponse contactShare) => "";
        private string GenerateVisitSchedulingEmailText(ContactShareResponse contactShare) => "";
        private string GenerateOfferWithVisitEmailHtml(ContactShareResponse contactShare) => "";
        private string GenerateOfferWithVisitEmailText(ContactShareResponse contactShare) => "";
        private string GenerateBuyerConfirmationEmailHtml(ContactShareResponse contactShare) => "";
        private string GenerateBuyerConfirmationEmailText(ContactShareResponse contactShare) => "";
        private string GenerateSellerResponseEmailHtml(ContactShareResponse contactShare, string sellerResponse) => "";
        private string GenerateSellerResponseEmailText(ContactShareResponse contactShare, string sellerResponse) => "";
        private string GenerateSellerReminderEmailHtml(ContactShareResponse contactShare) => "";
        private string GenerateSellerReminderEmailText(ContactShareResponse contactShare) => "";
        private string GetBuyerConfirmationSubject(ContactShareResponse contactShare) => "";
    }
}