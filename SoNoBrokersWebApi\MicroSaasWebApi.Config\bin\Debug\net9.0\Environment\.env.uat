# =============================================================================
# UAT ENVIRONMENT SECRETS
# =============================================================================
# ⚠️  DO NOT COMMIT THIS FILE TO SOURCE CONTROL
# This file contains UAT environment secrets and sensitive configuration
# Build pipeline will replace these values with Azure Key Vault references
# =============================================================================

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Single database connection using DATABASE_URL (consolidated configuration)
DATABASE_URL=#{UAT_DATABASE_URL}#

# =============================================================================
# API ENDPOINTS (UAT URLs)
# =============================================================================
ApiEndpoints__DataApi__BaseUrl=https://uat-api.microsaas.com/data-api/v1
ApiEndpoints__DocumentApi__BaseUrl=https://uat-api.microsaas.com/document-api/v1
ApiEndpoints__PermissionApi__BaseUrl=https://uat-api.microsaas.com/permission-api/v1

# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================
# Clerk Authentication (UAT)
Authentication__Clerk__PublishableKey=#{UAT_CLERK_PUBLISHABLE_KEY}#
Authentication__Clerk__SecretKey=#{UAT_CLERK_SECRET_KEY}#
Authentication__Clerk__WebhookSecret=#{UAT_CLERK_WEBHOOK_SECRET}#
Authentication__Clerk__JwtIssuer=#{UAT_CLERK_JWT_ISSUER}#

# JWT Configuration (UAT)
Authentication__Jwt__Issuer=https://uat-api.microsaas.com
Authentication__Jwt__SigningKey=#{UAT_JWT_SIGNING_KEY}#

# =============================================================================
# EXTERNAL SERVICES (UAT)
# =============================================================================
# Stripe Payment Service (Test Mode)
ExternalServices__Stripe__PublishableKey=#{UAT_STRIPE_PUBLISHABLE_KEY}#
ExternalServices__Stripe__SecretKey=#{UAT_STRIPE_SECRET_KEY}#
ExternalServices__Stripe__WebhookSecret=#{UAT_STRIPE_WEBHOOK_SECRET}#

# Azure Services (UAT)
ExternalServices__Azure__Storage__ConnectionString=#{UAT_AZURE_STORAGE_CONNECTION_STRING}#
ExternalServices__Azure__ServiceBus__ConnectionString=#{UAT_AZURE_SERVICEBUS_CONNECTION_STRING}#
ExternalServices__Azure__KeyVault__Uri=#{UAT_AZURE_KEYVAULT_URI}#
ExternalServices__Azure__KeyVault__ClientId=#{UAT_AZURE_CLIENT_ID}#
ExternalServices__Azure__KeyVault__ClientSecret=#{UAT_AZURE_CLIENT_SECRET}#
ExternalServices__Azure__KeyVault__TenantId=#{UAT_AZURE_TENANT_ID}#

# Email Services (UAT)
ExternalServices__Email__SendGrid__ApiKey=#{UAT_SENDGRID_API_KEY}#
ExternalServices__Email__Smtp__Host=#{UAT_SMTP_HOST}#
ExternalServices__Email__Smtp__Username=#{UAT_SMTP_USERNAME}#
ExternalServices__Email__Smtp__Password=#{UAT_SMTP_PASSWORD}#
ExternalServices__Email__Resend__ApiKey=#{UAT_RESEND_API_KEY}#

# =============================================================================
# EXTERNAL API INTEGRATIONS (UAT)
# =============================================================================
# Make.com Integration
MAKE_ORGANIZATION_ID=#{UAT_MAKE_ORGANIZATION_ID}#
MAKE_TEAM_ID=#{UAT_MAKE_TEAM_ID}#
MAKE_API_KEY=#{UAT_MAKE_API_KEY}#
MAKE_API_URL=https://us1.make.com/api/v2

# N8N Integration
N8N_API_KEY=#{UAT_N8N_API_KEY}#
N8N_API_URL=https://uat-n8n.microsaas.com/api/v1
N8N_WEBHOOK_URL=https://uat-n8n.microsaas.com/webhook

# =============================================================================
# LEGACY SYSTEMS (UAT)
# =============================================================================
LEGACY_DATABASE_URL=#{UAT_LEGACY_DATABASE_URL}#
WORDPRESS_DB_USER=#{UAT_WORDPRESS_DB_USER}#
WORDPRESS_DB_PASSWORD=#{UAT_WORDPRESS_DB_PASSWORD}#
WORDPRESS_DB_NAME=#{UAT_WORDPRESS_DB_NAME}#
WP_REST_ENDPOINT=#{UAT_WP_REST_ENDPOINT}#
