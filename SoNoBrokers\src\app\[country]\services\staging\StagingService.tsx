import React from 'react'
import { ServiceLayout } from '@/components/shared/services/ServiceLayout'

interface StagingServiceProps {
  userType: 'buyer' | 'seller'
  isSignedIn: boolean
  country: string
}

// Mock data - in real implementation, this would come from your database and Google Places API
const getMockProviders = (country: string) => {
  const baseProviders = [
    {
      id: '1',
      name: '<PERSON>',
      businessName: 'Rodriguez Home Staging',
      serviceType: 'Professional Home Stager',
      location: country === 'CA' ? 'Toronto, ON' : 'New York, NY',
      distance: '1.7 km',
      rating: 4.9,
      reviewCount: 143,
      price: 'From $1,500',
      specialties: ['Luxury Staging', 'Vacant Home Staging', 'Occupied Home Staging', 'Consultation'],
      verified: true,
      isAdvertiser: true,
      isPremium: true,
      image: '/api/placeholder/80/80',
      phone: '******-0181',
      email: '<EMAIL>',
      website: 'https://rodriguezstaging.com',
      description: 'Award-winning home stager with 12+ years experience. Specializing in luxury properties and quick sales.',
      coordinates: country === 'CA' ? { lat: 43.6532, lng: -79.3832 } : { lat: 40.7128, lng: -74.0060 }
    },
    {
      id: '2',
      name: 'Jennifer Walsh',
      businessName: 'Walsh Design & Staging',
      serviceType: 'Interior Designer & Stager',
      location: country === 'CA' ? 'Vancouver, BC' : 'Los Angeles, CA',
      distance: '3.1 km',
      rating: 4.8,
      reviewCount: 118,
      price: 'From $1,200',
      specialties: ['Modern Staging', 'Color Consultation', 'Furniture Rental', 'Decluttering'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/80/80',
      phone: '******-0182',
      description: 'Creative staging solutions that highlight your property\'s best features and appeal to target buyers.',
      coordinates: country === 'CA' ? { lat: 49.2827, lng: -123.1207 } : { lat: 34.0522, lng: -118.2437 }
    },
    {
      id: '3',
      name: 'Kevin Chen',
      businessName: 'Chen Staging Solutions',
      serviceType: 'Home Staging Specialist',
      location: country === 'CA' ? 'Calgary, AB' : 'Chicago, IL',
      distance: '4.3 km',
      rating: 4.7,
      reviewCount: 95,
      price: 'From $900',
      specialties: ['Budget Staging', 'Partial Staging', 'DIY Consultation', 'Quick Turnaround'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/80/80',
      description: 'Affordable staging solutions that maximize your property\'s appeal without breaking the budget.',
      coordinates: country === 'CA' ? { lat: 51.0447, lng: -114.0719 } : { lat: 41.8781, lng: -87.6298 }
    }
  ]

  // Add Google API providers (non-registered)
  const googleProviders = [
    {
      id: 'g1',
      name: 'Quick Stage Pro',
      businessName: 'Quick Stage Pro',
      serviceType: 'Staging Service',
      location: country === 'CA' ? 'Mississauga, ON' : 'Brooklyn, NY',
      distance: '8.7 km',
      rating: 4.3,
      reviewCount: 42,
      price: 'From $700',
      specialties: ['Basic Staging', 'Consultation Only'],
      verified: false,
      isAdvertiser: false,
      isPremium: false,
      description: 'Basic home staging services and consultation for DIY staging.',
      coordinates: country === 'CA' ? { lat: 43.5890, lng: -79.6441 } : { lat: 40.6782, lng: -73.9442 }
    }
  ]

  return [...baseProviders, ...googleProviders]
}

export function StagingService({
  userType,
  isSignedIn,
  country
}: StagingServiceProps) {
  // Get providers data (this would be async in real implementation)
  const mockProviders = getMockProviders(country)

  // Sort by distance and premium status
  const providers = mockProviders.sort((a, b) => {
    if (a.isPremium && !b.isPremium) return -1
    if (!a.isPremium && b.isPremium) return 1
    if (a.isAdvertiser && !b.isAdvertiser) return -1
    if (!a.isAdvertiser && b.isAdvertiser) return 1
    return parseFloat(a.distance) - parseFloat(b.distance)
  })

  const serviceDescription = userType === 'seller'
    ? 'Professional home staging services to maximize your property\'s appeal and selling potential. Our certified stagers transform your space to attract more buyers and achieve faster sales at higher prices.'
    : 'Home staging consultation services to help you envision the potential of properties you\'re considering. Professional advice on layout, design, and renovation possibilities.'

  // Check environment variable for Google providers
  const showGoogleProviders = process.env.NEXT_PUBLIC_ENABLE_GOOGLE_PROVIDERS !== 'false'

  return (
    <ServiceLayout
      userType={userType}
      isSignedIn={isSignedIn}
      serviceTitle="Staging Services"
      serviceDescription={serviceDescription}
      country={country}
      providers={providers}
      showGoogleProviders={showGoogleProviders}
    />
  )
}
