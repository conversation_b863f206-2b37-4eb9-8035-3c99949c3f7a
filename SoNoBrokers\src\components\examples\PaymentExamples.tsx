/**
 * Payment Examples Component
 * Demonstrates how to use the payment buttons with country-specific pricing
 */

'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import {
  PropertyListingPaymentButton,
  AdvertiserSubscriptionButton,
  ConciergeServicePaymentButton
} from '@/components/payments/PaymentButton'
import { PaymentMethodFactory } from '@/components/factories/PaymentMethodFactory'
import { Country } from '@/lib/geo'
import {
  getPropertyListingPrice,
  getAdvertiserSubscriptionPrice,
  getConciergeServicePrice,
  formatCurrency,
  validatePriceConfiguration,
  type Country as StripeCountry,
  type PropertyType,
  type AdvertiserPlan,
  type ConciergeService
} from '@/lib/stripe-price-config'

// Convert geo Country enum to Stripe Country format
function convertToStripeCountry(geoCountry: Country): StripeCountry {
  switch (geoCountry) {
    case Country.CA:
      return 'Canada';
    case Country.US:
      return 'USA';
    case Country.UAE:
      return 'UAE';
    default:
      return 'USA'; // Default fallback
  }
}

export function PaymentExamples() {
  const [selectedCountry, setSelectedCountry] = useState<Country>(Country.US)
  const [userEmail] = useState('<EMAIL>') // In real app, get from auth

  // Validate price configuration on component mount
  const { isValid, missingPriceIds } = validatePriceConfiguration()

  if (!isValid) {
    return (
      <div className="p-6 border border-red-200 rounded-lg bg-red-50">
        <h3 className="text-lg font-semibold text-red-800 mb-2">Payment Configuration Error</h3>
        <p className="text-red-600 mb-4">Some Stripe price IDs are not configured:</p>
        <ul className="list-disc list-inside text-red-600">
          {missingPriceIds.map(priceId => (
            <li key={priceId}>{priceId}</li>
          ))}
        </ul>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <h2 className="text-2xl font-bold">Payment Examples</h2>
        <Select value={selectedCountry} onValueChange={(value) => setSelectedCountry(value as Country)}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={Country.US}>🇺🇸 USA</SelectItem>
            <SelectItem value={Country.CA}>🇨🇦 Canada</SelectItem>
            <SelectItem value={Country.UAE}>🇦🇪 UAE</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Property Listing Payments */}
      <Card>
        <CardHeader>
          <CardTitle>Property Listing Fees</CardTitle>
          <CardDescription>One-time payments for listing properties</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {(['Residential', 'Commercial', 'Land', 'Rental'] as PropertyType[]).map(propertyType => {
            const priceConfig = getPropertyListingPrice(convertToStripeCountry(selectedCountry), propertyType)
            return (
              <div key={propertyType} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">{propertyType} Property</h4>
                  <p className="text-sm text-muted-foreground">
                    {formatCurrency(priceConfig.amount, priceConfig.currency)} - {priceConfig.displayName}
                  </p>
                </div>
                <PropertyListingPaymentButton
                  propertyId="example-property-123"
                  propertyType={propertyType}
                  country={convertToStripeCountry(selectedCountry)}
                  email={userEmail}
                  size="sm"
                />
              </div>
            )
          })}
        </CardContent>
      </Card>

      {/* Advertiser Subscriptions */}
      <Card>
        <CardHeader>
          <CardTitle>Advertiser Subscriptions</CardTitle>
          <CardDescription>Monthly recurring payments for advertising</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {(['Basic', 'Premium'] as AdvertiserPlan[]).map(plan => {
            const priceConfig = getAdvertiserSubscriptionPrice(convertToStripeCountry(selectedCountry), plan)
            return (
              <div key={plan} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{plan} Plan</h4>
                    <Badge variant={plan === 'Premium' ? 'default' : 'secondary'}>
                      {plan === 'Premium' ? 'Popular' : 'Starter'}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {formatCurrency(priceConfig.amount, priceConfig.currency)}/month - {priceConfig.displayName}
                  </p>
                </div>
                <AdvertiserSubscriptionButton
                  subscriptionPlan={plan}
                  country={convertToStripeCountry(selectedCountry)}
                  email={userEmail}
                  size="sm"
                />
              </div>
            )
          })}
        </CardContent>
      </Card>

      {/* Concierge Services */}
      <Card>
        <CardHeader>
          <CardTitle>Concierge Services</CardTitle>
          <CardDescription>One-time payments for professional services</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {(['Consultation', 'PropertyManagement', 'LegalAssistance', 'DocumentPreparation', 'MarketAnalysis'] as ConciergeService[]).map(service => {
            const priceConfig = getConciergeServicePrice(selectedCountry, service)
            return (
              <div key={service} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">{service.replace(/([A-Z])/g, ' $1').trim()}</h4>
                  <p className="text-sm text-muted-foreground">
                    {formatCurrency(priceConfig.amount, priceConfig.currency)} - {priceConfig.displayName}
                  </p>
                </div>
                <ConciergeServicePaymentButton
                  serviceType={service}
                  country={selectedCountry}
                  email={userEmail}
                  projectId="example-project-456"
                  size="sm"
                />
              </div>
            )
          })}
        </CardContent>
      </Card>

      {/* Payment Method Factory Example */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Method Factory</CardTitle>
          <CardDescription>Country-specific payment components using factory pattern</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <h4 className="font-medium">Property Listing Payment for {selectedCountry}</h4>
            <PaymentMethodFactory
              country={selectedCountry}
              paymentType="property_listing"
              propertyType="Residential"
              propertyId="factory-example-property"
              email={userEmail}
              onSuccess={(paymentId) => console.log('Payment successful:', paymentId)}
              onError={(error) => console.error('Payment error:', error)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Price Configuration Info */}
      <Card>
        <CardHeader>
          <CardTitle>Price Configuration Status</CardTitle>
          <CardDescription>Current Stripe price ID configuration</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-green-600 border-green-600">
              ✅ All price IDs configured
            </Badge>
            <p className="text-sm text-muted-foreground">
              All required Stripe price IDs are properly configured for all countries and services.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default PaymentExamples
