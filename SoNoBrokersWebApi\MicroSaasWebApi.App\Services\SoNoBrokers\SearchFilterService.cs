using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;

namespace MicroSaasWebApi.Services.SoNoBrokers
{
    public class SearchFilterService : ISearchFilterService
    {
        private readonly IDapperDbContext _dbContext;
        private readonly ILogger<SearchFilterService> _logger;

        public SearchFilterService(IDapperDbContext dbContext, ILogger<SearchFilterService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<IEnumerable<SearchFilter>> GetUserSearchFiltersAsync(string userId)
        {
            try
            {
                var sql = @"
                    SELECT id, ""userId"", name, filters, ""isActive"", ""createdAt"", ""updatedAt""
                    FROM public.""SearchFilter""
                    WHERE ""userId"" = @userId AND ""isActive"" = true
                    ORDER BY ""createdAt"" DESC";

                return await _dbContext.QueryAsync<SearchFilter>(sql, new { userId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving search filters for user {UserId}", userId);
                throw;
            }
        }

        public async Task<SearchFilter?> GetSearchFilterByIdAsync(string id)
        {
            try
            {
                var sql = @"
                    SELECT id, ""userId"", name, filters, ""isActive"", ""createdAt"", ""updatedAt""
                    FROM public.""SearchFilter""
                    WHERE id = @id";

                return await _dbContext.QueryFirstOrDefaultAsync<SearchFilter>(sql, new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving search filter {FilterId}", id);
                throw;
            }
        }

        public async Task<SearchFilter> CreateSearchFilterAsync(SearchFilter searchFilter)
        {
            try
            {
                searchFilter.Id = Guid.NewGuid().ToString();
                searchFilter.CreatedAt = DateTime.UtcNow;
                searchFilter.UpdatedAt = DateTime.UtcNow;

                var sql = @"
                    INSERT INTO public.""SearchFilter"" (id, ""userId"", name, filters, ""isActive"", ""createdAt"", ""updatedAt"")
                    VALUES (@Id, @UserId, @Name, @Filters::jsonb, @IsActive, @CreatedAt, @UpdatedAt)
                    RETURNING id, ""userId"", name, filters, ""isActive"", ""createdAt"", ""updatedAt""";

                var result = await _dbContext.QueryFirstOrDefaultAsync<SearchFilter>(sql, searchFilter);
                return result ?? searchFilter;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating search filter for user {UserId}", searchFilter.UserId);
                throw;
            }
        }

        public async Task<SearchFilter> UpdateSearchFilterAsync(SearchFilter searchFilter)
        {
            try
            {
                searchFilter.UpdatedAt = DateTime.UtcNow;

                var sql = @"
                    UPDATE public.""SearchFilter""
                    SET name = @Name, filters = @Filters::jsonb, ""isActive"" = @IsActive, ""updatedAt"" = @UpdatedAt
                    WHERE id = @Id
                    RETURNING id, ""userId"", name, filters, ""isActive"", ""createdAt"", ""updatedAt""";

                var result = await _dbContext.QueryFirstOrDefaultAsync<SearchFilter>(sql, searchFilter);
                return result ?? searchFilter;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating search filter {FilterId}", searchFilter.Id);
                throw;
            }
        }

        public async Task<bool> DeleteSearchFilterAsync(string id)
        {
            try
            {
                var sql = @"
                    UPDATE public.""SearchFilter""
                    SET ""isActive"" = false, ""updatedAt"" = @UpdatedAt
                    WHERE id = @id";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, new { id, UpdatedAt = DateTime.UtcNow });
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting search filter {FilterId}", id);
                throw;
            }
        }

        public async Task<bool> SetDefaultSearchFilterAsync(string userId, string filterId)
        {
            try
            {
                // This would require adding a default flag to the table
                // For now, just return true as this feature might not be implemented yet
                _logger.LogWarning("SetDefaultSearchFilter not implemented - requires schema update");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting default search filter {FilterId} for user {UserId}", filterId, userId);
                throw;
            }
        }
    }
}
