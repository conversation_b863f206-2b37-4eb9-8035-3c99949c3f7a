using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;

namespace MicroSaasWebApi.Services.SoNoBrokers
{
    public class SubscriptionService : ISubscriptionService
    {
        private readonly MicroSaasWebApi.App.Context.IDapperDbContext _dbContext;
        private readonly ILogger<SubscriptionService> _logger;

        public SubscriptionService(MicroSaasWebApi.App.Context.IDapperDbContext dbContext, ILogger<SubscriptionService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<IEnumerable<Subscription>> GetAllSubscriptionsAsync()
        {
            try
            {
                var sql = @"
                    SELECT s.*, u.full_name as user_name, u.email as user_email
                    FROM snb.subscriptions s
                    LEFT JOIN snb.users u ON s.user_id = u.id
                    ORDER BY s.created_at DESC";

                return await _dbContext.QueryAsync<Subscription>(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all subscriptions");
                throw;
            }
        }

        public async Task<Subscription?> GetSubscriptionByIdAsync(string id)
        {
            try
            {
                var sql = @"
                    SELECT s.*, u.full_name as user_name, u.email as user_email
                    FROM snb.subscriptions s
                    LEFT JOIN snb.users u ON s.user_id = u.id
                    WHERE s.id = @id";

                return await _dbContext.QueryFirstOrDefaultAsync<Subscription>(sql, new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving subscription {SubscriptionId}", id);
                throw;
            }
        }

        public async Task<Subscription?> GetSubscriptionByStripeIdAsync(string stripeSubscriptionId)
        {
            try
            {
                var sql = @"
                    SELECT s.*, u.full_name as user_name, u.email as user_email
                    FROM snb.subscriptions s
                    LEFT JOIN snb.users u ON s.user_id = u.id
                    WHERE s.stripe_subscription_id = @stripeSubscriptionId";

                return await _dbContext.QueryFirstOrDefaultAsync<Subscription>(sql, new { stripeSubscriptionId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving subscription by Stripe ID {StripeSubscriptionId}", stripeSubscriptionId);
                throw;
            }
        }

        public async Task<IEnumerable<Subscription>> GetUserSubscriptionsAsync(string userId)
        {
            try
            {
                var sql = @"
                    SELECT s.*, u.full_name as user_name, u.email as user_email
                    FROM snb.subscriptions s
                    LEFT JOIN snb.users u ON s.user_id = u.id
                    WHERE s.user_id = @userId
                    ORDER BY s.created_at DESC";

                return await _dbContext.QueryAsync<Subscription>(sql, new { userId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving subscriptions for user {UserId}", userId);
                throw;
            }
        }

        public async Task<Subscription> CreateSubscriptionAsync(Subscription subscription)
        {
            try
            {
                // Verify user exists
                var userExists = await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM snb.users WHERE id = @userId)",
                    new { userId = subscription.UserId });

                if (!userExists)
                {
                    throw new InvalidOperationException("User not found");
                }

                // Check if subscription with same Stripe ID already exists
                var stripeIdExists = await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM snb.subscriptions WHERE stripe_subscription_id = @stripeSubscriptionId)",
                    new { stripeSubscriptionId = subscription.StripeSubscriptionId });

                if (stripeIdExists)
                {
                    throw new InvalidOperationException("Subscription with this Stripe ID already exists");
                }

                subscription.Id = Guid.NewGuid().ToString();
                subscription.CreatedAt = DateTime.UtcNow;
                subscription.UpdatedAt = DateTime.UtcNow;

                var sql = @"
                    INSERT INTO snb.subscriptions (id, user_id, stripe_subscription_id, status, plan_type, starts_at, ends_at, created_at, updated_at)
                    VALUES (@Id, @UserId, @StripeSubscriptionId, @Status, @PlanType, @StartsAt, @EndsAt, @CreatedAt, @UpdatedAt)";

                await _dbContext.ExecuteAsync(sql, subscription);

                // Load the subscription with related data
                var createdSubscription = await GetSubscriptionByIdAsync(subscription.Id);

                return createdSubscription!;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating subscription");
                throw;
            }
        }

        public async Task<Subscription> UpdateSubscriptionAsync(Subscription subscription)
        {
            try
            {
                var exists = await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM snb.subscriptions WHERE id = @id)",
                    new { id = subscription.Id });

                if (!exists)
                {
                    throw new InvalidOperationException("Subscription not found");
                }

                subscription.UpdatedAt = DateTime.UtcNow;

                var sql = @"
                    UPDATE snb.subscriptions SET
                        status = @Status,
                        plan_type = @PlanType,
                        starts_at = @StartsAt,
                        ends_at = @EndsAt,
                        updated_at = @UpdatedAt
                    WHERE id = @Id";

                await _dbContext.ExecuteAsync(sql, subscription);

                // Load updated subscription with related data
                var updatedSubscription = await GetSubscriptionByIdAsync(subscription.Id);

                return updatedSubscription!;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating subscription {SubscriptionId}", subscription.Id);
                throw;
            }
        }

        public async Task<bool> DeleteSubscriptionAsync(string id)
        {
            try
            {
                var exists = await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM snb.subscriptions WHERE id = @id)",
                    new { id });

                if (!exists)
                {
                    return false;
                }

                await _dbContext.ExecuteAsync("DELETE FROM snb.subscriptions WHERE id = @id", new { id });
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting subscription {SubscriptionId}", id);
                throw;
            }
        }

        public async Task<bool> CancelSubscriptionAsync(string id)
        {
            try
            {
                var exists = await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM snb.subscriptions WHERE id = @id)",
                    new { id });

                if (!exists)
                {
                    return false;
                }

                var sql = @"
                    UPDATE snb.subscriptions SET
                        status = 'cancelled',
                        updated_at = @updatedAt
                    WHERE id = @id";

                await _dbContext.ExecuteAsync(sql, new { id, updatedAt = DateTime.UtcNow });
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling subscription {SubscriptionId}", id);
                throw;
            }
        }

        public async Task<bool> SubscriptionExistsAsync(string id)
        {
            try
            {
                return await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM snb.subscriptions WHERE id = @id)",
                    new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if subscription exists {SubscriptionId}", id);
                throw;
            }
        }

        public async Task<bool> StripeSubscriptionExistsAsync(string stripeSubscriptionId)
        {
            try
            {
                return await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM snb.subscriptions WHERE stripe_subscription_id = @stripeSubscriptionId)",
                    new { stripeSubscriptionId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if Stripe subscription exists {StripeSubscriptionId}", stripeSubscriptionId);
                throw;
            }
        }

        public async Task<Subscription?> GetActiveSubscriptionAsync(string userId)
        {
            try
            {
                var sql = @"
                    SELECT s.*, u.full_name as user_name, u.email as user_email
                    FROM snb.subscriptions s
                    LEFT JOIN snb.users u ON s.user_id = u.id
                    WHERE s.user_id = @userId AND s.status = 'active'
                    ORDER BY s.created_at DESC
                    LIMIT 1";

                return await _dbContext.QueryFirstOrDefaultAsync<Subscription>(sql, new { userId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active subscription for user {UserId}", userId);
                throw;
            }
        }
    }
}
