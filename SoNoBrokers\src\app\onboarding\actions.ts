"use server";
import { currentUser } from '@clerk/nextjs/server';
// TODO: Migrate to .NET Web API - temporarily disabled
// import { prisma } from '@/lib/prisma';
import { SnbUserType } from '@/types';

export async function updateUserRole(role: 'seller' | 'buyer') {
  // TODO: Migrate to .NET Web API
  throw new Error('Function temporarily disabled - migrating to .NET Web API');
}

/* DISABLED - MIGRATE TO .NET WEB API
export async function updateUserRole_DISABLED(role: 'seller' | 'buyer') {
  const user = await currentUser();
  if (!user) throw new Error('Not authenticated');

  // Convert lowercase role to capitalized enum value
  const userType = role === 'seller' ? SnbUserType.Seller : SnbUserType.Buyer;

  // Update userType in the User table
  await prisma.user.update({
    where: { email: user.emailAddresses[0].emailAddress },
    data: { userType },
  });

  // Create profile if not exists
  if (role === 'seller') {
    await prisma.sellerProfile.upsert({
      where: { userId: user.id },
      update: {},
      create: { userId: user.id },
    });
  } else if (role === 'buyer') {
    await prisma.buyerProfile.upsert({
      where: { userId: user.id },
      update: {},
      create: { userId: user.id },
    });
  }

  // Optionally, update Clerk public metadata
  // await user.update({ publicMetadata: { role } });

  return { success: true };
}
*/