using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    public enum OfferStatus
    {
        pending,
        reviewed,
        accepted,
        rejected
    }

    [Table("BuyerOffer", Schema = "snb")]
    public class BuyerOffer
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string PropertyId { get; set; } = string.Empty;

        [Required]
        public string BuyerId { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal OfferAmount { get; set; }

        [Column(TypeName = "jsonb")]
        public string? Conditions { get; set; }

        public string? Message { get; set; }

        public OfferStatus Status { get; set; } = OfferStatus.pending;

        public DateTime SubmittedAt { get; set; } = DateTime.UtcNow;

        public DateTime? ExpiresAt { get; set; }

        public DateTime? ReviewedAt { get; set; }

        public DateTime? RespondedAt { get; set; }

        public string? SellerResponse { get; set; }

        // Navigation properties
        [ForeignKey("PropertyId")]
        public virtual Property Property { get; set; } = null!;

        // Helper methods for JSON fields
        public T? GetConditionsAs<T>() where T : class
        {
            if (string.IsNullOrEmpty(Conditions)) return null;
            try
            {
                return JsonSerializer.Deserialize<T>(Conditions);
            }
            catch
            {
                return null;
            }
        }

        public void SetConditions<T>(T conditions) where T : class
        {
            Conditions = conditions != null ? JsonSerializer.Serialize(conditions) : null;
        }
    }
}
