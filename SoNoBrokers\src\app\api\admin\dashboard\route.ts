import { NextResponse } from 'next/server'
import { requireAdminAPI } from '@/lib/auth'
// TODO: Migrate to .NET Web API - temporarily disabled
// import { PrismaClient } from '@prisma/client'
// const prisma = new PrismaClient()

// GET /api/admin/dashboard - Get admin dashboard statistics
export async function GET() {
  // TODO: Migrate to .NET Web API
  return NextResponse.json(
    { error: 'API route temporarily disabled - migrating to .NET Web API' },
    { status: 503 }
  )
}

  /* DISABLED - MIGRATE TO .NET WEB API
  try {
    // Require admin authentication
    const adminUser = await requireAdminAPI()

    if (!adminUser) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }
    
    // Get user statistics
    const userStatsQuery = `
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN "isActive" = true THEN 1 END) as active_users,
        COUNT(CASE WHEN role = 'ADMIN' THEN 1 END) as admin_users,
        COUNT(CASE WHEN role = 'USER' THEN 1 END) as regular_users,
        COUNT(CASE WHEN role = 'PRODUCT' THEN 1 END) as product_users,
        COUNT(CASE WHEN role = 'OPERATOR' THEN 1 END) as operator_users,
        COUNT(CASE WHEN "userType" = 'buyer' THEN 1 END) as buyers,
        COUNT(CASE WHEN "userType" = 'seller' THEN 1 END) as sellers,
        COUNT(CASE WHEN "userType" = 'operator' THEN 1 END) as operators
      FROM snb."User"
    `

    const userStats = await prisma.$queryRawUnsafe(userStatsQuery) as any[]

    // Get recent users (last 30 days)
    const recentUsersQuery = `
      SELECT COUNT(*) as recent_users
      FROM snb."User"
      WHERE "createdAt" >= NOW() - INTERVAL '30 days'
    `

    const recentUsers = await prisma.$queryRawUnsafe(recentUsersQuery) as any[]

    // Get login statistics (last 30 days)
    const loginStatsQuery = `
      SELECT COUNT(*) as recent_logins
      FROM snb."User"
      WHERE "lastLoginAt" >= NOW() - INTERVAL '30 days'
    `

    const loginStats = await prisma.$queryRawUnsafe(loginStatsQuery) as any[]

    // Get subscription statistics if available
    const subscriptionStats = await prisma.subscriptionSnb.groupBy({
      by: ['status'],
      _count: {
        id: true
      }
    })

    const stats = userStats[0]
    
    return NextResponse.json({
      success: true,
      data: {
        users: {
          total: parseInt(stats.total_users),
          active: parseInt(stats.active_users),
          recent: parseInt(recentUsers[0].recent_users),
          byRole: {
            admin: parseInt(stats.admin_users),
            user: parseInt(stats.regular_users),
            product: parseInt(stats.product_users),
            operator: parseInt(stats.operator_users)
          },
          byType: {
            buyers: parseInt(stats.buyers),
            sellers: parseInt(stats.sellers),
            operators: parseInt(stats.operators)
          }
        },
        activity: {
          recentLogins: parseInt(loginStats[0].recent_logins)
        },
        subscriptions: subscriptionStats.reduce((acc, stat) => {
          acc[stat.status] = stat._count.id
          return acc
        }, {} as Record<string, number>)
      }
    })
  } catch (error) {
    console.error('Error fetching admin dashboard data:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch dashboard data' },
      { status: 500 }
    )
  }
}
*/
