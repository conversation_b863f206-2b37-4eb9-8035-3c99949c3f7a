'use client'

import { useState } from 'react'
import { useFormState } from 'react-dom'
import { useUser } from '@clerk/nextjs'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { MessageCircle, Send, User } from 'lucide-react'
import { startConversationFromPropertyAction } from '@/lib/actions/messaging-actions'

interface PropertyContactFormProps {
  propertyId: string
  sellerId: string
  sellerName: string
  propertyTitle: string
  propertyPrice: number
  className?: string
}

const initialState = {
  success: false,
  error: null,
  fieldErrors: {},
}

export function PropertyContactForm({
  propertyId,
  sellerId,
  sellerName,
  propertyTitle,
  propertyPrice,
  className = ''
}: PropertyContactFormProps) {
  const { user, isSignedIn } = useUser()
  const [isExpanded, setIsExpanded] = useState(false)
  const [state, formAction] = useFormState(
    startConversationFromPropertyAction.bind(null, propertyId, sellerId),
    initialState
  )

  if (!isSignedIn) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Contact Seller
          </CardTitle>
          <CardDescription>
            Sign in to contact {sellerName} about this property
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button className="w-full" onClick={() => window.location.href = '/sign-in'}>
            <User className="h-4 w-4 mr-2" />
            Sign In to Contact Seller
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (!isExpanded) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Contact Seller
          </CardTitle>
          <CardDescription>
            Send a message to {sellerName} about this property
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="font-medium text-sm">{sellerName}</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">Property Owner</p>
              </div>
            </div>
            
            <Button 
              className="w-full" 
              onClick={() => setIsExpanded(true)}
            >
              <MessageCircle className="h-4 w-4 mr-2" />
              Send Message
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5" />
          Contact Seller
        </CardTitle>
        <CardDescription>
          Send a message to {sellerName} about "{propertyTitle}"
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form action={formAction} className="space-y-4">
          {state.error && (
            <Alert variant="destructive">
              <AlertDescription>{state.error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="subject">Subject</Label>
            <Input
              id="subject"
              name="subject"
              placeholder="Property Inquiry"
              defaultValue={`Inquiry about ${propertyTitle}`}
              className={state.fieldErrors?.subject ? 'border-red-500' : ''}
            />
            {state.fieldErrors?.subject && (
              <p className="text-sm text-red-500">{state.fieldErrors.subject[0]}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="message">Message</Label>
            <Textarea
              id="message"
              name="message"
              placeholder={`Hi ${sellerName}, I'm interested in your property listed at $${propertyPrice.toLocaleString()}. Could you please provide more information?`}
              rows={4}
              className={state.fieldErrors?.message ? 'border-red-500' : ''}
              required
            />
            {state.fieldErrors?.message && (
              <p className="text-sm text-red-500">{state.fieldErrors.message[0]}</p>
            )}
            <p className="text-xs text-gray-500">
              Be specific about your interest and any questions you have about the property.
            </p>
          </div>

          <div className="flex gap-2">
            <Button type="submit" className="flex-1">
              <Send className="h-4 w-4 mr-2" />
              Send Message
            </Button>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setIsExpanded(false)}
            >
              Cancel
            </Button>
          </div>

          <div className="text-xs text-gray-500 space-y-1">
            <p>• Your message will be sent directly to the property owner</p>
            <p>• You'll be notified when they respond</p>
            <p>• All conversations are private and secure</p>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

/**
 * Compact version for property cards
 */
export function PropertyContactButton({
  propertyId,
  sellerId,
  sellerName,
  propertyTitle,
  className = ''
}: Omit<PropertyContactFormProps, 'propertyPrice'>) {
  const { isSignedIn } = useUser()

  const handleClick = () => {
    if (!isSignedIn) {
      window.location.href = '/sign-in'
      return
    }

    // Open contact form in a modal or navigate to property page
    window.location.href = `/properties/${propertyId}#contact`
  }

  return (
    <Button 
      variant="outline" 
      size="sm" 
      className={className}
      onClick={handleClick}
    >
      <MessageCircle className="h-4 w-4 mr-2" />
      Contact Seller
    </Button>
  )
}

/**
 * Quick contact modal trigger
 */
export function QuickContactModal({
  propertyId,
  sellerId,
  sellerName,
  propertyTitle,
  propertyPrice,
  trigger
}: PropertyContactFormProps & { trigger: React.ReactNode }) {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <div onClick={() => setIsOpen(true)} className="cursor-pointer">
        {trigger}
      </div>
      
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-900 rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-4 border-b">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Contact Seller</h3>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => setIsOpen(false)}
                >
                  ×
                </Button>
              </div>
            </div>
            <div className="p-4">
              <PropertyContactForm
                propertyId={propertyId}
                sellerId={sellerId}
                sellerName={sellerName}
                propertyTitle={propertyTitle}
                propertyPrice={propertyPrice}
              />
            </div>
          </div>
        </div>
      )}
    </>
  )
}
