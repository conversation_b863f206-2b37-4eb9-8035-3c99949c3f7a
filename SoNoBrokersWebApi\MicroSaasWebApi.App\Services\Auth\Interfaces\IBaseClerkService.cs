namespace MicroSaasWebApi.Services.Auth.Interfaces
{
    public interface IBaseClerkService
    {
        Task<string> CreateUserAsync(string email, string password);
        Task<bool> VerifyUserAsync(string userId, string token);
        Task<string> GeneratePasswordResetTokenAsync(string email);
        Task<bool> ResetPasswordAsync(string token, string newPassword);
        Task<object> GetUserAsync(string userId);
        Task<bool> UpdateUserAsync(string userId, object userData);
        Task<bool> DeleteUserAsync(string userId);
        Task<string> CreateSessionAsync(string userId);
        Task<bool> ValidateSessionAsync(string sessionToken);
        Task<bool> RevokeSessionAsync(string sessionToken);
    }
}
