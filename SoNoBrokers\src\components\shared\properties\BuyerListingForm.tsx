'use client';

import React, { useState, useCallback, useMemo } from 'react';
import Link from 'next/link';
import { Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import {
    Home,
    DollarSign,
    Bed
} from 'lucide-react';

interface FormData {
    title: string;
    description: string;
    price: number;
    propertyType: string;
    adType: string;
    bedrooms: string;
    bathrooms: string;
    sqft: string;
    storeys: string;
    yearBuilt: string;
    lotSize: string;
    lotSizeUnit: string;
    address: {
        street: string;
        city: string;
        province: string;
        postalCode: string;
        country: string;
    };
    parkingTypes: string[];
    extraFeatures: string[];
    mlsNumber: string;
    virtualTour: string;
    openHouse: {
        enabled: boolean;
        dates: string[];
    };
}

interface BuyerListingFormProps {
    onSubmit: (data: any) => Promise<void>;
    onCancel?: () => void;
}

export function BuyerListingForm({ onSubmit, onCancel }: BuyerListingFormProps) {
    const [formData, setFormData] = useState<FormData>({
        title: '',
        description: '',
        price: 0,
        propertyType: '',
        adType: 'sale',
        bedrooms: '',
        bathrooms: '',
        sqft: '',
        storeys: '',
        yearBuilt: '',
        lotSize: '',
        lotSizeUnit: 'sqft',
        address: {
            street: '',
            city: '',
            province: '',
            postalCode: '',
            country: 'Canada'
        },
        parkingTypes: [] as string[],
        extraFeatures: [] as string[],
        mlsNumber: '',
        virtualTour: '',
        openHouse: {
            enabled: false,
            dates: [] as string[]
        }
    });

    const [priceRange, setPriceRange] = useState([0]);
    const [images, setImages] = useState<File[]>([]);
    const [loading, setLoading] = useState(false);
    const [aiDescription, setAiDescription] = useState('');
    const [generatingAiDescription, setGeneratingAiDescription] = useState(false);

    const parkingOptions = [
        'Assigned Parking', 'Attached Garage', 'Carport', 'Covered Parking',
        'Detached Garage', 'Driveway', 'Indoor Parking', 'Other Parking',
        'Parking Lot', 'Parking Pad', 'Parking Stall', 'Street Parking',
        'Underground', 'Visitor Parking'
    ];

    const extraFeaturesOptions = [
        'Wheelchair Accessible', 'Hot Tub', 'Guest Suite', 'MLS® System',
        'Fireplace', 'Pool', 'Legal Suite', 'Wood Stove', 'Games Room',
        'Private Entrance', 'A/C', 'Home Theatre', 'Open House',
        'Jacuzzi', 'Walkout', 'Virtual Tour/Video'
    ];

    const handleInputChange = (field: string, value: any) => {
        setFormData(prev => {
            const updatedFormData = { ...prev };
            if (field.includes('.')) {
                const [parent, child] = field.split('.');
                (updatedFormData as any)[parent] = {
                    ...(updatedFormData as any)[parent],
                    [child]: value
                };
            } else {
                (updatedFormData as any)[field] = value;
            }
            return updatedFormData;
        });
    };

    const handleParkingChange = (parking: string, checked: boolean) => {
        if (checked) {
            setFormData(prev => ({
                ...prev,
                parkingTypes: [...prev.parkingTypes, parking]
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                parkingTypes: prev.parkingTypes.filter(p => p !== parking)
            }));
        }
    };

    const handleFeatureChange = (feature: string, checked: boolean) => {
        if (checked) {
            setFormData(prev => ({
                ...prev,
                extraFeatures: [...prev.extraFeatures, feature]
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                extraFeatures: prev.extraFeatures.filter(f => f !== feature)
            }));
        }
    };

    const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) {
            setImages(Array.from(e.target.files));
        }
    };

    const removeImage = (index: number) => {
        setImages(prev => prev.filter((_, i) => i !== index));
    };

    const handleGenerateAiDescription = useCallback(async () => {
        setGeneratingAiDescription(true);
        try {
            // Construct the prompt for OpenAI
            const prompt = `Write a compelling property description for a ${formData.propertyType} 
            with ${formData.bedrooms} bedrooms and ${formData.bathrooms} bathrooms, located in 
            ${formData.address.city}, ${formData.address.province}. The property is priced at 
            $${priceRange[0].toLocaleString()} and has ${formData.sqft} square feet. Key features include: 
            ${formData.extraFeatures.join(', ')}.`;

            // Call the OpenAI API via the new endpoint
            const response = await fetch('/api/generate-description', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt: prompt,
                    userId: 123, // Replace with actual user ID
                }),
            });

            if (response.ok) {
                const data = await response.json();
                const generatedDescription = data.description;

                setAiDescription(generatedDescription);
                setFormData(prev => ({ ...prev, description: generatedDescription }));
            } else {
                alert('Failed to generate description. Please try again.');
            }
        } catch (error) {
            console.error('Error generating AI description:', error);
            alert('Error generating AI description. Please try again.');
        } finally {
            setGeneratingAiDescription(false);
        }
    }, [formData, priceRange]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);

        try {
            const submissionData = {
                ...formData,
                price: priceRange[0],
                images: images
            };
            await onSubmit(submissionData);
        } catch (error) {
            console.error('Error submitting listing:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="min-h-screen bg-background p-4">
            {/* Header with consistent spacing */}
            <div className="flex h-12 w-full items-center justify-between px-4 py-2 border-b border-border bg-background/95 backdrop-blur mb-6">
                <div className="flex items-center gap-3">
                    <Home className="h-5 w-5 text-primary" />
                    <h1 className="text-xl font-bold">List Your Property</h1>
                    <Badge variant="outline" className="text-xs">
                        Buyer Listing
                    </Badge>
                </div>
                {onCancel && (
                    <Button variant="outline" onClick={onCancel} className="h-8 px-2">
                        Cancel
                    </Button>
                )}
            </div>

            <form onSubmit={handleSubmit} className="max-w-4xl mx-auto space-y-8">
                {/* Basic Information */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Home className="h-5 w-5" />
                            Basic Information
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="title">Property Title</Label>
                                <Input
                                    id="title"
                                    placeholder="Beautiful family home..."
                                    value={formData.title}
                                    onChange={(e) => handleInputChange('title', e.target.value)}
                                    className="h-8"
                                    required
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="propertyType">Property Type</Label>
                                <Select value={formData.propertyType} onValueChange={(value) => handleInputChange('propertyType', value)}>
                                    <SelectTrigger className="h-8">
                                        <SelectValue placeholder="Select type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="house">House</SelectItem>
                                        <SelectItem value="condo">Condo</SelectItem>
                                        <SelectItem value="townhouse">Townhouse</SelectItem>
                                        <SelectItem value="apartment">Apartment</SelectItem>
                                        <SelectItem value="land">Land/Acreage</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="description">Description</Label>
                            <Textarea
                                id="description"
                                placeholder="Describe your property..."
                                value={formData.description}
                                onChange={(e) => handleInputChange('description', e.target.value)}
                                className="min-h-[100px]"
                            />
                            <Button
                                type="button"
                                onClick={handleGenerateAiDescription}
                                disabled={generatingAiDescription}
                            >
                                {generatingAiDescription ? 'Generating...' : 'Generate Description'}
                            </Button>
                            <Button variant="outline" size="sm" className="h-8 px-1.5 bg-gradient-to-r from-orange-50 to-green-50 border-orange-200 hover:border-orange-400" asChild>
                                <Link href="/properties/new/ai" className="flex items-center gap-0.5 text-gray-600 font-medium">
                                    <Sparkles className="h-4 w-4 text-orange-500" />
                                    <span className="text-sm">AI Creator</span>
                                </Link>
                            </Button>
                        </div>

                        {/* Price */}
                        <div className="space-y-3">
                            <Label className="text-sm font-medium">
                                <DollarSign className="inline h-4 w-4 mr-1" />
                                Price: ${priceRange[0].toLocaleString()}
                            </Label>
                            <Slider
                                value={priceRange}
                                onValueChange={setPriceRange}
                                max={2000000}
                                min={50000}
                                step={5000}
                                className="w-full"
                            />
                        </div>
                    </CardContent>
                </Card>

                {/* Property Details */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Bed className="h-5 w-5" />
                            Property Details
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid md:grid-cols-4 gap-4">
                            <div className="space-y-2">
                                <Label>Bedrooms</Label>
                                <Select value={formData.bedrooms} onValueChange={(value) => handleInputChange('bedrooms', value)}>
                                    <SelectTrigger className="h-8">
                                        <SelectValue placeholder="Beds" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {[1, 2, 3, 4, 5, 6].map(num => (
                                            <SelectItem key={num} value={num.toString()}>{num}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <Label>Bathrooms</Label>
                                <Select value={formData.bathrooms} onValueChange={(value) => handleInputChange('bathrooms', value)}>
                                    <SelectTrigger className="h-8">
                                        <SelectValue placeholder="Baths" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {[1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5].map(num => (
                                            <SelectItem key={num} value={num.toString()}>{num}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <Label>Square Feet</Label>
                                <Input
                                    placeholder="1200"
                                    value={formData.sqft}
                                    onChange={(e) => handleInputChange('sqft', e.target.value)}
                                    className="h-8"
                                />
                            </div>
                            <div className="space-y-2">
                                <Label>Year Built</Label>
                                <Input
                                    placeholder="2020"
                                    value={formData.yearBuilt}
                                    onChange={(e) => handleInputChange('yearBuilt', e.target.value)}
                                    className="h-8"
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Submit Button */}
                <div className="flex justify-end gap-4">
                    {onCancel && (
                        <Button type="button" variant="outline" onClick={onCancel}>
                            Cancel
                        </Button>
                    )}
                    <Button type="submit" disabled={loading} className="px-8">
                        {loading ? 'Submitting...' : 'List Property'}
                    </Button>
                </div>
            </form>
        </div>
    );
}
