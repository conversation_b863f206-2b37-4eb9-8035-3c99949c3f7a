using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using System.Security.Claims;
using System.Text.Encodings.Web;
using System.Text.Json;

namespace MicroSaasWebApi.Tests.Common
{
    public static class TestHelpers
    {
        /// <summary>
        /// Creates a mock HTTP context with authenticated user
        /// </summary>
        public static HttpContext CreateMockHttpContext(string userId = "test-user-id", string email = "<EMAIL>")
        {
            var context = new DefaultHttpContext();

            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, userId),
                new(ClaimTypes.Email, email),
                new("sub", userId)
            };

            var identity = new ClaimsIdentity(claims, "Test");
            var principal = new ClaimsPrincipal(identity);

            context.User = principal;

            return context;
        }

        /// <summary>
        /// Creates a mock HTTP context for anonymous user
        /// </summary>
        public static HttpContext CreateAnonymousHttpContext()
        {
            var context = new DefaultHttpContext();
            context.User = new ClaimsPrincipal(new ClaimsIdentity());
            return context;
        }

        /// <summary>
        /// Converts object to JSON string for comparison
        /// </summary>
        public static string ToJson(object obj)
        {
            return JsonSerializer.Serialize(obj, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            });
        }

        /// <summary>
        /// Creates a mock logger for testing
        /// </summary>
        public static Mock<ILogger<T>> CreateMockLogger<T>()
        {
            return new Mock<ILogger<T>>();
        }

        /// <summary>
        /// Verifies that a logger was called with specific log level and message
        /// </summary>
        public static void VerifyLoggerCalled<T>(Mock<ILogger<T>> mockLogger, LogLevel logLevel, string message)
        {
            mockLogger.Verify(
                x => x.Log(
                    logLevel,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(message)),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        /// <summary>
        /// Creates test authentication scheme for integration tests
        /// </summary>
        public static void AddTestAuthentication(this IServiceCollection services)
        {
            services.AddAuthentication("Test")
                .AddScheme<AuthenticationSchemeOptions, TestAuthenticationHandler>("Test", options => { });
        }

        /// <summary>
        /// Generates a random test ID
        /// </summary>
        public static string GenerateTestId(string prefix = "test")
        {
            return $"{prefix}-{Guid.NewGuid():N}";
        }

        /// <summary>
        /// Creates a test database connection string
        /// </summary>
        public static string GetTestConnectionString(string databaseName = "sonobrokers_test")
        {
            return $"Host=localhost;Port=5432;Database={databaseName};Username=********;Password=********;";
        }

        /// <summary>
        /// Waits for a condition to be true with timeout
        /// </summary>
        public static async Task<bool> WaitForConditionAsync(Func<bool> condition, TimeSpan timeout)
        {
            var endTime = DateTime.UtcNow.Add(timeout);

            while (DateTime.UtcNow < endTime)
            {
                if (condition())
                    return true;

                await Task.Delay(100);
            }

            return false;
        }

        /// <summary>
        /// Creates test configuration for services
        /// </summary>
        public static Dictionary<string, string?> GetTestConfiguration()
        {
            return new Dictionary<string, string?>
            {
                ["ConnectionStrings:DefaultConnection"] = GetTestConnectionString(),
                ["ConnectionStrings:SupabaseConnection"] = GetTestConnectionString(),
                ["Clerk:SecretKey"] = "test-secret-key",
                ["Clerk:PublishableKey"] = "test-publishable-key",
                ["TestSettings:UseInMemoryDatabase"] = "true",
                ["TestSettings:SeedTestData"] = "true"
            };
        }

        /// <summary>
        /// Generates a random test email
        /// </summary>
        public static string GenerateTestEmail(string domain = "test.com")
        {
            return $"test-{Guid.NewGuid():N}@{domain}";
        }

        /// <summary>
        /// Generates a random test phone number
        /// </summary>
        public static string GenerateTestPhoneNumber()
        {
            var random = new Random();
            return $"+1-{random.Next(100, 999)}-{random.Next(100, 999)}-{random.Next(1000, 9999)}";
        }

        /// <summary>
        /// Generates a test Clerk user ID
        /// </summary>
        public static string GenerateClerkUserId()
        {
            return $"user_{GenerateRandomString(24)}";
        }

        /// <summary>
        /// Generates a random string of specified length
        /// </summary>
        public static string GenerateRandomString(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        /// <summary>
        /// Generates a test URL
        /// </summary>
        public static string GenerateTestUrl(string scheme = "https", string domain = "test.com")
        {
            return $"{scheme}://{domain}/{GenerateRandomString(10)}";
        }

        /// <summary>
        /// Creates a test date within a range
        /// </summary>
        public static DateTime GenerateTestDate(int daysBack = 30, int daysForward = 0)
        {
            var random = new Random();
            var start = DateTime.UtcNow.AddDays(-daysBack);
            var end = DateTime.UtcNow.AddDays(daysForward);
            var range = end - start;
            var randomTimeSpan = new TimeSpan((long)(random.NextDouble() * range.Ticks));
            return start + randomTimeSpan;
        }

        /// <summary>
        /// Generates a test price within a range
        /// </summary>
        public static decimal GenerateTestPrice(decimal min = 100000, decimal max = 2000000)
        {
            var random = new Random();
            return min + (decimal)random.NextDouble() * (max - min);
        }

        /// <summary>
        /// Creates test claims for authentication
        /// </summary>
        public static List<Claim> CreateTestClaims(string userId, string email, string role = "USER")
        {
            return new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, userId),
                new(ClaimTypes.Email, email),
                new(ClaimTypes.Role, role),
                new("sub", userId),
                new("email_verified", "true")
            };
        }

        /// <summary>
        /// Measures execution time of an action
        /// </summary>
        public static async Task<(T result, TimeSpan duration)> MeasureAsync<T>(Func<Task<T>> action)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = await action();
            stopwatch.Stop();
            return (result, stopwatch.Elapsed);
        }

        /// <summary>
        /// Retries an action until it succeeds or timeout
        /// </summary>
        public static async Task<T> RetryAsync<T>(Func<Task<T>> action, int maxAttempts = 3, TimeSpan? delay = null)
        {
            delay ??= TimeSpan.FromSeconds(1);
            Exception? lastException = null;

            for (int attempt = 1; attempt <= maxAttempts; attempt++)
            {
                try
                {
                    return await action();
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    if (attempt < maxAttempts)
                    {
                        await Task.Delay(delay.Value);
                    }
                }
            }

            throw new Exception($"Action failed after {maxAttempts} attempts", lastException);
        }
    }

    /// <summary>
    /// Test authentication handler for integration tests
    /// </summary>
    public class TestAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
    {
        public TestAuthenticationHandler(IOptionsMonitor<AuthenticationSchemeOptions> options,
            ILoggerFactory logger, UrlEncoder encoder, ISystemClock clock)
            : base(options, logger, encoder, clock)
        {
        }

        protected override Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, "test-user-id"),
                new Claim(ClaimTypes.Email, "<EMAIL>"),
                new Claim("sub", "test-user-id")
            };

            var identity = new ClaimsIdentity(claims, "Test");
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, "Test");

            return Task.FromResult(AuthenticateResult.Success(ticket));
        }
    }

    /// <summary>
    /// Custom assertions for testing
    /// </summary>
    public static class TestAssertions
    {
        public static void ShouldBeValidGuid(this string value, string? because = null)
        {
            if (!Guid.TryParse(value, out _))
            {
                throw new Xunit.Sdk.XunitException($"Expected '{value}' to be a valid GUID{(because != null ? $" because {because}" : "")}");
            }
        }

        public static void ShouldBeValidEmail(this string value, string? because = null)
        {
            if (!IsValidEmail(value))
            {
                throw new Xunit.Sdk.XunitException($"Expected '{value}' to be a valid email{(because != null ? $" because {because}" : "")}");
            }
        }

        public static void ShouldBeValidUrl(this string value, string? because = null)
        {
            if (!Uri.TryCreate(value, UriKind.Absolute, out _))
            {
                throw new Xunit.Sdk.XunitException($"Expected '{value}' to be a valid URL{(because != null ? $" because {because}" : "")}");
            }
        }

        private static bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}
