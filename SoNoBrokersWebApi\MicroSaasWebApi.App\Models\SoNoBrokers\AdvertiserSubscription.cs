using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    public enum SubscriptionStatus
    {
        active,
        inactive,
        cancelled,
        past_due,
        unpaid
    }

    [Table("AdvertiserSubscription", Schema = "public")]
    public class AdvertiserSubscription
    {
        [Key]
        [Column("id")]
        public string Id { get; set; } = string.Empty;

        [Required]
        [Column("advertiserId")]
        public string AdvertiserId { get; set; } = string.Empty;

        [Column("stripeSubscriptionId")]
        public string? StripeSubscriptionId { get; set; }

        [Column("stripePriceId")]
        public string? StripePriceId { get; set; }

        [Required]
        [Column("plan")]
        public AdvertiserPlan Plan { get; set; }

        [Column("status")]
        public SubscriptionStatus Status { get; set; } = SubscriptionStatus.active;

        [Column("currentPeriodStart")]
        public DateTime? CurrentPeriodStart { get; set; }

        [Column("currentPeriodEnd")]
        public DateTime? CurrentPeriodEnd { get; set; }

        [Column("cancelAtPeriodEnd")]
        public bool CancelAtPeriodEnd { get; set; } = false;

        [Column("canceledAt")]
        public DateTime? CanceledAt { get; set; }

        [Column("trialStart")]
        public DateTime? TrialStart { get; set; }

        [Column("trialEnd")]
        public DateTime? TrialEnd { get; set; }

        [Column("metadata")]
        public JsonDocument? Metadata { get; set; }

        [Column("createdAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("updatedAt")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public Advertiser? Advertiser { get; set; }
    }
}
