# SoNoBrokers Feature-API Mapping Guide

## Overview

This document provides a detailed mapping of every React feature in SoNoBrokers to its corresponding .NET Core Web API endpoints, including request/response formats and business logic.

## 🏠 Property Features

### Property Listing & Search

| React Feature | Component File | API Endpoint | HTTP Method | Description |
|---------------|----------------|--------------|-------------|-------------|
| Property Search | `src/app/properties/page.tsx` | `/api/sonobrokers/properties/search` | GET | Search properties with filters |
| Property List | `src/components/shared/properties/PropertyList.tsx` | `/api/sonobrokers/properties` | GET | Get paginated property list |
| Property Details | `src/app/properties/[id]/page.tsx` | `/api/sonobrokers/properties/{id}` | GET | Get single property details |
| Property Images | `src/components/shared/properties/PropertyGallery.tsx` | `/api/sonobrokers/properties/{id}/images` | GET | Get property images |
| Property Analytics | `src/app/dashboard/properties/analytics.tsx` | `/api/sonobrokers/properties/{id}/analytics` | GET | Get property view statistics |

### Property Management

| React Feature | Component File | API Endpoint | HTTP Method | Description |
|---------------|----------------|--------------|-------------|-------------|
| Create Property | `src/app/dashboard/properties/create/page.tsx` | `/api/sonobrokers/properties` | POST | Create new property listing |
| Edit Property | `src/app/dashboard/properties/[id]/edit/page.tsx` | `/api/sonobrokers/properties/{id}` | PUT | Update property details |
| Delete Property | `src/components/properties/PropertyActions.tsx` | `/api/sonobrokers/properties/{id}` | DELETE | Delete property listing |
| Upload Images | `src/components/properties/ImageUpload.tsx` | `/api/sonobrokers/properties/{id}/images` | POST | Upload property images |
| Property Status | `src/components/properties/PropertyStatus.tsx` | `/api/sonobrokers/properties/{id}/status` | PATCH | Update property status |

## 💬 Contact Sharing Features

### Contact Sharing Components

| React Feature | Component File | API Endpoint | HTTP Method | Description |
|---------------|----------------|--------------|-------------|-------------|
| Contact Share Button | `src/components/contact-sharing/ContactShareButton.tsx` | `/api/sonobrokers/contact-sharing` | POST | Share buyer contact with seller |
| Contact Share Modal | `src/components/contact-sharing/ContactShareModal.tsx` | `/api/sonobrokers/contact-sharing` | POST | Modal for contact sharing forms |
| Quick Actions | `src/components/contact-sharing/ContactShareButton.tsx` | `/api/sonobrokers/contact-sharing` | POST | Quick contact/offer/visit buttons |
| Property Actions | `src/components/contact-sharing/PropertyActionButtons.tsx` | `/api/sonobrokers/contact-sharing` | POST | Property-specific action buttons |

### Contact Share Types

| Share Type | React Component | API Payload | Email Template |
|------------|-----------------|-------------|----------------|
| Contact Request | ContactForm | `{ shareType: 1, message, buyerInfo }` | Contact request email |
| Property Offer | OfferForm | `{ shareType: 2, offerAmount, message }` | Property offer email |
| Schedule Visit | VisitForm | `{ shareType: 3, visitDate, visitTime }` | Visit request email |
| Offer + Visit | OfferWithVisitForm | `{ shareType: 4, offerAmount, visitDate }` | Combined offer/visit email |

### Contact Share Management

| React Feature | Component File | API Endpoint | HTTP Method | Description |
|---------------|----------------|--------------|-------------|-------------|
| Contact Shares Dashboard | `src/app/dashboard/contact-shares/page.tsx` | `/api/sonobrokers/contact-sharing` | GET | View all contact shares |
| Contact Share Details | `src/components/contact-sharing/ContactShareDetails.tsx` | `/api/sonobrokers/contact-sharing/{id}` | GET | View single contact share |
| Seller Response | `src/components/contact-sharing/SellerResponse.tsx` | `/api/sonobrokers/contact-sharing/{id}/respond` | PUT | Seller responds to contact |
| Contact Share Stats | `src/components/contact-sharing/ContactShareStats.tsx` | `/api/sonobrokers/contact-sharing/stats` | GET | Get contact share statistics |
| Delete Contact Share | `src/components/contact-sharing/ContactShareActions.tsx` | `/api/sonobrokers/contact-sharing/{id}` | DELETE | Delete contact share |

## 📅 Property Scheduling Features

### Seller Availability Management

| React Feature | Component File | API Endpoint | HTTP Method | Description |
|---------------|----------------|--------------|-------------|-------------|
| Availability Manager | `src/components/property-scheduling/SellerAvailabilityManager.tsx` | `/api/sonobrokers/property-scheduling/availability` | POST | Create seller availability |
| View Availability | `src/components/property-scheduling/SellerAvailabilityManager.tsx` | `/api/sonobrokers/property-scheduling/availability` | GET | Get seller availability |
| Update Availability | `src/components/property-scheduling/SellerAvailabilityManager.tsx` | `/api/sonobrokers/property-scheduling/availability/{id}` | PUT | Update availability slot |
| Delete Availability | `src/components/property-scheduling/SellerAvailabilityManager.tsx` | `/api/sonobrokers/property-scheduling/availability/{id}` | DELETE | Delete availability slot |
| Property Availability | `src/components/contact-sharing/CalendarScheduling.tsx` | `/api/sonobrokers/property-scheduling/property/{id}/availability` | GET | Get property availability |

### Visit Scheduling

| React Feature | Component File | API Endpoint | HTTP Method | Description |
|---------------|----------------|--------------|-------------|-------------|
| Calendar Scheduling | `src/components/contact-sharing/CalendarScheduling.tsx` | `/api/sonobrokers/property-scheduling/visits` | POST | Create visit schedule |
| Visit Details | `src/components/property-scheduling/VisitDetails.tsx` | `/api/sonobrokers/property-scheduling/visits/{id}` | GET | Get visit details |
| Respond to Visit | `src/components/property-scheduling/VisitResponse.tsx` | `/api/sonobrokers/property-scheduling/visits/{id}/respond` | PUT | Seller responds to visit |
| Cancel Visit | `src/components/property-scheduling/VisitActions.tsx` | `/api/sonobrokers/property-scheduling/visits/{id}/cancel` | PUT | Cancel visit schedule |
| Visit Statistics | `src/components/property-scheduling/VisitStats.tsx` | `/api/sonobrokers/property-scheduling/stats` | GET | Get visit statistics |

### QR Code Management

| React Feature | Component File | API Endpoint | HTTP Method | Description |
|---------------|----------------|--------------|-------------|-------------|
| QR Code Manager | `src/components/property-scheduling/PropertyQrCodeManager.tsx` | `/api/sonobrokers/property-scheduling/property/{id}/qr-code` | POST | Generate property QR code |
| View QR Code | `src/components/property-scheduling/PropertyQrCodeManager.tsx` | `/api/sonobrokers/property-scheduling/property/{id}/qr-code` | GET | Get property QR code |
| Regenerate QR Code | `src/components/property-scheduling/PropertyQrCodeManager.tsx` | `/api/sonobrokers/property-scheduling/property/{id}/qr-code` | PUT | Regenerate QR code |
| Visit Verification | `src/components/property-scheduling/VisitVerificationScanner.tsx` | `/api/sonobrokers/property-scheduling/visits/{id}/verify` | POST | Verify visit with QR code |

## 👤 User Management Features

### Authentication & Profile

| React Feature | Component File | API Endpoint | HTTP Method | Description |
|---------------|----------------|--------------|-------------|-------------|
| User Profile | `src/app/dashboard/profile/page.tsx` | `/api/sonobrokers/users/profile` | GET | Get user profile |
| Update Profile | `src/components/user/ProfileForm.tsx` | `/api/sonobrokers/users/profile` | PUT | Update user profile |
| User Preferences | `src/components/user/UserPreferences.tsx` | `/api/sonobrokers/users/preferences` | PUT | Update user preferences |
| User Statistics | `src/components/user/UserStats.tsx` | `/api/sonobrokers/users/stats` | GET | Get user statistics |

### Dashboard Features

| React Feature | Component File | API Endpoint | HTTP Method | Description |
|---------------|----------------|--------------|-------------|-------------|
| Dashboard Overview | `src/app/dashboard/page.tsx` | `/api/sonobrokers/dashboard/overview` | GET | Get dashboard data |
| Property Dashboard | `src/app/dashboard/properties/page.tsx` | `/api/sonobrokers/properties/user` | GET | Get user properties |
| Contact Dashboard | `src/app/dashboard/contact-shares/page.tsx` | `/api/sonobrokers/contact-sharing` | GET | Get user contact shares |
| Analytics Dashboard | `src/app/dashboard/analytics/page.tsx` | `/api/sonobrokers/analytics/user` | GET | Get user analytics |

## 🔧 Admin Features

### Admin Dashboard

| React Feature | Component File | API Endpoint | HTTP Method | Description |
|---------------|----------------|--------------|-------------|-------------|
| Admin Overview | `src/app/admin/page.tsx` | `/api/admin/dashboard` | GET | Get admin dashboard |
| User Management | `src/app/admin/users/page.tsx` | `/api/admin/users` | GET | Manage users |
| Property Management | `src/app/admin/properties/page.tsx` | `/api/admin/properties` | GET | Manage properties |
| Contact Share Admin | `src/app/admin/contact-shares/page.tsx` | `/api/admin/contact-sharing` | GET | Manage contact shares |
| System Statistics | `src/app/admin/stats/page.tsx` | `/api/admin/stats` | GET | Get system statistics |

## 📧 Email & Notifications

### Email Templates

| Feature | Trigger | API Endpoint | Template |
|---------|---------|--------------|----------|
| Contact Request Email | Contact share created | `/api/sonobrokers/contact-sharing` | Contact request template |
| Property Offer Email | Offer submitted | `/api/sonobrokers/contact-sharing` | Property offer template |
| Visit Request Email | Visit scheduled | `/api/sonobrokers/property-scheduling/visits` | Visit request template |
| Seller Response Email | Seller responds | `/api/sonobrokers/contact-sharing/{id}/respond` | Seller response template |
| Visit Confirmation | Visit confirmed | `/api/sonobrokers/property-scheduling/visits/{id}/respond` | Visit confirmation template |

## 🔍 Search & Filtering

### Property Search

| React Feature | Component File | API Endpoint | Query Parameters |
|---------------|----------------|--------------|------------------|
| Search Bar | `src/components/search/PropertySearch.tsx` | `/api/sonobrokers/properties/search` | `q, location, priceMin, priceMax` |
| Filter Panel | `src/components/search/PropertyFilters.tsx` | `/api/sonobrokers/properties` | `bedrooms, bathrooms, propertyType` |
| Sort Options | `src/components/search/PropertySort.tsx` | `/api/sonobrokers/properties` | `sortBy, sortOrder` |
| Map View | `src/components/search/PropertyMap.tsx` | `/api/sonobrokers/properties/map` | `bounds, zoom` |

## 📊 Analytics & Reporting

### Property Analytics

| React Feature | Component File | API Endpoint | Description |
|---------------|----------------|--------------|-------------|
| Property Views | `src/components/analytics/PropertyViews.tsx` | `/api/sonobrokers/properties/{id}/analytics` | Track property views |
| Contact Analytics | `src/components/analytics/ContactAnalytics.tsx` | `/api/sonobrokers/contact-sharing/property/{id}/stats` | Contact share statistics |
| Visit Analytics | `src/components/analytics/VisitAnalytics.tsx` | `/api/sonobrokers/property-scheduling/property/{id}/stats` | Visit statistics |
| Performance Metrics | `src/components/analytics/PerformanceMetrics.tsx` | `/api/sonobrokers/analytics/performance` | Overall performance |

## 🔐 Security Features

### Access Control

| Feature | Implementation | API Validation |
|---------|----------------|----------------|
| Authentication | Clerk JWT tokens | JWT validation middleware |
| Authorization | Role-based access | User role validation |
| Property Access | Owner/seller validation | Property ownership check |
| Contact Share Access | Buyer/seller validation | Contact share access check |
| Admin Access | Admin role required | Admin role validation |

## 📱 Mobile Responsiveness

### Mobile Components

| Feature | Desktop Component | Mobile Adaptation |
|---------|------------------|-------------------|
| Property Cards | Grid layout | Single column |
| Contact Modal | Full modal | Bottom sheet |
| Navigation | Horizontal menu | Hamburger menu |
| Search Filters | Sidebar | Collapsible panel |
| Dashboard | Multi-column | Stacked layout |

## 🚀 Performance Features

### Optimization Strategies

| Feature | React Implementation | API Optimization |
|---------|---------------------|------------------|
| Lazy Loading | React.lazy() | Pagination |
| Caching | React Query | Response caching |
| Image Optimization | Next.js Image | CDN delivery |
| Code Splitting | Dynamic imports | Minimal payloads |
| Server Components | RSC | Reduced client JS |

## 🧪 Testing Coverage

### Test Types

| Feature Area | React Tests | API Tests |
|--------------|-------------|-----------|
| Contact Sharing | Component tests | Integration tests |
| Property Scheduling | User interaction tests | Service tests |
| Authentication | Auth flow tests | JWT validation tests |
| Property Management | CRUD tests | Database tests |
| Search & Filtering | Search tests | Query performance tests |

This comprehensive mapping ensures that every React feature has a corresponding API endpoint and proper integration between the frontend and backend systems.
