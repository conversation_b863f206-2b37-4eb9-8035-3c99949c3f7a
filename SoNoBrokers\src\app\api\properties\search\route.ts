import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// Define the expected structure of a property for the frontend
interface FormattedProperty {
  id: string;
  title: string;
  description: string;
  price: number;
  bedrooms: number;
  bathrooms: number;
  square_footage: number;
  property_type: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  features: string[];
  images: { url: string; storagePath: string }[];
  seller: {
    id: string;
    full_name: string;
    avatar_url: string | null;
    role: string;
  };
  coordinates?: { lat: number; lng: number };
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);

    // Enhanced search parameters
    const location = searchParams.get('location');
    const mlsNumber = searchParams.get('mlsNumber');
    const propertyType = searchParams.get('propertyType');
    const adType = searchParams.get('adType');
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    const minYear = searchParams.get('minYear');
    const maxYear = searchParams.get('maxYear');
    const bedrooms = searchParams.get('bedrooms');
    const bathrooms = searchParams.get('bathrooms');
    const storeys = searchParams.get('storeys');
    const parkingTypes = searchParams.get('parkingTypes');
    const extraFeatures = searchParams.get('extraFeatures');

    // Enhanced mock data with MLS and non-MLS listings
    const mockProperties = [
      // MLS Listings
      {
        id: 'mls-001',
        title: 'Stunning Executive Home in Rosedale',
        description: 'Magnificent 4-bedroom, 3.5-bathroom executive home in prestigious Rosedale. Features include hardwood floors, granite countertops, stainless steel appliances, and a private backyard.',
        price: 2850000,
        bedrooms: 4,
        bathrooms: 3.5,
        sqft: 3200,
        propertyType: 'house',
        address: '45 Elm Avenue, Toronto, ON M4W 1N4',
        coordinates: { lat: 43.6782, lng: -79.3911 },
        status: 'active',
        mlsNumber: 'C5234567',
        isMLS: true,
        yearBuilt: 2018,
        lotSize: 0.25,
        parkingSpaces: 2,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        images: [
          { url: 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800&h=600&fit=crop', alt: 'Front exterior view' },
          { url: 'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?w=800&h=600&fit=crop', alt: 'Living room' },
          { url: 'https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?w=800&h=600&fit=crop', alt: 'Kitchen' }
        ],
        seller: { id: 'mls-agent-1', name: 'Sarah Wilson', company: 'Royal LePage' }
      },
      {
        id: 'mls-002',
        title: 'Luxury Condo with CN Tower Views',
        description: 'Spectacular 2-bedroom, 2-bathroom condo on the 35th floor with breathtaking CN Tower and lake views. Premium finishes throughout.',
        price: 1250000,
        bedrooms: 2,
        bathrooms: 2,
        sqft: 1100,
        propertyType: 'condo',
        address: '88 Harbour Street, Toronto, ON M5J 2G2',
        coordinates: { lat: 43.6426, lng: -79.3871 },
        status: 'active',
        mlsNumber: 'C5234568',
        isMLS: true,
        yearBuilt: 2020,
        parkingSpaces: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        images: [
          { url: 'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop', alt: 'Condo exterior' },
          { url: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop', alt: 'Living area with view' },
          { url: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop', alt: 'Modern kitchen' }
        ],
        seller: { id: 'mls-agent-2', name: 'Michael Chen', company: 'RE/MAX' }
      },
      {
        id: 'mls-003',
        title: 'Prime Development Land - Oakville',
        description: '5-acre development opportunity in growing Oakville area. Zoned for residential development. Perfect for builders and investors.',
        price: 3500000,
        bedrooms: 0,
        bathrooms: 0,
        sqft: 0,
        propertyType: 'land',
        address: 'Dundas Street West, Oakville, ON L6M 4J7',
        coordinates: { lat: 43.4675, lng: -79.6877 },
        status: 'active',
        mlsNumber: 'W5234569',
        isMLS: true,
        yearBuilt: null as number | null,
        lotSize: 5.0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        images: [
          { url: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?w=800&h=600&fit=crop', alt: 'Land overview' },
          { url: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop', alt: 'Natural landscape' }
        ],
        seller: { id: 'mls-agent-3', name: 'Jennifer Park', company: 'Century 21' }
      },
      // Non-MLS Private Listings
      {
        id: 'private-001',
        title: 'Charming Victorian Home - Private Sale',
        description: 'Beautiful 3-bedroom Victorian home with original character features. Hardwood floors, high ceilings, and a lovely garden. Selling privately to save on commission.',
        price: 875000,
        bedrooms: 3,
        bathrooms: 2,
        sqft: 1850,
        propertyType: 'house',
        address: '67 Queen Street East, Toronto, ON M5C 1R6',
        coordinates: { lat: 43.6532, lng: -79.3832 },
        status: 'active',
        mlsNumber: null,
        isMLS: false,
        yearBuilt: 1925,
        lotSize: 0.15,
        parkingSpaces: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        images: [
          { url: 'https://images.unsplash.com/photo-1518780664697-55e3ad937233?w=800&h=600&fit=crop', alt: 'Victorian house exterior' },
          { url: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop', alt: 'Traditional living room' },
          { url: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop', alt: 'Updated kitchen' }
        ],
        seller: { id: 'private-seller-1', name: 'David Thompson', company: 'Private Owner' }
      },
      {
        id: 'private-002',
        title: 'Modern Townhouse - No Agent Fees',
        description: 'Contemporary 3-bedroom townhouse in family-friendly neighborhood. Open concept living, finished basement, and private patio. Direct from owner.',
        price: 650000,
        bedrooms: 3,
        bathrooms: 2.5,
        sqft: 1600,
        propertyType: 'townhouse',
        address: '123 Maple Grove, Mississauga, ON L5B 3Y4',
        coordinates: { lat: 43.5890, lng: -79.6441 },
        status: 'active',
        mlsNumber: null,
        isMLS: false,
        yearBuilt: 2015,
        lotSize: 0.08,
        parkingSpaces: 2,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        images: [
          { url: 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=800&h=600&fit=crop', alt: 'Modern townhouse' },
          { url: 'https://images.unsplash.com/photo-1600607687644-aac4c3eac7f4?w=800&h=600&fit=crop', alt: 'Open concept living' },
          { url: 'https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?w=800&h=600&fit=crop', alt: 'Modern kitchen' }
        ],
        seller: { id: 'private-seller-2', name: 'Lisa Rodriguez', company: 'Private Owner' }
      },
      {
        id: 'private-003',
        title: 'Cozy Starter Condo - Owner Direct',
        description: 'Perfect 1-bedroom condo for first-time buyers or investors. Recently renovated, great location near transit. Selling direct to avoid realtor fees.',
        price: 425000,
        bedrooms: 1,
        bathrooms: 1,
        sqft: 650,
        propertyType: 'condo',
        address: '789 King Street West, Toronto, ON M5V 1N1',
        coordinates: { lat: 43.6441, lng: -79.4006 },
        status: 'active',
        mlsNumber: null,
        isMLS: false,
        yearBuilt: 2010,
        parkingSpaces: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        images: [
          { url: 'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800&h=600&fit=crop', alt: 'Condo building' },
          { url: 'https://images.unsplash.com/photo-1560448204-603b3fc33ddc?w=800&h=600&fit=crop', alt: 'Cozy living space' },
          { url: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop', alt: 'Compact kitchen' }
        ],
        seller: { id: 'private-seller-3', name: 'Alex Kim', company: 'Private Owner' }
      }
    ];

    // Apply basic filtering
    let filteredProperties = mockProperties;

    if (propertyType) {
      filteredProperties = filteredProperties.filter(p => p.propertyType === propertyType);
    }

    if (minPrice) {
      filteredProperties = filteredProperties.filter(p => p.price >= parseInt(minPrice));
    }

    if (maxPrice) {
      filteredProperties = filteredProperties.filter(p => p.price <= parseInt(maxPrice));
    }

    if (bedrooms) {
      filteredProperties = filteredProperties.filter(p => p.bedrooms >= parseInt(bedrooms));
    }

    if (bathrooms) {
      filteredProperties = filteredProperties.filter(p => p.bathrooms >= parseInt(bathrooms));
    }

    if (location) {
      filteredProperties = filteredProperties.filter(p =>
        p.address.toLowerCase().includes(location.toLowerCase())
      );
    }

    return NextResponse.json(filteredProperties);

  } catch (error) {
    console.error('Error in property search:', error);
    return NextResponse.json(
      { message: 'Error searching properties', error: error.message },
      { status: 500 }
    );
  }
}
