import { StagingService } from './StagingService'
// Removed auth import - no authentication required for browsing staging services
import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function StagingPage({ params, searchParams }: PageProps) {
  // No authentication required for browsing staging services
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/services/staging')
  }

  // Default to seller for staging services
  const userType = resolvedSearchParams.userType || 'seller'

  return (
    <StagingService
      userType={userType}
      isSignedIn={isSignedIn}
      country={country.toUpperCase()}
    />
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'seller'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Home Staging Services for ${userType === 'buyer' ? 'Buyers' : 'Sellers'} in ${country} | SoNoBrokers`,
    description: `Professional home staging services in ${country}. Certified stagers help sell your property faster and for more money. Compare staging professionals and book consultations.`,
    keywords: `home staging, property staging, interior staging, ${userType}, ${country}, staging services`,
  }
}
