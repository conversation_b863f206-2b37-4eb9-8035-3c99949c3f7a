import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { useUser } from '@clerk/nextjs'
import { ContactShareButton, PropertyQuickActions, CompactContactShareButton } from '../ContactShareButton'

// Mock Clerk
jest.mock('@clerk/nextjs', () => ({
  useUser: jest.fn(),
}))

// Mock ContactShareModal
jest.mock('../ContactShareModal', () => ({
  ContactShareModal: ({ isOpen, onClose, propertyTitle }: any) => (
    isOpen ? (
      <div data-testid="contact-share-modal">
        <h2>Contact Share Modal</h2>
        <p>Property: {propertyTitle}</p>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null
  ),
}))

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>

describe('ContactShareButton', () => {
  const defaultProps = {
    propertyId: 'prop-123',
    sellerId: 'seller-123',
    sellerName: '<PERSON>',
    propertyTitle: 'Beautiful Family Home',
    propertyPrice: 500000,
    propertyAddress: '123 Main St, City, State',
  }

  beforeEach(() => {
    jest.clearAllMocks()
    // Mock window.location.href
    delete (window as any).location
    window.location = { href: '' } as any
  })

  it('renders contact share button with text', () => {
    mockUseUser.mockReturnValue({
      user: { id: 'user-123', fullName: 'Test User' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<ContactShareButton {...defaultProps} />)

    expect(screen.getByText('Share Contact')).toBeInTheDocument()
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  it('redirects to sign in when user is not signed in', async () => {
    const user = userEvent.setup()
    mockUseUser.mockReturnValue({
      user: null,
      isSignedIn: false,
      isLoaded: true,
    })

    render(<ContactShareButton {...defaultProps} />)

    await user.click(screen.getByText('Share Contact'))

    expect(window.location.href).toBe('/sign-in')
  })

  it('opens modal when user is signed in', async () => {
    const user = userEvent.setup()
    mockUseUser.mockReturnValue({
      user: { id: 'user-123', fullName: 'Test User' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<ContactShareButton {...defaultProps} />)

    await user.click(screen.getByText('Share Contact'))

    expect(screen.getByTestId('contact-share-modal')).toBeInTheDocument()
    expect(screen.getByText('Property: Beautiful Family Home')).toBeInTheDocument()
  })

  it('closes modal when close button is clicked', async () => {
    const user = userEvent.setup()
    mockUseUser.mockReturnValue({
      user: { id: 'user-123', fullName: 'Test User' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<ContactShareButton {...defaultProps} />)

    // Open modal
    await user.click(screen.getByText('Share Contact'))
    expect(screen.getByTestId('contact-share-modal')).toBeInTheDocument()

    // Close modal
    await user.click(screen.getByText('Close'))
    
    await waitFor(() => {
      expect(screen.queryByTestId('contact-share-modal')).not.toBeInTheDocument()
    })
  })

  it('renders with custom variant and size', () => {
    mockUseUser.mockReturnValue({
      user: { id: 'user-123', fullName: 'Test User' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(
      <ContactShareButton 
        {...defaultProps} 
        variant="default" 
        size="lg" 
        className="custom-class"
      />
    )

    const button = screen.getByRole('button')
    expect(button).toHaveClass('custom-class')
  })

  it('renders without text when showText is false', () => {
    mockUseUser.mockReturnValue({
      user: { id: 'user-123', fullName: 'Test User' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<ContactShareButton {...defaultProps} showText={false} />)

    expect(screen.queryByText('Share Contact')).not.toBeInTheDocument()
    expect(screen.getByRole('button')).toBeInTheDocument()
  })
})

describe('PropertyQuickActions', () => {
  const defaultProps = {
    propertyId: 'prop-123',
    sellerId: 'seller-123',
    sellerName: 'John Seller',
    propertyTitle: 'Beautiful Family Home',
    propertyPrice: 500000,
    propertyAddress: '123 Main St, City, State',
  }

  beforeEach(() => {
    jest.clearAllMocks()
    delete (window as any).location
    window.location = { href: '' } as any
  })

  it('renders all quick action buttons', () => {
    mockUseUser.mockReturnValue({
      user: { id: 'user-123', fullName: 'Test User' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<PropertyQuickActions {...defaultProps} />)

    // Check for icon buttons (they don't have visible text)
    const buttons = screen.getAllByRole('button')
    expect(buttons).toHaveLength(3) // Mail, DollarSign, Calendar icons
  })

  it('redirects to sign in when user is not signed in', async () => {
    const user = userEvent.setup()
    mockUseUser.mockReturnValue({
      user: null,
      isSignedIn: false,
      isLoaded: true,
    })

    render(<PropertyQuickActions {...defaultProps} />)

    const buttons = screen.getAllByRole('button')
    await user.click(buttons[0]) // Click first button

    expect(window.location.href).toBe('/sign-in')
  })

  it('opens contact modal when contact button is clicked', async () => {
    const user = userEvent.setup()
    mockUseUser.mockReturnValue({
      user: { id: 'user-123', fullName: 'Test User' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<PropertyQuickActions {...defaultProps} />)

    const buttons = screen.getAllByRole('button')
    await user.click(buttons[0]) // Click contact button (Mail icon)

    expect(screen.getByTestId('contact-share-modal')).toBeInTheDocument()
  })

  it('opens offer modal when offer button is clicked', async () => {
    const user = userEvent.setup()
    mockUseUser.mockReturnValue({
      user: { id: 'user-123', fullName: 'Test User' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<PropertyQuickActions {...defaultProps} />)

    const buttons = screen.getAllByRole('button')
    await user.click(buttons[1]) // Click offer button (DollarSign icon)

    expect(screen.getByTestId('contact-share-modal')).toBeInTheDocument()
  })

  it('opens visit modal when visit button is clicked', async () => {
    const user = userEvent.setup()
    mockUseUser.mockReturnValue({
      user: { id: 'user-123', fullName: 'Test User' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<PropertyQuickActions {...defaultProps} />)

    const buttons = screen.getAllByRole('button')
    await user.click(buttons[2]) // Click visit button (Calendar icon)

    expect(screen.getByTestId('contact-share-modal')).toBeInTheDocument()
  })
})

describe('CompactContactShareButton', () => {
  const defaultProps = {
    propertyId: 'prop-123',
    sellerId: 'seller-123',
    sellerName: 'John Seller',
    propertyTitle: 'Beautiful Family Home',
    propertyPrice: 500000,
    propertyAddress: '123 Main St, City, State',
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders compact button without text', () => {
    mockUseUser.mockReturnValue({
      user: { id: 'user-123', fullName: 'Test User' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<CompactContactShareButton {...defaultProps} />)

    expect(screen.queryByText('Share Contact')).not.toBeInTheDocument()
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  it('opens modal when clicked', async () => {
    const user = userEvent.setup()
    mockUseUser.mockReturnValue({
      user: { id: 'user-123', fullName: 'Test User' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<CompactContactShareButton {...defaultProps} />)

    await user.click(screen.getByRole('button'))

    expect(screen.getByTestId('contact-share-modal')).toBeInTheDocument()
  })
})

describe('Button Accessibility', () => {
  const defaultProps = {
    propertyId: 'prop-123',
    sellerId: 'seller-123',
    sellerName: 'John Seller',
    propertyTitle: 'Beautiful Family Home',
    propertyPrice: 500000,
    propertyAddress: '123 Main St, City, State',
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('has proper accessibility attributes', () => {
    mockUseUser.mockReturnValue({
      user: { id: 'user-123', fullName: 'Test User' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<ContactShareButton {...defaultProps} />)

    const button = screen.getByRole('button')
    expect(button).toBeInTheDocument()
    expect(button).not.toHaveAttribute('disabled')
  })

  it('quick action buttons have proper titles', () => {
    mockUseUser.mockReturnValue({
      user: { id: 'user-123', fullName: 'Test User' },
      isSignedIn: true,
      isLoaded: true,
    })

    render(<PropertyQuickActions {...defaultProps} />)

    const buttons = screen.getAllByRole('button')
    
    // Check that buttons have title attributes for accessibility
    expect(buttons[0]).toHaveAttribute('title', 'Share your contact information with seller')
    expect(buttons[1]).toHaveAttribute('title', 'Submit an offer for this property')
    expect(buttons[2]).toHaveAttribute('title', 'Request to schedule a property visit')
  })
})
