import { notFound } from 'next/navigation';
import { getCurrentUserProfile } from '@/lib/api/auth-api';
import { getPropertyById } from '@/lib/api/properties-api';
import Image from 'next/image';
import { PropertyImageUpload } from '@/components/shared/properties/PropertyImageUpload';
import { OpenHouse } from '@/components/shared/properties/OpenHouse';
import { PropertyContactForm } from '@/components/messaging/PropertyContactForm';
import { PropertyQuickActions, FullContactShareButton } from '@/components/contact-sharing/ContactShareButton';
import { PropertyOfferButton, PropertyVisitButton } from '@/components/contact-sharing/PropertyActionButtons';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { formatCurrency } from '@/lib/utils';
import { MessageSquare, Edit, Home, User, MapPin, DollarSign, Calendar, Mail } from 'lucide-react';

interface PropertyPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function PropertyPage({ params }: PropertyPageProps) {
  const resolvedParams = await params;

  // Get current user profile
  const userProfile = await getCurrentUserProfile();

  // Get property details from .NET Core API
  const property = await getPropertyById(resolvedParams.id);

  if (!property) {
    notFound();
  }

  const isOwner = userProfile?.id === property.sellerId;

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Property Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">{property.title}</h1>
          <p className="text-2xl font-semibold text-primary mt-2">
            {formatCurrency(property.price)}
          </p>
          <div className="flex items-center gap-2 mt-2 text-sm text-gray-600 dark:text-gray-400">
            <MapPin className="h-4 w-4" />
            <span>{property.address}, {property.city}, {property.state}</span>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          {!isOwner && userProfile && (
            <PropertyQuickActions
              propertyId={property.id}
              sellerId={property.sellerId}
              sellerName={property.sellerName || 'Property Owner'}
              propertyTitle={property.title}
              propertyPrice={property.price}
              propertyAddress={`${property.address}, ${property.city}, ${property.state}`}
            />
          )}
          {!isOwner && !userProfile && (
            <div className="flex gap-2">
              <Button variant="outline" asChild>
                <a href="/sign-in">
                  <Mail className="h-4 w-4 mr-2" />
                  Contact Seller
                </a>
              </Button>
              <Button variant="outline" asChild>
                <a href="/sign-in">
                  <DollarSign className="h-4 w-4 mr-2" />
                  Make Offer
                </a>
              </Button>
            </div>
          )}
          {isOwner && (
            <Button variant="outline" asChild>
              <a href={`/properties/${property.id}/edit`}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Property
              </a>
            </Button>
          )}
        </div>
      </div>

      {/* Property Images */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {property.images?.[0] && (
          <div className="relative aspect-video rounded-lg overflow-hidden">
            <Image
              src={typeof property.images[0] === 'string' ? property.images[0] : property.images[0].url}
              alt={property.title}
              fill
              className="object-cover"
              priority
            />
          </div>
        )}
        <div className="grid grid-cols-2 gap-4">
          {property.images?.slice(1, 5).map((image: string | { url: string; storagePath: string }, index: number) => {
            const imageUrl = typeof image === 'string' ? image : image.url;
            const imageKey = typeof image === 'string' ? `${index}` : image.storagePath;
            return (
              <div key={imageKey} className="relative aspect-square rounded-lg overflow-hidden">
                <Image
                  src={imageUrl}
                  alt={`${property.title} - Image ${index + 2}`}
                  fill
                  className="object-cover"
                />
              </div>
            );
          })}
        </div>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="details" className="space-y-6">
        <TabsList className={`grid w-full ${isOwner ? 'grid-cols-3' : 'grid-cols-2'}`}>
          <TabsTrigger value="details">Property Details</TabsTrigger>
          <TabsTrigger value="openhouse">AI Open House</TabsTrigger>
          {isOwner && <TabsTrigger value="manage">Manage</TabsTrigger>}
        </TabsList>

        <TabsContent value="details" className="space-y-8">
          {/* Property Details */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="md:col-span-2 space-y-8">
              <Card>
                <CardHeader>
                  <CardTitle>Description</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="whitespace-pre-wrap">{property.description}</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Features</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {property.features?.map((feature: string) => (
                      <Badge key={feature} variant="secondary">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-8">
              <Card>
                <CardHeader>
                  <CardTitle>Property Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Type</span>
                    <span className="font-medium">{property.propertyType}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Bedrooms</span>
                    <span className="font-medium">{property.bedrooms}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Bathrooms</span>
                    <span className="font-medium">{property.bathrooms}</span>
                  </div>
                  {property.sqft && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Square Footage</span>
                      <span className="font-medium">{property.sqft.toLocaleString()} sq ft</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Status</span>
                    <Badge variant={property.status === 'active' ? 'default' : 'secondary'}>
                      {property.status}
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Location</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="font-medium">{property.address}</p>
                  <p className="text-muted-foreground">
                    {property.city}, {property.state} {property.zip_code}
                  </p>
                </CardContent>
              </Card>

              {/* Interested in Property Section */}
              {!isOwner && (
                <Card id="contact" className="border-green-200 dark:border-green-800">
                  <CardHeader className="bg-green-50 dark:bg-green-900/20">
                    <CardTitle className="text-green-800 dark:text-green-200">
                      Interested in this Property?
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    {userProfile ? (
                      <div className="space-y-4">
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Connect with the seller through secure messaging or submit an offer directly.
                        </p>

                        <FullContactShareButton
                          propertyId={property.id}
                          sellerId={property.sellerId}
                          sellerName={property.sellerName || 'Property Owner'}
                          propertyTitle={property.title}
                          propertyPrice={property.price}
                          propertyAddress={`${property.address}, ${property.city}, ${property.state}`}
                          className="w-full"
                        />

                        <div className="grid grid-cols-2 gap-2">
                          <PropertyOfferButton
                            propertyId={property.id}
                            sellerId={property.sellerId}
                            sellerName={property.sellerName || 'Property Owner'}
                            propertyTitle={property.title}
                            propertyPrice={property.price}
                            propertyAddress={`${property.address}, ${property.city}, ${property.state}`}
                            className="w-full"
                          />
                          <PropertyVisitButton
                            propertyId={property.id}
                            sellerId={property.sellerId}
                            sellerName={property.sellerName || 'Property Owner'}
                            propertyTitle={property.title}
                            propertyPrice={property.price}
                            propertyAddress={`${property.address}, ${property.city}, ${property.state}`}
                            className="w-full"
                          />
                        </div>

                        <div className="text-xs text-gray-500 space-y-1">
                          <p>• Your contact information will be shared with the seller</p>
                          <p>• All communications are secure and private</p>
                          <p>• No fees for buyers to contact sellers</p>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center space-y-4">
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Sign in to contact the seller, make offers, and schedule visits.
                        </p>
                        <Button asChild className="w-full">
                          <a href="/sign-in">
                            <User className="h-4 w-4 mr-2" />
                            Sign In to Get Started
                          </a>
                        </Button>
                        <div className="text-xs text-gray-500">
                          <p>Free to join • No hidden fees • Secure platform</p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              <Card>
                <CardHeader>
                  <CardTitle>Seller Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-4 mb-4">
                    <div className="relative w-12 h-12 rounded-full overflow-hidden bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                      <User className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <p className="font-medium">{property.sellerName || 'Property Owner'}</p>
                      <p className="text-sm text-muted-foreground">
                        Property Seller
                      </p>
                    </div>
                  </div>
                  {!isOwner && userProfile && (
                    <PropertyContactForm
                      propertyId={property.id}
                      sellerId={property.sellerId}
                      sellerName={property.sellerName || 'Property Owner'}
                      propertyTitle={property.title}
                      propertyPrice={property.price}
                    />
                  )}
                  {!userProfile && (
                    <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        Sign in to contact the seller
                      </p>
                      <Button asChild>
                        <a href="/sign-in">Sign In</a>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="openhouse">
          <OpenHouse property={property} />
        </TabsContent>

        {isOwner && (
          <TabsContent value="manage" className="space-y-8">
            {/* Image Upload Section (Only visible to owner) */}
            <Card>
              <CardHeader>
                <CardTitle>Manage Images</CardTitle>
              </CardHeader>
              <CardContent>
                <PropertyImageUpload
                  propertyId={property.id}
                  propertyName={property.title}
                  onUploadComplete={(images) => {
                    // This will be handled client-side
                    console.log('Images updated:', images);
                  }}
                />
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
