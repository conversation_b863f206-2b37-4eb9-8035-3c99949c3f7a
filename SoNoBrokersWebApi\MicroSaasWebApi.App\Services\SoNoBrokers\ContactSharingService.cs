using Dapper;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.SoNoBrokers.ContactSharing;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using Microsoft.Extensions.Logging;

namespace MicroSaasWebApi.Services.SoNoBrokers
{
    /// <summary>
    /// Service for managing contact sharing between buyers and sellers
    /// </summary>
    public class ContactSharingService : IContactSharingService
    {
        private readonly MicroSaasWebApi.App.Context.IDapperDbContext _context;
        private readonly ILogger<ContactSharingService> _logger;
        private readonly IContactSharingEmailService _emailService;

        public ContactSharingService(
            IDapperDbContext context,
            ILogger<ContactSharingService> logger,
            IContactSharingEmailService emailService)
        {
            _context = context;
            _logger = logger;
            _emailService = emailService;
        }

        public async Task<ContactShareResponse> CreateContactShareAsync(string buyerId, CreateContactShareRequest request)
        {
            try
            {
                using var connection = _context.CreateConnection();
                using var transaction = connection.BeginTransaction();

                // Generate contact share ID
                var contactShareId = Guid.NewGuid().ToString();

                // Insert contact share record
                var insertQuery = @"
                    INSERT INTO ""ContactShare"" (
                        ""id"", ""propertyId"", ""buyerId"", ""sellerId"", ""buyerName"", ""buyerEmail"", ""buyerPhone"",
                        ""message"", ""shareType"", ""offerAmount"", ""schedulingPreference"", ""preferredVisitDate"",
                        ""preferredVisitTime"", ""status"", ""createdAt"", ""emailSent""
                    ) VALUES (
                        @Id, @PropertyId, @BuyerId, @SellerId, @BuyerName, @BuyerEmail, @BuyerPhone,
                        @Message, @ShareType, @OfferAmount, @SchedulingPreference, @PreferredVisitDate,
                        @PreferredVisitTime, @Status, @CreatedAt, @EmailSent
                    )";

                await connection.ExecuteAsync(insertQuery, new
                {
                    Id = contactShareId,
                    PropertyId = request.PropertyId,
                    BuyerId = buyerId,
                    SellerId = request.SellerId,
                    BuyerName = request.BuyerName,
                    BuyerEmail = request.BuyerEmail,
                    BuyerPhone = request.BuyerPhone,
                    Message = request.Message,
                    ShareType = (int)request.ShareType,
                    OfferAmount = request.OfferAmount,
                    SchedulingPreference = request.SchedulingPreference,
                    PreferredVisitDate = request.PreferredVisitDate,
                    PreferredVisitTime = request.PreferredVisitTime,
                    Status = (int)ContactShareStatus.Sent,
                    CreatedAt = DateTime.UtcNow,
                    EmailSent = false
                }, transaction);

                transaction.Commit();

                // Get the created contact share with property and seller details
                var contactShare = await GetContactShareAsync(contactShareId);
                if (contactShare == null)
                {
                    throw new Exception("Failed to retrieve created contact share");
                }

                // Send email notification
                _ = Task.Run(async () =>
                {
                    try
                    {
                        bool emailSent = false;
                        switch (request.ShareType)
                        {
                            case ContactShareType.ContactRequest:
                                emailSent = await _emailService.SendContactShareEmailAsync(contactShare);
                                break;
                            case ContactShareType.PropertyOffer:
                                emailSent = await _emailService.SendPropertyOfferEmailAsync(contactShare);
                                break;
                            case ContactShareType.ScheduleVisit:
                                emailSent = await _emailService.SendVisitSchedulingEmailAsync(contactShare);
                                break;
                            case ContactShareType.OfferWithVisit:
                                emailSent = await _emailService.SendOfferWithVisitEmailAsync(contactShare);
                                break;
                        }

                        // Update email sent status
                        if (emailSent)
                        {
                            await UpdateEmailSentStatusAsync(contactShareId, true);
                        }

                        // Send buyer confirmation email
                        await _emailService.SendBuyerConfirmationEmailAsync(contactShare);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send contact share emails for {ContactShareId}", contactShareId);
                    }
                });

                return contactShare;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create contact share for buyer {BuyerId} and property {PropertyId}",
                    buyerId, request.PropertyId);
                throw;
            }
        }

        public async Task<ContactShareResponse?> GetContactShareAsync(string contactShareId)
        {
            try
            {
                using var connection = _context.CreateConnection();

                var query = @"
                    SELECT 
                        cs.""id"" as Id,
                        cs.""propertyId"" as PropertyId,
                        p.""title"" as PropertyTitle,
                        p.""address"" as PropertyAddress,
                        p.""price"" as PropertyPrice,
                        cs.""buyerId"" as BuyerId,
                        cs.""buyerName"" as BuyerName,
                        cs.""buyerEmail"" as BuyerEmail,
                        cs.""buyerPhone"" as BuyerPhone,
                        cs.""sellerId"" as SellerId,
                        seller.""fullName"" as SellerName,
                        seller.""email"" as SellerEmail,
                        cs.""message"" as Message,
                        cs.""shareType"" as ShareType,
                        cs.""offerAmount"" as OfferAmount,
                        cs.""schedulingPreference"" as SchedulingPreference,
                        cs.""preferredVisitDate"" as PreferredVisitDate,
                        cs.""preferredVisitTime"" as PreferredVisitTime,
                        cs.""status"" as Status,
                        cs.""createdAt"" as CreatedAt,
                        cs.""respondedAt"" as RespondedAt,
                        cs.""sellerResponse"" as SellerResponse,
                        cs.""emailSent"" as EmailSent,
                        cs.""emailSentAt"" as EmailSentAt
                    FROM ""ContactShare"" cs
                    INNER JOIN ""Property"" p ON cs.""propertyId"" = p.""id""
                    INNER JOIN ""User"" seller ON cs.""sellerId"" = seller.""id""
                    WHERE cs.""id"" = @ContactShareId";

                var result = await connection.QueryFirstOrDefaultAsync(query, new { ContactShareId = contactShareId });

                if (result == null) return null;

                return MapToContactShareResponse(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get contact share {ContactShareId}", contactShareId);
                return null;
            }
        }

        public async Task<ContactShareSearchResponse> GetContactSharesAsync(string userId, ContactShareSearchParams searchParams)
        {
            try
            {
                using var connection = _context.CreateConnection();

                var whereConditions = new List<string>
                {
                    "(cs.\"buyerId\" = @UserId OR cs.\"sellerId\" = @UserId)"
                };
                var parameters = new DynamicParameters();
                parameters.Add("UserId", userId);

                BuildSearchConditions(whereConditions, parameters, searchParams);

                var countQuery = $@"
                    SELECT COUNT(*)
                    FROM ""ContactShare"" cs
                    INNER JOIN ""Property"" p ON cs.""propertyId"" = p.""id""
                    WHERE {string.Join(" AND ", whereConditions)}";

                var total = await connection.QuerySingleAsync<int>(countQuery, parameters);

                var selectQuery = $@"
                    SELECT 
                        cs.""id"" as Id,
                        cs.""propertyId"" as PropertyId,
                        p.""title"" as PropertyTitle,
                        p.""address"" as PropertyAddress,
                        p.""price"" as PropertyPrice,
                        cs.""buyerId"" as BuyerId,
                        cs.""buyerName"" as BuyerName,
                        cs.""buyerEmail"" as BuyerEmail,
                        cs.""buyerPhone"" as BuyerPhone,
                        cs.""sellerId"" as SellerId,
                        seller.""fullName"" as SellerName,
                        seller.""email"" as SellerEmail,
                        cs.""message"" as Message,
                        cs.""shareType"" as ShareType,
                        cs.""offerAmount"" as OfferAmount,
                        cs.""schedulingPreference"" as SchedulingPreference,
                        cs.""preferredVisitDate"" as PreferredVisitDate,
                        cs.""preferredVisitTime"" as PreferredVisitTime,
                        cs.""status"" as Status,
                        cs.""createdAt"" as CreatedAt,
                        cs.""respondedAt"" as RespondedAt,
                        cs.""sellerResponse"" as SellerResponse,
                        cs.""emailSent"" as EmailSent,
                        cs.""emailSentAt"" as EmailSentAt
                    FROM ""ContactShare"" cs
                    INNER JOIN ""Property"" p ON cs.""propertyId"" = p.""id""
                    INNER JOIN ""User"" seller ON cs.""sellerId"" = seller.""id""
                    WHERE {string.Join(" AND ", whereConditions)}
                    ORDER BY cs.""{searchParams.SortBy}"" {searchParams.SortOrder.ToUpper()}
                    LIMIT @Limit OFFSET @Offset";

                parameters.Add("Limit", searchParams.Limit);
                parameters.Add("Offset", (searchParams.Page - 1) * searchParams.Limit);

                var results = await connection.QueryAsync(selectQuery, parameters);
                var contactShares = results.Select(MapToContactShareResponse).ToList();

                return new ContactShareSearchResponse
                {
                    ContactShares = contactShares,
                    Total = total,
                    Page = searchParams.Page,
                    TotalPages = (int)Math.Ceiling((double)total / searchParams.Limit),
                    HasMore = searchParams.Page * searchParams.Limit < total
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get contact shares for user {UserId}", userId);
                throw;
            }
        }

        // Placeholder implementations for remaining interface methods
        public async Task<ContactShareSearchResponse> GetPropertyContactSharesAsync(string propertyId, ContactShareSearchParams searchParams)
        {
            // Implementation similar to GetContactSharesAsync but filtered by propertyId
            return await GetContactSharesAsync("", searchParams); // Simplified for now
        }

        public async Task<bool> UpdateContactShareStatusAsync(string contactShareId, ContactShareSellerResponse response)
        {
            try
            {
                using var connection = _context.CreateConnection();

                var updateQuery = @"
                    UPDATE ""ContactShare""
                    SET ""status"" = @Status,
                        ""sellerResponse"" = @Response,
                        ""respondedAt"" = @RespondedAt
                    WHERE ""id"" = @ContactShareId";

                var rowsAffected = await connection.ExecuteAsync(updateQuery, new
                {
                    Status = (int)response.Status,
                    Response = response.Response,
                    RespondedAt = DateTime.UtcNow,
                    ContactShareId = contactShareId
                });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update contact share status for {ContactShareId}", contactShareId);
                return false;
            }
        }

        public Task<ContactShareStats> GetContactShareStatsAsync(string userId)
        {
            // Placeholder implementation
            return Task.FromResult(new ContactShareStats());
        }

        public Task<ContactShareStats> GetPropertyContactShareStatsAsync(string propertyId)
        {
            // Placeholder implementation
            return Task.FromResult(new ContactShareStats());
        }

        public async Task<bool> CanUserAccessContactShareAsync(string userId, string contactShareId)
        {
            try
            {
                using var connection = _context.CreateConnection();

                var count = await connection.QuerySingleAsync<int>(
                    @"SELECT COUNT(*) FROM ""ContactShare""
                      WHERE ""id"" = @ContactShareId
                      AND (""buyerId"" = @UserId OR ""sellerId"" = @UserId)",
                    new { ContactShareId = contactShareId, UserId = userId });

                return count > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking contact share access for user {UserId} and contact share {ContactShareId}",
                    userId, contactShareId);
                return false;
            }
        }

        public async Task<bool> MarkContactShareAsViewedAsync(string contactShareId)
        {
            try
            {
                using var connection = _context.CreateConnection();

                var updateQuery = @"
                    UPDATE ""ContactShare""
                    SET ""status"" = @Status
                    WHERE ""id"" = @ContactShareId AND ""status"" = @SentStatus";

                var rowsAffected = await connection.ExecuteAsync(updateQuery, new
                {
                    Status = (int)ContactShareStatus.Viewed,
                    SentStatus = (int)ContactShareStatus.Sent,
                    ContactShareId = contactShareId
                });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to mark contact share as viewed for {ContactShareId}", contactShareId);
                return false;
            }
        }

        public async Task<bool> DeleteContactShareAsync(string contactShareId)
        {
            try
            {
                using var connection = _context.CreateConnection();

                var deleteQuery = @"DELETE FROM ""ContactShare"" WHERE ""id"" = @ContactShareId";
                var rowsAffected = await connection.ExecuteAsync(deleteQuery, new { ContactShareId = contactShareId });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete contact share {ContactShareId}", contactShareId);
                return false;
            }
        }

        public Task<List<ContactShareResponse>> GetPendingContactSharesAsync(string sellerId, int daysOld = 3)
        {
            // Placeholder implementation
            return Task.FromResult(new List<ContactShareResponse>());
        }

        public Task<bool> SendReminderEmailsAsync()
        {
            // Placeholder implementation
            return Task.FromResult(true);
        }

        public Task<ContactShareStats> GetAdminContactShareStatsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            // Placeholder implementation
            return Task.FromResult(new ContactShareStats());
        }

        public Task<ContactShareSearchResponse> GetAllContactSharesAsync(ContactShareSearchParams searchParams)
        {
            // Placeholder implementation
            return Task.FromResult(new ContactShareSearchResponse());
        }

        /// <summary>
        /// Helper method to update email sent status
        /// </summary>
        private async Task UpdateEmailSentStatusAsync(string contactShareId, bool emailSent)
        {
            try
            {
                using var connection = _context.CreateConnection();

                var updateQuery = @"
                    UPDATE ""ContactShare""
                    SET ""emailSent"" = @EmailSent,
                        ""emailSentAt"" = @EmailSentAt
                    WHERE ""id"" = @ContactShareId";

                await connection.ExecuteAsync(updateQuery, new
                {
                    EmailSent = emailSent,
                    EmailSentAt = emailSent ? DateTime.UtcNow : (DateTime?)null,
                    ContactShareId = contactShareId
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update email sent status for {ContactShareId}", contactShareId);
            }
        }

        /// <summary>
        /// Helper method to build search conditions
        /// </summary>
        private void BuildSearchConditions(List<string> whereConditions, DynamicParameters parameters, ContactShareSearchParams searchParams)
        {
            if (!string.IsNullOrEmpty(searchParams.PropertyId))
            {
                whereConditions.Add("cs.\"propertyId\" = @PropertyId");
                parameters.Add("PropertyId", searchParams.PropertyId);
            }

            if (!string.IsNullOrEmpty(searchParams.BuyerId))
            {
                whereConditions.Add("cs.\"buyerId\" = @BuyerId");
                parameters.Add("BuyerId", searchParams.BuyerId);
            }

            if (!string.IsNullOrEmpty(searchParams.SellerId))
            {
                whereConditions.Add("cs.\"sellerId\" = @SellerId");
                parameters.Add("SellerId", searchParams.SellerId);
            }

            if (searchParams.ShareType.HasValue)
            {
                whereConditions.Add("cs.\"shareType\" = @ShareType");
                parameters.Add("ShareType", (int)searchParams.ShareType.Value);
            }

            if (searchParams.Status.HasValue)
            {
                whereConditions.Add("cs.\"status\" = @Status");
                parameters.Add("Status", (int)searchParams.Status.Value);
            }

            if (searchParams.FromDate.HasValue)
            {
                whereConditions.Add("cs.\"createdAt\" >= @FromDate");
                parameters.Add("FromDate", searchParams.FromDate.Value);
            }

            if (searchParams.ToDate.HasValue)
            {
                whereConditions.Add("cs.\"createdAt\" <= @ToDate");
                parameters.Add("ToDate", searchParams.ToDate.Value);
            }

            if (searchParams.MinOfferAmount.HasValue)
            {
                whereConditions.Add("cs.\"offerAmount\" >= @MinOfferAmount");
                parameters.Add("MinOfferAmount", searchParams.MinOfferAmount.Value);
            }

            if (searchParams.MaxOfferAmount.HasValue)
            {
                whereConditions.Add("cs.\"offerAmount\" <= @MaxOfferAmount");
                parameters.Add("MaxOfferAmount", searchParams.MaxOfferAmount.Value);
            }

            if (!string.IsNullOrEmpty(searchParams.Search))
            {
                whereConditions.Add("(cs.\"buyerName\" ILIKE @Search OR cs.\"message\" ILIKE @Search OR p.\"title\" ILIKE @Search)");
                parameters.Add("Search", $"%{searchParams.Search}%");
            }
        }

        /// <summary>
        /// Helper method to map database result to ContactShareResponse
        /// </summary>
        private ContactShareResponse MapToContactShareResponse(dynamic result)
        {
            var shareType = (ContactShareType)result.ShareType;
            var status = (ContactShareStatus)result.Status;

            return new ContactShareResponse
            {
                Id = result.Id,
                PropertyId = result.PropertyId,
                PropertyTitle = result.PropertyTitle ?? "",
                PropertyAddress = result.PropertyAddress ?? "",
                PropertyPrice = result.PropertyPrice,
                BuyerId = result.BuyerId,
                BuyerName = result.BuyerName,
                BuyerEmail = result.BuyerEmail,
                BuyerPhone = result.BuyerPhone,
                SellerId = result.SellerId,
                SellerName = result.SellerName ?? "",
                SellerEmail = result.SellerEmail ?? "",
                Message = result.Message,
                ShareType = shareType,
                ShareTypeDisplay = GetShareTypeDisplay(shareType),
                OfferAmount = result.OfferAmount,
                SchedulingPreference = result.SchedulingPreference,
                PreferredVisitDate = result.PreferredVisitDate,
                PreferredVisitTime = result.PreferredVisitTime,
                Status = status,
                StatusDisplay = GetStatusDisplay(status),
                CreatedAt = result.CreatedAt,
                RespondedAt = result.RespondedAt,
                SellerResponse = result.SellerResponse,
                EmailSent = result.EmailSent,
                EmailSentAt = result.EmailSentAt
            };
        }

        /// <summary>
        /// Get display text for share type
        /// </summary>
        private string GetShareTypeDisplay(ContactShareType shareType)
        {
            return shareType switch
            {
                ContactShareType.ContactRequest => "Contact Request",
                ContactShareType.PropertyOffer => "Property Offer",
                ContactShareType.ScheduleVisit => "Schedule Visit",
                ContactShareType.OfferWithVisit => "Offer with Visit",
                _ => "Unknown"
            };
        }

        /// <summary>
        /// Get display text for status
        /// </summary>
        private string GetStatusDisplay(ContactShareStatus status)
        {
            return status switch
            {
                ContactShareStatus.Sent => "Sent",
                ContactShareStatus.Delivered => "Delivered",
                ContactShareStatus.Viewed => "Viewed",
                ContactShareStatus.Responded => "Responded",
                ContactShareStatus.Accepted => "Accepted",
                ContactShareStatus.Declined => "Declined",
                ContactShareStatus.Expired => "Expired",
                _ => "Unknown"
            };
        }
    }
}
