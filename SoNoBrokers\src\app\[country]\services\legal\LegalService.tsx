import React from 'react'
import { ServiceLayout } from '@/components/shared/services/ServiceLayout'

interface LegalServiceProps {
  userType: 'buyer' | 'seller'
  isSignedIn: boolean
  country: string
}

// Mock data - in real implementation, this would come from your database and Google Places API
const getMockProviders = (country: string) => {
  const baseProviders = [
    {
      id: '1',
      name: '<PERSON>',
      businessName: 'Mitchell Real Estate Law',
      serviceType: 'Real Estate Lawyer',
      location: country === 'CA' ? 'Toronto, ON' : 'New York, NY',
      distance: '1.5 km',
      rating: 4.9,
      reviewCount: 312,
      price: 'From $1,200',
      specialties: ['Residential Transactions', 'Commercial Real Estate', 'Title Insurance', 'Contract Review'],
      verified: true,
      isAdvertiser: true,
      isPremium: true,
      image: '/api/placeholder/80/80',
      phone: '******-0151',
      email: '<EMAIL>',
      website: 'https://mitchelllaw.com',
      description: 'Experienced real estate lawyer with 20+ years in property law. Specializing in smooth closings and comprehensive legal protection.',
      coordinates: country === 'CA' ? { lat: 43.6532, lng: -79.3832 } : { lat: 40.7128, lng: -74.0060 }
    },
    {
      id: '2',
      name: '<PERSON>',
      businessName: 'Chen & Associates Law Firm',
      serviceType: 'Property Attorney',
      location: country === 'CA' ? 'Vancouver, BC' : 'Los Angeles, CA',
      distance: '2.7 km',
      rating: 4.8,
      reviewCount: 267,
      price: 'From $1,100',
      specialties: ['First-Time Buyers', 'Investment Properties', 'Condo Law', 'Dispute Resolution'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/80/80',
      phone: '******-0152',
      description: 'Dedicated property law firm helping clients navigate complex real estate transactions with confidence.',
      coordinates: country === 'CA' ? { lat: 49.2827, lng: -123.1207 } : { lat: 34.0522, lng: -118.2437 }
    },
    {
      id: '3',
      name: 'Jennifer Walsh',
      businessName: 'Walsh Legal Services',
      serviceType: 'Real Estate Attorney',
      location: country === 'CA' ? 'Calgary, AB' : 'Chicago, IL',
      distance: '4.1 km',
      rating: 4.7,
      reviewCount: 198,
      price: 'From $950',
      specialties: ['Affordable Legal Services', 'Quick Closings', 'Document Preparation', 'Legal Consultation'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/80/80',
      description: 'Affordable real estate legal services without compromising on quality. Fast turnaround and personalized attention.',
      coordinates: country === 'CA' ? { lat: 51.0447, lng: -114.0719 } : { lat: 41.8781, lng: -87.6298 }
    }
  ]

  // Add Google API providers (non-registered)
  const googleProviders = [
    {
      id: 'g1',
      name: 'Downtown Legal Group',
      businessName: 'Downtown Legal Group',
      serviceType: 'Law Firm',
      location: country === 'CA' ? 'Mississauga, ON' : 'Brooklyn, NY',
      distance: '7.8 km',
      rating: 4.5,
      reviewCount: 89,
      price: 'From $800',
      specialties: ['General Practice', 'Real Estate'],
      verified: false,
      isAdvertiser: false,
      isPremium: false,
      description: 'General practice law firm with real estate services available.',
      coordinates: country === 'CA' ? { lat: 43.5890, lng: -79.6441 } : { lat: 40.6782, lng: -73.9442 }
    },
    {
      id: 'g2',
      name: 'Quick Legal Solutions',
      businessName: 'Quick Legal Solutions',
      serviceType: 'Legal Services',
      location: country === 'CA' ? 'Markham, ON' : 'Queens, NY',
      distance: '10.2 km',
      rating: 4.3,
      reviewCount: 56,
      price: 'From $750',
      specialties: ['Document Review', 'Basic Legal Services'],
      verified: false,
      isAdvertiser: false,
      isPremium: false,
      description: 'Basic legal services for straightforward real estate transactions.',
      coordinates: country === 'CA' ? { lat: 43.8561, lng: -79.3370 } : { lat: 40.7282, lng: -73.7949 }
    }
  ]

  return [...baseProviders, ...googleProviders]
}

export function LegalService({
  userType,
  isSignedIn,
  country
}: LegalServiceProps) {
  // Get providers data (this would be async in real implementation)
  const mockProviders = getMockProviders(country)

  // Sort by distance and premium status
  const providers = mockProviders.sort((a, b) => {
    if (a.isPremium && !b.isPremium) return -1
    if (!a.isPremium && b.isPremium) return 1
    if (a.isAdvertiser && !b.isAdvertiser) return -1
    if (!a.isAdvertiser && b.isAdvertiser) return 1
    return parseFloat(a.distance) - parseFloat(b.distance)
  })

  const serviceDescription = userType === 'buyer'
    ? 'Expert real estate lawyers to guide you through your property purchase. Our qualified attorneys handle contract review, title searches, closing procedures, and ensure your legal interests are protected throughout the transaction.'
    : 'Professional legal services for property sellers. Our experienced real estate lawyers handle all legal aspects of selling your property, from contract preparation to closing, ensuring a smooth and legally sound transaction.'

  // Check environment variable for Google providers
  const showGoogleProviders = process.env.NEXT_PUBLIC_ENABLE_GOOGLE_PROVIDERS !== 'false'

  return (
    <ServiceLayout
      userType={userType}
      isSignedIn={isSignedIn}
      serviceTitle="Legal Services"
      serviceDescription={serviceDescription}
      country={country}
      providers={providers}
      showGoogleProviders={showGoogleProviders}
    />
  )
}
