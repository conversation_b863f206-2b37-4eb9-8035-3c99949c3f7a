'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  QrCode, 
  Download, 
  RefreshCw, 
  Eye, 
  EyeOff,
  Calendar,
  Shield,
  Printer,
  Copy,
  CheckCircle
} from 'lucide-react'

interface PropertyQrCode {
  id: string
  propertyId: string
  propertyTitle: string
  qrCodeData: string
  qrCodeImage: string
  isActive: boolean
  expiresAt?: string
  scanCount: number
  createdAt: string
  lastScannedAt?: string
}

interface PropertyQrCodeManagerProps {
  propertyId: string
  propertyTitle: string
  propertyAddress: string
}

export function PropertyQrCodeManager({ propertyId, propertyTitle, propertyAddress }: PropertyQrCodeManagerProps) {
  const [qrCode, setQrCode] = useState<PropertyQrCode | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isGenerating, setIsGenerating] = useState(false)
  const [showQrCode, setShowQrCode] = useState(false)
  const [copied, setCopied] = useState(false)

  useEffect(() => {
    loadQrCode()
  }, [propertyId])

  const loadQrCode = async () => {
    try {
      setIsLoading(true)
      // TODO: Replace with actual API call
      // const response = await getPropertyQrCode(propertyId)
      // setQrCode(response)
      
      // Mock data for now
      setQrCode({
        id: '1',
        propertyId,
        propertyTitle,
        qrCodeData: 'encrypted-qr-data-here',
        qrCodeImage: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        isActive: true,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
        scanCount: 5,
        createdAt: new Date().toISOString(),
        lastScannedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() // 2 days ago
      })
    } catch (error) {
      console.error('Failed to load QR code:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const generateQrCode = async () => {
    try {
      setIsGenerating(true)
      // TODO: Replace with actual API call
      // const response = await generatePropertyQrCode(propertyId)
      // setQrCode(response)
      
      // Mock generation
      await new Promise(resolve => setTimeout(resolve, 2000))
      await loadQrCode()
    } catch (error) {
      console.error('Failed to generate QR code:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  const regenerateQrCode = async () => {
    try {
      setIsGenerating(true)
      // TODO: Replace with actual API call
      // const response = await regeneratePropertyQrCode(propertyId)
      // setQrCode(response)
      
      // Mock regeneration
      await new Promise(resolve => setTimeout(resolve, 2000))
      await loadQrCode()
    } catch (error) {
      console.error('Failed to regenerate QR code:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  const downloadQrCode = () => {
    if (!qrCode) return

    const link = document.createElement('a')
    link.href = qrCode.qrCodeImage
    link.download = `${propertyTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_qr_code.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const copyQrCodeData = async () => {
    if (!qrCode) return

    try {
      await navigator.clipboard.writeText(qrCode.qrCodeData)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy QR code data:', error)
    }
  }

  const printQrCode = () => {
    if (!qrCode) return

    const printWindow = window.open('', '_blank')
    if (!printWindow) return

    printWindow.document.write(`
      <html>
        <head>
          <title>Property QR Code - ${propertyTitle}</title>
          <style>
            body { 
              font-family: Arial, sans-serif; 
              text-align: center; 
              padding: 20px; 
            }
            .qr-container { 
              border: 2px solid #000; 
              padding: 20px; 
              margin: 20px auto; 
              max-width: 400px; 
            }
            .qr-code { 
              max-width: 300px; 
              height: auto; 
            }
            .property-info { 
              margin-top: 20px; 
              font-size: 14px; 
            }
            .instructions { 
              margin-top: 20px; 
              font-size: 12px; 
              color: #666; 
            }
          </style>
        </head>
        <body>
          <div class="qr-container">
            <h2>Property Visit Verification</h2>
            <img src="${qrCode.qrCodeImage}" alt="Property QR Code" class="qr-code" />
            <div class="property-info">
              <h3>${propertyTitle}</h3>
              <p>${propertyAddress}</p>
            </div>
            <div class="instructions">
              <p><strong>Instructions for Buyers:</strong></p>
              <p>1. Scan this QR code when you arrive at the property</p>
              <p>2. This will verify your visit and notify the seller</p>
              <p>3. Keep this code secure and do not share with others</p>
              <p><strong>Powered by SoNoBrokers</strong></p>
            </div>
          </div>
        </body>
      </html>
    `)
    printWindow.document.close()
    printWindow.print()
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <QrCode className="h-5 w-5" />
          Property QR Code
        </CardTitle>
        <CardDescription>
          Generate a secure QR code for property visit verification
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {!qrCode ? (
          <div className="text-center py-8">
            <QrCode className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No QR Code Generated
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Generate a QR code to enable secure property visit verification
            </p>
            <Button onClick={generateQrCode} disabled={isGenerating}>
              {isGenerating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <QrCode className="h-4 w-4 mr-2" />
                  Generate QR Code
                </>
              )}
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            {/* QR Code Status */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Badge variant={qrCode.isActive ? "default" : "secondary"}>
                  {qrCode.isActive ? "Active" : "Inactive"}
                </Badge>
                {qrCode.expiresAt && (
                  <Badge variant="outline">
                    <Calendar className="h-3 w-3 mr-1" />
                    Expires {new Date(qrCode.expiresAt).toLocaleDateString()}
                  </Badge>
                )}
              </div>
              <div className="text-sm text-gray-600">
                Scanned {qrCode.scanCount} times
              </div>
            </div>

            {/* QR Code Display */}
            <div className="text-center">
              <div className="inline-block p-4 bg-white rounded-lg border-2 border-gray-200">
                {showQrCode ? (
                  <img 
                    src={qrCode.qrCodeImage} 
                    alt="Property QR Code" 
                    className="w-48 h-48 mx-auto"
                  />
                ) : (
                  <div className="w-48 h-48 bg-gray-100 flex items-center justify-center">
                    <div className="text-center">
                      <QrCode className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">QR Code Hidden</p>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="mt-4 flex justify-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowQrCode(!showQrCode)}
                >
                  {showQrCode ? (
                    <>
                      <EyeOff className="h-4 w-4 mr-2" />
                      Hide QR Code
                    </>
                  ) : (
                    <>
                      <Eye className="h-4 w-4 mr-2" />
                      Show QR Code
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Actions */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              <Button variant="outline" size="sm" onClick={downloadQrCode}>
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
              
              <Button variant="outline" size="sm" onClick={printQrCode}>
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
              
              <Button variant="outline" size="sm" onClick={copyQrCodeData}>
                {copied ? (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Copied!
                  </>
                ) : (
                  <>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Data
                  </>
                )}
              </Button>
              
              <Button 
                variant="outline" 
                size="sm" 
                onClick={regenerateQrCode}
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Regenerating...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Regenerate
                  </>
                )}
              </Button>
            </div>

            {/* Security Information */}
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                <strong>Security Features:</strong>
                <ul className="mt-2 space-y-1 text-sm">
                  <li>• QR code is encrypted and time-limited</li>
                  <li>• Each scan is logged for security tracking</li>
                  <li>• Only valid for this specific property</li>
                  <li>• Can be regenerated if compromised</li>
                </ul>
              </AlertDescription>
            </Alert>

            {/* Usage Statistics */}
            {qrCode.lastScannedAt && (
              <div className="text-sm text-gray-600">
                <p>Last scanned: {new Date(qrCode.lastScannedAt).toLocaleString()}</p>
                <p>Created: {new Date(qrCode.createdAt).toLocaleString()}</p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
