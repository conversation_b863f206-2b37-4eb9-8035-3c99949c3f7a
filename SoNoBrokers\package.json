{"name": "micro-sass-fast", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --experimental-https", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --ci --coverage --watchAll=false --passWithNoTests", "test:unit": "jest --testPathPattern=src/", "test:integration": "jest --testPathPattern=tests/integration/", "test:e2e": "playwright test", "test:coverage": "jest --coverage", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:clear-cache": "jest --clear<PERSON>ache", "i18n:apply": "node scripts/translation/auto-replace-strings.js", "i18n:translate": "node scripts/translation/ai-translator.js translate", "i18n:validate": "node scripts/translation/ai-translator.js validate", "i18n:workflow": "node scripts/translation/auto-translate-workflow.js full", "i18n:build-check": "node scripts/translation/build-time-i18n-check.js"}, "dependencies": {"@azure/storage-blob": "^12.27.0", "@clerk/nextjs": "^6.21.0", "@googlemaps/js-api-loader": "^1.16.8", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@mdx-js/loader": "^2.3.0", "@mdx-js/react": "^2.3.0", "@microsoft/signalr": "^8.0.7", "@next/mdx": "^13.5.11", "@next/third-parties": "^14.2.18", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.0.42", "@remixicon/react": "^4.6.0", "@shadcn/ui": "^0.0.4", "@strapi/blocks-react-renderer": "^1.0.2", "@stripe/stripe-js": "^7.3.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@types/google.maps": "^3.58.1", "@types/mapbox-gl": "^3.4.1", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "form-data": "^4.0.2", "formik": "^2.4.6", "framer-motion": "^12.18.1", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "jspdf": "^3.0.1", "lucide-react": "^0.515.0", "mapbox-gl": "^3.12.0", "motion": "^12.18.1", "next": "^14.2.18", "next-plausible": "^3.12.4", "next-sitemap": "^4.2.3", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "pg": "^8.16.0", "playwright": "^1.53.0", "prop-types": "^15.8.1", "radix-ui": "^1.4.2", "react": "^18.2.0", "react-day-picker": "^9.7.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-email": "^4.0.15", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.3", "react-icons": "^5.5.0", "react-resizable-panels": "^3.0.2", "react-responsive-masonry": "^2.7.1", "react-split": "^2.0.14", "react-syntax-highlighter": "^15.6.1", "react-to-print": "^3.1.0", "react-tooltip": "^5.29.0", "resend": "^4.5.1", "sass": "^1.89.0", "sharp": "^0.34.2", "sonner": "^2.0.5", "stripe": "^18.2.1", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "yup": "^1.6.1", "zod": "^3.25.64", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/jest": "^29.5.14", "@types/mdx": "^2.0.13", "@types/node": "^24.0.1", "@types/pdfkit": "^0.14.0", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-next": "^14.2.18", "glob": "^11.0.3", "husky": "^9.1.7", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.0", "typescript": "^5.8.3"}}