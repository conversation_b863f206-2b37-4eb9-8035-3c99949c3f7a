import { redirect } from 'next/navigation'
import { Country, SnbUserType } from '@/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Mail, Phone, MapPin, Clock } from 'lucide-react'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: SnbUserType
  }>
}

export default async function ContactPage({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = Object.values(Country)
  const countryParam = resolvedParams.country.toLowerCase()
  const countryEnum = countryParam.toUpperCase() as Country

  if (!validCountries.includes(countryEnum)) {
    redirect('/ca/contact')
  }

  const userType = resolvedSearchParams.userType || SnbUserType.buyer
  const country = countryParam

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-foreground mb-4">
              Contact Our Experts
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Get personalized advice and recommendations for your real estate journey.
              Our team is here to help you every step of the way.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <Card>
              <CardHeader>
                <CardTitle>Get Expert Advice</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <form className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName">First Name</Label>
                      <Input id="firstName" placeholder="John" />
                    </div>
                    <div>
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input id="lastName" placeholder="Doe" />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input id="email" type="email" placeholder="<EMAIL>" />
                  </div>

                  <div>
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input id="phone" type="tel" placeholder="+****************" />
                  </div>

                  <div>
                    <Label htmlFor="userType">I am a</Label>
                    <select
                      id="userType"
                      className="w-full p-2 border border-border rounded-md bg-background"
                      defaultValue={userType}
                    >
                      <option value="Buyer">Buyer</option>
                      <option value="Seller">Seller</option>
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="message">How can we help you?</Label>
                    <Textarea
                      id="message"
                      placeholder="Tell us about your real estate needs..."
                      rows={4}
                    />
                  </div>

                  <Button type="submit" className="w-full">
                    Send Message
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Get in Touch</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Mail className="w-5 h-5 text-primary" />
                    <div>
                      <p className="font-medium">Email</p>
                      <p className="text-muted-foreground"><EMAIL></p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Phone className="w-5 h-5 text-primary" />
                    <div>
                      <p className="font-medium">Phone</p>
                      <p className="text-muted-foreground">
                        {country === 'ca' ? '+****************' : '+****************'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <MapPin className="w-5 h-5 text-primary" />
                    <div>
                      <p className="font-medium">Office</p>
                      <p className="text-muted-foreground">
                        {country === 'ca'
                          ? 'Toronto, ON, Canada'
                          : 'New York, NY, USA'
                        }
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Clock className="w-5 h-5 text-primary" />
                    <div>
                      <p className="font-medium">Business Hours</p>
                      <p className="text-muted-foreground">Mon-Fri: 9AM-6PM</p>
                      <p className="text-muted-foreground">Sat-Sun: 10AM-4PM</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Frequently Asked Questions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <p className="font-medium mb-2">How quickly can I get a response?</p>
                    <p className="text-sm text-muted-foreground">
                      We typically respond to all inquiries within 24 hours during business days.
                    </p>
                  </div>

                  <div>
                    <p className="font-medium mb-2">Is the consultation free?</p>
                    <p className="text-sm text-muted-foreground">
                      Yes, initial consultations are completely free with no obligation.
                    </p>
                  </div>

                  <div>
                    <p className="font-medium mb-2">Do you serve all areas in {country.toUpperCase()}?</p>
                    <p className="text-sm text-muted-foreground">
                      We serve major metropolitan areas and can connect you with local experts anywhere.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'buyer'
  const countryDisplay = resolvedParams.country.toUpperCase()

  return {
    title: `Contact Our Experts - ${countryDisplay} | SoNoBrokers`,
    description: `Get expert real estate advice in ${countryDisplay}. Contact our team for personalized recommendations and guidance for ${userType}s.`,
    keywords: `contact, real estate experts, ${countryDisplay}, ${userType} advice, consultation`,
  }
}
