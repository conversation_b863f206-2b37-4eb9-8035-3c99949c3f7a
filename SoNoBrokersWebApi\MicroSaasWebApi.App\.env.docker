# =============================================================================
# MicroSaasWebApi.App - Docker Environment Variables
# =============================================================================
# Environment variables for Docker deployment
# Copy this file to .env.docker.local and update with your actual values
# =============================================================================

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=***************************************************************************/postgres

# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================
# Clerk Authentication
CLERK_SECRET_KEY=sk_test_VpPsw9paA18Uka7jxuB6PeE3bLvzKVEESGaDuysAgL
CLERK_PUBLISHABLE_KEY=pk_test_YmV0dGVyLXBvc3N1bS01OC5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_JWT_ISSUER=https://better-possum-58.clerk.accounts.dev

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Stripe Payment Service
STRIPE_SECRET_KEY=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

# Email Service (Resend)
RESEND_API_KEY=re_BM6pyT8x_ChaS1fbRCbdprxPy1fwimWCs

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
CORS_ALLOWED_ORIGINS=https://localhost:3000,https://www.api.sonobrokers.com,https://sonobrokers.com,https://localhost:7163

# =============================================================================
# FRONTEND INTEGRATION
# =============================================================================
Frontend__NextJs__AppUrl=https://www.sonobrokers.com
SoNoBrokers__AppUrl=https://www.sonobrokers.com

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=Information
ASPNETCORE_ENVIRONMENT=Production

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
ASPNETCORE_URLS=http://+:8080;https://+:8443
ASPNETCORE_HTTPS_PORT=8443
