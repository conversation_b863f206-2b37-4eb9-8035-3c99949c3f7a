# MicroSaaS Web API - Local Development Environment Variables
# Copy this file to .env and update with your actual values
# The .env file should NOT be committed to source control

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Supabase Database Connection
ConnectionStrings__DefaultConnection=*****************************************************************************************************/postgres
ConnectionStrings__SoNoBrokersConnection=*****************************************************************************************************/postgres

# Database Configuration (Individual Components)
Database__Postgres__User=postgres
Database__Postgres__Password=Shared4w0rk!!!
Database__Postgres__DatabaseName=postgres
Database__Postgres__Host=db.yfznlsisxsnymkvydzha.supabase.co
Database__Postgres__Port=5432
Database__Legacy__DatabaseUrl=*****************************************************************************************************/postgres

# =============================================================================
# AUTHENTICATION - CLERK
# =============================================================================
Authentication__Clerk__SecretKey=sk_test_qMWT9IyzEIVMz6bjQ7P4ZBVHsiy9ZERxHbGKipVHcl
Authentication__Clerk__PublishableKey=pk_test_ZGlzY3JldGUtaGFyZS03My5jbGVyay5hY2NvdW50cy5kZXYk
Authentication__Clerk__WebhookSecret=whsec_your_webhook_secret_here
Authentication__Clerk__JwtIssuer=https://discrete-hare-73.clerk.accounts.dev

# =============================================================================
# PAYMENT PROCESSING - STRIPE
# =============================================================================
ExternalServices__Stripe__SecretKey=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
ExternalServices__Stripe__PublishableKey=pk_live_51Qfy9dP82YH9JfOlFmwTk0RHvE7l6sow7ImJBI4c1GzcbhFydoVpb3DMevP8B3C4gj95qCoEise8vEhgFgWxhLEN000RfInKNH
ExternalServices__Stripe__WebhookSecret=whsec_your_stripe_webhook_secret_here

# =============================================================================
# EMAIL SERVICES
# =============================================================================
# Resend
ExternalServices__Email__Resend__ApiKey=re_H7U9GBDe_2D8FiBaiP6A59v5YHuHTXKei

# SendGrid
ExternalServices__Email__SendGrid__ApiKey=SG.your_sendgrid_api_key_here

# SMTP (Alternative to SendGrid)
ExternalServices__Email__Smtp__Host=smtp.gmail.com
ExternalServices__Email__Smtp__Port=587
ExternalServices__Email__Smtp__Username=<EMAIL>
ExternalServices__Email__Smtp__Password=your_app_password_here
ExternalServices__Email__Smtp__EnableSsl=true

# =============================================================================
# AZURE SERVICES
# =============================================================================
# Azure Storage
AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=your_storage_account;AccountKey=your_key;EndpointSuffix=core.windows.net

# Azure Service Bus
AZURE_SERVICE_BUS_CONNECTION_STRING=Endpoint=sb://your-servicebus.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your_key

# Azure Key Vault (for production-like testing)
AZURE_KEY_VAULT_URL=https://your-keyvault.vault.azure.net/

# Application Insights
APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=your_instrumentation_key;IngestionEndpoint=https://your_region.in.applicationinsights.azure.com/

# =============================================================================
# REDIS CACHE (Optional)
# =============================================================================
REDIS_CONNECTION_STRING=localhost:6379

# =============================================================================
# EXTERNAL API KEYS
# =============================================================================
# Supabase
Supabase__Url=https://yfznlsisxsnymkvydzha.supabase.co
Supabase__AnonKey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.qUBshv6Wi-90ZAEnUM2RXuhR77QFHnmEpI6O-y7l3BE
Supabase__ServiceRoleKey=your_service_role_key_here

# Mapbox
Mapbox__ApiKey=pk.eyJ1IjoiamF2aWFucGljYXJkbzMzIiwiYSI6ImNtYjY4ZGRyazBiaWYybHEyMWpnNGN4cDQifQ.m63aGFvbfzrQhT3sWlSbDQ

# OpenAI
ExternalServices__OpenAI__ApiKey=your_openai_api_key_here

# Make.com
ExternalServices__Make__OrganizationId=your_make_org_id
ExternalServices__Make__TeamId=your_make_team_id
ExternalServices__Make__ApiKey=your_make_api_key

# N8N
ExternalServices__N8N__ApiKey=your_n8n_api_key
ExternalServices__N8N__ApiUrl=your_n8n_api_url
ExternalServices__N8N__WebhookUrl=your_n8n_webhook_url

# =============================================================================
# FEATURE FLAGS (Development Overrides)
# =============================================================================
ENABLE_SWAGGER=true
ENABLE_HANGFIRE=true
ENABLE_DETAILED_LOGGING=true
ENABLE_PAYMENTS=false
ENABLE_ANALYTICS=true
ENABLE_CUSTOM_BRANDING=true
ENABLE_MULTI_REGION=false
ENABLE_ADVANCED_SECURITY=false

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# API Base URLs (Development)
DATA_API_BASE_URL=https://dev-api.microsaas.com/data-api/v1
DOCUMENT_API_BASE_URL=https://dev-api.microsaas.com/document-api/v1
PERMISSION_API_BASE_URL=https://dev-api.microsaas.com/permission-api/v1

# CORS Origins (Development)
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,https://dev-app.microsaas.com

# Rate Limiting (Development - More Permissive)
GLOBAL_REQUESTS_PER_MINUTE=2000
TENANT_REQUESTS_PER_MINUTE=200
USER_REQUESTS_PER_MINUTE=120

# =============================================================================
# TENANT CONFIGURATION
# =============================================================================
DEFAULT_TENANT_ID=demo
ENABLE_MULTI_TENANT_FEATURES=true
TENANT_CACHE_EXPIRY_MINUTES=5
MAX_TENANTS_PER_USER=10

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=Debug
ENABLE_FILE_LOGGING=true
LOG_DIRECTORY=Logs
ENABLE_CONSOLE_LOGGING=true

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
JWT_SECRET_KEY=your_jwt_secret_key_minimum_32_characters_long
JWT_ISSUER=https://api.microsaas.com
JWT_AUDIENCE=microsaas-clients
JWT_EXPIRY_MINUTES=60

# Data Protection Key
DATA_PROTECTION_KEY=your_data_protection_key_here

# =============================================================================
# BACKGROUND JOBS
# =============================================================================
HANGFIRE_CONNECTION_STRING=Host=localhost;Port=5432;Database=microsaas_hangfire;Username=postgres;Password=your_password_here
ENABLE_HANGFIRE_DASHBOARD=true

# =============================================================================
# MONITORING & HEALTH CHECKS
# =============================================================================
ENABLE_HEALTH_CHECKS_UI=true
ENABLE_DETAILED_HEALTH_ERRORS=true
HEALTH_CHECK_CACHE_DURATION_SECONDS=60

# =============================================================================
# NOTIFICATIONS
# =============================================================================
# SignalR
ENABLE_SIGNALR=true
SIGNALR_HUB_PATH=/notificationHub

# Push Notifications (Firebase)
FIREBASE_SERVER_KEY=your_firebase_server_key_here
FIREBASE_SENDER_ID=your_firebase_sender_id_here

# =============================================================================
# FILE STORAGE
# =============================================================================
STORAGE_PROVIDER=Local  # Local, AzureBlob, AWS
LOCAL_STORAGE_PATH=./uploads
MAX_FILE_SIZE_BYTES=10485760  # 10MB
ALLOWED_FILE_TYPES=.pdf,.doc,.docx,.xls,.xlsx,.jpg,.png,.gif

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
# Enable development-specific features
ENABLE_ENTITY_FRAMEWORK_SENSITIVE_LOGGING=true
ENABLE_DETAILED_ERRORS=true
ENABLE_BROWSER_LINK=true

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================
# Test database (for integration tests)
TEST_CONNECTION_STRING=Host=localhost;Port=5432;Database=microsaas_test;Username=postgres;Password=your_password_here

# Test API keys (use test/sandbox keys)
TEST_STRIPE_SECRET_KEY=sk_test_your_test_stripe_key_here
TEST_CLERK_SECRET_KEY=sk_test_your_test_clerk_key_here
