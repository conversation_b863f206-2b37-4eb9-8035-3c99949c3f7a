# API Migration Testing Guide

## Overview
This guide provides comprehensive testing instructions for the migrated API functionality from React to WebAPI using Dapper ORM and Supabase.

## Prerequisites
1. Ensure database is set up with proper schema
2. Run stored procedures script: `Scripts/StoredProcedures.sql`
3. Configure connection strings in appsettings.json
4. Set up Clerk authentication
5. Configure external service endpoints

## Testing Environment Setup

### 1. Database Verification
```sql
-- Verify all tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('User', 'Property', 'Advertiser', 'Project', 'PropertyImage');

-- Verify stored procedures exist
SELECT routine_name FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_type = 'FUNCTION';
```

### 2. Service Registration Check
Verify all services are registered in `ServiceCollectionExtensions.cs`:
- IAdvertiserService
- IAIPropertyService  
- ICommunicationService
- IAdminService
- IProjectService

## API Endpoint Testing

### 1. Advertiser API Testing

#### Test Advertiser Search
```bash
# GET /api/sonobrokers/advertisers
curl -X GET "https://localhost:7001/api/sonobrokers/advertisers?serviceType=photographer&location=Toronto&page=1&limit=10"
```

#### Test Create Advertiser
```bash
# POST /api/sonobrokers/advertisers
curl -X POST "https://localhost:7001/api/sonobrokers/advertisers" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_CLERK_TOKEN" \
  -d '{
    "businessName": "Test Photography",
    "email": "<EMAIL>",
    "serviceType": "photographer",
    "serviceAreas": ["Toronto", "Mississauga"],
    "plan": "basic"
  }'
```

### 2. AI Property Services Testing

#### Test Property Import
```bash
# POST /api/sonobrokers/ai/property-import
curl -X POST "https://localhost:7001/api/sonobrokers/ai/property-import" \
  -H "Content-Type: application/json" \
  -d '{
    "address": "123 Main St, Toronto, ON"
  }'
```

#### Test Property Valuation
```bash
# POST /api/sonobrokers/ai/property-valuation
curl -X POST "https://localhost:7001/api/sonobrokers/ai/property-valuation" \
  -H "Content-Type: application/json" \
  -d '{
    "address": "123 Main St, Toronto, ON",
    "country": "CA"
  }'
```

### 3. Communication API Testing

#### Test Concierge Contact
```bash
# POST /api/sonobrokers/contact-concierge
curl -X POST "https://localhost:7001/api/sonobrokers/contact-concierge" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "country": "CA",
    "propertyAddress": "123 Main St"
  }'
```

#### Test Geo Location
```bash
# GET /api/sonobrokers/geo
curl -X GET "https://localhost:7001/api/sonobrokers/geo?ip=*********"
```

### 4. Admin API Testing

#### Test Dashboard (Requires Admin Auth)
```bash
# GET /api/sonobrokers/admin/dashboard
curl -X GET "https://localhost:7001/api/sonobrokers/admin/dashboard" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### 5. Project API Testing

#### Test Get Projects (Requires Auth)
```bash
# GET /api/sonobrokers/projects
curl -X GET "https://localhost:7001/api/sonobrokers/projects" \
  -H "Authorization: Bearer YOUR_CLERK_TOKEN"
```

## Database Integration Testing

### 1. Verify Dapper Queries
Test that all services can connect to database and execute queries:

```csharp
// Example test for AdvertiserService
[Test]
public async Task AdvertiserService_GetAdvertisers_ReturnsResults()
{
    var request = new AdvertiserSearchRequest { Page = 1, Limit = 10 };
    var result = await _advertiserService.GetAdvertisersAsync(request);
    
    Assert.IsNotNull(result);
    Assert.IsTrue(result.Total >= 0);
}
```

### 2. Test Stored Procedures
```sql
-- Test property search procedure
SELECT * FROM public.search_properties_advanced(
    p_location := 'Toronto',
    p_property_type := 'Detached House',
    p_limit := 10,
    p_offset := 0
);

-- Test admin dashboard counts
SELECT * FROM public.get_admin_dashboard_counts();
```

## Error Handling Testing

### 1. Test Invalid Requests
```bash
# Test missing required fields
curl -X POST "https://localhost:7001/api/sonobrokers/advertisers" \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 2. Test Authentication Failures
```bash
# Test without auth token
curl -X GET "https://localhost:7001/api/sonobrokers/advertisers/me"
```

### 3. Test Not Found Scenarios
```bash
# Test non-existent resource
curl -X GET "https://localhost:7001/api/sonobrokers/advertisers/non-existent-id"
```

## Performance Testing

### 1. Load Testing
Use tools like Apache Bench or k6 to test endpoint performance:

```bash
# Test advertiser search under load
ab -n 1000 -c 10 "https://localhost:7001/api/sonobrokers/advertisers"
```

### 2. Database Query Performance
Monitor query execution times in logs and database metrics.

## Security Testing

### 1. Authentication Testing
- Verify all protected endpoints require valid tokens
- Test token expiration handling
- Verify user ownership checks

### 2. Input Validation Testing
- Test SQL injection prevention
- Test XSS prevention in text fields
- Verify parameter validation

## Integration Testing Checklist

- [ ] All services can connect to database
- [ ] All stored procedures execute successfully
- [ ] Authentication flow works end-to-end
- [ ] Error responses are consistent
- [ ] Logging is working properly
- [ ] External API calls work (geolocation, etc.)
- [ ] File upload functionality works
- [ ] Email services integrate properly

## Manual Testing Scenarios

### 1. Advertiser Workflow
1. Create advertiser account
2. Update advertiser profile
3. Search for advertisers
4. Verify advertiser in search results
5. Admin verify advertiser
6. Delete advertiser account

### 2. Property Workflow
1. Import property using AI
2. Get property valuation
3. Generate property description
4. Create property listing
5. Search for properties
6. View property analytics

### 3. Admin Workflow
1. Login as admin
2. View dashboard statistics
3. Manage user roles
4. Sync Stripe data
5. Manage role permissions

## Common Issues and Solutions

### 1. Database Connection Issues
- Verify connection string format
- Check database server accessibility
- Ensure proper credentials

### 2. Authentication Issues
- Verify Clerk configuration
- Check JWT token format
- Ensure proper middleware order

### 3. Service Registration Issues
- Verify all services are registered in DI container
- Check for circular dependencies
- Ensure proper interface implementations

## Test Data Setup

### 1. Create Test Users
```sql
INSERT INTO public."User" (id, email, "fullName", role, "userType", "isActive", "createdAt", "updatedAt")
VALUES 
('test-user-1', '<EMAIL>', 'Test User 1', 'USER', 'Buyer', true, NOW(), NOW()),
('test-admin-1', '<EMAIL>', 'Test Admin', 'ADMIN', 'Seller', true, NOW(), NOW());
```

### 2. Create Test Properties
```sql
INSERT INTO public."Property" (id, title, description, price, "propertyType", bedrooms, bathrooms, "sellerId", status, "createdAt", "updatedAt")
VALUES 
('test-prop-1', 'Test Property', 'A beautiful test property', 500000, 'Detached House', 3, 2, 'test-user-1', 'active', NOW(), NOW());
```

## Monitoring and Logging

### 1. Application Logs
Monitor logs for:
- Database connection errors
- Authentication failures
- Service exceptions
- Performance issues

### 2. Database Logs
Monitor for:
- Slow queries
- Connection pool exhaustion
- Deadlocks
- Failed transactions

## Success Criteria

✅ All API endpoints respond correctly
✅ Database operations complete successfully
✅ Authentication and authorization work properly
✅ Error handling is consistent
✅ Performance meets requirements
✅ Security measures are effective
✅ Integration with external services works
✅ Logging and monitoring are functional
