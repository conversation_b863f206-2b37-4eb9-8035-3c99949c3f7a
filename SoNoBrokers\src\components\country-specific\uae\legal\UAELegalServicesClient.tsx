'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { MapPin, Phone, Mail, Star, Clock, DollarSign } from 'lucide-react'

interface Lawyer {
  id: string
  name: string
  firm: string
  specialties: string[]
  rating: number
  reviews: number
  hourlyRate: string
  location: string
  phone: string
  email: string
  experience: string
  languages: string[]
  availability: string
  emirates: string[]
}

interface UAELegalServicesClientProps {
  userType: 'buyer' | 'seller'
}

export function UAELegalServicesClient({ userType }: UAELegalServicesClientProps) {
  const [selectedSpecialty, setSelectedSpecialty] = useState<string>('all')

  // Sample UAE lawyers data
  const lawyers: Lawyer[] = [
    {
      id: '1',
      name: 'Ahmed Al Mansouri',
      firm: 'Al Mansouri Legal Consultancy',
      specialties: ['Real Estate Law', 'Property Investment', 'RERA Compliance'],
      rating: 4.9,
      reviews: 156,
      hourlyRate: 'AED 800-1200',
      location: 'Dubai, UAE',
      phone: '+971 4 555 0123',
      email: '<EMAIL>',
      experience: '18+ years',
      languages: ['Arabic', 'English'],
      availability: 'Available this week',
      emirates: ['Dubai', 'Abu Dhabi', 'Sharjah']
    },
    {
      id: '2',
      name: 'Sarah Johnson',
      firm: 'Johnson & Partners Legal',
      specialties: ['Expat Property Law', 'Freehold Properties', 'Off-Plan Purchases'],
      rating: 4.8,
      reviews: 203,
      hourlyRate: 'AED 700-1000',
      location: 'Abu Dhabi, UAE',
      phone: '+971 2 555 0456',
      email: '<EMAIL>',
      experience: '15+ years',
      languages: ['English', 'Arabic', 'French'],
      availability: 'Available next week',
      emirates: ['Abu Dhabi', 'Dubai']
    },
    {
      id: '3',
      name: 'Fatima Al Zahra',
      firm: 'Al Zahra Law Firm',
      specialties: ['Property Disputes', 'Leasehold Rights', 'Commercial Real Estate'],
      rating: 4.7,
      reviews: 89,
      hourlyRate: 'AED 600-900',
      location: 'Sharjah, UAE',
      phone: '+971 6 555 0789',
      email: '<EMAIL>',
      experience: '12+ years',
      languages: ['Arabic', 'English', 'Urdu'],
      availability: 'Available today',
      emirates: ['Sharjah', 'Ajman', 'Dubai']
    },
    {
      id: '4',
      name: 'Michael Chen',
      firm: 'Chen Legal Services',
      specialties: ['International Property Law', 'Investment Visas', 'Property Finance'],
      rating: 4.9,
      reviews: 134,
      hourlyRate: 'AED 900-1300',
      location: 'Dubai, UAE',
      phone: '+971 4 555 0321',
      email: '<EMAIL>',
      experience: '20+ years',
      languages: ['English', 'Mandarin', 'Arabic'],
      availability: 'Available this week',
      emirates: ['Dubai', 'Abu Dhabi']
    }
  ]

  const specialties = ['all', 'Real Estate Law', 'Property Investment', 'RERA Compliance', 'Expat Property Law', 'Freehold Properties', 'Property Disputes']

  const filteredLawyers = selectedSpecialty === 'all' 
    ? lawyers 
    : lawyers.filter(lawyer => lawyer.specialties.includes(selectedSpecialty))

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">
          Legal Services in the UAE
        </h1>
        <p className="text-muted-foreground text-lg">
          {userType === 'buyer' 
            ? 'Find experienced real estate lawyers specializing in UAE property law and RERA regulations.'
            : 'Connect with legal professionals who understand UAE property sales and investment regulations.'
          }
        </p>
      </div>

      {/* Specialty Filter */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Filter by Specialty</h3>
        <div className="flex flex-wrap gap-2">
          {specialties.map((specialty) => (
            <Button
              key={specialty}
              variant={selectedSpecialty === specialty ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedSpecialty(specialty)}
              className="capitalize"
            >
              {specialty === 'all' ? 'All Specialties' : specialty}
            </Button>
          ))}
        </div>
      </div>

      {/* Lawyers Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredLawyers.map((lawyer) => (
          <Card key={lawyer.id} className="h-full">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-xl">{lawyer.name}</CardTitle>
                  <CardDescription className="font-medium">{lawyer.firm}</CardDescription>
                </div>
                <Badge variant="secondary" className="text-green-600">
                  <Clock className="w-3 h-3 mr-1" />
                  {lawyer.availability}
                </Badge>
              </div>
              
              <div className="flex items-center gap-2">
                <div className="flex items-center">
                  <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  <span className="font-medium ml-1">{lawyer.rating}</span>
                </div>
                <span className="text-muted-foreground">({lawyer.reviews} reviews)</span>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Specialties</h4>
                <div className="flex flex-wrap gap-1">
                  {lawyer.specialties.map((specialty) => (
                    <Badge key={specialty} variant="outline" className="text-xs">
                      {specialty}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-muted-foreground" />
                  <span>{lawyer.hourlyRate}/hour</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-muted-foreground" />
                  <span>{lawyer.location}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-muted-foreground" />
                  <span>{lawyer.phone}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-muted-foreground" />
                  <span className="truncate">{lawyer.email}</span>
                </div>
              </div>

              <div>
                <p className="text-sm text-muted-foreground">
                  <strong>Experience:</strong> {lawyer.experience}
                </p>
                <p className="text-sm text-muted-foreground">
                  <strong>Languages:</strong> {lawyer.languages.join(', ')}
                </p>
                <p className="text-sm text-muted-foreground">
                  <strong>Emirates:</strong> {lawyer.emirates.join(', ')}
                </p>
              </div>

              <div className="flex gap-2 pt-4">
                <Button className="flex-1" size="sm">
                  Contact Lawyer
                </Button>
                <Button variant="outline" size="sm">
                  View Profile
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredLawyers.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No lawyers found for the selected specialty.</p>
          <Button 
            variant="outline" 
            onClick={() => setSelectedSpecialty('all')}
            className="mt-4"
          >
            Show All Lawyers
          </Button>
        </div>
      )}

      {/* UAE Property Law Information */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>UAE Property Law Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <p>• RERA (Real Estate Regulatory Agency) governs property transactions</p>
          <p>• Freehold ownership available to expats in designated areas</p>
          <p>• Property registration required through Dubai Land Department or equivalent</p>
          <p>• Transfer fees typically 4% of property value</p>
          <p>• Legal representation recommended for all property transactions</p>
        </CardContent>
      </Card>
    </div>
  )
}
