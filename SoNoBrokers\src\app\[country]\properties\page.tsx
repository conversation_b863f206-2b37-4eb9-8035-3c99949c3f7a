
import PropertySearchClient from '@/components/shared/properties/PropertySearchClient'

interface PropertiesPageProps {
  params: Promise<{
    country: string
  }>
}

export default async function PropertiesPage({ params }: PropertiesPageProps) {
  const { country } = await params

  return (
    <PropertySearchClient
      initialProperties={[]}
      country={country}
      userType="buyer"
    />
  )
}
