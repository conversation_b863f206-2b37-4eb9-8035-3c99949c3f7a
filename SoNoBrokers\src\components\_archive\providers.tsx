'use client';

import { ReactNode, useEffect } from "react";
import { ThemeProvider } from "next-themes";
import { Toaster } from "react-hot-toast";
import { Tooltip } from "react-tooltip";
import { Header } from "@/components/shared/layout/Header";
import { Footer } from "@/components/shared/layout/Footer";
import { usePathname } from "next/navigation";
import { useTheme } from "next-themes";
import { Toaster as ShadcnToaster } from "@/components/ui/toaster";

export function Providers({ children }: { children: ReactNode }) {
  const pathname = usePathname();
  const { theme, setTheme } = useTheme();

  // Default values for Header and Footer props
  const defaultCountries = [
    { code: 'CA', name: 'Canada', flag: '🇨🇦', value: 'ca' },
    { code: 'US', name: 'United States', flag: '🇺🇸', value: 'us' }
  ];
  const defaultUserTypes = [
    { value: 'buyer', label: 'Buyer' },
    { value: 'seller', label: 'Seller' }
  ];

  // Initialize theme to light if not set
  useEffect(() => {
    if (!theme || theme === 'system') {
      setTheme('light');
    }
  }, [theme, setTheme]);

  // Track page views
  useEffect(() => {
    if (pathname) {
      // Example analytics call
      console.log(`Page viewed: ${pathname}`);
    }
  }, [pathname]);

  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="light"
      enableSystem
      disableTransitionOnChange
    >
      <div className="min-h-screen bg-background flex flex-col">
        <Header />
        <div className="flex-1">
          {children}
        </div>
        <Footer />
      </div>

      {/* Toast notifications - both libraries for different use cases */}
      <Toaster
        position="bottom-center"
        toastOptions={{
          duration: 3000,
          className: "text-sm dark:bg-black dark:text-white",
        }}
      />

      <ShadcnToaster />

      {/* Tooltip for accessibility */}
      <Tooltip
        id="tooltip"
        className="z-[60] !opacity-100 max-w-sm shadow-lg"
      />
    </ThemeProvider>
  );
}
