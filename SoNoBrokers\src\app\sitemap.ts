import { MetadataRoute } from 'next'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
    const currentUrl = process.env.NEXT_PUBLIC_APP_URL || ''

    // Removed WordPress blog posts since blog functionality is not used
    const blogPostsMaps: MetadataRoute.Sitemap = []

    return [
        {
            url: currentUrl,
            lastModified: new Date(),
            changeFrequency: 'yearly',
            priority: 1,
        },
        {
            url: `${currentUrl}/privacy-policy`,
            lastModified: new Date(),
            changeFrequency: 'yearly',
            priority: 0.8,
        },
        {
            url: `${currentUrl}/tos`,
            lastModified: new Date(),
            changeFrequency: 'yearly',
            priority: 0.7,
        },
        {
            url: `${currentUrl}/blog`,
            lastModified: new Date(),
            changeFrequency: 'yearly',
            priority: 0.6,
        },
        ...blogPostsMaps
    ]
}