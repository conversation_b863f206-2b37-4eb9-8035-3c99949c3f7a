# =============================================================================
# SoNoBrokers React/Next.js Application Dockerfile
# Multi-stage build for optimized production image
# =============================================================================

# Stage 1: Dependencies
FROM node:20-alpine AS deps
LABEL stage=deps
LABEL description="Install dependencies"

# Install libc6-compat for Alpine compatibility
RUN apk add --no-cache libc6-compat

WORKDIR /app

# Copy package files
COPY package.json package-lock.json* ./

# Install dependencies
RUN npm install --production --ignore-scripts

# =============================================================================
# Stage 2: Builder
FROM node:20-alpine AS builder
LABEL stage=builder
LABEL description="Build the application"

WORKDIR /app

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy source code
COPY . .

# Set environment variables for build
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production

# Build the application
RUN npm run build

# =============================================================================
# Stage 3: Runner (Production)
FROM node:20-alpine AS runner
LABEL stage=runner
LABEL description="Production runtime"

WORKDIR /app

# Create non-root user for security
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Set environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# Note: Prisma not needed - using .NET Web API backend

# Set correct permissions
RUN chown -R nextjs:nodejs /app
USER nextjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# Start the application
CMD ["node", "server.js"]

# =============================================================================
# Development Stage (Optional)
FROM node:20-alpine AS development
LABEL stage=development
LABEL description="Development environment"

WORKDIR /app

# Install dependencies including dev dependencies
COPY package.json package-lock.json* ./
RUN npm install

# Copy source code
COPY . .

# Note: Prisma not needed - using .NET Web API backend

# Expose port for development
EXPOSE 3000

# Start development server
CMD ["npm", "run", "dev"]

# =============================================================================
# Build Arguments and Labels
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION

LABEL org.label-schema.build-date=$BUILD_DATE \
  org.label-schema.name="SoNoBrokers React App" \
  org.label-schema.description="SoNoBrokers commission-free real estate platform frontend" \
  org.label-schema.url="https://sonobrokers.com" \
  org.label-schema.vcs-ref=$VCS_REF \
  org.label-schema.vcs-url="https://github.com/sonobrokers/sonobrokers" \
  org.label-schema.vendor="SoNoBrokers" \
  org.label-schema.version=$VERSION \
  org.label-schema.schema-version="1.0"
