import { NextRequest, NextResponse } from 'next/server'
import { getUserByEmail, updateUserRole } from '@/lib/api/user-api'
import { requireAdmin } from '@/lib/auth'
import { UserRole } from '@/types'

// POST /api/admin/roles - Grant admin role to user (Admin only)
export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    const adminUser = await requireAdmin()
    
    const body = await request.json()
    const { email, role } = body

    // Validate required fields
    if (!email || !role) {
      return NextResponse.json(
        { success: false, error: 'Email and role are required' },
        { status: 400 }
      )
    }

    // Validate role
    if (!Object.values(UserRole).includes(role)) {
      return NextResponse.json(
        { success: false, error: 'Invalid role' },
        { status: 400 }
      )
    }

    // Get user by email
    const user = await getUserByEmail(email)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    // Update user role
    const updatedUser = await updateUserRole(user.id, role)

    if (!updatedUser) {
      return NextResponse.json(
        { success: false, error: 'Failed to update user role' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        id: updatedUser.id,
        email: updatedUser.email,
        role: updatedUser.role
      },
      message: `User role updated to ${role} successfully`
    })
  } catch (error) {
    console.error('Error updating user role:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update user role' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/roles - Remove admin role from user (Admin only)
export async function DELETE(request: NextRequest) {
  try {
    // Require admin authentication
    const adminUser = await requireAdmin()
    
    const body = await request.json()
    const { email } = body

    // Validate required fields
    if (!email) {
      return NextResponse.json(
        { success: false, error: 'Email is required' },
        { status: 400 }
      )
    }

    // Prevent admin from removing their own admin role
    if (email === adminUser.email) {
      return NextResponse.json(
        { success: false, error: 'Cannot remove your own admin role' },
        { status: 400 }
      )
    }

    // Get user by email
    const user = await getUserByEmail(email)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    // Update user role to USER
    const updatedUser = await updateUserRole(user.id, 'USER')

    if (!updatedUser) {
      return NextResponse.json(
        { success: false, error: 'Failed to update user role' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        id: updatedUser.id,
        email: updatedUser.email,
        role: updatedUser.role
      },
      message: 'Admin role removed successfully'
    })
  } catch (error) {
    console.error('Error removing admin role:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to remove admin role' },
      { status: 500 }
    )
  }
}
