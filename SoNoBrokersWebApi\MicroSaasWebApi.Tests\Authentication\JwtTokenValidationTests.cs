using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using MicroSaasWebApi.Services.Auth.Clerk;
using MicroSaasWebApi.Tests.Common;
using Moq;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Authentication
{
    public class JwtTokenValidationTests : TestBase
    {
        private readonly Mock<ILogger<ClerkAuthService>> _mockLogger;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly ClerkAuthService _clerkAuthService;
        private readonly string _secretKey = "this-is-a-very-long-secret-key-for-testing-purposes-that-meets-minimum-requirements";

        public JwtTokenValidationTests(ITestOutputHelper output) : base(output)
        {
            _mockLogger = new Mock<ILogger<ClerkAuthService>>();
            _mockConfiguration = new Mock<IConfiguration>();
            
            // Setup configuration
            _mockConfiguration.Setup(x => x["Clerk:SecretKey"]).Returns(_secretKey);
            _mockConfiguration.Setup(x => x["Clerk:PublishableKey"]).Returns("test-publishable-key");
            _mockConfiguration.Setup(x => x["Clerk:ApiUrl"]).Returns("https://api.clerk.dev/v1");
            _mockConfiguration.Setup(x => x["Authentication:Jwt:Issuer"]).Returns("test-issuer");
            _mockConfiguration.Setup(x => x["Authentication:Jwt:Audience"]).Returns("test-audience");
            
            var httpClient = new HttpClient();
            _clerkAuthService = new ClerkAuthService(_mockConfiguration.Object, _mockLogger.Object, httpClient);
        }

        [Fact]
        public async Task ValidateClerkTokenAsync_WithValidToken_ReturnsClaimsPrincipal()
        {
            // Arrange
            var token = GenerateValidJwtToken();

            // Act
            var result = await _clerkAuthService.ValidateClerkTokenAsync(token);

            // Assert
            result.Should().NotBeNull();
            result!.Identity!.IsAuthenticated.Should().BeTrue();
            result.FindFirst(ClaimTypes.NameIdentifier)?.Value.Should().Be("user_123456");
            result.FindFirst(ClaimTypes.Email)?.Value.Should().Be("<EMAIL>");
        }

        [Fact]
        public async Task ValidateClerkTokenAsync_WithExpiredToken_ReturnsNull()
        {
            // Arrange
            var token = GenerateExpiredJwtToken();

            // Act
            var result = await _clerkAuthService.ValidateClerkTokenAsync(token);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task ValidateClerkTokenAsync_WithInvalidSignature_ReturnsNull()
        {
            // Arrange
            var token = GenerateTokenWithInvalidSignature();

            // Act
            var result = await _clerkAuthService.ValidateClerkTokenAsync(token);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task ValidateClerkTokenAsync_WithMalformedToken_ReturnsNull()
        {
            // Arrange
            var token = "invalid.malformed.token";

            // Act
            var result = await _clerkAuthService.ValidateClerkTokenAsync(token);

            // Assert
            result.Should().BeNull();
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        [InlineData("   ")]
        public async Task ValidateClerkTokenAsync_WithEmptyToken_ReturnsNull(string? token)
        {
            // Act
            var result = await _clerkAuthService.ValidateClerkTokenAsync(token!);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task ValidateClerkTokenAsync_WithTokenMissingRequiredClaims_ReturnsNull()
        {
            // Arrange
            var token = GenerateTokenWithoutRequiredClaims();

            // Act
            var result = await _clerkAuthService.ValidateClerkTokenAsync(token);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task ValidateClerkTokenAsync_WithValidTokenAndRoleClaims_ReturnsClaimsPrincipalWithRoles()
        {
            // Arrange
            var token = GenerateValidJwtTokenWithRoles();

            // Act
            var result = await _clerkAuthService.ValidateClerkTokenAsync(token);

            // Assert
            result.Should().NotBeNull();
            result!.IsInRole("ADMIN").Should().BeTrue();
            result.FindFirst(ClaimTypes.Role)?.Value.Should().Be("ADMIN");
        }

        [Fact]
        public async Task ValidateClerkTokenAsync_WithTokenFromDifferentIssuer_ReturnsNull()
        {
            // Arrange
            var token = GenerateTokenWithDifferentIssuer();

            // Act
            var result = await _clerkAuthService.ValidateClerkTokenAsync(token);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task ValidateClerkTokenAsync_WithTokenForDifferentAudience_ReturnsNull()
        {
            // Arrange
            var token = GenerateTokenWithDifferentAudience();

            // Act
            var result = await _clerkAuthService.ValidateClerkTokenAsync(token);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task ValidateClerkTokenAsync_LogsValidationAttempts()
        {
            // Arrange
            var token = GenerateValidJwtToken();

            // Act
            await _clerkAuthService.ValidateClerkTokenAsync(token);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Clerk token validated successfully")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task ValidateClerkTokenAsync_LogsValidationFailures()
        {
            // Arrange
            var token = "invalid-token";

            // Act
            await _clerkAuthService.ValidateClerkTokenAsync(token);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Clerk token validation failed")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        private string GenerateValidJwtToken()
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_secretKey);
            
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim(ClaimTypes.NameIdentifier, "user_123456"),
                    new Claim(ClaimTypes.Email, "<EMAIL>"),
                    new Claim("sub", "user_123456"),
                    new Claim("email_verified", "true")
                }),
                Expires = DateTime.UtcNow.AddHours(1),
                Issuer = "test-issuer",
                Audience = "test-audience",
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };
            
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        private string GenerateExpiredJwtToken()
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_secretKey);
            
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim(ClaimTypes.NameIdentifier, "user_123456"),
                    new Claim(ClaimTypes.Email, "<EMAIL>")
                }),
                Expires = DateTime.UtcNow.AddHours(-1), // Expired
                Issuer = "test-issuer",
                Audience = "test-audience",
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };
            
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        private string GenerateTokenWithInvalidSignature()
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var wrongKey = Encoding.ASCII.GetBytes("wrong-secret-key-that-is-different-from-the-configured-one");
            
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim(ClaimTypes.NameIdentifier, "user_123456"),
                    new Claim(ClaimTypes.Email, "<EMAIL>")
                }),
                Expires = DateTime.UtcNow.AddHours(1),
                Issuer = "test-issuer",
                Audience = "test-audience",
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(wrongKey), SecurityAlgorithms.HmacSha256Signature)
            };
            
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        private string GenerateTokenWithoutRequiredClaims()
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_secretKey);
            
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim("custom_claim", "value")
                    // Missing required claims like NameIdentifier
                }),
                Expires = DateTime.UtcNow.AddHours(1),
                Issuer = "test-issuer",
                Audience = "test-audience",
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };
            
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        private string GenerateValidJwtTokenWithRoles()
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_secretKey);
            
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim(ClaimTypes.NameIdentifier, "admin_123456"),
                    new Claim(ClaimTypes.Email, "<EMAIL>"),
                    new Claim(ClaimTypes.Role, "ADMIN"),
                    new Claim("sub", "admin_123456")
                }),
                Expires = DateTime.UtcNow.AddHours(1),
                Issuer = "test-issuer",
                Audience = "test-audience",
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };
            
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        private string GenerateTokenWithDifferentIssuer()
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_secretKey);
            
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim(ClaimTypes.NameIdentifier, "user_123456"),
                    new Claim(ClaimTypes.Email, "<EMAIL>")
                }),
                Expires = DateTime.UtcNow.AddHours(1),
                Issuer = "different-issuer", // Different issuer
                Audience = "test-audience",
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };
            
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        private string GenerateTokenWithDifferentAudience()
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_secretKey);
            
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim(ClaimTypes.NameIdentifier, "user_123456"),
                    new Claim(ClaimTypes.Email, "<EMAIL>")
                }),
                Expires = DateTime.UtcNow.AddHours(1),
                Issuer = "test-issuer",
                Audience = "different-audience", // Different audience
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };
            
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }
    }
}
