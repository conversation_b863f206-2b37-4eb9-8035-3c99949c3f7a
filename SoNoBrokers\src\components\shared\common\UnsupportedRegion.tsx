import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { getCountryName } from '@/lib/geo';

interface UnsupportedRegionProps {
  countryCode: string;
}

export function UnsupportedRegion({ countryCode }: UnsupportedRegionProps) {
  return (
    <div className="container mx-auto py-12 px-4">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-center">
            Coming Soon to {getCountryName(countryCode)}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 text-center">
          <p className="text-muted-foreground">
            We're currently working on expanding our services to your region. SoNoBrokers is currently available in the United States and Canada.
          </p>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold">What's Next?</h3>
            <ul className="space-y-2 text-muted-foreground">
              <li>• We're actively working on regulatory compliance for your region</li>
              <li>• Setting up local partnerships and services</li>
              <li>• Adapting our platform to meet local requirements</li>
            </ul>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Stay Updated</h3>
            <p className="text-muted-foreground">
              Join our mailing list to be notified when we launch in your region.
            </p>
            <Button size="lg" className="w-full">
              Subscribe for Updates
            </Button>
          </div>

          <div className="pt-4 border-t">
            <p className="text-sm text-muted-foreground">
              Currently Available In:
            </p>
            <div className="flex justify-center gap-4 mt-2">
              <Button variant="outline" disabled>
                United States
              </Button>
              <Button variant="outline" disabled>
                Canada
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}