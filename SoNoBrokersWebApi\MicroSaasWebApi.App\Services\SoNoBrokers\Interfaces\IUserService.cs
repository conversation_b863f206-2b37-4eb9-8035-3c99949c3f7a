using SoNoBrokersUser = MicroSaasWebApi.Models.SoNoBrokers.User;
using MicroSaasWebApi.Models.SoNoBrokers;

namespace MicroSaasWebApi.Services.SoNoBrokers.Interfaces
{
    public interface IUserService
    {
        Task<IEnumerable<SoNoBrokersUser>> GetAllUsersAsync();
        Task<SoNoBrokersUser?> GetUserByIdAsync(string id);
        Task<SoNoBrokersUser?> GetUserByEmailAsync(string email);
        Task<SoNoBrokersUser> CreateUserAsync(SoNoBrokersUser user);
        Task<SoNoBrokersUser> UpdateUserAsync(SoNoBrokersUser user);
        Task<bool> DeleteUserAsync(string id);
        Task<bool> UpdateLoginStatusAsync(string id, bool loggedIn);
        Task<bool> UserExistsAsync(string id);
        Task<bool> EmailExistsAsync(string email);
        Task<IEnumerable<SoNoBrokersUser>> GetUsersByTypeAsync(UserType userType);

        // Clerk integration methods
        Task<SoNoBrokersUser?> GetUserByClerkIdAsync(string clerkUserId);
        Task<SoNoBrokersUser> CreateUserAsync(CreateUserRequest request);
        Task<SoNoBrokersUser?> UpdateUserAsync(UpdateUserRequest request);

        // Auth tracking methods
        Task TrackLoginAsync(string userId);
        Task TrackLogoutAsync(string userId);
        Task<LoginStatsResponse> GetLoginStatsAsync(string userId);

        // User mapping and sync methods
        Task<SoNoBrokersUser> MapAuthUserToSnbUserAsync(MapAuthUserRequest request);
        Task<SoNoBrokersUser?> GetCurrentUserAsync(string clerkUserId);
        Task<SoNoBrokersUser?> SyncUserWithClerkAsync(string clerkUserId);

        // Permission and role methods
        Task<bool> HasPermissionAsync(string userId, string permission, string? resource = null);
        Task<IEnumerable<SoNoBrokersUser>> GetAllUsersWithPaginationAsync(int page = 1, int limit = 10, UserRole? role = null);
        Task<UserPermissionsResponse> GetUserPermissionsAsync(string userId);
    }
}
