using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using System.Net.Http.Json;
using System.Text.Json;
using MicroSaasWebApi.Models.SoNoBrokers.Messaging;
using MicroSaasWebApi.Tests.Configuration;

namespace MicroSaasWebApi.Tests.E2E
{
    [TestFixture]
    public class MessagingE2ETests
    {
        private WebApplicationFactory<Program> _factory;
        private HttpClient _httpClient;
        private HubConnection _hubConnection;
        private string _testUserId;
        private string _testPropertyId;
        private string _testSellerId;

        [OneTimeSetUp]
        public void OneTimeSetUp()
        {
            _factory = new WebApplicationFactory<Program>()
                .WithWebHostBuilder(builder =>
                {
                    builder.ConfigureServices(services =>
                    {
                        var configuration = TestConfiguration.CreateTestConfiguration();
                        services.AddSingleton(configuration);
                    });
                });

            _httpClient = _factory.CreateClient();
            
            // Set up test data
            _testUserId = "test-user-123";
            _testPropertyId = "test-property-123";
            _testSellerId = "test-seller-123";
        }

        [SetUp]
        public async Task SetUp()
        {
            // Set up SignalR connection
            _hubConnection = new HubConnectionBuilder()
                .WithUrl($"{_httpClient.BaseAddress}messaginghub", options =>
                {
                    options.HttpMessageHandlerFactory = _ => _factory.Server.CreateHandler();
                    options.AccessTokenProvider = () => Task.FromResult("test-token");
                })
                .Build();

            await _hubConnection.StartAsync();
        }

        [TearDown]
        public async Task TearDown()
        {
            if (_hubConnection != null)
            {
                await _hubConnection.DisposeAsync();
            }
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            _httpClient?.Dispose();
            _factory?.Dispose();
        }

        [Test]
        public async Task CompleteMessagingWorkflow_ShouldWork()
        {
            // This test would require proper authentication setup
            // For now, we'll test the structure and basic connectivity

            // Arrange
            var createConversationRequest = new CreateConversationRequest
            {
                PropertyId = _testPropertyId,
                SellerId = _testSellerId,
                Subject = "E2E Test Property Inquiry",
                InitialMessage = "I'm interested in this property for testing purposes."
            };

            // Act & Assert - Test API endpoints
            var conversationResponse = await _httpClient.PostAsJsonAsync(
                "/api/sonobrokers/messaging/conversations", 
                createConversationRequest);

            // Note: This will return 401 Unauthorized without proper auth setup
            // In a real E2E test, we'd set up proper test authentication
            Assert.That(conversationResponse.StatusCode, 
                Is.EqualTo(System.Net.HttpStatusCode.Unauthorized)
                .Or.EqualTo(System.Net.HttpStatusCode.Created));

            // Test SignalR connection
            Assert.That(_hubConnection.State, Is.EqualTo(HubConnectionState.Connected));
        }

        [Test]
        public async Task SignalRConnection_ShouldConnect()
        {
            // Arrange & Act
            var connectionState = _hubConnection.State;

            // Assert
            Assert.That(connectionState, Is.EqualTo(HubConnectionState.Connected));
        }

        [Test]
        public async Task SignalRHub_ShouldReceiveEvents()
        {
            // Arrange
            var eventReceived = false;
            var receivedData = string.Empty;

            _hubConnection.On<string>("TestEvent", (data) =>
            {
                eventReceived = true;
                receivedData = data;
            });

            // Act
            try
            {
                await _hubConnection.InvokeAsync("TestMethod", "test-data");
            }
            catch (Exception ex)
            {
                // Expected - method doesn't exist, but connection works
                Assert.That(ex.Message, Does.Contain("does not exist").Or.Contain("HubException"));
            }

            // Assert
            Assert.That(_hubConnection.State, Is.EqualTo(HubConnectionState.Connected));
        }

        [Test]
        public async Task MessagingAPI_GetConversations_ShouldReturnUnauthorized()
        {
            // Arrange & Act
            var response = await _httpClient.GetAsync("/api/sonobrokers/messaging/conversations");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(System.Net.HttpStatusCode.Unauthorized));
        }

        [Test]
        public async Task MessagingAPI_SendMessage_ShouldReturnUnauthorized()
        {
            // Arrange
            var sendMessageRequest = new CreateMessageRequest
            {
                ConversationId = "test-conversation-123",
                Content = "Test message content",
                MessageType = "text"
            };

            // Act
            var response = await _httpClient.PostAsJsonAsync(
                "/api/sonobrokers/messaging/messages", 
                sendMessageRequest);

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(System.Net.HttpStatusCode.Unauthorized));
        }

        [Test]
        public async Task MessagingAPI_GetMessageStats_ShouldReturnUnauthorized()
        {
            // Act
            var response = await _httpClient.GetAsync("/api/sonobrokers/messaging/stats");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(System.Net.HttpStatusCode.Unauthorized));
        }

        [Test]
        public async Task AdminMessagingAPI_GetAllConversations_ShouldReturnUnauthorized()
        {
            // Act
            var response = await _httpClient.GetAsync("/api/admin/messaging/conversations");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(System.Net.HttpStatusCode.Unauthorized));
        }

        [Test]
        public async Task AdminMessagingAPI_GetStats_ShouldReturnUnauthorized()
        {
            // Act
            var response = await _httpClient.GetAsync("/api/admin/messaging/stats");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(System.Net.HttpStatusCode.Unauthorized));
        }

        [Test]
        public async Task MessagingAPI_InvalidEndpoint_ShouldReturnNotFound()
        {
            // Act
            var response = await _httpClient.GetAsync("/api/sonobrokers/messaging/invalid-endpoint");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(System.Net.HttpStatusCode.NotFound));
        }

        [Test]
        public async Task MessagingAPI_InvalidData_ShouldReturnBadRequest()
        {
            // Arrange
            var invalidRequest = new { InvalidProperty = "invalid-value" };

            // Act
            var response = await _httpClient.PostAsJsonAsync(
                "/api/sonobrokers/messaging/conversations", 
                invalidRequest);

            // Assert
            Assert.That(response.StatusCode, 
                Is.EqualTo(System.Net.HttpStatusCode.Unauthorized)
                .Or.EqualTo(System.Net.HttpStatusCode.BadRequest));
        }

        [Test]
        public async Task SignalRHub_MultipleConnections_ShouldWork()
        {
            // Arrange
            var secondConnection = new HubConnectionBuilder()
                .WithUrl($"{_httpClient.BaseAddress}messaginghub", options =>
                {
                    options.HttpMessageHandlerFactory = _ => _factory.Server.CreateHandler();
                    options.AccessTokenProvider = () => Task.FromResult("test-token-2");
                })
                .Build();

            try
            {
                // Act
                await secondConnection.StartAsync();

                // Assert
                Assert.That(_hubConnection.State, Is.EqualTo(HubConnectionState.Connected));
                Assert.That(secondConnection.State, Is.EqualTo(HubConnectionState.Connected));
            }
            finally
            {
                // Cleanup
                await secondConnection.DisposeAsync();
            }
        }

        [Test]
        public async Task MessagingAPI_HealthCheck_ShouldReturnOk()
        {
            // Act
            var response = await _httpClient.GetAsync("/health");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(System.Net.HttpStatusCode.OK));
        }

        [Test]
        public async Task MessagingAPI_Swagger_ShouldBeAccessible()
        {
            // Act
            var response = await _httpClient.GetAsync("/openapi/v1.json");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(System.Net.HttpStatusCode.OK));
            
            var content = await response.Content.ReadAsStringAsync();
            Assert.That(content, Does.Contain("SoNoBrokers API"));
        }
    }
}
