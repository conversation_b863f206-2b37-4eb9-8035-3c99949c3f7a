# SoNoBrokers React Application - Independent Docker Setup

This document explains how to run the SoNoBrokers React frontend application independently using Docker.

## 🏗️ Architecture

This setup runs **ONLY** the React/Next.js frontend application. The backend .NET Web API runs separately.

```
┌─────────────────────┐    HTTP Requests    ┌─────────────────────┐
│   React Frontend    │ ──────────────────► │   .NET Web API      │
│   (Port 3000)       │                     │   (Port 8080)       │
│   Docker Container  │                     │   Separate Container │
└─────────────────────┘                     └─────────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Docker Desktop for Windows
- Git
- 2GB+ available RAM

### 1. Setup Environment
```powershell
# Copy environment template
Copy-Item .env.docker .env.local

# Edit .env.local with your configuration
notepad .env.local
```

**Important**: Update `NEXT_PUBLIC_API_BASE_URL` to point to your running Web API:
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
```

### 2. Production Mode
```powershell
# Build and start
.\docker-run.ps1 build
.\docker-run.ps1 up

# Or combined
.\docker-run.ps1 build; .\docker-run.ps1 up
```

### 3. Development Mode (with Hot Reload)
```powershell
# Start development environment
.\docker-run.ps1 dev

# Or step by step
.\docker-run.ps1 build -Development
.\docker-run.ps1 up -Development
```

## 📋 Available Commands

### PowerShell Script Commands
```powershell
.\docker-run.ps1 build          # Build production image
.\docker-run.ps1 up             # Start container
.\docker-run.ps1 down           # Stop container
.\docker-run.ps1 restart        # Restart container
.\docker-run.ps1 logs           # View logs
.\docker-run.ps1 clean          # Clean up resources
.\docker-run.ps1 dev            # Development mode
.\docker-run.ps1 help           # Show help
```

### Docker Compose Commands
```powershell
# Production
docker-compose up -d                                    # Start
docker-compose down                                     # Stop
docker-compose logs -f frontend                         # View logs

# Development
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
docker-compose -f docker-compose.yml -f docker-compose.dev.yml down
```

## 🔧 Configuration

### Environment Variables (.env.local)

**Required Variables:**
```env
# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080

# Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_key
CLERK_SECRET_KEY=sk_test_your_key

# Database (if using Prisma directly)
DATABASE_URL=********************************/db
```

**Optional Variables:**
```env
# External APIs
NEXT_PUBLIC_GOOGLE_PLACES_API_KEY=your_key
NEXT_PUBLIC_MAPBOX_API_KEY=your_key
RESEND_API_KEY=your_key

# Feature Flags
NEXT_PUBLIC_ENABLE_LANGUAGE_SELECTOR=false
NEXT_PUBLIC_DEV_MODE=false
```

### Service URLs
- **Frontend**: http://localhost:3000
- **Health Check**: http://localhost:3000/api/health

## 🔍 Development Workflow

### Hot Reload Development
```powershell
# Start with hot reload
.\docker-run.ps1 dev

# Your code changes will automatically reload
# No need to rebuild the container
```

### Debugging
```powershell
# View real-time logs
.\docker-run.ps1 logs

# Access container shell
docker-compose exec frontend /bin/sh

# Check container status
docker-compose ps
```

## 🚨 Troubleshooting

### Common Issues

#### 1. API Connection Errors
**Problem**: Frontend can't connect to API
**Solution**: 
- Ensure Web API is running on port 8080
- Check `NEXT_PUBLIC_API_BASE_URL` in `.env.local`
- Verify CORS settings in Web API

#### 2. Port Already in Use
**Problem**: Port 3000 is already in use
**Solution**:
```env
# Change port in .env.local
FRONTEND_PORT=3001
```

#### 3. Build Failures
**Problem**: Docker build fails
**Solution**:
```powershell
# Clean build
.\docker-run.ps1 clean
.\docker-run.ps1 build -NoCache
```

#### 4. Environment Variables Not Loading
**Problem**: Environment variables not working
**Solution**:
- Ensure `.env.local` exists
- Restart container after changes
- Check variable names (must start with `NEXT_PUBLIC_` for client-side)

### Performance Issues
```powershell
# Check resource usage
docker stats sonobrokers-frontend

# Clean up unused resources
docker system prune -f
```

## 🔗 Integration with Web API

### API Communication
The React app communicates with the Web API via HTTP requests:

```typescript
// Example API call
const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/properties`);
```

### CORS Configuration
Ensure your Web API allows requests from the React app:

```csharp
// In Web API Startup.cs or Program.cs
services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp",
        builder => builder
            .WithOrigins("http://localhost:3000")
            .AllowAnyMethod()
            .AllowAnyHeader());
});
```

## 📚 Additional Information

### File Structure
```
SoNoBrokers/
├── Dockerfile                 # Multi-stage Docker build
├── docker-compose.yml         # Production setup
├── docker-compose.dev.yml     # Development overrides
├── .dockerignore              # Files to exclude from build
├── .env.docker               # Environment template
├── .env.local                # Your local configuration
├── docker-run.ps1            # Management script
└── DOCKER-README.md          # This file
```

### Best Practices
1. **Always use .env.local** for local configuration
2. **Never commit secrets** to version control
3. **Use development mode** for active development
4. **Clean up regularly** to save disk space
5. **Monitor logs** for debugging issues

### Next Steps
1. Set up the Web API independently
2. Configure proper environment variables
3. Test API connectivity
4. Deploy both applications to production

For Web API setup, see: `../SoNoBrokersWebApi/DOCKER-README.md`
