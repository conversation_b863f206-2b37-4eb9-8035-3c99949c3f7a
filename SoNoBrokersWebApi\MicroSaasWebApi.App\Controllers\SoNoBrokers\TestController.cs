using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.Core;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/sonobrokers/test")]
    [Tags("Testing")]
    public class TestController : ControllerBase
    {
        private readonly IDapperDbContext _dbContext;

        public TestController(IDapperDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        [HttpGet("health")]
        [AllowAnonymous]
        public async Task<ActionResult<ApiResponse<object>>> HealthCheck()
        {
            try
            {
                // Simple database connectivity test
                var canConnect = await _dbContext.CanConnectAsync();

                return Ok(ApiResponse<object>.SuccessResult(new
                {
                    DatabaseConnected = canConnect,
                    Timestamp = DateTime.UtcNow
                }, "SoNoBrokers API is healthy"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponse<object>.ErrorResult($"Health check failed: {ex.Message}"));
            }
        }

        [HttpGet("ping")]
        [AllowAnonymous]
        public ActionResult<ApiResponse<object>> Ping()
        {
            return Ok(ApiResponse<object>.SuccessResult(new
            {
                Message = "Pong! SoNoBrokers API is running",
                Timestamp = DateTime.UtcNow,
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                Version = "1.0.0"
            }, "API is responding"));
        }
    }
}
