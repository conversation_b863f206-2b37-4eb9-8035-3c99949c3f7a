# =============================================================================
# SoNoBrokers Web API - Docker Structure Verification Script
# =============================================================================
# Verifies that Docker files are properly organized and configured
# =============================================================================

Write-Host "Verifying SoNoBrokers Docker Structure..." -ForegroundColor Cyan
Write-Host "=" * 50

$rootPath = $PSScriptRoot
$errors = @()
$warnings = @()
$success = @()

# Function to check if file exists
function Test-FileExists {
    param($Path, $Description)
    if (Test-Path $Path) {
        $success += "[OK] $Description found: $Path"
        return $true
    } else {
        $errors += "[ERROR] $Description missing: $Path"
        return $false
    }
}

# Function to check if directory is clean
function Test-DirectoryClean {
    param($Path, $Pattern, $Description)
    $files = Get-ChildItem -Path $Path -Filter $Pattern -Force -ErrorAction SilentlyContinue
    if ($files.Count -eq 0) {
        $success += "[OK] ${Description}: No unwanted files in $Path"
        return $true
    } else {
        $warnings += "[WARNING] ${Description}: Found files in $Path - $($files.Name -join ', ')"
        return $false
    }
}

Write-Host "Checking Root Directory Structure..." -ForegroundColor Yellow

# Check that root is clean of Docker files (except master)
Test-DirectoryClean -Path $rootPath -Pattern "docker-compose.yml" -Description "Root docker-compose cleanup"
Test-DirectoryClean -Path $rootPath -Pattern "Dockerfile" -Description "Root Dockerfile cleanup"
Test-DirectoryClean -Path $rootPath -Pattern ".env*" -Description "Root .env cleanup"

# Check master orchestration file exists
Test-FileExists -Path "$rootPath\docker-compose.master.yml" -Description "Master Docker Compose"

Write-Host "`n🏗️ Checking MicroSaasWebApi.App Structure..." -ForegroundColor Yellow

$appPath = "$rootPath\MicroSaasWebApi.App"
Test-FileExists -Path "$appPath\Dockerfile" -Description "App Dockerfile"
Test-FileExists -Path "$appPath\docker-compose.yml" -Description "App Docker Compose"
Test-FileExists -Path "$appPath\docker-compose.dev.yml" -Description "App Dev Docker Compose"
Test-FileExists -Path "$appPath\.env" -Description "App Environment File"
Test-FileExists -Path "$appPath\.env.docker" -Description "App Docker Environment"

Write-Host "`n⚙️ Checking MicroSaasWebApi.Config Structure..." -ForegroundColor Yellow

$configPath = "$rootPath\MicroSaasWebApi.Config"
Test-FileExists -Path "$configPath\Dockerfile" -Description "Config Dockerfile"
Test-FileExists -Path "$configPath\docker-compose.yml" -Description "Config Docker Compose"

Write-Host "`n🧪 Checking MicroSaasWebApi.Tests Structure..." -ForegroundColor Yellow

$testsPath = "$rootPath\MicroSaasWebApi.Tests"
Test-FileExists -Path "$testsPath\Dockerfile" -Description "Tests Dockerfile"
Test-FileExists -Path "$testsPath\docker-compose.yml" -Description "Tests Docker Compose"

Write-Host "`n🔧 Checking CORS Configuration..." -ForegroundColor Yellow

$expectedCors = "https://localhost:3000,https://www.api.sonobrokers.com,https://sonobrokers.com,https://localhost:7163"

# Check App .env file for CORS
if (Test-Path "$appPath\.env") {
    $envContent = Get-Content "$appPath\.env" -Raw
    if ($envContent -match "CORS_ALLOWED_ORIGINS.*$expectedCors") {
        $success += "✅ App CORS configuration is correct"
    } else {
        $warnings += "⚠️ App CORS configuration may need updating"
    }
}

# Check master docker-compose for CORS
if (Test-Path "$rootPath\docker-compose.master.yml") {
    $masterContent = Get-Content "$rootPath\docker-compose.master.yml" -Raw
    if ($masterContent -match $expectedCors) {
        $success += "✅ Master Docker Compose CORS configuration is correct"
    } else {
        $warnings += "⚠️ Master Docker Compose CORS configuration may need updating"
    }
}

Write-Host "`n📊 Verification Results:" -ForegroundColor Cyan
Write-Host "=" * 30

if ($success.Count -gt 0) {
    Write-Host "`n✅ Successful Checks:" -ForegroundColor Green
    $success | ForEach-Object { Write-Host "  $_" -ForegroundColor Green }
}

if ($warnings.Count -gt 0) {
    Write-Host "`n⚠️ Warnings:" -ForegroundColor Yellow
    $warnings | ForEach-Object { Write-Host "  $_" -ForegroundColor Yellow }
}

if ($errors.Count -gt 0) {
    Write-Host "`n❌ Errors:" -ForegroundColor Red
    $errors | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
}

Write-Host "`n📈 Summary:" -ForegroundColor Cyan
Write-Host "  ✅ Success: $($success.Count)" -ForegroundColor Green
Write-Host "  ⚠️ Warnings: $($warnings.Count)" -ForegroundColor Yellow
Write-Host "  ❌ Errors: $($errors.Count)" -ForegroundColor Red

if ($errors.Count -eq 0) {
    Write-Host "`n🎉 Docker structure verification completed successfully!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n💥 Docker structure verification failed. Please fix the errors above." -ForegroundColor Red
    exit 1
}
