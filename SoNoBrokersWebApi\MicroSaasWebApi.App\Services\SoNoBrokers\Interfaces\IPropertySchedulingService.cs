using MicroSaasWebApi.Models.SoNoBrokers.PropertyScheduling;

namespace MicroSaasWebApi.Services.SoNoBrokers.Interfaces
{
    /// <summary>
    /// Service interface for property visit scheduling and management
    /// </summary>
    public interface IPropertySchedulingService
    {
        // Seller Availability Management
        Task<SellerAvailabilityResponse> CreateSellerAvailabilityAsync(string sellerId, CreateSellerAvailabilityRequest request);
        Task<List<SellerAvailabilityResponse>> GetSellerAvailabilityAsync(string sellerId, string? propertyId = null);
        Task<SellerAvailabilityResponse?> GetSellerAvailabilityByIdAsync(string availabilityId);
        Task<SellerAvailabilityResponse?> UpdateSellerAvailabilityAsync(string availabilityId, UpdateSellerAvailabilityRequest request);
        Task<bool> DeleteSellerAvailabilityAsync(string availabilityId);
        Task<List<SellerAvailabilityResponse>> GetPropertyAvailabilityAsync(string propertyId);

        // Visit Scheduling
        Task<PropertyVisitScheduleResponse> CreateVisitScheduleAsync(string buyerId, CreateVisitScheduleRequest request);
        Task<PropertyVisitScheduleResponse?> GetVisitScheduleAsync(string visitId);
        Task<VisitScheduleSearchResponse> GetVisitSchedulesAsync(string userId, VisitScheduleSearchParams searchParams);
        Task<VisitScheduleSearchResponse> GetPropertyVisitSchedulesAsync(string propertyId, VisitScheduleSearchParams searchParams);
        Task<PropertyVisitScheduleResponse?> RespondToVisitRequestAsync(string visitId, RespondToVisitRequest request);
        Task<bool> CancelVisitScheduleAsync(string visitId, string reason);
        Task<bool> MarkVisitCompletedAsync(string visitId);

        // QR Code Management
        Task<PropertyQrCodeResponse> GeneratePropertyQrCodeAsync(string propertyId, string sellerId);
        Task<PropertyQrCodeResponse?> GetPropertyQrCodeAsync(string propertyId);
        Task<bool> RegeneratePropertyQrCodeAsync(string propertyId, string sellerId);
        Task<bool> DeactivatePropertyQrCodeAsync(string propertyId, string sellerId);

        // Visit Verification
        Task<VisitVerificationResponse> VerifyVisitAsync(string visitId, VerifyVisitRequest request);
        Task<List<VisitVerificationResponse>> GetVisitVerificationsAsync(string visitId);
        Task<bool> IsQrCodeValidAsync(string qrCodeData);

        // Calendar Integration
        Task<bool> SendCalendarInviteAsync(string visitId);
        Task<bool> UpdateCalendarInviteAsync(string visitId);
        Task<bool> CancelCalendarInviteAsync(string visitId);

        // Access Control
        Task<bool> CanUserAccessVisitScheduleAsync(string userId, string visitId);
        Task<bool> CanUserManagePropertySchedulingAsync(string userId, string propertyId);

        // Statistics and Analytics
        Task<VisitSchedulingStats> GetVisitSchedulingStatsAsync(string userId);
        Task<VisitSchedulingStats> GetPropertyVisitStatsAsync(string propertyId);
        Task<VisitSchedulingStats> GetAdminVisitStatsAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Notifications
        Task<bool> SendVisitRequestNotificationAsync(string visitId);
        Task<bool> SendVisitConfirmationNotificationAsync(string visitId);
        Task<bool> SendVisitReminderNotificationAsync(string visitId);
        Task<bool> SendVisitCancellationNotificationAsync(string visitId, string reason);

        // Bulk Operations
        Task<bool> SetWeeklyAvailabilityAsync(string sellerId, string propertyId, List<CreateSellerAvailabilityRequest> availability);
        Task<List<PropertyVisitScheduleResponse>> GetUpcomingVisitsAsync(string userId, int days = 7);
        Task<bool> SendDailyVisitRemindersAsync();
    }

    /// <summary>
    /// Statistics model for visit scheduling
    /// </summary>
    public class VisitSchedulingStats
    {
        public int TotalVisitRequests { get; set; }
        public int PendingRequests { get; set; }
        public int ConfirmedVisits { get; set; }
        public int CompletedVisits { get; set; }
        public int CancelledVisits { get; set; }
        public int NoShowVisits { get; set; }
        public int VerifiedVisits { get; set; }
        public double ConfirmationRate { get; set; }
        public double CompletionRate { get; set; }
        public double VerificationRate { get; set; }
        public DateTime? LastVisitRequest { get; set; }
        public DateTime? NextScheduledVisit { get; set; }
        public List<PropertyVisitStats> PropertyStats { get; set; } = new();
        public List<DailyVisitStats> DailyStats { get; set; } = new();
    }

    public class PropertyVisitStats
    {
        public string PropertyId { get; set; } = string.Empty;
        public string PropertyTitle { get; set; } = string.Empty;
        public int VisitRequests { get; set; }
        public int ConfirmedVisits { get; set; }
        public int CompletedVisits { get; set; }
        public DateTime? LastVisit { get; set; }
        public DateTime? NextVisit { get; set; }
    }

    public class DailyVisitStats
    {
        public DateTime Date { get; set; }
        public int Requests { get; set; }
        public int Confirmations { get; set; }
        public int Completions { get; set; }
        public int Cancellations { get; set; }
    }

    /// <summary>
    /// Calendar invite service interface
    /// </summary>
    public interface ICalendarInviteService
    {
        Task<bool> SendInviteAsync(string visitId, PropertyVisitScheduleResponse visit);
        Task<bool> UpdateInviteAsync(string visitId, PropertyVisitScheduleResponse visit);
        Task<bool> CancelInviteAsync(string visitId, PropertyVisitScheduleResponse visit);
        Task<string> GenerateIcsFileAsync(PropertyVisitScheduleResponse visit);
    }

    /// <summary>
    /// QR code service interface
    /// </summary>
    public interface IQrCodeService
    {
        Task<string> GenerateQrCodeAsync(string data);
        Task<byte[]> GenerateQrCodeImageAsync(string data);
        Task<bool> ValidateQrCodeAsync(string qrCodeData, string expectedData);
        string EncryptQrCodeData(string propertyId, string sellerId, DateTime expiresAt);
        QrCodeData? DecryptQrCodeData(string encryptedData);
    }

    public class QrCodeData
    {
        public string PropertyId { get; set; } = string.Empty;
        public string SellerId { get; set; } = string.Empty;
        public DateTime ExpiresAt { get; set; }
        public string Signature { get; set; } = string.Empty;
    }
}
