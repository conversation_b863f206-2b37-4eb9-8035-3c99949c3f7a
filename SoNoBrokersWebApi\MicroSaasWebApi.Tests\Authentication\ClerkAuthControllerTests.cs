using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using MicroSaasWebApi.Controllers.Core;
using MicroSaasWebApi.Models.Auth;
using MicroSaasWebApi.Models.Auth.Clerk;
using MicroSaasWebApi.Services.Auth.Interfaces;
using MicroSaasWebApi.Tests.Common;
using MicroSaasWebApi.Tests.Mocks;
using Moq;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Authentication
{
    public class ClerkAuthControllerTests : IClassFixture<TestWebApplicationFactory<Program>>, IAsyncLifetime
    {
        private readonly TestWebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;
        private readonly ITestOutputHelper _output;
        private readonly Mock<IClerkAuthService> _mockClerkAuthService;

        public ClerkAuthControllerTests(TestWebApplicationFactory<Program> factory, ITestOutputHelper output)
        {
            _factory = factory;
            _output = output;
            _mockClerkAuthService = MockServices.CreateMockClerkAuthService();
            
            _client = _factory.WithWebHostBuilder(builder =>
            {
                builder.ConfigureServices(services =>
                {
                    // Replace the real service with mock
                    var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(IClerkAuthService));
                    if (descriptor != null)
                    {
                        services.Remove(descriptor);
                    }
                    services.AddSingleton(_mockClerkAuthService.Object);
                });
            }).CreateClient();
        }

        public async Task InitializeAsync()
        {
            await _factory.SeedTestDataAsync();
        }

        public async Task DisposeAsync()
        {
            await _factory.CleanupTestDataAsync();
        }

        [Fact]
        public async Task Login_WithValidCredentials_ReturnsSuccessResponse()
        {
            // Arrange
            var request = new LoginRequest
            {
                Email = "<EMAIL>",
                Password = "TestPassword123!"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/clerk-auth/login", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AuthResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Token.Should().NotBeNullOrEmpty();
            result.User.Should().NotBeNull();
            result.User!.Email.Should().Be(request.Email);
        }

        [Fact]
        public async Task Login_WithInvalidCredentials_ReturnsUnauthorized()
        {
            // Arrange
            var request = new LoginRequest
            {
                Email = "<EMAIL>",
                Password = "WrongPassword"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/clerk-auth/login", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        }

        [Fact]
        public async Task Login_WithMissingEmail_ReturnsBadRequest()
        {
            // Arrange
            var request = new LoginRequest
            {
                Email = "",
                Password = "TestPassword123!"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/clerk-auth/login", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task Login_WithMissingPassword_ReturnsBadRequest()
        {
            // Arrange
            var request = new LoginRequest
            {
                Email = "<EMAIL>",
                Password = ""
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/clerk-auth/login", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task Register_WithValidData_ReturnsCreatedResponse()
        {
            // Arrange
            var request = new RegisterRequest
            {
                Email = "<EMAIL>",
                Password = "NewPassword123!",
                FirstName = "New",
                LastName = "User"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/clerk-auth/register", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Created);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AuthResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Token.Should().NotBeNullOrEmpty();
            result.User.Should().NotBeNull();
            result.User!.Email.Should().Be(request.Email);
            result.User.FirstName.Should().Be(request.FirstName);
            result.User.LastName.Should().Be(request.LastName);
        }

        [Fact]
        public async Task Register_WithExistingEmail_ReturnsBadRequest()
        {
            // Arrange
            var request = new RegisterRequest
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                FirstName = "Test",
                LastName = "User"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/clerk-auth/register", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Theory]
        [InlineData("", "Password123!", "First", "Last")]
        [InlineData("<EMAIL>", "", "First", "Last")]
        [InlineData("<EMAIL>", "Password123!", "", "Last")]
        [InlineData("<EMAIL>", "Password123!", "First", "")]
        [InlineData("invalid-email", "Password123!", "First", "Last")]
        [InlineData("<EMAIL>", "123", "First", "Last")] // Password too short
        public async Task Register_WithInvalidData_ReturnsBadRequest(string email, string password, string firstName, string lastName)
        {
            // Arrange
            var request = new RegisterRequest
            {
                Email = email,
                Password = password,
                FirstName = firstName,
                LastName = lastName
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/clerk-auth/register", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task RefreshToken_WithValidToken_ReturnsNewToken()
        {
            // Arrange
            var request = new RefreshTokenRequest
            {
                RefreshToken = "valid-refresh-token"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/clerk-auth/refresh-token", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AuthResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Token.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task RefreshToken_WithInvalidToken_ReturnsUnauthorized()
        {
            // Arrange
            var request = new RefreshTokenRequest
            {
                RefreshToken = ""
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/clerk-auth/refresh-token", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task Logout_WithValidAuth_ReturnsSuccess()
        {
            // Arrange
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

            // Act
            var response = await _client.PostAsync("/api/clerk-auth/logout", null);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        [Fact]
        public async Task Logout_WithoutAuth_ReturnsSuccess()
        {
            // Act - Logout should work even without auth (user already logged out)
            var response = await _client.PostAsync("/api/clerk-auth/logout", null);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        [Fact]
        public async Task GetProfile_WithValidAuth_ReturnsUserProfile()
        {
            // Arrange
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

            // Act
            var response = await _client.GetAsync("/api/clerk-auth/profile");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<UserInfo>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Email.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task GetProfile_WithoutAuth_ReturnsUnauthorized()
        {
            // Act
            var response = await _client.GetAsync("/api/clerk-auth/profile");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        }

        [Fact]
        public async Task UpdateProfile_WithValidAuth_ReturnsUpdatedProfile()
        {
            // Arrange
            var request = new
            {
                FirstName = "Updated",
                LastName = "Name"
            };

            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

            // Act
            var response = await _client.PutAsJsonAsync("/api/clerk-auth/profile", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<UserInfo>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
        }

        [Fact]
        public async Task UpdateProfile_WithoutAuth_ReturnsUnauthorized()
        {
            // Arrange
            var request = new
            {
                FirstName = "Updated",
                LastName = "Name"
            };

            // Act
            var response = await _client.PutAsJsonAsync("/api/clerk-auth/profile", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        }

        [Fact]
        public async Task ValidateAuth_WithValidToken_ReturnsValid()
        {
            // Arrange
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

            // Act
            var response = await _client.GetAsync("/api/clerk-auth/validate");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        [Fact]
        public async Task ValidateAuth_WithoutToken_ReturnsUnauthorized()
        {
            // Act
            var response = await _client.GetAsync("/api/clerk-auth/validate");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        }

        [Fact]
        public async Task AuthEndpoints_ArePubliclyAccessible()
        {
            // Arrange & Act
            var loginResponse = await _client.PostAsJsonAsync("/api/clerk-auth/login", new LoginRequest 
            { 
                Email = "<EMAIL>", 
                Password = "TestPassword123!" 
            });
            
            var registerResponse = await _client.PostAsJsonAsync("/api/clerk-auth/register", new RegisterRequest 
            { 
                Email = "<EMAIL>", 
                Password = "Password123!", 
                FirstName = "Test", 
                LastName = "User" 
            });
            
            var logoutResponse = await _client.PostAsync("/api/clerk-auth/logout", null);
            
            var refreshResponse = await _client.PostAsJsonAsync("/api/clerk-auth/refresh-token", new RefreshTokenRequest 
            { 
                RefreshToken = "test-token" 
            });

            // Assert - These endpoints should be accessible without authentication
            loginResponse.StatusCode.Should().NotBe(HttpStatusCode.Unauthorized);
            registerResponse.StatusCode.Should().NotBe(HttpStatusCode.Unauthorized);
            logoutResponse.StatusCode.Should().NotBe(HttpStatusCode.Unauthorized);
            refreshResponse.StatusCode.Should().NotBe(HttpStatusCode.Unauthorized);
        }

        [Fact]
        public async Task Login_WithServiceException_ReturnsInternalServerError()
        {
            // Arrange
            _mockClerkAuthService.Setup(x => x.LoginAsync(It.IsAny<LoginRequest>()))
                .ThrowsAsync(new Exception("Service unavailable"));

            var request = new LoginRequest
            {
                Email = "<EMAIL>",
                Password = "TestPassword123!"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/clerk-auth/login", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.InternalServerError);
        }
    }
}
