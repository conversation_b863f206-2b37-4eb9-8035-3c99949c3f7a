-- Migration: 0005_create_indexes.sql
-- Description: Create database indexes for performance optimization
-- Date: 2024-12-25
-- Author: Migration System
-- Dependencies: 0004_create_contact_scheduling_tables.sql

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- User indexes
CREATE INDEX IF NOT EXISTS "idx_user_clerk_id" ON public."User"("clerkId");
CREATE INDEX IF NOT EXISTS "idx_user_email" ON public."User"(email);
CREATE INDEX IF NOT EXISTS "idx_user_role" ON public."User"(role);
CREATE INDEX IF NOT EXISTS "idx_user_status" ON public."User"(status);

-- Property indexes
CREATE INDEX IF NOT EXISTS "idx_property_seller" ON public."Property"("sellerId");
CREATE INDEX IF NOT EXISTS "idx_property_type" ON public."Property"("propertyType");
CREATE INDEX IF NOT EXISTS "idx_property_status" ON public."Property"(status);
CREATE INDEX IF NOT EXISTS "idx_property_listing_type" ON public."Property"("listingType");
CREATE INDEX IF NOT EXISTS "idx_property_price" ON public."Property"(price);
CREATE INDEX IF NOT EXISTS "idx_property_bedrooms" ON public."Property"(bedrooms);
CREATE INDEX IF NOT EXISTS "idx_property_bathrooms" ON public."Property"(bathrooms);
CREATE INDEX IF NOT EXISTS "idx_property_location" ON public."Property" USING GIST("geoLocation");
CREATE INDEX IF NOT EXISTS "idx_property_listing_date" ON public."Property"("listingDate");
CREATE INDEX IF NOT EXISTS "idx_property_active" ON public."Property"("isActive");
CREATE INDEX IF NOT EXISTS "idx_property_featured" ON public."Property"("isFeatured");

-- Property Image indexes
CREATE INDEX IF NOT EXISTS "idx_property_image_property" ON public."PropertyImage"("propertyId");
CREATE INDEX IF NOT EXISTS "idx_property_image_main" ON public."PropertyImage"("isMain");

-- Conversation indexes
CREATE INDEX IF NOT EXISTS "idx_conversation_property" ON public."Conversation"("propertyId");
CREATE INDEX IF NOT EXISTS "idx_conversation_buyer" ON public."Conversation"("buyerId");
CREATE INDEX IF NOT EXISTS "idx_conversation_seller" ON public."Conversation"("sellerId");
CREATE INDEX IF NOT EXISTS "idx_conversation_active" ON public."Conversation"("isActive");

-- Message indexes
CREATE INDEX IF NOT EXISTS "idx_message_conversation" ON public."Message"("conversationId");
CREATE INDEX IF NOT EXISTS "idx_message_sender" ON public."Message"("senderId");
CREATE INDEX IF NOT EXISTS "idx_message_receiver" ON public."Message"("receiverId");
CREATE INDEX IF NOT EXISTS "idx_message_created" ON public."Message"("createdAt");
CREATE INDEX IF NOT EXISTS "idx_message_read" ON public."Message"("isRead");

-- Buyer Offer indexes
CREATE INDEX IF NOT EXISTS "idx_buyer_offer_property" ON public."BuyerOffer"("propertyId");
CREATE INDEX IF NOT EXISTS "idx_buyer_offer_buyer" ON public."BuyerOffer"("buyerId");
CREATE INDEX IF NOT EXISTS "idx_buyer_offer_seller" ON public."BuyerOffer"("sellerId");
CREATE INDEX IF NOT EXISTS "idx_buyer_offer_status" ON public."BuyerOffer"(status);

-- Property Viewing indexes
CREATE INDEX IF NOT EXISTS "idx_property_viewing_property" ON public."PropertyViewing"("propertyId");
CREATE INDEX IF NOT EXISTS "idx_property_viewing_buyer" ON public."PropertyViewing"("buyerId");
CREATE INDEX IF NOT EXISTS "idx_property_viewing_seller" ON public."PropertyViewing"("sellerId");
CREATE INDEX IF NOT EXISTS "idx_property_viewing_date" ON public."PropertyViewing"("scheduledDate");

-- Subscription indexes
CREATE INDEX IF NOT EXISTS "idx_subscription_user" ON public."SubscriptionSnb"("userId");
CREATE INDEX IF NOT EXISTS "idx_subscription_status" ON public."SubscriptionSnb"(status);
CREATE INDEX IF NOT EXISTS "idx_subscription_type" ON public."SubscriptionSnb"(type);

-- Advertiser indexes
CREATE INDEX IF NOT EXISTS "idx_advertiser_user" ON public."Advertiser"("userId");
CREATE INDEX IF NOT EXISTS "idx_advertiser_verified" ON public."Advertiser"("isVerified");
CREATE INDEX IF NOT EXISTS "idx_advertiser_active" ON public."Advertiser"("isActive");

-- Service Provider indexes
CREATE INDEX IF NOT EXISTS "idx_service_provider_user" ON public."ServiceProvider"("userId");
CREATE INDEX IF NOT EXISTS "idx_service_provider_service_type" ON public."ServiceProvider"("serviceType");
CREATE INDEX IF NOT EXISTS "idx_service_provider_verified" ON public."ServiceProvider"("isVerified");
CREATE INDEX IF NOT EXISTS "idx_service_provider_active" ON public."ServiceProvider"("isActive");
CREATE INDEX IF NOT EXISTS "idx_service_provider_rating" ON public."ServiceProvider"(rating);

-- Service Booking indexes
CREATE INDEX IF NOT EXISTS "idx_service_booking_provider" ON public."ServiceBooking"("serviceProviderId");
CREATE INDEX IF NOT EXISTS "idx_service_booking_customer" ON public."ServiceBooking"("customerId");
CREATE INDEX IF NOT EXISTS "idx_service_booking_property" ON public."ServiceBooking"("propertyId");
CREATE INDEX IF NOT EXISTS "idx_service_booking_date" ON public."ServiceBooking"("bookingDate");
CREATE INDEX IF NOT EXISTS "idx_service_booking_status" ON public."ServiceBooking"(status);

-- Contact Share indexes
CREATE INDEX IF NOT EXISTS "idx_contactshare_property" ON public."ContactShare"("propertyId");
CREATE INDEX IF NOT EXISTS "idx_contactshare_buyer" ON public."ContactShare"("buyerId");
CREATE INDEX IF NOT EXISTS "idx_contactshare_seller" ON public."ContactShare"("sellerId");
CREATE INDEX IF NOT EXISTS "idx_contactshare_status" ON public."ContactShare"(status);
CREATE INDEX IF NOT EXISTS "idx_contactshare_sharetype" ON public."ContactShare"("shareType");
CREATE INDEX IF NOT EXISTS "idx_contactshare_created" ON public."ContactShare"("createdAt");
CREATE INDEX IF NOT EXISTS "idx_contactshare_email_sent" ON public."ContactShare"("emailSent", "emailSentAt");

-- Seller Availability indexes
CREATE INDEX IF NOT EXISTS "idx_selleravailability_property" ON public."SellerAvailability"("propertyId");
CREATE INDEX IF NOT EXISTS "idx_selleravailability_seller" ON public."SellerAvailability"("sellerId");
CREATE INDEX IF NOT EXISTS "idx_selleravailability_day" ON public."SellerAvailability"("dayOfWeek");
CREATE INDEX IF NOT EXISTS "idx_selleravailability_available" ON public."SellerAvailability"("isAvailable");

-- Property Visit Schedule indexes
CREATE INDEX IF NOT EXISTS "idx_propertyvisitschedule_property" ON public."PropertyVisitSchedule"("propertyId");
CREATE INDEX IF NOT EXISTS "idx_propertyvisitschedule_buyer" ON public."PropertyVisitSchedule"("buyerId");
CREATE INDEX IF NOT EXISTS "idx_propertyvisitschedule_seller" ON public."PropertyVisitSchedule"("sellerId");
CREATE INDEX IF NOT EXISTS "idx_propertyvisitschedule_status" ON public."PropertyVisitSchedule"(status);
CREATE INDEX IF NOT EXISTS "idx_propertyvisitschedule_requested_date" ON public."PropertyVisitSchedule"("requestedDate");
CREATE INDEX IF NOT EXISTS "idx_propertyvisitschedule_confirmed_date" ON public."PropertyVisitSchedule"("confirmedDate");
CREATE INDEX IF NOT EXISTS "idx_propertyvisitschedule_contact_share" ON public."PropertyVisitSchedule"("contactShareId");

-- Property QR Code indexes
CREATE INDEX IF NOT EXISTS "idx_propertyqrcode_property" ON public."PropertyQrCode"("propertyId");
CREATE INDEX IF NOT EXISTS "idx_propertyqrcode_seller" ON public."PropertyQrCode"("sellerId");
CREATE INDEX IF NOT EXISTS "idx_propertyqrcode_active" ON public."PropertyQrCode"("isActive");
CREATE INDEX IF NOT EXISTS "idx_propertyqrcode_expires" ON public."PropertyQrCode"("expiresAt");

-- Visit Verification indexes
CREATE INDEX IF NOT EXISTS "idx_visitverification_visit" ON public."VisitVerification"("visitScheduleId");
CREATE INDEX IF NOT EXISTS "idx_visitverification_property" ON public."VisitVerification"("propertyId");
CREATE INDEX IF NOT EXISTS "idx_visitverification_buyer" ON public."VisitVerification"("buyerId");
CREATE INDEX IF NOT EXISTS "idx_visitverification_verified_at" ON public."VisitVerification"("verifiedAt");
CREATE INDEX IF NOT EXISTS "idx_visitverification_method" ON public."VisitVerification"(method);

-- Stripe indexes
CREATE INDEX IF NOT EXISTS "idx_stripe_customers_user" ON public."stripe_customers"("user_id");
CREATE INDEX IF NOT EXISTS "idx_stripe_customers_stripe_id" ON public."stripe_customers"("stripe_customer_id");

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ Migration 0005_create_indexes.sql completed successfully';
    RAISE NOTICE 'Created 50+ performance indexes for:';
    RAISE NOTICE '- User tables (clerk_id, email, role, status)';
    RAISE NOTICE '- Property tables (seller, type, status, price, location)';
    RAISE NOTICE '- Conversation and messaging tables';
    RAISE NOTICE '- Subscription and advertiser tables';
    RAISE NOTICE '- Service provider and booking tables';
    RAISE NOTICE '- Contact sharing and scheduling tables';
    RAISE NOTICE '- Stripe payment integration tables';
    RAISE NOTICE '';
END $$;
