'use client'

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from '@/components/ui/dropdown-menu';
import { useState } from 'react';
import { PropertyAnalytics } from '@/components/_archive/analytics/PropertyAnalytics';
import { UserAnalytics } from '@/components/_archive/analytics/UserAnalytics';
import { MarketTrends } from '@/components/_archive/analytics/MarketTrends';
import { HeroSection } from '@/components/country-specific/ca/HeroSection'
import { HowItWorks } from '@/components/country-specific/ca/HowItWorks'
import { FeaturedProperties } from '@/components/country-specific/ca/FeaturedProperties'
import { useAppContext } from '@/contexts/AppContext'

interface DashboardProps {
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
}

export function Dashboard({ user }: DashboardProps) {
  const [view, setView] = useState<'all' | 'active' | 'pending'>('all');
  const { isSignedIn } = useAppContext();
  const userType = user.role === 'seller' ? 'seller' : 'buyer';

  // Sample data for analytics components
  const propertyAnalyticsData = {
    views: 1234,
    saves: 87,
    inquiries: 23,
    averagePrice: 450000,
    marketTrend: 'up' as const,
    daysOnMarket: 28,
    similarProperties: [
      { id: '1', title: 'Modern Apartment in Downtown', price: 425000, status: 'active' as const },
      { id: '2', title: 'Suburban Family Home', price: 520000, status: 'pending' as const },
      { id: '3', title: 'Luxury Condo with Ocean View', price: 750000, status: 'sold' as const },
    ]
  };

  const userAnalyticsData = {
    totalProperties: 12,
    activeListings: 8,
    totalViews: 1234,
    totalInquiries: 23,
    averageResponseTime: 45,
    recentActivity: [
      {
        id: '1',
        type: 'view' as const,
        timestamp: new Date().toISOString(),
        propertyId: '1',
        propertyTitle: 'Modern Apartment in Downtown'
      },
      {
        id: '2',
        type: 'inquiry' as const,
        timestamp: new Date().toISOString(),
        propertyId: '2',
        propertyTitle: 'Suburban Family Home',
        message: 'Is this property still available?'
      },
      {
        id: '3',
        type: 'property_updated' as const,
        timestamp: new Date().toISOString(),
        propertyId: '3',
        propertyTitle: 'Luxury Condo with Ocean View'
      }
    ]
  };

  const marketTrendsData = {
    averagePrice: 450000,
    priceChange: 5.2,
    totalListings: 1245,
    listingsChange: 3.8,
    averageDaysOnMarket: 32,
    daysOnMarketChange: -2.5,
    topLocations: [
      { location: 'Downtown', averagePrice: 520000, totalListings: 245, priceChange: 6.7 },
      { location: 'Westside', averagePrice: 480000, totalListings: 187, priceChange: 4.2 },
      { location: 'Eastside', averagePrice: 390000, totalListings: 210, priceChange: 3.5 }
    ],
    propertyTypes: [
      { type: 'Apartment', count: 520, averagePrice: 380000, priceChange: 4.8 },
      { type: 'House', count: 430, averagePrice: 520000, priceChange: 5.6 },
      { type: 'Condo', count: 295, averagePrice: 450000, priceChange: 6.2 }
    ]
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6 bg-background text-foreground">
      <HeroSection userType={userType} />
      <HowItWorks userType={userType} />
      <FeaturedProperties userType={userType} />
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-8 px-1 cursor-pointer">
              View: {view.charAt(0).toUpperCase() + view.slice(1)}
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-background border border-border shadow-lg backdrop-blur-none z-[9999]" style={{ backgroundColor: 'var(--background)', background: 'var(--background)' }}>
              <DropdownMenuRadioGroup value={view} onValueChange={(value) => setView(value as any)}>
                <DropdownMenuRadioItem value="all" className="bg-background hover:bg-accent hover:text-accent-foreground cursor-pointer" style={{ backgroundColor: 'var(--background)', background: 'var(--background)' }}>All Properties</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="active" className="bg-background hover:bg-accent hover:text-accent-foreground cursor-pointer" style={{ backgroundColor: 'var(--background)', background: 'var(--background)' }}>Active Listings</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="pending" className="bg-background hover:bg-accent hover:text-accent-foreground cursor-pointer" style={{ backgroundColor: 'var(--background)', background: 'var(--background)' }}>Pending Approval</DropdownMenuRadioItem>
              </DropdownMenuRadioGroup>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button>Add New Property</Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="messages">Messages</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Properties</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">12</div>
                <p className="text-xs text-muted-foreground">+2 from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Listings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">8</div>
                <p className="text-xs text-muted-foreground">+1 from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Views</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1,234</div>
                <p className="text-xs text-muted-foreground">+24% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Inquiries</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">23</div>
                <p className="text-xs text-muted-foreground">+7 from last month</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Property Analytics</CardTitle>
              <CardDescription>
                View detailed statistics about your property listings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PropertyAnalytics analytics={propertyAnalyticsData} />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>User Analytics</CardTitle>
              <CardDescription>
                Track your account activity and engagement
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserAnalytics analytics={userAnalyticsData} />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Market Trends</CardTitle>
              <CardDescription>
                Analyze current real estate market conditions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <MarketTrends trends={marketTrendsData} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="messages" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Messages</CardTitle>
              <CardDescription>
                Manage your conversations with potential buyers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>Messages content will go here</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Settings</CardTitle>
              <CardDescription>
                Manage your account settings and preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>Settings content will go here</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
