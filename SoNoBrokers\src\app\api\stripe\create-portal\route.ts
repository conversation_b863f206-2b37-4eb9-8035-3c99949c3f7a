import { NextResponse, NextRequest } from "next/server";
import { stripeService } from "@/lib/stripe";
// TODO: Migrate to .NET Web API - temporarily disabled
// import prisma from "@/lib/prisma";
import { auth } from '@clerk/nextjs/server'


export async function POST(req: NextRequest) {
  // TODO: Migrate to .NET Web API
  return NextResponse.json(
    { error: 'API route temporarily disabled - migrating to .NET Web API' },
    { status: 503 }
  )
}

/* DISABLED - MIGRATE TO .NET WEB API
export async function POST_DISABLED(req: NextRequest) {
  const { userId }: { userId: string | null } = await auth()

  if(!userId) {
    return NextResponse.json(
      {
        error: "Not signed in",
      },
      {
        status: 401,
      }
    );
  }

  try {

    const subscription = await prisma.subscriptionSnb.findFirst({
      where: {
        userId: userId,
      },
    });

    if (!subscription?.stripeSubscriptionId) {
      return NextResponse.json(
        {
          error:
            "You don't have a billing account yet. Make a purchase first.",
        },
        {
          status: 400,
        }
      );
    }

    const returnUrl = `${req.headers.get('origin')}/dashboard`

    const stripePortalUrl = await stripeService.createCustomerPortal(subscription.stripeSubscriptionId, returnUrl);

    return NextResponse.json({
      url: stripePortalUrl,
    });
  } catch (e) {
    console.error(e);

    return NextResponse.json(
      {
        error: e?.message,
      },
      {
        status: 500,
      }
    );
  }

}
*/
