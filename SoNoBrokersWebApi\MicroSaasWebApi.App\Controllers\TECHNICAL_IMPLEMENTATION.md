# Technical Implementation Guide

## Migration Summary: Entity Framework → DapperDbContext

### ✅ COMPLETED MIGRATION STATUS
**All 13 SoNoBrokers controllers and services successfully migrated from Entity Framework to DapperDbContext**

## Core Stripe Controllers

### StripePaymentsController Implementation

```csharp
namespace MicroSaasWebApi.Controllers.Core.Stripe
{
    [Route("api/stripe/payments")]
    [ApiController]
    [Authorize]
    public class StripePaymentsController : ControllerBase
    {
        private readonly IStripePaymentService _stripeService;
        private readonly ILogger<StripePaymentsController> _logger;
        private readonly DapperDbContext _dbContext;
        private readonly IConfiguration _configuration;
    }
}
```

#### Key Features:
- **Unified Payment Handling:** Both one-time payments and subscriptions
- **SoNoBrokers Integration:** Specialized endpoints for the platform
- **Database Integration:** Direct Supabase queries via DapperDbContext
- **Security:** Full authorization and input validation

#### Critical Endpoints:

**SoNoBrokers Checkout Session:**
```csharp
[HttpPost("sonobrokers/create-checkout")]
public async Task<ActionResult<ApiResponse<SoNoBrokersCheckoutSessionResponse>>> 
    CreateSoNoBrokersCheckoutSession([FromBody] SoNoBrokersCreateCheckoutRequest request)
```

**Subscription Status Check:**
```csharp
[HttpGet("sonobrokers/subscription-status")]
public async Task<ActionResult<ApiResponse<SoNoBrokersSubscriptionStatusResponse>>> 
    GetSoNoBrokersSubscriptionStatus()
```

### StripeWebhookController Implementation

```csharp
namespace MicroSaasWebApi.Controllers.Core.Stripe
{
    [Route("api/stripe/webhooks")]
    [ApiController]
    public class StripeWebhookController : ControllerBase
    {
        private readonly DapperDbContext _dbContext;
        private readonly ILogger<StripeWebhookController> _logger;
        private readonly IConfiguration _configuration;
    }
}
```

#### Webhook Event Handling:

**Subscription Lifecycle:**
```csharp
// Handles: checkout.session.completed, customer.subscription.updated, customer.subscription.deleted
private async Task HandleCheckoutSessionCompleted(Event stripeEvent)
private async Task HandleSubscriptionUpdated(Event stripeEvent)
private async Task HandleSubscriptionDeleted(Event stripeEvent)
```

**Payment Processing:**
```csharp
// Handles: payment_intent.succeeded, payment_intent.payment_failed
private async Task HandlePaymentIntentSucceeded(Event stripeEvent)
private async Task HandlePaymentIntentFailed(Event stripeEvent)
```

**Database Operations Example:**
```csharp
const string sql = @"
    SELECT * FROM snb.subscriptions
    WHERE stripe_subscription_id = @stripeSubscriptionId";

var subscription = await _dbContext.QueryFirstOrDefaultAsync<Models.SoNoBrokers.Subscription>(
    sql, new { stripeSubscriptionId = stripeSubscription.Id });
```

## SoNoBrokers Controllers - DapperDbContext Implementation

### Database Context Pattern

All SoNoBrokers controllers follow this pattern:

```csharp
namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [Route("api/sonobrokers/[controller]")]
    [ApiController]
    public class ExampleController : ControllerBase
    {
        private readonly DapperDbContext _dbContext;
        private readonly ILogger<ExampleController> _logger;
        
        public ExampleController(DapperDbContext dbContext, ILogger<ExampleController> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }
    }
}
```

### Database Operation Patterns

#### 1. Simple Query Pattern
```csharp
const string sql = @"
    SELECT * FROM snb.users 
    WHERE id = @userId";
    
var user = await _dbContext.QueryFirstOrDefaultAsync<User>(sql, new { userId });
```

#### 2. Complex Query with Joins
```csharp
const string sql = @"
    SELECT p.*, pi.url as image_url, u.name as owner_name
    FROM snb.properties p
    LEFT JOIN snb.property_images pi ON p.id = pi.property_id
    LEFT JOIN snb.users u ON p.owner_id = u.id
    WHERE p.status = @status
    ORDER BY p.created_at DESC
    LIMIT @limit OFFSET @offset";

var properties = await _dbContext.QueryAsync<PropertyWithDetails>(sql, new { 
    status = "active", 
    limit = pageSize, 
    offset = (page - 1) * pageSize 
});
```

#### 3. Insert/Update Operations
```csharp
const string insertSql = @"
    INSERT INTO snb.properties (id, title, description, price, owner_id, created_at, updated_at)
    VALUES (@Id, @Title, @Description, @Price, @OwnerId, @CreatedAt, @UpdatedAt)";

await _dbContext.ExecuteAsync(insertSql, new
{
    Id = Guid.NewGuid().ToString(),
    Title = property.Title,
    Description = property.Description,
    Price = property.Price,
    OwnerId = userId,
    CreatedAt = DateTime.UtcNow,
    UpdatedAt = DateTime.UtcNow
});
```

#### 4. Transaction Support
```csharp
using var transaction = await _dbContext.BeginTransactionAsync();
try
{
    // Multiple database operations
    await _dbContext.ExecuteAsync(sql1, parameters1, transaction);
    await _dbContext.ExecuteAsync(sql2, parameters2, transaction);
    
    await transaction.CommitAsync();
}
catch
{
    await transaction.RollbackAsync();
    throw;
}
```

#### 5. Stored Procedure Support
```csharp
var result = await _dbContext.ExecuteStoredProcedureAsync<PropertySearchResult>(
    "snb.search_properties_advanced", 
    new { 
        search_term = searchTerm,
        min_price = minPrice,
        max_price = maxPrice,
        location = location 
    });
```

## Specific Controller Implementations

### UsersController.cs
**Database Tables:** `snb.users`, Clerk auth tables
**Key Operations:**
- User profile management
- Clerk integration for authentication
- Role-based access control
- User search and filtering

```csharp
[HttpGet("{id}")]
public async Task<ActionResult<ApiResponse<User>>> GetUser(string id)
{
    const string sql = @"
        SELECT * FROM snb.users 
        WHERE id = @id";
        
    var user = await _dbContext.QueryFirstOrDefaultAsync<User>(sql, new { id });
    
    if (user == null)
        return NotFound(ApiResponse<User>.ErrorResult("User not found"));
        
    return Ok(ApiResponse<User>.SuccessResult(user));
}
```

### PropertiesController.cs
**Database Tables:** `snb.properties`, `snb.property_images`
**Key Operations:**
- Property CRUD operations
- Advanced search functionality
- Image association management
- Status workflow management

### PropertyImagesController.cs
**Database Tables:** `snb.property_images`
**External Services:** Azure Blob Storage
**Key Operations:**
- Image upload and storage
- Metadata management
- Bulk operations
- Image optimization

### SearchFiltersController.cs
**Database Tables:** `snb.search_filters`
**Key Operations:**
- Custom filter creation
- Filter persistence
- Complex query building
- Performance optimization

## Database Schema Integration

### Supabase Schema Structure
```sql
-- Core SoNoBrokers schema
CREATE SCHEMA snb;

-- Users table
CREATE TABLE snb.users (
    id TEXT PRIMARY KEY,
    clerk_user_id TEXT UNIQUE,
    email TEXT NOT NULL,
    name TEXT,
    role TEXT DEFAULT 'user',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Properties table
CREATE TABLE snb.properties (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    price DECIMAL(12,2),
    owner_id TEXT REFERENCES snb.users(id),
    status TEXT DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Subscriptions table (for Stripe integration)
CREATE TABLE snb.subscriptions (
    id TEXT PRIMARY KEY,
    user_id TEXT REFERENCES snb.users(id),
    stripe_subscription_id TEXT UNIQUE,
    stripe_customer_id TEXT,
    status TEXT,
    plan_type TEXT,
    starts_at TIMESTAMP,
    ends_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Payments table (for one-time payments)
CREATE TABLE snb.payments (
    id TEXT PRIMARY KEY,
    user_id TEXT REFERENCES snb.users(id),
    stripe_payment_intent_id TEXT UNIQUE,
    amount BIGINT,
    currency TEXT,
    status TEXT,
    payment_type TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### Performance Optimizations
```sql
-- Indexes for optimal query performance
CREATE INDEX idx_properties_owner_id ON snb.properties(owner_id);
CREATE INDEX idx_properties_status ON snb.properties(status);
CREATE INDEX idx_subscriptions_user_id ON snb.subscriptions(user_id);
CREATE INDEX idx_subscriptions_stripe_id ON snb.subscriptions(stripe_subscription_id);
CREATE INDEX idx_payments_user_id ON snb.payments(user_id);
```

## Error Handling & Logging

### Consistent Error Response Format
```csharp
public class ApiResponse<T>
{
    public bool Success { get; set; }
    public string Message { get; set; }
    public T? Data { get; set; }
    public List<string>? Errors { get; set; }
    
    public static ApiResponse<T> SuccessResult(T data, string message = "Success")
    public static ApiResponse<T> ErrorResult(string message, List<string>? errors = null)
}
```

### Logging Pattern
```csharp
try
{
    // Database operation
    _logger.LogInformation("Processing request for user: {UserId}", userId);
    var result = await _dbContext.QueryAsync<T>(sql, parameters);
    return Ok(ApiResponse<T>.SuccessResult(result));
}
catch (Exception ex)
{
    _logger.LogError(ex, "Error processing request for user: {UserId}", userId);
    return StatusCode(500, ApiResponse<T>.ErrorResult("Internal server error"));
}
```

## Testing Strategy

### Unit Testing Pattern
```csharp
[Test]
public async Task GetUser_ValidId_ReturnsUser()
{
    // Arrange
    var mockDbContext = new Mock<DapperDbContext>();
    var controller = new UsersController(mockDbContext.Object, Mock.Of<ILogger<UsersController>>());
    
    // Act
    var result = await controller.GetUser("test-id");
    
    // Assert
    Assert.IsType<OkObjectResult>(result.Result);
}
```

### Integration Testing
- Database integration tests with test containers
- API endpoint testing with WebApplicationFactory
- Stripe webhook testing with mock events

## Deployment Considerations

### Environment Configuration
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=db.supabase.co;Database=postgres;..."
  },
  "ExternalServices": {
    "Stripe": {
      "SecretKey": "sk_...",
      "PublishableKey": "pk_...",
      "WebhookSecret": "whsec_..."
    }
  },
  "SoNoBrokers": {
    "AppUrl": "https://sonobrokers.com"
  }
}
```

### Health Checks
```csharp
builder.Services.AddHealthChecks()
    .AddCheck<DatabaseHealthCheck>("database")
    .AddCheck<StripeHealthCheck>("stripe");
```

This technical implementation provides a solid foundation for the SoNoBrokers platform with proper separation of concerns, scalable architecture, and comprehensive payment processing capabilities.
