import { Suspense } from 'react'
import { Metadata } from 'next'
import { redirect, notFound } from 'next/navigation'
import { getCurrentUserProfile } from '@/lib/api/auth-api'
import { getConversation, getMessages } from '@/lib/api/messaging-api'
import { ChatInterface } from '@/components/messaging/ChatInterface'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, MessageCircle } from 'lucide-react'

/**
 * Individual Conversation Page - Server Component
 * Shows a specific conversation with messages
 */

interface ConversationPageProps {
  params: Promise<{ conversationId: string }>
}

export async function generateMetadata({ params }: ConversationPageProps): Promise<Metadata> {
  const { conversationId } = await params
  
  try {
    const conversation = await getConversation(conversationId)
    
    return {
      title: conversation?.subject 
        ? `${conversation.subject} | Messages | SoNoBrokers`
        : 'Conversation | Messages | SoNoBrokers',
      description: conversation?.propertyTitle 
        ? `Conversation about ${conversation.propertyTitle}`
        : 'Property conversation on SoNoBrokers',
    }
  } catch {
    return {
      title: 'Conversation | Messages | SoNoBrokers',
      description: 'Property conversation on SoNoBrokers',
    }
  }
}

export default async function ConversationPage({ params }: ConversationPageProps) {
  const { conversationId } = await params
  
  // Check authentication
  const userProfile = await getCurrentUserProfile()
  if (!userProfile) {
    redirect('/sign-in')
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back Navigation */}
      <div className="mb-6">
        <Button 
          variant="ghost" 
          onClick={() => window.history.back()}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Messages
        </Button>
      </div>

      {/* Conversation Content */}
      <Suspense fallback={<ConversationLoading />}>
        <ConversationContent conversationId={conversationId} />
      </Suspense>
    </div>
  )
}

/**
 * Conversation Content Server Component
 */
async function ConversationContent({ conversationId }: { conversationId: string }) {
  try {
    // Fetch conversation and messages in parallel
    const [conversation, messagesResponse] = await Promise.all([
      getConversation(conversationId),
      getMessages(conversationId, {
        page: 1,
        limit: 100,
        sortBy: 'createdAt',
        sortOrder: 'asc'
      })
    ])

    if (!conversation) {
      notFound()
    }

    return (
      <div className="max-w-4xl mx-auto">
        <ChatInterface
          conversation={conversation}
          messages={messagesResponse.messages}
          onBack={() => window.history.back()}
        />
      </div>
    )
  } catch (error) {
    console.error('Failed to load conversation:', error)
    
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <MessageCircle className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Unable to load conversation
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-center mb-4">
            There was an error loading this conversation. It may have been deleted or you may not have access to it.
          </p>
          <div className="flex gap-2">
            <Button onClick={() => window.history.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
            <Button variant="outline" onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }
}

/**
 * Loading Component
 */
function ConversationLoading() {
  return (
    <div className="max-w-4xl mx-auto">
      <Card>
        {/* Header Loading */}
        <div className="border-b p-6">
          <div className="flex items-center gap-3 animate-pulse">
            <div className="h-10 w-10 bg-gray-300 dark:bg-gray-700 rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-5 bg-gray-300 dark:bg-gray-700 rounded w-48"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-64"></div>
            </div>
            <div className="h-8 w-8 bg-gray-300 dark:bg-gray-700 rounded"></div>
          </div>
        </div>

        {/* Messages Loading */}
        <CardContent className="p-0">
          <div className="h-96 p-4 space-y-4">
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                className={`flex ${i % 2 === 0 ? 'justify-start' : 'justify-end'} animate-pulse`}
              >
                <div className={`flex gap-2 max-w-[70%] ${i % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                  <div className="h-8 w-8 bg-gray-300 dark:bg-gray-700 rounded-full flex-shrink-0"></div>
                  <div className="space-y-1">
                    <div className={`h-16 bg-gray-300 dark:bg-gray-700 rounded-lg ${
                      i % 2 === 0 ? 'w-48' : 'w-32'
                    }`}></div>
                    <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-16"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Input Loading */}
          <div className="border-t p-4">
            <div className="flex gap-2 animate-pulse">
              <div className="flex-1 h-10 bg-gray-300 dark:bg-gray-700 rounded"></div>
              <div className="h-10 w-10 bg-gray-300 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
