# =============================================================================
# DEVELOPMENT ENVIRONMENT SECRETS
# =============================================================================
# ⚠️  DO NOT COMMIT THIS FILE TO SOURCE CONTROL
# This file contains development environment secrets and sensitive configuration
# =============================================================================

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Single database connection using DATABASE_URL (consolidated configuration)
DATABASE_URL=************************************************************/sonobrokers

# =============================================================================
# API ENDPOINTS (Development URLs)
# =============================================================================
ApiEndpoints__DataApi__BaseUrl=https://dev-api.microsaas.com/data-api/v1
ApiEndpoints__DocumentApi__BaseUrl=https://dev-api.microsaas.com/document-api/v1
ApiEndpoints__PermissionApi__BaseUrl=https://dev-api.microsaas.com/permission-api/v1

# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================
# Clerk Authentication (Development)
Authentication__Clerk__PublishableKey=pk_test_ZGlzY3JldGUtaGFyZS03My5jbGVyay5hY2NvdW50cy5kZXYk
Authentication__Clerk__SecretKey=sk_test_qMWT9IyzEIVMz6bjQ7P4ZBVHsiy9ZERxHbGKipVHcl
Authentication__Clerk__WebhookSecret=whsec_dev_webhook_secret_here
Authentication__Clerk__JwtIssuer=https://discrete-hare-73.clerk.accounts.dev

# JWT Configuration (Development)
Authentication__Jwt__Issuer=https://dev-api.microsaas.com
Authentication__Jwt__SigningKey=dev_jwt_signing_key_minimum_32_characters_long_for_security

# =============================================================================
# EXTERNAL SERVICES (Development)
# =============================================================================
# Stripe Payment Service (Live Keys for Development)
ExternalServices__Stripe__PublishableKey=pk_live_51Qfy9dP82YH9JfOlFmwTk0RHvE7l6sow7ImJBI4c1GzcbhFydoVpb3DMevP8B3C4gj95qCoEise8vEhgFgWxhLEN000RfInKNH
ExternalServices__Stripe__SecretKey=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
ExternalServices__Stripe__WebhookSecret=

# Azure Services (Development)
ExternalServices__Azure__Storage__ConnectionString=DefaultEndpointsProtocol=https;AccountName=microsaasdev;AccountKey=dev_storage_key_here;EndpointSuffix=core.windows.net
ExternalServices__Azure__ServiceBus__ConnectionString=Endpoint=sb://microsaas-dev.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=dev_servicebus_key
ExternalServices__Azure__KeyVault__Uri=https://microsaas-dev-kv.vault.azure.net/
ExternalServices__Azure__KeyVault__ClientId=dev_client_id_here
ExternalServices__Azure__KeyVault__ClientSecret=dev_client_secret_here
ExternalServices__Azure__KeyVault__TenantId=dev_tenant_id_here

# Email Services (Development)
ExternalServices__Email__SendGrid__ApiKey=SG.dev_sendgrid_api_key_here
ExternalServices__Email__Smtp__Host=smtp.gmail.com
ExternalServices__Email__Smtp__Username=<EMAIL>
ExternalServices__Email__Smtp__Password=dev_smtp_password_here
ExternalServices__Email__Resend__ApiKey=re_H7U9GBDe_2D8FiBaiP6A59v5YHuHTXKei

# =============================================================================
# EXTERNAL API INTEGRATIONS (Development)
# =============================================================================
# Make.com Integration
ExternalServices__Make__OrganizationId=
ExternalServices__Make__TeamId=
ExternalServices__Make__ApiKey=
ExternalServices__Make__ApiUrl=https://us1.make.com/api/v2

# N8N Integration
ExternalServices__N8N__ApiKey=
ExternalServices__N8N__ApiUrl=
ExternalServices__N8N__WebhookUrl=



# WordPress Integration
ExternalServices__WordPress__DatabaseUser=user
ExternalServices__WordPress__DatabasePassword=5vBiXJjkhIC.
ExternalServices__WordPress__DatabaseName=exampledb
ExternalServices__WordPress__RestEndpoint=http://*************
ExternalServices__WordPress__MySqlDatabase=wp_db
ExternalServices__WordPress__MySqlUser=admin
ExternalServices__WordPress__MySqlPassword=adminpass

# =============================================================================
# FRONTEND INTEGRATION (Development)
# =============================================================================
Frontend__NextJs__AppUrl=http://localhost:3000
Frontend__NextJs__SignInUrl=/sign-in
Frontend__NextJs__SignUpUrl=/sign-up

# Next.js Public Keys (for reference)
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_51Qfy9dP82YH9JfOlFmwTk0RHvE7l6sow7ImJBI4c1GzcbhFydoVpb3DMevP8B3C4gj95qCoEise8vEhgFgWxhLEN000RfInKNH
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_ZGlzY3JldGUtaGFyZS03My5jbGVyay5hY2NvdW50cy5kZXYk
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
