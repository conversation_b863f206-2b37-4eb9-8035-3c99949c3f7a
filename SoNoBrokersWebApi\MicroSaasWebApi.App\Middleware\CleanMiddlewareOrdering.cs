using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Scalar.AspNetCore;

namespace MicroSaasWebApi.Middleware
{
    /// <summary>
    /// Clean middleware pipeline configuration for Core functionality
    /// </summary>
    public static class CleanMiddlewareOrdering
    {
        /// <summary>
        /// Configure the clean middleware pipeline without tenant complexity
        /// </summary>
        public static WebApplication ConfigureCleanMiddlewarePipeline(this WebApplication app)
        {
            var logger = app.Services.GetRequiredService<ILogger<Program>>();
            logger.LogInformation("Configuring clean middleware pipeline...");

            // 1. Exception handling (must be first)
            if (app.Environment.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler("/Error");
                app.UseHsts();
            }

            // 2. HTTPS redirection
            app.UseHttpsRedirection();

            // 3. Static files
            app.UseStaticFiles();

            // 4. Routing
            app.UseRouting();

            // 5. CORS (before authentication)
            app.UseCors("AllowSpecificOrigins");

            // 6. Authentication
            app.UseAuthentication();

            // 7. Authorization
            app.UseAuthorization();

            // 8. Response compression
            if (app.Configuration.GetValue<bool>("FeatureFlags:EnableResponseCompression"))
            {
                app.UseResponseCompression();
            }

            // 9. Security headers
            app.Use(async (context, next) =>
            {
                context.Response.Headers.Append("X-Content-Type-Options", "nosniff");
                context.Response.Headers.Append("X-Frame-Options", "DENY");
                context.Response.Headers.Append("X-XSS-Protection", "1; mode=block");
                context.Response.Headers.Append("Referrer-Policy", "strict-origin-when-cross-origin");
                await next();
            });

            // 10. Request logging
            app.Use(async (context, next) =>
            {
                var requestLogger = app.Services.GetRequiredService<ILogger<Program>>();
                var startTime = DateTime.UtcNow;

                requestLogger.LogInformation("Request: {Method} {Path} from {RemoteIp}",
                    context.Request.Method,
                    context.Request.Path,
                    context.Connection.RemoteIpAddress);

                await next();

                var duration = DateTime.UtcNow - startTime;
                requestLogger.LogInformation("Response: {StatusCode} in {Duration}ms",
                    context.Response.StatusCode,
                    duration.TotalMilliseconds);
            });

            // 11. .NET 9 OpenAPI + Scalar (development only)
            if (app.Environment.IsDevelopment())
            {
                app.MapOpenApi();
                app.MapScalarApiReference(options =>
                {
                    options
                        .WithTitle("MicroSaaS Core API Documentation")
                        .WithTheme(ScalarTheme.Kepler)
                        .WithDarkModeToggle(true)
                        .WithDefaultHttpClient(ScalarTarget.CSharp, ScalarClient.HttpClient);
                });
            }

            // 12. Health checks
            app.MapHealthChecks("/health");
            app.MapHealthChecks("/health/ready");
            app.MapHealthChecks("/health/live");

            // 13. API controllers
            app.MapControllers();

            logger.LogInformation("Clean middleware pipeline configured successfully");
            return app;
        }
    }
}
