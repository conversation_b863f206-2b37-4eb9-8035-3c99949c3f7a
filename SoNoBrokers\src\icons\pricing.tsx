const Pricing = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11 21.1429H11.0002C13.6893 21.1399 16.2674 20.0703 18.1688 18.1688C20.0703 16.2674 21.1399 13.6893 21.1429 11.0002V11C21.1429 8.99393 20.548 7.03292 19.4335 5.36493C18.319 3.69695 16.7349 2.39691 14.8815 1.62923C13.0281 0.861538 10.9888 0.660675 9.02123 1.05204C7.05371 1.4434 5.24642 2.40942 3.82792 3.82792C2.40942 5.24642 1.4434 7.05371 1.05204 9.02123C0.660675 10.9888 0.861538 13.0281 1.62923 14.8815C2.39691 16.7349 3.69695 18.319 5.36493 19.4335C7.03292 20.548 8.99393 21.1429 11 21.1429ZM13.2506 6.78736C12.7348 6.36392 12.1035 6.10763 11.4406 6.05063V4.40667V4.26381H11.2978H10.6844H10.5416V4.40667V6.0457C9.85676 6.0814 9.20208 6.34595 8.68375 6.79912L8.68373 6.7991L8.68154 6.80109C8.4095 7.04906 8.19407 7.35272 8.04991 7.69141C7.90636 8.02864 7.83682 8.39273 7.84596 8.75909C7.83415 9.0731 7.88686 9.38622 8.00084 9.67909C8.1154 9.97343 8.2894 10.241 8.51197 10.4651L8.51286 10.466C8.94391 10.8924 9.62603 11.2138 10.5394 11.4419V14.5044C10.1907 14.4337 9.86031 14.2899 9.57021 14.0819C9.14572 13.7739 8.90057 13.2707 8.85141 12.5569L8.84133 12.4104L8.69525 12.4245L7.65303 12.5245L7.52396 12.5368L7.52381 12.6665C7.52278 13.5563 7.87096 14.4109 8.49349 15.0466L8.49454 15.0477C8.98947 15.5426 9.68256 15.8301 10.5483 15.927V17.5911V17.734H10.6911H11.3222H11.4651V17.5911V15.9565C12.2453 15.9409 12.9963 15.6476 13.5816 15.1267L13.5816 15.1267L13.5831 15.1254C13.8732 14.8598 14.1031 14.5352 14.2571 14.1733C14.4109 13.812 14.4856 13.422 14.4762 13.0294V13.0281C14.5052 12.4509 14.3277 11.8823 13.9755 11.4241L13.9755 11.424L13.9728 11.4207C13.6579 11.0353 13.2506 10.7357 12.7889 10.5497L12.7889 10.5497L12.7868 10.5489C12.3499 10.381 11.9034 10.2398 11.4495 10.1258V7.46143C11.7685 7.50551 12.0706 7.63531 12.323 7.83821C12.4868 7.9975 12.6178 8.1873 12.7086 8.39694C12.8003 8.60849 12.8492 8.83607 12.8527 9.0666L12.855 9.22049L13.0083 9.20673L14.0728 9.11118L14.2078 9.09905L14.2028 8.96354C14.1683 8.04353 13.8537 7.3049 13.253 6.78938L13.2531 6.78935L13.2506 6.78736ZM9.64479 7.75437C9.91626 7.58636 10.2211 7.4811 10.5371 7.44544V9.88697C10.1844 9.78815 9.84603 9.64299 9.53095 9.45508L9.53115 9.45475L9.52167 9.45001L9.52009 9.44922C9.40509 9.36102 9.31329 9.24605 9.25275 9.11424C9.19052 8.97873 9.16331 8.82976 9.17363 8.68099L9.17421 8.67258L9.1738 8.66415C9.16502 8.48429 9.20404 8.30531 9.28689 8.14543C9.36962 7.98579 9.49309 7.85087 9.64479 7.75437ZM13.1285 13.1136L13.1279 13.1136L13.1283 13.1273C13.1355 13.3299 13.0922 13.531 13.0022 13.7127C12.9124 13.894 12.779 14.0502 12.6139 14.1672C12.2711 14.4015 11.8713 14.5364 11.4584 14.5585V11.6798C11.8541 11.7928 12.2374 11.9458 12.6024 12.1364C12.7718 12.2341 12.9108 12.3769 13.0037 12.5491C13.0971 12.722 13.1403 12.9175 13.1285 13.1136ZM6.03109 3.5635C7.50189 2.58074 9.23108 2.05619 11 2.05619C13.3713 2.05855 15.6448 3.00159 17.3216 4.67837C18.9984 6.35519 19.9415 8.62877 19.9438 11.0001C19.9438 12.769 19.4192 14.4982 18.4365 15.9689C17.4537 17.4397 16.0569 18.5861 14.4226 19.263C12.7884 19.9399 10.9901 20.1171 9.25515 19.772C7.52022 19.4269 5.92659 18.575 4.67578 17.3242C3.42496 16.0734 2.57315 14.4798 2.22805 12.7449C1.88295 11.0099 2.06007 9.21162 2.737 7.57735C3.41394 5.94309 4.56029 4.54626 6.03109 3.5635Z"
        fill="currentColor"
        stroke="currentColor"
        strokeWidth="0.285714"
      />
    </svg>
  );
};

export default Pricing;
