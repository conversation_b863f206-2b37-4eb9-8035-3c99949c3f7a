// Removed auth import - no authentication required for browsing buyer's guide
import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function BuyersGuidePage({ params, searchParams }: PageProps) {
  // No authentication required for browsing buyer's guide
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/resources/buyers-guide')
  }

  const userType = resolvedSearchParams.userType || 'buyer'

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8">
            Complete Buyer's Guide
          </h1>

          <div className="prose prose-lg max-w-none">
            <div className="bg-card rounded-lg p-6 mb-8">
              <h2 className="text-2xl font-semibold mb-4">Your Home Buying Journey</h2>
              <p className="text-muted-foreground mb-4">
                Buying a home is one of the most significant investments you'll make. This comprehensive
                guide will help you navigate the process with confidence, from initial planning to closing day.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-6 mb-8">
              <div className="bg-card rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-3">Pre-Purchase Phase</h3>
                <ul className="space-y-2 text-sm">
                  <li>• Financial preparation and pre-approval</li>
                  <li>• Determining your budget and needs</li>
                  <li>• Understanding the local market</li>
                  <li>• Building your support team</li>
                </ul>
              </div>

              <div className="bg-card rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-3">Search & Purchase</h3>
                <ul className="space-y-2 text-sm">
                  <li>• Property search strategies</li>
                  <li>• Making competitive offers</li>
                  <li>• Home inspections and due diligence</li>
                  <li>• Closing process and final steps</li>
                </ul>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6 mb-8">
              <h2 className="text-2xl font-semibold mb-4">Step-by-Step Process</h2>
              <div className="space-y-6">
                <div className="flex gap-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold">1</div>
                  <div>
                    <h4 className="font-semibold">Get Pre-Approved</h4>
                    <p className="text-sm text-muted-foreground">
                      Understand your budget and get pre-approved for a mortgage to strengthen your offers.
                    </p>
                  </div>
                </div>

                <div className="flex gap-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold">2</div>
                  <div>
                    <h4 className="font-semibold">Define Your Criteria</h4>
                    <p className="text-sm text-muted-foreground">
                      Create a list of must-haves vs. nice-to-haves for your ideal home.
                    </p>
                  </div>
                </div>

                <div className="flex gap-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold">3</div>
                  <div>
                    <h4 className="font-semibold">Start Your Search</h4>
                    <p className="text-sm text-muted-foreground">
                      Use our property search tools to find homes that match your criteria.
                    </p>
                  </div>
                </div>

                <div className="flex gap-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold">4</div>
                  <div>
                    <h4 className="font-semibold">Make an Offer</h4>
                    <p className="text-sm text-muted-foreground">
                      Submit competitive offers with appropriate conditions and timelines.
                    </p>
                  </div>
                </div>

                <div className="flex gap-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold">5</div>
                  <div>
                    <h4 className="font-semibold">Complete Due Diligence</h4>
                    <p className="text-sm text-muted-foreground">
                      Arrange inspections, finalize financing, and review all documents.
                    </p>
                  </div>
                </div>

                <div className="flex gap-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold">6</div>
                  <div>
                    <h4 className="font-semibold">Close the Deal</h4>
                    <p className="text-sm text-muted-foreground">
                      Complete the final walkthrough, sign documents, and get your keys!
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6">
              <h2 className="text-2xl font-semibold mb-4">Essential Resources</h2>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Financial Tools</h4>
                  <ul className="space-y-1 text-sm">
                    <li>• Mortgage calculator</li>
                    <li>• Affordability calculator</li>
                    <li>• Closing cost estimator</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Professional Services</h4>
                  <ul className="space-y-1 text-sm">
                    <li>• Home inspection services</li>
                    <li>• Insurance providers</li>
                    <li>• Legal services</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'buyer'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Complete Buyer's Guide for ${country} | SoNoBrokers`,
    description: `Comprehensive home buying guide for ${country}. Step-by-step process, expert tips, and essential resources for first-time and experienced buyers.`,
    keywords: `buyers guide, home buying, real estate guide, ${country}, first time buyer`,
  }
}
