'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'motion/react'
import {
  Search, Star, ArrowRight, Home, Users, MapPin, Shield, DollarSign,
  Clock, CheckCircle, TrendingUp, Award, Phone, Mail, Globe,
  Calculator, FileText, Camera, Gavel, Building, Zap, Flag
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useAppContext } from '@/contexts/AppContext'

export function USLandingClient() {
  // Get authentication state from AppContext
  const { isSignedIn } = useAppContext()
  const [searchQuery, setSearchQuery] = useState('')
  const router = useRouter()

  const handleSearch = () => {
    if (searchQuery.trim()) {
      router.push(`/us/properties?search=${encodeURIComponent(searchQuery)}`)
    }
  }

  const handleStartBrowsing = () => {
    router.push('/us/properties')
  }

  const handleListProperty = () => {
    router.push('/us/list-property')
  }

  return (
    <div className="bg-background text-foreground">
      {/* Hero Section */}
      <section className="relative py-12 overflow-hidden">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <Badge variant="secondary" className="mb-4 text-sm px-4 py-2">
              🇺🇸 America's Premier Commission-Free Platform
            </Badge>

            <h1 className="text-4xl md:text-6xl font-bold mb-4 leading-tight">
              Find Your Perfect<br />
              <span className="text-primary">American Dream</span> Home
            </h1>

            <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
              Revolutionary commission-free real estate platform serving all 50 states.
              Connect directly with property owners and save thousands on your next home purchase.
            </p>

            <div className="flex items-center max-w-2xl mx-auto mb-6 bg-card rounded-full shadow-md border overflow-hidden">
              <Input
                type="text"
                placeholder="Search New York, California, Texas, Florida..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 border-0 focus-visible:ring-0 text-base px-6 h-12 rounded-none bg-transparent"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button
                onClick={handleSearch}
                className="rounded-full bg-primary hover:bg-primary/90 text-primary-foreground px-6 h-10 m-1"
                size="sm"
              >
                <Search className="w-5 h-5" />
              </Button>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-center items-center mb-8">
              <Button
                onClick={handleStartBrowsing}
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 h-11 rounded-full font-semibold"
              >
                Browse Properties
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
              <Button
                onClick={handleListProperty}
                variant="outline"
                className="border-2 border-primary text-primary hover:bg-primary/10 px-8 h-11 rounded-full font-semibold"
              >
                List Your Property
              </Button>
            </div>

            <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-primary" />
                <span>Zero Commission</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                <span>MLS Integration</span>
              </div>
              <div className="flex items-center space-x-2">
                <Flag className="h-4 w-4 text-primary" />
                <span>All 50 States</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-8 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="space-y-2"
            >
              <div className="flex items-center justify-center mb-2">
                <Home className="w-6 h-6 text-primary mr-2" />
                <div className="text-3xl font-bold text-primary">75k+</div>
              </div>
              <div className="text-muted-foreground font-medium">Active Properties</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="space-y-2"
            >
              <div className="flex items-center justify-center mb-2">
                <Users className="w-6 h-6 text-primary mr-2" />
                <div className="text-3xl font-bold text-primary">40k+</div>
              </div>
              <div className="text-muted-foreground font-medium">Happy Clients</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="space-y-2"
            >
              <div className="flex items-center justify-center mb-2">
                <Flag className="w-6 h-6 text-primary mr-2" />
                <div className="text-3xl font-bold text-primary">50</div>
              </div>
              <div className="text-muted-foreground font-medium">States Covered</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="space-y-2"
            >
              <div className="flex items-center justify-center mb-2">
                <DollarSign className="w-6 h-6 text-primary mr-2" />
                <div className="text-3xl font-bold text-primary">$4.2B+</div>
              </div>
              <div className="text-muted-foreground font-medium">Commission Saved</div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Why Choose SoNoBrokers Section */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-6"
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-3">
              Why Choose SoNoBrokers USA?
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              America's most innovative commission-free real estate platform, revolutionizing property transactions.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              {
                icon: DollarSign,
                title: "Zero Commission Fees",
                description: "Save thousands on traditional realtor commissions. Keep more money for your American dream home."
              },
              {
                icon: Flag,
                title: "Nationwide Coverage",
                description: "From New York to California, Alaska to Florida - we cover all 50 states with local expertise."
              },
              {
                icon: Shield,
                title: "MLS Integration",
                description: "Direct access to Multiple Listing Service data with real-time updates and verified information."
              },
              {
                icon: Users,
                title: "Direct Communication",
                description: "Connect directly with property owners and buyers. No middleman, transparent negotiations."
              },
              {
                icon: TrendingUp,
                title: "Market Intelligence",
                description: "Advanced analytics, price trends, and neighborhood insights powered by big data."
              },
              {
                icon: Award,
                title: "Licensed Professionals",
                description: "Access to state-licensed real estate professionals, lawyers, and service providers."
              }
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <feature.icon className="h-8 w-8 text-primary mb-2" />
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* US States Coverage */}
      <section className="py-8 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-6"
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-3">
              Coast to Coast Coverage
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Serving all 50 states with local expertise and nationwide reach.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-3">
            {[
              { region: "Northeast", states: ["New York", "Massachusetts", "Pennsylvania"], properties: "18,000+" },
              { region: "Southeast", states: ["Florida", "Georgia", "North Carolina"], properties: "15,000+" },
              { region: "West Coast", states: ["California", "Washington", "Oregon"], properties: "22,000+" },
              { region: "Southwest", states: ["Texas", "Arizona", "Nevada"], properties: "12,000+" },
              { region: "Midwest", states: ["Illinois", "Ohio", "Michigan"], properties: "8,500+" },
              { region: "Mountain West", states: ["Colorado", "Utah", "Montana"], properties: "4,200+" },
              { region: "Plains", states: ["Kansas", "Nebraska", "Oklahoma"], properties: "2,800+" },
              { region: "Alaska & Hawaii", states: ["Alaska", "Hawaii"], properties: "1,100+" }
            ].map((region, index) => (
              <motion.div
                key={region.region}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">{region.region}</CardTitle>
                    <Badge variant="secondary" className="w-fit text-xs">
                      {region.properties}
                    </Badge>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-1">
                      {region.states.map((state) => (
                        <div key={state} className="text-xs text-muted-foreground">
                          • {state}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Professional Services */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-6"
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-3">
              Professional Services Network
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Access America's largest network of licensed real estate professionals.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              {
                icon: Gavel,
                title: "Real Estate Attorneys",
                description: "State-licensed attorneys specializing in property law and closings.",
                features: ["Contract Review", "Title Insurance", "Closing Services"]
              },
              {
                icon: Calculator,
                title: "Mortgage Brokers",
                description: "Licensed mortgage professionals with access to hundreds of lenders.",
                features: ["Rate Shopping", "Pre-approval", "Loan Processing"]
              },
              {
                icon: Camera,
                title: "Photography & Media",
                description: "Professional real estate photography and marketing services.",
                features: ["HDR Photography", "Virtual Tours", "Drone Videos"]
              }
            ].map((service, index) => (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <service.icon className="h-8 w-8 text-primary mb-2" />
                    <CardTitle className="text-lg">{service.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3 pt-0">
                    <p className="text-sm text-muted-foreground">
                      {service.description}
                    </p>
                    <div className="space-y-1">
                      {service.features.map((feature) => (
                        <div key={feature} className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-primary" />
                          <span className="text-xs">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-8 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-6"
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-3">
              How SoNoBrokers Works
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Revolutionary commission-free real estate platform designed for the American market.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-6">
            {[
              {
                step: "01",
                title: "Sign Up Free",
                description: "Create your account and verify identity. Choose your state and property preferences.",
                icon: Users
              },
              {
                step: "02",
                title: "Search or List",
                description: "Browse MLS-integrated listings or list your property with professional photos.",
                icon: Home
              },
              {
                step: "03",
                title: "Connect & Close",
                description: "Connect directly with buyers/sellers. Use our network of professionals for closing.",
                icon: Phone
              }
            ].map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="relative mb-4">
                  <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mx-auto mb-3">
                    <step.icon className="h-6 w-6 text-primary-foreground" />
                  </div>
                  <Badge variant="secondary" className="absolute -top-1 -right-1 text-xs font-bold">
                    {step.step}
                  </Badge>
                </div>
                <h3 className="text-lg font-bold mb-2">{step.title}</h3>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {step.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center bg-primary/5 rounded-xl p-6"
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-3">
              Ready to Find Your American Dream Home?
            </h2>
            <p className="text-base text-muted-foreground mb-4 max-w-2xl mx-auto">
              Join thousands of Americans who have saved millions in commission fees nationwide.
            </p>

            <div className="flex flex-col sm:flex-row gap-3 justify-center items-center mb-4">
              <Button
                onClick={handleStartBrowsing}
                size="lg"
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-6"
              >
                Start Browsing Properties
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
              <Button
                onClick={handleListProperty}
                variant="outline"
                size="lg"
                className="border-2 border-primary text-primary hover:bg-primary/10 px-6"
              >
                List Your Property Free
              </Button>
            </div>

            <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                <span>No Commission Fees</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                <span>MLS Integration</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                <span>Licensed Professionals</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
