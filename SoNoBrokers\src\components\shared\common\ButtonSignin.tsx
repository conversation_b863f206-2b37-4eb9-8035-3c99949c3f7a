'use client'

import { Button } from '@/components/ui/button'
import { useTheme } from 'next-themes'
import { useAppContext } from '@/contexts/AppContext'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

// A simple button to sign in with <PERSON>.
// It automatically redirects the user to callbackUrl (config.auth.callbackUrl) after login,
// which is normally a private page for users to manage their accounts.
// If the user is already logged in, it will show their profile picture & redirect them to callbackUrl immediately.
const ButtonSignin = ({
	text = 'Get started',
	extraStyle,
}: {
	text?: string
	extraStyle?: string
}) => {
	const router = useRouter()
	const { isSignedIn, snbUser, showSignInModal } = useAppContext()
	const { theme } = useTheme()

	const handleClick = () => {
		if (isSignedIn) {
			router.push('/')
		} else {
			showSignInModal({
				message: 'Please sign in to continue'
			})
		}
	}

	if (isSignedIn && snbUser) {
		return (
			<Button variant="outline" size="sm" asChild>
				<Link href={'/dashboard'} className="flex items-center gap-2">
					<span className='w-6 h-6 bg-primary text-primary-foreground flex justify-center items-center rounded-full shrink-0 text-xs font-medium'>
						{snbUser.firstName
							? snbUser.firstName.charAt(0).toUpperCase()
							: snbUser.email.charAt(0).toUpperCase()}
					</span>
					{snbUser.firstName || snbUser.email || 'Account'}
				</Link>
			</Button>
		)
	}

	return (
		<Button
			onClick={handleClick}
			className={`h-8 px-4 text-sm min-w-[100px] bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 ${extraStyle || ''}`}
		>
			{text}
		</Button>
	)
}

export default ButtonSignin
