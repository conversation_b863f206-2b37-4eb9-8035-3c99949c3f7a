-- Migration: 0002_create_core_tables.sql
-- Description: Create core tables (User, Property, PropertyImage, Conversation, Message, BuyerOffer, PropertyViewing)
-- Date: 2024-12-25
-- Author: Migration System
-- Dependencies: 0001_create_enums.sql

-- =====================================================
-- CORE TABLES
-- =====================================================

-- Users table
CREATE TABLE IF NOT EXISTS public."User" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "clerkId" TEXT UNIQUE NOT NULL,
  email TEXT UNIQUE NOT NULL,
  "fullName" TEXT NOT NULL,
  "firstName" TEXT,
  "lastName" TEXT,
  "phoneNumber" TEXT,
  "profileImageUrl" TEXT,
  role "UserRole" DEFAULT 'USER',
  status "UserStatus" DEFAULT 'ACTIVE',
  "emailVerified" BOOLEAN DEFAULT FALSE,
  "phoneVerified" BOOLEAN DEFAULT FALSE,
  "lastLoginAt" TIMESTAMP WITH TIME ZONE,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Properties table
CREATE TABLE IF NOT EXISTS public."Property" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "sellerId" UUID NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  price DECIMAL(12,2) NOT NULL,
  "propertyType" "PropertyType" NOT NULL,
  "listingType" "ListingType" DEFAULT 'FOR_SALE',
  status "PropertyStatus" DEFAULT 'DRAFT',
  bedrooms INTEGER,
  bathrooms DECIMAL(3,1),
  "squareFootage" INTEGER,
  "lotSize" DECIMAL(10,2),
  "yearBuilt" INTEGER,
  address JSONB NOT NULL,
  "geoLocation" POINT,
  "imageUrls" TEXT[],
  "virtualTourUrl" TEXT,
  "videoUrl" TEXT,
  features TEXT[],
  amenities TEXT[],
  "parkingSpaces" INTEGER,
  "garageSpaces" INTEGER,
  "basementType" TEXT,
  "heatingType" TEXT,
  "coolingType" TEXT,
  "flooring" TEXT[],
  "appliances" TEXT[],
  "utilities" TEXT[],
  "propertyTaxes" DECIMAL(10,2),
  "maintenanceFees" DECIMAL(10,2),
  "hoaFees" DECIMAL(10,2),
  "listingDate" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "expiryDate" TIMESTAMP WITH TIME ZONE,
  "viewCount" INTEGER DEFAULT 0,
  "favoriteCount" INTEGER DEFAULT 0,
  "isActive" BOOLEAN DEFAULT TRUE,
  "isFeatured" BOOLEAN DEFAULT FALSE,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT "Property_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- Property Images table
CREATE TABLE IF NOT EXISTS public."PropertyImage" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "propertyId" UUID NOT NULL,
  "imageUrl" TEXT NOT NULL,
  "altText" TEXT,
  "displayOrder" INTEGER DEFAULT 0,
  "isMain" BOOLEAN DEFAULT FALSE,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT "PropertyImage_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE
);

-- Conversations table
CREATE TABLE IF NOT EXISTS public."Conversation" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "propertyId" UUID NOT NULL,
  "buyerId" UUID NOT NULL,
  "sellerId" UUID NOT NULL,
  "lastMessageAt" TIMESTAMP WITH TIME ZONE,
  "isActive" BOOLEAN DEFAULT TRUE,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT "Conversation_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE,
  CONSTRAINT "Conversation_buyerId_fkey" FOREIGN KEY ("buyerId") REFERENCES public."User"(id) ON DELETE CASCADE,
  CONSTRAINT "Conversation_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES public."User"(id) ON DELETE CASCADE,
  CONSTRAINT "Conversation_unique_participants" UNIQUE ("propertyId", "buyerId", "sellerId")
);

-- Messages table
CREATE TABLE IF NOT EXISTS public."Message" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "conversationId" UUID NOT NULL,
  "senderId" UUID NOT NULL,
  "receiverId" UUID NOT NULL,
  content TEXT NOT NULL,
  "messageType" TEXT DEFAULT 'text',
  "isRead" BOOLEAN DEFAULT FALSE,
  "readAt" TIMESTAMP WITH TIME ZONE,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT "Message_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES public."Conversation"(id) ON DELETE CASCADE,
  CONSTRAINT "Message_senderId_fkey" FOREIGN KEY ("senderId") REFERENCES public."User"(id) ON DELETE CASCADE,
  CONSTRAINT "Message_receiverId_fkey" FOREIGN KEY ("receiverId") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- Buyer Offers table
CREATE TABLE IF NOT EXISTS public."BuyerOffer" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "propertyId" UUID NOT NULL,
  "buyerId" UUID NOT NULL,
  "sellerId" UUID NOT NULL,
  "offerAmount" DECIMAL(12,2) NOT NULL,
  message TEXT,
  status TEXT DEFAULT 'pending',
  "expiryDate" TIMESTAMP WITH TIME ZONE,
  "respondedAt" TIMESTAMP WITH TIME ZONE,
  "sellerResponse" TEXT,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT "BuyerOffer_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE,
  CONSTRAINT "BuyerOffer_buyerId_fkey" FOREIGN KEY ("buyerId") REFERENCES public."User"(id) ON DELETE CASCADE,
  CONSTRAINT "BuyerOffer_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- Property Viewings table
CREATE TABLE IF NOT EXISTS public."PropertyViewing" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "propertyId" UUID NOT NULL,
  "buyerId" UUID NOT NULL,
  "sellerId" UUID NOT NULL,
  "scheduledDate" TIMESTAMP WITH TIME ZONE NOT NULL,
  "duration" INTEGER DEFAULT 60,
  status TEXT DEFAULT 'scheduled',
  notes TEXT,
  "buyerNotes" TEXT,
  "sellerNotes" TEXT,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT "PropertyViewing_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES public."Property"(id) ON DELETE CASCADE,
  CONSTRAINT "PropertyViewing_buyerId_fkey" FOREIGN KEY ("buyerId") REFERENCES public."User"(id) ON DELETE CASCADE,
  CONSTRAINT "PropertyViewing_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES public."User"(id) ON DELETE CASCADE
);

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ Migration 0002_create_core_tables.sql completed successfully';
    RAISE NOTICE 'Created tables:';
    RAISE NOTICE '- User (with Clerk integration)';
    RAISE NOTICE '- Property (main property listings)';
    RAISE NOTICE '- PropertyImage (property photos)';
    RAISE NOTICE '- Conversation (buyer-seller conversations)';
    RAISE NOTICE '- Message (conversation messages)';
    RAISE NOTICE '- BuyerOffer (property offers)';
    RAISE NOTICE '- PropertyViewing (scheduled viewings)';
    RAISE NOTICE '';
END $$;
