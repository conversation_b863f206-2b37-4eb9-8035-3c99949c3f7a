using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MicroSaasWebApi.Models.Core
{
    /// <summary>
    /// User role model for authorization
    /// </summary>
    [Table("user_roles", Schema = "core")]
    public class UserRole
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        public Guid UserId { get; set; }

        [Required]
        [MaxLength(50)]
        public string Role { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? Scope { get; set; }

        [MaxLength(500)]
        public string? Permissions { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime AssignedAt { get; set; } = DateTime.UtcNow;
        public DateTime? RevokedAt { get; set; }

        public Guid AssignedBy { get; set; }
        public Guid? RevokedBy { get; set; }

        // Navigation properties
        public virtual User User { get; set; } = null!;
        public virtual User Assigner { get; set; } = null!;
        public virtual User? Revoker { get; set; }
    }
}
