# DapperDbContext Usage Guide

## Overview
The `DapperDbContext` provides a comprehensive interface for interacting with Supabase PostgreSQL database using Dapper ORM. It supports both direct SQL queries and stored procedures/functions.

## Features

### 1. Basic Query Operations
```csharp
// Query multiple records
var users = await _dbContext.QueryAsync<User>("SELECT * FROM auth.users WHERE role = @role", new { role = "buyer" });

// Query single record
var user = await _dbContext.QueryFirstOrDefaultAsync<User>("SELECT * FROM auth.users WHERE id = @id", new { id = userId });

// Execute command (INSERT, UPDATE, DELETE)
var rowsAffected = await _dbContext.ExecuteAsync("UPDATE snb.properties SET status = @status WHERE id = @id", new { status = "sold", id = propertyId });
```

### 2. Stored Procedures (Traditional)
```csharp
// Execute stored procedure returning data
var results = await _dbContext.ExecuteStoredProcedureAsync<Property>("sp_search_properties", new { location = "Toronto", minPrice = 100000 });

// Execute stored procedure returning single result
var result = await _dbContext.ExecuteStoredProcedureSingleAsync<int>("sp_get_property_count", new { status = "active" });

// Execute stored procedure without return value
var rowsAffected = await _dbContext.ExecuteStoredProcedureAsync("sp_update_property_status", new { propertyId = "123", status = "sold" });
```

### 3. Supabase Functions (PostgreSQL Functions)
```csharp
// Execute Supabase function returning multiple results
var properties = await _dbContext.ExecuteFunctionAsync<Property>("snb.search_properties_advanced", new { 
    p_location = "Toronto",
    p_min_price = 100000,
    p_max_price = 500000
});

// Execute Supabase function returning single result
var count = await _dbContext.ExecuteFunctionSingleAsync<int>("snb.get_total_properties_count");

// Execute function without parameters
var stats = await _dbContext.ExecuteFunctionAsync<PropertyStats>("snb.get_property_statistics");
```

### 4. Transaction Support
```csharp
// Execute multiple operations in a transaction
await _dbContext.ExecuteInTransactionAsync(async (context) =>
{
    await context.ExecuteAsync("INSERT INTO snb.properties (...) VALUES (...)", propertyData);
    await context.ExecuteAsync("INSERT INTO snb.property_images (...) VALUES (...)", imageData);
    await context.ExecuteAsync("UPDATE auth.users SET property_count = property_count + 1 WHERE id = @userId", new { userId });
});

// Execute with return value
var result = await _dbContext.ExecuteInTransactionAsync(async (context) =>
{
    var propertyId = await context.QuerySingleAsync<string>("INSERT INTO snb.properties (...) VALUES (...) RETURNING id", propertyData);
    await context.ExecuteAsync("INSERT INTO snb.property_images (...) VALUES (...)", new { propertyId, imageData });
    return propertyId;
});
```

### 5. Connection Management
```csharp
// Get managed connection (recommended for most cases)
var connection = _dbContext.Connection;

// Create new connection (for specific scenarios)
using var newConnection = _dbContext.CreateConnection();
```

## Example Supabase Functions

### 1. Property Search Function
```sql
CREATE OR REPLACE FUNCTION snb.search_properties_advanced(
    p_location TEXT DEFAULT NULL,
    p_property_type TEXT DEFAULT NULL,
    p_min_price DECIMAL DEFAULT NULL,
    p_max_price DECIMAL DEFAULT NULL,
    p_bedrooms INTEGER DEFAULT NULL,
    p_bathrooms DECIMAL DEFAULT NULL
) 
RETURNS TABLE(
    id UUID,
    title TEXT,
    description TEXT,
    price DECIMAL,
    property_type TEXT,
    address TEXT,
    city TEXT,
    state TEXT,
    bedrooms INTEGER,
    bathrooms DECIMAL,
    seller_name TEXT,
    seller_avatar TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.title,
        p.description,
        p.price,
        p.property_type,
        p.address,
        p.city,
        p.state,
        p.bedrooms,
        p.bathrooms,
        u.full_name as seller_name,
        u.avatar_url as seller_avatar
    FROM snb.properties p
    LEFT JOIN auth.users u ON p.seller_id = u.id
    WHERE 
        (p_location IS NULL OR p.address ILIKE '%' || p_location || '%' 
         OR p.city ILIKE '%' || p_location || '%' 
         OR p.state ILIKE '%' || p_location || '%')
        AND (p_property_type IS NULL OR p.property_type = p_property_type)
        AND (p_min_price IS NULL OR p.price >= p_min_price)
        AND (p_max_price IS NULL OR p.price <= p_max_price)
        AND (p_bedrooms IS NULL OR p.bedrooms >= p_bedrooms)
        AND (p_bathrooms IS NULL OR p.bathrooms >= p_bathrooms)
    ORDER BY p.created_at DESC;
END;
$$ LANGUAGE plpgsql;
```

### 2. Property Status Update Function
```sql
CREATE OR REPLACE FUNCTION snb.update_property_status(
    p_property_id UUID,
    p_status TEXT,
    p_user_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
    property_exists BOOLEAN;
    user_authorized BOOLEAN;
BEGIN
    -- Check if property exists
    SELECT EXISTS(SELECT 1 FROM snb.properties WHERE id = p_property_id) INTO property_exists;
    
    IF NOT property_exists THEN
        RETURN FALSE;
    END IF;
    
    -- Check if user is authorized (owner or admin)
    SELECT EXISTS(
        SELECT 1 FROM snb.properties p 
        LEFT JOIN auth.users u ON p.seller_id = u.id
        WHERE p.id = p_property_id 
        AND (p.seller_id = p_user_id OR u.role = 'admin')
    ) INTO user_authorized;
    
    IF NOT user_authorized THEN
        RETURN FALSE;
    END IF;
    
    -- Update the property status
    UPDATE snb.properties 
    SET status = p_status, updated_at = NOW()
    WHERE id = p_property_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;
```

### 3. Statistics Function
```sql
CREATE OR REPLACE FUNCTION snb.get_property_statistics()
RETURNS TABLE(
    total_properties INTEGER,
    active_properties INTEGER,
    sold_properties INTEGER,
    average_price DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_properties,
        COUNT(CASE WHEN status = 'active' THEN 1 END)::INTEGER as active_properties,
        COUNT(CASE WHEN status = 'sold' THEN 1 END)::INTEGER as sold_properties,
        AVG(price) as average_price
    FROM snb.properties
    WHERE deleted_at IS NULL;
END;
$$ LANGUAGE plpgsql;
```

## Best Practices

1. **Use transactions for multi-step operations**
2. **Prefer Supabase functions for complex business logic**
3. **Use parameterized queries to prevent SQL injection**
4. **Handle exceptions appropriately in your services**
5. **Use the managed Connection property for simple operations**
6. **Create new connections only when needed for parallel operations**

## Service Integration Example

```csharp
public class PropertyService : IPropertyService
{
    private readonly DapperDbContext _dbContext;
    private readonly ILogger<PropertyService> _logger;

    public PropertyService(DapperDbContext dbContext, ILogger<PropertyService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<Property> CreatePropertyAsync(Property property)
    {
        try
        {
            return await _dbContext.ExecuteInTransactionAsync(async (context) =>
            {
                // Insert property
                var propertyId = await context.QuerySingleAsync<string>(
                    "INSERT INTO snb.properties (...) VALUES (...) RETURNING id", 
                    property);

                // Insert images if any
                if (property.Images?.Any() == true)
                {
                    foreach (var image in property.Images)
                    {
                        await context.ExecuteAsync(
                            "INSERT INTO snb.property_images (...) VALUES (...)", 
                            new { propertyId, image });
                    }
                }

                // Update user property count
                await context.ExecuteAsync(
                    "UPDATE auth.users SET property_count = property_count + 1 WHERE id = @sellerId", 
                    new { sellerId = property.SellerId });

                property.Id = propertyId;
                return property;
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating property");
            throw;
        }
    }
}
```
