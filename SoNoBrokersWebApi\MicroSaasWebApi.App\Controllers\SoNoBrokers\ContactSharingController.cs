using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Extensions;
using MicroSaasWebApi.Models.SoNoBrokers.ContactSharing;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    /// <summary>
    /// Controller for contact sharing functionality
    /// </summary>
    [ApiController]
    [Route("api/sonobrokers/contact-sharing")]
    [Tags("Contact Sharing")]
    [Authorize]
    public class ContactSharingController : ControllerBase
    {
        private readonly IContactSharingService _contactSharingService;
        private readonly ILogger<ContactSharingController> _logger;

        public ContactSharingController(
            IContactSharingService contactSharingService,
            ILogger<ContactSharingController> logger)
        {
            _contactSharingService = contactSharingService;
            _logger = logger;
        }

        /// <summary>
        /// Create a new contact share request
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<ContactShareResponse>> CreateContactShare([FromBody] CreateContactShareRequest request)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                var contactShare = await _contactSharingService.CreateContactShareAsync(userId, request);
                return Ok(contactShare);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create contact share for user {UserId}", User.GetUserId());
                return StatusCode(500, "An error occurred while creating the contact share");
            }
        }

        /// <summary>
        /// Get contact share by ID
        /// </summary>
        [HttpGet("{contactShareId}")]
        public async Task<ActionResult<ContactShareResponse>> GetContactShare(string contactShareId)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                // Check if user has access to this contact share
                var hasAccess = await _contactSharingService.CanUserAccessContactShareAsync(userId, contactShareId);
                if (!hasAccess)
                {
                    return Forbid("Access denied to this contact share");
                }

                var contactShare = await _contactSharingService.GetContactShareAsync(contactShareId);
                if (contactShare == null)
                {
                    return NotFound("Contact share not found");
                }

                // Mark as viewed if it's the seller viewing
                if (contactShare.SellerId == userId && contactShare.Status == ContactShareStatus.Sent)
                {
                    await _contactSharingService.MarkContactShareAsViewedAsync(contactShareId);
                }

                return Ok(contactShare);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get contact share {ContactShareId} for user {UserId}",
                    contactShareId, User.GetUserId());
                return StatusCode(500, "An error occurred while retrieving the contact share");
            }
        }

        /// <summary>
        /// Get contact shares for current user
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ContactShareSearchResponse>> GetContactShares([FromQuery] ContactShareSearchParams searchParams)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                var result = await _contactSharingService.GetContactSharesAsync(userId, searchParams);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get contact shares for user {UserId}", User.GetUserId());
                return StatusCode(500, "An error occurred while retrieving contact shares");
            }
        }

        /// <summary>
        /// Get contact shares for a specific property
        /// </summary>
        [HttpGet("property/{propertyId}")]
        public async Task<ActionResult<ContactShareSearchResponse>> GetPropertyContactShares(
            string propertyId,
            [FromQuery] ContactShareSearchParams searchParams)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                // TODO: Add authorization check - user should be the property owner or admin

                var result = await _contactSharingService.GetPropertyContactSharesAsync(propertyId, searchParams);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get contact shares for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property contact shares");
            }
        }

        /// <summary>
        /// Update contact share status (seller response)
        /// </summary>
        [HttpPut("{contactShareId}/respond")]
        public async Task<ActionResult> RespondToContactShare(string contactShareId, [FromBody] ContactShareSellerResponse response)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                // Check if user has access to this contact share
                var hasAccess = await _contactSharingService.CanUserAccessContactShareAsync(userId, contactShareId);
                if (!hasAccess)
                {
                    return Forbid("Access denied to this contact share");
                }

                // Verify the user is the seller
                var contactShare = await _contactSharingService.GetContactShareAsync(contactShareId);
                if (contactShare == null)
                {
                    return NotFound("Contact share not found");
                }

                if (contactShare.SellerId != userId)
                {
                    return Forbid("Only the seller can respond to this contact share");
                }

                response.ContactShareId = contactShareId;
                var success = await _contactSharingService.UpdateContactShareStatusAsync(contactShareId, response);

                if (success)
                {
                    return Ok(new { message = "Response recorded successfully" });
                }
                else
                {
                    return BadRequest("Failed to record response");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to respond to contact share {ContactShareId} for user {UserId}",
                    contactShareId, User.GetUserId());
                return StatusCode(500, "An error occurred while recording the response");
            }
        }

        /// <summary>
        /// Get contact share statistics for current user
        /// </summary>
        [HttpGet("stats")]
        public async Task<ActionResult<ContactShareStats>> GetContactShareStats()
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                var stats = await _contactSharingService.GetContactShareStatsAsync(userId);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get contact share stats for user {UserId}", User.GetUserId());
                return StatusCode(500, "An error occurred while retrieving contact share statistics");
            }
        }

        /// <summary>
        /// Get contact share statistics for a property
        /// </summary>
        [HttpGet("property/{propertyId}/stats")]
        public async Task<ActionResult<ContactShareStats>> GetPropertyContactShareStats(string propertyId)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                // TODO: Add authorization check - user should be the property owner or admin

                var stats = await _contactSharingService.GetPropertyContactShareStatsAsync(propertyId);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get contact share stats for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property contact share statistics");
            }
        }

        /// <summary>
        /// Delete a contact share
        /// </summary>
        [HttpDelete("{contactShareId}")]
        public async Task<ActionResult> DeleteContactShare(string contactShareId)
        {
            try
            {
                var userId = User.GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                // Check if user has access to this contact share
                var hasAccess = await _contactSharingService.CanUserAccessContactShareAsync(userId, contactShareId);
                if (!hasAccess)
                {
                    return Forbid("Access denied to this contact share");
                }

                var success = await _contactSharingService.DeleteContactShareAsync(contactShareId);

                if (success)
                {
                    return Ok(new { message = "Contact share deleted successfully" });
                }
                else
                {
                    return NotFound("Contact share not found");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete contact share {ContactShareId} for user {UserId}",
                    contactShareId, User.GetUserId());
                return StatusCode(500, "An error occurred while deleting the contact share");
            }
        }
    }
}
