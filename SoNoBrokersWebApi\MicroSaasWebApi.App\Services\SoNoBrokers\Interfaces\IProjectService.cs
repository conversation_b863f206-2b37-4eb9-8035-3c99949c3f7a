using MicroSaasWebApi.Models.SoNoBrokers;

namespace MicroSaasWebApi.Services.SoNoBrokers.Interfaces
{
    public interface IProjectService
    {
        Task<ProjectSearchResponse> GetProjectsAsync(ProjectSearchRequest request, string userClerkId);
        Task<ProjectResponse?> GetProjectByIdAsync(string id, string userClerkId);
        Task<ProjectResponse> CreateProjectAsync(CreateProjectRequest request, string userClerkId);
        Task<ProjectResponse?> UpdateProjectAsync(UpdateProjectRequest request, string userClerkId);
        Task<bool> DeleteProjectAsync(string id, string userClerkId);
    }
}
