using System.ComponentModel.DataAnnotations;

namespace MicroSaasWebApi.Models.SoNoBrokers.ContactSharing
{
    /// <summary>
    /// Contact sharing entity for buyer-seller direct communication
    /// </summary>
    public class ContactShare
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string PropertyId { get; set; } = string.Empty;
        public string BuyerId { get; set; } = string.Empty;
        public string SellerId { get; set; } = string.Empty;
        public string BuyerName { get; set; } = string.Empty;
        public string BuyerEmail { get; set; } = string.Empty;
        public string? BuyerPhone { get; set; }
        public string? Message { get; set; }
        public ContactShareType ShareType { get; set; } = ContactShareType.ContactRequest;
        public decimal? OfferAmount { get; set; }
        public string? SchedulingPreference { get; set; }
        public DateTime? PreferredVisitDate { get; set; }
        public string? PreferredVisitTime { get; set; }
        public ContactShareStatus Status { get; set; } = ContactShareStatus.Sent;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? RespondedAt { get; set; }
        public string? SellerResponse { get; set; }
        public bool EmailSent { get; set; } = false;
        public DateTime? EmailSentAt { get; set; }
        public string? EmailId { get; set; }
    }

    /// <summary>
    /// Type of contact sharing request
    /// </summary>
    public enum ContactShareType
    {
        ContactRequest = 1,
        PropertyOffer = 2,
        ScheduleVisit = 3,
        OfferWithVisit = 4
    }

    /// <summary>
    /// Status of contact sharing request
    /// </summary>
    public enum ContactShareStatus
    {
        Sent = 1,
        Delivered = 2,
        Viewed = 3,
        Responded = 4,
        Accepted = 5,
        Declined = 6,
        Expired = 7
    }

    /// <summary>
    /// DTO for creating contact share request
    /// </summary>
    public class CreateContactShareRequest
    {
        [Required]
        public string PropertyId { get; set; } = string.Empty;

        [Required]
        public string SellerId { get; set; } = string.Empty;

        [Required]
        [StringLength(100, MinimumLength = 2)]
        public string BuyerName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        public string BuyerEmail { get; set; } = string.Empty;

        [Phone]
        public string? BuyerPhone { get; set; }

        [StringLength(1000)]
        public string? Message { get; set; }

        [Required]
        public ContactShareType ShareType { get; set; }

        [Range(0, 999999999, ErrorMessage = "Offer amount must be positive")]
        public decimal? OfferAmount { get; set; }

        public string? SchedulingPreference { get; set; }

        public DateTime? PreferredVisitDate { get; set; }

        public string? PreferredVisitTime { get; set; }
    }

    /// <summary>
    /// DTO for contact share response
    /// </summary>
    public class ContactShareResponse
    {
        public string Id { get; set; } = string.Empty;
        public string PropertyId { get; set; } = string.Empty;
        public string PropertyTitle { get; set; } = string.Empty;
        public string PropertyAddress { get; set; } = string.Empty;
        public decimal PropertyPrice { get; set; }
        public string BuyerId { get; set; } = string.Empty;
        public string BuyerName { get; set; } = string.Empty;
        public string BuyerEmail { get; set; } = string.Empty;
        public string? BuyerPhone { get; set; }
        public string SellerId { get; set; } = string.Empty;
        public string SellerName { get; set; } = string.Empty;
        public string SellerEmail { get; set; } = string.Empty;
        public string? Message { get; set; }
        public ContactShareType ShareType { get; set; }
        public string ShareTypeDisplay { get; set; } = string.Empty;
        public decimal? OfferAmount { get; set; }
        public string? SchedulingPreference { get; set; }
        public DateTime? PreferredVisitDate { get; set; }
        public string? PreferredVisitTime { get; set; }
        public ContactShareStatus Status { get; set; }
        public string StatusDisplay { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? RespondedAt { get; set; }
        public string? SellerResponse { get; set; }
        public bool EmailSent { get; set; }
        public DateTime? EmailSentAt { get; set; }
    }

    /// <summary>
    /// DTO for seller response to contact share
    /// </summary>
    public class ContactShareSellerResponse
    {
        [Required]
        public string ContactShareId { get; set; } = string.Empty;

        [Required]
        public ContactShareStatus Status { get; set; }

        [StringLength(1000)]
        public string? Response { get; set; }

        public DateTime? CounterOfferAmount { get; set; }

        public DateTime? AlternativeVisitDate { get; set; }

        public string? AlternativeVisitTime { get; set; }
    }

    /// <summary>
    /// DTO for contact share search parameters
    /// </summary>
    public class ContactShareSearchParams
    {
        public string? PropertyId { get; set; }
        public string? BuyerId { get; set; }
        public string? SellerId { get; set; }
        public ContactShareType? ShareType { get; set; }
        public ContactShareStatus? Status { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public decimal? MinOfferAmount { get; set; }
        public decimal? MaxOfferAmount { get; set; }
        public string? Search { get; set; }
        public int Page { get; set; } = 1;
        public int Limit { get; set; } = 20;
        public string SortBy { get; set; } = "createdAt";
        public string SortOrder { get; set; } = "desc";
    }

    /// <summary>
    /// DTO for contact share search response
    /// </summary>
    public class ContactShareSearchResponse
    {
        public List<ContactShareResponse> ContactShares { get; set; } = new();
        public int Total { get; set; }
        public int Page { get; set; }
        public int TotalPages { get; set; }
        public bool HasMore { get; set; }
    }

    /// <summary>
    /// DTO for contact share statistics
    /// </summary>
    public class ContactShareStats
    {
        public int TotalContactShares { get; set; }
        public int ContactRequests { get; set; }
        public int PropertyOffers { get; set; }
        public int ScheduleRequests { get; set; }
        public int PendingResponses { get; set; }
        public int AcceptedRequests { get; set; }
        public int DeclinedRequests { get; set; }
        public decimal AverageOfferAmount { get; set; }
        public decimal HighestOffer { get; set; }
        public DateTime? LastContactShare { get; set; }
        public List<ContactSharePropertyStats> PropertyStats { get; set; } = new();
    }

    /// <summary>
    /// DTO for property-specific contact share statistics
    /// </summary>
    public class ContactSharePropertyStats
    {
        public string PropertyId { get; set; } = string.Empty;
        public string PropertyTitle { get; set; } = string.Empty;
        public int ContactCount { get; set; }
        public int OfferCount { get; set; }
        public decimal? HighestOffer { get; set; }
        public DateTime? LastContact { get; set; }
    }
}
