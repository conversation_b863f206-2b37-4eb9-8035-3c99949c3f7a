using System.Security.Claims;
using MicroSaasWebApi.Models.SoNoBrokers;

namespace MicroSaasWebApi.Extensions
{
    /// <summary>
    /// Extension methods for ClaimsPrincipal to provide common authentication utilities
    /// </summary>
    public static class ClaimsPrincipalExtensions
    {
        /// <summary>
        /// Gets the user ID from the claims principal
        /// </summary>
        /// <param name="principal">The claims principal</param>
        /// <returns>The user ID or null if not found</returns>
        public static string? GetUserId(this ClaimsPrincipal principal)
        {
            return principal?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        }

        /// <summary>
        /// Gets the user email from the claims principal
        /// </summary>
        /// <param name="principal">The claims principal</param>
        /// <returns>The user email or null if not found</returns>
        public static string? GetUserEmail(this ClaimsPrincipal principal)
        {
            return principal?.FindFirst(ClaimTypes.Email)?.Value;
        }

        /// <summary>
        /// Gets the user name from the claims principal
        /// </summary>
        /// <param name="principal">The claims principal</param>
        /// <returns>The user name or null if not found</returns>
        public static string? GetUserName(this ClaimsPrincipal principal)
        {
            return principal?.FindFirst(ClaimTypes.Name)?.Value;
        }

        /// <summary>
        /// Checks if the user has admin role
        /// </summary>
        /// <param name="principal">The claims principal</param>
        /// <returns>True if user is admin, false otherwise</returns>
        public static bool IsAdmin(this ClaimsPrincipal principal)
        {
            if (principal == null) return false;

            // Check for admin role in claims
            var roleClaim = principal.FindFirst(ClaimTypes.Role)?.Value;
            if (roleClaim != null && (roleClaim == "ADMIN" || roleClaim == "SUPER_ADMIN"))
            {
                return true;
            }

            // Check for custom admin claims
            var adminClaim = principal.FindFirst("is_admin")?.Value;
            if (adminClaim != null && bool.TryParse(adminClaim, out bool isAdmin))
            {
                return isAdmin;
            }

            return false;
        }

        /// <summary>
        /// Checks if the user has a specific role
        /// </summary>
        /// <param name="principal">The claims principal</param>
        /// <param name="role">The role to check</param>
        /// <returns>True if user has the role, false otherwise</returns>
        public static bool HasRole(this ClaimsPrincipal principal, string role)
        {
            if (principal == null || string.IsNullOrEmpty(role)) return false;

            return principal.IsInRole(role) || 
                   principal.FindFirst(ClaimTypes.Role)?.Value == role;
        }

        /// <summary>
        /// Checks if the user has a specific role
        /// </summary>
        /// <param name="principal">The claims principal</param>
        /// <param name="role">The role to check</param>
        /// <returns>True if user has the role, false otherwise</returns>
        public static bool HasRole(this ClaimsPrincipal principal, UserRole role)
        {
            return HasRole(principal, role.ToString());
        }

        /// <summary>
        /// Gets the user's role from claims
        /// </summary>
        /// <param name="principal">The claims principal</param>
        /// <returns>The user role or null if not found</returns>
        public static UserRole? GetUserRole(this ClaimsPrincipal principal)
        {
            if (principal == null) return null;

            var roleClaim = principal.FindFirst(ClaimTypes.Role)?.Value;
            if (roleClaim != null && Enum.TryParse<UserRole>(roleClaim, out var role))
            {
                return role;
            }

            return null;
        }

        /// <summary>
        /// Checks if the user is authenticated
        /// </summary>
        /// <param name="principal">The claims principal</param>
        /// <returns>True if authenticated, false otherwise</returns>
        public static bool IsAuthenticated(this ClaimsPrincipal principal)
        {
            return principal?.Identity?.IsAuthenticated == true;
        }

        /// <summary>
        /// Gets a custom claim value
        /// </summary>
        /// <param name="principal">The claims principal</param>
        /// <param name="claimType">The claim type</param>
        /// <returns>The claim value or null if not found</returns>
        public static string? GetClaimValue(this ClaimsPrincipal principal, string claimType)
        {
            return principal?.FindFirst(claimType)?.Value;
        }
    }
}
