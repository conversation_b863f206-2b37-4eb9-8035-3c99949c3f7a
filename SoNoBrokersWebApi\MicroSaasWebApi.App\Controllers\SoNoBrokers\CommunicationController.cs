using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/sonobrokers/communication")]
    [Tags("Communication")]
    public class CommunicationController : ControllerBase
    {
        private readonly ICommunicationService _communicationService;
        private readonly ILogger<CommunicationController> _logger;

        public CommunicationController(
            ICommunicationService communicationService,
            ILogger<CommunicationController> logger)
        {
            _communicationService = communicationService;
            _logger = logger;
        }

        /// <summary>
        /// Send concierge service inquiry
        /// </summary>
        [HttpPost("contact-concierge")]
        public async Task<ActionResult<ContactConciergeResponse>> ContactConcierge([FromBody] ContactConciergeRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Name) || string.IsNullOrEmpty(request.Email))
                {
                    return BadRequest(new { error = "Name and email are required" });
                }

                var result = await _communicationService.SendConciergeInquiryAsync(request);

                if (!result.Success)
                {
                    return StatusCode(500, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing concierge inquiry");
                return StatusCode(500, new { error = "Failed to send inquiry" });
            }
        }

        /// <summary>
        /// Add email to waiting list
        /// </summary>
        [HttpPost("waiting-list")]
        public async Task<ActionResult<WaitingListResponse>> AddToWaitingList([FromBody] WaitingListRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Email))
                {
                    return BadRequest(new { error = "Email is required" });
                }

                var result = await _communicationService.AddToWaitingListAsync(request);

                if (!result.Success)
                {
                    return StatusCode(500, result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding to waiting list");
                return StatusCode(500, new { error = "Error adding to waiting list" });
            }
        }

        /// <summary>
        /// Get geolocation information by IP
        /// </summary>
        [HttpGet("geo")]
        [AllowAnonymous]
        public async Task<ActionResult<GeoLocationResponse>> GetGeoLocation([FromQuery] string? ip)
        {
            try
            {
                var request = new GeoLocationRequest { Ip = ip };
                var result = await _communicationService.GetLocationByIpAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting geo location");
                return StatusCode(500, new { error = "Failed to get location data" });
            }
        }

        /// <summary>
        /// Get all enum values from database
        /// </summary>
        [HttpGet("enums")]
        [AllowAnonymous]
        public async Task<ActionResult<EnumValuesResponse>> GetEnumValues()
        {
            try
            {
                var result = await _communicationService.GetEnumValuesAsync();

                // Add cache headers
                Response.Headers.Append("Cache-Control", "public, s-maxage=300, stale-while-revalidate=600");

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting enum values");
                return StatusCode(500, new { error = "Unable to load application configuration from database" });
            }
        }
    }
}
