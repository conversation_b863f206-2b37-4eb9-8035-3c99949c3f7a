using MicroSaasWebApi.App.Context;

namespace MicroSaasWebApi.Services.SoNoBrokers
{
    public interface IDatabaseMigrationService
    {
        Task RunMigrationsAsync();
        Task<bool> IsDatabaseInitializedAsync();
    }

    public class DatabaseMigrationService : IDatabaseMigrationService
    {
        private readonly IDapperDbContext _dbContext;
        private readonly ILogger<DatabaseMigrationService> _logger;
        private readonly IWebHostEnvironment _environment;

        public DatabaseMigrationService(
            IDapperDbContext dbContext,
            ILogger<DatabaseMigrationService> logger,
            IWebHostEnvironment environment)
        {
            _dbContext = dbContext;
            _logger = logger;
            _environment = environment;
        }

        public async Task<bool> IsDatabaseInitializedAsync()
        {
            try
            {
                // Check if the migrations table exists
                const string sql = @"
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'snb' 
                        AND table_name = 'schema_migrations'
                    )";

                return await _dbContext.QuerySingleAsync<bool>(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if database is initialized");
                return false;
            }
        }

        public async Task RunMigrationsAsync()
        {
            try
            {
                _logger.LogInformation("Starting database migrations for SoNoBrokers");

                // Create migrations tracking table if it doesn't exist
                await CreateMigrationsTableAsync();

                // Get all migration files
                var migrationFiles = GetMigrationFiles();

                foreach (var migrationFile in migrationFiles.OrderBy(f => f.Name))
                {
                    await RunMigrationAsync(migrationFile);
                }

                _logger.LogInformation("Database migrations completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error running database migrations");
                throw;
            }
        }

        private async Task CreateMigrationsTableAsync()
        {
            const string sql = @"
                CREATE TABLE IF NOT EXISTS public.schema_migrations (
                    id SERIAL PRIMARY KEY,
                    migration_name VARCHAR(255) NOT NULL UNIQUE,
                    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    checksum VARCHAR(64) NOT NULL
                );";

            await _dbContext.ExecuteAsync(sql);
        }

        private List<FileInfo> GetMigrationFiles()
        {
            var migrationsPath = Path.Combine(_environment.ContentRootPath, "Migrations");

            if (!Directory.Exists(migrationsPath))
            {
                _logger.LogWarning("Migrations directory not found: {Path}", migrationsPath);
                return new List<FileInfo>();
            }

            var directory = new DirectoryInfo(migrationsPath);

            // Only get numbered migration files (0001_, 0002_, etc.) and order them properly
            return directory.GetFiles("*.sql")
                .Where(f => System.Text.RegularExpressions.Regex.IsMatch(f.Name, @"^\d{4}_.*\.sql$"))
                .OrderBy(f => f.Name)
                .ToList();
        }

        private async Task RunMigrationAsync(FileInfo migrationFile)
        {
            try
            {
                var migrationName = Path.GetFileNameWithoutExtension(migrationFile.Name);

                // Check if migration has already been run
                const string checkSql = @"
                    SELECT COUNT(*) FROM public.schema_migrations
                    WHERE migration_name = @MigrationName";

                var alreadyRun = await _dbContext.QuerySingleAsync<int>(checkSql, new { MigrationName = migrationName });

                if (alreadyRun > 0)
                {
                    _logger.LogInformation("Migration {MigrationName} already executed, skipping", migrationName);
                    return;
                }

                // Read migration file
                var migrationSql = await File.ReadAllTextAsync(migrationFile.FullName);
                var checksum = CalculateChecksum(migrationSql);

                _logger.LogInformation("Executing migration: {MigrationName}", migrationName);

                // Execute migration in a transaction
                var transaction = await _dbContext.BeginTransactionAsync();
                try
                {
                    // Execute the migration SQL
                    await _dbContext.ExecuteAsync(migrationSql);

                    // Record the migration
                    const string recordSql = @"
                        INSERT INTO public.schema_migrations (migration_name, checksum)
                        VALUES (@MigrationName, @Checksum)";

                    await _dbContext.ExecuteAsync(recordSql, new
                    {
                        MigrationName = migrationName,
                        Checksum = checksum
                    });

                    await _dbContext.CommitTransactionAsync();
                    _logger.LogInformation("Migration {MigrationName} executed successfully", migrationName);
                }
                catch
                {
                    await _dbContext.RollbackTransactionAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing migration {MigrationName}", migrationFile.Name);
                throw;
            }
        }

        private static string CalculateChecksum(string content)
        {
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(content));
            return Convert.ToHexString(hash);
        }
    }

    // Extension method to register migration service
    public static class MigrationServiceExtensions
    {
        public static IServiceCollection AddDatabaseMigrations(this IServiceCollection services)
        {
            services.AddScoped<IDapperDbContext, DapperDbContext>();
            services.AddScoped<IDatabaseMigrationService, DatabaseMigrationService>();
            return services;
        }

        public static async Task<IApplicationBuilder> UseDatabaseMigrations(this IApplicationBuilder app)
        {
            using var scope = app.ApplicationServices.CreateScope();
            var migrationService = scope.ServiceProvider.GetRequiredService<IDatabaseMigrationService>();

            try
            {
                await migrationService.RunMigrationsAsync();
            }
            catch (Exception ex)
            {
                var logger = scope.ServiceProvider.GetRequiredService<ILogger<DatabaseMigrationService>>();
                logger.LogError(ex, "Failed to run database migrations on startup");

                // In development, we might want to continue despite migration failures
                // In production, you might want to throw to prevent startup
                var environment = scope.ServiceProvider.GetRequiredService<IWebHostEnvironment>();
                if (!environment.IsDevelopment())
                {
                    throw;
                }
            }

            return app;
        }
    }
}
