using System.Data;

namespace MicroSaasWebApi.Data.Context
{
    /// <summary>
    /// Dapper database context interface for SoNoBrokers operations
    /// </summary>
    public interface IDapperDbContext
    {
        // Basic query operations
        Task<IEnumerable<T>> QueryAsync<T>(string sql, object? param = null);
        Task<T?> QueryFirstOrDefaultAsync<T>(string sql, object? param = null);
        Task<T> QueryFirstAsync<T>(string sql, object? param = null);
        Task<T> QuerySingleAsync<T>(string sql, object? param = null);
        Task<T?> QuerySingleOrDefaultAsync<T>(string sql, object? param = null);
        Task<int> ExecuteAsync(string sql, object? param = null);
        Task<T> ExecuteScalarAsync<T>(string sql, object? param = null);
        
        // Connection management
        IDbConnection GetConnection();
        IDbConnection CreateConnection();
        
        // Transaction support
        Task<IDbTransaction> BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
        
        // Stored procedures and functions
        Task<IEnumerable<T>> ExecuteStoredProcedureAsync<T>(string procedureName, object? parameters = null);
        Task<T?> ExecuteStoredProcedureSingleAsync<T>(string procedureName, object? parameters = null);
        Task<int> ExecuteStoredProcedureAsync(string procedureName, object? parameters = null);
        Task<IEnumerable<T>> ExecuteFunctionAsync<T>(string functionName, object? parameters = null);
        
        // Bulk operations
        Task<int> BulkInsertAsync<T>(IEnumerable<T> entities, string tableName);
        Task<int> BulkUpdateAsync<T>(IEnumerable<T> entities, string tableName, string keyColumn);
        Task<int> BulkDeleteAsync<T>(IEnumerable<T> entities, string tableName, string keyColumn);
        
        // Utility methods
        Task<bool> CanConnectAsync();
        Task ExecuteInTransactionAsync(Func<IDapperDbContext, Task> operation);
        Task<T> ExecuteInTransactionAsync<T>(Func<IDapperDbContext, Task<T>> operation);
    }
}
