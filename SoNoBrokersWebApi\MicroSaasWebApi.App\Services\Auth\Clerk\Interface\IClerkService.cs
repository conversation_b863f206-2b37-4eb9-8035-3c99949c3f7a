using System.Security.Claims;
using MicroSaasWebApi.Models.Auth;
using MicroSaasWebApi.Models.Auth.Clerk;

namespace MicroSaasWebApi.Services.Auth.Clerk.Interface
{
    public interface IClerkService
    {
        Task<ClerkUser?> GetUserByIdAsync(string userId);
        Task<ClerkUser?> GetUserByEmailAsync(string email);
        ClaimsPrincipal? ValidateJwtToken(string token);
        UserInfo? ExtractUserInfoFromToken(string token);

        // Additional methods required by ClerkAuthService
        Task<ClerkUser?> AuthenticateUserAsync(string email, string password);
        Task<ClerkUser?> CreateUserAsync(string email, string password, string firstName, string lastName);
        Task<ClerkUser?> GetUserAsync(string userId);
        Task<bool> UpdateUserAsync(string userId, string? firstName = null, string? lastName = null);
        Task<bool> DeleteUserAsync(string userId);
    }

    public class ClerkUser
    {
        public string Id { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string EmailAddress { get; set; } = string.Empty;
        public bool EmailVerified { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string? ProfileImageUrl { get; set; }
        public Dictionary<string, object>? PublicMetadata { get; set; }
        public Dictionary<string, object>? PrivateMetadata { get; set; }
        public Dictionary<string, object>? UnsafeMetadata { get; set; }
    }
}
