'use client'

import { useState } from 'react'
import { useUser } from '@clerk/nextjs'
import { Button } from '@/components/ui/button'
import { DollarSign, Calendar } from 'lucide-react'
import { ContactShareModal } from './ContactShareModal'

interface PropertyActionButtonProps {
  propertyId: string
  sellerId: string
  sellerName: string
  propertyTitle: string
  propertyPrice: number
  propertyAddress: string
  className?: string
}

/**
 * Property Offer Button Component
 * Opens modal specifically for submitting property offers
 */
export function PropertyOfferButton({
  propertyId,
  sellerId,
  sellerName,
  propertyTitle,
  propertyPrice,
  propertyAddress,
  className = ''
}: PropertyActionButtonProps) {
  const { user, isSignedIn } = useUser()
  const [isModalOpen, setIsModalOpen] = useState(false)

  const handleClick = () => {
    if (!isSignedIn) {
      // Redirect to sign in
      window.location.href = '/sign-in'
      return
    }

    // Open offer modal
    setIsModalOpen(true)
  }

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        className={className}
        onClick={handleClick}
      >
        <DollarSign className="h-4 w-4 mr-2" />
        Make Offer
      </Button>

      {isModalOpen && (
        <ContactShareModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          propertyId={propertyId}
          sellerId={sellerId}
          sellerName={sellerName}
          propertyTitle={propertyTitle}
          propertyPrice={propertyPrice}
          propertyAddress={propertyAddress}
          defaultBuyerName={user?.fullName || ''}
          defaultBuyerEmail={user?.primaryEmailAddress?.emailAddress || ''}
          defaultShareType="offer"
        />
      )}
    </>
  )
}

/**
 * Property Visit Button Component
 * Opens modal specifically for scheduling property visits
 */
export function PropertyVisitButton({
  propertyId,
  sellerId,
  sellerName,
  propertyTitle,
  propertyPrice,
  propertyAddress,
  className = ''
}: PropertyActionButtonProps) {
  const { user, isSignedIn } = useUser()
  const [isModalOpen, setIsModalOpen] = useState(false)

  const handleClick = () => {
    if (!isSignedIn) {
      // Redirect to sign in
      window.location.href = '/sign-in'
      return
    }

    // Open visit scheduling modal
    setIsModalOpen(true)
  }

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        className={className}
        onClick={handleClick}
      >
        <Calendar className="h-4 w-4 mr-2" />
        Schedule Visit
      </Button>

      {isModalOpen && (
        <ContactShareModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          propertyId={propertyId}
          sellerId={sellerId}
          sellerName={sellerName}
          propertyTitle={propertyTitle}
          propertyPrice={propertyPrice}
          propertyAddress={propertyAddress}
          defaultBuyerName={user?.fullName || ''}
          defaultBuyerEmail={user?.primaryEmailAddress?.emailAddress || ''}
          defaultShareType="visit"
        />
      )}
    </>
  )
}

/**
 * Combined Property Actions Component
 * Shows both offer and visit buttons together
 */
export function PropertyActions({
  propertyId,
  sellerId,
  sellerName,
  propertyTitle,
  propertyPrice,
  propertyAddress,
  className = ''
}: PropertyActionButtonProps) {
  return (
    <div className={`flex gap-2 ${className}`}>
      <PropertyOfferButton
        propertyId={propertyId}
        sellerId={sellerId}
        sellerName={sellerName}
        propertyTitle={propertyTitle}
        propertyPrice={propertyPrice}
        propertyAddress={propertyAddress}
        className="flex-1"
      />
      <PropertyVisitButton
        propertyId={propertyId}
        sellerId={sellerId}
        sellerName={sellerName}
        propertyTitle={propertyTitle}
        propertyPrice={propertyPrice}
        propertyAddress={propertyAddress}
        className="flex-1"
      />
    </div>
  )
}

/**
 * Property Contact Actions Component
 * Shows contact, offer, and visit buttons together
 */
export function PropertyContactActions({
  propertyId,
  sellerId,
  sellerName,
  propertyTitle,
  propertyPrice,
  propertyAddress,
  className = ''
}: PropertyActionButtonProps) {
  const { user, isSignedIn } = useUser()
  const [activeModal, setActiveModal] = useState<'contact' | 'offer' | 'visit' | null>(null)

  const handleAction = (action: 'contact' | 'offer' | 'visit') => {
    if (!isSignedIn) {
      window.location.href = '/sign-in'
      return
    }
    setActiveModal(action)
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Primary Contact Button */}
      <Button
        className="w-full"
        onClick={() => handleAction('contact')}
      >
        Contact Seller
      </Button>

      {/* Secondary Actions */}
      <div className="grid grid-cols-2 gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleAction('offer')}
        >
          <DollarSign className="h-4 w-4 mr-2" />
          Make Offer
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleAction('visit')}
        >
          <Calendar className="h-4 w-4 mr-2" />
          Schedule Visit
        </Button>
      </div>

      {/* Contact Share Modal */}
      {activeModal && (
        <ContactShareModal
          isOpen={true}
          onClose={() => setActiveModal(null)}
          propertyId={propertyId}
          sellerId={sellerId}
          sellerName={sellerName}
          propertyTitle={propertyTitle}
          propertyPrice={propertyPrice}
          propertyAddress={propertyAddress}
          defaultBuyerName={user?.fullName || ''}
          defaultBuyerEmail={user?.primaryEmailAddress?.emailAddress || ''}
          defaultShareType={activeModal}
        />
      )}
    </div>
  )
}

/**
 * Compact Property Actions for Property Cards
 */
export function CompactPropertyActions({
  propertyId,
  sellerId,
  sellerName,
  propertyTitle,
  propertyPrice,
  propertyAddress,
  className = ''
}: PropertyActionButtonProps) {
  const { user, isSignedIn } = useUser()
  const [activeModal, setActiveModal] = useState<'offer' | 'visit' | null>(null)

  const handleAction = (action: 'offer' | 'visit') => {
    if (!isSignedIn) {
      window.location.href = '/sign-in'
      return
    }
    setActiveModal(action)
  }

  return (
    <div className={`flex gap-1 ${className}`}>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleAction('offer')}
        title="Make an offer on this property"
      >
        <DollarSign className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleAction('visit')}
        title="Schedule a visit to this property"
      >
        <Calendar className="h-4 w-4" />
      </Button>

      {/* Modal */}
      {activeModal && (
        <ContactShareModal
          isOpen={true}
          onClose={() => setActiveModal(null)}
          propertyId={propertyId}
          sellerId={sellerId}
          sellerName={sellerName}
          propertyTitle={propertyTitle}
          propertyPrice={propertyPrice}
          propertyAddress={propertyAddress}
          defaultBuyerName={user?.fullName || ''}
          defaultBuyerEmail={user?.primaryEmailAddress?.emailAddress || ''}
          defaultShareType={activeModal}
        />
      )}
    </div>
  )
}
