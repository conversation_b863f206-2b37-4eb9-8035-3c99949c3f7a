using MicroSaasWebApi.Models.SoNoBrokers.ContactSharing;

namespace MicroSaasWebApi.Services.SoNoBrokers.Interfaces
{
    /// <summary>
    /// Interface for contact sharing service
    /// </summary>
    public interface IContactSharingService
    {
        /// <summary>
        /// Create a new contact share request
        /// </summary>
        Task<ContactShareResponse> CreateContactShareAsync(string buyerId, CreateContactShareRequest request);

        /// <summary>
        /// Get contact share by ID
        /// </summary>
        Task<ContactShareResponse?> GetContactShareAsync(string contactShareId);

        /// <summary>
        /// Get contact shares for a user (buyer or seller)
        /// </summary>
        Task<ContactShareSearchResponse> GetContactSharesAsync(string userId, ContactShareSearchParams searchParams);

        /// <summary>
        /// Get contact shares for a specific property
        /// </summary>
        Task<ContactShareSearchResponse> GetPropertyContactSharesAsync(string propertyId, ContactShareSearchParams searchParams);

        /// <summary>
        /// Update contact share status (seller response)
        /// </summary>
        Task<bool> UpdateContactShareStatusAsync(string contactShareId, ContactShareSellerResponse response);

        /// <summary>
        /// Get contact share statistics for a user
        /// </summary>
        Task<ContactShareStats> GetContactShareStatsAsync(string userId);

        /// <summary>
        /// Get contact share statistics for a property
        /// </summary>
        Task<ContactShareStats> GetPropertyContactShareStatsAsync(string propertyId);

        /// <summary>
        /// Check if user can access contact share
        /// </summary>
        Task<bool> CanUserAccessContactShareAsync(string userId, string contactShareId);

        /// <summary>
        /// Mark contact share as viewed
        /// </summary>
        Task<bool> MarkContactShareAsViewedAsync(string contactShareId);

        /// <summary>
        /// Delete contact share
        /// </summary>
        Task<bool> DeleteContactShareAsync(string contactShareId);

        /// <summary>
        /// Get pending contact shares for seller (for reminders)
        /// </summary>
        Task<List<ContactShareResponse>> GetPendingContactSharesAsync(string sellerId, int daysOld = 3);

        /// <summary>
        /// Send reminder emails for pending contact shares
        /// </summary>
        Task<bool> SendReminderEmailsAsync();

        /// <summary>
        /// Get contact share analytics for admin
        /// </summary>
        Task<ContactShareStats> GetAdminContactShareStatsAsync(DateTime? fromDate = null, DateTime? toDate = null);

        /// <summary>
        /// Get all contact shares for admin
        /// </summary>
        Task<ContactShareSearchResponse> GetAllContactSharesAsync(ContactShareSearchParams searchParams);
    }
}
