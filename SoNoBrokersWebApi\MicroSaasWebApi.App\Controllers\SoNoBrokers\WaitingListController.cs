using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.Core;
using MicroSaasWebApi.Models.SoNoBrokers;

using System.ComponentModel.DataAnnotations;
using System.Text.Json;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/sonobrokers/[controller]")]
    public class WaitingListController : ControllerBase
    {
        private readonly MicroSaasWebApi.App.Context.IDapperDbContext _dbContext;
        private readonly ILogger<WaitingListController> _logger;
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;

        public WaitingListController(
            MicroSaasWebApi.App.Context.IDapperDbContext dbContext,
            ILogger<WaitingListController> logger,
            IConfiguration configuration,
            HttpClient httpClient)
        {
            _dbContext = dbContext;
            _logger = logger;
            _configuration = configuration;
            _httpClient = httpClient;
        }

        /// <summary>
        /// Add email to waiting list
        /// </summary>
        /// <param name="request">Email to add to waiting list</param>
        /// <returns>Success message</returns>
        [HttpPost]





        public async Task<ActionResult<ApiResponse<object>>> AddToWaitingList([FromBody] WaitingListRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.Email))
                {
                    return BadRequest(ApiResponse<object>.ErrorResult("Email is required"));
                }

                // Validate email format
                if (!IsValidEmail(request.Email))
                {
                    return BadRequest(ApiResponse<object>.ErrorResult("Invalid email format"));
                }

                // Check if email already exists in waiting list
                var existingEntry = await _dbContext.QueryFirstOrDefaultAsync<WaitingList>(
                    "SELECT * FROM snb.waiting_list WHERE LOWER(email) = LOWER(@email)",
                    new { email = request.Email.Trim() });

                if (existingEntry != null)
                {
                    return Conflict(ApiResponse<object>.ErrorResult("Email already exists in waiting list"));
                }

                // Add to waiting list
                var waitingListEntry = new WaitingList
                {
                    Id = Guid.NewGuid().ToString(),
                    Email = request.Email.ToLower().Trim(),
                    Name = request.Name?.Trim(),
                    Source = request.Source ?? "website",
                    CreatedAt = DateTime.UtcNow
                };

                await _dbContext.ExecuteAsync(
                    "INSERT INTO snb.waiting_list (id, email, name, source, created_at) VALUES (@Id, @Email, @Name, @Source, @CreatedAt)",
                    waitingListEntry);

                // Send welcome email using Resend
                await SendWelcomeEmail(request.Email, request.Name);

                return Ok(ApiResponse<object>.SuccessResult(
                    new { email = request.Email },
                    "Email added to waiting list successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding email to waiting list: {Email}", request.Email);
                return StatusCode(500, ApiResponse<object>.ErrorResult("Failed to add email to waiting list"));
            }
        }

        /// <summary>
        /// Get waiting list statistics (admin only)
        /// </summary>
        /// <returns>Waiting list statistics</returns>
        [HttpGet("stats")]



        public async Task<ActionResult<ApiResponse<WaitingListStats>>> GetWaitingListStats()
        {
            try
            {
                var totalCount = await _dbContext.QuerySingleAsync<int>("SELECT COUNT(*) FROM snb.waiting_list");
                var todayCount = await _dbContext.QuerySingleAsync<int>(
                    "SELECT COUNT(*) FROM snb.waiting_list WHERE DATE(created_at) = DATE(@today)",
                    new { today = DateTime.UtcNow });
                var thisWeekCount = await _dbContext.QuerySingleAsync<int>(
                    "SELECT COUNT(*) FROM snb.waiting_list WHERE created_at >= @weekAgo",
                    new { weekAgo = DateTime.UtcNow.AddDays(-7) });
                var thisMonthCount = await _dbContext.QuerySingleAsync<int>(
                    "SELECT COUNT(*) FROM snb.waiting_list WHERE created_at >= @monthAgo",
                    new { monthAgo = DateTime.UtcNow.AddDays(-30) });

                var stats = new WaitingListStats
                {
                    TotalCount = totalCount,
                    TodayCount = todayCount,
                    ThisWeekCount = thisWeekCount,
                    ThisMonthCount = thisMonthCount,
                    LastUpdated = DateTime.UtcNow
                };

                return Ok(ApiResponse<WaitingListStats>.SuccessResult(stats, "Statistics retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving waiting list statistics");
                return StatusCode(500, ApiResponse<WaitingListStats>.ErrorResult("Failed to retrieve statistics"));
            }
        }

        /// <summary>
        /// Check if email exists in waiting list
        /// </summary>
        /// <param name="email">Email to check</param>
        /// <returns>Whether email exists in waiting list</returns>
        [HttpGet("check/{email}")]



        public async Task<ActionResult<ApiResponse<EmailCheckResult>>> CheckEmail(string email)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(email) || !IsValidEmail(email))
                {
                    return BadRequest(ApiResponse<EmailCheckResult>.ErrorResult("Invalid email format"));
                }

                var exists = await _dbContext.QuerySingleAsync<bool>(
                    "SELECT EXISTS(SELECT 1 FROM snb.waiting_list WHERE LOWER(email) = LOWER(@email))",
                    new { email });

                var result = new EmailCheckResult
                {
                    Email = email,
                    Exists = exists,
                    CheckedAt = DateTime.UtcNow
                };

                return Ok(ApiResponse<EmailCheckResult>.SuccessResult(result, "Email check completed"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking email in waiting list: {Email}", email);
                return StatusCode(500, ApiResponse<EmailCheckResult>.ErrorResult("Failed to check email"));
            }
        }

        private async Task SendWelcomeEmail(string email, string? name)
        {
            try
            {
                var resendApiKey = _configuration["ExternalServices:Email:Resend:ApiKey"];
                if (string.IsNullOrEmpty(resendApiKey))
                {
                    _logger.LogWarning("Resend API key not configured, skipping welcome email");
                    return;
                }

                var emailData = new
                {
                    from = "SoNoBrokers <<EMAIL>>",
                    to = new[] { email },
                    subject = "Welcome to SoNoBrokers Waiting List!",
                    html = GenerateWelcomeEmailHtml(name ?? "there")
                };

                var jsonContent = JsonSerializer.Serialize(emailData);
                var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {resendApiKey}");

                var response = await _httpClient.PostAsync("https://api.resend.com/emails", content);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to send welcome email. Status: {StatusCode}, Error: {Error}",
                        response.StatusCode, errorContent);
                }
                else
                {
                    _logger.LogInformation("Welcome email sent successfully to {Email}", email);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending welcome email to {Email}", email);
            }
        }

        private static string GenerateWelcomeEmailHtml(string name)
        {
            return $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h1 style='color: #2563eb;'>Welcome to SoNoBrokers!</h1>
                        <p>Hi {name},</p>
                        <p>Thank you for joining our waiting list! We're excited to have you on board.</p>
                        <p>SoNoBrokers is revolutionizing the real estate industry by connecting buyers and sellers directly, eliminating the need for traditional brokers and saving you thousands in fees.</p>
                        <h3>What's Next?</h3>
                        <ul>
                            <li>We'll keep you updated on our launch progress</li>
                            <li>You'll be among the first to access our platform</li>
                            <li>Early access to exclusive features and pricing</li>
                        </ul>
                        <p>Stay tuned for updates, and thank you for being part of the SoNoBrokers community!</p>
                        <p>Best regards,<br>The SoNoBrokers Team</p>
                        <hr style='margin: 30px 0; border: none; border-top: 1px solid #eee;'>
                        <p style='font-size: 12px; color: #666;'>
                            If you have any questions, feel free to reply to this email or contact <NAME_EMAIL>
                        </p>
                    </div>
                </body>
                </html>";
        }

        private static bool IsValidEmail(string email)
        {
            try
            {
                var emailAttribute = new EmailAddressAttribute();
                return emailAttribute.IsValid(email);
            }
            catch
            {
                return false;
            }
        }
    }

    // WaitingListRequest moved to MicroSaasWebApi.Models.SoNoBrokers.CommunicationDTOs

    public class WaitingListStats
    {
        public int TotalCount { get; set; }
        public int TodayCount { get; set; }
        public int ThisWeekCount { get; set; }
        public int ThisMonthCount { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    public class EmailCheckResult
    {
        public string Email { get; set; } = string.Empty;
        public bool Exists { get; set; }
        public DateTime CheckedAt { get; set; }
    }
}
