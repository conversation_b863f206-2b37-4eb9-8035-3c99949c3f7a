import { MortgageService } from './MortgageService'
// Removed auth import - no authentication required for browsing mortgage services
import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function MortgagePage({ params, searchParams }: PageProps) {
  // No authentication required for browsing mortgage services
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/services/mortgage')
  }

  // Default to buyer for mortgage services
  const userType = resolvedSearchParams.userType || 'buyer'

  return (
    <MortgageService
      userType={userType}
      isSignedIn={isSignedIn}
      country={country.toUpperCase()}
    />
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'buyer'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Mortgage Services for ${userType === 'buyer' ? 'Home Buyers' : 'Property Owners'} in ${country} | SoNoBrokers`,
    description: `Find trusted mortgage brokers and lenders in ${country}. Compare rates, get pre-approved, and secure the best financing for your property purchase or refinance.`,
    keywords: `mortgage broker, home loans, mortgage rates, ${userType}, ${country}, financing, pre-approval`,
  }
}
