using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    [Table("Conversation", Schema = "snb")]
    public class Conversation
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        public string? PropertyId { get; set; }

        [Required]
        [Column(TypeName = "jsonb")]
        public string Participants { get; set; } = string.Empty;

        public DateTime LastMessageAt { get; set; } = DateTime.UtcNow;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        [ForeignKey("PropertyId")]
        public virtual Property? Property { get; set; }

        public virtual ICollection<Message> Messages { get; set; } = new List<Message>();

        // Helper methods for JSON fields
        public T? GetParticipantsAs<T>() where T : class
        {
            if (string.IsNullOrEmpty(Participants)) return null;
            try
            {
                return JsonSerializer.Deserialize<T>(Participants);
            }
            catch
            {
                return null;
            }
        }

        public void SetParticipants<T>(T participants) where T : class
        {
            Participants = participants != null ? JsonSerializer.Serialize(participants) : "[]";
        }
    }
}
