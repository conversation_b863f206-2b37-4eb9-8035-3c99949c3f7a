# =============================================================================
# SoNoBrokers React Application - Development Override
# Use: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
# =============================================================================

services:
  # ===========================================================================
  # Development Frontend with Hot Reload
  # ===========================================================================
  frontend:
    build:
      target: development
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-http://localhost:8080}
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
    volumes:
      - .:/app:cached
      - frontend-node-modules:/app/node_modules
      - frontend-next-cache:/app/.next
    command: ["npm", "run", "dev"]
    stdin_open: true
    tty: true
    ports:
      - "${FRONTEND_PORT:-3000}:3000"

# =============================================================================
# Development Volumes
# =============================================================================
volumes:
  frontend-node-modules:
    driver: local
  frontend-next-cache:
    driver: local
