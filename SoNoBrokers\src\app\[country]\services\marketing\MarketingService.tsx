import React from 'react'
import { ServiceLayout } from '@/components/shared/services/ServiceLayout'

interface MarketingServiceProps {
  userType: 'buyer' | 'seller'
  isSignedIn: boolean
  country: string
}

// Mock data - in real implementation, this would come from your database and Google Places API
const getMockProviders = (country: string) => {
  const baseProviders = [
    {
      id: '1',
      name: '<PERSON>',
      businessName: 'Kim Digital Marketing',
      serviceType: 'Real Estate Marketing Specialist',
      location: country === 'CA' ? 'Toronto, ON' : 'New York, NY',
      distance: '2.2 km',
      rating: 4.9,
      reviewCount: 167,
      price: 'From $500',
      specialties: ['Social Media Marketing', 'Google Ads', 'Professional Listings', 'Video Marketing'],
      verified: true,
      isAdvertiser: true,
      isPremium: true,
      image: '/api/placeholder/80/80',
      phone: '******-0191',
      email: '<EMAIL>',
      website: 'https://kimdigital.com',
      description: 'Digital marketing expert specializing in real estate. Proven strategies to maximize property exposure and attract qualified buyers.',
      coordinates: country === 'CA' ? { lat: 43.6532, lng: -79.3832 } : { lat: 40.7128, lng: -74.0060 }
    },
    {
      id: '2',
      name: '<PERSON>',
      businessName: 'Torres Marketing Solutions',
      serviceType: 'Property Marketing Consultant',
      location: country === 'CA' ? 'Vancouver, BC' : 'Los Angeles, CA',
      distance: '3.6 km',
      rating: 4.8,
      reviewCount: 134,
      price: 'From $400',
      specialties: ['Print Advertising', 'Online Listings', 'Open House Promotion', 'Brochure Design'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/80/80',
      phone: '******-0192',
      description: 'Comprehensive marketing solutions for property sellers. Traditional and digital marketing strategies combined.',
      coordinates: country === 'CA' ? { lat: 49.2827, lng: -123.1207 } : { lat: 34.0522, lng: -118.2437 }
    },
    {
      id: '3',
      name: 'Lisa Anderson',
      businessName: 'Anderson Property Promotion',
      serviceType: 'Marketing Specialist',
      location: country === 'CA' ? 'Calgary, AB' : 'Chicago, IL',
      distance: '4.9 km',
      rating: 4.7,
      reviewCount: 98,
      price: 'From $300',
      specialties: ['Budget Marketing', 'Social Media Setup', 'Basic Advertising', 'Listing Optimization'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/80/80',
      description: 'Affordable marketing services for property sellers on a budget. Effective strategies without the high cost.',
      coordinates: country === 'CA' ? { lat: 51.0447, lng: -114.0719 } : { lat: 41.8781, lng: -87.6298 }
    }
  ]

  // Add Google API providers (non-registered)
  const googleProviders = [
    {
      id: 'g1',
      name: 'Quick Marketing Pro',
      businessName: 'Quick Marketing Pro',
      serviceType: 'Marketing Service',
      location: country === 'CA' ? 'Mississauga, ON' : 'Brooklyn, NY',
      distance: '7.8 km',
      rating: 4.4,
      reviewCount: 56,
      price: 'From $250',
      specialties: ['Basic Marketing', 'Social Media'],
      verified: false,
      isAdvertiser: false,
      isPremium: false,
      description: 'Basic marketing services for property listings and promotion.',
      coordinates: country === 'CA' ? { lat: 43.5890, lng: -79.6441 } : { lat: 40.6782, lng: -73.9442 }
    }
  ]

  return [...baseProviders, ...googleProviders]
}

export function MarketingService({
  userType,
  isSignedIn,
  country
}: MarketingServiceProps) {
  // Get providers data (this would be async in real implementation)
  const mockProviders = getMockProviders(country)

  // Sort by distance and premium status
  const providers = mockProviders.sort((a, b) => {
    if (a.isPremium && !b.isPremium) return -1
    if (!a.isPremium && b.isPremium) return 1
    if (a.isAdvertiser && !b.isAdvertiser) return -1
    if (!a.isAdvertiser && b.isAdvertiser) return 1
    return parseFloat(a.distance) - parseFloat(b.distance)
  })

  const serviceDescription = userType === 'seller'
    ? 'Professional marketing services to maximize your property\'s exposure and attract qualified buyers. Our marketing specialists use proven digital and traditional strategies to sell your property faster and for the best price.'
    : 'Marketing consultation services to help you understand property marketing strategies and identify well-marketed properties during your home search.'

  // Check environment variable for Google providers
  const showGoogleProviders = process.env.NEXT_PUBLIC_ENABLE_GOOGLE_PROVIDERS !== 'false'

  return (
    <ServiceLayout
      userType={userType}
      isSignedIn={isSignedIn}
      serviceTitle="Marketing Services"
      serviceDescription={serviceDescription}
      country={country}
      providers={providers}
      showGoogleProviders={showGoogleProviders}
    />
  )
}
