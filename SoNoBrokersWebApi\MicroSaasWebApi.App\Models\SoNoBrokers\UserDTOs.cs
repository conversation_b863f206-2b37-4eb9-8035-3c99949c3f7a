using System.ComponentModel.DataAnnotations;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    public class CreateUserRequest
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        public string FullName { get; set; } = string.Empty;

        public string? FirstName { get; set; }

        public string? LastName { get; set; }

        public string? Phone { get; set; }

        [Required]
        public string ClerkUserId { get; set; } = string.Empty;

        public UserRole Role { get; set; } = UserRole.USER;

        public SnbUserType UserType { get; set; } = SnbUserType.BUYER;
    }

    public class UpdateUserRequest
    {
        [Required]
        public string Id { get; set; } = string.Empty;

        [EmailAddress]
        public string? Email { get; set; }

        public string? FullName { get; set; }

        public string? FirstName { get; set; }

        public string? LastName { get; set; }

        public string? Phone { get; set; }

        public UserRole? Role { get; set; }

        public SnbUserType? UserType { get; set; }

        public bool? IsActive { get; set; }
    }

    public class UserResponse
    {
        public string Id { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Phone { get; set; }
        public string? ClerkUserId { get; set; }
        public UserRole Role { get; set; }
        public SnbUserType UserType { get; set; }
        public bool IsActive { get; set; }
        public bool LoggedIn { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class UserSearchRequest
    {
        public string? Email { get; set; }
        public UserRole? Role { get; set; }
        public SnbUserType? UserType { get; set; }
        public bool? IsActive { get; set; }
        public int Page { get; set; } = 1;
        public int Limit { get; set; } = 20;
    }

    public class UserSearchResponse
    {
        public List<UserResponse> Users { get; set; } = new();
        public int Total { get; set; }
        public int Page { get; set; }
        public int TotalPages { get; set; }
        public bool HasMore { get; set; }
    }

    public class LoginStatsResponse
    {
        public bool IsLoggedIn { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public int? LoginCount { get; set; }
    }

    public class MapAuthUserRequest
    {
        public string? Id { get; set; }
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;
        public string? FullName { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? ClerkUserId { get; set; }
        public string? AuthUserId { get; set; }
    }

    public class UserPermissionsResponse
    {
        public string UserId { get; set; } = string.Empty;
        public UserRole Role { get; set; }
        public List<PermissionInfo> Permissions { get; set; } = new();
        public bool IsActive { get; set; }
    }
}
