// Removed auth import - no authentication required for browsing resources
import { redirect } from 'next/navigation'
import Link from 'next/link'
import { Country, SnbUserType } from '@/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  BookOpen,
  Calculator,
  TrendingUp,
  MapPin,
  DollarSign,
  FileText,
  PieChart,
  Home,
  CheckCircle,
  Target
} from 'lucide-react'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: SnbUserType
  }>
}

const buyerResources = [
  {
    title: 'Buyer\'s Guide',
    href: '/resources/buyers-guide',
    icon: BookOpen,
    description: 'Complete guide to buying your first home'
  },
  {
    title: 'Buying Checklist',
    href: '/resources/buying-checklist',
    icon: CheckCircle,
    description: 'Step-by-step checklist for home buyers'
  },
  {
    title: 'First-Time Buyer',
    href: '/resources/first-time-buyer',
    icon: Home,
    description: 'Essential information for first-time home buyers'
  },
  {
    title: 'Financing Options',
    href: '/resources/financing',
    icon: DollarSign,
    description: 'Understand your mortgage and financing options'
  },
  {
    title: 'Closing Costs',
    href: '/resources/closing-costs',
    icon: Calculator,
    description: 'Calculate and understand closing costs'
  },
  {
    title: 'Neighborhood Info',
    href: '/resources/neighborhoods',
    icon: MapPin,
    description: 'Detailed information about local neighborhoods'
  }
]

const sellerResources = [
  {
    title: 'Seller\'s Guide',
    href: '/resources/seller-guide',
    icon: FileText,
    description: 'Complete guide to selling your property'
  },
  {
    title: 'Property Valuation',
    href: '/resources/property-valuation',
    icon: PieChart,
    description: 'Get an accurate valuation of your property'
  },
  {
    title: 'Pricing Calculator',
    href: '/resources/pricing-calculator',
    icon: Calculator,
    description: 'Calculate the optimal price for your property'
  },
  {
    title: 'Commission Calculator',
    href: '/resources/commission-calculator',
    icon: DollarSign,
    description: 'Calculate potential savings with SoNoBrokers'
  },
  {
    title: 'Tax Calculator',
    href: '/resources/tax-calculator',
    icon: Calculator,
    description: 'Understand tax implications of selling'
  },
  {
    title: 'Market Analysis',
    href: '/resources/market-analysis',
    icon: TrendingUp,
    description: 'Current market trends and analysis'
  }
]

const generalResources = [
  {
    title: 'Market Trends',
    href: '/resources/market-trends',
    icon: TrendingUp,
    description: 'Latest real estate market trends and forecasts'
  },
  {
    title: 'Investment Guide',
    href: '/resources/investment-guide',
    icon: Target,
    description: 'Real estate investment strategies and tips'
  },
  {
    title: 'Market Analysis',
    href: '/resources/market-analysis',
    icon: PieChart,
    description: 'Comprehensive market analysis and reports'
  }
]

export default async function ResourcesPage({ params, searchParams }: PageProps) {
  // No authentication required for browsing resources
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = Object.values(Country)
  const countryParam = resolvedParams.country.toLowerCase()
  const countryEnum = countryParam.toUpperCase() as Country

  if (!validCountries.includes(countryEnum)) {
    redirect('/ca/resources')
  }

  // Get user type from search params or default to buyer
  const userType: SnbUserType = resolvedSearchParams.userType || SnbUserType.Buyer

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4">
              Real Estate Resources
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Access comprehensive guides, calculators, and tools to help you make informed
              real estate decisions in {countryEnum === 'CA' ? 'Canada' : 'United States'}.
            </p>
          </div>

          {/* User Type Specific Resources */}
          {userType === SnbUserType.Buyer && (
            <div className="mb-12">
              <h2 className="text-2xl font-semibold mb-6 text-center">
                Resources for Buyers
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {buyerResources.map((resource) => {
                  const IconComponent = resource.icon
                  return (
                    <Card key={resource.title} className="hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <div className="flex items-center gap-3">
                          <IconComponent className="h-6 w-6 text-primary" />
                          <CardTitle className="text-lg">{resource.title}</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground mb-4">
                          {resource.description}
                        </p>
                        <Button asChild className="w-full">
                          <Link href={`/${countryParam}${resource.href}`}>
                            Learn More
                          </Link>
                        </Button>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </div>
          )}

          {userType === SnbUserType.SELLER && (
            <div className="mb-12">
              <h2 className="text-2xl font-semibold mb-6 text-center">
                Resources for Sellers
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {sellerResources.map((resource) => {
                  const IconComponent = resource.icon
                  return (
                    <Card key={resource.title} className="hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <div className="flex items-center gap-3">
                          <IconComponent className="h-6 w-6 text-primary" />
                          <CardTitle className="text-lg">{resource.title}</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground mb-4">
                          {resource.description}
                        </p>
                        <Button asChild className="w-full">
                          <Link href={`/${countryParam}${resource.href}`}>
                            Learn More
                          </Link>
                        </Button>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </div>
          )}

          {/* General Resources */}
          <div className="mb-12">
            <h2 className="text-2xl font-semibold mb-6 text-center">
              Market Intelligence
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {generalResources.map((resource) => {
                const IconComponent = resource.icon
                return (
                  <Card key={resource.title} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-center gap-3">
                        <IconComponent className="h-6 w-6 text-primary" />
                        <CardTitle className="text-lg">{resource.title}</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground mb-4">
                        {resource.description}
                      </p>
                      <Button asChild className="w-full">
                        <Link href={`/${countryParam}${resource.href}`}>
                          Learn More
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center">
            <Card className="bg-primary/5 border-primary/20">
              <CardContent className="pt-6">
                <h3 className="text-xl font-semibold mb-2">
                  Need Personalized Help?
                </h3>
                <p className="text-muted-foreground mb-4">
                  Connect with our experts for personalized guidance on your real estate journey.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button asChild>
                    <Link href={`/${countryParam}/dashboard`}>
                      {isSignedIn ? 'Go to Dashboard' : 'Get Started'}
                    </Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href={`/${countryParam}/services`}>
                      Browse Services
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
