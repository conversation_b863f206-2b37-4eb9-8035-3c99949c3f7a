# Controller Architecture Guide

## Overview

This guide documents the restructured controller architecture following SOLID principles and implementing a clean separation of concerns for the MicroSaaS Web API.

## Controller Structure

### 📁 Controllers/
```
Controllers/
├── 📁 Core/                           # Core business functionality
│   ├── 📄 AuthenticationController.cs  # Authentication & authorization
│   ├── 📄 ConfigurationController.cs   # Application configuration
│   ├── 📄 DataController.cs           # Data management operations
│   ├── 📄 DocumentController.cs       # Document management with SharePoint
│   ├── 📄 PaymentController.cs        # Stripe payment operations
│   ├── 📄 ProfileController.cs        # User profile management
│   └── 📄 StorageController.cs        # Azure Blob Storage operations
│
├── 📁 Tenant/                         # Multi-tenant functionality
│   ├── 📄 TenantController.cs         # Tenant management
│   └── 📄 TenantWorkflowController.cs # Tenant-specific workflows
│
└── 📄 HealthCheckController.cs        # Health monitoring (root level)
```

## SOLID Principles Implementation

### 1. Single Responsibility Principle (SRP)
Each controller has a single, focused responsibility:

- **AuthenticationController**: Handles authentication and authorization operations
- **PaymentController**: Manages Stripe payment operations
- **StorageController**: Handles Azure Blob Storage operations
- **DocumentController**: Manages SharePoint document operations
- **ProfileController**: User profile management with email as primary key
- **DataController**: Data management and analytics
- **ConfigurationController**: Application configuration management
- **TenantController**: Multi-tenant operations
- **TenantWorkflowController**: Tenant-specific workflow automation

### 2. Open/Closed Principle (OCP)
Controllers are open for extension via:
- Interface-based service dependencies
- Configurable authorization policies
- Extensible request/response models

### 3. Liskov Substitution Principle (LSP)
All service implementations can replace their interfaces without breaking functionality.

### 4. Interface Segregation Principle (ISP)
Controllers depend on small, focused interfaces:
- `IClerkAuthService`, `IAzureAdAuthService`, `IRoleBasedAuthService`
- `IStripePaymentService`
- `IAzureBlobStorageService`, `ISharePointDocumentService`
- `IUnitOfWork` with specific repositories

### 5. Dependency Inversion Principle (DIP)
Controllers depend on abstractions (interfaces) rather than concrete implementations.

## Core Controllers

### AuthenticationController
**Purpose**: Handles authentication and authorization operations

**Key Features**:
- Clerk authentication (login, register, refresh, logout)
- Azure AD token validation
- Role-based authorization
- User role and permission management

**Authorization Policies**:
- `AdminOnly` - Admin-only operations
- Custom role-based policies

### PaymentController
**Purpose**: Manages Stripe payment operations

**Key Features**:
- Customer management (CRUD operations)
- Payment intent creation and confirmation
- Subscription management
- Webhook handling

**Authorization Policies**:
- `CanProcessPayments` - Payment processing operations

### StorageController
**Purpose**: Handles Azure Blob Storage operations

**Key Features**:
- File upload/download
- Container management
- SAS URL generation
- File metadata management

**Authorization Policies**:
- `CanManageStorage` - Storage management operations

### DocumentController
**Purpose**: Manages SharePoint document operations

**Key Features**:
- Document upload/download
- SharePoint integration
- Document search and metadata
- Analytics and reporting

**Authorization Policies**:
- `CanManageDocuments` - Document management operations
- `CanViewAnalytics` - Analytics access

### ProfileController
**Purpose**: User profile management with email as primary key

**Key Features**:
- Profile creation after login
- Profile updates and management
- Email-based user identification
- Profile search and analytics

**Authorization Policies**:
- `AdminOnly` - Administrative operations

### DataController
**Purpose**: Data management and analytics

**Key Features**:
- Product management (CRUD operations)
- Data analytics and insights
- Data export functionality
- Search and filtering

**Authorization Policies**:
- `CanManageData` - Data management operations
- `CanViewAnalytics` - Analytics access
- `CanExportData` - Data export operations

### ConfigurationController
**Purpose**: Application configuration management

**Key Features**:
- Application information retrieval
- Feature flag management
- Environment configuration
- Health status monitoring

**Authorization Policies**:
- `AdminOnly` - Administrative configuration access

## Tenant Controllers

### TenantController
**Purpose**: Multi-tenant operations for MicroSaaS functionality

**Key Features**:
- Tenant creation and management
- Tenant settings and branding
- User access control
- Tenant activation/deactivation

**Authorization Policies**:
- `CanCreateTenant` - Tenant creation
- `CanManageTenant` - Tenant management
- `AdminOnly` - Administrative operations

### TenantWorkflowController
**Purpose**: Tenant-specific workflow automation

**Key Features**:
- Workflow creation and management
- Workflow execution
- Execution history tracking
- Tenant-specific automation

**Authorization Policies**:
- `CanManageTenant` - Workflow management

## Authorization Policies

### Standard Policies
- **AdminOnly**: Requires Admin role
- **CanProcessPayments**: Payment processing permissions
- **CanManageStorage**: Storage management permissions
- **CanManageDocuments**: Document management permissions
- **CanManageData**: Data management permissions
- **CanViewAnalytics**: Analytics viewing permissions
- **CanExportData**: Data export permissions
- **CanCreateTenant**: Tenant creation permissions
- **CanManageTenant**: Tenant management permissions

### Implementation
Policies are configured in `ServiceCollectionExtensions.cs` and enforced through:
- `[Authorize(Policy = "PolicyName")]` attributes
- Custom authorization handlers
- Role-based access control

## Request/Response Models

### Consistent Patterns
All controllers follow consistent patterns for:
- **Request Models**: Strongly typed request objects
- **Response Models**: Standardized response formats
- **Validation**: Model validation with `[Required]`, `[EmailAddress]`, etc.
- **Error Handling**: Consistent error response format

### Example Request Model
```csharp
public class CreateTenantRequest
{
    [Required]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    public string Subdomain { get; set; } = string.Empty;
    
    [EmailAddress]
    public string ContactEmail { get; set; } = string.Empty;
    
    // Additional properties...
}
```

## Swagger Documentation

All controllers include comprehensive Swagger documentation:
- **SwaggerOperation** attributes for endpoint descriptions
- **Parameter documentation** for all inputs
- **Response documentation** for all outputs
- **Authorization requirements** clearly documented

## Error Handling

Consistent error handling across all controllers:
- **Try-catch blocks** for all operations
- **Structured logging** with relevant context
- **Standardized error responses** with appropriate HTTP status codes
- **Security considerations** (no sensitive data in error messages)

## Future Extensibility

The architecture supports future extensions:
- **New controllers** can be added following the same patterns
- **Additional authorization policies** can be configured
- **Service interfaces** can be extended without breaking existing code
- **Tenant-specific functionality** can be added to the Tenant folder

## Migration Notes

### Removed Components
- **Base folder**: Eliminated to follow SOLID principles
- **BaseController**: Replaced with direct `ControllerBase` inheritance
- **Conflicting RequestBody classes**: Moved to TODO folder for review

### Moved Components
- **Configuration management**: Moved to Core folder
- **Legacy controllers**: Restructured following SOLID principles
- **Tenant functionality**: Organized in dedicated Tenant folder

This architecture provides a solid foundation for scalable, maintainable, and testable MicroSaaS functionality.
