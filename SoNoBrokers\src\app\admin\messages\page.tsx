import { Suspense } from 'react'
import { Metadata } from 'next'
import { redirect } from 'next/navigation'
import { getCurrentUserProfile, isAdmin } from '@/lib/api/auth-api'
import { AdminMessagesClient } from '@/components/admin/AdminMessagesClient'
import { Card, CardContent } from '@/components/ui/card'
import { MessageCircle } from 'lucide-react'

/**
 * Admin Messages Page - Server Component
 * Shows all conversations in the system for admin oversight
 */

export const metadata: Metadata = {
  title: 'Messages | Admin | SoNoBrokers',
  description: 'Monitor and manage all user conversations',
}

export default async function AdminMessagesPage() {
  // Check authentication and admin access
  const userProfile = await getCurrentUserProfile()
  if (!userProfile) {
    redirect('/sign-in')
  }

  const isUserAdmin = await isAdmin()
  if (!isUserAdmin) {
    redirect('/dashboard')
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Messages Management
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-300">
          Monitor and manage all user conversations across the platform
        </p>
      </div>

      {/* Content */}
      <Suspense fallback={<AdminMessagesLoading />}>
        <AdminMessagesContent />
      </Suspense>
    </div>
  )
}

/**
 * Admin Messages Content Server Component
 */
async function AdminMessagesContent() {
  try {
    // Fetch admin conversations and stats
    // Note: These would be actual API calls to admin endpoints
    const mockConversations = [
      {
        id: '1',
        propertyId: 'prop-1',
        propertyTitle: 'Beautiful Family Home',
        propertyAddress: '123 Main St, Toronto, ON',
        propertyPrice: 750000,
        otherParticipantId: 'user-1',
        otherParticipantName: 'John Buyer',
        otherParticipantEmail: '<EMAIL>',
        subject: 'Property Inquiry',
        lastMessageAt: new Date().toISOString(),
        lastMessageContent: 'Is this property still available?',
        lastMessageSenderId: 'user-1',
        isActive: true,
        unreadCount: 2,
        createdAt: new Date().toISOString(),
      },
      {
        id: '2',
        propertyId: 'prop-2',
        propertyTitle: 'Modern Downtown Condo',
        propertyAddress: '456 Bay St, Vancouver, BC',
        propertyPrice: 650000,
        otherParticipantId: 'user-2',
        otherParticipantName: 'Jane Seller',
        otherParticipantEmail: '<EMAIL>',
        subject: 'Viewing Request',
        lastMessageAt: new Date(Date.now() - 3600000).toISOString(),
        lastMessageContent: 'When would be a good time for a viewing?',
        lastMessageSenderId: 'user-3',
        isActive: true,
        unreadCount: 0,
        createdAt: new Date(Date.now() - 86400000).toISOString(),
      },
    ]

    const mockStats = {
      totalConversations: 25,
      activeConversations: 18,
      totalMessages: 156,
      unreadMessages: 8,
    }

    return (
      <AdminMessagesClient
        initialConversations={mockConversations}
        stats={mockStats}
      />
    )
  } catch (error) {
    console.error('Failed to load admin messages:', error)
    
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <MessageCircle className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Unable to load messages
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-center mb-4">
            There was an error loading the messaging data. Please try again.
          </p>
        </CardContent>
      </Card>
    )
  }
}

/**
 * Loading Component
 */
function AdminMessagesLoading() {
  return (
    <div className="space-y-6">
      {/* Stats Loading */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-2">
                <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-24"></div>
                <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-16"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Loading */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Conversations List Loading */}
        <div className="lg:col-span-1">
          <Card>
            <div className="p-6 border-b">
              <div className="animate-pulse space-y-4">
                <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-32"></div>
                <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-48"></div>
                <div className="h-10 bg-gray-300 dark:bg-gray-700 rounded"></div>
                <div className="flex space-x-2">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-16"></div>
                  ))}
                </div>
              </div>
            </div>
            <CardContent className="p-0">
              <div className="space-y-4 p-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="animate-pulse flex items-center space-x-3">
                    <div className="h-8 w-8 bg-gray-300 dark:bg-gray-700 rounded-full"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-1/2"></div>
                      <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-2/3"></div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Details Loading */}
        <div className="lg:col-span-2">
          <Card>
            <div className="p-6 border-b">
              <div className="animate-pulse space-y-2">
                <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-40"></div>
                <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-64"></div>
              </div>
            </div>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="flex justify-between">
                  <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-16"></div>
                  <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-20"></div>
                </div>
                <div className="flex justify-between">
                  <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-20"></div>
                  <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-16"></div>
                </div>
                <div className="flex justify-between">
                  <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-18"></div>
                  <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-20"></div>
                </div>
                <div className="flex gap-2 mt-4">
                  <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-32"></div>
                  <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-24"></div>
                  <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-20"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
