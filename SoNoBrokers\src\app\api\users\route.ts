import { NextRequest, NextResponse } from 'next/server';
// TODO: Migrate to .NET Web API - temporarily disabled
// import prisma from '@/lib/prisma';

export async function GET(req: NextRequest) {
  // TODO: Migrate to .NET Web API
  return NextResponse.json(
    { error: 'API route temporarily disabled - migrating to .NET Web API' },
    { status: 503 }
  )
}

export async function POST(req: NextRequest) {
  // TODO: Migrate to .NET Web API
  return NextResponse.json(
    { error: 'API route temporarily disabled - migrating to .NET Web API' },
    { status: 503 }
  )
}

/* DISABLED - MIGRATE TO .NET WEB API
export async function GET_DISABLED(req: NextRequest) {
  const users = await prisma.user.findMany();
  return NextResponse.json(users);
}

export async function POST_DISABLED(req: NextRequest) {
  const data = await req.json();
  const user = await prisma.user.create({ data });
  return NextResponse.json(user);
}
*/