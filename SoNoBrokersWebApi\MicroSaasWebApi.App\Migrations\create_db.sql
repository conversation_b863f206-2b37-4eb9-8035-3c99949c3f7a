-- SoNoBrokers Database Complete Schema with Advertiser Functionality
-- Updated and Synchronized Version
-- Run this script in Supabase SQL Editor
-- File: create_db.sql

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Drop existing tables in correct order (respecting foreign keys)
DROP TABLE IF EXISTS public."AdvertiserSubscription" CASCADE;
DROP TABLE IF EXISTS public."Advertiser" CASCADE;
DROP TABLE IF EXISTS public."stripe_webhook_events" CASCADE;
DROP TABLE IF EXISTS public."stripe_invoices" CASCADE;
DROP TABLE IF EXISTS public."stripe_payments" CASCADE;
DROP TABLE IF EXISTS public."stripe_prices" CASCADE;
DROP TABLE IF EXISTS public."stripe_products" CASCADE;
DROP TABLE IF EXISTS public."stripe_customers" CASCADE;
DROP TABLE IF EXISTS public."project" CASCADE;
DROP TABLE IF EXISTS public."role_permissions" CASCADE;
DROP TABLE IF EXISTS public."Audiences" CASCADE;
DROP TABLE IF EXISTS public."AIReport" CASCADE;
DROP TABLE IF EXISTS public."BuyerListings" CASCADE;
DROP TABLE IF EXISTS public."UserActivity" CASCADE;
DROP TABLE IF EXISTS public."SearchFilter" CASCADE;
DROP TABLE IF EXISTS public."PropertyAnalytics" CASCADE;
DROP TABLE IF EXISTS public."PropertyViewing" CASCADE;
DROP TABLE IF EXISTS public."OpenHouseAccess" CASCADE;
DROP TABLE IF EXISTS public."Subscription" CASCADE;
DROP TABLE IF EXISTS public."Notifications" CASCADE;
DROP TABLE IF EXISTS public."Message" CASCADE;
DROP TABLE IF EXISTS public."Conversation" CASCADE;
DROP TABLE IF EXISTS public."BuyerOffer" CASCADE;
DROP TABLE IF EXISTS public."ServiceBooking" CASCADE;
DROP TABLE IF EXISTS public."ServiceProvider" CASCADE;
DROP TABLE IF EXISTS public."SellerProfile" CASCADE;
DROP TABLE IF EXISTS public."BuyerProfile" CASCADE;
DROP TABLE IF EXISTS public."PropertyImage" CASCADE;
DROP TABLE IF EXISTS public."Property" CASCADE;
DROP TABLE IF EXISTS public."User" CASCADE;

-- Drop existing enums if they exist
DROP TYPE IF EXISTS public."UserRole" CASCADE;
DROP TYPE IF EXISTS public."UserType" CASCADE;
DROP TYPE IF EXISTS public."SubscriptionStatus" CASCADE;
DROP TYPE IF EXISTS public."PropertyStatus" CASCADE;
DROP TYPE IF EXISTS public."ServiceType" CASCADE;
DROP TYPE IF EXISTS public."BookingStatus" CASCADE;
DROP TYPE IF EXISTS public."OfferStatus" CASCADE;
DROP TYPE IF EXISTS public."AccessType" CASCADE;
DROP TYPE IF EXISTS public."Country" CASCADE;
DROP TYPE IF EXISTS public."AdvertiserPlan" CASCADE;
DROP TYPE IF EXISTS public."AdvertiserStatus" CASCADE;

-- Create enums in public schema
CREATE TYPE public."UserRole" AS ENUM ('ADMIN', 'USER', 'PRODUCT', 'OPERATOR', 'service_provider');
CREATE TYPE public."UserType" AS ENUM ('Buyer', 'Seller');
CREATE TYPE public."SubscriptionStatus" AS ENUM ('active', 'inactive', 'cancelled', 'past_due', 'unpaid');
CREATE TYPE public."PropertyStatus" AS ENUM ('pending', 'active', 'sold', 'expired');
CREATE TYPE public."ServiceType" AS ENUM ('lawyer', 'photographer', 'inspector', 'appraiser', 'home_inspector', 'mortgage_broker', 'insurance_agent', 'contractor', 'cleaner', 'stager', 'marketing_agency');
CREATE TYPE public."BookingStatus" AS ENUM ('pending', 'confirmed', 'completed', 'cancelled');
CREATE TYPE public."OfferStatus" AS ENUM ('pending', 'reviewed', 'accepted', 'rejected');
CREATE TYPE public."AccessType" AS ENUM ('qr_scan', 'online_access');
CREATE TYPE public."Country" AS ENUM ('CA', 'US');
CREATE TYPE public."AdvertiserPlan" AS ENUM ('basic', 'premium', 'enterprise');
CREATE TYPE public."AdvertiserStatus" AS ENUM ('pending', 'active', 'suspended', 'cancelled');

-- Create User table
CREATE TABLE public."User" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "email" TEXT UNIQUE NOT NULL,
    "fullName" TEXT NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "phone" TEXT,
    "address" JSONB,
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW(),
    "lastLoginAt" TIMESTAMPTZ,
    "loggedIn" BOOLEAN DEFAULT FALSE,
    "clerkUserId" TEXT UNIQUE,
    "authUserId" TEXT UNIQUE,
    "role" public."UserRole" DEFAULT 'USER',
    "userType" public."UserType" DEFAULT 'Buyer',
    "isActive" BOOLEAN DEFAULT TRUE,
    "createdByAdmin" UUID
);

-- Create Property table (synchronized with Prisma schema)
CREATE TABLE public."Property" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "title" TEXT NOT NULL,
    "description" TEXT,
    "price" DECIMAL(12,2) NOT NULL,
    "address" JSONB NOT NULL,
    "bedrooms" INTEGER,
    "bathrooms" DECIMAL(3,1),
    "squareFeet" INTEGER,
    "lotSize" DECIMAL(10,2),
    "yearBuilt" INTEGER,
    "propertyType" TEXT NOT NULL,
    "status" public."PropertyStatus" DEFAULT 'pending',
    "listingDate" TIMESTAMPTZ DEFAULT NOW(),
    "expiryDate" TIMESTAMPTZ,
    "sellerId" UUID NOT NULL,
    "agentId" UUID,
    "features" JSONB DEFAULT '[]',
    "amenities" JSONB DEFAULT '[]',
    "virtualTourUrl" TEXT,
    "videoUrl" TEXT,
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create PropertyImage table
CREATE TABLE public."PropertyImage" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "propertyId" UUID NOT NULL,
    "url" TEXT NOT NULL,
    "alt" TEXT,
    "isPrimary" BOOLEAN DEFAULT FALSE,
    "order" INTEGER DEFAULT 0,
    "createdAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create PropertyImage table
CREATE TABLE public."PropertyImage" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "propertyId" UUID NOT NULL,
    "url" TEXT NOT NULL,
    "alt" TEXT,
    "isPrimary" BOOLEAN DEFAULT FALSE,
    "order" INTEGER DEFAULT 0,
    "createdAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create BuyerProfile table
CREATE TABLE public."BuyerProfile" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID UNIQUE NOT NULL,
    "preApprovalAmount" DECIMAL(12,2),
    "preApprovalDate" DATE,
    "lenderName" TEXT,
    "agentId" UUID,
    "preferences" JSONB DEFAULT '{}',
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create SellerProfile table
CREATE TABLE public."SellerProfile" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID UNIQUE NOT NULL,
    "agentId" UUID,
    "commissionRate" DECIMAL(5,4),
    "marketingBudget" DECIMAL(10,2),
    "preferences" JSONB DEFAULT '{}',
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create ServiceProvider table
CREATE TABLE public."ServiceProvider" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID UNIQUE NOT NULL,
    "serviceType" public."ServiceType" NOT NULL,
    "businessName" TEXT,
    "licenseNumber" TEXT,
    "serviceArea" TEXT[],
    "hourlyRate" DECIMAL(8,2),
    "fixedRates" JSONB DEFAULT '{}',
    "availability" JSONB DEFAULT '{}',
    "rating" DECIMAL(3,2) DEFAULT 0.00,
    "totalReviews" INTEGER DEFAULT 0,
    "isVerified" BOOLEAN DEFAULT FALSE,
    "insuranceInfo" JSONB,
    "website" TEXT,
    "description" TEXT,
    "images" TEXT[],
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create Advertiser table
CREATE TABLE public."Advertiser" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID UNIQUE NOT NULL,
    "serviceProviderId" UUID,
    "businessName" TEXT NOT NULL,
    "contactName" TEXT,
    "email" TEXT NOT NULL,
    "phone" TEXT,
    "website" TEXT,
    "description" TEXT,
    "serviceType" public."ServiceType" NOT NULL,
    "serviceAreas" TEXT[],
    "licenseNumber" TEXT,
    "plan" public."AdvertiserPlan" DEFAULT 'basic',
    "status" public."AdvertiserStatus" DEFAULT 'pending',
    "featuredUntil" TIMESTAMPTZ,
    "isPremium" BOOLEAN DEFAULT FALSE,
    "isVerified" BOOLEAN DEFAULT FALSE,
    "images" TEXT[],
    "metadata" JSONB DEFAULT '{}',
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create AdvertiserSubscription table
CREATE TABLE public."AdvertiserSubscription" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "advertiserId" UUID NOT NULL,
    "stripeSubscriptionId" TEXT UNIQUE,
    "stripePriceId" TEXT,
    "plan" public."AdvertiserPlan" NOT NULL,
    "status" public."SubscriptionStatus" DEFAULT 'active',
    "currentPeriodStart" TIMESTAMPTZ,
    "currentPeriodEnd" TIMESTAMPTZ,
    "cancelAtPeriodEnd" BOOLEAN DEFAULT FALSE,
    "canceledAt" TIMESTAMPTZ,
    "trialStart" TIMESTAMPTZ,
    "trialEnd" TIMESTAMPTZ,
    "metadata" JSONB DEFAULT '{}',
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create ServiceBooking table
CREATE TABLE public."ServiceBooking" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "serviceProviderId" UUID NOT NULL,
    "buyerId" UUID NOT NULL,
    "propertyId" UUID,
    "serviceType" public."ServiceType" NOT NULL,
    "scheduledDate" TIMESTAMPTZ NOT NULL,
    "duration" INTEGER, -- in minutes
    "notes" TEXT,
    "status" public."BookingStatus" DEFAULT 'pending',
    "totalCost" DECIMAL(10,2),
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create BuyerOffer table
CREATE TABLE public."BuyerOffer" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "propertyId" UUID NOT NULL,
    "buyerId" UUID NOT NULL,
    "offerAmount" DECIMAL(12,2) NOT NULL,
    "conditions" TEXT[],
    "closingDate" DATE,
    "status" public."OfferStatus" DEFAULT 'pending',
    "notes" TEXT,
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create Conversation table
CREATE TABLE public."Conversation" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "buyerId" UUID,
    "sellerId" UUID,
    "agentId" UUID,
    "propertyId" UUID,
    "subject" TEXT,
    "lastMessageAt" TIMESTAMPTZ,
    "isActive" BOOLEAN DEFAULT TRUE,
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create Message table
CREATE TABLE public."Message" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "conversationId" UUID NOT NULL,
    "senderId" UUID NOT NULL,
    "content" TEXT NOT NULL,
    "messageType" TEXT DEFAULT 'text',
    "attachments" JSONB,
    "isRead" BOOLEAN DEFAULT FALSE,
    "readAt" TIMESTAMPTZ,
    "createdAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create Notifications table
CREATE TABLE public."Notifications" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "notificationType" TEXT NOT NULL,
    "entityType" TEXT,
    "entityId" UUID,
    "isRead" BOOLEAN DEFAULT FALSE,
    "readAt" TIMESTAMPTZ,
    "createdAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create Subscription table
CREATE TABLE public."Subscription" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,
    "planName" TEXT NOT NULL,
    "status" public."SubscriptionStatus" DEFAULT 'active',
    "startDate" TIMESTAMPTZ DEFAULT NOW(),
    "endDate" TIMESTAMPTZ,
    "features" JSONB DEFAULT '{}',
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create OpenHouseAccess table
CREATE TABLE public."OpenHouseAccess" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "propertyId" UUID NOT NULL,
    "buyerId" UUID NOT NULL,
    "accessType" public."AccessType" NOT NULL,
    "accessCode" TEXT,
    "qrCode" TEXT,
    "accessedAt" TIMESTAMPTZ,
    "isActive" BOOLEAN DEFAULT TRUE,
    "expiresAt" TIMESTAMPTZ,
    "createdAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create PropertyViewing table
CREATE TABLE public."PropertyViewing" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "propertyId" UUID NOT NULL,
    "buyerId" UUID NOT NULL,
    "scheduledAt" TIMESTAMPTZ NOT NULL,
    "duration" INTEGER DEFAULT 30, -- in minutes
    "notes" TEXT,
    "status" TEXT DEFAULT 'scheduled',
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create PropertyAnalytics table
CREATE TABLE public."PropertyAnalytics" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "propertyId" UUID NOT NULL,
    "views" INTEGER DEFAULT 0,
    "favorites" INTEGER DEFAULT 0,
    "inquiries" INTEGER DEFAULT 0,
    "viewingRequests" INTEGER DEFAULT 0,
    "offers" INTEGER DEFAULT 0,
    "lastViewedAt" TIMESTAMPTZ,
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create SearchFilter table
CREATE TABLE public."SearchFilter" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "filters" JSONB NOT NULL,
    "isActive" BOOLEAN DEFAULT TRUE,
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create UserActivity table
CREATE TABLE public."UserActivity" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,
    "action" TEXT NOT NULL,
    "entityType" TEXT,
    "entityId" UUID,
    "metadata" JSONB,
    "ipAddress" INET,
    "userAgent" TEXT,
    "createdAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create BuyerListings table
CREATE TABLE public."BuyerListings" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "buyerId" UUID NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "budget" DECIMAL(12,2),
    "preferredAreas" TEXT[],
    "requirements" JSONB DEFAULT '{}',
    "isActive" BOOLEAN DEFAULT TRUE,
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create AIReport table
CREATE TABLE public."AIReport" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,
    "propertyId" UUID,
    "reportType" TEXT NOT NULL,
    "content" JSONB NOT NULL,
    "confidence" DECIMAL(5,4),
    "createdAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create Audiences table (for email marketing)
CREATE TABLE public."Audiences" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "resendId" TEXT UNIQUE NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create role_permissions table
CREATE TABLE public."role_permissions" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "role" public."UserRole" NOT NULL,
    "permission" TEXT NOT NULL,
    "resource" TEXT,
    "createdAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Create project table (for Make.com integration)
CREATE TABLE public."project" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "user_clerk_id" TEXT NOT NULL,
    "scenario_id" TEXT,
    "webhook_link" TEXT,
    "connection_id" TEXT,
    "webhook_id" TEXT,
    "assistant_id" TEXT,
    "type" TEXT,
    "status" TEXT DEFAULT 'inactive',
    "name" TEXT,
    "description" TEXT,
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- Stripe integration tables
CREATE TABLE public."stripe_customers" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "user_id" UUID NOT NULL,
    "stripe_customer_id" TEXT UNIQUE NOT NULL,
    "email" TEXT,
    "name" TEXT,
    "created_at" TIMESTAMPTZ DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE public."stripe_products" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "stripe_product_id" TEXT UNIQUE NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "active" BOOLEAN DEFAULT TRUE,
    "metadata" JSONB,
    "created_at" TIMESTAMPTZ DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE public."stripe_prices" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "stripe_price_id" TEXT UNIQUE NOT NULL,
    "stripe_product_id" TEXT NOT NULL,
    "unit_amount" INTEGER,
    "currency" TEXT DEFAULT 'usd',
    "recurring_interval" TEXT,
    "recurring_interval_count" INTEGER,
    "active" BOOLEAN DEFAULT TRUE,
    "metadata" JSONB,
    "created_at" TIMESTAMPTZ DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE public."stripe_payments" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "user_id" UUID NOT NULL,
    "stripe_payment_intent_id" TEXT UNIQUE NOT NULL,
    "amount" INTEGER NOT NULL,
    "currency" TEXT DEFAULT 'usd',
    "status" TEXT NOT NULL,
    "metadata" JSONB,
    "created_at" TIMESTAMPTZ DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE public."stripe_invoices" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "user_id" UUID NOT NULL,
    "stripe_invoice_id" TEXT UNIQUE NOT NULL,
    "stripe_customer_id" TEXT NOT NULL,
    "amount_paid" INTEGER,
    "amount_due" INTEGER,
    "currency" TEXT DEFAULT 'usd',
    "status" TEXT NOT NULL,
    "hosted_invoice_url" TEXT,
    "invoice_pdf" TEXT,
    "metadata" JSONB,
    "created_at" TIMESTAMPTZ DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE public."stripe_webhook_events" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "stripe_event_id" TEXT UNIQUE NOT NULL,
    "event_type" TEXT NOT NULL,
    "processed" BOOLEAN DEFAULT FALSE,
    "data" JSONB NOT NULL,
    "created_at" TIMESTAMPTZ DEFAULT NOW()
);

-- Add Foreign Key Constraints
ALTER TABLE public."User" ADD CONSTRAINT "User_createdByAdmin_fkey"
    FOREIGN KEY ("createdByAdmin") REFERENCES public."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE public."Property" ADD CONSTRAINT "Property_sellerId_fkey"
    FOREIGN KEY ("sellerId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."Property" ADD CONSTRAINT "Property_agentId_fkey"
    FOREIGN KEY ("agentId") REFERENCES public."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE public."PropertyImage" ADD CONSTRAINT "PropertyImage_propertyId_fkey"
    FOREIGN KEY ("propertyId") REFERENCES public."Property"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."BuyerProfile" ADD CONSTRAINT "BuyerProfile_userId_fkey"
    FOREIGN KEY ("userId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."BuyerProfile" ADD CONSTRAINT "BuyerProfile_agentId_fkey"
    FOREIGN KEY ("agentId") REFERENCES public."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE public."SellerProfile" ADD CONSTRAINT "SellerProfile_userId_fkey"
    FOREIGN KEY ("userId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."SellerProfile" ADD CONSTRAINT "SellerProfile_agentId_fkey"
    FOREIGN KEY ("agentId") REFERENCES public."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE public."ServiceProvider" ADD CONSTRAINT "ServiceProvider_userId_fkey"
    FOREIGN KEY ("userId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."ServiceBooking" ADD CONSTRAINT "ServiceBooking_serviceProviderId_fkey"
    FOREIGN KEY ("serviceProviderId") REFERENCES public."ServiceProvider"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."ServiceBooking" ADD CONSTRAINT "ServiceBooking_buyerId_fkey"
    FOREIGN KEY ("buyerId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."ServiceBooking" ADD CONSTRAINT "ServiceBooking_propertyId_fkey"
    FOREIGN KEY ("propertyId") REFERENCES public."Property"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE public."BuyerOffer" ADD CONSTRAINT "BuyerOffer_propertyId_fkey"
    FOREIGN KEY ("propertyId") REFERENCES public."Property"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."BuyerOffer" ADD CONSTRAINT "BuyerOffer_buyerId_fkey"
    FOREIGN KEY ("buyerId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."Conversation" ADD CONSTRAINT "Conversation_buyerId_fkey"
    FOREIGN KEY ("buyerId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."Conversation" ADD CONSTRAINT "Conversation_sellerId_fkey"
    FOREIGN KEY ("sellerId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."Conversation" ADD CONSTRAINT "Conversation_agentId_fkey"
    FOREIGN KEY ("agentId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."Conversation" ADD CONSTRAINT "Conversation_propertyId_fkey"
    FOREIGN KEY ("propertyId") REFERENCES public."Property"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE public."Message" ADD CONSTRAINT "Message_conversationId_fkey"
    FOREIGN KEY ("conversationId") REFERENCES public."Conversation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."Message" ADD CONSTRAINT "Message_senderId_fkey"
    FOREIGN KEY ("senderId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."Notifications" ADD CONSTRAINT "Notifications_userId_fkey"
    FOREIGN KEY ("userId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."Subscription" ADD CONSTRAINT "Subscription_userId_fkey"
    FOREIGN KEY ("userId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."OpenHouseAccess" ADD CONSTRAINT "OpenHouseAccess_propertyId_fkey"
    FOREIGN KEY ("propertyId") REFERENCES public."Property"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."OpenHouseAccess" ADD CONSTRAINT "OpenHouseAccess_buyerId_fkey"
    FOREIGN KEY ("buyerId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."PropertyViewing" ADD CONSTRAINT "PropertyViewing_propertyId_fkey"
    FOREIGN KEY ("propertyId") REFERENCES public."Property"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."PropertyViewing" ADD CONSTRAINT "PropertyViewing_buyerId_fkey"
    FOREIGN KEY ("buyerId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."PropertyAnalytics" ADD CONSTRAINT "PropertyAnalytics_propertyId_fkey"
    FOREIGN KEY ("propertyId") REFERENCES public."Property"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."SearchFilter" ADD CONSTRAINT "SearchFilter_userId_fkey"
    FOREIGN KEY ("userId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."UserActivity" ADD CONSTRAINT "UserActivity_userId_fkey"
    FOREIGN KEY ("userId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."BuyerListings" ADD CONSTRAINT "BuyerListings_buyerId_fkey"
    FOREIGN KEY ("buyerId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."AIReport" ADD CONSTRAINT "AIReport_userId_fkey"
    FOREIGN KEY ("userId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."AIReport" ADD CONSTRAINT "AIReport_propertyId_fkey"
    FOREIGN KEY ("propertyId") REFERENCES public."Property"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE public."stripe_customers" ADD CONSTRAINT "stripe_customers_user_id_fkey"
    FOREIGN KEY ("user_id") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."stripe_payments" ADD CONSTRAINT "stripe_payments_user_id_fkey"
    FOREIGN KEY ("user_id") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."stripe_invoices" ADD CONSTRAINT "stripe_invoices_user_id_fkey"
    FOREIGN KEY ("user_id") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."Advertiser" ADD CONSTRAINT "Advertiser_userId_fkey"
    FOREIGN KEY ("userId") REFERENCES public."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public."Advertiser" ADD CONSTRAINT "Advertiser_serviceProviderId_fkey"
    FOREIGN KEY ("serviceProviderId") REFERENCES public."ServiceProvider"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE public."AdvertiserSubscription" ADD CONSTRAINT "AdvertiserSubscription_advertiserId_fkey"
    FOREIGN KEY ("advertiserId") REFERENCES public."Advertiser"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Create Indexes for Performance
CREATE INDEX "idx_user_active" ON public."User"("isActive");
CREATE INDEX "idx_user_auth_id" ON public."User"("authUserId");
CREATE INDEX "idx_user_clerk_id" ON public."User"("clerkUserId");
CREATE INDEX "idx_user_email" ON public."User"("email");
CREATE INDEX "idx_user_role" ON public."User"("role");
CREATE INDEX "idx_user_type" ON public."User"("userType");

CREATE INDEX "idx_property_status" ON public."Property"("status");
CREATE INDEX "idx_property_seller" ON public."Property"("sellerId");
CREATE INDEX "idx_property_agent" ON public."Property"("agentId");
CREATE INDEX "idx_property_price" ON public."Property"("price");
CREATE INDEX "idx_property_type" ON public."Property"("propertyType");
CREATE INDEX "idx_property_location" ON public."Property" USING GIST("location");

CREATE INDEX "idx_property_image_property" ON public."PropertyImage"("propertyId");
CREATE INDEX "idx_property_image_primary" ON public."PropertyImage"("isPrimary");

CREATE INDEX "idx_buyer_profile_user" ON public."BuyerProfile"("userId");
CREATE INDEX "idx_buyer_profile_agent" ON public."BuyerProfile"("agentId");

CREATE INDEX "idx_seller_profile_user" ON public."SellerProfile"("userId");
CREATE INDEX "idx_seller_profile_agent" ON public."SellerProfile"("agentId");

CREATE INDEX "idx_service_provider_user" ON public."ServiceProvider"("userId");
CREATE INDEX "idx_service_provider_type" ON public."ServiceProvider"("serviceType");
CREATE INDEX "idx_service_provider_verified" ON public."ServiceProvider"("isVerified");

CREATE INDEX "idx_service_booking_provider" ON public."ServiceBooking"("serviceProviderId");
CREATE INDEX "idx_service_booking_buyer" ON public."ServiceBooking"("buyerId");
CREATE INDEX "idx_service_booking_property" ON public."ServiceBooking"("propertyId");
CREATE INDEX "idx_service_booking_status" ON public."ServiceBooking"("status");
CREATE INDEX "idx_service_booking_date" ON public."ServiceBooking"("scheduledDate");

CREATE INDEX "idx_buyer_offer_property" ON public."BuyerOffer"("propertyId");
CREATE INDEX "idx_buyer_offer_buyer" ON public."BuyerOffer"("buyerId");
CREATE INDEX "idx_buyer_offer_status" ON public."BuyerOffer"("status");

CREATE INDEX "idx_conversation_buyer" ON public."Conversation"("buyerId");
CREATE INDEX "idx_conversation_seller" ON public."Conversation"("sellerId");
CREATE INDEX "idx_conversation_agent" ON public."Conversation"("agentId");
CREATE INDEX "idx_conversation_property" ON public."Conversation"("propertyId");
CREATE INDEX "idx_conversation_active" ON public."Conversation"("isActive");

CREATE INDEX "idx_message_conversation" ON public."Message"("conversationId");
CREATE INDEX "idx_message_sender" ON public."Message"("senderId");
CREATE INDEX "idx_message_read" ON public."Message"("isRead");

CREATE INDEX "idx_notifications_user" ON public."Notifications"("userId");
CREATE INDEX "idx_notifications_read" ON public."Notifications"("isRead");
CREATE INDEX "idx_notifications_type" ON public."Notifications"("notificationType");

CREATE INDEX "idx_subscription_user" ON public."Subscription"("userId");
CREATE INDEX "idx_subscription_status" ON public."Subscription"("status");

CREATE INDEX "idx_open_house_property" ON public."OpenHouseAccess"("propertyId");
CREATE INDEX "idx_open_house_buyer" ON public."OpenHouseAccess"("buyerId");
CREATE INDEX "idx_open_house_active" ON public."OpenHouseAccess"("isActive");

CREATE INDEX "idx_property_viewing_property" ON public."PropertyViewing"("propertyId");
CREATE INDEX "idx_property_viewing_buyer" ON public."PropertyViewing"("buyerId");
CREATE INDEX "idx_property_viewing_date" ON public."PropertyViewing"("scheduledAt");

CREATE INDEX "idx_property_analytics_property" ON public."PropertyAnalytics"("propertyId");

CREATE INDEX "idx_search_filter_user" ON public."SearchFilter"("userId");
CREATE INDEX "idx_search_filter_active" ON public."SearchFilter"("isActive");

CREATE INDEX "idx_user_activity_user" ON public."UserActivity"("userId");
CREATE INDEX "idx_user_activity_action" ON public."UserActivity"("action");
CREATE INDEX "idx_user_activity_entity" ON public."UserActivity"("entityType", "entityId");

CREATE INDEX "idx_buyer_listings_buyer" ON public."BuyerListings"("buyerId");
CREATE INDEX "idx_buyer_listings_active" ON public."BuyerListings"("isActive");

CREATE INDEX "idx_ai_report_user" ON public."AIReport"("userId");
CREATE INDEX "idx_ai_report_property" ON public."AIReport"("propertyId");
CREATE INDEX "idx_ai_report_type" ON public."AIReport"("reportType");

CREATE INDEX "idx_role_permissions_role" ON public."role_permissions"("role");
CREATE INDEX "idx_role_permissions_permission" ON public."role_permissions"("permission");

CREATE INDEX "idx_stripe_customers_user" ON public."stripe_customers"("user_id");
CREATE INDEX "idx_stripe_customers_stripe_id" ON public."stripe_customers"("stripe_customer_id");

CREATE INDEX "idx_stripe_payments_user" ON public."stripe_payments"("user_id");
CREATE INDEX "idx_stripe_payments_status" ON public."stripe_payments"("status");

CREATE INDEX "idx_stripe_invoices_user" ON public."stripe_invoices"("user_id");
CREATE INDEX "idx_stripe_invoices_status" ON public."stripe_invoices"("status");

CREATE INDEX "idx_stripe_webhook_events_type" ON public."stripe_webhook_events"("event_type");
CREATE INDEX "idx_stripe_webhook_events_processed" ON public."stripe_webhook_events"("processed");

-- Advertiser indexes
CREATE INDEX "idx_advertiser_user" ON public."Advertiser"("userId");
CREATE INDEX "idx_advertiser_service_provider" ON public."Advertiser"("serviceProviderId");
CREATE INDEX "idx_advertiser_service_type" ON public."Advertiser"("serviceType");
CREATE INDEX "idx_advertiser_plan" ON public."Advertiser"("plan");
CREATE INDEX "idx_advertiser_status" ON public."Advertiser"("status");
CREATE INDEX "idx_advertiser_premium" ON public."Advertiser"("isPremium");
CREATE INDEX "idx_advertiser_verified" ON public."Advertiser"("isVerified");
CREATE INDEX "idx_advertiser_featured" ON public."Advertiser"("featuredUntil");

CREATE INDEX "idx_advertiser_subscription_advertiser" ON public."AdvertiserSubscription"("advertiserId");
CREATE INDEX "idx_advertiser_subscription_stripe" ON public."AdvertiserSubscription"("stripeSubscriptionId");
CREATE INDEX "idx_advertiser_subscription_plan" ON public."AdvertiserSubscription"("plan");
CREATE INDEX "idx_advertiser_subscription_status" ON public."AdvertiserSubscription"("status");

-- Advertiser indexes
CREATE INDEX "idx_advertiser_user" ON public."Advertiser"("userId");
CREATE INDEX "idx_advertiser_service_provider" ON public."Advertiser"("serviceProviderId");
CREATE INDEX "idx_advertiser_service_type" ON public."Advertiser"("serviceType");
CREATE INDEX "idx_advertiser_plan" ON public."Advertiser"("plan");
CREATE INDEX "idx_advertiser_status" ON public."Advertiser"("status");
CREATE INDEX "idx_advertiser_premium" ON public."Advertiser"("isPremium");
CREATE INDEX "idx_advertiser_verified" ON public."Advertiser"("isVerified");
CREATE INDEX "idx_advertiser_featured" ON public."Advertiser"("featuredUntil");

CREATE INDEX "idx_advertiser_subscription_advertiser" ON public."AdvertiserSubscription"("advertiserId");
CREATE INDEX "idx_advertiser_subscription_stripe" ON public."AdvertiserSubscription"("stripeSubscriptionId");
CREATE INDEX "idx_advertiser_subscription_plan" ON public."AdvertiserSubscription"("plan");
CREATE INDEX "idx_advertiser_subscription_status" ON public."AdvertiserSubscription"("status");
