using MicroSaasWebApi.Services.Auth.Clerk.Interface;
using Microsoft.Extensions.Configuration;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Threading.Tasks;
using System.Text.Json;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.IdentityModel.Tokens;
using MicroSaasWebApi.Models.Auth;
using MicroSaasWebApi.Models.Auth.Clerk;
using MicroSaasWebApi.Services.Configuration;

namespace MicroSaasWebApi.Services.Auth.Clerk
{
    public class ClerkService : IClerkService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<ClerkService> _logger;

        public ClerkService(HttpClient httpClient, IConfiguration configuration, ILogger<ClerkService> logger)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _logger = logger;

            var clerkSecretKey = EnvironmentConfigurationService.GetEnvironmentVariable("CLERK_SECRET_KEY");
            if (string.IsNullOrEmpty(clerkSecretKey))
            {
                throw new InvalidOperationException("CLERK_SECRET_KEY environment variable is required");
            }

            _httpClient.BaseAddress = new Uri("https://api.clerk.com/v1/");
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", clerkSecretKey);
        }

        public async Task<ClerkUser?> GetUserByIdAsync(string userId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"users/{userId}");
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<ClerkUser>(json, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
                    });
                }

                _logger.LogWarning("Failed to get user from Clerk API. Status: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user from Clerk API for userId: {UserId}", userId);
                return null;
            }
        }

        public async Task<ClerkUser?> GetUserByEmailAsync(string email)
        {
            try
            {
                var response = await _httpClient.GetAsync($"users?email_address={Uri.EscapeDataString(email)}");
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    var users = JsonSerializer.Deserialize<ClerkUser[]>(json, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
                    });

                    return users?.FirstOrDefault();
                }

                _logger.LogWarning("Failed to get user by email from Clerk API. Status: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by email from Clerk API for email: {Email}", email);
                return null;
            }
        }

        public ClaimsPrincipal? ValidateJwtToken(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var clerkDomain = EnvironmentConfigurationService.GetEnvironmentVariable("NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY");

                if (string.IsNullOrEmpty(clerkDomain))
                {
                    _logger.LogError("NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY environment variable is required");
                    return null;
                }

                // Extract domain from publishable key (format: pk_test_domain or pk_live_domain)
                var domain = ExtractDomainFromPublishableKey(clerkDomain);

                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKeyResolver = (token, securityToken, kid, parameters) =>
                    {
                        // Clerk uses JWKS endpoint for key validation
                        return GetClerkSigningKeys(domain, kid);
                    },
                    ValidateIssuer = true,
                    ValidIssuer = $"https://{domain}",
                    ValidateAudience = false, // Clerk doesn't use audience validation by default
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.FromMinutes(5)
                };

                var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);
                return principal;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating JWT token");
                return null;
            }
        }

        public UserInfo? ExtractUserInfoFromToken(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var jwtToken = tokenHandler.ReadJwtToken(token);

                return new UserInfo
                {
                    Id = Guid.TryParse(jwtToken.Claims.FirstOrDefault(c => c.Type == "sub")?.Value, out var userId) ? userId : Guid.NewGuid(),
                    Email = jwtToken.Claims.FirstOrDefault(c => c.Type == "email")?.Value ?? string.Empty,
                    FirstName = jwtToken.Claims.FirstOrDefault(c => c.Type == "given_name")?.Value ?? string.Empty,
                    LastName = jwtToken.Claims.FirstOrDefault(c => c.Type == "family_name")?.Value ?? string.Empty,
                    IsEmailVerified = bool.TryParse(jwtToken.Claims.FirstOrDefault(c => c.Type == "email_verified")?.Value, out var verified) && verified
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting user info from token");
                return null;
            }
        }

        private string ExtractDomainFromPublishableKey(string publishableKey)
        {
            // Clerk publishable keys format: pk_test_domain.clerk.accounts.dev or pk_live_domain
            if (publishableKey.StartsWith("pk_test_"))
            {
                var base64Part = publishableKey.Substring(8); // Remove "pk_test_"
                try
                {
                    var decoded = Convert.FromBase64String(base64Part + "=="); // Add padding if needed
                    return System.Text.Encoding.UTF8.GetString(decoded);
                }
                catch
                {
                    // Fallback to configuration
                    return _configuration["Clerk:Domain"] ?? "discrete-hare-73.clerk.accounts.dev";
                }
            }

            return _configuration["Clerk:Domain"] ?? "discrete-hare-73.clerk.accounts.dev";
        }

        private IEnumerable<SecurityKey> GetClerkSigningKeys(string domain, string? kid)
        {
            // In a production environment, you would fetch and cache the JWKS from Clerk
            // For now, return empty collection - this would need to be implemented based on Clerk's JWKS endpoint
            _logger.LogWarning("JWKS key resolution not implemented. Domain: {Domain}, Kid: {Kid}", domain, kid);
            return new List<SecurityKey>();
        }

        public async Task<ClerkUser?> AuthenticateUserAsync(string email, string password)
        {
            // Placeholder implementation - integrate with actual Clerk API
            // For now, return a mock user for testing
            return await Task.FromResult(new ClerkUser
            {
                Id = Guid.NewGuid().ToString(),
                EmailAddress = email,
                FirstName = "Test",
                LastName = "User",
                EmailVerified = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            });
        }

        public async Task<ClerkUser?> CreateUserAsync(string email, string password, string firstName, string lastName)
        {
            // Placeholder implementation - integrate with actual Clerk API
            // For now, return a mock user for testing
            return await Task.FromResult(new ClerkUser
            {
                Id = Guid.NewGuid().ToString(),
                EmailAddress = email,
                FirstName = firstName,
                LastName = lastName,
                EmailVerified = false,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            });
        }

        public async Task<ClerkUser?> GetUserAsync(string userId)
        {
            // Use the existing GetUserByIdAsync method
            return await GetUserByIdAsync(userId);
        }

        public async Task<bool> UpdateUserAsync(string userId, string? firstName = null, string? lastName = null)
        {
            // Placeholder implementation - integrate with actual Clerk API
            return await Task.FromResult(true);
        }

        public async Task<bool> DeleteUserAsync(string userId)
        {
            // Placeholder implementation - integrate with actual Clerk API
            return await Task.FromResult(true);
        }
    }
}
