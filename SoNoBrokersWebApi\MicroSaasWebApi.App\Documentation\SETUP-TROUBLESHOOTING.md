# MicroSaaS Web API Setup Troubleshooting Guide

## Package Version Conflicts

### Problem: NU1605 Package Downgrade Errors
```
error: NU1605: Warning As Error: Detected package downgrade: System.IdentityModel.Tokens.Jwt from 7.1.2 to 7.0.3
```

### Solution:
1. **Update package versions in .csproj file** (already done)
2. **Clean and restore packages:**
   ```bash
   dotnet clean
   dotnet restore
   dotnet build
   ```

3. **If issues persist, clear NuGet cache:**
   ```bash
   dotnet nuget locals all --clear
   dotnet restore
   ```

## Entity Framework Tools Missing

### Problem: `dotnet-ef` command not found

### Solution:
```bash
# Install EF Core tools globally
dotnet tool install --global dotnet-ef

# Verify installation
dotnet ef --version
```

## Database Connection Issues

### Problem: Cannot connect to PostgreSQL database

### Solutions:

1. **Check your .env file has correct database settings:**
   ```env
   DATABASE_URL=***************************************************************************************************/postgres
   POSTGRES_USER=postgres
   POSTGRES_PASSWORD=Shared4w0rk!
   POSTGRES_DBNAME=postgres
   DATABASE_HOST=aws-0-ca-central-1.pooler.supabase.com
   DATABASE_PORT=5432
   ```

2. **Test connection manually:**
   ```bash
   # Using psql (if installed)
   psql "***************************************************************************************************/postgres"
   ```

3. **Check if Supabase database is accessible:**
   - Verify your Supabase project is active
   - Check if your IP is whitelisted in Supabase settings
   - Ensure the connection string is correct

## Migration Issues

### Problem: Migration creation fails

### Solutions:

1. **Check DbContext configuration:**
   - Ensure `ApplicationDbContext` is properly configured
   - Verify connection string is loaded correctly

2. **Build project first:**
   ```bash
   dotnet build
   dotnet ef migrations add InitialMicroSaasMigration
   ```

3. **Specify startup project if needed:**
   ```bash
   dotnet ef migrations add InitialMicroSaasMigration --startup-project MicroSaasWebApi.Full
   ```

## Runtime Issues

### Problem: Application fails to start

### Common Solutions:

1. **Check required environment variables:**
   ```bash
   # Run configuration validation
   curl http://localhost:5000/api/configuration/validate
   ```

2. **Verify .env file is loaded:**
   - Check if `.env` file exists in project root
   - Ensure `EnvironmentConfigurationService.LoadEnvironmentVariables()` is called

3. **Check logs for specific errors:**
   - Look in console output
   - Check log files if file logging is enabled

## Clerk Authentication Issues

### Problem: JWT validation fails

### Solutions:

1. **Verify Clerk configuration:**
   ```env
   NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
   CLERK_SECRET_KEY=sk_test_...
   ```

2. **Check Clerk domain configuration:**
   - Ensure the domain matches your Clerk application
   - Verify JWKS endpoint is accessible

## Stripe Payment Issues

### Problem: Payment processing fails

### Solutions:

1. **Verify Stripe keys:**
   ```env
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_...
   STRIPE_SECRET_KEY=sk_live_...
   STRIPE_WEBHOOK_SECRET=whsec_...
   ```

2. **Test Stripe connection:**
   - Use Stripe CLI to test webhooks
   - Verify API keys are valid

## Quick Setup Commands

### For Windows (PowerShell):
```powershell
# Run the setup script
.\setup-database.ps1
```

### For Linux/Mac (Bash):
```bash
# Make script executable
chmod +x setup-database.sh

# Run the setup script
./setup-database.sh
```

### Manual Setup:
```bash
# Install EF tools
dotnet tool install --global dotnet-ef

# Navigate to project
cd MicroSaasWebApi.Full

# Clean and restore
dotnet clean
dotnet restore
dotnet build

# Create migration
dotnet ef migrations add InitialMicroSaasMigration

# Update database
dotnet ef database update

# Run application
dotnet run
```

## Testing the Setup

### 1. Check Application Info:
```bash
curl http://localhost:5000/api/configuration/info
```

### 2. Validate Configuration (requires admin auth):
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" http://localhost:5000/api/configuration/validate
```

### 3. Check Health Status:
```bash
curl http://localhost:5000/health
```

### 4. View API Documentation:
- Swagger UI: `http://localhost:5000/swagger`
- ReDoc: `http://localhost:5000/redoc`

## Common Environment Variables

Make sure your `.env` file includes these required variables:

```env
# Database (Required)
DATABASE_URL=postgresql://user:password@host:port/database

# Clerk Authentication (Required)
CLERK_SECRET_KEY=sk_test_...
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...

# Stripe Payments (Required for payment features)
STRIPE_SECRET_KEY=sk_live_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_...

# Application Settings (Optional)
APP_NAME=MicroSaasWebApi
APP_VERSION=1.0.0
ASPNETCORE_ENVIRONMENT=Development
```

## Getting Help

If you continue to have issues:

1. Check the application logs
2. Verify all environment variables are set correctly
3. Ensure your database server is running and accessible
4. Test individual components (database connection, authentication, etc.)
5. Review the error messages carefully for specific guidance
