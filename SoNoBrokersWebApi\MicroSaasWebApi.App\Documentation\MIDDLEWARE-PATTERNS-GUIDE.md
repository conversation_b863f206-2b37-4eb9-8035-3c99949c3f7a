# ⚙️ Middleware Patterns & Ordering Guide

## 📋 Table of Contents
- [Middleware Design Patterns](#middleware-design-patterns)
- [Execution Order Strategy](#execution-order-strategy)
- [Custom Middleware Examples](#custom-middleware-examples)
- [Error Handling Patterns](#error-handling-patterns)
- [Performance Optimization](#performance-optimization)
- [Testing Middleware](#testing-middleware)

## 🎯 Middleware Design Patterns

### **1. Chain of Responsibility Pattern**

The middleware pipeline implements the Chain of Responsibility pattern, where each middleware can:
- Process the request
- Pass control to the next middleware
- Process the response on the way back

```csharp
public class ExampleMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ExampleMiddleware> _logger;

    public ExampleMiddleware(RequestDelegate next, ILogger<ExampleMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Pre-processing (request phase)
        _logger.LogInformation("Processing request: {Path}", context.Request.Path);
        
        try
        {
            // Call the next middleware in the pipeline
            await _next(context);
        }
        catch (Exception ex)
        {
            // Handle exceptions from downstream middleware
            _logger.LogError(ex, "Error in downstream middleware");
            throw;
        }
        
        // Post-processing (response phase)
        _logger.LogInformation("Response status: {StatusCode}", context.Response.StatusCode);
    }
}
```

### **2. Decorator Pattern**

Enhance existing middleware functionality without modifying the original:

```csharp
public class TimingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<TimingMiddleware> _logger;

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        
        await _next(context);
        
        stopwatch.Stop();
        _logger.LogInformation("Request {Path} took {ElapsedMs}ms", 
            context.Request.Path, stopwatch.ElapsedMilliseconds);
    }
}
```

### **3. Strategy Pattern**

Different middleware strategies based on conditions:

```csharp
public class ConditionalMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IEnumerable<IMiddlewareStrategy> _strategies;

    public async Task InvokeAsync(HttpContext context)
    {
        var strategy = _strategies.FirstOrDefault(s => s.CanHandle(context));
        
        if (strategy != null)
        {
            await strategy.HandleAsync(context, _next);
        }
        else
        {
            await _next(context);
        }
    }
}

public interface IMiddlewareStrategy
{
    bool CanHandle(HttpContext context);
    Task HandleAsync(HttpContext context, RequestDelegate next);
}
```

## 📊 Execution Order Strategy

### **Critical Ordering Rules**

```csharp
public static class MiddlewareOrderingRules
{
    // 1. Exception handling MUST be first
    public static void UseExceptionHandling(this IApplicationBuilder app)
    {
        if (app.Environment.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }
        else
        {
            app.UseExceptionHandler("/Error");
        }
    }

    // 2. Security headers early in pipeline
    public static void UseSecurityHeaders(this IApplicationBuilder app)
    {
        app.Use(async (context, next) =>
        {
            context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
            context.Response.Headers.Add("X-Frame-Options", "DENY");
            context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");
            await next();
        });
    }

    // 3. Rate limiting before expensive operations
    public static void UseRateLimiting(this IApplicationBuilder app)
    {
        app.UseRateLimiter();
    }

    // 4. Authentication before authorization
    public static void UseAuthenticationAndAuthorization(this IApplicationBuilder app)
    {
        app.UseAuthentication();
        app.UseAuthorization();
    }

    // 5. Tenant resolution after authentication
    public static void UseTenantResolution(this IApplicationBuilder app)
    {
        app.UseMiddleware<TenantResolutionMiddleware>();
    }
}
```

### **Middleware Priority Matrix**

| Priority | Middleware Type | Reason | Example |
|----------|----------------|--------|---------|
| 1 | Exception Handling | Must catch all exceptions | `UseExceptionHandler` |
| 2 | Security Headers | Early security measures | `UseSecurityHeaders` |
| 3 | Rate Limiting | Prevent abuse early | `UseRateLimiter` |
| 4 | Request Logging | Audit all requests | `UseRequestLogging` |
| 5 | HTTPS Redirection | Force secure connections | `UseHttpsRedirection` |
| 6 | Static Files | Serve static content efficiently | `UseStaticFiles` |
| 7 | Routing | Enable routing system | `UseRouting` |
| 8 | CORS | Handle cross-origin requests | `UseCors` |
| 9 | Authentication | Identify users | `UseAuthentication` |
| 10 | Authorization | Check permissions | `UseAuthorization` |
| 11 | Tenant Resolution | Multi-tenant context | `UseTenantResolution` |
| 12 | Business Logic | Custom application logic | Custom middleware |
| 13 | Response Processing | Modify responses | `UseResponseCompression` |

## 🛠️ Custom Middleware Examples

### **Request/Response Logging Middleware**

```csharp
public class RequestResponseLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestResponseLoggingMiddleware> _logger;

    public async Task InvokeAsync(HttpContext context)
    {
        // Log request
        await LogRequestAsync(context);

        // Capture response
        var originalBodyStream = context.Response.Body;
        using var responseBody = new MemoryStream();
        context.Response.Body = responseBody;

        await _next(context);

        // Log response
        await LogResponseAsync(context, responseBody);

        // Copy response back to original stream
        await responseBody.CopyToAsync(originalBodyStream);
    }

    private async Task LogRequestAsync(HttpContext context)
    {
        var request = context.Request;
        var requestBody = string.Empty;

        if (request.ContentLength > 0)
        {
            request.EnableBuffering();
            using var reader = new StreamReader(request.Body, leaveOpen: true);
            requestBody = await reader.ReadToEndAsync();
            request.Body.Position = 0;
        }

        _logger.LogInformation("Request: {Method} {Path} {QueryString} Body: {Body}",
            request.Method, request.Path, request.QueryString, requestBody);
    }

    private async Task LogResponseAsync(HttpContext context, MemoryStream responseBody)
    {
        responseBody.Seek(0, SeekOrigin.Begin);
        var responseText = await new StreamReader(responseBody).ReadToEndAsync();
        responseBody.Seek(0, SeekOrigin.Begin);

        _logger.LogInformation("Response: {StatusCode} Body: {Body}",
            context.Response.StatusCode, responseText);
    }
}
```

### **Correlation ID Middleware**

```csharp
public class CorrelationIdMiddleware
{
    private readonly RequestDelegate _next;
    private const string CorrelationIdHeader = "X-Correlation-ID";

    public async Task InvokeAsync(HttpContext context)
    {
        var correlationId = GetOrCreateCorrelationId(context);
        
        // Add to response headers
        context.Response.Headers.Add(CorrelationIdHeader, correlationId);
        
        // Add to logging context
        using (LogContext.PushProperty("CorrelationId", correlationId))
        {
            await _next(context);
        }
    }

    private string GetOrCreateCorrelationId(HttpContext context)
    {
        if (context.Request.Headers.TryGetValue(CorrelationIdHeader, out var correlationId))
        {
            return correlationId.FirstOrDefault() ?? Guid.NewGuid().ToString();
        }

        return Guid.NewGuid().ToString();
    }
}
```

### **API Versioning Middleware**

```csharp
public class ApiVersioningMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ApiVersioningMiddleware> _logger;

    public async Task InvokeAsync(HttpContext context)
    {
        var version = ExtractApiVersion(context);
        
        if (version != null)
        {
            context.Items["ApiVersion"] = version;
            _logger.LogInformation("API Version: {Version}", version);
        }

        await _next(context);
    }

    private string? ExtractApiVersion(HttpContext context)
    {
        // Check header
        if (context.Request.Headers.TryGetValue("X-API-Version", out var headerVersion))
        {
            return headerVersion.FirstOrDefault();
        }

        // Check query parameter
        if (context.Request.Query.TryGetValue("version", out var queryVersion))
        {
            return queryVersion.FirstOrDefault();
        }

        // Check path
        var path = context.Request.Path.Value;
        var match = Regex.Match(path ?? "", @"/api/v(\d+)/");
        if (match.Success)
        {
            return match.Groups[1].Value;
        }

        return null;
    }
}
```

## 🚨 Error Handling Patterns

### **Global Exception Handler**

```csharp
public class GlobalExceptionHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionHandlingMiddleware> _logger;

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred");
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var response = context.Response;
        response.ContentType = "application/json";

        var errorResponse = exception switch
        {
            ValidationException => new ErrorResponse
            {
                StatusCode = 400,
                Message = "Validation failed",
                Details = exception.Message
            },
            UnauthorizedException => new ErrorResponse
            {
                StatusCode = 401,
                Message = "Unauthorized access"
            },
            NotFoundException => new ErrorResponse
            {
                StatusCode = 404,
                Message = "Resource not found"
            },
            TenantNotFoundException => new ErrorResponse
            {
                StatusCode = 400,
                Message = "Tenant not found or invalid"
            },
            _ => new ErrorResponse
            {
                StatusCode = 500,
                Message = "An internal server error occurred"
            }
        };

        response.StatusCode = errorResponse.StatusCode;
        await response.WriteAsync(JsonSerializer.Serialize(errorResponse));
    }
}

public class ErrorResponse
{
    public int StatusCode { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Details { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}
```

### **Circuit Breaker Pattern**

```csharp
public class CircuitBreakerMiddleware
{
    private readonly RequestDelegate _next;
    private readonly CircuitBreakerOptions _options;
    private int _failureCount = 0;
    private DateTime _lastFailureTime = DateTime.MinValue;
    private CircuitState _state = CircuitState.Closed;

    public async Task InvokeAsync(HttpContext context)
    {
        if (_state == CircuitState.Open)
        {
            if (DateTime.UtcNow - _lastFailureTime > _options.Timeout)
            {
                _state = CircuitState.HalfOpen;
            }
            else
            {
                context.Response.StatusCode = 503;
                await context.Response.WriteAsync("Service temporarily unavailable");
                return;
            }
        }

        try
        {
            await _next(context);
            
            if (_state == CircuitState.HalfOpen)
            {
                _state = CircuitState.Closed;
                _failureCount = 0;
            }
        }
        catch (Exception)
        {
            _failureCount++;
            _lastFailureTime = DateTime.UtcNow;

            if (_failureCount >= _options.FailureThreshold)
            {
                _state = CircuitState.Open;
            }

            throw;
        }
    }
}

public enum CircuitState { Closed, Open, HalfOpen }
```

## ⚡ Performance Optimization

### **Response Caching Middleware**

```csharp
public class SmartCachingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IMemoryCache _cache;
    private readonly ILogger<SmartCachingMiddleware> _logger;

    public async Task InvokeAsync(HttpContext context)
    {
        if (!ShouldCache(context))
        {
            await _next(context);
            return;
        }

        var cacheKey = GenerateCacheKey(context);
        
        if (_cache.TryGetValue(cacheKey, out CachedResponse cachedResponse))
        {
            await WriteCachedResponse(context, cachedResponse);
            return;
        }

        var originalBodyStream = context.Response.Body;
        using var responseBody = new MemoryStream();
        context.Response.Body = responseBody;

        await _next(context);

        var response = await CaptureResponse(context, responseBody);
        _cache.Set(cacheKey, response, TimeSpan.FromMinutes(5));

        await responseBody.CopyToAsync(originalBodyStream);
    }

    private bool ShouldCache(HttpContext context)
    {
        return context.Request.Method == "GET" && 
               !context.Request.Path.StartsWithSegments("/api/admin");
    }

    private string GenerateCacheKey(HttpContext context)
    {
        var tenantId = context.Items["TenantId"]?.ToString() ?? "default";
        return $"{tenantId}:{context.Request.Path}:{context.Request.QueryString}";
    }
}
```

### **Request Compression Middleware**

```csharp
public class RequestCompressionMiddleware
{
    private readonly RequestDelegate _next;

    public async Task InvokeAsync(HttpContext context)
    {
        if (context.Request.Headers.ContainsKey("Content-Encoding"))
        {
            var encoding = context.Request.Headers["Content-Encoding"].ToString();
            
            if (encoding.Contains("gzip"))
            {
                context.Request.Body = new GZipStream(context.Request.Body, CompressionMode.Decompress);
            }
            else if (encoding.Contains("deflate"))
            {
                context.Request.Body = new DeflateStream(context.Request.Body, CompressionMode.Decompress);
            }
        }

        await _next(context);
    }
}
```

## 🧪 Testing Middleware

### **Unit Testing Middleware**

```csharp
[Test]
public async Task TenantResolutionMiddleware_ShouldSetTenantFromSubdomain()
{
    // Arrange
    var context = new DefaultHttpContext();
    context.Request.Host = new HostString("acme.microsaas.com");
    
    var tenantProvider = new Mock<ITenantProviderService>();
    var logger = new Mock<ILogger<TenantResolutionMiddleware>>();
    
    var middleware = new TenantResolutionMiddleware(
        async (ctx) => await Task.CompletedTask, 
        logger.Object);

    // Act
    await middleware.InvokeAsync(context, tenantProvider.Object);

    // Assert
    tenantProvider.Verify(tp => tp.SetTenant(It.Is<Tenant>(t => t.Id == "acme")), Times.Once);
    Assert.AreEqual("acme", context.Items["TenantId"]);
}
```

### **Integration Testing**

```csharp
[Test]
public async Task MiddlewarePipeline_ShouldExecuteInCorrectOrder()
{
    // Arrange
    var builder = WebApplication.CreateBuilder();
    builder.Services.AddScoped<ITenantProviderService, TenantProviderService>();
    
    var app = builder.Build();
    app.ConfigureMiddlewarePipeline();

    var client = new TestServer(app).CreateClient();

    // Act
    var response = await client.GetAsync("https://acme.microsaas.com/api/users");

    // Assert
    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
    Assert.IsTrue(response.Headers.Contains("X-Tenant-Id"));
}
```

This middleware patterns guide provides a comprehensive foundation for building robust, ordered, and testable middleware pipelines in your .NET 9 MicroSaaS application!
