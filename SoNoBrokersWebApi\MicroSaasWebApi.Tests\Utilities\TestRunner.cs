using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Reflection;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Utilities
{
    /// <summary>
    /// Advanced test runner for executing and managing test scenarios
    /// </summary>
    public class TestRunner : IDisposable
    {
        private readonly ITestOutputHelper _output;
        private readonly IServiceProvider _serviceProvider;
        private readonly List<TestResult> _results = new();
        private readonly Stopwatch _overallStopwatch = new();

        public TestRunner(ITestOutputHelper output, IServiceProvider? serviceProvider = null)
        {
            _output = output;
            _serviceProvider = serviceProvider ?? CreateDefaultServiceProvider();
        }

        /// <summary>
        /// Runs a single test scenario
        /// </summary>
        public async Task<TestResult> RunTestAsync<T>(string testName, Func<T, Task> testAction) where T : class
        {
            var service = _serviceProvider.GetRequiredService<T>();
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                _output.WriteLine($"Starting test: {testName}");
                await testAction(service);
                stopwatch.Stop();
                
                var result = new TestResult
                {
                    TestName = testName,
                    Success = true,
                    Duration = stopwatch.Elapsed,
                    Message = "Test completed successfully"
                };
                
                _results.Add(result);
                _output.WriteLine($"✓ {testName} completed in {stopwatch.ElapsedMilliseconds}ms");
                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                var result = new TestResult
                {
                    TestName = testName,
                    Success = false,
                    Duration = stopwatch.Elapsed,
                    Message = ex.Message,
                    Exception = ex
                };
                
                _results.Add(result);
                _output.WriteLine($"✗ {testName} failed after {stopwatch.ElapsedMilliseconds}ms: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// Runs multiple test scenarios in sequence
        /// </summary>
        public async Task<TestSuiteResult> RunTestSuiteAsync(params TestScenario[] scenarios)
        {
            _overallStopwatch.Start();
            _output.WriteLine($"Starting test suite with {scenarios.Length} scenarios");

            var results = new List<TestResult>();

            foreach (var scenario in scenarios)
            {
                var result = await RunTestAsync(scenario.Name, scenario.TestAction);
                results.Add(result);

                if (!result.Success && scenario.StopOnFailure)
                {
                    _output.WriteLine($"Stopping test suite due to failure in {scenario.Name}");
                    break;
                }
            }

            _overallStopwatch.Stop();

            var suiteResult = new TestSuiteResult
            {
                Results = results,
                TotalDuration = _overallStopwatch.Elapsed,
                SuccessCount = results.Count(r => r.Success),
                FailureCount = results.Count(r => !r.Success),
                TotalCount = results.Count
            };

            _output.WriteLine($"Test suite completed: {suiteResult.SuccessCount}/{suiteResult.TotalCount} passed in {suiteResult.TotalDuration.TotalSeconds:F2}s");

            return suiteResult;
        }

        /// <summary>
        /// Runs tests in parallel
        /// </summary>
        public async Task<TestSuiteResult> RunParallelTestsAsync(params TestScenario[] scenarios)
        {
            _overallStopwatch.Start();
            _output.WriteLine($"Starting parallel test execution with {scenarios.Length} scenarios");

            var tasks = scenarios.Select(async scenario =>
            {
                return await RunTestAsync(scenario.Name, scenario.TestAction);
            });

            var results = await Task.WhenAll(tasks);
            _overallStopwatch.Stop();

            var suiteResult = new TestSuiteResult
            {
                Results = results.ToList(),
                TotalDuration = _overallStopwatch.Elapsed,
                SuccessCount = results.Count(r => r.Success),
                FailureCount = results.Count(r => !r.Success),
                TotalCount = results.Length
            };

            _output.WriteLine($"Parallel tests completed: {suiteResult.SuccessCount}/{suiteResult.TotalCount} passed in {suiteResult.TotalDuration.TotalSeconds:F2}s");

            return suiteResult;
        }

        /// <summary>
        /// Runs performance benchmarks
        /// </summary>
        public async Task<PerformanceBenchmarkResult> RunPerformanceBenchmarkAsync<T>(
            string benchmarkName,
            Func<T, Task> action,
            int iterations = 10,
            int warmupIterations = 2) where T : class
        {
            var service = _serviceProvider.GetRequiredService<T>();
            _output.WriteLine($"Starting performance benchmark: {benchmarkName}");

            // Warmup
            for (int i = 0; i < warmupIterations; i++)
            {
                await action(service);
            }

            // Actual benchmark
            var durations = new List<TimeSpan>();
            var overallStopwatch = Stopwatch.StartNew();

            for (int i = 0; i < iterations; i++)
            {
                var iterationStopwatch = Stopwatch.StartNew();
                await action(service);
                iterationStopwatch.Stop();
                durations.Add(iterationStopwatch.Elapsed);
            }

            overallStopwatch.Stop();

            var result = new PerformanceBenchmarkResult
            {
                BenchmarkName = benchmarkName,
                Iterations = iterations,
                TotalDuration = overallStopwatch.Elapsed,
                AverageDuration = TimeSpan.FromTicks((long)durations.Average(d => d.Ticks)),
                MinDuration = durations.Min(),
                MaxDuration = durations.Max(),
                MedianDuration = durations.OrderBy(d => d).Skip(durations.Count / 2).First(),
                StandardDeviation = CalculateStandardDeviation(durations)
            };

            _output.WriteLine($"Benchmark completed: {benchmarkName}");
            _output.WriteLine($"  Iterations: {result.Iterations}");
            _output.WriteLine($"  Average: {result.AverageDuration.TotalMilliseconds:F2}ms");
            _output.WriteLine($"  Min: {result.MinDuration.TotalMilliseconds:F2}ms");
            _output.WriteLine($"  Max: {result.MaxDuration.TotalMilliseconds:F2}ms");
            _output.WriteLine($"  Median: {result.MedianDuration.TotalMilliseconds:F2}ms");
            _output.WriteLine($"  Std Dev: {result.StandardDeviation:F2}ms");

            return result;
        }

        /// <summary>
        /// Runs load tests with increasing concurrency
        /// </summary>
        public async Task<LoadTestResult> RunLoadTestAsync<T>(
            string testName,
            Func<T, Task> action,
            int[] concurrencyLevels = null!) where T : class
        {
            concurrencyLevels ??= new[] { 1, 5, 10, 20, 50 };
            _output.WriteLine($"Starting load test: {testName}");

            var results = new List<ConcurrencyResult>();

            foreach (var concurrency in concurrencyLevels)
            {
                _output.WriteLine($"Testing with {concurrency} concurrent requests...");

                var tasks = new List<Task>();
                var stopwatch = Stopwatch.StartNew();

                for (int i = 0; i < concurrency; i++)
                {
                    var service = _serviceProvider.GetRequiredService<T>();
                    tasks.Add(action(service));
                }

                await Task.WhenAll(tasks);
                stopwatch.Stop();

                var concurrencyResult = new ConcurrencyResult
                {
                    ConcurrencyLevel = concurrency,
                    TotalDuration = stopwatch.Elapsed,
                    AverageResponseTime = TimeSpan.FromTicks(stopwatch.Elapsed.Ticks / concurrency),
                    RequestsPerSecond = concurrency / stopwatch.Elapsed.TotalSeconds
                };

                results.Add(concurrencyResult);
                _output.WriteLine($"  {concurrency} requests in {stopwatch.ElapsedMilliseconds}ms ({concurrencyResult.RequestsPerSecond:F2} req/s)");
            }

            return new LoadTestResult
            {
                TestName = testName,
                Results = results
            };
        }

        /// <summary>
        /// Generates a comprehensive test report
        /// </summary>
        public TestReport GenerateReport()
        {
            return new TestReport
            {
                GeneratedAt = DateTime.UtcNow,
                TotalTests = _results.Count,
                PassedTests = _results.Count(r => r.Success),
                FailedTests = _results.Count(r => !r.Success),
                TotalDuration = _results.Sum(r => r.Duration.TotalMilliseconds),
                AverageDuration = _results.Any() ? _results.Average(r => r.Duration.TotalMilliseconds) : 0,
                Results = _results.ToList()
            };
        }

        private static IServiceProvider CreateDefaultServiceProvider()
        {
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddConsole());
            return services.BuildServiceProvider();
        }

        private static double CalculateStandardDeviation(List<TimeSpan> durations)
        {
            var average = durations.Average(d => d.TotalMilliseconds);
            var sumOfSquares = durations.Sum(d => Math.Pow(d.TotalMilliseconds - average, 2));
            return Math.Sqrt(sumOfSquares / durations.Count);
        }

        public void Dispose()
        {
            _overallStopwatch?.Stop();
            if (_serviceProvider is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }
    }

    /// <summary>
    /// Test scenario definition
    /// </summary>
    public class TestScenario
    {
        public string Name { get; set; } = string.Empty;
        public Func<object, Task> TestAction { get; set; } = null!;
        public bool StopOnFailure { get; set; } = false;
    }

    /// <summary>
    /// Individual test result
    /// </summary>
    public class TestResult
    {
        public string TestName { get; set; } = string.Empty;
        public bool Success { get; set; }
        public TimeSpan Duration { get; set; }
        public string Message { get; set; } = string.Empty;
        public Exception? Exception { get; set; }
    }

    /// <summary>
    /// Test suite result
    /// </summary>
    public class TestSuiteResult
    {
        public List<TestResult> Results { get; set; } = new();
        public TimeSpan TotalDuration { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public int TotalCount { get; set; }
        public double SuccessRate => TotalCount > 0 ? (double)SuccessCount / TotalCount * 100 : 0;
    }

    /// <summary>
    /// Performance benchmark result
    /// </summary>
    public class PerformanceBenchmarkResult
    {
        public string BenchmarkName { get; set; } = string.Empty;
        public int Iterations { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public TimeSpan AverageDuration { get; set; }
        public TimeSpan MinDuration { get; set; }
        public TimeSpan MaxDuration { get; set; }
        public TimeSpan MedianDuration { get; set; }
        public double StandardDeviation { get; set; }
    }

    /// <summary>
    /// Load test result
    /// </summary>
    public class LoadTestResult
    {
        public string TestName { get; set; } = string.Empty;
        public List<ConcurrencyResult> Results { get; set; } = new();
    }

    /// <summary>
    /// Concurrency test result
    /// </summary>
    public class ConcurrencyResult
    {
        public int ConcurrencyLevel { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public double RequestsPerSecond { get; set; }
    }

    /// <summary>
    /// Comprehensive test report
    /// </summary>
    public class TestReport
    {
        public DateTime GeneratedAt { get; set; }
        public int TotalTests { get; set; }
        public int PassedTests { get; set; }
        public int FailedTests { get; set; }
        public double TotalDuration { get; set; }
        public double AverageDuration { get; set; }
        public List<TestResult> Results { get; set; } = new();
    }
}
