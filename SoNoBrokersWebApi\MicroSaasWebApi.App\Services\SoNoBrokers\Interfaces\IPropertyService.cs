using MicroSaasWebApi.Models.SoNoBrokers;

namespace MicroSaasWebApi.Services.SoNoBrokers.Interfaces
{
    public interface IPropertyService
    {
        Task<IEnumerable<Property>> GetAllPropertiesAsync();
        Task<Property?> GetPropertyByIdAsync(string id);
        Task<Property> CreatePropertyAsync(Property property);
        Task<Property> UpdatePropertyAsync(Property property);
        Task<bool> DeletePropertyAsync(string id);
        Task<bool> PropertyExistsAsync(string id);
        Task<IEnumerable<Property>> GetPropertiesBySellerAsync(string sellerId);
        Task<IEnumerable<Property>> GetPropertiesByStatusAsync(PropertyStatus status);
        Task<IEnumerable<Property>> SearchPropertiesAsync(
            string? searchTerm = null,
            string? city = null,
            string? province = null,
            decimal? minPrice = null,
            decimal? maxPrice = null,
            int? bedrooms = null,
            int? bathrooms = null,
            PropertyStatus? status = null);
        Task<(IEnumerable<Property> Properties, int TotalCount)> GetPropertiesPagedAsync(
            int page,
            int pageSize,
            PropertyStatus? status = null,
            string? propertyType = null,
            decimal? minPrice = null,
            decimal? maxPrice = null);
    }
}
