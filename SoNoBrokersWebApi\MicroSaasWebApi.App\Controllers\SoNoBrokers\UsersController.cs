using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.Core;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using MicroSaasWebApi.Models.SoNoBrokers;
using SoNoBrokersUser = MicroSaasWebApi.Models.SoNoBrokers.User;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/sonobrokers/users")]
    [Tags("Users")]
    public class UsersController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly ILogger<UsersController> _logger;

        public UsersController(IUserService userService, ILogger<UsersController> logger)
        {
            _userService = userService;
            _logger = logger;
        }

        /// <summary>
        /// Get all users
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ApiResponse<IEnumerable<SoNoBrokersUser>>>> GetUsers()
        {
            try
            {
                var users = await _userService.GetAllUsersAsync();
                return Ok(ApiResponse<IEnumerable<SoNoBrokersUser>>.SuccessResult(users, "Users retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving users");
                return StatusCode(500, ApiResponse<IEnumerable<SoNoBrokersUser>>.ErrorResult("Failed to retrieve users"));
            }
        }

        /// <summary>
        /// Get user by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<ApiResponse<SoNoBrokersUser>>> GetUser(string id)
        {
            try
            {
                var user = await _userService.GetUserByIdAsync(id);
                if (user == null)
                {
                    return NotFound(ApiResponse<SoNoBrokersUser>.ErrorResult("User not found"));
                }

                return Ok(ApiResponse<SoNoBrokersUser>.SuccessResult(user, "User retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user {UserId}", id);
                return StatusCode(500, ApiResponse<SoNoBrokersUser>.ErrorResult("Failed to retrieve user"));
            }
        }

        /// <summary>
        /// Create a new user
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<ApiResponse<SoNoBrokersUser>>> CreateUser(SoNoBrokersUser user)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse<SoNoBrokersUser>.ErrorResult("Invalid user data"));
                }

                var createdUser = await _userService.CreateUserAsync(user);

                return CreatedAtAction(nameof(GetUser), new { id = createdUser.Id },
                    ApiResponse<SoNoBrokersUser>.SuccessResult(createdUser, "User created successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user");
                return StatusCode(500, ApiResponse<SoNoBrokersUser>.ErrorResult("Failed to create user"));
            }
        }

        /// <summary>
        /// Update user
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<ApiResponse<SoNoBrokersUser>>> UpdateUser(string id, SoNoBrokersUser user)
        {
            try
            {
                if (id != user.Id)
                {
                    return BadRequest(ApiResponse<SoNoBrokersUser>.ErrorResult("User ID mismatch"));
                }

                user.Id = id; // Ensure ID is set
                var updatedUser = await _userService.UpdateUserAsync(user);

                if (updatedUser == null)
                {
                    return NotFound(ApiResponse<SoNoBrokersUser>.ErrorResult("User not found"));
                }

                return Ok(ApiResponse<SoNoBrokersUser>.SuccessResult(updatedUser, "User updated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {UserId}", id);
                return StatusCode(500, ApiResponse<SoNoBrokersUser>.ErrorResult("Failed to update user"));
            }
        }

        /// <summary>
        /// Delete user
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult<ApiResponse<object>>> DeleteUser(string id)
        {
            try
            {
                var deleted = await _userService.DeleteUserAsync(id);

                if (!deleted)
                {
                    return NotFound(ApiResponse<object>.ErrorResult("User not found"));
                }

                return Ok(ApiResponse<object>.SuccessResult(new { }, "User deleted successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user {UserId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResult("Failed to delete user"));
            }
        }



        /// <summary>
        /// Get user by email
        /// </summary>
        [HttpGet("by-email/{email}")]
        public async Task<ActionResult<ApiResponse<SoNoBrokersUser>>> GetUserByEmail(string email)
        {
            try
            {
                var user = await _userService.GetUserByEmailAsync(email);
                if (user == null)
                {
                    return NotFound(ApiResponse<SoNoBrokersUser>.ErrorResult("User not found"));
                }
                return Ok(ApiResponse<SoNoBrokersUser>.SuccessResult(user));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by email {Email}", email);
                return StatusCode(500, ApiResponse<SoNoBrokersUser>.ErrorResult("Failed to get user"));
            }
        }



        /// <summary>
        /// Update user login status
        /// </summary>
        [HttpPatch("{id}/login-status")]
        public async Task<ActionResult<ApiResponse<object>>> UpdateLoginStatus(string id, [FromBody] bool loggedIn)
        {
            try
            {
                var success = await _userService.UpdateLoginStatusAsync(id, loggedIn);
                if (!success)
                {
                    return NotFound(ApiResponse<object>.ErrorResult("User not found"));
                }
                return Ok(ApiResponse<object>.SuccessResult(new { }, "Login status updated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating login status for user {UserId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResult("Failed to update login status"));
            }
        }

        /// <summary>
        /// Get current user profile (authenticated user)
        /// </summary>
        [HttpGet("profile")]
        public async Task<ActionResult<ApiResponse<SoNoBrokersUser>>> GetCurrentUserProfile()
        {
            try
            {
                // Get user ID from JWT token claims
                var userId = User.FindFirst("sub")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(ApiResponse<SoNoBrokersUser>.ErrorResult("User not authenticated"));
                }

                var user = await _userService.GetUserByClerkIdAsync(userId);
                if (user == null)
                {
                    return NotFound(ApiResponse<SoNoBrokersUser>.ErrorResult("User profile not found"));
                }

                return Ok(ApiResponse<SoNoBrokersUser>.SuccessResult(user));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current user profile");
                return StatusCode(500, ApiResponse<SoNoBrokersUser>.ErrorResult("Failed to get user profile"));
            }
        }

        /// <summary>
        /// Update current user profile
        /// </summary>
        [HttpPut("profile")]
        public async Task<ActionResult<ApiResponse<SoNoBrokersUser>>> UpdateCurrentUserProfile([FromBody] SoNoBrokersUser updateData)
        {
            try
            {
                // Get user ID from JWT token claims
                var userId = User.FindFirst("sub")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(ApiResponse<SoNoBrokersUser>.ErrorResult("User not authenticated"));
                }

                var existingUser = await _userService.GetUserByClerkIdAsync(userId);
                if (existingUser == null)
                {
                    return NotFound(ApiResponse<SoNoBrokersUser>.ErrorResult("User not found"));
                }

                // Update only allowed fields
                existingUser.FullName = updateData.FullName ?? existingUser.FullName;
                existingUser.FirstName = updateData.FirstName ?? existingUser.FirstName;
                existingUser.LastName = updateData.LastName ?? existingUser.LastName;
                existingUser.PhoneNumber = updateData.PhoneNumber ?? existingUser.PhoneNumber;
                existingUser.UpdatedAt = DateTime.UtcNow;

                var updatedUser = await _userService.UpdateUserAsync(existingUser);
                return Ok(ApiResponse<SoNoBrokersUser>.SuccessResult(updatedUser));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating current user profile");
                return StatusCode(500, ApiResponse<SoNoBrokersUser>.ErrorResult("Failed to update user profile"));
            }
        }

        /// <summary>
        /// Update current user type (Buyer/Seller)
        /// </summary>
        [HttpPut("user-type")]
        public async Task<ActionResult<ApiResponse<SoNoBrokersUser>>> UpdateCurrentUserType([FromBody] string userType)
        {
            try
            {
                // Get user ID from JWT token claims
                var userId = User.FindFirst("sub")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(ApiResponse<SoNoBrokersUser>.ErrorResult("User not authenticated"));
                }

                var existingUser = await _userService.GetUserByClerkIdAsync(userId);
                if (existingUser == null)
                {
                    return NotFound(ApiResponse<SoNoBrokersUser>.ErrorResult("User not found"));
                }

                // Validate and convert user type
                SnbUserType snbUserType;
                if (userType == "Buyer")
                {
                    snbUserType = SnbUserType.Buyer;
                }
                else if (userType == "Seller")
                {
                    snbUserType = SnbUserType.Seller;
                }
                else
                {
                    return BadRequest(ApiResponse<SoNoBrokersUser>.ErrorResult("Invalid user type. Must be 'Buyer' or 'Seller'"));
                }

                existingUser.UserType = snbUserType;
                existingUser.UpdatedAt = DateTime.UtcNow;

                var updatedUser = await _userService.UpdateUserAsync(existingUser);
                return Ok(ApiResponse<SoNoBrokersUser>.SuccessResult(updatedUser));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating current user type");
                return StatusCode(500, ApiResponse<SoNoBrokersUser>.ErrorResult("Failed to update user type"));
            }
        }

        /// <summary>
        /// Sync user with Clerk authentication
        /// </summary>
        [HttpPost("sync")]
        public async Task<ActionResult<ApiResponse<SoNoBrokersUser>>> SyncUserWithClerk()
        {
            try
            {
                // Get user data from JWT token claims
                var clerkUserId = User.FindFirst("sub")?.Value;
                var email = User.FindFirst("email")?.Value;
                var firstName = User.FindFirst("given_name")?.Value;
                var lastName = User.FindFirst("family_name")?.Value;
                var fullName = User.FindFirst("name")?.Value;

                if (string.IsNullOrEmpty(clerkUserId) || string.IsNullOrEmpty(email))
                {
                    return Unauthorized(ApiResponse<SoNoBrokersUser>.ErrorResult("Invalid authentication data"));
                }

                // Check if user already exists
                var existingUser = await _userService.GetUserByClerkIdAsync(clerkUserId);

                if (existingUser != null)
                {
                    // Update existing user with latest Clerk data
                    existingUser.Email = email;
                    existingUser.FirstName = firstName ?? existingUser.FirstName;
                    existingUser.LastName = lastName ?? existingUser.LastName;
                    existingUser.FullName = fullName ?? existingUser.FullName;
                    existingUser.LastLoginAt = DateTime.UtcNow;
                    existingUser.LoggedIn = true;
                    existingUser.UpdatedAt = DateTime.UtcNow;

                    var updatedUser = await _userService.UpdateUserAsync(existingUser);
                    return Ok(ApiResponse<SoNoBrokersUser>.SuccessResult(updatedUser));
                }
                else
                {
                    // Create new user
                    var newUser = new SoNoBrokersUser
                    {
                        Id = Guid.NewGuid().ToString(),
                        Email = email,
                        FirstName = firstName,
                        LastName = lastName,
                        FullName = fullName ?? $"{firstName} {lastName}".Trim(),
                        ClerkUserId = clerkUserId,
                        Role = MicroSaasWebApi.Models.SoNoBrokers.UserRole.USER, // Default role
                        UserType = SnbUserType.Buyer, // Default user type
                        IsActive = true,
                        LoggedIn = true,
                        LastLoginAt = DateTime.UtcNow,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    var createdUser = await _userService.CreateUserAsync(newUser);
                    return Ok(ApiResponse<SoNoBrokersUser>.SuccessResult(createdUser));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing user with Clerk");
                return StatusCode(500, ApiResponse<SoNoBrokersUser>.ErrorResult("Failed to sync user"));
            }
        }

        /// <summary>
        /// Update user role (admin only)
        /// </summary>
        [HttpPut("{id}/role")]
        public async Task<ActionResult<ApiResponse<SoNoBrokersUser>>> UpdateUserRole(string id, [FromBody] string role)
        {
            try
            {
                // Check if current user is admin
                var currentUserRole = User.FindFirst("role")?.Value;
                if (currentUserRole != "ADMIN")
                {
                    return Forbid("Admin access required");
                }

                var existingUser = await _userService.GetUserByIdAsync(id);
                if (existingUser == null)
                {
                    return NotFound(ApiResponse<SoNoBrokersUser>.ErrorResult("User not found"));
                }

                // Validate and convert role
                Models.SoNoBrokers.UserRole userRole;
                switch (role.ToUpper())
                {
                    case "USER":
                        userRole = Models.SoNoBrokers.UserRole.USER;
                        break;
                    case "ADMIN":
                        userRole = Models.SoNoBrokers.UserRole.ADMIN;
                        break;
                    case "PRODUCT":
                        userRole = Models.SoNoBrokers.UserRole.PRODUCT;
                        break;
                    case "OPERATOR":
                        userRole = Models.SoNoBrokers.UserRole.OPERATOR;
                        break;
                    default:
                        return BadRequest(ApiResponse<SoNoBrokersUser>.ErrorResult("Invalid role. Must be USER, ADMIN, PRODUCT, or OPERATOR"));
                }

                existingUser.Role = userRole;
                existingUser.UpdatedAt = DateTime.UtcNow;

                var updatedUser = await _userService.UpdateUserAsync(existingUser);
                return Ok(ApiResponse<SoNoBrokersUser>.SuccessResult(updatedUser));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user role for user {UserId}", id);
                return StatusCode(500, ApiResponse<SoNoBrokersUser>.ErrorResult("Failed to update user role"));
            }
        }

        /// <summary>
        /// Update user status (admin only)
        /// </summary>
        [HttpPut("{id}/status")]
        public async Task<ActionResult<ApiResponse<SoNoBrokersUser>>> UpdateUserStatus(string id, [FromBody] bool isActive)
        {
            try
            {
                // Check if current user is admin
                var currentUserRole = User.FindFirst("role")?.Value;
                if (currentUserRole != "ADMIN")
                {
                    return Forbid("Admin access required");
                }

                var existingUser = await _userService.GetUserByIdAsync(id);
                if (existingUser == null)
                {
                    return NotFound(ApiResponse<SoNoBrokersUser>.ErrorResult("User not found"));
                }

                existingUser.IsActive = isActive;
                existingUser.UpdatedAt = DateTime.UtcNow;

                var updatedUser = await _userService.UpdateUserAsync(existingUser);
                return Ok(ApiResponse<SoNoBrokersUser>.SuccessResult(updatedUser));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user status for user {UserId}", id);
                return StatusCode(500, ApiResponse<SoNoBrokersUser>.ErrorResult("Failed to update user status"));
            }
        }
    }
}
