using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.Core;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using SoNoBrokersUser = MicroSaasWebApi.Models.SoNoBrokers.User;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/sonobrokers/users")]
    [Tags("Users")]
    public class UsersController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly ILogger<UsersController> _logger;

        public UsersController(IUserService userService, ILogger<UsersController> logger)
        {
            _userService = userService;
            _logger = logger;
        }

        /// <summary>
        /// Get all users
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ApiResponse<IEnumerable<SoNoBrokersUser>>>> GetUsers()
        {
            try
            {
                var users = await _userService.GetAllUsersAsync();
                return Ok(ApiResponse<IEnumerable<SoNoBrokersUser>>.SuccessResult(users, "Users retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving users");
                return StatusCode(500, ApiResponse<IEnumerable<SoNoBrokersUser>>.ErrorResult("Failed to retrieve users"));
            }
        }

        /// <summary>
        /// Get user by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<ApiResponse<SoNoBrokersUser>>> GetUser(string id)
        {
            try
            {
                var user = await _userService.GetUserByIdAsync(id);
                if (user == null)
                {
                    return NotFound(ApiResponse<SoNoBrokersUser>.ErrorResult("User not found"));
                }

                return Ok(ApiResponse<SoNoBrokersUser>.SuccessResult(user, "User retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user {UserId}", id);
                return StatusCode(500, ApiResponse<SoNoBrokersUser>.ErrorResult("Failed to retrieve user"));
            }
        }

        /// <summary>
        /// Create a new user
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<ApiResponse<SoNoBrokersUser>>> CreateUser(SoNoBrokersUser user)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse<SoNoBrokersUser>.ErrorResult("Invalid user data"));
                }

                var createdUser = await _userService.CreateUserAsync(user);

                return CreatedAtAction(nameof(GetUser), new { id = createdUser.Id },
                    ApiResponse<SoNoBrokersUser>.SuccessResult(createdUser, "User created successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user");
                return StatusCode(500, ApiResponse<SoNoBrokersUser>.ErrorResult("Failed to create user"));
            }
        }

        /// <summary>
        /// Update user
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<ApiResponse<SoNoBrokersUser>>> UpdateUser(string id, SoNoBrokersUser user)
        {
            try
            {
                if (id != user.Id)
                {
                    return BadRequest(ApiResponse<SoNoBrokersUser>.ErrorResult("User ID mismatch"));
                }

                user.Id = id; // Ensure ID is set
                var updatedUser = await _userService.UpdateUserAsync(user);

                if (updatedUser == null)
                {
                    return NotFound(ApiResponse<SoNoBrokersUser>.ErrorResult("User not found"));
                }

                return Ok(ApiResponse<SoNoBrokersUser>.SuccessResult(updatedUser, "User updated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {UserId}", id);
                return StatusCode(500, ApiResponse<SoNoBrokersUser>.ErrorResult("Failed to update user"));
            }
        }

        /// <summary>
        /// Delete user
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult<ApiResponse<object>>> DeleteUser(string id)
        {
            try
            {
                var deleted = await _userService.DeleteUserAsync(id);

                if (!deleted)
                {
                    return NotFound(ApiResponse<object>.ErrorResult("User not found"));
                }

                return Ok(ApiResponse<object>.SuccessResult(new { }, "User deleted successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user {UserId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResult("Failed to delete user"));
            }
        }



        /// <summary>
        /// Get user by email
        /// </summary>
        [HttpGet("by-email/{email}")]
        public async Task<ActionResult<ApiResponse<SoNoBrokersUser>>> GetUserByEmail(string email)
        {
            try
            {
                var user = await _userService.GetUserByEmailAsync(email);
                if (user == null)
                {
                    return NotFound(ApiResponse<SoNoBrokersUser>.ErrorResult("User not found"));
                }
                return Ok(ApiResponse<SoNoBrokersUser>.SuccessResult(user));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by email {Email}", email);
                return StatusCode(500, ApiResponse<SoNoBrokersUser>.ErrorResult("Failed to get user"));
            }
        }



        /// <summary>
        /// Update user login status
        /// </summary>
        [HttpPatch("{id}/login-status")]
        public async Task<ActionResult<ApiResponse<object>>> UpdateLoginStatus(string id, [FromBody] bool loggedIn)
        {
            try
            {
                var success = await _userService.UpdateLoginStatusAsync(id, loggedIn);
                if (!success)
                {
                    return NotFound(ApiResponse<object>.ErrorResult("User not found"));
                }
                return Ok(ApiResponse<object>.SuccessResult(new { }, "Login status updated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating login status for user {UserId}", id);
                return StatusCode(500, ApiResponse<object>.ErrorResult("Failed to update login status"));
            }
        }
    }
}
