using System.ComponentModel.DataAnnotations;

namespace MicroSaasWebApi.Models.SoNoBrokers
{
    // AI Property Import DTOs
    public class AIPropertyImportRequest
    {
        [Required]
        public string Address { get; set; } = string.Empty;
    }

    public class AIPropertyImportResponse
    {
        public PropertyDetails PropertyDetails { get; set; } = new();
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }

    public class PropertyDetails
    {
        public string Address { get; set; } = string.Empty;
        public string PropertyType { get; set; } = string.Empty;
        public int Bedrooms { get; set; }
        public decimal Bathrooms { get; set; }
        public int SquareFeet { get; set; }
        public int YearBuilt { get; set; }
        public decimal LotSize { get; set; }
        public string LotSizeUnit { get; set; } = string.Empty;
        public string[] Features { get; set; } = Array.Empty<string>();
        public string[] Amenities { get; set; } = Array.Empty<string>();
        public string Description { get; set; } = string.Empty;
    }

    // AI Property Valuation DTOs
    public class AIPropertyValuationRequest
    {
        [Required]
        public string Address { get; set; } = string.Empty;

        [Required]
        public string Country { get; set; } = string.Empty;
    }

    public class AIPropertyValuationResponse
    {
        public PropertyValuation Valuation { get; set; } = new();
        public PropertyInfo PropertyInfo { get; set; } = new();
        public MarketAnalysis MarketAnalysis { get; set; } = new();
        public ComparableProperties[] ComparableProperties { get; set; } = Array.Empty<ComparableProperties>();
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }

    public class PropertyValuation
    {
        public decimal EstimatedValue { get; set; }
        public decimal LowEstimate { get; set; }
        public decimal HighEstimate { get; set; }
        public string Currency { get; set; } = string.Empty;
        public decimal PricePerSquareFoot { get; set; }
        public string ValuationDate { get; set; } = string.Empty;
        public string ConfidenceLevel { get; set; } = string.Empty;
    }

    public class PropertyInfo
    {
        public string Address { get; set; } = string.Empty;
        public string PropertyType { get; set; } = string.Empty;
        public int Bedrooms { get; set; }
        public decimal Bathrooms { get; set; }
        public int SquareFeet { get; set; }
        public int YearBuilt { get; set; }
        public decimal LotSize { get; set; }
        public string LotSizeUnit { get; set; } = string.Empty;
        public string Condition { get; set; } = string.Empty;
        public string[] Features { get; set; } = Array.Empty<string>();
        public string Neighborhood { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string Province { get; set; } = string.Empty;
        public string PostalCode { get; set; } = string.Empty;
    }

    public class MarketAnalysis
    {
        public decimal MedianPrice { get; set; }
        public decimal AveragePrice { get; set; }
        public int DaysOnMarket { get; set; }
        public decimal PricePerSquareFoot { get; set; }
        public string MarketTrend { get; set; } = string.Empty;
        public decimal YearOverYearChange { get; set; }
        public int ActiveListings { get; set; }
        public int SoldLastMonth { get; set; }
        public string MarketCondition { get; set; } = string.Empty;
    }

    public class ComparableProperties
    {
        public string Address { get; set; } = string.Empty;
        public decimal SoldPrice { get; set; }
        public string SoldDate { get; set; } = string.Empty;
        public int Bedrooms { get; set; }
        public decimal Bathrooms { get; set; }
        public int SquareFeet { get; set; }
        public decimal Distance { get; set; }
        public string DistanceUnit { get; set; } = string.Empty;
        public decimal PricePerSquareFoot { get; set; }
        public int DaysOnMarket { get; set; }
    }

    // AI Description Generation DTOs
    public class GenerateDescriptionRequest
    {
        [Required]
        public string Prompt { get; set; } = string.Empty;

        [Required]
        public string UserId { get; set; } = string.Empty;
    }

    public class GenerateDescriptionResponse
    {
        public string Description { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }
}
