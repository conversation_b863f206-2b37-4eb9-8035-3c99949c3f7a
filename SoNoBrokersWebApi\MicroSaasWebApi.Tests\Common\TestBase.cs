using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Common
{
    public abstract class TestBase : IDisposable
    {
        protected readonly ITestOutputHelper Output;
        protected readonly IConfiguration Configuration;
        protected readonly IServiceProvider ServiceProvider;
        protected readonly Mock<ILogger> MockLogger;

        protected TestBase(ITestOutputHelper output)
        {
            Output = output;
            Configuration = BuildConfiguration();
            MockLogger = new Mock<ILogger>();
            ServiceProvider = BuildServiceProvider();
        }

        private IConfiguration BuildConfiguration()
        {
            return new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.Test.json", optional: false)
                .AddEnvironmentVariables()
                .Build();
        }

        protected virtual IServiceProvider BuildServiceProvider()
        {
            var services = new ServiceCollection();
            
            // Add configuration
            services.AddSingleton(Configuration);
            
            // Add logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // Add test-specific services
            ConfigureTestServices(services);

            return services.BuildServiceProvider();
        }

        protected virtual void ConfigureTestServices(IServiceCollection services)
        {
            // Override in derived classes to add specific test services
        }

        protected T GetService<T>() where T : notnull
        {
            return ServiceProvider.GetRequiredService<T>();
        }

        protected T? GetOptionalService<T>() where T : class
        {
            return ServiceProvider.GetService<T>();
        }

        public virtual void Dispose()
        {
            if (ServiceProvider is IDisposable disposable)
            {
                disposable.Dispose();
            }
            GC.SuppressFinalize(this);
        }
    }
}
