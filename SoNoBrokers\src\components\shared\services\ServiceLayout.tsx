import { ServiceLayoutClient } from './ServiceLayoutClient'

export interface ServiceProvider {
  id: string
  name: string
  businessName: string
  serviceType: string
  location: string
  distance: string
  rating: number
  reviewCount: number
  price: string
  specialties: string[]
  verified: boolean
  isAdvertiser: boolean
  isPremium: boolean
  image?: string
  phone?: string
  email?: string
  website?: string
  description?: string
  coordinates: {
    lat: number
    lng: number
  }
  advertiser?: {
    plan: 'basic' | 'premium' | 'enterprise'
    status: 'pending' | 'active' | 'suspended' | 'cancelled'
    featuredUntil?: Date
    isPremium: boolean
  }
}

export interface ServiceLayoutProps {
  userType: 'buyer' | 'seller'
  isSignedIn: boolean
  serviceTitle: string
  serviceDescription: string
  country: string
  providers: ServiceProvider[]
  showGoogleProviders?: boolean
}

export function ServiceLayout(props: ServiceLayoutProps) {
  return <ServiceLayoutClient {...props} />
}
