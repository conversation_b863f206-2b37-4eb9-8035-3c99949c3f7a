'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Search, MapPin, Filter, DollarSign, Home, Bed, Eye } from 'lucide-react'
import { useSearchOptional } from '@/contexts/SearchContext'

interface PropertySearchProps {
  userType: 'buyer' | 'seller'
  onViewProperties?: () => void
}

export function PropertySearch({ userType, onViewProperties }: PropertySearchProps) {
  const searchContext = useSearchOptional()
  const [searchQuery, setSearchQuery] = useState('')
  const [priceRange, setPriceRange] = useState('')
  const [propertyType, setPropertyType] = useState('')
  const [bedrooms, setBedrooms] = useState('')

  const handleSearch = () => {
    // Parse price range
    const [priceMin, priceMax] = priceRange ? priceRange.split('-') : ['', '']

    // Update search context with current form values if available
    if (searchContext?.setSearchParams) {
      searchContext.setSearchParams({
        searchQuery,
        propertyType,
        priceMin: priceMin || '',
        priceMax: priceMax || '',
        beds: bedrooms
      })
    }

    // Navigate to PropertySearchPage
    onViewProperties?.()
  }

  if (userType === 'seller') {
    return (
      <section className="py-16 bg-accent/5">
        <div className="container mx-auto px-4">
          <Card className="max-w-4xl mx-auto bg-background border border-border shadow-lg">
            <CardHeader className="text-center">
              <CardTitle className="text-3xl font-bold text-foreground">
                List Your Property
              </CardTitle>
              <p className="text-muted-foreground">
                Get your property in front of thousands of potential buyers
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center p-6 rounded-lg bg-accent/10 border border-accent/20">
                  <DollarSign className="h-12 w-12 text-accent mx-auto mb-4" />
                  <h3 className="font-semibold text-foreground mb-2">No Commission</h3>
                  <p className="text-sm text-muted-foreground">Keep 100% of your sale price</p>
                </div>
                <div className="text-center p-6 rounded-lg bg-accent/10 border border-accent/20">
                  <Home className="h-12 w-12 text-accent mx-auto mb-4" />
                  <h3 className="font-semibold text-foreground mb-2">Professional Photos</h3>
                  <p className="text-sm text-muted-foreground">High-quality listing photography</p>
                </div>
                <div className="text-center p-6 rounded-lg bg-accent/10 border border-accent/20">
                  <MapPin className="h-12 w-12 text-accent mx-auto mb-4" />
                  <h3 className="font-semibold text-foreground mb-2">Prime Exposure</h3>
                  <p className="text-sm text-muted-foreground">Featured on our marketplace</p>
                </div>
              </div>

              <div className="text-center space-y-4">
                <Button size="lg" className="text-lg px-8">
                  Start Listing Process
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="text-lg px-8 ml-4"
                  onClick={onViewProperties}
                >
                  <Eye className="mr-2 h-5 w-5" />
                  View Properties
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    )
  }

  return (
    <section className="py-16 bg-accent/5">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-foreground mb-4">
            Find Your Perfect Property
          </h2>
          <p className="text-lg text-muted-foreground">
            Search thousands of commission-free listings across Canada
          </p>
        </div>

        <Card className="max-w-6xl mx-auto bg-background border border-border shadow-lg">
          <CardContent className="p-8">
            <div className="space-y-6">
              {/* Search Bar */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <Input
                  placeholder="Enter city, neighborhood, or postal code..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 h-12 text-lg"
                />
              </div>

              {/* Filters */}
              <div className="grid md:grid-cols-4 gap-4">
                <Select value={priceRange} onValueChange={setPriceRange}>
                  <SelectTrigger className="h-12">
                    <SelectValue placeholder="Price Range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0-300000">Under $300K</SelectItem>
                    <SelectItem value="300000-500000">$300K - $500K</SelectItem>
                    <SelectItem value="500000-750000">$500K - $750K</SelectItem>
                    <SelectItem value="750000-1000000">$750K - $1M</SelectItem>
                    <SelectItem value="1000000+">$1M+</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={propertyType} onValueChange={setPropertyType}>
                  <SelectTrigger className="h-12">
                    <SelectValue placeholder="Property Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="house">House</SelectItem>
                    <SelectItem value="condo">Condo</SelectItem>
                    <SelectItem value="townhouse">Townhouse</SelectItem>
                    <SelectItem value="apartment">Apartment</SelectItem>
                    <SelectItem value="land">Land</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={bedrooms} onValueChange={setBedrooms}>
                  <SelectTrigger className="h-12">
                    <SelectValue placeholder="Bedrooms" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1+ Bedroom</SelectItem>
                    <SelectItem value="2">2+ Bedrooms</SelectItem>
                    <SelectItem value="3">3+ Bedrooms</SelectItem>
                    <SelectItem value="4">4+ Bedrooms</SelectItem>
                    <SelectItem value="5">5+ Bedrooms</SelectItem>
                  </SelectContent>
                </Select>

                <Button size="lg" className="h-12 text-lg" onClick={handleSearch}>
                  <Search className="mr-2 h-5 w-5" />
                  Search Properties
                </Button>
              </div>

              {/* Popular Searches */}
              <div className="space-y-3">
                <p className="text-sm font-medium text-muted-foreground">Popular Searches:</p>
                <div className="flex flex-wrap gap-2">
                  {[
                    'Toronto Condos',
                    'Vancouver Houses',
                    'Calgary Townhouses',
                    'Montreal Apartments',
                    'Ottawa Properties',
                    'Edmonton Homes'
                  ].map((search) => (
                    <Badge
                      key={search}
                      variant="outline"
                      className="cursor-pointer hover:bg-accent hover:text-accent-foreground transition-colors"
                    >
                      {search}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  )
}
