using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.App.Repositories.Interfaces;

namespace MicroSaasWebApi.App.Repositories
{
    /// <summary>
    /// Advertiser repository implementation using Dapper
    /// </summary>
    public class AdvertiserRepository : IAdvertiserRepository
    {
        private readonly IDapperDbContext _dbContext;
        private readonly ILogger<AdvertiserRepository> _logger;

        public AdvertiserRepository(IDapperDbContext dbContext, ILogger<AdvertiserRepository> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<IEnumerable<Advertiser>> GetAllAsync()
        {
            try
            {
                const string sql = @"
                    SELECT a.*, u.""fullName"" as user_name, u.email as user_email
                    FROM public.""Advertiser"" a
                    LEFT JOIN public.""User"" u ON a.""userId"" = u.id
                    ORDER BY a.""createdAt"" DESC";

                return await _dbContext.QueryAsync<Advertiser>(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all advertisers");
                throw;
            }
        }

        public async Task<Advertiser?> GetByIdAsync(string id)
        {
            try
            {
                const string sql = @"
                    SELECT a.*, u.""fullName"" as user_name, u.email as user_email
                    FROM public.""Advertiser"" a
                    LEFT JOIN public.""User"" u ON a.""userId"" = u.id
                    WHERE a.id = @Id";

                return await _dbContext.QueryFirstOrDefaultAsync<Advertiser>(sql, new { Id = id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving advertiser by ID: {Id}", id);
                throw;
            }
        }

        public async Task<Advertiser?> GetByUserIdAsync(string userId)
        {
            try
            {
                const string sql = @"
                    SELECT a.*, u.""fullName"" as user_name, u.email as user_email
                    FROM public.""Advertiser"" a
                    LEFT JOIN public.""User"" u ON a.""userId"" = u.id
                    WHERE a.""userId"" = @UserId";

                return await _dbContext.QueryFirstOrDefaultAsync<Advertiser>(sql, new { UserId = userId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving advertiser by user ID: {UserId}", userId);
                throw;
            }
        }

        public async Task<string> CreateAsync(Advertiser advertiser)
        {
            try
            {
                advertiser.Id = Guid.NewGuid().ToString();
                advertiser.CreatedAt = DateTime.UtcNow;
                advertiser.UpdatedAt = DateTime.UtcNow;

                const string sql = @"
                    INSERT INTO public.""Advertiser"" (
                        id, ""userId"", ""businessName"", ""contactName"", email, phone, website,
                        description, ""serviceType"", ""serviceAreas"", ""licenseNumber"", plan,
                        status, ""isPremium"", ""isVerified"", images, metadata, ""createdAt"", ""updatedAt""
                    ) VALUES (
                        @Id, @UserId, @BusinessName, @ContactName, @Email, @Phone, @Website,
                        @Description, @ServiceType, @ServiceAreas, @LicenseNumber, @Plan,
                        @Status, @IsPremium, @IsVerified, @Images, @Metadata, @CreatedAt, @UpdatedAt
                    )";

                await _dbContext.ExecuteAsync(sql, advertiser);
                return advertiser.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating advertiser");
                throw;
            }
        }

        public async Task<bool> UpdateAsync(Advertiser advertiser)
        {
            try
            {
                advertiser.UpdatedAt = DateTime.UtcNow;

                const string sql = @"
                    UPDATE public.""Advertiser"" SET
                        ""businessName"" = @BusinessName,
                        ""contactName"" = @ContactName,
                        email = @Email,
                        phone = @Phone,
                        website = @Website,
                        description = @Description,
                        ""serviceType"" = @ServiceType,
                        ""serviceAreas"" = @ServiceAreas,
                        ""licenseNumber"" = @LicenseNumber,
                        plan = @Plan,
                        status = @Status,
                        ""isPremium"" = @IsPremium,
                        ""isVerified"" = @IsVerified,
                        images = @Images,
                        metadata = @Metadata,
                        ""updatedAt"" = @UpdatedAt
                    WHERE id = @Id";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, advertiser);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating advertiser: {Id}", advertiser.Id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(string id)
        {
            try
            {
                const string sql = @"DELETE FROM public.""Advertiser"" WHERE id = @Id";
                var rowsAffected = await _dbContext.ExecuteAsync(sql, new { Id = id });
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting advertiser: {Id}", id);
                throw;
            }
        }

        public async Task<IEnumerable<Advertiser>> GetByServiceTypeAsync(ServiceType serviceType)
        {
            try
            {
                const string sql = @"
                    SELECT a.*, u.""fullName"" as user_name, u.email as user_email
                    FROM public.""Advertiser"" a
                    LEFT JOIN public.""User"" u ON a.""userId"" = u.id
                    WHERE a.""serviceType"" = @ServiceType
                    ORDER BY a.""createdAt"" DESC";

                return await _dbContext.QueryAsync<Advertiser>(sql, new { ServiceType = serviceType });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving advertisers by service type: {ServiceType}", serviceType);
                throw;
            }
        }

        public async Task<IEnumerable<Advertiser>> GetByServiceAreaAsync(string serviceArea)
        {
            try
            {
                const string sql = @"
                    SELECT a.*, u.""fullName"" as user_name, u.email as user_email
                    FROM public.""Advertiser"" a
                    LEFT JOIN public.""User"" u ON a.""userId"" = u.id
                    WHERE @ServiceArea = ANY(a.""serviceAreas"")
                    ORDER BY a.""createdAt"" DESC";

                return await _dbContext.QueryAsync<Advertiser>(sql, new { ServiceArea = serviceArea });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving advertisers by service area: {ServiceArea}", serviceArea);
                throw;
            }
        }

        public async Task<IEnumerable<Advertiser>> GetVerifiedAdvertisersAsync()
        {
            try
            {
                const string sql = @"
                    SELECT a.*, u.""fullName"" as user_name, u.email as user_email
                    FROM public.""Advertiser"" a
                    LEFT JOIN public.""User"" u ON a.""userId"" = u.id
                    WHERE a.""isVerified"" = true
                    ORDER BY a.""createdAt"" DESC";

                return await _dbContext.QueryAsync<Advertiser>(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving verified advertisers");
                throw;
            }
        }

        public async Task<IEnumerable<Advertiser>> GetPremiumAdvertisersAsync()
        {
            try
            {
                const string sql = @"
                    SELECT a.*, u.""fullName"" as user_name, u.email as user_email
                    FROM public.""Advertiser"" a
                    LEFT JOIN public.""User"" u ON a.""userId"" = u.id
                    WHERE a.""isPremium"" = true
                    ORDER BY a.""createdAt"" DESC";

                return await _dbContext.QueryAsync<Advertiser>(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving premium advertisers");
                throw;
            }
        }

        public async Task<bool> UpdateVerificationStatusAsync(string id, bool isVerified)
        {
            try
            {
                const string sql = @"
                    UPDATE public.""Advertiser"" SET
                        ""isVerified"" = @IsVerified,
                        ""updatedAt"" = @UpdatedAt
                    WHERE id = @Id";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, new
                {
                    Id = id,
                    IsVerified = isVerified,
                    UpdatedAt = DateTime.UtcNow
                });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating verification status for advertiser: {Id}", id);
                throw;
            }
        }

        public async Task<bool> UpdatePremiumStatusAsync(string id, bool isPremium)
        {
            try
            {
                const string sql = @"
                    UPDATE public.""Advertiser"" SET
                        ""isPremium"" = @IsPremium,
                        ""updatedAt"" = @UpdatedAt
                    WHERE id = @Id";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, new
                {
                    Id = id,
                    IsPremium = isPremium,
                    UpdatedAt = DateTime.UtcNow
                });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating premium status for advertiser: {Id}", id);
                throw;
            }
        }

        public async Task<(IEnumerable<Advertiser> Advertisers, int TotalCount)> SearchAsync(
            string? searchTerm = null,
            ServiceType? serviceType = null,
            string? serviceArea = null,
            AdvertiserStatus? status = null,
            bool? isVerified = null,
            bool? isPremium = null,
            int page = 1,
            int pageSize = 20)
        {
            try
            {
                var whereConditions = new List<string>();
                var parameters = new Dictionary<string, object>();

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    whereConditions.Add(@"(a.""businessName"" ILIKE @SearchTerm OR a.""contactName"" ILIKE @SearchTerm OR a.email ILIKE @SearchTerm)");
                    parameters.Add("SearchTerm", $"%{searchTerm}%");
                }

                if (serviceType.HasValue)
                {
                    whereConditions.Add(@"a.""serviceType"" = @ServiceType");
                    parameters.Add("ServiceType", serviceType.Value);
                }

                if (!string.IsNullOrEmpty(serviceArea))
                {
                    whereConditions.Add(@"@ServiceArea = ANY(a.""serviceAreas"")");
                    parameters.Add("ServiceArea", serviceArea);
                }

                if (status.HasValue)
                {
                    whereConditions.Add("a.status = @Status");
                    parameters.Add("Status", status.Value);
                }

                if (isVerified.HasValue)
                {
                    whereConditions.Add(@"a.""isVerified"" = @IsVerified");
                    parameters.Add("IsVerified", isVerified.Value);
                }

                if (isPremium.HasValue)
                {
                    whereConditions.Add(@"a.""isPremium"" = @IsPremium");
                    parameters.Add("IsPremium", isPremium.Value);
                }

                var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";
                var offset = (page - 1) * pageSize;

                parameters.Add("Offset", offset);
                parameters.Add("PageSize", pageSize);

                // Get total count
                var countSql = $@"
                    SELECT COUNT(*) FROM public.""Advertiser"" a
                    {whereClause}";

                var totalCount = await _dbContext.QuerySingleAsync<int>(countSql, parameters);

                // Get paginated results
                var dataSql = $@"
                    SELECT a.*, u.""fullName"" as user_name, u.email as user_email
                    FROM public.""Advertiser"" a
                    LEFT JOIN public.""User"" u ON a.""userId"" = u.id
                    {whereClause}
                    ORDER BY a.""createdAt"" DESC
                    LIMIT @PageSize OFFSET @Offset";

                var advertisers = await _dbContext.QueryAsync<Advertiser>(dataSql, parameters);

                return (advertisers, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching advertisers");
                throw;
            }
        }

        public async Task<int> GetCountByStatusAsync(AdvertiserStatus status)
        {
            try
            {
                const string sql = @"
                    SELECT COUNT(*) FROM public.""Advertiser""
                    WHERE status = @Status";

                return await _dbContext.QuerySingleAsync<int>(sql, new { Status = status });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting advertiser count by status: {Status}", status);
                throw;
            }
        }

        public async Task<int> GetVerifiedCountAsync()
        {
            try
            {
                const string sql = @"
                    SELECT COUNT(*) FROM public.""Advertiser""
                    WHERE ""isVerified"" = true";

                return await _dbContext.QuerySingleAsync<int>(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting verified advertisers count");
                throw;
            }
        }

        public async Task<int> GetPremiumCountAsync()
        {
            try
            {
                const string sql = @"
                    SELECT COUNT(*) FROM public.""Advertiser""
                    WHERE ""isPremium"" = true";

                return await _dbContext.QuerySingleAsync<int>(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting premium advertisers count");
                throw;
            }
        }
    }
}
