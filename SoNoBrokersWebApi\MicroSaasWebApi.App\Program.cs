using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.ResponseCompression;
// .NET 9 native OpenAPI + Scalar imports
using Microsoft.OpenApi.Models;
using MicroSaasWebApi.Filters;
using MicroSaasWebApi.Health;
using MicroSaasWebApi.Middlewares;
using MicroSaasWebApi.Models;
// Legacy services - temporarily commented for build fix
// using MicroSaasWebApi.Services.Auth.Base;
// using MicroSaasWebApi.Services.Auth.Base.Interface;
// using MicroSaasWebApi.Services.Base;
// using MicroSaasWebApi.Services.Base.Interface;
using System.IO.Compression;
using System.Reflection;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System.Security.Authentication;
using System.Net.Security;
using Scalar.AspNetCore;
using Azure.Identity;
using Microsoft.Extensions.Configuration.AzureAppConfiguration;
using Azure.Security.KeyVault.Secrets;
using MicroSaasWebApi.Services.Auth.Clerk;
using MicroSaasWebApi.Services.Auth.Clerk.Interface;
using System.Net.Http;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using MicroSaasWebApi.Services.Auth.Interfaces;
using MicroSaasWebApi.Services.Auth;
using MicroSaasWebApi.Services.Logging.Interfaces;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Services.Core;
using MicroSaasWebApi.Services.SoNoBrokers;

// MicroSaaS specific imports
using MicroSaasWebApi.Services.Configuration;
using MicroSaasWebApi.Services.Payment;
using MicroSaasWebApi.Services.Payment.Interfaces;
using MicroSaasWebApi.Hubs;
using AspNetCoreRateLimit;
using Serilog;
using Hangfire;
using Hangfire.PostgreSql;
using MicroSaasWebApi.Middleware;

// Load environment variables from .env file
EnvironmentConfigurationService.LoadEnvironmentVariables();

var builder = WebApplication.CreateBuilder(args);

// Add environment variables to configuration after loading .env file
builder.Configuration.AddEnvironmentVariables();

// Initialize simplified app settings
var appSettings = new AppSettings();

// Validate required settings
var missingSettings = appSettings.ValidateRequiredSettings();
if (missingSettings.Any())
{
    Console.WriteLine($"Missing required environment variables: {string.Join(", ", missingSettings)}");
    Console.WriteLine("Please check your .env file and ensure all required variables are set.");
}

var certPath = builder.Configuration["CertPath"];
var certKey = builder.Configuration["CertificateKey"];
var targetUrl = builder.Configuration["TargetUrl"];

// Configure Services
ConfigureServices(builder.Services, builder.Configuration, builder.Environment);

// Configure Kestrel
ConfigureKestrel(builder.WebHost);

var app = builder.Build();

// Configure health check middleware and endpoints
app.UseHealthCheckMiddleware();

// Configure middleware pipeline with proper ordering
app.ConfigureMiddlewarePipeline();

app.Run();

void ConfigureServices(IServiceCollection services, IConfiguration configuration, IWebHostEnvironment env)
{
    // Optional: Configure Azure App Configuration & KeyVault if environment variables are set
    if (!string.IsNullOrEmpty(appSettings.AppConfigurationEndpoint) || !string.IsNullOrEmpty(appSettings.KeyVaultUri))
    {
        ConfigureAzureAppConfigurationAndKeyVault();
    }

    // Http Client, Controllers, Health Checks, HttpContextAccessor, etc.
    services.AddHttpClient("MyClient", client =>
    {
        client.Timeout = TimeSpan.FromMinutes(1);
    })
    .ConfigurePrimaryHttpMessageHandler(() =>
    {
        var httpClientHandler = new HttpClientHandler
        {
            SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls13
        };
        httpClientHandler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) =>
        {
            return errors == SslPolicyErrors.None;
        };
        return httpClientHandler;
    });

    services.AddControllers().AddNewtonsoftJson(options =>
        options.SerializerSettings.ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver());

    services.ConfigureApplicationCookie(options =>
    {
        options.Cookie.HttpOnly = true;
        options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
        options.Cookie.SameSite = SameSiteMode.Strict;
    });

    // Add Clerk JWT Authentication
    services.AddAuthentication(options =>
    {
        options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
        options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    })
    .AddJwtBearer(options =>
    {
        options.Authority = $"https://{configuration["Clerk:Domain"]}";
        options.Audience = configuration["Clerk:Audience"];
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = $"https://{configuration["Clerk:Domain"]}",
            ValidAudience = configuration["Clerk:Audience"],
            IssuerSigningKeyResolver = (token, securityToken, kid, parameters) =>
            {
                var jwksUri = $"{options.Authority}/.well-known/jwks.json";
                var json = new HttpClient().GetStringAsync(jwksUri).Result;
                var keys = new JsonWebKeySet(json).Keys;
                return keys;
            }
        };
    });

    // Add role-based authorization policies
    services.AddAuthorization(options =>
    {
        options.AddPolicy("AdminOnly", policy => policy.RequireRole("Admin"));
        options.AddPolicy("BuyerOnly", policy => policy.RequireRole("Buyer"));
        options.AddPolicy("SellerOnly", policy => policy.RequireRole("Seller"));
        options.AddPolicy("BuyerOrSeller", policy => policy.RequireRole("Buyer", "Seller"));
        options.AddPolicy("AdminOrSeller", policy => policy.RequireRole("Admin", "Seller"));
    });

    // Configure comprehensive health checks for .NET 9
    services.ConfigureHealthChecks(builder.Configuration);

    services.AddHttpContextAccessor();
    AddResponseCompression(services);
    RegisterManualServices(services);
    services.AddLogging();

    // Database contexts - Entity Framework commented out, using IDapperDbContext instead
    // services.AddDbContext<MicroSaasDbContext>(options =>
    //     options.UseNpgsql(appSettings.GetConnectionString()));

    // Add Dapper-based database migrations and context for SoNoBrokers
    services.AddDatabaseMigrations();

    // Register Unit of Work and repositories - commented out for clean build
    // services.AddScoped<MicroSaasWebApi.Data.Interfaces.IUnitOfWork, MicroSaasWebApi.Data.UnitOfWork>();
    // services.AddScoped<MicroSaasWebApi.Services.Core.Interfaces.IUserService, MicroSaasWebApi.Services.Core.UserService>();
    // services.AddScoped<MicroSaasWebApi.Services.Core.Interfaces.IProductService, MicroSaasWebApi.Services.Core.ProductService>();
    // services.AddScoped<MicroSaasWebApi.Services.Core.Interfaces.IDocumentService, MicroSaasWebApi.Services.Core.DocumentService>();

    // Register only SoNoBrokers DapperDbContext (consolidated implementation)
    services.AddScoped<MicroSaasWebApi.App.Context.IDapperDbContext, MicroSaasWebApi.App.Context.DapperDbContext>();
    services.AddScoped<MicroSaasWebApi.App.Repositories.Interfaces.IPropertyRepository, MicroSaasWebApi.App.Repositories.PropertyRepository>();

    // Register SoNoBrokers services with aliases to avoid conflicts
    services.AddScoped<MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IUserService, MicroSaasWebApi.Services.SoNoBrokers.UserService>();
    services.AddScoped<MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IPropertyService, MicroSaasWebApi.Services.SoNoBrokers.PropertyService>();
    services.AddScoped<MicroSaasWebApi.Services.SoNoBrokers.Interfaces.IPropertyImageService, MicroSaasWebApi.Services.SoNoBrokers.PropertyImageService>();
    services.AddScoped<MicroSaasWebApi.Services.SoNoBrokers.Interfaces.ISubscriptionService, MicroSaasWebApi.Services.SoNoBrokers.SubscriptionService>();

    // Add SignalR
    services.AddSignalR();

    // Add rate limiting
    services.AddMemoryCache();
    services.Configure<IpRateLimitOptions>(configuration.GetSection("IpRateLimiting"));
    services.Configure<IpRateLimitPolicies>(configuration.GetSection("IpRateLimitPolicies"));
    services.AddInMemoryRateLimiting();
    services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();

    // Add Hangfire for background jobs - temporarily commented out for build fix
    // services.AddHangfire(config => config
    //     .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
    //     .UseSimpleAssemblyNameTypeSerializer()
    //     .UseRecommendedSerializerSettings()
    //     .UseNpgsqlStorage(appSettings.GetConnectionString()));

    // services.AddHangfireServer();

    RegisterAppSettings(services, appSettings);
    services.AddDirectoryBrowser();
    AddCorsPolicy(services);

    // Add .NET 9 native OpenAPI support with SoNoBrokers customization
    if (!env.IsProduction())
    {
        services.AddOpenApi(options =>
        {
            options.AddDocumentTransformer((document, context, cancellationToken) =>
            {
                // SoNoBrokers API Information
                document.Info.Version = "1.0.0";
                document.Info.Title = "SoNoBrokers API";
                document.Info.Description = "Real Estate Platform API - Connecting buyers and sellers directly without brokers. " +
                                          "This API provides comprehensive endpoints for property listings, user management, " +
                                          "search functionality, and secure transactions.";
                document.Info.TermsOfService = new Uri("https://sonobrokers.com/terms");
                document.Info.Contact = new OpenApiContact
                {
                    Name = "SoNoBrokers Support Team",
                    Email = "<EMAIL>",
                    Url = new Uri("https://sonobrokers.com/contact")
                };
                document.Info.License = new OpenApiLicense
                {
                    Name = "Proprietary License",
                    Url = new Uri("https://sonobrokers.com/license")
                };

                // Add server configurations with proper subdomain structure
                document.Servers.Clear();
                document.Servers.Add(new OpenApiServer
                {
                    Url = "https://api.sonobrokers.com",
                    Description = "SoNoBrokers Production API Server"
                });
                document.Servers.Add(new OpenApiServer
                {
                    Url = "https://staging-api.sonobrokers.com",
                    Description = "SoNoBrokers Staging API Server"
                });
                document.Servers.Add(new OpenApiServer
                {
                    Url = "https://localhost:7163",
                    Description = "SoNoBrokers Development Server"
                });

                // Add security definitions for Clerk JWT
                document.Components ??= new OpenApiComponents();
                document.Components.SecuritySchemes.Add("Bearer", new OpenApiSecurityScheme
                {
                    Type = SecuritySchemeType.Http,
                    Scheme = "bearer",
                    BearerFormat = "JWT",
                    Description = "Enter your Clerk JWT token in the format: Bearer {your-token}"
                });

                // Add global security requirement
                document.SecurityRequirements.Add(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        },
                        Array.Empty<string>()
                    }
                });

                // Add external documentation
                document.ExternalDocs = new OpenApiExternalDocs
                {
                    Description = "SoNoBrokers Developer Portal",
                    Url = new Uri("https://docs.sonobrokers.com")
                };

                return Task.CompletedTask;
            });

            // Add API tags for better organization
            options.AddDocumentTransformer((document, context, cancellationToken) =>
            {
                document.Tags = new List<OpenApiTag>
                {
                    new OpenApiTag
                    {
                        Name = "Properties",
                        Description = "Property listing management - Create, read, update, and delete property listings"
                    },
                    new OpenApiTag
                    {
                        Name = "Users",
                        Description = "User management - Registration, authentication, and profile management"
                    },
                    new OpenApiTag
                    {
                        Name = "Search",
                        Description = "Property search and filtering - Advanced search capabilities with filters"
                    },
                    new OpenApiTag
                    {
                        Name = "Images",
                        Description = "Property image management - Upload, manage, and organize property photos"
                    },
                    new OpenApiTag
                    {
                        Name = "Authentication",
                        Description = "Clerk-based authentication - Login, logout, and token management"
                    },
                    new OpenApiTag
                    {
                        Name = "Payments",
                        Description = "Stripe payment processing - Handle subscriptions and transactions"
                    },
                    new OpenApiTag
                    {
                        Name = "Health",
                        Description = "API health monitoring - System health checks and diagnostics"
                    }
                };

                return Task.CompletedTask;
            });
        });
    }
}

void ConfigureAzureAppConfigurationAndKeyVault()
{
    if (!string.IsNullOrEmpty(appSettings.AppConfigurationEndpoint) && !string.IsNullOrEmpty(appSettings.AppConfigurationEnvironmentLabel) &&
    !string.IsNullOrEmpty(appSettings.KeyVaultUri))
    {
        Console.WriteLine("Using Azure App Configuration with Managed Identity");
        try
        {
            builder.Configuration.AddAzureAppConfiguration(options =>
            {
                options.Connect(
                    new Uri(appSettings.AppConfigurationEndpoint),
                    new ManagedIdentityCredential())
                .ConfigureRefresh(refresh =>
                {
                    refresh.Register("Settings:Sentinel", refreshAll: true)
                           .SetCacheExpiration(new TimeSpan(0, 5, 0));
                })
                .ConfigureKeyVault(kv =>
                {
                    kv.SetCredential(new DefaultAzureCredential());
                })
                .Select(KeyFilter.Any, appSettings.AppConfigurationEnvironmentLabel);
            });
            Console.WriteLine("Connected to Azure App Configuration successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error connecting to Azure App Configuration: {ex.Message}");
        }

    }
    else if (!string.IsNullOrEmpty(appSettings.AppConfigurationConnectionString) && !string.IsNullOrEmpty(appSettings.AppConfigurationEnvironmentLabel) &&
    !string.IsNullOrEmpty(appSettings.KeyVaultUri))
    {
        Console.WriteLine("Using Azure App Configuration with Connection String");
        try
        {
            builder.Configuration.AddAzureAppConfiguration(options =>
            {
                options.Connect(appSettings.AppConfigurationConnectionString)
                    .ConfigureRefresh(refresh =>
                    {
                        refresh.Register("Settings:Sentinel", refreshAll: true)
                               .SetCacheExpiration(new TimeSpan(0, 5, 0));
                    })
                    .ConfigureKeyVault(kv => { kv.SetCredential(new DefaultAzureCredential()); })
                    .Select(KeyFilter.Any, appSettings.AppConfigurationEnvironmentLabel);
            });
            Console.WriteLine("Connected to Azure App Configuration successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error connecting to Azure App Configuration: {ex.Message}");
        }
    }

    if (string.IsNullOrEmpty(appSettings.AppConfigurationEndpoint) && string.IsNullOrEmpty(appSettings.AppConfigurationConnectionString) &&
        !string.IsNullOrEmpty(appSettings.KeyVaultUri) && !string.IsNullOrEmpty(appSettings.AzureTenantId) &&
        !string.IsNullOrEmpty(appSettings.AzureClientId) && !string.IsNullOrEmpty(appSettings.AzureClientSecret))
    {
        try
        {
            Console.WriteLine("Fetching values directly from Key Vault");

            builder.Configuration.AddAzureKeyVault(new Uri(appSettings.KeyVaultUri),
                new ClientSecretCredential(appSettings.AzureTenantId, appSettings.AzureClientId, appSettings.AzureClientSecret));
            Console.WriteLine("Connected to Azure Key Vault successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error connecting to Azure Key Vault: {ex.Message}");
        }
    }
}

// Old health check method removed - using new comprehensive health check configuration

void AddResponseCompression(IServiceCollection services)
{
    services.AddResponseCompression(options =>
    {
        options.EnableForHttps = true;
        options.Providers.Add<BrotliCompressionProvider>();
        options.Providers.Add<GzipCompressionProvider>();
    });

    services.Configure<BrotliCompressionProviderOptions>(options =>
    {
        options.Level = CompressionLevel.Fastest;
    });

    services.Configure<GzipCompressionProviderOptions>(options =>
        options.Level = CompressionLevel.SmallestSize);
}
void RegisterManualServices(IServiceCollection services)
{
    // Core services - simplified for clean build
    services.AddScoped<IBaseLoggerService, LoggerService>();
    services.AddSingleton<IActionContextAccessor, ActionContextAccessor>();

    // MicroSaaS services
    services.AddHttpClient<IClerkService, ClerkService>();
    services.AddScoped<IClerkAuthService, ClerkAuthService>();
    // services.AddScoped<IRoleBasedAuthService, RoleBasedAuthService>(); // Commented out - service not implemented
    services.AddScoped<IStripePaymentService, StripePaymentService>();
    services.AddScoped<IAppConfigurationService, AppConfigurationService>();
}
void RegisterAppSettings(IServiceCollection services, AppSettings appSettings)
{
    // Register the AppSettings as a singleton
    services.AddSingleton(appSettings);

    // Configure rate limiting if enabled
    if (appSettings.Logging.EnableFileLogging || appSettings.Logging.EnableConsoleLogging)
    {
        // Configure Serilog
        var loggerConfig = new LoggerConfiguration()
            .MinimumLevel.Information()
            .MinimumLevel.Override("Microsoft", Serilog.Events.LogEventLevel.Warning)
            .MinimumLevel.Override("System", Serilog.Events.LogEventLevel.Warning);

        if (appSettings.Logging.EnableConsoleLogging)
        {
            loggerConfig.WriteTo.Console();
        }

        if (appSettings.Logging.EnableFileLogging)
        {
            loggerConfig.WriteTo.File(
                appSettings.Logging.LogFilePath,
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: appSettings.Logging.RetainedFileCountLimit,
                fileSizeLimitBytes: appSettings.Logging.MaxLogFileSizeMB * 1024 * 1024);
        }

        Log.Logger = loggerConfig.CreateLogger();
        services.AddSerilog();
    }
}

void ConfigureKestrel(IWebHostBuilder webHostBuilder)
{
    webHostBuilder.ConfigureKestrel(serverOptions =>
    {
        serverOptions.Limits.MaxRequestBodySize = 100_000_000;
        serverOptions.Limits.MaxConcurrentConnections = 100;
        serverOptions.Limits.MaxConcurrentUpgradedConnections = 100;
        serverOptions.Limits.KeepAliveTimeout = TimeSpan.FromSeconds(130);
    });
}

void AddCorsPolicy(IServiceCollection services)
{
    var corsSettings = new CorsSettings();
    var allowedOrigins = corsSettings.AllowedOrigins.Split(',', StringSplitOptions.RemoveEmptyEntries);
    var allowedMethods = corsSettings.AllowedMethods.Split(',', StringSplitOptions.RemoveEmptyEntries);
    var allowedHeaders = corsSettings.AllowedHeaders.Split(',', StringSplitOptions.RemoveEmptyEntries);

    services.AddCors(options =>
    {
        options.AddPolicy("MicroSaasPolicy", builder =>
        {
            builder.WithOrigins(allowedOrigins)
                   .WithMethods(allowedMethods)
                   .WithHeaders(allowedHeaders);

            if (corsSettings.AllowCredentials)
            {
                builder.AllowCredentials();
            }
        });

        // Keep a permissive policy for development
        options.AddPolicy("AllowAll", builder =>
        {
            builder.AllowAnyOrigin()
                   .AllowAnyMethod()
                   .AllowAnyHeader();
        });
    });
}

// Old Swagger method removed - using .NET 9 native OpenAPI + Scalar

// Old ConfigureMiddleware function removed - now using MiddlewareOrdering.ConfigureMiddlewarePipeline()

// Old Swagger/OpenAPI configuration methods removed - now handled in MiddlewareOrdering.cs

