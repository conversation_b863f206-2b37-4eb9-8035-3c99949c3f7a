using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using MicroSaasWebApi.Services.Auth.Interfaces;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/sonobrokers/projects")]
    public class ProjectsController : ControllerBase
    {
        private readonly IProjectService _projectService;
        private readonly IClerkAuthService _authService;
        private readonly ILogger<ProjectsController> _logger;

        public ProjectsController(
            IProjectService projectService,
            IClerkAuthService authService,
            ILogger<ProjectsController> logger)
        {
            _projectService = projectService;
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// Get projects for the authenticated user
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ProjectSearchResponse>> GetProjects([FromQuery] ProjectSearchRequest request)
        {
            try
            {
                // Get authenticated user
                var userClerkId = await _authService.GetUserIdAsync(HttpContext);
                if (string.IsNullOrEmpty(userClerkId))
                {
                    return Unauthorized(new { error = "Authentication required" });
                }

                var result = await _projectService.GetProjectsAsync(request, userClerkId);
                return Ok(new { success = true, projects = result.Projects, total = result.Total });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting projects");
                return StatusCode(500, new { error = "An unexpected error occurred" });
            }
        }

        /// <summary>
        /// Get project by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<ProjectResponse>> GetProject(string id)
        {
            try
            {
                // Get authenticated user
                var userClerkId = await _authService.GetUserIdAsync(HttpContext);
                if (string.IsNullOrEmpty(userClerkId))
                {
                    return Unauthorized(new { error = "Authentication required" });
                }

                var project = await _projectService.GetProjectByIdAsync(id, userClerkId);
                if (project == null)
                {
                    return NotFound(new { error = "Project not found" });
                }

                return Ok(project);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting project: {Id}", id);
                return StatusCode(500, new { error = "Failed to get project" });
            }
        }

        /// <summary>
        /// Create a new project
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<ProjectResponse>> CreateProject([FromBody] CreateProjectRequest request)
        {
            try
            {
                // Get authenticated user
                var userClerkId = await _authService.GetUserIdAsync(HttpContext);
                if (string.IsNullOrEmpty(userClerkId))
                {
                    return Unauthorized(new { error = "Authentication required" });
                }

                var project = await _projectService.CreateProjectAsync(request, userClerkId);
                return CreatedAtAction(nameof(GetProject), new { id = project.Id }, project);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating project");
                return StatusCode(500, new { error = "Failed to create project" });
            }
        }

        /// <summary>
        /// Update an existing project
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<ProjectResponse>> UpdateProject(string id, [FromBody] UpdateProjectRequest request)
        {
            try
            {
                // Get authenticated user
                var userClerkId = await _authService.GetUserIdAsync(HttpContext);
                if (string.IsNullOrEmpty(userClerkId))
                {
                    return Unauthorized(new { error = "Authentication required" });
                }

                request.Id = id;
                var updated = await _projectService.UpdateProjectAsync(request, userClerkId);
                if (updated == null)
                {
                    return NotFound(new { error = "Project not found" });
                }

                return Ok(updated);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating project: {Id}", id);
                return StatusCode(500, new { error = "Failed to update project" });
            }
        }

        /// <summary>
        /// Delete a project
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteProject(string id)
        {
            try
            {
                // Get authenticated user
                var userClerkId = await _authService.GetUserIdAsync(HttpContext);
                if (string.IsNullOrEmpty(userClerkId))
                {
                    return Unauthorized(new { error = "Authentication required" });
                }

                var deleted = await _projectService.DeleteProjectAsync(id, userClerkId);
                if (!deleted)
                {
                    return NotFound(new { error = "Project not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting project: {Id}", id);
                return StatusCode(500, new { error = "Failed to delete project" });
            }
        }
    }
}
