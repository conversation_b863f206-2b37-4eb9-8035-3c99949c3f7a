'use client'

import React, { useState } from 'react'
import { HeroSection } from '@/components/country-specific/ca/HeroSection'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Calculator, DollarSign, TrendingDown, Percent, PiggyBank } from 'lucide-react'

interface CommissionCalculatorProps {
  userType: 'buyer' | 'seller'
  isSignedIn: boolean
  country: string
}

export function CommissionCalculator({ userType, isSignedIn, country }: CommissionCalculatorProps) {
  const [salePrice, setSalePrice] = useState('')
  const [traditionalRate, setTraditionalRate] = useState([6])
  const [soNoBrokersRate, setSoNoBrokersRate] = useState([1.5])
  const [serviceLevel, setServiceLevel] = useState('full')
  const [calculations, setCalculations] = useState<any>(null)

  const calculateSavings = () => {
    const price = parseFloat(salePrice)
    if (!price || price <= 0) return

    const traditionalCommission = price * (traditionalRate[0] / 100)
    const soNoBrokersCommission = price * (soNoBrokersRate[0] / 100)
    const savings = traditionalCommission - soNoBrokersCommission
    const savingsPercentage = (savings / traditionalCommission) * 100

    // Additional costs
    const legalFees = country === 'CA' ? 1500 : 1200
    const inspectionFee = 500
    const photographyFee = serviceLevel === 'full' ? 300 : 0
    const marketingFee = serviceLevel === 'full' ? 200 : 0

    const totalAdditionalCosts = legalFees + inspectionFee + photographyFee + marketingFee
    const netSavings = savings - totalAdditionalCosts

    setCalculations({
      salePrice: price,
      traditionalCommission,
      soNoBrokersCommission,
      savings,
      savingsPercentage,
      netSavings,
      additionalCosts: {
        legal: legalFees,
        inspection: inspectionFee,
        photography: photographyFee,
        marketing: marketingFee,
        total: totalAdditionalCosts
      }
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: country === 'CA' ? 'CAD' : 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Calculator Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-4 flex items-center justify-center">
              <Calculator className="w-8 h-8 mr-3 text-primary" />
              Commission Savings Calculator
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              See how much you can save on real estate commissions by using SoNoBrokers instead of traditional agents.
              Calculate your potential savings and compare service options.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Input Form */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <DollarSign className="w-5 h-5 mr-2" />
                  Sale Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label>Expected Sale Price</Label>
                  <Input
                    placeholder={`Enter sale price in ${country === 'CA' ? 'CAD' : 'USD'}`}
                    value={salePrice}
                    onChange={(e) => setSalePrice(e.target.value)}
                    type="number"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Service Level</Label>
                  <Select value={serviceLevel} onValueChange={setServiceLevel}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select service level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="basic">Basic Listing (1.0%)</SelectItem>
                      <SelectItem value="standard">Standard Service (1.5%)</SelectItem>
                      <SelectItem value="full">Full Service (2.0%)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Traditional Agent Commission (%)</Label>
                  <div className="px-3">
                    <Slider
                      value={traditionalRate}
                      onValueChange={setTraditionalRate}
                      max={8}
                      min={4}
                      step={0.5}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground mt-1">
                      <span>4%</span>
                      <span>{traditionalRate[0]}%</span>
                      <span>8%</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>SoNoBrokers Commission (%)</Label>
                  <div className="px-3">
                    <Slider
                      value={soNoBrokersRate}
                      onValueChange={setSoNoBrokersRate}
                      max={3}
                      min={1}
                      step={0.25}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground mt-1">
                      <span>1%</span>
                      <span>{soNoBrokersRate[0]}%</span>
                      <span>3%</span>
                    </div>
                  </div>
                </div>

                <Button
                  onClick={calculateSavings}
                  className="w-full"
                  size="lg"
                  disabled={!salePrice || parseFloat(salePrice) <= 0}
                >
                  <Calculator className="w-4 h-4 mr-2" />
                  Calculate Savings
                </Button>
              </CardContent>
            </Card>

            {/* Results */}
            <div className="space-y-6">
              {calculations && (
                <>
                  <Card className="border-primary">
                    <CardHeader>
                      <CardTitle className="flex items-center text-primary">
                        <PiggyBank className="w-5 h-5 mr-2" />
                        Your Potential Savings
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center">
                        <div className="text-4xl font-bold text-green-600 mb-2">
                          {formatCurrency(calculations.netSavings)}
                        </div>
                        <p className="text-sm text-muted-foreground mb-4">
                          Total savings after all costs
                        </p>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="text-center">
                            <div className="font-semibold text-red-600">
                              {formatCurrency(calculations.traditionalCommission)}
                            </div>
                            <div className="text-muted-foreground">Traditional Agent</div>
                          </div>
                          <div className="text-center">
                            <div className="font-semibold text-green-600">
                              {formatCurrency(calculations.soNoBrokersCommission)}
                            </div>
                            <div className="text-muted-foreground">SoNoBrokers</div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Percent className="w-5 h-5 mr-2" />
                        Commission Breakdown
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Sale Price</span>
                          <span className="font-medium">{formatCurrency(calculations.salePrice)}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Traditional Commission ({traditionalRate[0]}%)</span>
                          <span className="font-medium text-red-600">
                            -{formatCurrency(calculations.traditionalCommission)}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">SoNoBrokers Commission ({soNoBrokersRate[0]}%)</span>
                          <span className="font-medium text-green-600">
                            -{formatCurrency(calculations.soNoBrokersCommission)}
                          </span>
                        </div>
                        <div className="border-t pt-2">
                          <div className="flex items-center justify-between font-semibold">
                            <span>Commission Savings</span>
                            <span className="text-green-600">
                              {formatCurrency(calculations.savings)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Additional Costs</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between text-sm">
                          <span>Legal Fees</span>
                          <span>{formatCurrency(calculations.additionalCosts.legal)}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span>Home Inspection</span>
                          <span>{formatCurrency(calculations.additionalCosts.inspection)}</span>
                        </div>
                        {calculations.additionalCosts.photography > 0 && (
                          <div className="flex items-center justify-between text-sm">
                            <span>Photography</span>
                            <span>{formatCurrency(calculations.additionalCosts.photography)}</span>
                          </div>
                        )}
                        {calculations.additionalCosts.marketing > 0 && (
                          <div className="flex items-center justify-between text-sm">
                            <span>Marketing</span>
                            <span>{formatCurrency(calculations.additionalCosts.marketing)}</span>
                          </div>
                        )}
                        <div className="border-t pt-2">
                          <div className="flex items-center justify-between font-semibold">
                            <span>Total Additional Costs</span>
                            <span>{formatCurrency(calculations.additionalCosts.total)}</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}

              <Card>
                <CardHeader>
                  <CardTitle>Why Choose SoNoBrokers?</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <ul className="text-sm space-y-2">
                      <li>• Save thousands in commission fees</li>
                      <li>• Direct buyer-seller connections</li>
                      <li>• Professional support when needed</li>
                      <li>• Transparent pricing with no hidden fees</li>
                      <li>• Full control over your sale process</li>
                    </ul>
                    <Button className="w-full mt-4">
                      Get Started Today
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section - Moved to bottom */}
      <HeroSection userType={userType} />
    </div>
  )
}
