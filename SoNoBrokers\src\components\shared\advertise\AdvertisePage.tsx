'use client'

import React, { useState } from 'react'
import { HeroSection } from '@/components/country-specific/ca/HeroSection'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Star,
  CheckCircle,
  Crown,
  MapPin,
  Users,
  TrendingUp,
  Shield,
  Phone,
  Mail,
  Globe,
  Camera,
  Scale,
  Home,
  Wrench,
  Truck,
  Calculator,
  Search,
  Paintbrush,
  FileText
} from 'lucide-react'

interface AdvertisePageProps {
  userType: 'buyer' | 'seller'
  isSignedIn: boolean
  country: string
}

const serviceTypes = [
  // Services available in Navigation Menu
  { value: 'photography', label: 'Photography Services', icon: Camera },
  { value: 'legal', label: 'Legal Services', icon: Scale },
  { value: 'inspection', label: 'Home Inspection', icon: Search },
  { value: 'staging', label: 'Staging Services', icon: Paintbrush },
  { value: 'moving', label: 'Moving Services', icon: Truck },
  { value: 'insurance', label: 'Insurance Services', icon: Shield },
  { value: 'mortgage', label: 'Mortgage Services', icon: Calculator },
  { value: 'marketing', label: 'Marketing Services', icon: TrendingUp },
  { value: 'appraisal', label: 'Property Appraisal', icon: FileText },
]

const pricingPlans = [
  {
    id: 'basic',
    name: 'Basic Listing',
    price: '$49',
    period: '/month',
    description: 'Get listed in our service directory',
    features: [
      'Basic business listing',
      'Contact information display',
      'Service area coverage',
      'Customer reviews',
      'Mobile-friendly profile'
    ],
    popular: false
  },
  {
    id: 'premium',
    name: 'Premium Listing',
    price: '$149',
    period: '/month',
    description: 'Stand out with premium features',
    features: [
      'Everything in Basic',
      'Featured placement',
      'Premium badge',
      'Photo gallery (up to 10 images)',
      'Detailed service descriptions',
      'Priority customer support',
      'Analytics dashboard'
    ],
    popular: true
  }
]

export function AdvertisePage({ userType, isSignedIn, country }: AdvertisePageProps) {
  const [selectedPlan, setSelectedPlan] = useState<string>('')
  const [formData, setFormData] = useState({
    // Basic Plan Fields
    businessName: '',
    serviceType: '',
    contactName: '',
    email: '',
    phone: '',
    website: '',
    description: '',
    serviceAreas: '',
    licenseNumber: '',

    // Premium Plan Additional Fields
    detailedDescription: '',
    specialties: '',
    yearsExperience: '',
    certifications: '',
    insuranceInfo: '',
    emergencyAvailable: false,
    languages: '',
    paymentMethods: '',

    // Photo Gallery (Premium)
    photos: [] as File[],

    // Business Hours
    businessHours: {
      monday: { open: '09:00', close: '17:00', closed: false },
      tuesday: { open: '09:00', close: '17:00', closed: false },
      wednesday: { open: '09:00', close: '17:00', closed: false },
      thursday: { open: '09:00', close: '17:00', closed: false },
      friday: { open: '09:00', close: '17:00', closed: false },
      saturday: { open: '09:00', close: '17:00', closed: false },
      sunday: { open: '09:00', close: '17:00', closed: true }
    }
  })

  const handleInputChange = (field: string, value: string | boolean | File[]) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleFileUpload = (files: FileList | null) => {
    if (files) {
      const fileArray = Array.from(files).slice(0, 10) // Limit to 10 files
      setFormData(prev => ({ ...prev, photos: fileArray }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Prepare form data based on selected plan
    const submissionData = {
      plan: selectedPlan,
      basicInfo: {
        businessName: formData.businessName,
        serviceType: formData.serviceType,
        contactName: formData.contactName,
        email: formData.email,
        phone: formData.phone,
        website: formData.website,
        description: formData.description,
        serviceAreas: formData.serviceAreas,
        licenseNumber: formData.licenseNumber,
      },
      ...(selectedPlan === 'premium' && {
        premiumInfo: {
          detailedDescription: formData.detailedDescription,
          specialties: formData.specialties,
          yearsExperience: formData.yearsExperience,
          certifications: formData.certifications,
          insuranceInfo: formData.insuranceInfo,
          emergencyAvailable: formData.emergencyAvailable,
          languages: formData.languages,
          paymentMethods: formData.paymentMethods,
          photos: formData.photos,
          businessHours: formData.businessHours,
        }
      })
    }

    console.log('Form submitted:', submissionData)

    // TODO: Save to database and redirect to Stripe checkout
    // This would typically involve:
    // 1. Save advertiser data to database
    // 2. Create Stripe checkout session
    // 3. Redirect to payment
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-primary/10 via-background to-accent/10 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl font-bold text-foreground mb-4">
              Advertise Your Services on SoNoBrokers
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              Connect with thousands of {userType === 'seller' ? 'property sellers' : 'home buyers'} in {country}
              looking for professional services. Grow your business with location-based advertising.
            </p>
            <div className="flex items-center justify-center space-x-8 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <Users className="w-5 h-5 text-primary" />
                <span>50,000+ Active Users</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="w-5 h-5 text-primary" />
                <span>Location-Based Matching</span>
              </div>
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-primary" />
                <span>Proven Results</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-6xl mx-auto">
          {/* Pricing Plans */}
          <div className="mb-12">
            <h2 className="text-3xl font-bold text-center mb-8">Choose Your Advertising Plan</h2>
            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              {pricingPlans.map((plan) => (
                <Card
                  key={plan.id}
                  className={`relative cursor-pointer transition-all hover:shadow-lg ${selectedPlan === plan.id ? 'ring-2 ring-primary' : ''
                    } ${plan.popular ? 'border-primary shadow-lg' : ''}`}
                  onClick={() => setSelectedPlan(plan.id)}
                >
                  {plan.popular && (
                    <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary text-white">
                      Most Popular
                    </Badge>
                  )}
                  <CardHeader className="text-center">
                    <CardTitle className="text-2xl">{plan.name}</CardTitle>
                    <div className="text-3xl font-bold text-primary">
                      {plan.price}<span className="text-lg text-muted-foreground">{plan.period}</span>
                    </div>
                    <p className="text-muted-foreground">{plan.description}</p>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center space-x-2">
                          <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                          <span className="text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Registration Form */}
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-2xl text-center">Get Started Today</CardTitle>
              <p className="text-center text-muted-foreground">
                Fill out the form below to start advertising your services
              </p>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Business Name *</label>
                    <Input
                      value={formData.businessName}
                      onChange={(e) => handleInputChange('businessName', e.target.value)}
                      placeholder="Your Business Name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Service Type *</label>
                    <Select value={formData.serviceType} onValueChange={(value) => handleInputChange('serviceType', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select service type" />
                      </SelectTrigger>
                      <SelectContent>
                        {serviceTypes.map((service) => (
                          <SelectItem key={service.value} value={service.value}>
                            <div className="flex items-center space-x-2">
                              <service.icon className="w-4 h-4" />
                              <span>{service.label}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Contact Name *</label>
                    <Input
                      value={formData.contactName}
                      onChange={(e) => handleInputChange('contactName', e.target.value)}
                      placeholder="Your Name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Email *</label>
                    <Input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Phone Number *</label>
                    <Input
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder="+****************"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Website</label>
                    <Input
                      value={formData.website}
                      onChange={(e) => handleInputChange('website', e.target.value)}
                      placeholder="https://yourwebsite.com"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Service Description *</label>
                  <Textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Describe your services, experience, and what makes you unique..."
                    rows={4}
                    required
                  />
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Service Areas *</label>
                    <Input
                      value={formData.serviceAreas}
                      onChange={(e) => handleInputChange('serviceAreas', e.target.value)}
                      placeholder="Toronto, Mississauga, Vaughan..."
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">License Number</label>
                    <Input
                      value={formData.licenseNumber}
                      onChange={(e) => handleInputChange('licenseNumber', e.target.value)}
                      placeholder="Professional license number"
                    />
                  </div>
                </div>

                {/* Premium Plan Additional Fields */}
                {selectedPlan === 'premium' && (
                  <div className="space-y-6 border-t pt-6">
                    <h3 className="text-lg font-semibold text-primary flex items-center">
                      <Crown className="w-5 h-5 mr-2" />
                      Premium Features
                    </h3>

                    <div>
                      <label className="block text-sm font-medium mb-2">Detailed Service Description</label>
                      <Textarea
                        value={formData.detailedDescription}
                        onChange={(e) => handleInputChange('detailedDescription', e.target.value)}
                        placeholder="Provide a comprehensive description of your services, methodology, and unique value proposition..."
                        rows={6}
                      />
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">Specialties</label>
                        <Input
                          value={formData.specialties}
                          onChange={(e) => handleInputChange('specialties', e.target.value)}
                          placeholder="e.g., Luxury homes, Commercial properties"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">Years of Experience</label>
                        <Input
                          type="number"
                          value={formData.yearsExperience}
                          onChange={(e) => handleInputChange('yearsExperience', e.target.value)}
                          placeholder="e.g., 10"
                        />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">Certifications</label>
                        <Input
                          value={formData.certifications}
                          onChange={(e) => handleInputChange('certifications', e.target.value)}
                          placeholder="Professional certifications, awards"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">Insurance Information</label>
                        <Input
                          value={formData.insuranceInfo}
                          onChange={(e) => handleInputChange('insuranceInfo', e.target.value)}
                          placeholder="Liability insurance details"
                        />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">Languages Spoken</label>
                        <Input
                          value={formData.languages}
                          onChange={(e) => handleInputChange('languages', e.target.value)}
                          placeholder="English, French, Spanish..."
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">Payment Methods</label>
                        <Input
                          value={formData.paymentMethods}
                          onChange={(e) => handleInputChange('paymentMethods', e.target.value)}
                          placeholder="Cash, Credit Card, E-transfer..."
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">Photo Gallery (up to 10 images)</label>
                      <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                        <Camera className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                        <p className="text-sm text-muted-foreground mb-2">
                          Upload photos of your work, team, and equipment
                        </p>
                        <input
                          type="file"
                          multiple
                          accept="image/jpeg,image/png,image/webp"
                          onChange={(e) => handleFileUpload(e.target.files)}
                          className="hidden"
                          id="photo-upload"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => document.getElementById('photo-upload')?.click()}
                        >
                          Choose Files
                        </Button>
                        <p className="text-xs text-muted-foreground mt-2">
                          Supported formats: JPG, PNG, WebP (max 5MB each)
                        </p>
                        {formData.photos.length > 0 && (
                          <div className="mt-4">
                            <p className="text-sm font-medium mb-2">{formData.photos.length} file(s) selected</p>
                            <div className="flex flex-wrap gap-2">
                              {formData.photos.map((file, index) => (
                                <div key={index} className="text-xs bg-muted px-2 py-1 rounded">
                                  {file.name}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                <div className="text-center">
                  <Button
                    type="submit"
                    size="lg"
                    className="w-full md:w-auto px-8"
                    disabled={!selectedPlan}
                  >
                    {selectedPlan ? `Continue with ${pricingPlans.find(p => p.id === selectedPlan)?.name}` : 'Select a Plan First'}
                  </Button>
                  <p className="text-sm text-muted-foreground mt-2">
                    You'll be redirected to secure payment processing
                  </p>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
