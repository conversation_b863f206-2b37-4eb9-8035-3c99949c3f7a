const Github = () => {
  return (
    <svg
      width="64"
      height="63"
      viewBox="0 0 64 63"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M32.0265 0C14.3167 0 0 14.4222 0 32.2644C0 46.5267 9.17318 58.5994 21.8988 62.8723C23.4898 63.1935 24.0726 62.1781 24.0726 61.3239C24.0726 60.5759 24.0202 58.012 24.0202 55.3406C15.1112 57.264 13.256 51.4945 13.256 51.4945C11.8242 47.7552 9.70287 46.7942 9.70287 46.7942C6.78696 44.8177 9.91527 44.8177 9.91527 44.8177C13.1498 45.0314 14.847 48.1295 14.847 48.1295C17.7098 53.0436 22.323 51.6551 24.1788 50.8003C24.4437 48.7169 25.2926 47.2747 26.194 46.4736C19.0884 45.7256 11.6125 42.948 11.6125 30.5548C11.6125 27.0292 12.8843 24.1447 14.8995 21.9014C14.5815 21.1003 13.4677 17.7878 15.2181 13.3543C15.2181 13.3543 17.9222 12.4995 24.0195 16.6662C26.63 15.9599 29.3222 15.6006 32.0265 15.5976C34.7306 15.5976 37.4873 15.9719 40.0328 16.6662C46.1307 12.4995 48.8349 13.3543 48.8349 13.3543C50.5852 17.7878 49.4708 21.1003 49.1528 21.9014C51.2211 24.1447 52.4405 27.0292 52.4405 30.5548C52.4405 42.948 44.9645 45.6719 37.8059 46.4736C38.9727 47.4884 39.9797 49.4111 39.9797 52.4562C39.9797 56.7829 39.9272 60.2553 39.9272 61.3232C39.9272 62.1781 40.5107 63.1935 42.101 62.873C54.8267 58.5987 63.9999 46.5267 63.9999 32.2644C64.0523 14.4222 49.6832 0 32.0265 0Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default Github;
