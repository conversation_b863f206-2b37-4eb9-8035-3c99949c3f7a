{"AppInfo": {"Environment": "Development"}, "TenantSettings": {"EnableMultiTenantFeatures": true, "TenantCacheExpiry": "00:05:00", "DefaultTenantId": "demo"}, "ApiEndpoints": {"DataApi": {"BaseUrl": "https://dev-api.microsaas.com/data-api/v1"}, "DocumentApi": {"BaseUrl": "https://dev-api.microsaas.com/document-api/v1"}, "PermissionApi": {"BaseUrl": "https://dev-api.microsaas.com/permission-api/v1"}}, "FeatureFlags": {"EnableSwagger": true, "EnableHangfire": true, "EnableDetailedLogging": true, "EnablePayments": false, "EnableAnalytics": true, "EnableCustomBranding": true, "EnableMultiRegion": false, "EnableAdvancedSecurity": false}, "Authentication": {"Clerk": {"PublishableKey": "pk_test_development_key_here", "JwtIssuer": "https://dev-clerk.microsaas.com", "AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "https://dev-app.microsaas.com"]}}, "RateLimiting": {"GlobalRequestsPerMinute": 2000, "TenantRequestsPerMinute": 200, "UserRequestsPerMinute": 120}, "Security": {"Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "https://dev-app.microsaas.com"]}, "Headers": {"EnableHsts": false}}, "Monitoring": {"HealthChecks": {"EnableDetailedErrors": true}}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore": "Information", "Microsoft.AspNetCore.Hosting": "Debug", "System.Net.Http.HttpClient": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=microsaas_dev;Username=postgres;Password=password"}}