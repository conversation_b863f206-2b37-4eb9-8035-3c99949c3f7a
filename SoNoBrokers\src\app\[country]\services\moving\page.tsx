import { MovingService } from './MovingService'
// Removed auth import - no authentication required for browsing moving services
import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function MovingPage({ params, searchParams }: PageProps) {
  // No authentication required for browsing moving services
  const isSignedIn = false // Will be handled by client components when needed

  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/services/moving')
  }

  // Default to buyer for moving services
  const userType = resolvedSearchParams.userType || 'buyer'

  return (
    <MovingService
      userType={userType}
      isSignedIn={isSignedIn}
      country={country.toUpperCase()}
    />
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'buyer'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Moving Services for ${userType === 'buyer' ? 'Home Buyers' : 'Property Owners'} in ${country} | SoNoBrokers`,
    description: `Find trusted moving companies in ${country}. Professional movers, packing services, and relocation assistance. Compare quotes and book reliable moving services.`,
    keywords: `moving services, movers, relocation, ${userType}, ${country}, moving company`,
  }
}
