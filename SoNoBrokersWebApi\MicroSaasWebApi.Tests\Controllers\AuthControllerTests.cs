using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using MicroSaasWebApi.Models.Auth;
using MicroSaasWebApi.Tests.Common;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;

namespace MicroSaasWebApi.Tests.Controllers
{
    public class AuthControllerTests : IClassFixture<TestWebApplicationFactory<Program>>, IAsyncLifetime
    {
        private readonly TestWebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;
        private readonly ITestOutputHelper _output;

        public AuthControllerTests(TestWebApplicationFactory<Program> factory, ITestOutputHelper output)
        {
            _factory = factory;
            _output = output;
            _client = _factory.CreateClient();
        }

        public async Task InitializeAsync()
        {
            await _factory.SeedTestDataAsync();
        }

        public async Task DisposeAsync()
        {
            await _factory.CleanupTestDataAsync();
        }

        [Fact]
        public async Task Login_WithValidCredentials_ReturnsSuccess()
        {
            // Arrange
            var request = new LoginRequest
            {
                Email = "<EMAIL>",
                Password = "TestPassword123!"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/auth/login", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AuthResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Token.Should().NotBeNullOrEmpty();
            result.User.Should().NotBeNull();
        }

        [Fact]
        public async Task Login_WithInvalidCredentials_ReturnsUnauthorized()
        {
            // Arrange
            var request = new LoginRequest
            {
                Email = "<EMAIL>",
                Password = "WrongPassword"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/auth/login", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        }

        [Fact]
        public async Task Login_WithMissingEmail_ReturnsBadRequest()
        {
            // Arrange
            var request = new LoginRequest
            {
                Email = "",
                Password = "TestPassword123!"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/auth/login", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task Login_WithMissingPassword_ReturnsBadRequest()
        {
            // Arrange
            var request = new LoginRequest
            {
                Email = "<EMAIL>",
                Password = ""
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/auth/login", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task Register_WithValidData_ReturnsCreated()
        {
            // Arrange
            var request = new RegisterRequest
            {
                Email = "<EMAIL>",
                Password = "NewPassword123!",
                FirstName = "New",
                LastName = "User"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/auth/register", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Created);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AuthResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Token.Should().NotBeNullOrEmpty();
            result.User.Should().NotBeNull();
            result.User!.Email.Should().Be(request.Email);
        }

        [Fact]
        public async Task Register_WithExistingEmail_ReturnsBadRequest()
        {
            // Arrange
            var request = new RegisterRequest
            {
                Email = "<EMAIL>", // Existing email
                Password = "NewPassword123!",
                FirstName = "Test",
                LastName = "User"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/auth/register", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task GetProfile_WithValidAuth_ReturnsProfile()
        {
            // Arrange
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

            // Act
            var response = await _client.GetAsync("/api/sonobrokers/auth/profile");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<UserInfo>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Email.Should().NotBeNullOrEmpty();
            result.Id.Should().NotBeEmpty();
        }

        [Fact]
        public async Task GetProfile_WithoutAuth_ReturnsUnauthorized()
        {
            // Act
            var response = await _client.GetAsync("/api/sonobrokers/auth/profile");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        }

        [Fact]
        public async Task UpdateProfile_WithValidData_ReturnsUpdatedProfile()
        {
            // Arrange
            var request = new
            {
                FirstName = "Updated",
                LastName = "Name"
            };

            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

            // Act
            var response = await _client.PutAsJsonAsync("/api/sonobrokers/auth/profile", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<UserInfo>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.FirstName.Should().Be("Updated");
            result.LastName.Should().Be("Name");
        }

        [Fact]
        public async Task UpdateProfile_WithoutAuth_ReturnsUnauthorized()
        {
            // Arrange
            var request = new
            {
                FirstName = "Updated",
                LastName = "Name"
            };

            // Act
            var response = await _client.PutAsJsonAsync("/api/sonobrokers/auth/profile", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        }

        [Fact]
        public async Task RefreshToken_WithValidToken_ReturnsNewToken()
        {
            // Arrange
            var request = new
            {
                RefreshToken = "valid-refresh-token"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/auth/refresh", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AuthResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Token.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task RefreshToken_WithInvalidToken_ReturnsBadRequest()
        {
            // Arrange
            var request = new
            {
                RefreshToken = ""
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/auth/refresh", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task Logout_WithValidAuth_ReturnsSuccess()
        {
            // Arrange
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

            // Act
            var response = await _client.PostAsync("/api/sonobrokers/auth/logout", null);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<object>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
        }

        [Fact]
        public async Task Logout_WithoutAuth_ReturnsOk()
        {
            // Act
            var response = await _client.PostAsync("/api/sonobrokers/auth/logout", null);

            // Assert
            // Should return OK even without auth (user already logged out)
            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        [Fact]
        public async Task ValidateAuth_WithValidAuth_ReturnsValid()
        {
            // Arrange
            _client.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

            // Act
            var response = await _client.GetAsync("/api/sonobrokers/auth/validate");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<object>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
        }

        [Fact]
        public async Task ValidateAuth_WithoutAuth_ReturnsUnauthorized()
        {
            // Act
            var response = await _client.GetAsync("/api/sonobrokers/auth/validate");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        }

        [Theory]
        [InlineData("<EMAIL>", "Password123!")]
        [InlineData("<EMAIL>", "AnotherPass456!")]
        public async Task Login_WithDifferentValidCredentials_ReturnsSuccess(string email, string password)
        {
            // Arrange
            var request = new LoginRequest
            {
                Email = email,
                Password = password
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/auth/login", request);

            // Assert
            // Note: This test assumes the mock auth service accepts any valid format
            response.StatusCode.Should().BeOneOf(HttpStatusCode.OK, HttpStatusCode.Unauthorized);
        }

        [Theory]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        public async Task Register_WithDifferentEmails_ReturnsCreated(string email)
        {
            // Arrange
            var request = new RegisterRequest
            {
                Email = email,
                Password = "TestPassword123!",
                FirstName = "Test",
                LastName = "User"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/sonobrokers/auth/register", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Created);
        }
    }
}
