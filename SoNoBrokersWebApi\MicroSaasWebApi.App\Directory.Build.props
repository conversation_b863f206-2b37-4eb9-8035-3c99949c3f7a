<Project>
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>NU1605;NU1902</WarningsNotAsErrors>
  </PropertyGroup>

  <!-- Central Package Management - DISABLED: Using explicit versions in project files -->
  <!--
  <ItemGroup>
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="9.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0" />
    <PackageVersion Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.0" />
    <PackageVersion Include="System.IdentityModel.Tokens.Jwt" Version="7.1.2" />
    <PackageVersion Include="Microsoft.Identity.Web" Version="2.16.1" />
    <PackageVersion Include="Microsoft.Identity.Web.UI" Version="2.16.1" />
    <PackageVersion Include="Stripe.net" Version="43.15.0" />
    <PackageVersion Include="Dapper" Version="2.1.35" />
    <PackageVersion Include="DotNetEnv" Version="3.0.0" />
    <PackageVersion Include="AspNetCoreRateLimit" Version="5.0.0" />
    <PackageVersion Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageVersion Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageVersion Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageVersion Include="Hangfire.Core" Version="1.8.14" />
    <PackageVersion Include="Hangfire.PostgreSql" Version="1.20.9" />
    <PackageVersion Include="Hangfire.AspNetCore" Version="1.8.14" />
  </ItemGroup>
  -->
</Project>
