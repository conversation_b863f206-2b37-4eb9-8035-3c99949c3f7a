'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Calculator, DollarSign, Percent, Calendar, TrendingUp, Home } from 'lucide-react'

interface USMortgageCalculatorClientProps {
  userType: 'buyer' | 'seller'
}

interface MortgageCalculation {
  monthlyPayment: number
  totalInterest: number
  totalPayment: number
  principalAndInterest: number
  pmi: number
  monthlyPMI: number
  propertyTax: number
  homeInsurance: number
  totalMonthlyPayment: number
}

export function USMortgageCalculatorClient({ userType }: USMortgageCalculatorClientProps) {
  const [homePrice, setHomePrice] = useState<number>(400000)
  const [downPayment, setDownPayment] = useState<number>(80000)
  const [interestRate, setInterestRate] = useState<number>(6.5)
  const [loanTerm, setLoanTerm] = useState<number>(30)
  const [propertyTaxRate, setPropertyTaxRate] = useState<number>(1.2)
  const [homeInsuranceRate, setHomeInsuranceRate] = useState<number>(0.35)
  const [calculation, setCalculation] = useState<MortgageCalculation | null>(null)

  // Calculate PMI (Private Mortgage Insurance)
  const calculatePMI = (price: number, down: number): number => {
    const downPaymentPercent = (down / price) * 100
    
    if (downPaymentPercent >= 20) return 0
    
    const loanAmount = price - down
    const pmiRate = 0.005 // 0.5% annually is typical
    
    return (loanAmount * pmiRate) / 12 // Monthly PMI
  }

  // Calculate mortgage payment
  const calculateMortgage = (): MortgageCalculation => {
    const principal = homePrice - downPayment
    const monthlyRate = interestRate / 100 / 12
    const numberOfPayments = loanTerm * 12
    
    const monthlyPayment = principal * 
      (monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments)) / 
      (Math.pow(1 + monthlyRate, numberOfPayments) - 1)
    
    const totalPayment = monthlyPayment * numberOfPayments
    const totalInterest = totalPayment - principal
    
    const monthlyPMI = calculatePMI(homePrice, downPayment)
    const propertyTax = (homePrice * (propertyTaxRate / 100)) / 12
    const homeInsurance = (homePrice * (homeInsuranceRate / 100)) / 12
    
    const totalMonthlyPayment = monthlyPayment + monthlyPMI + propertyTax + homeInsurance
    
    return {
      monthlyPayment,
      totalInterest,
      totalPayment,
      principalAndInterest: monthlyPayment,
      pmi: monthlyPMI * numberOfPayments,
      monthlyPMI,
      propertyTax,
      homeInsurance,
      totalMonthlyPayment
    }
  }

  useEffect(() => {
    setCalculation(calculateMortgage())
  }, [homePrice, downPayment, interestRate, loanTerm, propertyTaxRate, homeInsuranceRate])

  const downPaymentPercent = (downPayment / homePrice) * 100
  const maxDownPayment = homePrice * 0.97 // Maximum 97% financing

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4 flex items-center gap-2">
          <Calculator className="w-8 h-8" />
          US Mortgage Calculator
        </h1>
        <p className="text-muted-foreground text-lg">
          Calculate your mortgage payments with US rates, PMI, property taxes, and insurance.
        </p>
      </div>

      <div className="grid gap-8 lg:grid-cols-2">
        {/* Input Section */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Loan Details</CardTitle>
              <CardDescription>
                Enter your loan information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Home Price */}
              <div className="space-y-2">
                <Label htmlFor="homePrice">Home Price</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="homePrice"
                    type="number"
                    value={homePrice}
                    onChange={(e) => setHomePrice(Number(e.target.value))}
                    className="pl-10"
                    placeholder="400,000"
                  />
                </div>
              </div>

              {/* Down Payment */}
              <div className="space-y-4">
                <Label>Down Payment: ${downPayment.toLocaleString()} ({downPaymentPercent.toFixed(1)}%)</Label>
                <Slider
                  value={[downPayment]}
                  onValueChange={(value) => setDownPayment(value[0])}
                  max={maxDownPayment}
                  min={homePrice * 0.03} // Minimum 3% down payment
                  step={5000}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>3% (${(homePrice * 0.03).toLocaleString()})</span>
                  <span>97% (${maxDownPayment.toLocaleString()})</span>
                </div>
              </div>

              {/* Interest Rate */}
              <div className="space-y-4">
                <Label>Interest Rate: {interestRate}%</Label>
                <Slider
                  value={[interestRate]}
                  onValueChange={(value) => setInterestRate(value[0])}
                  max={12}
                  min={2}
                  step={0.1}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>2%</span>
                  <span>12%</span>
                </div>
              </div>

              {/* Loan Term */}
              <div className="space-y-2">
                <Label>Loan Term</Label>
                <Select value={loanTerm.toString()} onValueChange={(value) => setLoanTerm(Number(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15">15 years</SelectItem>
                    <SelectItem value="20">20 years</SelectItem>
                    <SelectItem value="25">25 years</SelectItem>
                    <SelectItem value="30">30 years</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Additional Costs</CardTitle>
              <CardDescription>
                Property taxes and insurance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Property Tax Rate */}
              <div className="space-y-4">
                <Label>Property Tax Rate: {propertyTaxRate}% annually</Label>
                <Slider
                  value={[propertyTaxRate]}
                  onValueChange={(value) => setPropertyTaxRate(value[0])}
                  max={3}
                  min={0.1}
                  step={0.1}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>0.1%</span>
                  <span>3%</span>
                </div>
              </div>

              {/* Home Insurance Rate */}
              <div className="space-y-4">
                <Label>Home Insurance: {homeInsuranceRate}% annually</Label>
                <Slider
                  value={[homeInsuranceRate]}
                  onValueChange={(value) => setHomeInsuranceRate(value[0])}
                  max={1}
                  min={0.1}
                  step={0.05}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>0.1%</span>
                  <span>1%</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Results Section */}
        <div className="space-y-6">
          {calculation && (
            <>
              {/* Total Monthly Payment */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Home className="w-5 h-5" />
                    Total Monthly Payment
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-primary">
                    ${calculation.totalMonthlyPayment.toLocaleString('en-US', { 
                      minimumFractionDigits: 2, 
                      maximumFractionDigits: 2 
                    })}
                  </div>
                  <p className="text-muted-foreground">Including all costs</p>
                </CardContent>
              </Card>

              {/* Payment Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Monthly Payment Breakdown</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span>Principal & Interest:</span>
                    <span className="font-semibold">
                      ${calculation.principalAndInterest.toLocaleString('en-US', { 
                        minimumFractionDigits: 2, 
                        maximumFractionDigits: 2 
                      })}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Property Tax:</span>
                    <span className="font-semibold">
                      ${calculation.propertyTax.toLocaleString('en-US', { 
                        minimumFractionDigits: 2, 
                        maximumFractionDigits: 2 
                      })}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Home Insurance:</span>
                    <span className="font-semibold">
                      ${calculation.homeInsurance.toLocaleString('en-US', { 
                        minimumFractionDigits: 2, 
                        maximumFractionDigits: 2 
                      })}
                    </span>
                  </div>
                  {calculation.monthlyPMI > 0 && (
                    <div className="flex justify-between">
                      <span>PMI:</span>
                      <span className="font-semibold">
                        ${calculation.monthlyPMI.toLocaleString('en-US', { 
                          minimumFractionDigits: 2, 
                          maximumFractionDigits: 2 
                        })}
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* PMI Information */}
              {calculation.monthlyPMI > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="w-5 h-5" />
                      Private Mortgage Insurance (PMI)
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Monthly PMI:</span>
                        <span className="font-semibold">
                          ${calculation.monthlyPMI.toLocaleString('en-US', { 
                            minimumFractionDigits: 2, 
                            maximumFractionDigits: 2 
                          })}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Required for down payments less than 20%. Can be removed when you reach 20% equity.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Loan Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    Loan Summary
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Loan Amount</p>
                      <p className="font-semibold">
                        ${(homePrice - downPayment).toLocaleString('en-US')}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Total Interest</p>
                      <p className="font-semibold">
                        ${calculation.totalInterest.toLocaleString('en-US', { 
                          minimumFractionDigits: 2, 
                          maximumFractionDigits: 2 
                        })}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Total Payments</p>
                      <p className="font-semibold">
                        ${calculation.totalPayment.toLocaleString('en-US', { 
                          minimumFractionDigits: 2, 
                          maximumFractionDigits: 2 
                        })}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Down Payment</p>
                      <p className="font-semibold">
                        ${downPayment.toLocaleString('en-US')} ({downPaymentPercent.toFixed(1)}%)
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
