import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function NeighborhoodsPage({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/resources/neighborhoods')
  }

  const userType = resolvedSearchParams.userType || 'buyer'

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8">
            Neighborhood Insights
          </h1>

          <div className="bg-card rounded-lg p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-4">Discover Your Perfect Community</h2>
            <p className="text-muted-foreground mb-6">
              Get detailed insights about neighborhoods across {country.toUpperCase()}. From school ratings
              to local amenities, make informed decisions about where to call home.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <div className="bg-card rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Search Neighborhoods</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">City or Postal Code</label>
                  <input
                    type="text"
                    placeholder="Enter city or postal code"
                    className="w-full p-3 border border-border rounded-lg"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Budget Range</label>
                  <select className="w-full p-3 border border-border rounded-lg">
                    <option>Under $300,000</option>
                    <option>$300,000 - $500,000</option>
                    <option>$500,000 - $750,000</option>
                    <option>$750,000 - $1,000,000</option>
                    <option>Over $1,000,000</option>
                  </select>
                </div>

                <button className="w-full bg-primary text-primary-foreground py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors">
                  Search Neighborhoods
                </button>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">What We Analyze</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span className="text-sm">School ratings and performance</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span className="text-sm">Crime statistics and safety</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span className="text-sm">Local amenities and services</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span className="text-sm">Transportation and commute times</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span className="text-sm">Property value trends</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span className="text-sm">Demographics and lifestyle</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6 mb-8">
            <h3 className="text-xl font-semibold mb-4">Featured Neighborhoods</h3>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="border border-border rounded-lg p-4">
                <h4 className="font-semibold mb-2">Downtown Core</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Avg. Price:</span>
                    <span className="font-semibold">$650,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>School Rating:</span>
                    <span className="font-semibold">8.5/10</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Walk Score:</span>
                    <span className="font-semibold">95/100</span>
                  </div>
                </div>
              </div>

              <div className="border border-border rounded-lg p-4">
                <h4 className="font-semibold mb-2">Suburban Heights</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Avg. Price:</span>
                    <span className="font-semibold">$450,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>School Rating:</span>
                    <span className="font-semibold">9.2/10</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Walk Score:</span>
                    <span className="font-semibold">72/100</span>
                  </div>
                </div>
              </div>

              <div className="border border-border rounded-lg p-4">
                <h4 className="font-semibold mb-2">Waterfront District</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Avg. Price:</span>
                    <span className="font-semibold">$850,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>School Rating:</span>
                    <span className="font-semibold">8.8/10</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Walk Score:</span>
                    <span className="font-semibold">88/100</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4">Neighborhood Comparison Tool</h3>
            <p className="text-muted-foreground mb-4">
              Compare up to 3 neighborhoods side-by-side to make the best decision for your lifestyle and budget.
            </p>
            <button className="bg-primary text-primary-foreground px-6 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors">
              Start Comparison
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'buyer'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Neighborhood Insights for ${country} | SoNoBrokers`,
    description: `Discover the best neighborhoods in ${country}. School ratings, amenities, safety scores, and community insights to help you choose the perfect location.`,
    keywords: `neighborhoods, school ratings, community insights, ${country}, local amenities`,
  }
}
