using Dapper;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;

namespace MicroSaasWebApi.Services.SoNoBrokers
{
    public class ProjectService : IProjectService
    {
        private readonly MicroSaasWebApi.App.Context.IDapperDbContext _dbContext;
        private readonly ILogger<ProjectService> _logger;

        public ProjectService(MicroSaasWebApi.App.Context.IDapperDbContext dbContext, ILogger<ProjectService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<ProjectSearchResponse> GetProjectsAsync(ProjectSearchRequest request, string userClerkId)
        {
            try
            {
                var sql = @"
                    SELECT * FROM public.""Project""
                    WHERE ""userClerkId"" = @userClerkId";

                var parameters = new DynamicParameters();
                parameters.Add("userClerkId", userClerkId);

                var conditions = new List<string>();

                if (!string.IsNullOrEmpty(request.Type))
                {
                    conditions.Add(@"""type"" = @type");
                    parameters.Add("type", request.Type);
                }

                if (!string.IsNullOrEmpty(request.Status))
                {
                    conditions.Add(@"status = @status");
                    parameters.Add("status", request.Status);
                }

                if (conditions.Any())
                {
                    sql += " AND " + string.Join(" AND ", conditions);
                }

                // Add ordering and pagination
                sql += @" ORDER BY ""createdAt"" DESC LIMIT @limit OFFSET @offset";

                var offset = (request.Page - 1) * request.Limit;
                parameters.Add("limit", request.Limit);
                parameters.Add("offset", offset);

                var projects = await _dbContext.QueryAsync<Project>(sql, parameters);

                // Get total count
                var countSql = @"
                    SELECT COUNT(*)
                    FROM public.""Project""
                    WHERE ""userClerkId"" = @userClerkId";

                if (conditions.Any())
                {
                    countSql += " AND " + string.Join(" AND ", conditions);
                }

                var total = await _dbContext.QuerySingleAsync<int>(countSql, parameters);

                var response = new ProjectSearchResponse
                {
                    Projects = projects.Select(MapToResponse).ToList(),
                    Total = total,
                    Page = request.Page,
                    TotalPages = (int)Math.Ceiling((double)total / request.Limit),
                    HasMore = (request.Page * request.Limit) < total
                };

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting projects for user: {UserClerkId}", userClerkId);
                throw;
            }
        }

        public async Task<ProjectResponse?> GetProjectByIdAsync(string id, string userClerkId)
        {
            try
            {
                const string sql = @"
                    SELECT * FROM public.""Project""
                    WHERE id = @id AND ""userClerkId"" = @userClerkId";

                var project = await _dbContext.QueryFirstOrDefaultAsync<Project>(sql, new { id, userClerkId });
                return project != null ? MapToResponse(project) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting project by ID: {Id} for user: {UserClerkId}", id, userClerkId);
                throw;
            }
        }

        public async Task<ProjectResponse> CreateProjectAsync(CreateProjectRequest request, string userClerkId)
        {
            try
            {
                var id = Guid.NewGuid().ToString();
                var now = DateTime.UtcNow;

                const string sql = @"
                    INSERT INTO public.""Project"" (
                        id, ""connectionId"", ""webhookId"", ""scenarioId"", ""userClerkId"",
                        ""webhookLink"", ""assistantId"", type, status, ""createdAt"", ""updatedAt""
                    ) VALUES (
                        @id, @connectionId, @webhookId, @scenarioId, @userClerkId,
                        @webhookLink, @assistantId, @type, @status, @createdAt, @updatedAt
                    )";

                var parameters = new
                {
                    id,
                    connectionId = request.ConnectionId,
                    webhookId = request.WebhookId,
                    scenarioId = request.ScenarioId,
                    userClerkId,
                    webhookLink = request.WebhookLink,
                    assistantId = request.AssistantId,
                    type = request.Type,
                    status = request.Status,
                    createdAt = now,
                    updatedAt = now
                };

                await _dbContext.ExecuteAsync(sql, parameters);

                var created = await GetProjectByIdAsync(id, userClerkId);
                return created ?? throw new InvalidOperationException("Failed to retrieve created project");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating project for user: {UserClerkId}", userClerkId);
                throw;
            }
        }

        public async Task<ProjectResponse?> UpdateProjectAsync(UpdateProjectRequest request, string userClerkId)
        {
            try
            {
                var updates = new List<string>();
                var parameters = new DynamicParameters();
                parameters.Add("id", request.Id);
                parameters.Add("userClerkId", userClerkId);
                parameters.Add("updatedAt", DateTime.UtcNow);

                if (!string.IsNullOrEmpty(request.ConnectionId))
                {
                    updates.Add(@"""connectionId"" = @connectionId");
                    parameters.Add("connectionId", request.ConnectionId);
                }

                if (!string.IsNullOrEmpty(request.WebhookId))
                {
                    updates.Add(@"""webhookId"" = @webhookId");
                    parameters.Add("webhookId", request.WebhookId);
                }

                if (!string.IsNullOrEmpty(request.ScenarioId))
                {
                    updates.Add(@"""scenarioId"" = @scenarioId");
                    parameters.Add("scenarioId", request.ScenarioId);
                }

                if (!string.IsNullOrEmpty(request.WebhookLink))
                {
                    updates.Add(@"""webhookLink"" = @webhookLink");
                    parameters.Add("webhookLink", request.WebhookLink);
                }

                if (request.AssistantId != null)
                {
                    updates.Add(@"""assistantId"" = @assistantId");
                    parameters.Add("assistantId", request.AssistantId);
                }

                if (!string.IsNullOrEmpty(request.Type))
                {
                    updates.Add(@"type = @type");
                    parameters.Add("type", request.Type);
                }

                if (!string.IsNullOrEmpty(request.Status))
                {
                    updates.Add(@"status = @status");
                    parameters.Add("status", request.Status);
                }

                if (!updates.Any())
                {
                    return await GetProjectByIdAsync(request.Id, userClerkId);
                }

                updates.Add(@"""updatedAt"" = @updatedAt");

                var sql = $@"
                    UPDATE public.""Project""
                    SET {string.Join(", ", updates)}
                    WHERE id = @id AND ""userClerkId"" = @userClerkId";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, parameters);

                if (rowsAffected == 0)
                {
                    return null; // Project not found or user doesn't own it
                }

                return await GetProjectByIdAsync(request.Id, userClerkId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating project: {Id} for user: {UserClerkId}", request.Id, userClerkId);
                throw;
            }
        }

        public async Task<bool> DeleteProjectAsync(string id, string userClerkId)
        {
            try
            {
                const string sql = @"
                    DELETE FROM public.""Project""
                    WHERE id = @id AND ""userClerkId"" = @userClerkId";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, new { id, userClerkId });
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting project: {Id} for user: {UserClerkId}", id, userClerkId);
                throw;
            }
        }

        private static ProjectResponse MapToResponse(Project project)
        {
            return new ProjectResponse
            {
                Id = project.Id,
                ConnectionId = project.ConnectionId,
                WebhookId = project.WebhookId,
                ScenarioId = project.ScenarioId,
                UserClerkId = project.UserClerkId,
                WebhookLink = project.WebhookLink,
                AssistantId = project.AssistantId,
                Type = project.Type,
                Status = project.Status,
                CreatedAt = project.CreatedAt,
                UpdatedAt = project.UpdatedAt
            };
        }
    }
}
