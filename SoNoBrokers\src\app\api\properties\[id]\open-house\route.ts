import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { AIPropertyService } from '@/lib/ai-services';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const supabase = await createServerSupabaseClient();

    // Get the property data
    const { data: property, error } = await supabase
      .from('properties')
      .select('*')
      .eq('id', resolvedParams.id)
      .single();

    if (error || !property) {
      return NextResponse.json(
        { error: 'Property not found' },
        { status: 404 }
      );
    }

    // Generate the open house data
    const openHouseData = await AIPropertyService.generateOpenHouseData(property);

    return NextResponse.json(openHouseData);
  } catch (error) {
    console.error('Error generating open house data:', error);
    return NextResponse.json(
      { error: 'Failed to generate open house data' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const supabase = await createServerSupabaseClient();

    // Verify user authorization
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the property data and verify ownership
    const { data: property, error } = await supabase
      .from('properties')
      .select('*')
      .eq('id', resolvedParams.id)
      .eq('seller_id', user.id)
      .single();

    if (error || !property) {
      return NextResponse.json(
        { error: 'Property not found or access denied' },
        { status: 404 }
      );
    }

    // Parse request body for any custom options
    const body = await request.json();
    const { regenerate = false } = body;

    // Generate the open house data
    const openHouseData = await AIPropertyService.generateOpenHouseData(property);

    // Optionally save to database or cache here
    // For now, we'll just return the generated data

    return NextResponse.json(openHouseData);
  } catch (error) {
    console.error('Error generating open house data:', error);
    return NextResponse.json(
      { error: 'Failed to generate open house data' },
      { status: 500 }
    );
  }
}
