using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Data.Context;
using MicroSaasWebApi.Data.Repositories.Interfaces;
using System.Text;

namespace MicroSaasWebApi.Data.Repositories.SoNoBrokers
{
    /// <summary>
    /// Property repository implementation using Dapper
    /// </summary>
    public class PropertyRepository : IPropertyRepository
    {
        private readonly IDapperDbContext _dbContext;
        private readonly ILogger<PropertyRepository> _logger;

        public PropertyRepository(IDapperDbContext dbContext, ILogger<PropertyRepository> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<IEnumerable<Property>> GetAllAsync()
        {
            try
            {
                const string sql = @"
                    SELECT p.*, u.full_name as seller_name, u.email as seller_email
                    FROM snb.properties p
                    LEFT JOIN snb.users u ON p.seller_id = u.id
                    ORDER BY p.created_at DESC";

                return await _dbContext.QueryAsync<Property>(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all properties");
                throw;
            }
        }

        public async Task<Property?> GetByIdAsync(string id)
        {
            try
            {
                const string sql = @"
                    SELECT p.*, u.full_name as seller_name, u.email as seller_email
                    FROM snb.properties p
                    LEFT JOIN snb.users u ON p.seller_id = u.id
                    WHERE p.id = @Id";

                return await _dbContext.QueryFirstOrDefaultAsync<Property>(sql, new { Id = id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving property {PropertyId}", id);
                throw;
            }
        }

        public async Task<string> CreateAsync(Property property)
        {
            try
            {
                property.Id = Guid.NewGuid().ToString();
                property.CreatedAt = DateTime.UtcNow;
                property.UpdatedAt = DateTime.UtcNow;

                const string sql = @"
                    INSERT INTO snb.properties (
                        id, title, description, price, property_type, ad_type,
                        bedrooms, bathrooms, sqft, storeys, year_built,
                        lot_size, lot_size_unit, address, coordinates, features,
                        parking_types, extra_features, mls_number, listing_id,
                        province, postal_code, city, neighborhood, price_history,
                        virtual_tour, open_house, is_liked, liked_by, distance_filters,
                        ai_generated_description, ai_reports, open_house_qr_data,
                        walkability_score, neighborhood_score, ai_analysis_generated,
                        ai_generated_at, status, expires_at, listed_by_buyer,
                        buyer_id, amenities, seller_id, created_at, updated_at
                    ) VALUES (
                        @Id, @Title, @Description, @Price, @PropertyType, @AdType,
                        @Bedrooms, @Bathrooms, @Sqft, @Storeys, @YearBuilt,
                        @LotSize, @LotSizeUnit, @Address::jsonb, @Coordinates::jsonb, @Features::jsonb,
                        @ParkingTypes::jsonb, @ExtraFeatures::jsonb, @MlsNumber, @ListingId,
                        @Province, @PostalCode, @City, @Neighborhood, @PriceHistory::jsonb,
                        @VirtualTour, @OpenHouse::jsonb, @IsLiked, @LikedBy::jsonb, @DistanceFilters::jsonb,
                        @AiGeneratedDescription, @AiReports::jsonb, @OpenHouseQrData::jsonb,
                        @WalkabilityScore, @NeighborhoodScore, @AiAnalysisGenerated,
                        @AiGeneratedAt, @Status::snb.property_status, @ExpiresAt, @ListedByBuyer,
                        @BuyerId, @Amenities::jsonb, @SellerId, @CreatedAt, @UpdatedAt
                    )";

                await _dbContext.ExecuteAsync(sql, property);
                return property.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating property");
                throw;
            }
        }

        public async Task<bool> UpdateAsync(Property property)
        {
            try
            {
                property.UpdatedAt = DateTime.UtcNow;

                const string sql = @"
                    UPDATE snb.properties SET
                        title = @Title,
                        description = @Description,
                        price = @Price,
                        property_type = @PropertyType,
                        ad_type = @AdType,
                        bedrooms = @Bedrooms,
                        bathrooms = @Bathrooms,
                        sqft = @Sqft,
                        storeys = @Storeys,
                        year_built = @YearBuilt,
                        lot_size = @LotSize,
                        lot_size_unit = @LotSizeUnit,
                        address = @Address::jsonb,
                        parking_types = @ParkingTypes::jsonb,
                        extra_features = @ExtraFeatures::jsonb,
                        mls_number = @MlsNumber,
                        virtual_tour = @VirtualTour,
                        open_house = @OpenHouse::jsonb,
                        status = @Status::snb.property_status,
                        updated_at = @UpdatedAt
                    WHERE id = @Id";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, property);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating property {PropertyId}", property.Id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(string id)
        {
            try
            {
                const string sql = "DELETE FROM snb.properties WHERE id = @Id";
                var rowsAffected = await _dbContext.ExecuteAsync(sql, new { Id = id });
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting property {PropertyId}", id);
                throw;
            }
        }

        public async Task<(IEnumerable<Property> Properties, int TotalCount)> SearchAsync(
            string? searchTerm = null,
            string? city = null,
            string? province = null,
            decimal? minPrice = null,
            decimal? maxPrice = null,
            int? bedrooms = null,
            int? bathrooms = null,
            PropertyStatus? status = null,
            int page = 1,
            int pageSize = 20)
        {
            try
            {
                var whereConditions = new List<string>();
                var parameters = new Dictionary<string, object>();

                // Build WHERE conditions
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    whereConditions.Add("(p.title ILIKE @SearchTerm OR p.description ILIKE @SearchTerm OR p.property_type ILIKE @SearchTerm)");
                    parameters.Add("SearchTerm", $"%{searchTerm}%");
                }

                if (!string.IsNullOrEmpty(city))
                {
                    whereConditions.Add("p.address->>'city' ILIKE @City");
                    parameters.Add("City", $"%{city}%");
                }

                if (!string.IsNullOrEmpty(province))
                {
                    whereConditions.Add("p.address->>'province' ILIKE @Province");
                    parameters.Add("Province", $"%{province}%");
                }

                if (minPrice.HasValue)
                {
                    whereConditions.Add("p.price >= @MinPrice");
                    parameters.Add("MinPrice", minPrice.Value);
                }

                if (maxPrice.HasValue)
                {
                    whereConditions.Add("p.price <= @MaxPrice");
                    parameters.Add("MaxPrice", maxPrice.Value);
                }

                if (bedrooms.HasValue)
                {
                    whereConditions.Add("p.bedrooms >= @Bedrooms");
                    parameters.Add("Bedrooms", bedrooms.Value);
                }

                if (bathrooms.HasValue)
                {
                    whereConditions.Add("p.bathrooms >= @Bathrooms");
                    parameters.Add("Bathrooms", bathrooms.Value);
                }

                if (status.HasValue)
                {
                    whereConditions.Add("p.status = @Status::snb.property_status");
                    parameters.Add("Status", status.Value.ToString());
                }

                var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

                // Count query
                var countSql = $@"
                    SELECT COUNT(*)
                    FROM snb.properties p
                    LEFT JOIN snb.users u ON p.seller_id = u.id
                    {whereClause}";

                var totalCount = await _dbContext.QuerySingleAsync<int>(countSql, parameters);

                // Data query with pagination
                var offset = (page - 1) * pageSize;
                parameters.Add("Limit", pageSize);
                parameters.Add("Offset", offset);

                var dataSql = $@"
                    SELECT p.*, u.full_name as seller_name, u.email as seller_email
                    FROM snb.properties p
                    LEFT JOIN snb.users u ON p.seller_id = u.id
                    {whereClause}
                    ORDER BY p.created_at DESC
                    LIMIT @Limit OFFSET @Offset";

                var properties = await _dbContext.QueryAsync<Property>(dataSql, parameters);

                return (properties, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching properties");
                throw;
            }
        }

        public async Task<IEnumerable<Property>> GetBySellerIdAsync(string sellerId)
        {
            try
            {
                const string sql = @"
                    SELECT p.*, u.full_name as seller_name, u.email as seller_email
                    FROM snb.properties p
                    LEFT JOIN snb.users u ON p.seller_id = u.id
                    WHERE p.seller_id = @SellerId
                    ORDER BY p.created_at DESC";

                return await _dbContext.QueryAsync<Property>(sql, new { SellerId = sellerId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving properties for seller {SellerId}", sellerId);
                throw;
            }
        }

        public async Task<IEnumerable<Property>> GetActivePropertiesAsync()
        {
            try
            {
                const string sql = @"
                    SELECT p.*, u.full_name as seller_name, u.email as seller_email
                    FROM snb.properties p
                    LEFT JOIN snb.users u ON p.seller_id = u.id
                    WHERE p.status = 'active'::snb.property_status
                    ORDER BY p.created_at DESC";

                return await _dbContext.QueryAsync<Property>(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active properties");
                throw;
            }
        }

        public async Task<bool> UpdateStatusAsync(string id, PropertyStatus status)
        {
            try
            {
                const string sql = @"
                    UPDATE snb.properties 
                    SET status = @Status::snb.property_status, updated_at = @UpdatedAt
                    WHERE id = @Id";

                var rowsAffected = await _dbContext.ExecuteAsync(sql, new
                {
                    Id = id,
                    Status = status.ToString(),
                    UpdatedAt = DateTime.UtcNow
                });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating property status {PropertyId}", id);
                throw;
            }
        }

        public async Task<int> GetCountByStatusAsync(PropertyStatus status)
        {
            try
            {
                const string sql = @"
                    SELECT COUNT(*) 
                    FROM snb.properties 
                    WHERE status = @Status::snb.property_status";

                return await _dbContext.QuerySingleAsync<int>(sql, new { Status = status.ToString() });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property count by status {Status}", status);
                throw;
            }
        }
    }
}
