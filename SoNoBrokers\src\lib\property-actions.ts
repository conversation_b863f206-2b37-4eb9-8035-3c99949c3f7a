"use server"

import { redirect } from 'next/navigation'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function updateProperty(propertyId: string, data: any) {
  try {
    const supabase = await createServerSupabaseClient();
    const { error } = await supabase
      .from('properties')
      .update(data)
      .eq('id', propertyId);

    if (error) {
      throw new Error('Failed to update property');
    }

    redirect('/properties/' + propertyId);
  } catch (error) {
    console.error('Error updating property:', error);
    throw error;
  }
}
