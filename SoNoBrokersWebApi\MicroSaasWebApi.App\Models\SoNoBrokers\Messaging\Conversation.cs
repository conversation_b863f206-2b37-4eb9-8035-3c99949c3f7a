using System.ComponentModel.DataAnnotations;

namespace MicroSaasWebApi.Models.SoNoBrokers.Messaging
{
    /// <summary>
    /// Represents a conversation between users about a property
    /// </summary>
    public class Conversation
    {
        [Key]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Property that the conversation is about
        /// </summary>
        public string? PropertyId { get; set; }

        /// <summary>
        /// Buyer participating in the conversation
        /// </summary>
        public string? BuyerId { get; set; }

        /// <summary>
        /// Seller participating in the conversation
        /// </summary>
        public string? SellerId { get; set; }

        /// <summary>
        /// Agent participating in the conversation (optional)
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// Subject/title of the conversation
        /// </summary>
        public string? Subject { get; set; }

        /// <summary>
        /// Timestamp of the last message in this conversation
        /// </summary>
        public DateTime? LastMessageAt { get; set; }

        /// <summary>
        /// Whether the conversation is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// When the conversation was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// When the conversation was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Property? Property { get; set; }
        public virtual User? Buyer { get; set; }
        public virtual User? Seller { get; set; }
        public virtual User? Agent { get; set; }
        public virtual ICollection<Message> Messages { get; set; } = new List<Message>();
    }

    /// <summary>
    /// DTO for creating a new conversation
    /// </summary>
    public class CreateConversationRequest
    {
        [Required]
        public string PropertyId { get; set; } = string.Empty;

        [Required]
        public string SellerId { get; set; } = string.Empty;

        public string? Subject { get; set; }

        [Required]
        [StringLength(1000, MinimumLength = 1)]
        public string InitialMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for conversation response
    /// </summary>
    public class ConversationResponse
    {
        public string Id { get; set; } = string.Empty;
        public string? PropertyId { get; set; }
        public string? PropertyTitle { get; set; }
        public string? PropertyAddress { get; set; }
        public decimal? PropertyPrice { get; set; }
        public string? BuyerId { get; set; }
        public string? BuyerName { get; set; }
        public string? BuyerEmail { get; set; }
        public string? SellerId { get; set; }
        public string? SellerName { get; set; }
        public string? SellerEmail { get; set; }
        public string? AgentId { get; set; }
        public string? AgentName { get; set; }
        public string? Subject { get; set; }
        public DateTime? LastMessageAt { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public int UnreadCount { get; set; }
        public MessageResponse? LastMessage { get; set; }
        public List<MessageResponse> Messages { get; set; } = new();
    }

    /// <summary>
    /// DTO for conversation list item
    /// </summary>
    public class ConversationListItem
    {
        public string Id { get; set; } = string.Empty;
        public string? PropertyId { get; set; }
        public string? PropertyTitle { get; set; }
        public string? PropertyAddress { get; set; }
        public decimal? PropertyPrice { get; set; }
        public string? OtherParticipantId { get; set; }
        public string? OtherParticipantName { get; set; }
        public string? OtherParticipantEmail { get; set; }
        public string? Subject { get; set; }
        public DateTime? LastMessageAt { get; set; }
        public string? LastMessageContent { get; set; }
        public string? LastMessageSenderId { get; set; }
        public bool IsActive { get; set; }
        public int UnreadCount { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// DTO for conversation search parameters
    /// </summary>
    public class ConversationSearchParams
    {
        public string? PropertyId { get; set; }
        public string? UserId { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? Search { get; set; }
        public int Page { get; set; } = 1;
        public int Limit { get; set; } = 20;
        public string? SortBy { get; set; } = "lastMessageAt";
        public string? SortOrder { get; set; } = "desc";
    }

    /// <summary>
    /// DTO for conversation search response
    /// </summary>
    public class ConversationSearchResponse
    {
        public List<ConversationListItem> Conversations { get; set; } = new();
        public int Total { get; set; }
        public int Page { get; set; }
        public int TotalPages { get; set; }
        public bool HasMore { get; set; }
    }

    /// <summary>
    /// DTO for updating conversation
    /// </summary>
    public class UpdateConversationRequest
    {
        public string? Subject { get; set; }
        public bool? IsActive { get; set; }
    }

    /// <summary>
    /// DTO for conversation participants
    /// </summary>
    public class ConversationParticipant
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty; // buyer, seller, agent
        public bool IsOnline { get; set; }
        public DateTime? LastSeenAt { get; set; }
    }
}
