import InvoiceTemplate from '@/components/_archive/email-templates/Invoice'
import ThankYouTemplate from '@/components/_archive/email-templates/ThanksYouTemplate'
import config from '@/config'
import prisma from '@/lib/prisma'
import { Resend } from 'resend'

class ResendService {
	private resend = new Resend(process.env.RESEND_API_KEY)

	public async sendThanksYouEmail(toMail: string) {
		const { data, error } = await this.resend.emails.send({
			from: config.resend.fromAdmin,
			to: [toMail],
			replyTo: config.resend.forwardRepliesTo,
			subject: config.resend.subjects.thankYou,
			react: ThankYouTemplate({ email: toMail }),
		})

		if (error) {
			throw error
		}

		return data
	}

	public async sendInvoice(toMail: string, renderData: any) {
		const { data, error } = await this.resend.emails.send({
			from: config.resend.fromAdmin,
			to: [toMail],
			replyTo: config.resend.forwardRepliesTo,
			subject: 'Invoice: ' + renderData.id,
			react: InvoiceTemplate(renderData),
		})

		if (error) {
			throw error
		}

		return data
	}

	public async addNewEmailAddress(email: string) {
		const audience = await this.upsertAudience()
		return this.resend.contacts.create({
			email,
			unsubscribed: false,
			audienceId: audience.resend_id,
		})
	}

	public async sendConciergeInquiry(toEmail: string, subject: string, content: string) {
		const { data, error } = await this.resend.emails.send({
			from: config.resend.fromAdmin,
			to: [toEmail],
			replyTo: config.resend.forwardRepliesTo,
			subject: subject,
			text: content,
		})

		if (error) {
			throw error
		}

		return data
	}

	public async sendConciergeConfirmation(toEmail: string, name: string, packageName: string) {
		const { data, error } = await this.resend.emails.send({
			from: config.resend.fromAdmin,
			to: [toEmail],
			replyTo: config.resend.forwardRepliesTo,
			subject: 'Thank you for your Concierge Services inquiry',
			text: `Dear ${name},

Thank you for your interest in our ${packageName} service. We have received your inquiry and our concierge team will contact you within 24 hours to discuss your property sale needs.

In the meantime, if you have any urgent questions, please don't hesitate to contact <NAME_EMAIL>.

Best regards,
The SoNoBrokers Concierge Team

---
This is an automated confirmation email from SoNoBrokers.`,
		})

		if (error) {
			throw error
		}

		return data
	}

	private async upsertAudience() {
		// TODO: Add audiences table to Prisma schema if needed
		// const audience = await prisma.audiences.findFirst()

		// if (audience) {
		// 	return audience
		// }

		const resendAudience = await this.resend.audiences.create({
			name: 'Waiting List',
		})
		const {
			data: { id, name },
		} = resendAudience

		// TODO: Uncomment when audiences table is added to schema
		// return prisma.audiences.create({
		// 	data: {
		// 		resend_id: id,
		// 		name,
		// 	},
		// })

		// Return mock data for now
		return { resend_id: id, name }
	}
}

export const resendService = new ResendService()
