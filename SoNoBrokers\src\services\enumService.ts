"use server";

// MIGRATED TO .NET WEB API - Use enum-actions.ts instead
// import { prisma } from '@/lib/prisma';

/**
 * Enum Service for dynamic enum fetching from database
 * MIGRATED TO .NET WEB API - Use enum-actions.ts instead
 *
 * @deprecated Use enum-actions.ts Server Actions instead
 */

// Types for backward compatibility
export interface CountryInfo {
  code: string
  name: string
  flag: string
  value: string
}

export interface UserTypeInfo {
  value: string
  label: string
}

// Fetch countries from database enum
export async function getCountriesFromDatabase() {
  throw new Error('MIGRATED TO .NET WEB API - Use getCountriesAction from enum-actions.ts instead');
}

// Fetch user types from database enum
export async function getUserTypesFromDatabase() {
  throw new Error('MIGRATED TO .NET WEB API - Use getUserTypesAction from enum-actions.ts instead');
}

// Clear cache (useful for testing or when you know enum values have changed)
export async function clearEnumCache() {
  throw new Error('MIGRATED TO .NET WEB API - Use clearEnumCacheAction from enum-actions.ts instead');
}

// Get all enum values at once
export async function getAllEnumValues() {
  throw new Error('MIGRATED TO .NET WEB API - Use getEnumValuesAction from enum-actions.ts instead');
}
