using MicroSaasWebApi.Models.SoNoBrokers;

namespace MicroSaasWebApi.Services.SoNoBrokers.Interfaces
{
    public interface ISubscriptionService
    {
        Task<IEnumerable<Subscription>> GetAllSubscriptionsAsync();
        Task<Subscription?> GetSubscriptionByIdAsync(string id);
        Task<Subscription?> GetSubscriptionByStripeIdAsync(string stripeSubscriptionId);
        Task<IEnumerable<Subscription>> GetUserSubscriptionsAsync(string userId);
        Task<Subscription> CreateSubscriptionAsync(Subscription subscription);
        Task<Subscription> UpdateSubscriptionAsync(Subscription subscription);
        Task<bool> DeleteSubscriptionAsync(string id);
        Task<bool> CancelSubscriptionAsync(string id);
        Task<bool> SubscriptionExistsAsync(string id);
        Task<bool> StripeSubscriptionExistsAsync(string stripeSubscriptionId);
        Task<Subscription?> GetActiveSubscriptionAsync(string userId);
    }
}
