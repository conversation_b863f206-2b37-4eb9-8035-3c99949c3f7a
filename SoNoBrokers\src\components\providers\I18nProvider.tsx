'use client'

import { <PERSON>actN<PERSON>, useEffect, useState } from 'react'
import { I18nextProvider } from 'react-i18next'
import i18n from '@/lib/i18n'

interface I18nProviderProps {
  children: ReactNode
}

export function I18nProvider({ children }: I18nProviderProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)

    // Initialize i18n language detection on mount
    if (!i18n.isInitialized) {
      i18n.init()
    }
  }, [])

  // Don't render i18n provider until mounted to avoid hydration issues
  if (!mounted) {
    return <>{children}</>
  }

  return (
    <I18nextProvider i18n={i18n}>
      {children}
    </I18nextProvider>
  )
}
