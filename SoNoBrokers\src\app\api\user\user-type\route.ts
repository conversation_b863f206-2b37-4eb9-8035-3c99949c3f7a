import { NextRequest, NextResponse } from 'next/server'
import { UserService } from '@/services/userService'
import { requireAuth } from '@/lib/auth'
import { SnbUserType } from '@/types'

// PATCH /api/user/user-type - Update user type (Buyer/Seller)
export async function PATCH(request: NextRequest) {
  try {
    const user = await requireAuth()
    const body = await request.json()
    
    const { userType } = body

    // Validate userType
    if (!userType || ![SnbUserType.Buyer, SnbUserType.Seller].includes(userType)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user type. Only Buyer and Seller are allowed.' },
        { status: 400 }
      )
    }

    const updatedUser = await UserService.updateUserType(user.id, userType)

    if (!updatedUser) {
      return NextResponse.json(
        { success: false, error: 'Failed to update user type' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        id: updatedUser.id,
        email: updatedUser.email,
        userType: updatedUser.userType
      },
      message: `User type updated to ${userType} successfully`
    })
  } catch (error) {
    console.error('Error updating user type:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update user type' },
      { status: 500 }
    )
  }
}
