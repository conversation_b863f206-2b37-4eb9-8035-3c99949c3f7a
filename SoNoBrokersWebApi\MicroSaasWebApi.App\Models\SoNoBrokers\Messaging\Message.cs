using System.ComponentModel.DataAnnotations;

namespace MicroSaasWebApi.Models.SoNoBrokers.Messaging
{
    /// <summary>
    /// Represents a message in a conversation
    /// </summary>
    public class Message
    {
        [Key]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// ID of the conversation this message belongs to
        /// </summary>
        [Required]
        public string ConversationId { get; set; } = string.Empty;

        /// <summary>
        /// ID of the user who sent this message
        /// </summary>
        [Required]
        public string SenderId { get; set; } = string.Empty;

        /// <summary>
        /// Content of the message
        /// </summary>
        [Required]
        [StringLength(2000, MinimumLength = 1)]
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// Type of message (text, system, etc.)
        /// </summary>
        public string MessageType { get; set; } = "text";

        /// <summary>
        /// Attachments (for future use)
        /// </summary>
        public string? Attachments { get; set; }

        /// <summary>
        /// Whether the message has been read
        /// </summary>
        public bool IsRead { get; set; } = false;

        /// <summary>
        /// When the message was read
        /// </summary>
        public DateTime? ReadAt { get; set; }

        /// <summary>
        /// When the message was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Conversation? Conversation { get; set; }
        public virtual User? Sender { get; set; }
    }

    /// <summary>
    /// DTO for creating a new message
    /// </summary>
    public class CreateMessageRequest
    {
        [Required]
        public string ConversationId { get; set; } = string.Empty;

        [Required]
        [StringLength(2000, MinimumLength = 1)]
        public string Content { get; set; } = string.Empty;

        public string MessageType { get; set; } = "text";
    }

    /// <summary>
    /// DTO for message response
    /// </summary>
    public class MessageResponse
    {
        public string Id { get; set; } = string.Empty;
        public string ConversationId { get; set; } = string.Empty;
        public string SenderId { get; set; } = string.Empty;
        public string SenderName { get; set; } = string.Empty;
        public string SenderEmail { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string MessageType { get; set; } = string.Empty;
        public bool IsRead { get; set; }
        public DateTime? ReadAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsOwnMessage { get; set; }
    }

    /// <summary>
    /// DTO for message search parameters
    /// </summary>
    public class MessageSearchParams
    {
        public string? ConversationId { get; set; }
        public string? SenderId { get; set; }
        public string? Search { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool? IsRead { get; set; }
        public int Page { get; set; } = 1;
        public int Limit { get; set; } = 50;
        public string? SortBy { get; set; } = "createdAt";
        public string? SortOrder { get; set; } = "asc";
    }

    /// <summary>
    /// DTO for message search response
    /// </summary>
    public class MessageSearchResponse
    {
        public List<MessageResponse> Messages { get; set; } = new();
        public int Total { get; set; }
        public int Page { get; set; }
        public int TotalPages { get; set; }
        public bool HasMore { get; set; }
    }

    /// <summary>
    /// DTO for marking messages as read
    /// </summary>
    public class MarkMessagesReadRequest
    {
        public List<string> MessageIds { get; set; } = new();
    }

    /// <summary>
    /// DTO for bulk message operations
    /// </summary>
    public class BulkMessageRequest
    {
        public List<string> MessageIds { get; set; } = new();
        public string Action { get; set; } = string.Empty; // read, unread, delete
    }

    /// <summary>
    /// DTO for message statistics
    /// </summary>
    public class MessageStats
    {
        public int TotalMessages { get; set; }
        public int UnreadMessages { get; set; }
        public int TotalConversations { get; set; }
        public int ActiveConversations { get; set; }
        public DateTime? LastMessageAt { get; set; }
        public List<ConversationStats> ConversationStats { get; set; } = new();
    }

    /// <summary>
    /// DTO for conversation statistics
    /// </summary>
    public class ConversationStats
    {
        public string ConversationId { get; set; } = string.Empty;
        public string? PropertyTitle { get; set; }
        public int MessageCount { get; set; }
        public int UnreadCount { get; set; }
        public DateTime? LastMessageAt { get; set; }
        public string? LastMessageSender { get; set; }
    }

    /// <summary>
    /// DTO for real-time message notification
    /// </summary>
    public class MessageNotification
    {
        public string MessageId { get; set; } = string.Empty;
        public string ConversationId { get; set; } = string.Empty;
        public string SenderId { get; set; } = string.Empty;
        public string SenderName { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string? PropertyId { get; set; }
        public string? PropertyTitle { get; set; }
        public DateTime CreatedAt { get; set; }
        public List<string> RecipientIds { get; set; } = new();
    }

    /// <summary>
    /// DTO for typing indicator
    /// </summary>
    public class TypingIndicator
    {
        public string ConversationId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public bool IsTyping { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }
}
