# 🚀 MicroSaaS Web API

A modern, multi-tenant SaaS template built with .NET 9, featuring clean architecture, proper middleware ordering, and comprehensive multi-tenant support.

## ⚡ Quick Start

```bash
# Clone and run
git clone <repository-url>
cd MicroSaasWebApiRoot
dotnet build
dotnet run --project MicroSaasWebApi.Full
```

**🌐 Access the API**: https://localhost:7000/swagger

## 📚 Documentation

All comprehensive documentation is located in the [`/Documentation`](./Documentation/) folder:

### 🎯 **Start Here**

- **[📋 DOCUMENTATION-INDEX.md](./Documentation/DOCUMENTATION-INDEX.md)** - Complete documentation index
- **[⚡ QUICK-START.md](./Documentation/QUICK-START.md)** - 5-minute setup guide
- **[📖 README.md](./Documentation/README.md)** - Detailed project overview

### 🏗️ **Architecture & Patterns**

- **[🏗️ ARCHITECTURE-DOCUMENTATION.md](./Documentation/ARCHITECTURE-DOCUMENTATION.md)** - Complete architecture overview
- **[🏢 MULTI-TENANT-IMPLEMENTATION-GUIDE.md](./Documentation/MULTI-TENANT-IMPLEMENTATION-GUIDE.md)** - Multi-tenant setup
- **[⚙️ MIDDLEWARE-PATTERNS-GUIDE.md](./Documentation/MIDDLEWARE-PATTERNS-GUIDE.md)** - Middleware ordering patterns
- **[🚀 DOTNET9-PATTERNS-GUIDE.md](./Documentation/DOTNET9-PATTERNS-GUIDE.md)** - Modern .NET 9 patterns

### 🔧 **Setup & Troubleshooting**

- **[📋 PROJECT-EXECUTION-GUIDE.md](./Documentation/PROJECT-EXECUTION-GUIDE.md)** - Comprehensive execution guide
- **[🌍 CROSS-PLATFORM-GUIDE.md](./Documentation/CROSS-PLATFORM-GUIDE.md)** - Windows, Mac, Linux setup
- **[🔧 SETUP-TROUBLESHOOTING.md](./Documentation/SETUP-TROUBLESHOOTING.md)** - Common issues & solutions

## ✨ Key Features

### ✅ **Clean Architecture**

- **Modern namespace structure**: `Base`, `Tenant`, `Core` (replaces legacy `PXW`, `AlphaCCO`, `MICROSAAS`)
- **Separation of concerns** with clear boundaries
- **SOLID principles** throughout the codebase

### ✅ **Multi-Tenant Support**

- **5 tenant resolution strategies**: Subdomain, Header, Path, Query, JWT Claims
- **Complete data isolation** with tenant-scoped services
- **Per-tenant customization**: Features, branding, limits
- **Scalable architecture** for thousands of tenants

### ✅ **Proper Middleware Ordering**

- **16-step ordered pipeline** with Chain of Responsibility pattern
- **Security-first approach** with early security middleware
- **Performance optimized** request processing
- **Extensible design** for custom middleware

### ✅ **Modern .NET 9 Patterns**

- **Minimal APIs** with filters and validation
- **Keyed dependency injection** for multi-provider scenarios
- **Strongly-typed configuration** with validation
- **Background services** with proper cancellation
- **Comprehensive health checks**

### ✅ **Production Ready**

- **Authentication & Authorization** with JWT/OAuth
- **Rate limiting** and security headers
- **Comprehensive logging** and monitoring
- **Error handling** with global exception middleware
- **Health checks** and metrics

## 🛠️ Tech Stack

- **Backend**: .NET 9, ASP.NET Core, Entity Framework Core
- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Authentication**: Clerk SDK
- **Payments**: Stripe integration
- **Database**: PostgreSQL with multi-tenant support
- **Caching**: Redis for distributed caching
- **Monitoring**: Health checks, logging, metrics

## 🚀 Getting Started

1. **📖 Read the documentation**: Start with [`/Documentation/QUICK-START.md`](./Documentation/QUICK-START.md)
2. **🔧 Set up your environment**: Follow the cross-platform guide
3. **🏗️ Understand the architecture**: Review the architecture documentation
4. **🏢 Configure multi-tenancy**: Set up tenant resolution
5. **🚀 Deploy**: Use the production deployment guide

## 📁 Project Structure

```
MicroSaasWebApiRoot/
├── Documentation/              # 📚 All documentation
├── MicroSaasWebApi.Full/      # 🎯 Main API project
├── MicroSaasWebApi.Full.Tests.Unit/ # 🧪 Unit tests
├── MicroSaasWebApiUI/         # 🌐 Next.js frontend
├── MicroSaasWebApi-Config/    # 🔧 Configuration & API testing
├── azure_pipelines/           # 🚀 Multi-tenant CI/CD pipelines
└── README.md                  # 📖 This file
```

## 🤝 Contributing

1. Read the [Architecture Documentation](./Documentation/ARCHITECTURE-DOCUMENTATION.md)
2. Follow the [.NET 9 Patterns Guide](./Documentation/DOTNET9-PATTERNS-GUIDE.md)
3. Ensure all tests pass: `dotnet test`
4. Follow the established namespace structure

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**🎯 Ready to build your SaaS?** Start with the [Quick Start Guide](./Documentation/QUICK-START.md)!
