namespace MicroSaasWebApi.Services.Logging.Interfaces
{
    public interface IBaseLoggerService
    {
        Task LogInformationAsync(string message);
        Task LogWarningAsync(string message);
        Task LogErrorAsync(string message);
        Task LogErrorAsync(Exception exception, string message);
        Task LogRequestInformationAsync();
        Task LogDebugAsync(string message);
        Task SetResponseAndLogAsync(int statusCode, string message, Exception? e = null);
        Task LogInformationAndRequestAsync(string message);
    }
}
