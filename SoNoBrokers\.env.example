#MAKE

MAKE_ORGANIZATION_ID=
MAKE_TEAM_ID=
MAKE_API_KEY=
MAKE_API_URL=

#N8N
N8N_API_KEY=
N8N_API_URL=
N8N_WEBHOOK_URL=

POSTGRES_USER=postgres
POSTGRES_PASSWORD=Shared4w0rk!!!
POSTGRES_DBNAME=postgres
DATABASE_HOST=db.yfznlsisxsnymkvydzha.supabase.co
DATABASE_PORT=5432

#DB PRISMA
DATABASE_URL=*****************************************************************************************************/postgres

NEXT_PUBLIC_MAPBOX_API_KEY=pk.eyJ1IjoiamF2aWFucGljYXJkbzMzIiwiYSI6ImNtYjY4ZGRyazBiaWYybHEyMWpnNGN4cDQifQ.m63aGFvbfzrQhT3sWlSbDQ
# Stripe keys
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_51Qfy9dP82YH9JfOlFmwTk0RHvE7l6sow7ImJBI4c1GzcbhFydoVpb3DMevP8B3C4gj95qCoEise8vEhgFgWxhLEN000RfInKNH
STRIPE_SECRET_KEY=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
STRIPE_WEBHOOK_SECRET=

# Clerk keys - PRODUCTION (requires custom domain setup)
# NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsuc29ub2Jyb2tlcnMuY29tJA
# CLERK_SECRET_KEY=**************************************************
#TEST - Use these for development
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_YmV0dGVyLXBvc3N1bS01OC5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_SECRET_KEY=sk_test_VpPsw9paA18Uka7jxuB6PeE3bLvzKVEESGaDuysAgL
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up

# Supabase keys
NEXT_PUBLIC_SUPABASE_URL=https://yfznlsisxsnymkvydzha.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.qUBshv6Wi-90ZAEnUM2RXuhR77QFHnmEpI6O-y7l3BE

# Your app's base URL
NEXT_PUBLIC_APP_URL=http://localhost:3000

#Resend
RESEND_API_KEY=re_BM6pyT8x_ChaS1fbRCbdprxPy1fwimWCs



# Feature Flags
NEXT_PUBLIC_ENABLE_GOOGLE_PROVIDERS=true
NEXT_PUBLIC_LAUNCH_MODE=false
NEXT_PUBLIC_ENABLE_LANGUAGE_SELECTOR=false
NEXT_PUBLIC_ENABLE_REGION_TESTER=true
NEXT_PUBLIC_ENABLE_ARCHIVE_MIGRATION=true

# Google Places API (for non-registered service providers)
NEXT_PUBLIC_GOOGLE_PLACES_API_KEY=your_google_places_api_key_here

# Development Features
NEXT_PUBLIC_DEV_MODE=true
NEXT_PUBLIC_SHOW_DEBUG_INFO=false

# Component Features
NEXT_PUBLIC_ENABLE_PROPERTY_FAVORITES=true
NEXT_PUBLIC_ENABLE_OPEN_HOUSE_FAVORITES=true
NEXT_PUBLIC_ENABLE_SERVICE_BOOKING=true

# Country Support
NEXT_PUBLIC_SUPPORTED_COUNTRIES=CA,US,UAE
NEXT_PUBLIC_DEFAULT_COUNTRY=CA

# Service Features
NEXT_PUBLIC_ENABLE_AI_PROPERTY_CREATOR=true
NEXT_PUBLIC_ENABLE_MORTGAGE_CALCULATOR=true
NEXT_PUBLIC_ENABLE_COMMISSION_CALCULATOR=true

# Authentication Features
NEXT_PUBLIC_ENABLE_ROLE_BASED_AUTH=true
NEXT_PUBLIC_ADMIN_EMAIL=<EMAIL>
