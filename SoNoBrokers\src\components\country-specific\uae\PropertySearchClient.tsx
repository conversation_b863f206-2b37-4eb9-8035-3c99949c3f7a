'use client'

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';

const UAE_EMIRATES = [
  'Dubai',
  'Abu Dhabi',
  'Sharjah',
  'Ajman',
  'Ras Al Khaimah',
  'Fu<PERSON>ira<PERSON>',
  '<PERSON><PERSON> Al Quwain'
];

const DUBAI_AREAS = [
  'Downtown Dubai', 'Dubai Marina', 'Jumeirah Beach Residence', 'Business Bay',
  'Dubai Hills Estate', 'Arabian Ranches', 'The Springs', 'The Meadows',
  'Jumeirah Village Circle', 'Dubai Investment Park'
];

const ABU_DHABI_AREAS = [
  'Corniche Area', 'Al Reem Island', 'Saadiyat Island', 'Yas Island',
  'Al Reef', 'Khalifa City', 'Mohammed Bin Zayed City', 'Masdar City'
];

interface PropertySearchProps {
  userType: 'buyer' | 'seller'
  onViewProperties?: () => void
}

export function PropertySearch({ userType, onViewProperties }: PropertySearchProps) {
  const [searchParams, setSearchParams] = useState({
    emirate: '',
    area: '',
    propertyType: '',
    minPrice: '',
    maxPrice: '',
    bedrooms: '',
    bathrooms: '',
    furnishing: ''
  });

  const getAreasForEmirate = (emirate: string) => {
    switch (emirate) {
      case 'dubai':
        return DUBAI_AREAS;
      case 'abu-dhabi':
        return ABU_DHABI_AREAS;
      default:
        return [];
    }
  };

  const handleSearch = () => {
    console.log('Searching UAE properties with params:', searchParams);
    // Implement search logic
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardContent className="p-6">
        <h2 className="text-2xl font-bold mb-6 text-center">Search UAE Properties</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium mb-2">Emirate</label>
            <Select value={searchParams.emirate} onValueChange={(value) => setSearchParams(prev => ({ ...prev, emirate: value, area: '' }))}>
              <SelectTrigger>
                <SelectValue placeholder="Select emirate" />
              </SelectTrigger>
              <SelectContent>
                {UAE_EMIRATES.map((emirate) => (
                  <SelectItem key={emirate} value={emirate.toLowerCase().replace(' ', '-')}>
                    {emirate}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Area</label>
            <Select
              value={searchParams.area}
              onValueChange={(value) => setSearchParams(prev => ({ ...prev, area: value }))}
              disabled={!searchParams.emirate}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select area" />
              </SelectTrigger>
              <SelectContent>
                {getAreasForEmirate(searchParams.emirate).map((area) => (
                  <SelectItem key={area} value={area.toLowerCase().replace(' ', '-')}>
                    {area}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Property Type</label>
            <Select value={searchParams.propertyType} onValueChange={(value) => setSearchParams(prev => ({ ...prev, propertyType: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Property type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="apartment">Apartment</SelectItem>
                <SelectItem value="villa">Villa</SelectItem>
                <SelectItem value="townhouse">Townhouse</SelectItem>
                <SelectItem value="penthouse">Penthouse</SelectItem>
                <SelectItem value="studio">Studio</SelectItem>
                <SelectItem value="office">Office</SelectItem>
                <SelectItem value="retail">Retail</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Min Price (AED)</label>
            <Input
              type="number"
              placeholder="0"
              value={searchParams.minPrice}
              onChange={(e) => setSearchParams(prev => ({ ...prev, minPrice: e.target.value }))}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Max Price (AED)</label>
            <Input
              type="number"
              placeholder="10,000,000"
              value={searchParams.maxPrice}
              onChange={(e) => setSearchParams(prev => ({ ...prev, maxPrice: e.target.value }))}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Bedrooms</label>
            <Select value={searchParams.bedrooms} onValueChange={(value) => setSearchParams(prev => ({ ...prev, bedrooms: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Any" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="studio">Studio</SelectItem>
                <SelectItem value="1">1 BR</SelectItem>
                <SelectItem value="2">2 BR</SelectItem>
                <SelectItem value="3">3 BR</SelectItem>
                <SelectItem value="4">4 BR</SelectItem>
                <SelectItem value="5">5+ BR</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Furnishing</label>
            <Select value={searchParams.furnishing} onValueChange={(value) => setSearchParams(prev => ({ ...prev, furnishing: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Any" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="furnished">Furnished</SelectItem>
                <SelectItem value="semi-furnished">Semi-Furnished</SelectItem>
                <SelectItem value="unfurnished">Unfurnished</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Button onClick={handleSearch} className="w-full bg-emerald-600 hover:bg-emerald-700">
          Search Properties
        </Button>
      </CardContent>
    </Card>
  );
}
