'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Clock, 
  Plus, 
  Edit, 
  Trash2, 
  Calendar,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface SellerAvailability {
  id: string
  propertyId: string
  propertyTitle: string
  dayOfWeek: number
  dayOfWeekDisplay: string
  startTime: string
  endTime: string
  timeRange: string
  isAvailable: boolean
  notes?: string
  createdAt: string
  updatedAt: string
}

interface SellerAvailabilityManagerProps {
  propertyId: string
  propertyTitle: string
}

const DAYS_OF_WEEK = [
  { value: 0, label: 'Sunday' },
  { value: 1, label: 'Monday' },
  { value: 2, label: 'Tuesday' },
  { value: 3, label: 'Wednesday' },
  { value: 4, label: 'Thursday' },
  { value: 5, label: 'Friday' },
  { value: 6, label: 'Saturday' },
]

export function SellerAvailabilityManager({ propertyId, propertyTitle }: SellerAvailabilityManagerProps) {
  const [availability, setAvailability] = useState<SellerAvailability[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingAvailability, setEditingAvailability] = useState<SellerAvailability | null>(null)

  // Form state
  const [selectedDay, setSelectedDay] = useState<number>(1)
  const [startTime, setStartTime] = useState('09:00')
  const [endTime, setEndTime] = useState('17:00')
  const [isAvailable, setIsAvailable] = useState(true)
  const [notes, setNotes] = useState('')

  useEffect(() => {
    loadAvailability()
  }, [propertyId])

  const loadAvailability = async () => {
    try {
      setIsLoading(true)
      // TODO: Replace with actual API call
      // const response = await getSellerAvailability(propertyId)
      // setAvailability(response)
      
      // Mock data for now
      setAvailability([
        {
          id: '1',
          propertyId,
          propertyTitle,
          dayOfWeek: 1,
          dayOfWeekDisplay: 'Monday',
          startTime: '09:00',
          endTime: '17:00',
          timeRange: '09:00 - 17:00',
          isAvailable: true,
          notes: 'Available for showings',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ])
    } catch (error) {
      console.error('Failed to load availability:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveAvailability = async () => {
    try {
      const availabilityData = {
        propertyId,
        dayOfWeek: selectedDay,
        startTime,
        endTime,
        isAvailable,
        notes: notes || undefined
      }

      if (editingAvailability) {
        // TODO: Update existing availability
        console.log('Updating availability:', availabilityData)
      } else {
        // TODO: Create new availability
        console.log('Creating availability:', availabilityData)
      }

      // Reload availability
      await loadAvailability()
      
      // Reset form
      resetForm()
      setIsDialogOpen(false)
    } catch (error) {
      console.error('Failed to save availability:', error)
    }
  }

  const handleEditAvailability = (availability: SellerAvailability) => {
    setEditingAvailability(availability)
    setSelectedDay(availability.dayOfWeek)
    setStartTime(availability.startTime)
    setEndTime(availability.endTime)
    setIsAvailable(availability.isAvailable)
    setNotes(availability.notes || '')
    setIsDialogOpen(true)
  }

  const handleDeleteAvailability = async (availabilityId: string) => {
    try {
      // TODO: Delete availability
      console.log('Deleting availability:', availabilityId)
      
      // Reload availability
      await loadAvailability()
    } catch (error) {
      console.error('Failed to delete availability:', error)
    }
  }

  const resetForm = () => {
    setEditingAvailability(null)
    setSelectedDay(1)
    setStartTime('09:00')
    setEndTime('17:00')
    setIsAvailable(true)
    setNotes('')
  }

  const handleDialogClose = () => {
    setIsDialogOpen(false)
    resetForm()
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Availability Schedule
            </CardTitle>
            <CardDescription>
              Set your availability for property showings
            </CardDescription>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => setIsDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Availability
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {editingAvailability ? 'Edit' : 'Add'} Availability
                </DialogTitle>
                <DialogDescription>
                  Set your availability for {propertyTitle}
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="dayOfWeek">Day of Week</Label>
                  <Select value={selectedDay.toString()} onValueChange={(value) => setSelectedDay(parseInt(value))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select day" />
                    </SelectTrigger>
                    <SelectContent>
                      {DAYS_OF_WEEK.map((day) => (
                        <SelectItem key={day.value} value={day.value.toString()}>
                          {day.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="startTime">Start Time</Label>
                    <Input
                      id="startTime"
                      type="time"
                      value={startTime}
                      onChange={(e) => setStartTime(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="endTime">End Time</Label>
                    <Input
                      id="endTime"
                      type="time"
                      value={endTime}
                      onChange={(e) => setEndTime(e.target.value)}
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="isAvailable"
                    checked={isAvailable}
                    onCheckedChange={setIsAvailable}
                  />
                  <Label htmlFor="isAvailable">Available for showings</Label>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">Notes (Optional)</Label>
                  <Input
                    id="notes"
                    placeholder="Any special instructions or notes..."
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                  />
                </div>

                <div className="flex gap-2">
                  <Button onClick={handleSaveAvailability} className="flex-1">
                    {editingAvailability ? 'Update' : 'Add'} Availability
                  </Button>
                  <Button variant="outline" onClick={handleDialogClose}>
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>

      <CardContent>
        {availability.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No availability set
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Set your availability to allow buyers to schedule visits
            </p>
            <Button onClick={() => setIsDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Availability
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            {DAYS_OF_WEEK.map((day) => {
              const dayAvailability = availability.filter(a => a.dayOfWeek === day.value)
              
              return (
                <div key={day.value} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-20 font-medium">{day.label}</div>
                    <div className="flex gap-2">
                      {dayAvailability.length === 0 ? (
                        <Badge variant="secondary">Not Available</Badge>
                      ) : (
                        dayAvailability.map((avail) => (
                          <div key={avail.id} className="flex items-center gap-2">
                            <Badge variant={avail.isAvailable ? "default" : "secondary"}>
                              {avail.isAvailable ? (
                                <CheckCircle className="h-3 w-3 mr-1" />
                              ) : (
                                <XCircle className="h-3 w-3 mr-1" />
                              )}
                              {avail.timeRange}
                            </Badge>
                            {avail.notes && (
                              <span className="text-xs text-gray-500">({avail.notes})</span>
                            )}
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                  
                  <div className="flex gap-1">
                    {dayAvailability.map((avail) => (
                      <div key={avail.id} className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditAvailability(avail)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteAvailability(avail.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                    {dayAvailability.length === 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedDay(day.value)
                          setIsDialogOpen(true)
                        }}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
