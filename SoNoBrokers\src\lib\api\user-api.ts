/**
 * User API Service
 * Handles user management and authentication via .NET Web API
 */

import { auth } from '@clerk/nextjs/server'
import { apiClient } from '@/lib/api-client'
import type {
  ApiResponse,
  UserProfile,
  UpdateUserProfileRequest,
  CreateUserRequest,
  UserRole,
  SnbUserType
} from '@/types'

/**
 * Server Action: Get current user profile
 */
export async function getCurrentUserProfile(): Promise<UserProfile | null> {
  try {
    const { getToken } = await auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const response = await apiClient.get<ApiResponse<UserProfile>>('/api/sonobrokers/users/profile')
    return response.data || null
  } catch (error) {
    console.error('Failed to get current user profile:', error)
    return null
  }
}

/**
 * Server Action: Update user profile
 */
export async function updateUserProfile(data: UpdateUserProfileRequest): Promise<UserProfile | null> {
  try {
    const { getToken } = await auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const response = await apiClient.put<ApiResponse<UserProfile>>('/api/sonobrokers/users/profile', data)
    return response.data || null
  } catch (error) {
    console.error('Failed to update user profile:', error)
    return null
  }
}

/**
 * Server Action: Update user type (Buyer/Seller)
 */
export async function updateUserType(userType: 'Buyer' | 'Seller'): Promise<UserProfile | null> {
  try {
    const { getToken } = await auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const response = await apiClient.put<ApiResponse<UserProfile>>('/api/sonobrokers/users/user-type', userType)
    return response.data || null
  } catch (error) {
    console.error('Failed to update user type:', error)
    return null
  }
}

/**
 * Server Action: Check if user is authenticated
 */
export async function isAuthenticated(): Promise<boolean> {
  try {
    const { getToken } = await auth()
    const token = await getToken()
    return !!token
  } catch (error) {
    return false
  }
}

/**
 * Server Action: Get user role
 */
export async function getUserRole(): Promise<string | null> {
  try {
    const profile = await getCurrentUserProfile()
    return profile?.role || null
  } catch (error) {
    console.error('Failed to get user role:', error)
    return null
  }
}

/**
 * Server Action: Check if user has admin access
 */
export async function isAdmin(): Promise<boolean> {
  try {
    const role = await getUserRole()
    return role === 'ADMIN'
  } catch (error) {
    return false
  }
}

/**
 * Server Action: Check if user has product access
 */
export async function isProductUser(): Promise<boolean> {
  try {
    const role = await getUserRole()
    return role === 'PRODUCT'
  } catch (error) {
    return false
  }
}

/**
 * Server Action: Sync user with Clerk
 */
export async function syncUserWithClerk(): Promise<UserProfile | null> {
  try {
    const { getToken } = await auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const response = await apiClient.post<ApiResponse<UserProfile>>('/api/sonobrokers/users/sync')
    return response.data || null
  } catch (error) {
    console.error('Failed to sync user with Clerk:', error)
    return null
  }
}

/**
 * Server Action: Get user by ID (admin only)
 */
export async function getUserById(id: string): Promise<UserProfile | null> {
  try {
    const { getToken } = await auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const response = await apiClient.get<ApiResponse<UserProfile>>(`/api/sonobrokers/users/${id}`)
    return response.data || null
  } catch (error) {
    console.error('Failed to get user by ID:', error)
    return null
  }
}

/**
 * Server Action: Get user by email (admin only)
 */
export async function getUserByEmail(email: string): Promise<UserProfile | null> {
  try {
    const { getToken } = await auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const response = await apiClient.get<ApiResponse<UserProfile>>(`/api/sonobrokers/users/by-email/${encodeURIComponent(email)}`)
    return response.data || null
  } catch (error) {
    console.error('Failed to get user by email:', error)
    return null
  }
}

/**
 * Server Action: Create user (admin only)
 */
export async function createUser(userData: CreateUserRequest): Promise<UserProfile | null> {
  try {
    const { getToken } = await auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const response = await apiClient.post<ApiResponse<UserProfile>>('/api/sonobrokers/users', userData)
    return response.data || null
  } catch (error) {
    console.error('Failed to create user:', error)
    return null
  }
}

/**
 * Server Action: Delete user (admin only)
 */
export async function deleteUser(id: string): Promise<boolean> {
  try {
    const { getToken } = await auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    await apiClient.delete(`/api/sonobrokers/users/${id}`)
    return true
  } catch (error) {
    console.error('Failed to delete user:', error)
    return false
  }
}

/**
 * Server Action: Get all users (admin only)
 */
export async function getAllUsers(): Promise<UserProfile[]> {
  try {
    const { getToken } = await auth()
    const token = await getToken()
    
    if (!token) {
      throw new Error('Authentication required')
    }

    const response = await apiClient.get<ApiResponse<UserProfile[]>>('/api/sonobrokers/users')
    return response.data || []
  } catch (error) {
    console.error('Failed to get all users:', error)
    return []
  }
}

/**
 * Server Action: Update user role (admin only)
 */
export async function updateUserRole(userId: string, role: string): Promise<UserProfile | null> {
  try {
    const { getToken } = await auth()
    const token = await getToken()

    if (!token) {
      throw new Error('Authentication required')
    }

    const response = await apiClient.put<ApiResponse<UserProfile>>(`/api/sonobrokers/users/${userId}/role`, role)
    return response.data || null
  } catch (error) {
    console.error('Failed to update user role:', error)
    return null
  }
}

/**
 * Client-side helper: Get auth token for client components
 */
export async function getAuthToken(): Promise<string | null> {
  try {
    const { getToken } = await auth()
    return await getToken()
  } catch (error) {
    console.error('Failed to get auth token:', error)
    return null
  }
}
