import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { AdvertiserService } from '@/services/advertiserService'
import { CreateAdvertiserRequest } from '@/types/advertiser'

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const serviceType = searchParams.get('serviceType')
    const location = searchParams.get('location')
    const sortBy = searchParams.get('sortBy') || 'premium'

    const filters = {
      serviceType: serviceType as any,
      location,
      isVerified: searchParams.get('verified') === 'true' ? true : undefined,
      isPremium: searchParams.get('premium') === 'true' ? true : undefined,
    }

    // TODO: Migrate to .NET Web API - temporarily return empty result
    const result = {
      advertisers: [] as any[],
      total: 0,
      page,
      totalPages: 0
    }
    // const result = await AdvertiserService.searchServiceProviders({
    //   query: searchParams.get('query') || '',
    //   filters,
    //   sortBy: sortBy as any,
    //   page,
    //   limit
    // })

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error fetching advertisers:', error)
    return NextResponse.json(
      { error: 'Failed to fetch advertisers' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body: CreateAdvertiserRequest = await request.json()
    
    // Add userId to the request
    const advertiserData = {
      ...body,
      userId
    }

    // TODO: Migrate to .NET Web API - temporarily return mock data
    const advertiser = { id: 'temp-id', ...advertiserData }
    // const advertiser = await AdvertiserService.createAdvertiser(advertiserData)
    
    return NextResponse.json(advertiser, { status: 201 })
  } catch (error) {
    console.error('Error creating advertiser:', error)
    return NextResponse.json(
      { error: 'Failed to create advertiser' },
      { status: 500 }
    )
  }
}
