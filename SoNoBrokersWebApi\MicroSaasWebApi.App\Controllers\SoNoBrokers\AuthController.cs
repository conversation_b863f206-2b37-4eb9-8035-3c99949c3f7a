using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using MicroSaasWebApi.Models.Auth;
using MicroSaasWebApi.Models.SoNoBrokers;
using MicroSaasWebApi.Services.Auth.Interfaces;
using MicroSaasWebApi.Services.SoNoBrokers.Interfaces;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace MicroSaasWebApi.Controllers.SoNoBrokers
{
    [ApiController]
    [Route("api/sonobrokers/auth")]
    [Tags("Authentication")]
    public class AuthController : ControllerBase
    {
        private readonly IClerkAuthService _authService;
        private readonly IUserService _userService;
        private readonly ILogger<AuthController> _logger;
        private readonly IWebHostEnvironment _environment;

        public AuthController(
            IClerkAuthService authService,
            IUserService userService,
            ILogger<AuthController> logger,
            IWebHostEnvironment environment)
        {
            _authService = authService;
            _userService = userService;
            _logger = logger;
            _environment = environment;
        }

        /// <summary>
        /// Login user with email and password
        /// </summary>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<ActionResult<AuthResponse>> Login([FromBody] LoginRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Email) || string.IsNullOrEmpty(request.Password))
                {
                    return BadRequest(new AuthResponse
                    {
                        Success = false,
                        Message = "Email and password are required"
                    });
                }

                // Map SoNoBrokers LoginRequest to Clerk LoginRequest
                var clerkRequest = new MicroSaasWebApi.Models.Auth.Clerk.LoginRequest
                {
                    Email = request.Email,
                    Password = request.Password,
                    RememberMe = request.RememberMe
                };

                var result = await _authService.LoginAsync(clerkRequest);

                if (!result.Success)
                {
                    return Unauthorized(result);
                }

                // Sync user with local database
                if (!string.IsNullOrEmpty(result.UserId) && result.User != null)
                {
                    await SyncUserWithDatabase(result.UserId, result.User);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for email: {Email}", request.Email);
                return StatusCode(500, new AuthResponse
                {
                    Success = false,
                    Message = "Login failed due to server error"
                });
            }
        }

        /// <summary>
        /// Register new user
        /// </summary>
        [HttpPost("register")]
        [AllowAnonymous]
        public async Task<ActionResult<AuthResponse>> Register([FromBody] RegisterRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Email) || string.IsNullOrEmpty(request.Password))
                {
                    return BadRequest(new AuthResponse
                    {
                        Success = false,
                        Message = "Email and password are required"
                    });
                }

                // Map SoNoBrokers RegisterRequest to Clerk RegisterRequest
                var clerkRequest = new MicroSaasWebApi.Models.Auth.Clerk.RegisterRequest
                {
                    Email = request.Email,
                    Password = request.Password,
                    FirstName = request.FirstName ?? "",
                    LastName = request.LastName ?? "",
                    Phone = request.Phone
                };

                var result = await _authService.RegisterAsync(clerkRequest);

                if (!result.Success)
                {
                    return BadRequest(result);
                }

                // Sync user with local database
                if (!string.IsNullOrEmpty(result.UserId) && result.User != null)
                {
                    await SyncUserWithDatabase(result.UserId, result.User);
                }

                return CreatedAtAction(nameof(GetProfile), result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during registration for email: {Email}", request.Email);
                return StatusCode(500, new AuthResponse
                {
                    Success = false,
                    Message = "Registration failed due to server error"
                });
            }
        }

        /// <summary>
        /// Get current user profile
        /// </summary>
        [HttpGet("profile")]
        [Authorize]
        public async Task<ActionResult<UserInfo>> GetProfile()
        {
            try
            {
                var userProfile = await _authService.GetUserProfileAsync(HttpContext);
                if (userProfile == null)
                {
                    return NotFound(new { error = "User profile not found" });
                }

                return Ok(userProfile);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user profile");
                return StatusCode(500, new { error = "Failed to get user profile" });
            }
        }

        /// <summary>
        /// Update user profile
        /// </summary>
        [HttpPut("profile")]
        [Authorize]
        public async Task<ActionResult<UserInfo>> UpdateProfile([FromBody] UpdateProfileRequest request)
        {
            try
            {
                var userId = await _authService.GetUserIdAsync(HttpContext);
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { error = "User not authenticated" });
                }

                var updated = await _authService.UpdateUserAsync(userId, request.FirstName, request.LastName);
                if (!updated)
                {
                    return BadRequest(new { error = "Failed to update profile" });
                }

                // Get updated profile
                var updatedProfile = await _authService.GetUserProfileAsync(HttpContext);
                if (updatedProfile == null)
                {
                    return NotFound(new { error = "Updated profile not found" });
                }

                // Sync with local database
                await SyncUserWithDatabase(userId, updatedProfile);

                return Ok(updatedProfile);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user profile");
                return StatusCode(500, new { error = "Failed to update profile" });
            }
        }

        /// <summary>
        /// Refresh authentication token
        /// </summary>
        [HttpPost("refresh")]
        [AllowAnonymous]
        public async Task<ActionResult<AuthResponse>> RefreshToken([FromBody] RefreshTokenRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.RefreshToken))
                {
                    return BadRequest(new AuthResponse
                    {
                        Success = false,
                        Message = "Refresh token is required"
                    });
                }

                var result = await _authService.RefreshTokenAsync(request.RefreshToken);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing token");
                return StatusCode(500, new AuthResponse
                {
                    Success = false,
                    Message = "Token refresh failed"
                });
            }
        }

        /// <summary>
        /// Logout user
        /// </summary>
        [HttpPost("logout")]
        [Authorize]
        public async Task<ActionResult> Logout()
        {
            try
            {
                var userId = await _authService.GetUserIdAsync(HttpContext);
                if (string.IsNullOrEmpty(userId))
                {
                    return Ok(new { message = "User already logged out" });
                }

                var result = await _authService.LogoutAsync(userId);
                if (result)
                {
                    return Ok(new { message = "Logout successful" });
                }

                return BadRequest(new { error = "Logout failed" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                return StatusCode(500, new { error = "Logout failed" });
            }
        }

        /// <summary>
        /// Validate current authentication status
        /// </summary>
        [HttpGet("validate")]
        [Authorize]
        public async Task<ActionResult> ValidateAuth()
        {
            try
            {
                var userId = await _authService.GetUserIdAsync(HttpContext);
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { error = "Invalid authentication" });
                }

                return Ok(new
                {
                    valid = true,
                    userId = userId,
                    message = "Authentication valid"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating authentication");
                return StatusCode(500, new { error = "Validation failed" });
            }
        }

        /// <summary>
        /// Track user login - updates lastLoginAt and loggedIn status
        /// </summary>
        [HttpPost("track-login")]
        [Authorize]
        public async Task<ActionResult> TrackLogin()
        {
            string? userId = null;
            try
            {
                userId = await _authService.GetUserIdAsync(HttpContext);
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { error = "User not authenticated" });
                }

                // Get SNB user by Clerk ID
                var snbUser = await _userService.GetUserByClerkIdAsync(userId);
                if (snbUser != null)
                {
                    await _userService.TrackLoginAsync(snbUser.Id);
                }

                return Ok(new { message = "Login tracked successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking login for user: {UserId}", userId ?? "unknown");
                return StatusCode(500, new { error = "Failed to track login" });
            }
        }

        /// <summary>
        /// Track user logout - updates loggedIn status to false
        /// </summary>
        [HttpPost("track-logout")]
        [Authorize]
        public async Task<ActionResult> TrackLogout()
        {
            string? userId = null;
            try
            {
                userId = await _authService.GetUserIdAsync(HttpContext);
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { error = "User not authenticated" });
                }

                // Get SNB user by Clerk ID
                var snbUser = await _userService.GetUserByClerkIdAsync(userId);
                if (snbUser != null)
                {
                    await _userService.TrackLogoutAsync(snbUser.Id);
                }

                return Ok(new { message = "Logout tracked successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking logout for user: {UserId}", userId ?? "unknown");
                return StatusCode(500, new { error = "Failed to track logout" });
            }
        }

        /// <summary>
        /// Get login statistics for current user
        /// </summary>
        [HttpGet("login-stats")]
        [Authorize]
        public async Task<ActionResult> GetLoginStats()
        {
            string? userId = null;
            try
            {
                userId = await _authService.GetUserIdAsync(HttpContext);
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { error = "User not authenticated" });
                }

                // Get SNB user by Clerk ID
                var snbUser = await _userService.GetUserByClerkIdAsync(userId);
                if (snbUser == null)
                {
                    return NotFound(new { error = "User not found" });
                }

                var stats = await _userService.GetLoginStatsAsync(snbUser.Id);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting login stats for user: {UserId}", userId ?? "unknown");
                return StatusCode(500, new { error = "Failed to get login stats" });
            }
        }

        /// <summary>
        /// Sync user data with local database
        /// </summary>
        private async Task SyncUserWithDatabase(string clerkUserId, MicroSaasWebApi.Models.Auth.Clerk.UserInfo userInfo)
        {
            try
            {
                // Check if user exists in local database
                var existingUser = await _userService.GetUserByClerkIdAsync(clerkUserId);

                if (existingUser == null)
                {
                    // Create new user in local database
                    var createRequest = new Models.SoNoBrokers.CreateUserRequest
                    {
                        Email = userInfo.Email,
                        FullName = $"{userInfo.FirstName} {userInfo.LastName}".Trim(),
                        FirstName = userInfo.FirstName,
                        LastName = userInfo.LastName,
                        ClerkUserId = clerkUserId,
                        Role = Models.SoNoBrokers.UserRole.USER,
                        UserType = Models.SoNoBrokers.SnbUserType.Buyer
                    };

                    await _userService.CreateUserAsync(createRequest);
                    _logger.LogInformation("Created new user in database for Clerk ID: {ClerkUserId}", clerkUserId);
                }
                else
                {
                    // Update existing user if needed
                    var updateRequest = new Models.SoNoBrokers.UpdateUserRequest
                    {
                        Id = existingUser.Id,
                        Email = userInfo.Email,
                        FullName = $"{userInfo.FirstName} {userInfo.LastName}".Trim(),
                        FirstName = userInfo.FirstName,
                        LastName = userInfo.LastName
                    };

                    await _userService.UpdateUserAsync(updateRequest);
                    _logger.LogInformation("Updated user in database for Clerk ID: {ClerkUserId}", clerkUserId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing user with database for Clerk ID: {ClerkUserId}", clerkUserId);
                // Don't throw - this is a background sync operation
            }
        }

        /// <summary>
        /// Generate test bearer token for API testing (Development only)
        /// </summary>
        [HttpPost("generate-test-token")]
        [AllowAnonymous]
        public ActionResult<object> GenerateTestToken([FromBody] TestTokenRequest request)
        {
            try
            {
                // Only allow in development environment
                if (!_environment.IsDevelopment())
                {
                    return BadRequest(new { error = "Test token generation is only available in development environment" });
                }

                // Create a test JWT token with claims
                var claims = new List<Claim>
                {
                    new(ClaimTypes.NameIdentifier, request.UserId ?? "test-user-123"),
                    new(ClaimTypes.Email, request.Email ?? "<EMAIL>"),
                    new(ClaimTypes.Name, request.Name ?? "Test User"),
                    new("user_type", request.UserType ?? "buyer"),
                    new("country", request.Country ?? "CA")
                };

                // Add role claims if specified
                if (!string.IsNullOrEmpty(request.Role))
                {
                    claims.Add(new(ClaimTypes.Role, request.Role));
                }

                // Create token (valid for 1 hour)
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes("test-secret-key-for-development-only-do-not-use-in-production");
                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(claims),
                    Expires = DateTime.UtcNow.AddHours(1),
                    Issuer = "sonobrokers-test",
                    Audience = "sonobrokers-api",
                    SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
                };

                var token = tokenHandler.CreateToken(tokenDescriptor);
                var tokenString = tokenHandler.WriteToken(token);

                return Ok(new
                {
                    success = true,
                    token = tokenString,
                    expires = tokenDescriptor.Expires,
                    claims = claims.Select(c => new { type = c.Type, value = c.Value }),
                    message = "Test token generated successfully. Use this token in the Authorization header as 'Bearer {token}'"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating test token");
                return StatusCode(500, new { error = "Failed to generate test token" });
            }
        }
    }

    /// <summary>
    /// Request model for generating test tokens
    /// </summary>
    public class TestTokenRequest
    {
        public string? UserId { get; set; }
        public string? Email { get; set; }
        public string? Name { get; set; }
        public string? UserType { get; set; } // buyer, seller, admin
        public string? Role { get; set; } // admin, user
        public string? Country { get; set; } // CA, US, AE
    }

    /// <summary>
    /// Request model for updating user profile
    /// </summary>
    public class UpdateProfileRequest
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
    }

    /// <summary>
    /// Request model for refreshing token
    /// </summary>
    public class RefreshTokenRequest
    {
        public string RefreshToken { get; set; } = string.Empty;
    }
}
