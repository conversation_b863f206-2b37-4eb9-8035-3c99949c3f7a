# Hangfire Background Jobs for SoNoBrokers

## Overview

Hangfire is a .NET library for background job processing that allows SoNoBrokers to run tasks asynchronously without blocking the main web request-response cycle. This document outlines the planned implementation and use cases for Hangfire in the SoNoBrokers real estate platform.

## Current Status

**Status**: Temporarily disabled for development simplicity
**Location**: `Program.cs` lines 205-208 (commented out)
**Storage**: PostgreSQL/Supabase database

```csharp
// Add Hangfire for background jobs - temporarily commented out for build fix
// services.AddHangfire(config => config
//     .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
//     .UseSimpleAssemblyNameTypeSerializer()
//     .UseRecommendedSerializerSettings()
//     .UseNpgsqlStorage(appSettings.GetConnectionString()));

// services.AddHangfireServer();
```

## 5 Key Use Cases for SoNoBrokers

### 1. Email Notifications 📧

**Purpose**: Send automated emails without blocking user interactions

**Use Cases**:
- **Property Alerts**: Notify buyers when new properties match their search criteria
- **Welcome Emails**: Send onboarding emails to new users
- **Listing Confirmations**: Confirm property listing submissions to sellers
- **Price Change Alerts**: Notify interested buyers of price reductions
- **Newsletter Distribution**: Send weekly market updates to subscribers

**Implementation Example**:
```csharp
// Fire-and-forget job
BackgroundJob.Enqueue(() => emailService.SendPropertyAlert(userId, propertyId));

// Delayed job
BackgroundJob.Schedule(() => emailService.SendWelcomeEmail(userId), TimeSpan.FromHours(1));

// Recurring job
RecurringJob.AddOrUpdate("weekly-newsletter", 
    () => emailService.SendWeeklyNewsletter(), 
    Cron.Weekly(DayOfWeek.Sunday, 9));
```

### 2. Image Processing 🖼️

**Purpose**: Process property images without making users wait

**Use Cases**:
- **Image Resizing**: Create multiple sizes (thumbnail, medium, large) for responsive display
- **Image Optimization**: Compress images for faster loading
- **Watermark Addition**: Add SoNoBrokers branding to property images
- **Format Conversion**: Convert images to WebP for better performance
- **Metadata Extraction**: Extract EXIF data for property location verification

**Implementation Example**:
```csharp
// Process uploaded images in background
BackgroundJob.Enqueue(() => imageService.ProcessPropertyImages(propertyId, imageUrls));

// Generate thumbnails
BackgroundJob.Enqueue(() => imageService.GenerateThumbnails(propertyId));
```

### 3. Data Cleanup & Maintenance 🧹

**Purpose**: Keep the database clean and performant

**Use Cases**:
- **Expired Listings**: Remove or archive properties that have been sold or expired
- **Old Search Filters**: Clean up saved searches that haven't been used in 6+ months
- **Session Cleanup**: Remove expired user sessions and temporary data
- **Log Rotation**: Archive old application logs
- **Cache Invalidation**: Clear outdated cached property data

**Implementation Example**:
```csharp
// Daily cleanup jobs
RecurringJob.AddOrUpdate("cleanup-expired-listings", 
    () => cleanupService.RemoveExpiredListings(), 
    Cron.Daily(2)); // Run at 2 AM

RecurringJob.AddOrUpdate("cleanup-old-sessions", 
    () => cleanupService.CleanupExpiredSessions(), 
    Cron.Daily(3)); // Run at 3 AM
```

### 4. Scheduled Reports & Analytics 📊

**Purpose**: Generate insights and reports for users and administrators

**Use Cases**:
- **Daily Property Statistics**: Track new listings, views, and inquiries
- **Weekly Market Reports**: Generate neighborhood price trends and market analysis
- **Monthly User Activity**: Create user engagement and platform usage reports
- **Seller Performance Reports**: Provide listing performance metrics to sellers
- **Admin Dashboard Data**: Update administrative metrics and KPIs

**Implementation Example**:
```csharp
// Generate daily reports
RecurringJob.AddOrUpdate("daily-property-stats", 
    () => reportService.GenerateDailyPropertyStats(), 
    Cron.Daily(6)); // Run at 6 AM

// Weekly market analysis
RecurringJob.AddOrUpdate("weekly-market-report", 
    () => reportService.GenerateWeeklyMarketReport(), 
    Cron.Weekly(DayOfWeek.Monday, 7)); // Monday at 7 AM
```

### 5. External API Synchronization 🔄

**Purpose**: Keep SoNoBrokers data synchronized with external services

**Use Cases**:
- **MLS Integration**: Sync with Multiple Listing Service databases
- **Property Valuation Updates**: Fetch updated property values from Zillow/Redfin APIs
- **Geocoding Services**: Convert addresses to coordinates for map display
- **Market Data Sync**: Update neighborhood statistics and trends
- **Payment Processing**: Sync Stripe payment statuses and subscription updates

**Implementation Example**:
```csharp
// Sync with external MLS every hour
RecurringJob.AddOrUpdate("mls-sync", 
    () => mlsService.SyncPropertyData(), 
    Cron.Hourly());

// Update property valuations daily
RecurringJob.AddOrUpdate("valuation-update", 
    () => valuationService.UpdatePropertyValues(), 
    Cron.Daily(4)); // Run at 4 AM
```

## Configuration Setup

### 1. Enable Hangfire Services

Uncomment the following lines in `Program.cs`:

```csharp
services.AddHangfire(config => config
    .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
    .UseSimpleAssemblyNameTypeSerializer()
    .UseRecommendedSerializerSettings()
    .UseNpgsqlStorage(appSettings.GetConnectionString()));

services.AddHangfireServer();
```

### 2. Enable Hangfire Dashboard

Uncomment in `MiddlewareOrdering.cs`:

```csharp
app.UseHangfireDashboard("/hangfire", new DashboardOptions
{
    Authorization = new IDashboardAuthorizationFilter[] { new HangfireAuthorizationFilter() }
});
```

### 3. Database Tables

Hangfire will automatically create the following tables in your Supabase database:
- `hangfire.job`
- `hangfire.jobparameter`
- `hangfire.jobqueue`
- `hangfire.server`
- `hangfire.state`

## Benefits for SoNoBrokers

1. **Improved User Experience**: Users don't wait for background tasks to complete
2. **Better Performance**: Main application remains responsive
3. **Reliability**: Jobs are persisted and can be retried on failure
4. **Scalability**: Background processing can be scaled independently
5. **Monitoring**: Built-in dashboard for job monitoring and management

## Dashboard Access

Once enabled, access the Hangfire dashboard at:
- **Development**: `https://localhost:7163/hangfire`
- **Production**: `https://api.sonobrokers.com/hangfire`

## Security Considerations

- Dashboard access is restricted via `HangfireAuthorizationFilter`
- Only authenticated admin users should access the dashboard
- Sensitive data in jobs should be encrypted
- Job parameters should not contain passwords or API keys

## Future Enhancements

- **Job Prioritization**: Critical jobs (payment processing) get higher priority
- **Job Batches**: Group related jobs together (e.g., all images for a property)
- **Job Continuations**: Chain jobs together (upload → process → notify)
- **Custom Job Filters**: Add logging, error handling, and performance monitoring
