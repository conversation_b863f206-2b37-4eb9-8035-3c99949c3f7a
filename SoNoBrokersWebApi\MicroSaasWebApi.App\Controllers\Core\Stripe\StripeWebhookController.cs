using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.App.Context;
using MicroSaasWebApi.Models.Core;
using MicroSaasWebApi.Models.SoNoBrokers;

using Stripe;
using System.Text.Json;
using StripeCheckout = Stripe.Checkout;
using StripeSubscription = Stripe.Subscription;

namespace MicroSaasWebApi.Controllers.Core.Stripe
{
    [ApiController]
    [Route("api/stripe/webhooks")]
    public class StripeWebhookController : ControllerBase
    {
        private readonly IDapperDbContext _dbContext;
        private readonly ILogger<StripeWebhookController> _logger;
        private readonly IConfiguration _configuration;

        public StripeWebhookController(
            IDapperDbContext dbContext,
            ILogger<StripeWebhookController> logger,
            IConfiguration configuration)
        {
            _dbContext = dbContext;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Handle Stripe webhooks for SoNoBrokers
        /// </summary>
        /// <returns>Webhook processing result</returns>
        [HttpPost]




        public async Task<ActionResult<ApiResponse<object>>> HandleStripeWebhook()
        {
            try
            {
                var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
                var stripeSignature = Request.Headers["Stripe-Signature"].FirstOrDefault();
                var webhookSecret = _configuration["ExternalServices:Stripe:WebhookSecret"];

                if (string.IsNullOrEmpty(webhookSecret))
                {
                    _logger.LogError("Stripe webhook secret not configured");
                    return BadRequest(ApiResponse<object>.ErrorResult("Webhook secret not configured"));
                }

                Event stripeEvent;
                try
                {
                    stripeEvent = EventUtility.ConstructEvent(json, stripeSignature, webhookSecret);
                }
                catch (StripeException ex)
                {
                    _logger.LogError(ex, "Invalid Stripe webhook signature");
                    return BadRequest(ApiResponse<object>.ErrorResult("Invalid webhook signature"));
                }

                _logger.LogInformation("Processing Stripe webhook event: {EventType}", stripeEvent.Type);

                switch (stripeEvent.Type)
                {
                    case Events.CheckoutSessionCompleted:
                        await HandleCheckoutSessionCompleted(stripeEvent);
                        break;

                    case Events.CustomerSubscriptionUpdated:
                        await HandleSubscriptionUpdated(stripeEvent);
                        break;

                    case Events.CustomerSubscriptionDeleted:
                        await HandleSubscriptionDeleted(stripeEvent);
                        break;

                    case Events.InvoicePaid:
                        await HandleInvoicePaid(stripeEvent);
                        break;

                    case Events.InvoicePaymentFailed:
                        await HandleInvoicePaymentFailed(stripeEvent);
                        break;

                    case Events.PaymentIntentSucceeded:
                        await HandlePaymentIntentSucceeded(stripeEvent);
                        break;

                    case Events.PaymentIntentPaymentFailed:
                        await HandlePaymentIntentFailed(stripeEvent);
                        break;

                    default:
                        _logger.LogInformation("Unhandled Stripe webhook event type: {EventType}", stripeEvent.Type);
                        break;
                }

                return Ok(ApiResponse<object>.SuccessResult(new { }, "Webhook processed successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Stripe webhook");
                return StatusCode(500, ApiResponse<object>.ErrorResult("Failed to process webhook"));
            }
        }

        private async Task HandleCheckoutSessionCompleted(Event stripeEvent)
        {
            try
            {
                var session = stripeEvent.Data.Object as StripeCheckout.Session;
                if (session == null) return;

                var userId = session.Metadata?.GetValueOrDefault("userId");
                var planType = session.Metadata?.GetValueOrDefault("planType");

                if (string.IsNullOrEmpty(userId))
                {
                    _logger.LogWarning("No userId found in checkout session metadata");
                    return;
                }

                // Get the subscription from Stripe
                var subscriptionService = new SubscriptionService();
                var stripeSubscription = await subscriptionService.GetAsync(session.SubscriptionId);

                // Create or update subscription in database
                const string checkSql = @"
                    SELECT * FROM snb.subscriptions
                    WHERE user_id = @userId
                    ORDER BY created_at DESC
                    LIMIT 1";

                var existingSubscription = await _dbContext.QueryFirstOrDefaultAsync<Models.SoNoBrokers.Subscription>(checkSql, new { userId });

                if (existingSubscription != null)
                {
                    // Update existing subscription
                    const string updateSql = @"
                        UPDATE snb.subscriptions SET
                            stripe_subscription_id = @StripeSubscriptionId,
                            stripe_customer_id = @StripeCustomerId,
                            status = @Status,
                            plan_type = @PlanType,
                            starts_at = @StartsAt,
                            ends_at = @EndsAt,
                            updated_at = @UpdatedAt
                        WHERE id = @Id";

                    await _dbContext.ExecuteAsync(updateSql, new
                    {
                        Id = existingSubscription.Id,
                        StripeSubscriptionId = stripeSubscription.Id,
                        StripeCustomerId = stripeSubscription.CustomerId,
                        Status = stripeSubscription.Status,
                        PlanType = planType ?? "unknown",
                        StartsAt = stripeSubscription.CurrentPeriodStart,
                        EndsAt = stripeSubscription.CurrentPeriodEnd,
                        UpdatedAt = DateTime.UtcNow
                    });
                }
                else
                {
                    // Create new subscription
                    const string insertSql = @"
                        INSERT INTO snb.subscriptions (id, user_id, stripe_subscription_id, stripe_customer_id, status, plan_type, starts_at, ends_at, created_at, updated_at)
                        VALUES (@Id, @UserId, @StripeSubscriptionId, @StripeCustomerId, @Status, @PlanType, @StartsAt, @EndsAt, @CreatedAt, @UpdatedAt)";

                    await _dbContext.ExecuteAsync(insertSql, new
                    {
                        Id = Guid.NewGuid().ToString(),
                        UserId = userId,
                        StripeSubscriptionId = stripeSubscription.Id,
                        StripeCustomerId = stripeSubscription.CustomerId,
                        Status = stripeSubscription.Status,
                        PlanType = planType ?? "unknown",
                        StartsAt = stripeSubscription.CurrentPeriodStart,
                        EndsAt = stripeSubscription.CurrentPeriodEnd,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    });
                }
                _logger.LogInformation("Subscription created/updated for user: {UserId}", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling checkout session completed");
            }
        }

        private async Task HandleSubscriptionUpdated(Event stripeEvent)
        {
            try
            {
                var stripeSubscription = stripeEvent.Data.Object as StripeSubscription;
                if (stripeSubscription == null) return;

                const string sql = @"
                    SELECT * FROM snb.subscriptions
                    WHERE stripe_subscription_id = @stripeSubscriptionId";

                var subscription = await _dbContext.QueryFirstOrDefaultAsync<Models.SoNoBrokers.Subscription>(sql, new { stripeSubscriptionId = stripeSubscription.Id });

                if (subscription != null)
                {
                    const string updateSql = @"
                        UPDATE snb.subscriptions SET
                            status = @Status,
                            starts_at = @StartsAt,
                            ends_at = @EndsAt,
                            updated_at = @UpdatedAt
                        WHERE stripe_subscription_id = @StripeSubscriptionId";

                    await _dbContext.ExecuteAsync(updateSql, new
                    {
                        Status = stripeSubscription.Status,
                        StartsAt = stripeSubscription.CurrentPeriodStart,
                        EndsAt = stripeSubscription.CurrentPeriodEnd,
                        UpdatedAt = DateTime.UtcNow,
                        StripeSubscriptionId = stripeSubscription.Id
                    });

                    _logger.LogInformation("Subscription updated: {SubscriptionId}", stripeSubscription.Id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling subscription updated");
            }
        }

        private async Task HandleSubscriptionDeleted(Event stripeEvent)
        {
            try
            {
                var stripeSubscription = stripeEvent.Data.Object as StripeSubscription;
                if (stripeSubscription == null) return;

                const string sql = @"
                    SELECT * FROM snb.subscriptions
                    WHERE stripe_subscription_id = @stripeSubscriptionId";

                var subscription = await _dbContext.QueryFirstOrDefaultAsync<Models.SoNoBrokers.Subscription>(sql, new { stripeSubscriptionId = stripeSubscription.Id });

                if (subscription != null)
                {
                    const string updateSql = @"
                        UPDATE snb.subscriptions SET
                            status = 'canceled',
                            updated_at = @UpdatedAt
                        WHERE stripe_subscription_id = @StripeSubscriptionId";

                    await _dbContext.ExecuteAsync(updateSql, new
                    {
                        UpdatedAt = DateTime.UtcNow,
                        StripeSubscriptionId = stripeSubscription.Id
                    });

                    _logger.LogInformation("Subscription canceled: {SubscriptionId}", stripeSubscription.Id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling subscription deleted");
            }
        }

        private async Task HandleInvoicePaid(Event stripeEvent)
        {
            try
            {
                var invoice = stripeEvent.Data.Object as Invoice;
                if (invoice == null) return;

                // Update subscription status if needed
                if (!string.IsNullOrEmpty(invoice.SubscriptionId))
                {
                    const string sql = @"
                        SELECT * FROM snb.subscriptions
                        WHERE stripe_subscription_id = @stripeSubscriptionId";

                    var subscription = await _dbContext.QueryFirstOrDefaultAsync<Models.SoNoBrokers.Subscription>(sql, new { stripeSubscriptionId = invoice.SubscriptionId });

                    if (subscription != null && subscription.Status != "active")
                    {
                        const string updateSql = @"
                            UPDATE snb.subscriptions SET
                                status = 'active',
                                updated_at = @UpdatedAt
                            WHERE stripe_subscription_id = @StripeSubscriptionId";

                        await _dbContext.ExecuteAsync(updateSql, new
                        {
                            UpdatedAt = DateTime.UtcNow,
                            StripeSubscriptionId = invoice.SubscriptionId
                        });
                    }
                }

                _logger.LogInformation("Invoice paid: {InvoiceId}", invoice.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling invoice paid");
            }
        }

        private async Task HandleInvoicePaymentFailed(Event stripeEvent)
        {
            try
            {
                var invoice = stripeEvent.Data.Object as Invoice;
                if (invoice == null) return;

                // Update subscription status if needed
                if (!string.IsNullOrEmpty(invoice.SubscriptionId))
                {
                    const string sql = @"
                        SELECT * FROM snb.subscriptions
                        WHERE stripe_subscription_id = @stripeSubscriptionId";

                    var subscription = await _dbContext.QueryFirstOrDefaultAsync<Models.SoNoBrokers.Subscription>(sql, new { stripeSubscriptionId = invoice.SubscriptionId });

                    if (subscription != null)
                    {
                        const string updateSql = @"
                            UPDATE snb.subscriptions SET
                                status = 'past_due',
                                updated_at = @UpdatedAt
                            WHERE stripe_subscription_id = @StripeSubscriptionId";

                        await _dbContext.ExecuteAsync(updateSql, new
                        {
                            UpdatedAt = DateTime.UtcNow,
                            StripeSubscriptionId = invoice.SubscriptionId
                        });
                    }
                }

                _logger.LogWarning("Invoice payment failed: {InvoiceId}", invoice.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling invoice payment failed");
            }
        }

        private async Task HandlePaymentIntentSucceeded(Event stripeEvent)
        {
            try
            {
                var paymentIntent = stripeEvent.Data.Object as PaymentIntent;
                if (paymentIntent == null) return;

                var userId = paymentIntent.Metadata?.GetValueOrDefault("userId");
                var paymentType = paymentIntent.Metadata?.GetValueOrDefault("paymentType") ?? "one_time";

                if (string.IsNullOrEmpty(userId))
                {
                    _logger.LogWarning("No userId found in payment intent metadata");
                    return;
                }

                // Record the one-time payment in database (you may want to create a payments table)
                const string insertPaymentSql = @"
                    INSERT INTO snb.payments (id, user_id, stripe_payment_intent_id, amount, currency, status, payment_type, created_at, updated_at)
                    VALUES (@Id, @UserId, @StripePaymentIntentId, @Amount, @Currency, @Status, @PaymentType, @CreatedAt, @UpdatedAt)
                    ON CONFLICT (stripe_payment_intent_id) DO UPDATE SET
                        status = @Status,
                        updated_at = @UpdatedAt";

                await _dbContext.ExecuteAsync(insertPaymentSql, new
                {
                    Id = Guid.NewGuid().ToString(),
                    UserId = userId,
                    StripePaymentIntentId = paymentIntent.Id,
                    Amount = paymentIntent.Amount,
                    Currency = paymentIntent.Currency,
                    Status = paymentIntent.Status,
                    PaymentType = paymentType,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });

                _logger.LogInformation("One-time payment succeeded for user: {UserId}, PaymentIntent: {PaymentIntentId}", userId, paymentIntent.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling payment intent succeeded");
            }
        }

        private async Task HandlePaymentIntentFailed(Event stripeEvent)
        {
            try
            {
                var paymentIntent = stripeEvent.Data.Object as PaymentIntent;
                if (paymentIntent == null) return;

                var userId = paymentIntent.Metadata?.GetValueOrDefault("userId");
                var paymentType = paymentIntent.Metadata?.GetValueOrDefault("paymentType") ?? "one_time";

                if (string.IsNullOrEmpty(userId))
                {
                    _logger.LogWarning("No userId found in payment intent metadata");
                    return;
                }

                // Update payment status in database
                const string updatePaymentSql = @"
                    INSERT INTO snb.payments (id, user_id, stripe_payment_intent_id, amount, currency, status, payment_type, created_at, updated_at)
                    VALUES (@Id, @UserId, @StripePaymentIntentId, @Amount, @Currency, @Status, @PaymentType, @CreatedAt, @UpdatedAt)
                    ON CONFLICT (stripe_payment_intent_id) DO UPDATE SET
                        status = @Status,
                        updated_at = @UpdatedAt";

                await _dbContext.ExecuteAsync(updatePaymentSql, new
                {
                    Id = Guid.NewGuid().ToString(),
                    UserId = userId,
                    StripePaymentIntentId = paymentIntent.Id,
                    Amount = paymentIntent.Amount,
                    Currency = paymentIntent.Currency,
                    Status = paymentIntent.Status,
                    PaymentType = paymentType,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });

                _logger.LogWarning("One-time payment failed for user: {UserId}, PaymentIntent: {PaymentIntentId}", userId, paymentIntent.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling payment intent failed");
            }
        }
    }
}
