import Link from "next/link";
import { getSEOTags } from "@/lib/seo";
import config from "@/config";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { getRegionSpecificPolicies } from '@/lib/geo';

// CHATGPT PROMPT TO GENERATE YOUR PRIVACY POLICY — replace with your own data 👇

// 1. Go to https://chat.openai.com/
// 2. Copy paste bellow
// 3. Replace the data with your own (if needed)
// 4. Paste the answer from ChatGPT directly in the <pre> tag below

// You are an excellent lawyer.

// I need your help to write a simple privacy policy for my website. Here is some context:
// - Website: https://micro.st
// - Name: Micro Sass Fast
// - Description: A JavaScript code boilerplate to help entrepreneurs launch their startups faster
// - User data collected: name, email and payment information
// - Non-personal data collection: web cookies
// - Purpose of Data Collection: Order processing
// - Data sharing: we do not share the data with any other parties
// - Children's Privacy: we do not collect any data from children
// - Updates to the Privacy Policy: users will be updated by email
// - Contact information: <EMAIL>

// Please write a simple privacy policy for my site. Add the current date.  Do not add or explain your reasoning. Answer:

export const metadata = getSEOTags({
  title: `Privacy Policy | ${config.appName}`,
  canonicalUrlRelative: "/privacy-policy",
});

const PrivacyPolicy = () => {
  // In a real implementation, this would come from the user's session or request
  const userCountry = 'US'; // This should be dynamically determined
  const regionPolicies = getRegionSpecificPolicies(userCountry);

  return (
    <div className="container mx-auto py-12 px-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-3xl font-bold">Privacy Policy</CardTitle>
          <p className="text-muted-foreground">Last updated: {new Date().toLocaleDateString()}</p>
          {regionPolicies && (
            <div className="mt-4 p-4 bg-muted rounded-lg">
              <h3 className="font-semibold mb-2">Region-Specific Privacy Information</h3>
              <ul className="list-disc pl-6 space-y-1 text-sm text-muted-foreground">
                <li>Applicable Privacy Laws: {regionPolicies.privacyLaws.join(', ')}</li>
                <li>Data Retention Period: {regionPolicies.dataRetention}</li>
                <li>Data Transfer: {regionPolicies.dataTransfer}</li>
              </ul>
            </div>
          )}
        </CardHeader>
        <CardContent className="space-y-8">
          <section>
            <h2 className="text-2xl font-semibold mb-4">1. Introduction</h2>
            <p className="text-muted-foreground">
              SoNoBrokers ("we," "our," or "us") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our real estate platform. This policy is specifically tailored for users in the United States and Canada.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">2. Information We Collect</h2>
            <div className="space-y-4">
              <h3 className="text-xl font-medium">2.1 Personal Information</h3>
              <ul className="list-disc pl-6 space-y-2 text-muted-foreground">
                <li>Name and contact information</li>
                <li>Email address and phone number</li>
                <li>Property details and preferences</li>
                <li>Payment information (processed securely through Stripe)</li>
                <li>Authentication data (managed by Clerk)</li>
                <li>Location data (to ensure compliance with regional regulations)</li>
              </ul>

              <h3 className="text-xl font-medium">2.2 Usage Information</h3>
              <ul className="list-disc pl-6 space-y-2 text-muted-foreground">
                <li>IP address and device information</li>
                <li>Browser type and settings</li>
                <li>Pages visited and time spent</li>
                <li>Search queries and property views</li>
                <li>Geographic location data</li>
              </ul>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">3. Regional Compliance</h2>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                Our platform is currently available only in the United States and Canada. We comply with the following regional privacy laws:
              </p>
              <ul className="list-disc pl-6 space-y-2 text-muted-foreground">
                <li>United States: CCPA, CPRA, and other applicable state laws</li>
                <li>Canada: PIPEDA and applicable provincial laws</li>
              </ul>
              <p className="text-muted-foreground mt-4">
                Users from other regions will be notified when our services become available in their area.
              </p>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">4. Data Storage and Transfer</h2>
            <p className="text-muted-foreground">
              All personal data is stored and processed within the United States and Canada. We do not transfer personal data outside these regions. By using our services, you consent to the storage and processing of your data within these regions.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">5. Third-Party Services</h2>
            <div className="space-y-4">
              <h3 className="text-xl font-medium">5.1 Stripe</h3>
              <p className="text-muted-foreground">
                We use Stripe for payment processing. Your payment information is handled securely by Stripe and is subject to their privacy policy. All payment processing is conducted within the United States and Canada.
              </p>

              <h3 className="text-xl font-medium">5.2 Clerk</h3>
              <p className="text-muted-foreground">
                We use Clerk for authentication and user management. Your authentication data is handled securely by Clerk and is subject to their privacy policy. All authentication services are provided within the United States and Canada.
              </p>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">6. Data Security</h2>
            <p className="text-muted-foreground">
              We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. All data is encrypted in transit and at rest.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">7. Your Rights</h2>
            <p className="text-muted-foreground">
              Depending on your location, you may have the following rights:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-muted-foreground">
              <li>Access your personal data</li>
              <li>Correct inaccurate data</li>
              <li>Request deletion of your data</li>
              <li>Object to data processing</li>
              <li>Data portability</li>
              <li>Opt-out of data sales (where applicable)</li>
            </ul>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">8. Contact Us</h2>
            <p className="text-muted-foreground">
              If you have any questions about this Privacy Policy or our data practices, please contact us at:
            </p>
            <p className="text-muted-foreground">
              Email: <EMAIL>
            </p>
          </section>
        </CardContent>
      </Card>
    </div>
  );
};

export default PrivacyPolicy;
