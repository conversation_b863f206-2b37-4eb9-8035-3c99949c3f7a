import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Scale, 
  Camera, 
  Home, 
  Shield, 
  Truck, 
  Calculator,
  FileText,
  MapPin
} from 'lucide-react';

interface ServiceProvidersProps {
  userType: 'buyer' | 'seller';
}

export default function USServiceProviders({ userType }: ServiceProvidersProps) {
  const services = [
    {
      icon: Scale,
      title: 'Real Estate Attorneys',
      description: 'Licensed attorneys specializing in US property law',
      features: ['Contract review', 'Title searches', 'Closing services', 'State-specific expertise'],
      price: 'From $800',
      popular: true
    },
    {
      icon: Calculator,
      title: 'Mortgage Brokers',
      description: 'FHA, VA, conventional, and jumbo loan specialists',
      features: ['Pre-approval', 'Rate comparison', 'First-time buyer programs', 'Refinancing'],
      price: 'Free consultation',
      popular: false
    },
    {
      icon: Camera,
      title: 'Professional Photography',
      description: 'High-quality real estate photography and virtual tours',
      features: ['HDR photography', '3D virtual tours', 'Drone photography', 'Virtual staging'],
      price: 'From $200',
      popular: false
    },
    {
      icon: Home,
      title: 'Home Inspectors',
      description: 'Certified inspectors for all property types',
      features: ['Pre-purchase inspection', 'New construction', 'Specialized inspections', 'Detailed reports'],
      price: 'From $400',
      popular: true
    },
    {
      icon: Shield,
      title: 'Insurance Agents',
      description: 'Homeowners, flood, and specialty insurance',
      features: ['Homeowners insurance', 'Flood insurance', 'Umbrella policies', 'Claims assistance'],
      price: 'Free quotes',
      popular: false
    },
    {
      icon: Truck,
      title: 'Moving Services',
      description: 'Interstate and local moving specialists',
      features: ['Interstate moving', 'Packing services', 'Storage solutions', 'Military discounts'],
      price: 'From $1,200',
      popular: false
    }
  ];

  return (
    <section className="py-16 bg-gradient-to-br from-blue-50 to-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Trusted Service Providers Across America
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Connect with vetted professionals in all 50 states to make your real estate journey smooth and successful
          </p>
          <div className="flex items-center justify-center mt-4">
            <Badge variant="outline" className="text-blue-600 border-blue-200">
              🇺🇸 Nationwide Coverage
            </Badge>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card key={index} className={`relative transition-all duration-300 hover:shadow-lg hover:-translate-y-1 ${service.popular ? 'ring-2 ring-blue-500' : ''}`}>
              {service.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-blue-500 text-white">Most Popular</Badge>
                </div>
              )}
              
              <CardHeader className="text-center pb-4">
                <div className="mx-auto mb-4 p-3 bg-blue-100 rounded-full w-fit">
                  <service.icon className="h-8 w-8 text-blue-600" />
                </div>
                <CardTitle className="text-xl font-bold text-gray-900">
                  {service.title}
                </CardTitle>
                <p className="text-gray-600 text-sm">
                  {service.description}
                </p>
              </CardHeader>

              <CardContent className="space-y-4">
                <div className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                      {feature}
                    </div>
                  ))}
                </div>

                <div className="pt-4 border-t">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-lg font-bold text-blue-600">
                      {service.price}
                    </span>
                    <div className="flex items-center text-sm text-gray-500">
                      <MapPin className="h-4 w-4 mr-1" />
                      All States
                    </div>
                  </div>
                  
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                    Find {service.title}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-gray-600 mb-6">
            Need help choosing the right services for your {userType === 'buyer' ? 'purchase' : 'sale'}?
          </p>
          <Button variant="outline" className="border-blue-500 text-blue-600 hover:bg-blue-50">
            <FileText className="h-4 w-4 mr-2" />
            Get Personalized Recommendations
          </Button>
        </div>

        <div className="mt-16 bg-blue-600 rounded-2xl p-8 text-white text-center">
          <h3 className="text-2xl font-bold mb-4">
            State-Specific Expertise
          </h3>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            Our network includes professionals familiar with local regulations, market conditions, 
            and requirements in all 50 states, from California's disclosure laws to New York's co-op boards.
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold">50</div>
              <div className="text-blue-200 text-sm">States Covered</div>
            </div>
            <div>
              <div className="text-2xl font-bold">5,000+</div>
              <div className="text-blue-200 text-sm">Verified Professionals</div>
            </div>
            <div>
              <div className="text-2xl font-bold">24/7</div>
              <div className="text-blue-200 text-sm">Support Available</div>
            </div>
            <div>
              <div className="text-2xl font-bold">4.8★</div>
              <div className="text-blue-200 text-sm">Average Rating</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
