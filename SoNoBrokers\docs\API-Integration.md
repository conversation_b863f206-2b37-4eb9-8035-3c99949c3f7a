# SoNoBrokers API Integration Guide

## Overview

This document describes the integration between the SoNoBrokers React frontend and .NET Web API backend. The migration from direct Prisma/Supabase calls to a centralized .NET API provides better security, performance, and maintainability.

## Architecture

### Request Flow
```
React Component → Server Action → API Service → .NET Controller → Service Layer → Database
```

### Authentication Flow
```
Clerk (Frontend) → JWT Token → .NET Middleware → Controller Authorization
```

## API Client Configuration

### Base Configuration
```typescript
// src/lib/api-client.ts
import axios from 'axios';
import { auth } from '@clerk/nextjs';

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5005',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(async (config) => {
  const { getToken } = auth();
  const token = await getToken();
  
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  
  return config;
});

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      window.location.href = '/sign-in';
    }
    return Promise.reject(error);
  }
);

export default apiClient;
```

## API Services

### Properties API
```typescript
// src/lib/api/properties-api.ts
import apiClient from '../api-client';
import type { 
  PropertySearchParams, 
  PropertyResponse, 
  CreatePropertyRequest,
  UpdatePropertyRequest 
} from '@/types/property';

export const propertiesApi = {
  // Search properties
  searchProperties: async (params: PropertySearchParams): Promise<PropertyResponse[]> => {
    const response = await apiClient.get('/api/sonobrokers/properties', { params });
    return response.data.data;
  },

  // Get property by ID
  getProperty: async (id: string): Promise<PropertyResponse> => {
    const response = await apiClient.get(`/api/sonobrokers/properties/${id}`);
    return response.data.data;
  },

  // Create new property
  createProperty: async (data: CreatePropertyRequest): Promise<PropertyResponse> => {
    const response = await apiClient.post('/api/sonobrokers/properties', data);
    return response.data.data;
  },

  // Update property
  updateProperty: async (id: string, data: UpdatePropertyRequest): Promise<PropertyResponse> => {
    const response = await apiClient.put(`/api/sonobrokers/properties/${id}`, data);
    return response.data.data;
  },

  // Delete property
  deleteProperty: async (id: string): Promise<void> => {
    await apiClient.delete(`/api/sonobrokers/properties/${id}`);
  },

  // Get user's properties
  getUserProperties: async (userId: string): Promise<PropertyResponse[]> => {
    const response = await apiClient.get(`/api/sonobrokers/properties/user/${userId}`);
    return response.data.data;
  },

  // Get featured properties
  getFeaturedProperties: async (): Promise<PropertyResponse[]> => {
    const response = await apiClient.get('/api/sonobrokers/properties/featured');
    return response.data.data;
  }
};
```

### Users API
```typescript
// src/lib/api/users-api.ts
import apiClient from '../api-client';
import type { UserProfile, UpdateUserRequest } from '@/types/user';

export const usersApi = {
  // Get current user profile
  getProfile: async (): Promise<UserProfile> => {
    const response = await apiClient.get('/api/sonobrokers/users/profile');
    return response.data.data;
  },

  // Update user profile
  updateProfile: async (data: UpdateUserRequest): Promise<UserProfile> => {
    const response = await apiClient.put('/api/sonobrokers/users/profile', data);
    return response.data.data;
  },

  // Get user by ID
  getUser: async (id: string): Promise<UserProfile> => {
    const response = await apiClient.get(`/api/sonobrokers/users/${id}`);
    return response.data.data;
  },

  // Get user's saved properties
  getSavedProperties: async (): Promise<PropertyResponse[]> => {
    const response = await apiClient.get('/api/sonobrokers/users/saved-properties');
    return response.data.data;
  },

  // Save property
  saveProperty: async (propertyId: string): Promise<void> => {
    await apiClient.post(`/api/sonobrokers/users/saved-properties/${propertyId}`);
  },

  // Remove saved property
  removeSavedProperty: async (propertyId: string): Promise<void> => {
    await apiClient.delete(`/api/sonobrokers/users/saved-properties/${propertyId}`);
  }
};
```

### Property Images API
```typescript
// src/lib/api/property-images-api.ts
import apiClient from '../api-client';
import type { PropertyImage, UploadImageRequest } from '@/types/property-image';

export const propertyImagesApi = {
  // Get property images
  getPropertyImages: async (propertyId: string): Promise<PropertyImage[]> => {
    const response = await apiClient.get(`/api/sonobrokers/property-images/${propertyId}`);
    return response.data.data;
  },

  // Upload property image
  uploadImage: async (data: UploadImageRequest): Promise<PropertyImage> => {
    const formData = new FormData();
    formData.append('file', data.file);
    formData.append('propertyId', data.propertyId);
    formData.append('caption', data.caption || '');
    formData.append('isPrimary', data.isPrimary?.toString() || 'false');

    const response = await apiClient.post('/api/sonobrokers/property-images', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.data;
  },

  // Update image
  updateImage: async (id: string, data: Partial<PropertyImage>): Promise<PropertyImage> => {
    const response = await apiClient.put(`/api/sonobrokers/property-images/${id}`, data);
    return response.data.data;
  },

  // Delete image
  deleteImage: async (id: string): Promise<void> => {
    await apiClient.delete(`/api/sonobrokers/property-images/${id}`);
  },

  // Set primary image
  setPrimaryImage: async (propertyId: string, imageId: string): Promise<void> => {
    await apiClient.put(`/api/sonobrokers/property-images/${propertyId}/primary/${imageId}`);
  }
};
```

## Server Actions

### Property Actions
```typescript
// src/lib/actions/property-actions.ts
'use server';

import { propertiesApi } from '@/lib/api/properties-api';
import { revalidatePath } from 'next/cache';
import type { PropertySearchParams, CreatePropertyRequest } from '@/types/property';

export async function getPropertiesAction(params: PropertySearchParams) {
  try {
    const properties = await propertiesApi.searchProperties(params);
    return { success: true, data: properties };
  } catch (error) {
    console.error('Error fetching properties:', error);
    return { success: false, error: 'Failed to fetch properties' };
  }
}

export async function getPropertyAction(id: string) {
  try {
    const property = await propertiesApi.getProperty(id);
    return { success: true, data: property };
  } catch (error) {
    console.error('Error fetching property:', error);
    return { success: false, error: 'Failed to fetch property' };
  }
}

export async function createPropertyAction(data: CreatePropertyRequest) {
  try {
    const property = await propertiesApi.createProperty(data);
    revalidatePath('/dashboard/properties');
    return { success: true, data: property };
  } catch (error) {
    console.error('Error creating property:', error);
    return { success: false, error: 'Failed to create property' };
  }
}

export async function updatePropertyAction(id: string, data: UpdatePropertyRequest) {
  try {
    const property = await propertiesApi.updateProperty(id, data);
    revalidatePath(`/properties/${id}`);
    revalidatePath('/dashboard/properties');
    return { success: true, data: property };
  } catch (error) {
    console.error('Error updating property:', error);
    return { success: false, error: 'Failed to update property' };
  }
}

export async function deletePropertyAction(id: string) {
  try {
    await propertiesApi.deleteProperty(id);
    revalidatePath('/dashboard/properties');
    return { success: true };
  } catch (error) {
    console.error('Error deleting property:', error);
    return { success: false, error: 'Failed to delete property' };
  }
}
```

### User Actions
```typescript
// src/lib/actions/user-actions.ts
'use server';

import { usersApi } from '@/lib/api/users-api';
import { revalidatePath } from 'next/cache';
import type { UpdateUserRequest } from '@/types/user';

export async function getUserProfileAction() {
  try {
    const profile = await usersApi.getProfile();
    return { success: true, data: profile };
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return { success: false, error: 'Failed to fetch user profile' };
  }
}

export async function updateUserProfileAction(data: UpdateUserRequest) {
  try {
    const profile = await usersApi.updateProfile(data);
    revalidatePath('/dashboard/profile');
    return { success: true, data: profile };
  } catch (error) {
    console.error('Error updating user profile:', error);
    return { success: false, error: 'Failed to update user profile' };
  }
}

export async function savePropertyAction(propertyId: string) {
  try {
    await usersApi.saveProperty(propertyId);
    revalidatePath('/dashboard/saved-properties');
    return { success: true };
  } catch (error) {
    console.error('Error saving property:', error);
    return { success: false, error: 'Failed to save property' };
  }
}

export async function removeSavedPropertyAction(propertyId: string) {
  try {
    await usersApi.removeSavedProperty(propertyId);
    revalidatePath('/dashboard/saved-properties');
    return { success: true };
  } catch (error) {
    console.error('Error removing saved property:', error);
    return { success: false, error: 'Failed to remove saved property' };
  }
}
```

## Component Integration

### Using Server Actions in Components
```typescript
// src/components/PropertyList.tsx
import { getPropertiesAction } from '@/lib/actions/property-actions';
import { PropertyCard } from './PropertyCard';

interface PropertyListProps {
  searchParams: PropertySearchParams;
}

export async function PropertyList({ searchParams }: PropertyListProps) {
  const result = await getPropertiesAction(searchParams);

  if (!result.success) {
    return <div>Error: {result.error}</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {result.data.map((property) => (
        <PropertyCard key={property.id} property={property} />
      ))}
    </div>
  );
}
```

### Client-Side API Calls
```typescript
// src/components/PropertyFormClient.tsx
'use client';

import { useState } from 'react';
import { createPropertyAction } from '@/lib/actions/property-actions';
import { useRouter } from 'next/navigation';

export function PropertyFormClient() {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (formData: FormData) => {
    setLoading(true);
    
    const data = {
      title: formData.get('title') as string,
      description: formData.get('description') as string,
      price: Number(formData.get('price')),
      // ... other fields
    };

    const result = await createPropertyAction(data);
    
    if (result.success) {
      router.push(`/properties/${result.data.id}`);
    } else {
      // Handle error
      console.error(result.error);
    }
    
    setLoading(false);
  };

  return (
    <form action={handleSubmit}>
      {/* Form fields */}
      <button type="submit" disabled={loading}>
        {loading ? 'Creating...' : 'Create Property'}
      </button>
    </form>
  );
}
```

## Error Handling

### API Error Response Format
```typescript
interface ApiErrorResponse {
  success: false;
  message: string;
  errors: string[];
  statusCode: number;
  timestamp: string;
}
```

### Error Handling in Services
```typescript
// src/lib/api/error-handler.ts
export function handleApiError(error: any): string {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (error.response?.status === 401) {
    return 'Authentication required';
  }
  
  if (error.response?.status === 403) {
    return 'Access denied';
  }
  
  if (error.response?.status === 404) {
    return 'Resource not found';
  }
  
  if (error.response?.status >= 500) {
    return 'Server error occurred';
  }
  
  return 'An unexpected error occurred';
}
```

## Testing API Integration

### Test Endpoints
```bash
# Test API connectivity
curl http://localhost:5005/api/sonobrokers/test/ping

# Test with authentication
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:5005/api/sonobrokers/properties
```

### Integration Tests
```typescript
// src/__tests__/api/properties.test.ts
import { propertiesApi } from '@/lib/api/properties-api';

describe('Properties API', () => {
  it('should fetch properties', async () => {
    const properties = await propertiesApi.searchProperties({
      page: 1,
      limit: 10
    });
    
    expect(Array.isArray(properties)).toBe(true);
  });
  
  it('should create property', async () => {
    const propertyData = {
      title: 'Test Property',
      description: 'Test Description',
      price: 500000,
      // ... other required fields
    };
    
    const property = await propertiesApi.createProperty(propertyData);
    
    expect(property.id).toBeDefined();
    expect(property.title).toBe(propertyData.title);
  });
});
```

## Performance Optimization

### Caching Strategy
- Use Next.js built-in caching for static data
- Implement SWR or React Query for client-side caching
- Cache API responses at the server level
- Use revalidation for dynamic content

### Request Optimization
- Implement request batching where possible
- Use pagination for large datasets
- Optimize image uploads with compression
- Implement proper loading states

## Security Considerations

### Authentication
- All API calls require valid Clerk JWT tokens
- Tokens are automatically refreshed
- Implement proper logout handling
- Secure token storage

### Authorization
- Role-based access control (RBAC)
- Resource-level permissions
- Input validation and sanitization
- Rate limiting and abuse prevention

## Migration Status

### ✅ Completed
- API client configuration
- Core API services (Properties, Users, Images)
- Server actions implementation
- Authentication integration
- Error handling framework

### 🔄 In Progress
- Complete API endpoint coverage
- Advanced features (Scheduling, Communication)
- Performance optimization
- Comprehensive testing

### 📋 Pending
- Real-time features (WebSocket integration)
- Advanced caching strategies
- Monitoring and analytics
- Production deployment optimization

## Next Steps

1. Complete remaining API endpoints
2. Implement comprehensive error handling
3. Add performance monitoring
4. Create automated tests
5. Optimize for production deployment

For more information, see the main [README.md](./README.md) and API documentation at `/scalar/v1` when running the API.
