# PowerShell script to convert _context references to _dbContext in SoNoBrokers files
# This handles the bulk of simple replacements

param(
    [string]$RootPath = ".",
    [switch]$WhatIf = $false
)

Write-Host "Starting conversion of _context to _dbContext references..." -ForegroundColor Green

# Define the folders to process
$foldersToProcess = @(
    "Controllers\SoNoBrokers",
    "Services\SoNoBrokers"
)

# Define simple replacement patterns
$simpleReplacements = @{
    # Constructor parameter and field assignments
    '_context = context;' = '_dbContext = dbContext;'
    
    # Simple property access patterns that can be directly replaced
    'await _context.SaveChangesAsync()' = '// SaveChanges converted to individual ExecuteAsync calls'
    '_context.SaveChanges()' = '// SaveChanges converted to individual Execute calls'
    
    # Simple field declarations (will need manual review)
    'private readonly.*_context;' = '// MANUAL REVIEW NEEDED: Convert to DapperDbContext _dbContext;'
    
    # Constructor parameters (will need manual review)
    '\(.*_context.*\)' = '// MANUAL REVIEW NEEDED: Update constructor to use DapperDbContext'
}

# Define Entity Framework patterns that need complex conversion
$complexPatterns = @(
    # These patterns require manual conversion to SQL queries
    '_context\..*\.FindAsync\(',
    '_context\..*\.FirstOrDefaultAsync\(',
    '_context\..*\.Where\(',
    '_context\..*\.Include\(',
    '_context\..*\.OrderBy',
    '_context\..*\.ToListAsync\(',
    '_context\..*\.CountAsync\(',
    '_context\..*\.AnyAsync\(',
    '_context\..*\.Add\(',
    '_context\..*\.Remove\(',
    '_context\..*\.Update\('
)

function Process-File {
    param(
        [string]$FilePath
    )
    
    Write-Host "Processing: $FilePath" -ForegroundColor Yellow
    
    $content = Get-Content $FilePath -Raw
    $originalContent = $content
    $hasComplexPatterns = $false
    
    # Check for complex patterns that need manual conversion
    foreach ($pattern in $complexPatterns) {
        if ($content -match $pattern) {
            $hasComplexPatterns = $true
            Write-Host "  Found complex pattern: $pattern" -ForegroundColor Red
        }
    }
    
    # Apply simple replacements
    $replacementCount = 0
    foreach ($find in $simpleReplacements.Keys) {
        $replace = $simpleReplacements[$find]
        if ($content -match $find) {
            $content = $content -replace $find, $replace
            $replacementCount++
            Write-Host "  Applied replacement: $find -> $replace" -ForegroundColor Cyan
        }
    }
    
    # Simple _context to _dbContext replacement (only for variable names, not method calls)
    $simpleContextPattern = '\b_context\b(?!\.[A-Z])'
    if ($content -match $simpleContextPattern) {
        $content = $content -replace $simpleContextPattern, '_dbContext'
        $replacementCount++
        Write-Host "  Replaced simple _context references with _dbContext" -ForegroundColor Cyan
    }
    
    # Write results
    if ($replacementCount -gt 0) {
        if ($WhatIf) {
            Write-Host "  [WHAT-IF] Would make $replacementCount replacements" -ForegroundColor Magenta
        } else {
            Set-Content $FilePath $content -NoNewline
            Write-Host "  Made $replacementCount replacements" -ForegroundColor Green
        }
    }
    
    if ($hasComplexPatterns) {
        Write-Host "  ⚠️  MANUAL REVIEW REQUIRED: Complex Entity Framework patterns found" -ForegroundColor Red
        return $false
    }
    
    return $replacementCount -gt 0
}

function Get-FilesToProcess {
    $files = @()
    
    foreach ($folder in $foldersToProcess) {
        $fullPath = Join-Path $RootPath $folder
        if (Test-Path $fullPath) {
            $csFiles = Get-ChildItem $fullPath -Filter "*.cs" -Recurse
            $files += $csFiles
        } else {
            Write-Warning "Folder not found: $fullPath"
        }
    }
    
    return $files
}

# Main execution
try {
    $files = Get-FilesToProcess
    
    if ($files.Count -eq 0) {
        Write-Warning "No C# files found to process"
        exit 1
    }
    
    Write-Host "Found $($files.Count) C# files to process" -ForegroundColor Green
    
    if ($WhatIf) {
        Write-Host "Running in WHAT-IF mode - no files will be modified" -ForegroundColor Yellow
    }
    
    $processedFiles = 0
    $modifiedFiles = 0
    $filesNeedingManualReview = @()
    
    foreach ($file in $files) {
        $processedFiles++
        $result = Process-File $file.FullName
        
        if ($result) {
            $modifiedFiles++
        }
        
        # Check if file needs manual review (has complex patterns)
        $content = Get-Content $file.FullName -Raw
        $needsReview = $false
        foreach ($pattern in $complexPatterns) {
            if ($content -match $pattern) {
                $needsReview = $true
                break
            }
        }
        
        if ($needsReview) {
            $filesNeedingManualReview += $file.FullName
        }
    }
    
    # Summary
    Write-Host "`n=== CONVERSION SUMMARY ===" -ForegroundColor Green
    Write-Host "Files processed: $processedFiles" -ForegroundColor White
    Write-Host "Files modified: $modifiedFiles" -ForegroundColor Green
    Write-Host "Files needing manual review: $($filesNeedingManualReview.Count)" -ForegroundColor Yellow
    
    if ($filesNeedingManualReview.Count -gt 0) {
        Write-Host "`nFiles requiring manual Entity Framework to Dapper conversion:" -ForegroundColor Red
        foreach ($file in $filesNeedingManualReview) {
            Write-Host "  - $file" -ForegroundColor Red
        }
        
        Write-Host "`nNext steps:" -ForegroundColor Yellow
        Write-Host "1. Review the files listed above" -ForegroundColor White
        Write-Host "2. Convert Entity Framework patterns to Dapper SQL queries" -ForegroundColor White
        Write-Host "3. Use DapperDbContext methods like QueryAsync<T>, ExecuteAsync, etc." -ForegroundColor White
        Write-Host "4. Refer to DapperDbContext_Usage.md for examples" -ForegroundColor White
    }
    
    if (!$WhatIf -and $modifiedFiles -gt 0) {
        Write-Host "`nRecommendation: Run 'dotnet build' to check for compilation errors" -ForegroundColor Cyan
    }
    
} catch {
    Write-Error "Error during conversion: $_"
    exit 1
}

Write-Host "`nConversion script completed!" -ForegroundColor Green
