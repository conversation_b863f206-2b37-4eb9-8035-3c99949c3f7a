using MicroSaasWebApi.Models.SoNoBrokers;

namespace MicroSaasWebApi.Services.SoNoBrokers.Interfaces
{
    public interface IWaitingListService
    {
        Task<WaitingList> AddToWaitingListAsync(string email);
        Task<WaitingList?> GetWaitingListEntryByEmailAsync(string email);
        Task<IEnumerable<WaitingList>> GetAllWaitingListEntriesAsync();
        Task<bool> RemoveFromWaitingListAsync(string email);
    }
}
