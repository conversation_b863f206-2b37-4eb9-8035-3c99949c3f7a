'use client'

/**
 * CountryDetector Component
 * Handles IP-based country detection and routing on page load
 * Integrates with AppContext for country state management
 */

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAppContext } from '@/contexts/AppContext'
import {
  initializeCountryRouting,
  performCountryRedirect,
  getCurrentCountryFromPath,
  Country
} from '@/lib/geo'

interface CountryDetectorProps {
  children: React.ReactNode
}

export function CountryDetector({ children }: CountryDetectorProps) {
  const [isDetecting, setIsDetecting] = useState(true)
  const [hasRedirected, setHasRedirected] = useState(false)
  const router = useRouter()
  const pathname = usePathname()
  const { setCountry, validateAndSetRegion } = useAppContext()

  useEffect(() => {
    let isMounted = true

    const detectAndRoute = async () => {
      try {
        console.log('CountryDetector: Starting country detection...')

        // Check if we're already on a valid country route
        const currentCountry = getCurrentCountryFromPath()
        if (currentCountry) {
          console.log(`CountryDetector: Already on valid country route: ${currentCountry}`)
          setCountry(currentCountry)
          setIsDetecting(false)
          return
        }

        // Initialize country routing (IP detection + validation)
        const routing = await initializeCountryRouting()

        if (!isMounted) return

        if (routing.shouldRedirect && !hasRedirected) {
          console.log(`CountryDetector: Redirecting to ${routing.redirectPath}`)
          setHasRedirected(true)

          // Use Next.js router for client-side navigation
          router.push(routing.redirectPath)
          return
        }

        // Set the detected country in context
        setCountry(routing.detectedCountry)
        console.log(`CountryDetector: Country set to ${routing.detectedCountry}`)

      } catch (error) {
        console.error('CountryDetector: Error during country detection:', error)
        // Fallback to Canada
        setCountry(Country.CA)
      } finally {
        if (isMounted) {
          setIsDetecting(false)
        }
      }
    }

    // Only run on client side
    if (typeof window !== 'undefined') {
      detectAndRoute()
    } else {
      setIsDetecting(false)
    }

    return () => {
      isMounted = false
    }
  }, [pathname, router, setCountry, hasRedirected])

  // Show loading state while detecting country
  if (isDetecting) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-sm text-muted-foreground">Detecting your location...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}

/**
 * CountryRedirectHandler Component
 * Handles server-side country detection and client-side routing
 * Should be used in layout.tsx or page.tsx
 */
export function CountryRedirectHandler(): null {
  const pathname = usePathname()
  const router = useRouter()

  useEffect(() => {
    const handleCountryRedirect = async () => {
      try {
        // Only run on client side
        if (typeof window === 'undefined') return

        // Skip if already on a country route
        const currentCountry = getCurrentCountryFromPath()
        if (currentCountry) return

        // Initialize country routing
        const routing = await initializeCountryRouting()

        if (routing.shouldRedirect) {
          console.log(`CountryRedirectHandler: Redirecting to ${routing.redirectPath}`)
          router.replace(routing.redirectPath)
        }
      } catch (error) {
        console.error('CountryRedirectHandler: Error:', error)
      }
    }

    handleCountryRedirect()
  }, [pathname, router])

  return null
}

/**
 * useCountryDetection Hook
 * Custom hook for country detection in components
 */
export function useCountryDetection() {
  const [detectedCountry, setDetectedCountry] = useState<Country | null>(null)
  const [isDetecting, setIsDetecting] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const detectCountry = async () => {
      try {
        setIsDetecting(true)
        setError(null)

        const routing = await initializeCountryRouting()
        setDetectedCountry(routing.detectedCountry)
      } catch (err) {
        console.error('useCountryDetection: Error:', err)
        setError(err instanceof Error ? err.message : 'Failed to detect country')
        setDetectedCountry(Country.CA) // Fallback
      } finally {
        setIsDetecting(false)
      }
    }

    if (typeof window !== 'undefined') {
      detectCountry()
    } else {
      setIsDetecting(false)
    }
  }, [])

  return {
    detectedCountry,
    isDetecting,
    error
  }
}

export default CountryDetector
