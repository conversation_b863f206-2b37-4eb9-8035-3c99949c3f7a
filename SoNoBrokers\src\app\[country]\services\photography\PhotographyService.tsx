import React from 'react'
import { ServiceLayout } from '@/components/shared/services/ServiceLayout'

interface PhotographyServiceProps {
  userType: 'buyer' | 'seller'
  isSignedIn: boolean
  country: string
}

// Mock data - in real implementation, this would come from your database and Google Places API
const getMockProviders = (country: string) => {
  const baseProviders = [
    {
      id: '1',
      name: '<PERSON>',
      businessName: 'Parker Real Estate Photography',
      serviceType: 'Professional Photographer',
      location: country === 'CA' ? 'Toronto, ON' : 'New York, NY',
      distance: '2.0 km',
      rating: 4.9,
      reviewCount: 187,
      price: 'From $250',
      specialties: ['HDR Photography', 'Virtual Tours', 'Drone Footage', 'Twilight Photos'],
      verified: true,
      isAdvertiser: true,
      isPremium: true,
      image: '/api/placeholder/80/80',
      phone: '******-0171',
      email: '<EMAIL>',
      website: 'https://parkerphotography.com',
      description: 'Award-winning real estate photographer specializing in luxury properties. Professional HDR photography and virtual tours.',
      coordinates: country === 'CA' ? { lat: 43.6532, lng: -79.3832 } : { lat: 40.7128, lng: -74.0060 }
    },
    {
      id: '2',
      name: '<PERSON>',
      businessName: 'Martinez Visual Media',
      serviceType: 'Real Estate Photographer',
      location: country === 'CA' ? 'Vancouver, BC' : 'Los Angeles, CA',
      distance: '3.5 km',
      rating: 4.8,
      reviewCount: 156,
      price: 'From $200',
      specialties: ['Interior Photography', 'Exterior Shots', 'Video Tours', 'Floor Plans'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/80/80',
      phone: '******-0172',
      description: 'Creative photographer with expertise in showcasing properties at their best. Fast turnaround and competitive pricing.',
      coordinates: country === 'CA' ? { lat: 49.2827, lng: -123.1207 } : { lat: 34.0522, lng: -118.2437 }
    },
    {
      id: '3',
      name: 'Amanda Foster',
      businessName: 'Foster Photography Studio',
      serviceType: 'Property Photographer',
      location: country === 'CA' ? 'Calgary, AB' : 'Chicago, IL',
      distance: '4.8 km',
      rating: 4.7,
      reviewCount: 134,
      price: 'From $180',
      specialties: ['Budget Photography', 'Quick Turnaround', 'Basic Editing', 'Social Media Ready'],
      verified: true,
      isAdvertiser: true,
      isPremium: false,
      image: '/api/placeholder/80/80',
      description: 'Affordable real estate photography without compromising quality. Perfect for budget-conscious sellers.',
      coordinates: country === 'CA' ? { lat: 51.0447, lng: -114.0719 } : { lat: 41.8781, lng: -87.6298 }
    }
  ]

  // Add Google API providers (non-registered)
  const googleProviders = [
    {
      id: 'g1',
      name: 'Quick Snap Photography',
      businessName: 'Quick Snap Photography',
      serviceType: 'Photographer',
      location: country === 'CA' ? 'Mississauga, ON' : 'Brooklyn, NY',
      distance: '7.2 km',
      rating: 4.4,
      reviewCount: 67,
      price: 'From $150',
      specialties: ['Basic Photos', 'Same Day Service'],
      verified: false,
      isAdvertiser: false,
      isPremium: false,
      description: 'Basic real estate photography services with quick turnaround.',
      coordinates: country === 'CA' ? { lat: 43.5890, lng: -79.6441 } : { lat: 40.6782, lng: -73.9442 }
    }
  ]

  return [...baseProviders, ...googleProviders]
}

export function PhotographyService({
  userType,
  isSignedIn,
  country
}: PhotographyServiceProps) {
  // Get providers data (this would be async in real implementation)
  const mockProviders = getMockProviders(country)

  // Sort by distance and premium status
  const providers = mockProviders.sort((a, b) => {
    if (a.isPremium && !b.isPremium) return -1
    if (!a.isPremium && b.isPremium) return 1
    if (a.isAdvertiser && !b.isAdvertiser) return -1
    if (!a.isAdvertiser && b.isAdvertiser) return 1
    return parseFloat(a.distance) - parseFloat(b.distance)
  })

  const serviceDescription = userType === 'seller'
    ? 'Professional real estate photography services to showcase your property at its best. Our photographers specialize in high-quality images, virtual tours, and drone footage that help your listing stand out and sell faster.'
    : 'Professional photography services for property documentation and inspection purposes. High-quality photos for insurance, records, or personal use during your home buying process.'

  // Check environment variable for Google providers
  const showGoogleProviders = process.env.NEXT_PUBLIC_ENABLE_GOOGLE_PROVIDERS !== 'false'

  return (
    <ServiceLayout
      userType={userType}
      isSignedIn={isSignedIn}
      serviceTitle="Photography Services"
      serviceDescription={serviceDescription}
      country={country}
      providers={providers}
      showGoogleProviders={showGoogleProviders}
    />
  )
}
