import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import Image from 'next/image';

interface Property {
  id: string;
  title: string;
  price: number;
  location: string;
  imageUrl: string;
  bedrooms: number;
  bathrooms: number;
  squareFeet: number;
  matchScore: number;
}

interface PropertyRecommendationsProps {
  recommendations: Property[];
  userRole: 'buyer' | 'seller';
}

export function PropertyRecommendations({
  recommendations,
  userRole,
}: PropertyRecommendationsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {userRole === 'buyer'
            ? 'Recommended Properties'
            : 'Similar Properties'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {recommendations.map((property) => (
            <Card key={property.id} className="overflow-hidden">
              <div className="relative h-48">
                <Image
                  src={property.imageUrl}
                  alt={property.title}
                  fill
                  className="object-cover"
                />
                <div className="absolute top-2 right-2 bg-primary text-primary-foreground px-2 py-1 rounded-full text-sm">
                  {property.matchScore}% Match
                </div>
              </div>
              <CardContent className="p-4">
                <h3 className="font-semibold text-lg mb-2">{property.title}</h3>
                <p className="text-2xl font-bold mb-2">
                  ${property.price.toLocaleString()}
                </p>
                <p className="text-muted-foreground mb-2">{property.location}</p>
                <div className="flex gap-4 text-sm text-muted-foreground mb-4">
                  <span>{property.bedrooms} beds</span>
                  <span>{property.bathrooms} baths</span>
                  <span>{property.squareFeet} sqft</span>
                </div>
                <Button asChild className="w-full">
                  <Link href={`/properties/${property.id}`}>View Details</Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
} 