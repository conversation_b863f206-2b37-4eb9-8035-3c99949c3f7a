import { MortgageCalculator } from '@/components/shared/services/MortgageCalculator'
import { redirect } from 'next/navigation'

interface PageProps {
  params: Promise<{
    country: string
  }>
  searchParams: Promise<{
    userType?: 'buyer' | 'seller'
  }>
}

export default async function MortgageCalculatorPage({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Validate country
  const validCountries = ['ca', 'us']
  const country = resolvedParams.country.toLowerCase()

  if (!validCountries.includes(country)) {
    redirect('/ca/services/mortgage-calculator')
  }

  // Default to buyer for mortgage calculator
  const userType = resolvedSearchParams.userType || 'buyer'

  return (
    <MortgageCalculator
      userType={userType}
      country={country.toUpperCase()}
    />
  )
}

export async function generateMetadata({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const userType = resolvedSearchParams.userType || 'buyer'
  const country = resolvedParams.country.toUpperCase()

  return {
    title: `Mortgage Calculator for ${country} | SoNoBrokers`,
    description: `Free mortgage calculator for ${country}. Calculate monthly payments, compare loan terms, and plan your home purchase budget with accurate payment estimates.`,
    keywords: `mortgage calculator, home loan calculator, monthly payment, ${country}, mortgage rates, home affordability`,
  }
}
