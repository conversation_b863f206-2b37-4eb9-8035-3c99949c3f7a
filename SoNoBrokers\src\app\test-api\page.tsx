'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, Loader2 } from 'lucide-react'

interface ApiTestResult {
  endpoint: string
  status: 'success' | 'error' | 'loading'
  response?: any
  error?: string
  duration?: number
}

export default function TestApiPage() {
  const [testResults, setTestResults] = useState<ApiTestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const apiEndpoints = [
    { name: 'Ping Test', url: '/api/sonobrokers/test/ping' },
    { name: 'Health Check', url: '/api/sonobrokers/test/health' },
    { name: 'Properties List', url: '/api/sonobrokers/properties' },
    { name: 'Users Profile', url: '/api/sonobrokers/users/profile' },
    { name: 'Enums', url: '/api/sonobrokers/communication/enums' },
  ]

  const testEndpoint = async (endpoint: { name: string; url: string }): Promise<ApiTestResult> => {
    const startTime = Date.now()

    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://localhost:7163'
      const response = await fetch(`${baseUrl}${endpoint.url}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const duration = Date.now() - startTime

      if (response.ok) {
        const data = await response.json()
        return {
          endpoint: endpoint.name,
          status: 'success',
          response: data,
          duration,
        }
      } else {
        return {
          endpoint: endpoint.name,
          status: 'error',
          error: `HTTP ${response.status}: ${response.statusText}`,
          duration,
        }
      }
    } catch (error) {
      const duration = Date.now() - startTime
      return {
        endpoint: endpoint.name,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        duration,
      }
    }
  }

  const runAllTests = async () => {
    setIsRunning(true)
    setTestResults([])

    // Initialize with loading states
    const initialResults = apiEndpoints.map(endpoint => ({
      endpoint: endpoint.name,
      status: 'loading' as const,
    }))
    setTestResults(initialResults)

    // Run tests sequentially
    for (let i = 0; i < apiEndpoints.length; i++) {
      const result = await testEndpoint(apiEndpoints[i])

      setTestResults(prev =>
        prev.map((item, index) =>
          index === i ? result : item
        )
      )
    }

    setIsRunning(false)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'loading':
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
      default:
        return null
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-500">Success</Badge>
      case 'error':
        return <Badge variant="destructive">Error</Badge>
      case 'loading':
        return <Badge variant="secondary">Loading...</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">API Connection Test</h1>
        <p className="text-muted-foreground">
          Test the connection between React frontend and .NET Core Web API
        </p>
      </div>

      <div className="mb-6">
        <Card>
          <CardHeader>
            <CardTitle>API Configuration</CardTitle>
            <CardDescription>Current API settings</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>
                <strong>Base URL:</strong> {process.env.NEXT_PUBLIC_API_BASE_URL || 'https://localhost:7163'}
              </div>
              <div>
                <strong>Environment:</strong> {process.env.NODE_ENV}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="mb-6">
        <Button
          onClick={runAllTests}
          disabled={isRunning}
          className="w-full"
        >
          {isRunning ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Running Tests...
            </>
          ) : (
            'Run API Tests'
          )}
        </Button>
      </div>

      <div className="space-y-4">
        {testResults.map((result, index) => (
          <Card key={index}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  {getStatusIcon(result.status)}
                  {result.endpoint}
                </CardTitle>
                {getStatusBadge(result.status)}
              </div>
              {result.duration && (
                <CardDescription>
                  Response time: {result.duration}ms
                </CardDescription>
              )}
            </CardHeader>
            <CardContent>
              {result.error && (
                <div className="text-red-500 mb-2">
                  <strong>Error:</strong> {result.error}
                </div>
              )}
              {result.response && (
                <div>
                  <strong>Response:</strong>
                  <pre className="mt-2 p-3 bg-muted rounded-md text-sm overflow-auto">
                    {JSON.stringify(result.response, null, 2)}
                  </pre>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
