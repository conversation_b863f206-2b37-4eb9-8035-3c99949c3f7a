'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface SelectorChipsProps {
  options: Array<{ value: string; label: string }> | string[]
  selectedValues?: string[]
  selected?: string[]
  onSelectionChange?: (values: string[]) => void
  onChange?: (values: string[]) => void
  placeholder?: string
  className?: string
  maxSelections?: number
}

export function SelectorChips({
  options,
  selectedValues,
  selected,
  onSelectionChange,
  onChange,
  placeholder = "Select options...",
  className,
  maxSelections
}: SelectorChipsProps) {
  // Support both prop names for backward compatibility
  const currentSelected = selectedValues || selected || []
  const handleChange = onSelectionChange || onChange || (() => { })

  // Normalize options to always be objects with value and label
  const normalizedOptions = options.map(option =>
    typeof option === 'string'
      ? { value: option, label: option }
      : option
  )
  
  const handleToggle = (value: string) => {
    const isSelected = currentSelected.includes(value)

    if (isSelected) {
      // Remove from selection
      handleChange(currentSelected.filter(v => v !== value))
    } else {
      // Add to selection (if not at max limit)
      if (!maxSelections || currentSelected.length < maxSelections) {
        handleChange([...currentSelected, value])
      }
    }
  }

  const handleRemove = (value: string) => {
    handleChange(currentSelected.filter(v => v !== value))
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Selected chips */}
      {currentSelected.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {currentSelected.map(value => {
            const option = normalizedOptions.find(opt => opt.value === value)
            return (
              <Badge
                key={value}
                variant="secondary"
                className="flex items-center gap-1 px-2 py-1"
              >
                {option?.label || value}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-transparent"
                  onClick={() => handleRemove(value)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )
          })}
        </div>
      )}

      {/* Available options */}
      <div className="flex flex-wrap gap-2">
        {normalizedOptions
          .filter(option => !currentSelected.includes(option.value))
          .map(option => (
            <Button
              key={option.value}
              variant="outline"
              size="sm"
              onClick={() => handleToggle(option.value)}
              disabled={maxSelections && currentSelected.length >= maxSelections}
              className="h-8 px-3 text-sm"
            >
              {option.label}
            </Button>
          ))}
      </div>

      {currentSelected.length === 0 && (
        <p className="text-sm text-muted-foreground">{placeholder}</p>
      )}
    </div>
  )
}
