'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Star, MapPin, Phone, Mail, Camera, Scale, Search, CheckCircle } from 'lucide-react'

interface ServiceProvidersProps {
  userType: 'buyer' | 'seller'
}

// Mock service provider data
const mockProviders = [
  {
    id: 1,
    name: "<PERSON>",
    type: "Real Estate Lawyer",
    location: "Toronto, ON",
    rating: 4.9,
    reviews: 127,
    price: "From $1,200",
    specialties: ["Property Transactions", "Title Searches", "Contract Review"],
    verified: true,
    image: "/api/placeholder/80/80"
  },
  {
    id: 2,
    name: "<PERSON>",
    type: "Property Photographer",
    location: "Vancouver, BC",
    rating: 4.8,
    reviews: 89,
    price: "From $350",
    specialties: ["Interior Photography", "Drone Shots", "Virtual Tours"],
    verified: true,
    image: "/api/placeholder/80/80"
  },
  {
    id: 3,
    name: "<PERSON>",
    type: "Home Inspector",
    location: "Calgary, AB",
    rating: 4.9,
    reviews: 156,
    price: "From $450",
    specialties: ["Full Inspections", "Thermal Imaging", "Detailed Reports"],
    verified: true,
    image: "/api/placeholder/80/80"
  }
]

export function ServiceProviders({ userType }: ServiceProvidersProps) {
  return (
    <section className="py-16 bg-accent/5">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-foreground mb-4">
            Professional Services
          </h2>
          <p className="text-lg text-muted-foreground">
            {userType === 'buyer' 
              ? 'Connect with trusted professionals for your property purchase'
              : 'Get professional support to sell your property faster'
            }
          </p>
        </div>

        {/* Service Categories */}
        <div className="grid md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto">
          <Card className="bg-background border border-border shadow-lg text-center p-6">
            <Scale className="h-12 w-12 text-accent mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-foreground mb-2">Legal Services</h3>
            <p className="text-muted-foreground text-sm">
              Experienced real estate lawyers for contracts and closings
            </p>
          </Card>
          
          <Card className="bg-background border border-border shadow-lg text-center p-6">
            <Camera className="h-12 w-12 text-accent mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-foreground mb-2">Photography</h3>
            <p className="text-muted-foreground text-sm">
              Professional property photography and virtual tours
            </p>
          </Card>
          
          <Card className="bg-background border border-border shadow-lg text-center p-6">
            <Search className="h-12 w-12 text-accent mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-foreground mb-2">Inspections</h3>
            <p className="text-muted-foreground text-sm">
              Thorough property inspections and detailed reports
            </p>
          </Card>
        </div>

        {/* Featured Providers */}
        <div className="space-y-6 max-w-4xl mx-auto">
          <h3 className="text-2xl font-bold text-foreground text-center mb-8">
            Featured Service Providers
          </h3>
          
          {mockProviders.map((provider) => (
            <Card key={provider.id} className="bg-background border border-border shadow-lg hover:shadow-xl transition-shadow">
              <CardContent className="p-6">
                <div className="flex flex-col md:flex-row gap-6">
                  {/* Provider Info */}
                  <div className="flex-1 space-y-4">
                    <div className="flex items-start gap-4">
                      <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center">
                        <span className="text-2xl font-bold text-muted-foreground">
                          {provider.name.charAt(0)}
                        </span>
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="text-xl font-semibold text-foreground">
                            {provider.name}
                          </h4>
                          {provider.verified && (
                            <CheckCircle className="h-5 w-5 text-green-600" />
                          )}
                        </div>
                        
                        <p className="text-accent font-medium mb-2">{provider.type}</p>
                        
                        <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4" />
                            <span>{provider.location}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span>{provider.rating}</span>
                            <span>({provider.reviews} reviews)</span>
                          </div>
                        </div>
                        
                        <div className="flex flex-wrap gap-2 mb-4">
                          {provider.specialties.map((specialty) => (
                            <Badge key={specialty} variant="outline" className="text-xs">
                              {specialty}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Pricing & Actions */}
                  <div className="md:w-48 space-y-4">
                    <div className="text-center md:text-right">
                      <p className="text-2xl font-bold text-foreground">{provider.price}</p>
                      <p className="text-sm text-muted-foreground">Starting price</p>
                    </div>
                    
                    <div className="space-y-2">
                      <Button className="w-full">
                        Book Service
                      </Button>
                      <Button variant="outline" className="w-full">
                        View Profile
                      </Button>
                    </div>
                    
                    <div className="flex justify-center md:justify-end gap-2">
                      <Button variant="ghost" size="sm">
                        <Phone className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Mail className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button size="lg" variant="outline" className="text-lg px-8">
            View All Service Providers
          </Button>
        </div>
      </div>
    </section>
  )
}
