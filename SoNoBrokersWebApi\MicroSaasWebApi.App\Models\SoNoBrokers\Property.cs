using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace MicroSaasWebApi.Models.SoNoBrokers
{

    [Table("Property", Schema = "snb")]
    public class Property
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string SellerId { get; set; } = string.Empty;

        [Required]
        public string Title { get; set; } = string.Empty;

        public string? Description { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }

        public int? Bedrooms { get; set; }

        public int? Bathrooms { get; set; }

        public int? Sqft { get; set; }

        [Required]
        public string PropertyType { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "jsonb")]
        public string Address { get; set; } = string.Empty;

        [Column(TypeName = "jsonb")]
        public string? Coordinates { get; set; }

        [Column(TypeName = "jsonb")]
        public string? Features { get; set; }

        public PropertyStatus Status { get; set; } = PropertyStatus.pending;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? ExpiresAt { get; set; }

        public bool ListedByBuyer { get; set; } = false;

        public string? BuyerId { get; set; }

        [Column(TypeName = "jsonb")]
        public string? Amenities { get; set; }

        public int? YearBuilt { get; set; }

        public int? LotSize { get; set; }

        // Enhanced property fields
        public string? AdType { get; set; }

        public int? Storeys { get; set; }

        [Column(TypeName = "jsonb")]
        public string? ParkingTypes { get; set; }

        [Column(TypeName = "jsonb")]
        public string? ExtraFeatures { get; set; }

        public string? MlsNumber { get; set; }

        public string? ListingId { get; set; }

        public string? Province { get; set; }

        public string? PostalCode { get; set; }

        public string? City { get; set; }

        public string? Neighborhood { get; set; }

        public string? LotSizeUnit { get; set; }

        [Column(TypeName = "jsonb")]
        public string? PriceHistory { get; set; }

        public string? VirtualTour { get; set; }

        [Column(TypeName = "jsonb")]
        public string? OpenHouse { get; set; }

        public bool IsLiked { get; set; } = false;

        [Column(TypeName = "jsonb")]
        public string? LikedBy { get; set; }

        [Column(TypeName = "jsonb")]
        public string? DistanceFilters { get; set; }

        // AI-powered features
        public string? AiGeneratedDescription { get; set; }

        [Column(TypeName = "jsonb")]
        public string? AiReports { get; set; }

        [Column(TypeName = "jsonb")]
        public string? OpenHouseQrData { get; set; }

        public int? WalkabilityScore { get; set; }

        public int? NeighborhoodScore { get; set; }

        public bool AiAnalysisGenerated { get; set; } = false;

        public DateTime? AiGeneratedAt { get; set; }

        // Navigation properties
        [ForeignKey("SellerId")]
        public virtual User Seller { get; set; } = null!;

        public virtual ICollection<PropertyImage> Images { get; set; } = new List<PropertyImage>();
        public virtual ICollection<ServiceBooking> Bookings { get; set; } = new List<ServiceBooking>();
        public virtual ICollection<PropertyViewing> Viewings { get; set; } = new List<PropertyViewing>();
        public virtual ICollection<Conversation> Conversations { get; set; } = new List<Conversation>();
        public virtual ICollection<BuyerListings> BuyerListings { get; set; } = new List<BuyerListings>();
        public virtual ICollection<OpenHouseAccess> OpenHouseAccesses { get; set; } = new List<OpenHouseAccess>();
        public virtual ICollection<BuyerOffer> BuyerOffers { get; set; } = new List<BuyerOffer>();
        public virtual ICollection<AIReport> AiReportsRelation { get; set; } = new List<AIReport>();

        // Helper methods for JSON fields
        public T? GetAddressAs<T>() where T : class
        {
            if (string.IsNullOrEmpty(Address)) return null;
            try
            {
                return JsonSerializer.Deserialize<T>(Address);
            }
            catch
            {
                return null;
            }
        }

        public void SetAddress<T>(T address) where T : class
        {
            Address = address != null ? JsonSerializer.Serialize(address) : null;
        }

        public T? GetCoordinatesAs<T>() where T : class
        {
            if (string.IsNullOrEmpty(Coordinates)) return null;
            try
            {
                return JsonSerializer.Deserialize<T>(Coordinates);
            }
            catch
            {
                return null;
            }
        }

        public void SetCoordinates<T>(T coordinates) where T : class
        {
            Coordinates = coordinates != null ? JsonSerializer.Serialize(coordinates) : null;
        }

        public T? GetFeaturesAs<T>() where T : class
        {
            if (string.IsNullOrEmpty(Features)) return null;
            try
            {
                return JsonSerializer.Deserialize<T>(Features);
            }
            catch
            {
                return null;
            }
        }

        public void SetFeatures<T>(T features) where T : class
        {
            Features = features != null ? JsonSerializer.Serialize(features) : null;
        }
    }
}
