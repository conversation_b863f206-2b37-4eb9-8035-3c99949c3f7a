"use server";
import { currentUser } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { SnbUserType } from '@prisma/client';

// Helper to get current user's role
async function getCurrentUserRole() {
  const user = await currentUser();
  if (!user) throw new Error('Not authenticated');
  const dbUser = await prisma.user.findUnique({ where: { email: user.emailAddresses[0].emailAddress } });
  if (!dbUser) throw new Error('User not found');
  return dbUser.userType;
}

// Check if user is admin (based on email for now since enum only has buyer/seller)
function isAdminUser(email: string): boolean {
  const adminEmails = ['<EMAIL>'];
  return adminEmails.includes(email);
}

// Admin: Add or update user (limited to buyer/seller types in current schema)
export async function addOrUpdateUser(email: string, userType: 'buyer' | 'seller') {
  const currentUserData = await currentUser();
  if (!currentUserData) throw new Error('Not authenticated');

  // Check if current user is admin
  if (!isAdminUser(currentUserData.emailAddresses[0].emailAddress)) {
    throw new Error('Only admin can manage users');
  }

  if (!email) throw new Error('Email required');

  // Validate userType
  if (!['buyer', 'seller'].includes(userType)) {
    throw new Error('Invalid user type. Must be buyer or seller.');
  }

  // Upsert user
  const user = await prisma.user.upsert({
    where: { email },
    update: { userType: userType as SnbUserType },
    create: {
      email,
      fullName: email.split('@')[0],
      userType: userType as SnbUserType,
    },
  });
  return { success: true, user };
}

// Get user by email
export async function getUserByEmail(email: string) {
  const currentUserData = await currentUser();
  if (!currentUserData) throw new Error('Not authenticated');

  // Check if current user is admin
  if (!isAdminUser(currentUserData.emailAddresses[0].emailAddress)) {
    throw new Error('Only admin can view user details');
  }

  const user = await prisma.user.findUnique({ where: { email } });
  return user;
}