"use server";
import { currentUser } from '@clerk/nextjs/server';
// TODO: Migrate to .NET Web API - temporarily disabled
// import { prisma } from '@/lib/prisma';
import { SnbUserType } from '@/types';

// Helper to get current user's role from database after login
async function getCurrentUserRole(): Promise<SnbUserType> {
  const user = await currentUser();
  if (!user) throw new Error('Not authenticated');

  try {
    const userEmail = user.emailAddresses[0].emailAddress;

    // Call .NET Web API to get user details from auth.users or public.user table
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/sonobrokers/users/email/${encodeURIComponent(userEmail)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
        // Note: Server-side calls don't need Authorization header for internal API calls
      }
    });

    if (response.ok) {
      const result = await response.json();
      if (result.success && result.data) {
        return result.data.userType as SnbUserType;
      }
    }

    // If user not found in database, create them first
    const createResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/sonobrokers/clerk/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
        // Note: Server-side calls don't need Authorization header for internal API calls
      },
      body: JSON.stringify({
        clerkUserId: user.id,
        email: userEmail,
        fullName: `${user.firstName || ''} ${user.lastName || ''}`.trim() || userEmail,
        firstName: user.firstName,
        lastName: user.lastName,
        userType: 'Buyer' // Default
      })
    });

    if (createResponse.ok) {
      const createResult = await createResponse.json();
      if (createResult.success && createResult.data) {
        return createResult.data.userType as SnbUserType;
      }
    }

    // Fallback to default
    return 'Buyer' as SnbUserType;

  } catch (error) {
    console.error('Error getting user role from database:', error);
    return 'Buyer' as SnbUserType; // Fallback
  }
}

// Check if user is admin (based on email for now since enum only has buyer/seller)
function isAdminUser(email: string): boolean {
  const adminEmails = ['<EMAIL>'];
  return adminEmails.includes(email);
}

// Admin: Add or update user (limited to buyer/seller types in current schema)
export async function addOrUpdateUser(email: string, userType: 'buyer' | 'seller') {
  const currentUserData = await currentUser();
  if (!currentUserData) throw new Error('Not authenticated');

  // Check if current user is admin
  if (!isAdminUser(currentUserData.emailAddresses[0].emailAddress)) {
    throw new Error('Only admin can manage users');
  }

  if (!email) throw new Error('Email required');

  // Validate userType
  if (!['buyer', 'seller'].includes(userType)) {
    throw new Error('Invalid user type. Must be buyer or seller.');
  }

  // TODO: Migrate to .NET Web API - temporarily disabled
  // Upsert user
  // const user = await prisma.user.upsert({
  //   where: { email },
  //   update: { userType: userType as SnbUserType },
  //   create: {
  //     email,
  //     fullName: email.split('@')[0],
  //     userType: userType as SnbUserType,
  //   },
  // });

  console.log('User management - migrating to .NET Web API');
  return { success: true, user: { email, userType } };
}

// Get user by email
export async function getUserByEmail(email: string): Promise<any> {
  const currentUserData = await currentUser();
  if (!currentUserData) throw new Error('Not authenticated');

  // Check if current user is admin
  if (!isAdminUser(currentUserData.emailAddresses[0].emailAddress)) {
    throw new Error('Only admin can view user details');
  }

  // TODO: Migrate to .NET Web API - temporarily return null
  // const user = await prisma.user.findUnique({ where: { email } });
  console.log('Get user by email - migrating to .NET Web API');
  return null;
}