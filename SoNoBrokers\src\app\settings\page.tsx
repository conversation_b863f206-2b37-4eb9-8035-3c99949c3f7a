'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAppContext } from '@/contexts/AppContext'
import { useUser } from '@clerk/nextjs'

// Helper function for role-based routing
const getRoleSettingsPath = (role: string): string => {
  const roleMap: Record<string, string> = {
    'ADMIN': 'admin',
    'PRODUCT': 'product',
    'OPERATOR': 'operator',
    'SERVICE_PROVIDER': 'service-provider',
    'USER': 'user'
  }
  return roleMap[role] || 'user'
}

export default function SettingsPage() {
  const router = useRouter()
  const { country, snbUser, isLoading } = useAppContext()
  const { isLoaded } = useUser()

  useEffect(() => {
    if (isLoaded && !isLoading) {
      // Redirect to role-based settings page
      const role = snbUser?.role || 'USER'
      const rolePath = getRoleSettingsPath(role)
      const countryCode = country?.toLowerCase() || 'ca'
      
      router.push(`/${countryCode}/${rolePath}/settings`)
    }
  }, [isLoaded, isLoading, snbUser, country, router])

  // Show loading while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to your settings...</p>
      </div>
    </div>
  )
}
