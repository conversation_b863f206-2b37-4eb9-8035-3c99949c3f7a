@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mapbox GL JS CSS */
@import "mapbox-gl/dist/mapbox-gl.css";
/* npx shadcn@latest add https://tweakcn.com/r/themes/supabase.json */
:root {
  --background: #fcfcfc;
  --foreground: #171717;
  --card: #fcfcfc;
  --card-foreground: #171717;
  --popover: #fcfcfc;
  --popover-foreground: #525252;
  --primary: #72e3ad;
  --primary-foreground: #1e2723;
  --secondary: #fdfdfd;
  --secondary-foreground: #171717;
  --muted: #ededed;
  --muted-foreground: #202020;
  --accent: #ededed;
  --accent-foreground: #202020;
  --destructive: #ca3214;
  --destructive-foreground: #fffcfc;
  --border: #dfdfdf;
  --input: #f6f6f6;
  --ring: #72e3ad;
  --chart-1: #72e3ad;
  --chart-2: #3b82f6;
  --chart-3: #8b5cf6;
  --chart-4: #f59e0b;
  --chart-5: #10b981;
  --sidebar: #fcfcfc;
  --sidebar-foreground: #707070;
  --sidebar-primary: #72e3ad;
  --sidebar-primary-foreground: #1e2723;
  --sidebar-accent: #ededed;
  --sidebar-accent-foreground: #202020;
  --sidebar-border: #dfdfdf;
  --sidebar-ring: #72e3ad;
  --font-sans: Outfit, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.09);
  --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.09);
  --shadow-sm: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 1px 2px -1px hsl(0 0% 0% / 0.17);
  --shadow: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 1px 2px -1px hsl(0 0% 0% / 0.17);
  --shadow-md: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 2px 4px -1px hsl(0 0% 0% / 0.17);
  --shadow-lg: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 4px 6px -1px hsl(0 0% 0% / 0.17);
  --shadow-xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 8px 10px -1px hsl(0 0% 0% / 0.17);
  --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.43);
  --tracking-normal: 0.025em;
}

.dark {
  --background: #121212;
  --foreground: #e2e8f0;
  --card: #171717;
  --card-foreground: #e2e8f0;
  --popover: #242424;
  --popover-foreground: #a9a9a9;
  --primary: #006239;
  --primary-foreground: #dde8e3;
  --secondary: #242424;
  --secondary-foreground: #fafafa;
  --muted: #1f1f1f;
  --muted-foreground: #a2a2a2;
  --accent: #313131;
  --accent-foreground: #fafafa;
  --destructive: #541c15;
  --destructive-foreground: #ede9e8;
  --border: #292929;
  --input: #242424;
  --ring: #4ade80;
  --chart-1: #4ade80;
  --chart-2: #60a5fa;
  --chart-3: #a78bfa;
  --chart-4: #fbbf24;
  --chart-5: #2dd4bf;
  --sidebar: #121212;
  --sidebar-foreground: #898989;
  --sidebar-primary: #006239;
  --sidebar-primary-foreground: #dde8e3;
  --sidebar-accent: #313131;
  --sidebar-accent-foreground: #fafafa;
  --sidebar-border: #292929;
  --sidebar-ring: #4ade80;
  --font-sans: Outfit, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.09);
  --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.09);
  --shadow-sm: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 1px 2px -1px hsl(0 0% 0% / 0.17);
  --shadow: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 1px 2px -1px hsl(0 0% 0% / 0.17);
  --shadow-md: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 2px 4px -1px hsl(0 0% 0% / 0.17);
  --shadow-lg: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 4px 6px -1px hsl(0 0% 0% / 0.17);
  --shadow-xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 8px 10px -1px hsl(0 0% 0% / 0.17);
  --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.43);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
  letter-spacing: var(--tracking-normal);
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}