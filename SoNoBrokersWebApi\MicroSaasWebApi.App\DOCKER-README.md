# SoNoBrokers .NET Web API - Independent Docker Setup

This document explains how to run the SoNoBrokers .NET Web API backend independently using Docker.

## 🏗️ Architecture

This setup runs **ONLY** the .NET 9 Web API backend. The React frontend runs separately.

```
┌─────────────────────┐    HTTP Requests    ┌─────────────────────┐
│   React Frontend    │ ──────────────────► │   .NET Web API      │
│   (Port 3000)       │                     │   (Port 8080)       │
│   Separate Container│                     │   Docker Container  │
└─────────────────────┘                     └─────────────────────┘
                                                       │
                                                       ▼
                                            ┌─────────────────────┐
                                            │   Supabase DB       │
                                            │   (External)        │
                                            └─────────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Docker Desktop for Windows
- Git
- 2GB+ available RAM
- Supabase database access

### 1. Setup Environment
```powershell
# Copy environment template
Copy-Item .env.docker .env

# Edit .env with your configuration
notepad .env
```

**Important**: Update database connection string and Clerk keys:
```env
CONNECTION_STRING_SUPABASE=Host=your-supabase-host;Port=5432;Database=postgres;Username=your-user;Password=your-password
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key
CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key
```

### 2. Production Mode
```powershell
# Build and start
.\docker-run.ps1 build
.\docker-run.ps1 up

# Or combined
.\docker-run.ps1 build; .\docker-run.ps1 up
```

### 3. Development Mode (with Hot Reload)
```powershell
# Start development environment
.\docker-run.ps1 dev

# Or step by step
.\docker-run.ps1 build -Development
.\docker-run.ps1 up -Development
```

## 📋 Available Commands

### PowerShell Script Commands
```powershell
.\docker-run.ps1 build          # Build production image
.\docker-run.ps1 up             # Start container
.\docker-run.ps1 down           # Stop container
.\docker-run.ps1 restart        # Restart container
.\docker-run.ps1 logs           # View logs
.\docker-run.ps1 clean          # Clean up resources
.\docker-run.ps1 dev            # Development mode
.\docker-run.ps1 test           # Test API health
.\docker-run.ps1 help           # Show help
```

### Docker Compose Commands
```powershell
# Production
docker-compose up -d                                    # Start
docker-compose down                                     # Stop
docker-compose logs -f api                              # View logs

# Development
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
docker-compose -f docker-compose.yml -f docker-compose.dev.yml down
```

## 🔧 Configuration

### Environment Variables (.env)

**Required Variables:**
```env
# Database
CONNECTION_STRING_SUPABASE=Host=your-host;Port=5432;Database=postgres;Username=user;Password=pass

# Authentication
CLERK_SECRET_KEY=sk_test_your_secret_key
CLERK_PUBLISHABLE_KEY=pk_test_your_publishable_key

# CORS (allow React app)
CORS_ALLOWED_ORIGINS=http://localhost:3000
```

**Optional Variables:**
```env
# Logging
LOG_LEVEL=Information

# External Services
RESEND_API_KEY=your_resend_key
STRIPE_SECRET_KEY=sk_test_your_stripe_key

# Performance
DB_MAX_POOL_SIZE=100
API_RATE_LIMIT_REQUESTS=1000
```

### Service URLs
- **API Base**: http://localhost:8080
- **Health Check**: http://localhost:8080/health
- **Swagger UI**: http://localhost:8080/swagger
- **API Documentation**: http://localhost:8080/swagger/v1/swagger.json

## 🔍 Development Workflow

### Hot Reload Development
```powershell
# Start with hot reload
.\docker-run.ps1 dev

# Your C# code changes will automatically reload
# No need to rebuild the container
```

### Testing API
```powershell
# Test API health
.\docker-run.ps1 test

# Manual testing
curl http://localhost:8080/health
curl http://localhost:8080/api/users/profile
```

### Debugging
```powershell
# View real-time logs
.\docker-run.ps1 logs

# Access container shell
docker-compose exec api /bin/bash

# Check container status
docker-compose ps
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Database Connection Errors
**Problem**: Can't connect to Supabase
**Solution**: 
- Verify connection string in `.env`
- Check Supabase database is accessible
- Ensure IP is whitelisted in Supabase

#### 2. Port Already in Use
**Problem**: Port 8080 is already in use
**Solution**:
```env
# Change port in .env
API_PORT=8081
```

#### 3. Build Failures
**Problem**: Docker build fails
**Solution**:
```powershell
# Clean build
.\docker-run.ps1 clean
.\docker-run.ps1 build -NoCache
```

#### 4. Authentication Issues
**Problem**: Clerk authentication not working
**Solution**:
- Verify Clerk keys in `.env`
- Check Clerk dashboard configuration
- Ensure CORS allows React app origin

#### 5. CORS Errors
**Problem**: React app can't access API
**Solution**:
```env
# Update CORS origins in .env
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://your-domain.com
```

### Performance Issues
```powershell
# Check resource usage
docker stats sonobrokers-api

# Monitor database connections
# Check logs for connection pool issues
.\docker-run.ps1 logs
```

## 🔗 Integration with React App

### API Endpoints
The API provides the following main endpoints:

```
GET    /health                    # Health check
GET    /swagger                   # API documentation

# Authentication
POST   /api/auth/login           # User login
GET    /api/auth/profile         # User profile

# Properties
GET    /api/properties           # List properties
POST   /api/properties           # Create property
GET    /api/properties/{id}      # Get property
PUT    /api/properties/{id}      # Update property

# Advertisers
GET    /api/advertisers          # List advertisers
POST   /api/advertisers          # Create advertiser

# Admin
GET    /api/admin/users          # Admin user management
```

### CORS Configuration
The API is configured to allow requests from the React app:

```csharp
// Configured in Program.cs
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp", policy =>
    {
        policy.WithOrigins(corsOrigins)
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});
```

## 📊 Monitoring and Logging

### Health Checks
```powershell
# Check API health
curl http://localhost:8080/health

# Detailed health check
curl http://localhost:8080/health/detailed
```

### Logging
```powershell
# View all logs
.\docker-run.ps1 logs

# Filter logs by level
docker-compose logs api | findstr "ERROR"
docker-compose logs api | findstr "WARNING"
```

## 📚 Additional Information

### File Structure
```
SoNoBrokersWebApi/
├── Dockerfile                 # Multi-stage Docker build
├── docker-compose.yml         # Production setup
├── docker-compose.dev.yml     # Development overrides
├── .dockerignore              # Files to exclude from build
├── .env.docker               # Environment template
├── .env                      # Your local configuration
├── docker-run.ps1            # Management script
└── DOCKER-README.md          # This file
```

### Best Practices
1. **Always use .env** for local configuration
2. **Never commit secrets** to version control
3. **Use development mode** for active development
4. **Monitor database connections** for performance
5. **Test API endpoints** regularly
6. **Keep Swagger documentation** up to date

### Security Considerations
- Use strong database passwords
- Rotate Clerk keys regularly
- Enable HTTPS in production
- Implement rate limiting
- Monitor for suspicious activity

### Next Steps
1. Set up the React frontend independently
2. Configure proper CORS settings
3. Test API connectivity from React app
4. Deploy both applications to production

For React app setup, see: `../SoNoBrokers/DOCKER-README.md`
