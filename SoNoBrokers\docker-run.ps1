# =============================================================================
# SoNoBrokers React Application - Docker Management Script
# Independent Docker setup for React frontend only
# =============================================================================

[CmdletBinding()]
param(
    [Parameter(Position = 0)]
    [ValidateSet("build", "up", "down", "restart", "logs", "clean", "dev", "help")]
    [string]$Command = "help",

    [switch]$Development,
    [switch]$NoCache,
    [switch]$Help
)

# =============================================================================
# Configuration
# =============================================================================

$ErrorActionPreference = "Stop"
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# =============================================================================
# Helper Functions
# =============================================================================

function Write-Header {
    param([string]$Message)
    Write-Host "=============================================================================" -ForegroundColor Blue
    Write-Host " $Message" -ForegroundColor Blue
    Write-Host "=============================================================================" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Cyan
}

function Show-Usage {
    @"
Usage: .\docker-run.ps1 [COMMAND] [OPTIONS]

SoNoBrokers React Application - Independent Docker Management

COMMANDS:
    build       Build the React application Docker image
    up          Start the React application container
    down        Stop the React application container
    restart     Restart the React application container
    logs        Show application logs
    clean       Clean up containers and images
    dev         Start in development mode with hot reload
    help        Show this help message

OPTIONS:
    -Development    Start in development mode
    -NoCache        Build without cache
    -Verbose        Verbose output (built-in PowerShell parameter)
    -Help           Show this help message

EXAMPLES:
    .\docker-run.ps1 build                     # Build production image
    .\docker-run.ps1 up                        # Start production container
    .\docker-run.ps1 dev                       # Start development with hot reload
    .\docker-run.ps1 build -NoCache            # Build without cache
    .\docker-run.ps1 logs                      # View application logs

IMPORTANT:
    This script runs ONLY the React frontend application.
    Make sure your .NET Web API is running independently on port 8080.
    Update NEXT_PUBLIC_API_BASE_URL in .env.local to point to your API.

"@
}

function Test-Dependencies {
    Write-Info "Checking dependencies..."
    
    if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
        Write-Error "Docker is not installed or not in PATH"
        exit 1
    }
    
    if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue)) {
        Write-Error "Docker Compose is not installed or not in PATH"
        exit 1
    }
    
    Write-Success "All dependencies are available"
}

function Initialize-Environment {
    Write-Info "Setting up environment..."
    
    Set-Location $ScriptDir
    
    # Create .env.local file if it doesn't exist
    $envFile = Join-Path $ScriptDir ".env.local"
    $envDockerFile = Join-Path $ScriptDir ".env.docker"
    
    if (-not (Test-Path $envFile)) {
        if (Test-Path $envDockerFile) {
            Write-Warning ".env.local not found, copying from .env.docker"
            Copy-Item $envDockerFile $envFile
            Write-Success ".env.local file created"
            Write-Warning "Please update .env.local with your configuration"
        }
        else {
            Write-Warning "No environment template found"
        }
    }
    else {
        Write-Success ".env.local file exists"
    }
    
    # Set build arguments
    $env:BUILD_DATE = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
    
    try {
        $env:VCS_REF = (git rev-parse --short HEAD 2>$null)
    }
    catch {
        $env:VCS_REF = "unknown"
    }
    
    try {
        $env:VERSION = (git describe --tags --always 2>$null)
    }
    catch {
        $env:VERSION = "1.0.0"
    }
    
    Write-Success "Environment setup complete"
}

function Get-ComposeFiles {
    $files = @("-f", "docker-compose.yml")
    
    if ($Development) {
        $files += @("-f", "docker-compose.dev.yml")
    }
    
    return $files
}

# =============================================================================
# Command Functions
# =============================================================================

function Invoke-Build {
    Write-Header "Building SoNoBrokers React Application"
    
    $buildArgs = @()
    if ($NoCache) {
        $buildArgs += "--no-cache"
    }
    
    $composeFiles = Get-ComposeFiles
    $allArgs = $composeFiles + @("build") + $buildArgs + @("frontend")
    
    Write-Info "Building React application..."
    if ($VerbosePreference -eq 'Continue') {
        & docker-compose @allArgs
    }
    else {
        & docker-compose @allArgs 2>$null | Out-Null
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "React application built successfully"
    }
    else {
        Write-Error "Failed to build React application"
        exit 1
    }
}

function Invoke-Up {
    Write-Header "Starting SoNoBrokers React Application"
    
    $mode = if ($Development) { "development" } else { "production" }
    Write-Info "Starting React application in $mode mode..."
    
    $composeFiles = Get-ComposeFiles
    & docker-compose @composeFiles up -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "React application started successfully"
        Write-Info "Application URL: http://localhost:3000"
        Write-Warning "Make sure your .NET Web API is running on port 8080"
    }
    else {
        Write-Error "Failed to start React application"
        exit 1
    }
}

function Invoke-Down {
    Write-Header "Stopping SoNoBrokers React Application"
    
    $composeFiles = Get-ComposeFiles
    & docker-compose @composeFiles down
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "React application stopped successfully"
    }
    else {
        Write-Error "Failed to stop React application"
        exit 1
    }
}

function Invoke-Restart {
    Write-Header "Restarting SoNoBrokers React Application"
    
    Invoke-Down
    Invoke-Up
}

function Invoke-Logs {
    Write-Header "Showing React Application Logs"
    
    $composeFiles = Get-ComposeFiles
    & docker-compose @composeFiles logs -f frontend
}

function Invoke-Clean {
    Write-Header "Cleaning up React Application Resources"
    
    Write-Warning "This will remove containers, images, and volumes for the React application"
    $confirmation = Read-Host "Are you sure? (y/N)"
    
    if ($confirmation -match "^[Yy]$") {
        $composeFiles = Get-ComposeFiles
        & docker-compose @composeFiles down -v --rmi all --remove-orphans
        Write-Success "Cleanup completed"
    }
    else {
        Write-Info "Cleanup cancelled"
    }
}

function Invoke-Dev {
    Write-Header "Starting Development Environment"
    
    $script:Development = $true
    Invoke-Build
    Invoke-Up
    
    Write-Info "Development environment started with hot reload"
    Write-Info "Application URL: http://localhost:3000"
    Write-Warning "Make sure your .NET Web API is running on port 8080"
}

# =============================================================================
# Main Script
# =============================================================================

function Main {
    if ($Help -or $Command -eq "help") {
        Show-Usage
        return
    }
    
    # Change to script directory
    Set-Location $ScriptDir
    
    # Run pre-checks
    Test-Dependencies
    Initialize-Environment
    
    # Execute command
    switch ($Command) {
        "build" { Invoke-Build }
        "up" { Invoke-Up }
        "down" { Invoke-Down }
        "restart" { Invoke-Restart }
        "logs" { Invoke-Logs }
        "clean" { Invoke-Clean }
        "dev" { Invoke-Dev }
        default {
            Write-Error "Unknown command: $Command"
            Show-Usage
            exit 1
        }
    }
}

# =============================================================================
# Script Execution
# =============================================================================

# Run main function
try {
    Main
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
