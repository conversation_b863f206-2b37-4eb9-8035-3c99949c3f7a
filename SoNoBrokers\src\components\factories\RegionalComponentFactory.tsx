import { Country } from '@prisma/client';
import { lazy } from 'react';

// Lazy load country-specific components
const CAHeroSection = lazy(() => import('@/components/country-specific/ca/HeroSection').then(module => ({ default: module.HeroSection })));
const USHeroSection = lazy(() => import('@/components/country-specific/us/HeroSection'));
const UAEHeroSection = lazy(() => import('@/components/country-specific/uae/HeroSection'));

const CAPropertySearch = lazy(() => import('@/components/country-specific/ca/PropertySearch').then(module => ({ default: module.PropertySearch })));
const USPropertySearch = lazy(() => import('@/components/country-specific/us/PropertySearch').then(module => ({ default: module.PropertySearch })));
const UAEPropertySearch = lazy(() => import('@/components/country-specific/uae/PropertySearch').then(module => ({ default: module.PropertySearch })));

const CAServiceProviders = lazy(() => import('@/components/country-specific/ca/ServiceProviders').then(module => ({ default: module.ServiceProviders })));
const USServiceProviders = lazy(() => import('@/components/country-specific/us/ServiceProviders'));
const UAEServiceProviders = lazy(() => import('@/components/country-specific/uae/ServiceProviders'));

interface RegionalComponentFactoryProps {
  country: Country;
  component: 'HeroSection' | 'PropertySearch' | 'ServiceProviders';
  userType: 'buyer' | 'seller';
  onViewProperties?: () => void;
  [key: string]: any;
}

export function RegionalComponentFactory({
  country,
  component,
  ...props
}: RegionalComponentFactoryProps) {
  const componentMap = {
    CA: {
      HeroSection: CAHeroSection,
      PropertySearch: CAPropertySearch,
      ServiceProviders: CAServiceProviders,
    },
    US: {
      HeroSection: USHeroSection,
      PropertySearch: USPropertySearch,
      ServiceProviders: USServiceProviders,
    },
    UAE: {
      HeroSection: UAEHeroSection,
      PropertySearch: UAEPropertySearch,
      ServiceProviders: UAEServiceProviders,
    },
  };

  const Component = componentMap[country]?.[component];

  if (!Component) {
    console.warn(`Component ${component} not found for country ${country}`);
    return null;
  }

  return <Component {...props} />;
}

export default RegionalComponentFactory;
