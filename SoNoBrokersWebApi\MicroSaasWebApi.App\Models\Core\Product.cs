using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MicroSaasWebApi.Models.Core
{
    /// <summary>
    /// Product model for the MicroSaaS application
    /// </summary>
    [Table("products", Schema = "core")]
    public class Product
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string Description { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string Category { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string SKU { get; set; } = string.Empty;

        [Column(TypeName = "decimal(10,2)")]
        public decimal Price { get; set; }

        [MaxLength(3)]
        public string Currency { get; set; } = "USD";

        public int StockQuantity { get; set; } = 0;

        [MaxLength(255)]
        public string? ImageUrl { get; set; }

        [MaxLength(500)]
        public string? Tags { get; set; }

        public bool IsActive { get; set; } = true;
        public bool IsDigital { get; set; } = false;
        public bool RequiresShipping { get; set; } = true;

        [Column(TypeName = "decimal(5,2)")]
        public decimal? Weight { get; set; }

        [MaxLength(50)]
        public string? WeightUnit { get; set; } = "kg";

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }
        public Guid? CreatedBy { get; set; }
        public Guid? UpdatedBy { get; set; }

        // Navigation properties
        public virtual User? Creator { get; set; }
        public virtual User? Updater { get; set; }
    }
}
