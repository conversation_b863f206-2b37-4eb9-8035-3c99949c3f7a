'use client'

import { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'

interface TextRotateProps {
  words: string[]
  className?: string
  duration?: number
}

export function TextRotate({ words, className, duration = 2000 }: TextRotateProps) {
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % words.length)
    }, duration)

    return () => clearInterval(interval)
  }, [words.length, duration])

  return (
    <span className={cn("inline-block transition-all duration-500", className)}>
      {words[currentIndex]}
    </span>
  )
}
