/** @type {import('next').NextConfig} */
const nextConfig = {
	eslint: {
		ignoreDuringBuilds: true,
	},
	reactStrictMode: true,
	// Enable standalone output for Docker
	output: 'standalone',
	images: {
		remotePatterns: [
			{
				protocol: 'https',
				hostname: 'lh3.googleusercontent.com',
			},
			{
				protocol: 'https',
				hostname: 'pbs.twimg.com',
			},
			{
				protocol: 'https',
				hostname: 'images.unsplash.com',
			},
			{
				protocol: 'https',
				hostname: 'logos-world.net',
			},
			{
				protocol: 'http',
				hostname: 'localhost',
			},
			{
				protocol: 'https',
				hostname: 'cdn-icons-png.flaticon.com',
			},
			{
				protocol: 'https',
				hostname: 'res.cloudinary.com',
			},
			{
				protocol: 'https',
				hostname: 'blogger.googleusercontent.com',
			},
			{
				protocol: 'https',
				hostname: 'secure.gravatar.com',
			},
			{
				protocol: 'https',
				hostname: 'img.clerk.com',
			},
		],
	},
	// Add optimizations from Next.js 15
	experimental: {
		optimizePackageImports: [
			'lucide-react',
			'@radix-ui/react-icons',
			'@heroicons/react',
			'date-fns',
		],
		// Enable React compiler for better performance
		// reactCompiler: true, // Uncomment if you want to try the React compiler
	},
	// Note: serverExternalPackages is only available in Next.js 15+
	// For Next.js 14, Clerk should work without this configuration
	// Suppress headers() warnings for Clerk compatibility
	logging: {
		fetches: {
			fullUrl: false,
		},
	},
}

module.exports = nextConfig
