// TODO: Migrate to .NET Web API - temporarily disabled
// import prisma from '@/lib/prisma'
import { currentUser } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'

export async function GET() {
	// TODO: Migrate to .NET Web API
	return NextResponse.json(
		{ error: 'API route temporarily disabled - migrating to .NET Web API' },
		{ status: 503 }
	)
}

/* DISABLED - MIGRATE TO .NET WEB API
export async function GET_DISABLED() {
	try {
		const user = await currentUser()

		if (!user) {
			throw new Error('user in undefined')
		}

		const projects = await prisma.project.findMany({
			where: {
				userClerkId: user.id,
			},
		})

		return NextResponse.json({ success: true, projects })
	} catch (err) {
		console.error('Connection error:', err)
		if (err) {
			return NextResponse.json({ error: err.message })
		}
		return NextResponse.json(
			{ error: 'An unexpected error occurred' },
			{ status: 500 }
		)
	}
}
*/
