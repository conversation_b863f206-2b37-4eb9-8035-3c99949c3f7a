version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sonobrokers-frontend-dev
    env_file:
      - .env.docker.development
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
    volumes:
      # Mount source code for hot reload in development
      - .:/app
      - /app/node_modules
      - /app/.next
    networks:
      - sonobrokers-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  sonobrokers-dev-network:
    driver: bridge
